/**
 * preloader-removal.js
 * 
 * This script handles the immediate removal of any preloader elements
 * to ensure the page is interactive as quickly as possible.
 */

// Immediately attempt to remove preloader and restore scrolling
(function() {
    console.log('Immediate preloader removal script running');
    document.body.style.overflow = 'auto';

    // Try to find preloader by ID
    const preloader = document.getElementById('preloader');
    if (preloader) {
        preloader.style.display = 'none';
        console.log('Preloader hidden by ID');
    }

    // Also try by class
    const preloaders = document.querySelectorAll('.preloader');
    if (preloaders.length > 0) {
        preloaders.forEach(function(el) {
            el.style.display = 'none';
        });
        console.log('Preloaders hidden by class');
    }
})();
