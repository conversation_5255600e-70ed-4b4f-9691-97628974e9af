{% extends "base.html" %}
{% load static %}

{% block title %}Payment Successful - Enterprise QR{% endblock %}

{% block extra_css %}
<style>
    .success-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 3rem 0;
        text-align: center;
    }

    .success-icon {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 3rem;
        margin: 0 auto 2rem;
        box-shadow: 0 10px 15px -3px rgba(16, 185, 129, 0.2), 0 4px 6px -2px rgba(16, 185, 129, 0.1);
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.4);
        }
        70% {
            box-shadow: 0 0 0 20px rgba(16, 185, 129, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
        }
    }

    .success-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        color: #10b981;
    }

    .success-message {
        font-size: 1.25rem;
        color: #334155;
        max-width: 600px;
        margin: 0 auto 2rem;
    }

    .order-details {
        background-color: #f8fafc;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        text-align: left;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    .order-details-title {
        font-weight: 600;
        margin-bottom: 1.5rem;
        color: #1e293b;
        font-size: 1.25rem;
        display: flex;
        align-items: center;
    }

    .order-details-title i {
        margin-right: 0.75rem;
        color: #10b981;
    }

    .detail-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 1rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #e2e8f0;
    }

    .detail-row:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }

    .detail-label {
        font-weight: 500;
        color: #64748b;
    }

    .detail-value {
        font-weight: 600;
        color: #334155;
        display: flex;
        align-items: center;
    }

    .payment-logo {
        height: 20px;
        margin-right: 0.75rem;
        object-fit: contain;
    }

    .next-steps {
        background-color: #f0fdf4;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        text-align: left;
        border-left: 4px solid #10b981;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    .next-steps-title {
        font-weight: 600;
        margin-bottom: 1rem;
        color: #10b981;
        font-size: 1.25rem;
        display: flex;
        align-items: center;
    }

    .next-steps-title i {
        margin-right: 0.75rem;
    }

    .steps-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .step-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 1rem;
    }

    .step-item:last-child {
        margin-bottom: 0;
    }

    .step-number {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background-color: #10b981;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 0.875rem;
        margin-right: 1rem;
        flex-shrink: 0;
    }

    .step-text {
        color: #334155;
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
    }

    .btn-action {
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
    }

    .btn-primary-action {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
        border: none;
    }

    .btn-primary-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.4);
        color: white;
    }

    .btn-secondary-action {
        background-color: white;
        color: #1e293b;
        border: 1px solid #cbd5e1;
    }

    .btn-secondary-action:hover {
        background-color: #f8fafc;
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="success-container">
    <div class="success-icon">
        <i class="fas fa-check"></i>
    </div>

    <h1 class="success-title">Payment Successful!</h1>
    {% if user.is_authenticated %}
    <p class="success-message">Thank you for upgrading to Premium. Your account has been successfully upgraded and you now have access to all premium features.</p>
    {% else %}
    <p class="success-message">Thank you for your purchase! To access premium features, please log in or create an account.</p>
    {% endif %}

    <div class="order-details">
        <h2 class="order-details-title">
            <i class="fas fa-receipt"></i> Order Details
        </h2>

        <div class="detail-row">
            <span class="detail-label">Order Number</span>
            <span class="detail-value">#ENT-{{ "now"|date:"Ymd" }}-{% if user.is_authenticated %}{{ user.id }}{% else %}GUEST{% endif %}</span>
        </div>

        <div class="detail-row">
            <span class="detail-label">Plan</span>
            <span class="detail-value">Premium (Monthly)</span>
        </div>

        <div class="detail-row">
            <span class="detail-label">Amount</span>
            <span class="detail-value">$19.99</span>
        </div>

        <div class="detail-row">
            <span class="detail-label">Payment Method</span>
            <span class="detail-value">
                <img src="https://www.safaricom.co.ke/images/M-PESA_LOGO.png" alt="M-Pesa" class="payment-logo">
                M-Pesa (•••• 4567)
            </span>
        </div>

        <div class="detail-row">
            <span class="detail-label">Date</span>
            <span class="detail-value">{{ "now"|date:"F j, Y" }}</span>
        </div>
    </div>

    <div class="next-steps">
        <h2 class="next-steps-title">
            <i class="fas fa-arrow-right"></i> Next Steps
        </h2>

        <ul class="steps-list">
            <li class="step-item">
                <div class="step-number">1</div>
                <div class="step-text">Explore your new premium features, including batch processing for generating multiple QR codes at once.</div>
            </li>
            <li class="step-item">
                <div class="step-number">2</div>
                <div class="step-text">Check out the advanced customization options now available to you for creating professional QR codes.</div>
            </li>
            <li class="step-item">
                <div class="step-number">3</div>
                <div class="step-text">Access your premium analytics dashboard to track QR code performance and engagement.</div>
            </li>
        </ul>
    </div>

    <div class="action-buttons">
        {% if user.is_authenticated %}
        <a href="{% url 'batch_processing' %}" class="btn btn-action btn-primary-action">
            <i class="fas fa-layer-group me-2"></i> Try Batch Processing
        </a>
        <a href="{% url 'index' %}" class="btn btn-action btn-secondary-action">
            <i class="fas fa-home me-2"></i> Back to Home
        </a>
        {% else %}
        <a href="{% url 'custom_login' %}" class="btn btn-action btn-primary-action">
            <i class="fas fa-sign-in-alt me-2"></i> Log In
        </a>
        <a href="{% url 'index' %}" class="btn btn-action btn-secondary-action">
            <i class="fas fa-home me-2"></i> Back to Home
        </a>
        {% endif %}
    </div>
</div>
{% endblock %}
