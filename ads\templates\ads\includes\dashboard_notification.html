{% if messages %}
<div class="dashboard-notification-container">
    {% for message in messages %}
    <div class="dashboard-notification {{ message.tags }}">
        <div class="notification-content">
            <i class="fas fa-info-circle notification-icon"></i>
            <span class="notification-message">{{ message }}</span>
        </div>
        <button class="notification-close" onclick="this.parentElement.style.display='none';">
            <i class="fas fa-times"></i>
        </button>
    </div>
    {% endfor %}
</div>
{% endif %}

<style>
    .dashboard-notification-container {
        position: fixed;
        top: 70px;
        left: 0;
        right: 0;
        z-index: 9999;
        display: flex;
        flex-direction: column;
        align-items: center;
        pointer-events: none;
    }

    .dashboard-notification {
        background: linear-gradient(135deg, #1a237e, #283593);
        color: white;
        border-radius: 8px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        margin-bottom: 10px;
        padding: 12px 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 90%;
        max-width: 800px;
        animation: slideDown 0.5s ease-out forwards;
        pointer-events: auto;
        border-left: 4px solid #3949ab;
    }

    .dashboard-notification.info {
        background: linear-gradient(135deg, #1a237e, #283593);
        border-left-color: #3949ab;
    }

    .dashboard-notification.success {
        background: linear-gradient(135deg, #2e7d32, #388e3c);
        border-left-color: #4caf50;
    }

    .dashboard-notification.warning {
        background: linear-gradient(135deg, #f57f17, #ffa000);
        border-left-color: #ffb300;
    }

    .dashboard-notification.error {
        background: linear-gradient(135deg, #c62828, #d32f2f);
        border-left-color: #f44336;
    }

    .notification-content {
        display: flex;
        align-items: center;
        flex: 1;
    }

    .notification-icon {
        font-size: 18px;
        margin-right: 12px;
    }

    .notification-message {
        font-size: 14px;
        font-weight: 500;
    }

    .notification-close {
        background: none;
        border: none;
        color: rgba(255, 255, 255, 0.7);
        cursor: pointer;
        font-size: 14px;
        margin-left: 15px;
        padding: 5px;
        transition: color 0.3s ease;
    }

    .notification-close:hover {
        color: white;
    }

    @keyframes slideDown {
        0% {
            transform: translateY(-20px);
            opacity: 0;
        }
        100% {
            transform: translateY(0);
            opacity: 1;
        }
    }

    @media (max-width: 768px) {
        .dashboard-notification-container {
            top: 60px;
        }

        .dashboard-notification {
            width: 95%;
            padding: 10px 15px;
        }

        .notification-message {
            font-size: 13px;
        }
    }
</style>

<script>
    // Auto-hide notifications after 10 seconds
    document.addEventListener('DOMContentLoaded', function() {
        const notifications = document.querySelectorAll('.dashboard-notification');
        notifications.forEach(notification => {
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateY(-20px)';
                notification.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                
                setTimeout(() => {
                    notification.style.display = 'none';
                }, 500);
            }, 10000);
        });
    });
</script>
