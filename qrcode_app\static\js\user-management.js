/**
 * User Management JavaScript
 * 
 * This file handles the functionality for the user management page,
 * including user listing, role-based access control, and user editing.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize the user interface
    initUI();
    
    // Set default role if not already set
    if (!rbac.currentUser) {
        // Default to ADMIN role for demonstration
        rbac.setUser({
            name: 'Admin User',
            email: '<EMAIL>'
        }, ['ADMIN']);
    }
    
    // Update UI based on current permissions
    rbac.updateUI();
    
    // Load sample users
    loadUsers();
    
    // Initialize role selector
    initRoleSelector();
    
    // Initialize user dropdown
    initUserDropdown();
    
    // Initialize user actions
    initUserActions();
});

/**
 * Initialize the user interface
 */
function initUI() {
    // Set active role button
    updateActiveRoleButton();
    
    // Update user name in header
    updateUserInfo();
}

/**
 * Update the active role button based on current role
 */
function updateActiveRoleButton() {
    const roleButtons = document.querySelectorAll('.role-btn');
    roleButtons.forEach(button => {
        button.classList.remove('active');
        if (rbac.userRoles.includes(button.dataset.role)) {
            button.classList.add('active');
        }
    });
}

/**
 * Update user info in the header
 */
function updateUserInfo() {
    if (rbac.currentUser) {
        const userNameElement = document.querySelector('.user-name');
        if (userNameElement) {
            userNameElement.textContent = rbac.currentUser.name;
        }
    }
}

/**
 * Initialize the role selector
 */
function initRoleSelector() {
    const roleButtons = document.querySelectorAll('.role-btn');
    
    roleButtons.forEach(button => {
        button.addEventListener('click', function() {
            const role = this.dataset.role;
            
            // Clear existing roles and set the new role
            rbac.userRoles = [];
            rbac.addRole(role);
            
            // Update UI
            updateActiveRoleButton();
            rbac.updateUI();
            
            // Show notification
            showNotification(`Role changed to ${ROLE_TEMPLATES[role].name}`, 'success');
            
            // Reload users to reflect permission changes
            loadUsers();
        });
    });
}

/**
 * Initialize user dropdown menu
 */
function initUserDropdown() {
    const userMenu = document.getElementById('user-menu');
    
    if (userMenu) {
        const userMenuBtn = userMenu.querySelector('.user-menu-btn');
        
        userMenuBtn.addEventListener('click', function() {
            userMenu.classList.toggle('active');
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            if (!userMenu.contains(event.target)) {
                userMenu.classList.remove('active');
            }
        });
    }
}

/**
 * Initialize user action buttons
 */
function initUserActions() {
    // Add user button
    const addUserBtn = document.querySelector('.admin-actions .primary-btn');
    if (addUserBtn) {
        addUserBtn.addEventListener('click', function() {
            if (rbac.hasPermission(PERMISSIONS.USER_CREATE)) {
                showNotification('Add user functionality would open a form here', 'info');
            } else {
                showNotification('You do not have permission to add users', 'error');
            }
        });
    }
    
    // Edit user modal
    const editModal = document.getElementById('user-edit-modal');
    const modalOverlay = document.querySelector('.modal-overlay');
    
    // Close modal when clicking close button or overlay
    document.querySelectorAll('.modal-close, .modal-overlay, [data-action="cancel"]').forEach(element => {
        element.addEventListener('click', function() {
            editModal.classList.remove('active');
            modalOverlay.classList.remove('active');
        });
    });
    
    // Save user changes
    const saveButton = document.querySelector('[data-action="save"]');
    if (saveButton) {
        saveButton.addEventListener('click', function() {
            if (rbac.hasPermission(PERMISSIONS.USER_UPDATE)) {
                // In a real app, this would save the user data
                showNotification('User updated successfully', 'success');
                editModal.classList.remove('active');
                modalOverlay.classList.remove('active');
            } else {
                showNotification('You do not have permission to update users', 'error');
            }
        });
    }
}

/**
 * Sample user data
 */
const sampleUsers = [
    {
        id: 1,
        name: 'John Smith',
        email: '<EMAIL>',
        title: 'Marketing Director',
        role: 'ADMIN',
        status: 'active',
        lastActive: '2023-05-10T14:30:00Z'
    },
    {
        id: 2,
        name: 'Sarah Johnson',
        email: '<EMAIL>',
        title: 'Product Manager',
        role: 'MANAGER',
        status: 'active',
        lastActive: '2023-05-10T10:15:00Z'
    },
    {
        id: 3,
        name: 'Michael Brown',
        email: '<EMAIL>',
        title: 'Graphic Designer',
        role: 'CREATOR',
        status: 'active',
        lastActive: '2023-05-09T16:45:00Z'
    },
    {
        id: 4,
        name: 'Emily Davis',
        email: '<EMAIL>',
        title: 'Content Writer',
        role: 'CREATOR',
        status: 'inactive',
        lastActive: '2023-04-28T09:20:00Z'
    },
    {
        id: 5,
        name: 'David Wilson',
        email: '<EMAIL>',
        title: 'Sales Representative',
        role: 'VIEWER',
        status: 'active',
        lastActive: '2023-05-10T11:30:00Z'
    },
    {
        id: 6,
        name: 'Jennifer Taylor',
        email: '<EMAIL>',
        title: 'IT Specialist',
        role: 'SECURITY',
        status: 'active',
        lastActive: '2023-05-10T13:10:00Z'
    },
    {
        id: 7,
        name: 'Robert Martinez',
        email: '<EMAIL>',
        title: 'Finance Manager',
        role: 'BILLING',
        status: 'active',
        lastActive: '2023-05-09T15:20:00Z'
    },
    {
        id: 8,
        name: 'Lisa Anderson',
        email: '<EMAIL>',
        title: 'HR Specialist',
        role: 'MANAGER',
        status: 'pending',
        lastActive: '2023-05-08T14:45:00Z'
    }
];

/**
 * Load users into the table
 */
function loadUsers() {
    const tableBody = document.querySelector('.users-table tbody');
    
    // Clear existing rows
    tableBody.innerHTML = '';
    
    // Check if user has permission to view users
    if (!rbac.hasPermission(PERMISSIONS.USER_READ)) {
        const noPermissionRow = document.createElement('tr');
        noPermissionRow.innerHTML = `
            <td colspan="7" class="text-center">
                <div class="no-permission">
                    <i class="fas fa-lock"></i>
                    <p>You do not have permission to view users</p>
                </div>
            </td>
        `;
        tableBody.appendChild(noPermissionRow);
        return;
    }
    
    // Add user rows
    sampleUsers.forEach(user => {
        const row = document.createElement('tr');
        
        // Format date
        const lastActive = new Date(user.lastActive);
        const formattedDate = lastActive.toLocaleDateString() + ' ' + lastActive.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        
        row.innerHTML = `
            <td>
                <input type="checkbox" id="user-${user.id}" class="user-checkbox">
                <label for="user-${user.id}"></label>
            </td>
            <td>
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-details">
                        <span class="user-name">${user.name}</span>
                        <span class="user-title">${user.title}</span>
                    </div>
                </div>
            </td>
            <td>${user.email}</td>
            <td><span class="user-role role-${user.role.toLowerCase()}">${ROLE_TEMPLATES[user.role].name}</span></td>
            <td>
                <div class="user-status status-${user.status}">
                    <span class="status-dot"></span>
                    <span>${capitalizeFirstLetter(user.status)}</span>
                </div>
            </td>
            <td>${formattedDate}</td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn edit-btn" data-user-id="${user.id}" title="Edit User">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete-btn" data-user-id="${user.id}" title="Delete User">
                        <i class="fas fa-trash-alt"></i>
                    </button>
                </div>
            </td>
        `;
        
        tableBody.appendChild(row);
    });
    
    // Add event listeners to action buttons
    addActionButtonListeners();
}

/**
 * Add event listeners to user action buttons
 */
function addActionButtonListeners() {
    // Edit buttons
    document.querySelectorAll('.edit-btn').forEach(button => {
        button.addEventListener('click', function() {
            const userId = this.dataset.userId;
            const user = sampleUsers.find(u => u.id == userId);
            
            if (rbac.hasPermission(PERMISSIONS.USER_UPDATE)) {
                openEditModal(user);
            } else {
                showNotification('You do not have permission to edit users', 'error');
            }
        });
    });
    
    // Delete buttons
    document.querySelectorAll('.delete-btn').forEach(button => {
        button.addEventListener('click', function() {
            const userId = this.dataset.userId;
            const user = sampleUsers.find(u => u.id == userId);
            
            if (rbac.hasPermission(PERMISSIONS.USER_DELETE)) {
                if (confirm(`Are you sure you want to delete ${user.name}?`)) {
                    showNotification(`User ${user.name} would be deleted`, 'info');
                }
            } else {
                showNotification('You do not have permission to delete users', 'error');
            }
        });
    });
}

/**
 * Open the edit user modal
 * @param {Object} user - User object to edit
 */
function openEditModal(user) {
    const modal = document.getElementById('user-edit-modal');
    const overlay = document.querySelector('.modal-overlay');
    
    // Populate form fields
    document.getElementById('edit-name').value = user.name;
    document.getElementById('edit-email').value = user.email;
    document.getElementById('edit-role').value = user.role;
    document.getElementById('edit-status').value = user.status;
    
    // Show modal
    modal.classList.add('active');
    overlay.classList.add('active');
}

/**
 * Show a notification
 * @param {string} message - Notification message
 * @param {string} type - Notification type (success, error, info)
 */
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    
    // Add icon based on type
    let icon = 'info-circle';
    if (type === 'success') icon = 'check-circle';
    if (type === 'error') icon = 'exclamation-circle';
    
    notification.innerHTML = `
        <i class="fas fa-${icon}"></i>
        <span>${message}</span>
        <button class="notification-close"><i class="fas fa-times"></i></button>
    `;
    
    // Add to the DOM
    document.body.appendChild(notification);
    
    // Add styles if not already added
    if (!document.getElementById('notification-styles')) {
        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            .notification {
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem;
                background-color: white;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                display: flex;
                align-items: center;
                gap: 0.75rem;
                z-index: 1000;
                max-width: 350px;
                transform: translateX(120%);
                transition: transform 0.3s ease;
                border-left: 4px solid #3b82f6;
            }
            
            .notification.show {
                transform: translateX(0);
            }
            
            .notification i {
                font-size: 1.25rem;
                flex-shrink: 0;
            }
            
            .notification span {
                font-size: 0.875rem;
                color: #374151;
                flex-grow: 1;
            }
            
            .notification-close {
                background: none;
                border: none;
                color: #9ca3af;
                cursor: pointer;
                font-size: 0.875rem;
                padding: 0.25rem;
                transition: color 0.2s ease;
            }
            
            .notification-close:hover {
                color: #4b5563;
            }
            
            .notification-success {
                border-left-color: #10b981;
            }
            
            .notification-success i {
                color: #10b981;
            }
            
            .notification-error {
                border-left-color: #ef4444;
            }
            
            .notification-error i {
                color: #ef4444;
            }
            
            .notification-info i {
                color: #3b82f6;
            }
        `;
        document.head.appendChild(style);
    }
    
    // Show the notification
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);
    
    // Set up close button
    const closeButton = notification.querySelector('.notification-close');
    closeButton.addEventListener('click', () => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    });
    
    // Auto-close after 5 seconds
    setTimeout(() => {
        if (document.body.contains(notification)) {
            notification.classList.remove('show');
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    notification.remove();
                }
            }, 300);
        }
    }, 5000);
}

/**
 * Capitalize the first letter of a string
 * @param {string} string - String to capitalize
 * @returns {string} - Capitalized string
 */
function capitalizeFirstLetter(string) {
    return string.charAt(0).toUpperCase() + string.slice(1);
}
