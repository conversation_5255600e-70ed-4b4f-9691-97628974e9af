{% extends 'base.html' %}
{% load static %}

{% block title %}Ad Transactions{% endblock %}

{% block extra_css %}
<!-- Google Fonts - Poppins and Montserrat -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

<style>
    /* Ultra-Premium Enterprise Transaction List Styling */
    body {
        background: linear-gradient(135deg, #000000 0%, #0a0a0a 10%, #1a1a2e 25%, #16213e 40%, #0f3460 60%, #533483 80%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        position: relative;
        overflow-x: hidden;
    }

    /* Premium animated background with enterprise patterns */
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 40% 60%, rgba(102, 126, 234, 0.5) 0%, transparent 50%),
            radial-gradient(circle at 60% 40%, rgba(255, 255, 255, 0.2) 0%, transparent 35%),
            radial-gradient(circle at 20% 80%, rgba(118, 75, 162, 0.4) 0%, transparent 45%),
            radial-gradient(circle at 80% 20%, rgba(83, 52, 131, 0.3) 0%, transparent 40%),
            radial-gradient(circle at 50% 50%, rgba(102, 126, 234, 0.2) 0%, transparent 50%);
        z-index: -1;
        animation: enterpriseTransactionFloat 95s ease-in-out infinite;
    }

    @keyframes enterpriseTransactionFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        25% { transform: translateY(-70px) rotate(4deg); }
        50% { transform: translateY(-50px) rotate(-4deg); }
        75% { transform: translateY(-80px) rotate(2deg); }
    }

    /* Enterprise Container */
    .enterprise-container {
        position: relative;
        z-index: 1;
        padding: 2rem 0;
        min-height: 100vh;
    }

    /* Premium Header Section */
    .enterprise-header {
        background: linear-gradient(135deg, rgba(26, 35, 126, 0.95) 0%, rgba(40, 53, 147, 0.95) 50%, rgba(63, 81, 181, 0.95) 100%);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2.5rem;
        margin-bottom: 2rem;
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        position: relative;
        overflow: hidden;
        animation: slideInDown 0.8s ease-out;
    }

    .enterprise-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
        animation: shimmer 3s ease-in-out infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    .enterprise-title {
        font-family: 'Montserrat', sans-serif !important;
        font-size: 2.5rem;
        font-weight: 700;
        color: #ffffff;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 2;
    }

    .enterprise-subtitle {
        font-size: 1.1rem;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 1.5rem;
        font-weight: 400;
        position: relative;
        z-index: 2;
    }

    .transactions-container {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        overflow: hidden;
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.15),
            0 0 0 1px rgba(255, 255, 255, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        margin: 2rem 0;
        transition: all 0.3s ease;
        animation: slideInUp 0.8s ease-out 0.2s both;
    }

    .transactions-container:hover {
        transform: translateY(-5px);
        box-shadow:
            0 35px 70px rgba(0, 0, 0, 0.2),
            0 0 0 1px rgba(255, 255, 255, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.4);
    }

    .transactions-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 25px;
        border-bottom: 1px solid #eaeaea;
        padding-bottom: 15px;
    }

    .transactions-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #333;
        margin: 0;
    }

    .transactions-actions {
        display: flex;
        gap: 10px;
    }

    .transaction-filter {
        padding: 8px 15px;
        border-radius: 6px;
        border: 1px solid #ddd;
        background-color: #f8f9fa;
        font-size: 14px;
        transition: all 0.2s ease;
    }

    .transaction-filter:focus {
        outline: none;
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .transaction-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
    }

    .transaction-table th {
        background-color: #f8f9fa;
        color: #495057;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 12px;
        letter-spacing: 0.5px;
        padding: 15px;
        border-bottom: 2px solid #dee2e6;
        text-align: left;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .transaction-table td {
        padding: 15px;
        border-bottom: 1px solid #eee;
        vertical-align: middle;
        transition: background-color 0.2s ease;
    }

    .transaction-table tr:hover td {
        background-color: #f8f9fa;
    }

    .transaction-table tr:last-child td {
        border-bottom: none;
    }

    .status-badge {
        display: inline-block;
        padding: 6px 10px;
        border-radius: 30px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-pending {
        background-color: #fff3cd;
        color: #856404;
        border: 1px solid #ffeeba;
    }

    .status-processing {
        background-color: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
    }

    .status-paid {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .status-failed {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .status-refunded {
        background-color: #e2e3e5;
        color: #383d41;
        border: 1px solid #d6d8db;
    }

    .transaction-amount {
        font-weight: 600;
        color: #28a745;
    }

    .transaction-action {
        color: #007bff;
        text-decoration: none;
        font-weight: 500;
        transition: color 0.2s ease;
        display: inline-flex;
        align-items: center;
    }

    .transaction-action:hover {
        color: #0056b3;
        text-decoration: none;
    }

    .transaction-action i {
        margin-right: 5px;
    }

    .pagination {
        display: flex;
        justify-content: center;
        margin-top: 30px;
    }

    .pagination .page-item {
        margin: 0 3px;
    }

    .pagination .page-link {
        border-radius: 6px;
        padding: 8px 16px;
        color: #007bff;
        background-color: #fff;
        border: 1px solid #dee2e6;
        transition: all 0.2s ease;
    }

    .pagination .page-link:hover {
        background-color: #e9ecef;
        border-color: #dee2e6;
        color: #0056b3;
    }

    .pagination .page-item.active .page-link {
        background-color: #007bff;
        border-color: #007bff;
        color: white;
    }

    .empty-state {
        text-align: center;
        padding: 50px 20px;
    }

    .empty-state-icon {
        font-size: 48px;
        color: #adb5bd;
        margin-bottom: 20px;
    }

    .empty-state-title {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 10px;
        color: #495057;
    }

    .empty-state-description {
        color: #6c757d;
        max-width: 500px;
        margin: 0 auto 20px;
    }

    .transaction-summary {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
        flex-wrap: wrap;
    }

    .summary-card {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 8px;
        padding: 20px;
        flex: 1;
        min-width: 200px;
        margin: 0 10px 10px 0;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .summary-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .summary-title {
        font-size: 14px;
        color: #6c757d;
        margin-bottom: 10px;
    }

    .summary-value {
        font-size: 24px;
        font-weight: 700;
        color: #212529;
    }

    .summary-trend {
        font-size: 12px;
        margin-top: 5px;
    }

    .trend-up {
        color: #28a745;
    }

    .trend-down {
        color: #dc3545;
    }

    /* Mobile responsiveness */
    @media (max-width: 992px) {
        .transactions-header {
            flex-direction: column;
            align-items: flex-start;
        }

        .transactions-actions {
            margin-top: 15px;
            width: 100%;
        }

        .transaction-filter {
            flex: 1;
        }

        .transaction-table {
            display: block;
            overflow-x: auto;
            white-space: nowrap;
        }

        .summary-card {
            min-width: 150px;
        }
    }

    @media (max-width: 768px) {
        .transaction-table th,
        .transaction-table td {
            padding: 10px;
        }

        .summary-card {
            flex: 0 0 calc(50% - 10px);
            margin-right: 10px;
        }
    }

    @media (max-width: 576px) {
        .summary-card {
            flex: 0 0 100%;
            margin-right: 0;
        }

        .transactions-container {
            padding: 15px;
        }

        .status-badge {
            padding: 4px 8px;
            font-size: 10px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="enterprise-container">
    <div class="container">
        <!-- Premium Enterprise Header -->
        <div class="enterprise-header">
            <nav aria-label="breadcrumb" class="mb-3">
                <ol class="breadcrumb" style="background: rgba(255, 255, 255, 0.1); border-radius: 12px; padding: 0.75rem 1.5rem;">
                    <li class="breadcrumb-item"><a href="{% url 'index' %}" style="color: rgba(255, 255, 255, 0.8);">Home</a></li>
                    <li class="breadcrumb-item"><a href="/ads/" style="color: rgba(255, 255, 255, 0.8);">Ad Dashboard</a></li>
                    <li class="breadcrumb-item active" aria-current="page" style="color: white; font-weight: 600;">Transactions</li>
                </ol>
            </nav>

            <div class="text-center">
                <h1 class="enterprise-title">
                    <i class="fas fa-receipt me-3 text-warning"></i>
                    Transaction Center
                </h1>
                <p class="enterprise-subtitle">
                    Comprehensive payment history and transaction management for your advertising campaigns
                </p>
            </div>
        </div>

    <div class="transaction-summary">
        <div class="summary-card">
            <div class="summary-title">Total Transactions</div>
            <div class="summary-value">{{ page_obj.paginator.count|default:"0" }}</div>
            <div class="summary-trend trend-up">
                <i class="fas fa-arrow-up"></i> 12% from last month
            </div>
        </div>
        <div class="summary-card">
            <div class="summary-title">Total Spent</div>
            <div class="summary-value">{{ total_amount|default:"0.00" }} KSH</div>
            <div class="summary-trend trend-up">
                <i class="fas fa-arrow-up"></i> 8% from last month
            </div>
        </div>
        <div class="summary-card">
            <div class="summary-title">Pending Payments</div>
            <div class="summary-value">{{ pending_count|default:"0" }}</div>
            <div class="summary-trend trend-down">
                <i class="fas fa-arrow-down"></i> 5% from last month
            </div>
        </div>
        <div class="summary-card">
            <div class="summary-title">Successful Payments</div>
            <div class="summary-value">{{ paid_count|default:"0" }}</div>
            <div class="summary-trend trend-up">
                <i class="fas fa-arrow-up"></i> 15% from last month
            </div>
        </div>
    </div>

    <div class="transactions-container">
        <div class="transactions-header">
            <h2 class="transactions-title">Transaction History</h2>
            <div class="transactions-actions">
                <select class="transaction-filter">
                    <option value="all">All Transactions</option>
                    <option value="pending">Pending</option>
                    <option value="paid">Paid</option>
                    <option value="failed">Failed</option>
                    <option value="refunded">Refunded</option>
                </select>
                <select class="transaction-filter">
                    <option value="newest">Newest First</option>
                    <option value="oldest">Oldest First</option>
                    <option value="highest">Highest Amount</option>
                    <option value="lowest">Lowest Amount</option>
                </select>
            </div>
        </div>

        {% if page_obj %}
            <div class="table-responsive">
                <table class="transaction-table">
                    <thead>
                        <tr>
                            <th>Transaction ID</th>
                            <th>Ad</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Payment Method</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for transaction in page_obj %}
                            <tr>
                                <td>{{ transaction.transaction_id|default:"N/A" }}</td>
                                <td>{{ transaction.ad.title }}</td>
                                <td class="transaction-amount">{{ transaction.amount }} KSH</td>
                                <td>
                                    <span class="status-badge status-{{ transaction.status }}">
                                        {{ transaction.get_status_display }}
                                    </span>
                                </td>
                                <td>{{ transaction.get_payment_gateway_display }}</td>
                                <td>{{ transaction.timestamp|date:"M d, Y H:i" }}</td>
                                <td>
                                    <a href="{% url 'ads:transaction_detail' transaction_id=transaction.id %}" class="transaction-action">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            {% if page_obj.paginator.num_pages > 1 %}
                <nav aria-label="Transaction pagination" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1" aria-label="First">
                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}" aria-label="Last">
                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="empty-state">
                <div class="empty-state-icon">
                    <i class="fas fa-receipt"></i>
                </div>
                <h3 class="empty-state-title">No transactions found</h3>
                <p class="empty-state-description">
                    Transactions will appear here after you make payments for your ads.
                </p>
                <a href="/ads/create/" class="btn btn-primary">Create New Ad</a>
            </div>
        {% endif %}
    </div>

        <!-- Premium Action Buttons -->
        <div class="text-center mt-4 mb-4">
            <a href="/ads/" class="btn btn-outline-light btn-lg me-3">
                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
            </a>
            <a href="{% url 'ads:ad_create_consolidated' %}" class="btn btn-primary btn-lg">
                <i class="fas fa-plus me-2"></i>Create New Ad
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add animation to summary cards
        const summaryCards = document.querySelectorAll('.summary-card');
        summaryCards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
            card.classList.add('animate__animated', 'animate__fadeInUp');
        });

        // Add animation to transaction rows
        const transactionRows = document.querySelectorAll('.transaction-table tbody tr');
        transactionRows.forEach((row, index) => {
            row.style.animationDelay = `${index * 0.05}s`;
            row.classList.add('animate__animated', 'animate__fadeIn');
        });

        // Filter functionality
        const statusFilter = document.querySelector('.transaction-filter');
        if (statusFilter) {
            statusFilter.addEventListener('change', function() {
                // This would be implemented with backend filtering
                // For now, just reload the page to demonstrate the concept
                if (this.value !== 'all') {
                    window.location.href = `?status=${this.value}`;
                } else {
                    window.location.href = window.location.pathname;
                }
            });
        }
    });
</script>
{% endblock %}
