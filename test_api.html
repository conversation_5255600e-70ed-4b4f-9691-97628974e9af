<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        button {
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
            margin-top: 20px;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .suggestion {
            border: 1px solid #ddd;
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 5px;
        }
        .suggestion h3 {
            margin-top: 0;
        }
    </style>
</head>
<body>
    <h1>Test API</h1>
    <button id="testButton">Test API</button>
    <div id="results"></div>

    <script>
        document.getElementById('testButton').addEventListener('click', async () => {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<p>Testing API...</p>';

            try {
                // Get CSRF token from cookies
                function getCookie(name) {
                    let cookieValue = null;
                    if (document.cookie && document.cookie !== '') {
                        const cookies = document.cookie.split(';');
                        for (let i = 0; i < cookies.length; i++) {
                            const cookie = cookies[i].trim();
                            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                                break;
                            }
                        }
                    }
                    return cookieValue;
                }
                const csrftoken = getCookie('csrftoken');

                // Make the API request
                const response = await fetch('/ads/api/generate-suggestions/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': csrftoken
                    },
                    body: JSON.stringify({
                        language: 'english',
                        business_type: 'mobile app',
                        target_audience: 'young professionals',
                        tone: 'professional',
                        title: 'Mobile App Development'
                    })
                });

                // Check if the request was successful
                if (response.ok) {
                    const data = await response.json();
                    
                    // Display the results
                    let html = '<h2>Results</h2>';
                    html += `<p>Success: ${data.success}</p>`;
                    html += `<p>Source: ${data.source}</p>`;
                    
                    if (data.suggestions && data.suggestions.length > 0) {
                        html += `<h2>Suggestions (${data.suggestions.length})</h2>`;
                        
                        data.suggestions.forEach((suggestion, index) => {
                            html += `<div class="suggestion">`;
                            html += `<h3>Suggestion ${index + 1}</h3>`;
                            html += `<p><strong>Title:</strong> ${suggestion.title || 'No title'}</p>`;
                            html += `<p><strong>Content:</strong> ${suggestion.content || 'No content'}</p>`;
                            html += `</div>`;
                        });
                    } else {
                        html += '<p>No suggestions returned</p>';
                    }
                    
                    // Display the raw JSON
                    html += '<h2>Raw JSON</h2>';
                    html += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                    
                    resultsDiv.innerHTML = html;
                } else {
                    resultsDiv.innerHTML = `<p>Error: ${response.status} ${response.statusText}</p>`;
                    const text = await response.text();
                    resultsDiv.innerHTML += `<pre>${text}</pre>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<p>Error: ${error.message}</p>`;
            }
        });
    </script>
</body>
</html>
