/**
 * Ensure Suggestions Container
 * This script ensures that the aiSuggestionsContainer element exists
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Ensure Suggestions Container loaded');
    
    // Check if the aiSuggestionsContainer element exists
    let aiSuggestionsContainer = document.getElementById('aiSuggestionsContainer');
    
    // If it doesn't exist, create it
    if (!aiSuggestionsContainer) {
        console.log('Creating aiSuggestionsContainer element');
        
        // Create the container
        aiSuggestionsContainer = document.createElement('div');
        aiSuggestionsContainer.id = 'aiSuggestionsContainer';
        aiSuggestionsContainer.className = 'mt-4';
        aiSuggestionsContainer.style.display = 'none';
        
        // Find the smart engine options container
        const smartEngineOptions = document.getElementById('smartEngineOptions');
        
        if (smartEngineOptions) {
            // Insert the container after the smart engine options
            smartEngineOptions.parentNode.insertBefore(aiSuggestionsContainer, smartEngineOptions.nextSibling);
        } else {
            // If smart engine options doesn't exist, find the ad title field
            const adTitle = document.getElementById('adTitle');
            
            if (adTitle) {
                // Find the parent form group
                const formGroup = adTitle.closest('.mb-4');
                
                if (formGroup) {
                    // Insert the container after the form group
                    formGroup.parentNode.insertBefore(aiSuggestionsContainer, formGroup.nextSibling);
                } else {
                    // If all else fails, add it to the body
                    document.body.appendChild(aiSuggestionsContainer);
                }
            } else {
                // If all else fails, add it to the body
                document.body.appendChild(aiSuggestionsContainer);
            }
        }
    }
    
    console.log('aiSuggestionsContainer element:', aiSuggestionsContainer);
});
