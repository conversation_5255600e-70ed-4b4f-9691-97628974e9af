{% extends 'base.html' %}
{% load static %}

{% block title %}Admin - View Ad: {{ ad.title }}{% endblock %}

{% block extra_css %}
<!-- Include common ads CSS -->
{% include 'ads/includes/ads_common_css.html' %}
<style>
    .admin-badge {
        background-color: #1a237e;
        color: white;
        padding: 3px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 600;
        margin-left: 10px;
    }

    .ad-user {
        font-weight: 600;
        color: #1a237e;
    }

    .ad-details-list {
        list-style: none;
        padding: 0;
        margin: 0 0 20px;
    }

    .ad-details-item {
        display: flex;
        margin-bottom: 15px;
        border-bottom: 1px solid rgba(0,0,0,0.05);
        padding-bottom: 15px;
    }

    .ad-details-item:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }

    .ad-details-label {
        width: 150px;
        font-weight: 600;
        color: #1a237e;
    }

    .ad-details-value {
        flex: 1;
        color: #212529;
    }

    .ad-media {
        margin-bottom: 20px;
        border-radius: 8px;
        overflow: hidden;
    }

    .ad-media img {
        width: 100%;
        height: auto;
        object-fit: cover;
    }

    .ad-content {
        margin-bottom: 20px;
        line-height: 1.6;
    }

    .action-buttons {
        display: flex;
        gap: 10px;
        margin-top: 20px;
    }
</style>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="ads-page-header">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <nav aria-label="breadcrumb" class="ads-breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'index' %}">Home</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'ads:dashboard' %}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'ads:admin_pending_ads' %}">Admin - Pending Ads</a></li>
                        <li class="breadcrumb-item active" aria-current="page">{{ ad.title }}</li>
                    </ol>
                </nav>

                <h1 class="display-6 text-center mb-1 ads-page-title">{{ ad.title }}</h1>
                <p class="lead text-center mb-2 ads-page-subtitle">Ad Review</p>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="container mt-4">
    <div class="row">
        <div class="col-lg-8">
            <div class="ads-card animate__animated animate__fadeIn">
                <div class="ads-card-header">
                    <h3 class="ads-card-title">Ad Content</h3>
                </div>

                {% if ad.media %}
                <div class="ad-media">
                    <img src="{{ ad.media.url }}" alt="{{ ad.title }}" class="img-fluid">
                </div>
                {% endif %}

                <div class="ad-content">
                    {{ ad.content|linebreaks }}
                </div>

                {% if ad.cta_link %}
                <div class="text-center mb-3">
                    <a href="{{ ad.cta_link }}" target="_blank" class="ads-btn ads-btn-primary">
                        <i class="fas fa-external-link-alt"></i> Visit Website
                    </a>
                </div>
                {% endif %}

                <div class="action-buttons">
                    {% if ad.status == 'pending' %}
                        <a href="{% url 'ads:admin_approve_ad' ad.slug %}" class="ads-btn ads-btn-primary" onclick="return confirm('Are you sure you want to approve this ad?')">
                            <i class="fas fa-check"></i> Approve Ad
                        </a>
                        <a href="{% url 'ads:admin_reject_ad' ad.slug %}" class="btn btn-danger">
                            <i class="fas fa-times"></i> Reject Ad
                        </a>
                    {% elif ad.status == 'approved' %}
                        <div class="alert alert-success mb-3">
                            <i class="fas fa-check-circle"></i> This ad has been approved and is awaiting payment.
                        </div>
                    {% elif ad.status == 'active' %}
                        <div class="alert alert-info mb-3">
                            <i class="fas fa-info-circle"></i> This ad is active and running.
                        </div>
                    {% elif ad.status == 'rejected' %}
                        <div class="alert alert-danger mb-3">
                            <i class="fas fa-times-circle"></i> This ad was rejected.
                            {% if ad.rejection_reason %}
                                <p class="mt-2">Reason: {{ ad.rejection_reason }}</p>
                            {% endif %}
                        </div>
                    {% endif %}

                    <a href="{% url 'ads:admin_update_ad_status' ad.slug %}" class="ads-btn ads-btn-warning">
                        <i class="fas fa-edit"></i> Update Status
                    </a>

                    <a href="{% url 'ads:admin_pending_ads' %}" class="ads-btn ads-btn-outline">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="ads-card animate__animated animate__fadeIn">
                <div class="ads-card-header">
                    <h3 class="ads-card-title">Ad Details</h3>
                </div>

                <ul class="ad-details-list">
                    <li class="ad-details-item">
                        <div class="ad-details-label">Submitted By</div>
                        <div class="ad-details-value">
                            <span class="ad-user">{{ ad.user.username }}</span>
                        </div>
                    </li>
                    <li class="ad-details-item">
                        <div class="ad-details-label">Status</div>
                        <div class="ad-details-value">
                            <span class="badge bg-warning">{{ ad.get_status_display }}</span>
                        </div>
                    </li>
                    <li class="ad-details-item">
                        <div class="ad-details-label">Ad Type</div>
                        <div class="ad-details-value">{{ ad.ad_type.name }}</div>
                    </li>
                    <li class="ad-details-item">
                        <div class="ad-details-label">Campaign</div>
                        <div class="ad-details-value">
                            {% if ad.campaign %}
                                <a href="{% url 'ads:campaign_detail' ad.campaign.slug %}" class="text-primary">
                                    {{ ad.campaign.name }}
                                </a>
                            {% else %}
                                <span class="text-muted">No Campaign (Individual Ad)</span>
                            {% endif %}
                        </div>
                    </li>
                    <li class="ad-details-item">
                        <div class="ad-details-label">Start Date</div>
                        <div class="ad-details-value">{{ ad.start_date|date:"M d, Y H:i" }}</div>
                    </li>
                    <li class="ad-details-item">
                        <div class="ad-details-label">End Date</div>
                        <div class="ad-details-value">{{ ad.end_date|date:"M d, Y H:i" }}</div>
                    </li>
                    <li class="ad-details-item">
                        <div class="ad-details-label">Duration</div>
                        <div class="ad-details-value">{{ ad.end_date|timeuntil:ad.start_date }}</div>
                    </li>
                    <li class="ad-details-item">
                        <div class="ad-details-label">Target Location</div>
                        <div class="ad-details-value">{{ ad.target_location|default:"Not specified" }}</div>
                    </li>
                    <li class="ad-details-item">
                        <div class="ad-details-label">Target Audience</div>
                        <div class="ad-details-value">{{ ad.target_audience|default:"Not specified" }}</div>
                    </li>
                    <li class="ad-details-item">
                        <div class="ad-details-label">AI Enhancement</div>
                        <div class="ad-details-value">
                            {{ ad.requires_ai|yesno:"Yes,No" }}
                            {% if ad.used_ai %}
                                <span class="badge bg-info ms-2">AI-Generated</span>
                            {% endif %}
                        </div>
                    </li>
                    {% if ad.used_ai %}
                    <li class="ad-details-item">
                        <div class="ad-details-label">AI Language</div>
                        <div class="ad-details-value">
                            {% if ad.ai_language %}
                                {{ ad.ai_language|title }}
                            {% else %}
                                <span class="text-muted">Not specified</span>
                            {% endif %}
                        </div>
                    </li>
                    <li class="ad-details-item">
                        <div class="ad-details-label">AI Suggestion</div>
                        <div class="ad-details-value">
                            {% if ad.ai_suggestion %}
                                <div class="ai-suggestion-preview">
                                    <div class="ai-suggestion-title fw-bold">{{ ad.ai_suggestion.title }}</div>
                                    <div class="ai-suggestion-content small text-muted">{{ ad.ai_suggestion.content }}</div>
                                </div>
                            {% else %}
                                <span class="text-muted">No suggestion data available</span>
                            {% endif %}
                        </div>
                    </li>
                    {% endif %}
                    <li class="ad-details-item">
                        <div class="ad-details-label">Social Sharing</div>
                        <div class="ad-details-value">{{ ad.wants_social|yesno:"Yes,No" }}</div>
                    </li>
                    <li class="ad-details-item">
                        <div class="ad-details-label">Base Price</div>
                        <div class="ad-details-value">{{ ad.base_pricing }} KSH</div>
                    </li>
                    <li class="ad-details-item">
                        <div class="ad-details-label">Final Price</div>
                        <div class="ad-details-value">{{ ad.final_pricing }} KSH</div>
                    </li>
                    <li class="ad-details-item">
                        <div class="ad-details-label">Submitted On</div>
                        <div class="ad-details-value">{{ ad.updated_at|date:"M d, Y H:i" }}</div>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
