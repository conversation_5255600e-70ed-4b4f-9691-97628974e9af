/**
 * Select2 Campaign Dropdown JavaScript
 * Handles the campaign dropdown functionality in the ad creation form
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize Select2 for campaign dropdown with AJAX
    $('.campaign-select').select2({
        placeholder: "Search for a campaign or select 'No Campaign'",
        allowClear: true,
        width: '100%',
        dropdownParent: $('#adCreationForm'),
        ajax: {
            url: campaignSearchUrl, // This variable should be defined in the template
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    term: params.term || '',
                    page: params.page || 1
                };
            },
            processResults: function (data, params) {
                return {
                    results: data.results,
                    pagination: data.pagination
                };
            },
            cache: true
        },
        templateResult: formatCampaign,
        templateSelection: formatCampaignSelection
    });

    // Format campaign in dropdown
    function formatCampaign(campaign) {
        if (!campaign.id) {
            return campaign.text;
        }

        if (campaign.id === '') {
            return $('<span><i class="fas fa-times-circle me-2"></i>' + campaign.text + '</span>');
        }

        var $container = $(
            '<div class="select2-result-campaign">' +
                '<div class="select2-result-campaign__title">' + campaign.text + '</div>' +
                '<div class="select2-result-campaign__meta">' +
                    '<span class="badge ' + getBadgeClass(campaign.status) + ' me-2">' +
                        (campaign.status ? campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1) : '') +
                    '</span>' +
                    '<small class="text-muted">' + (campaign.created_at || '') + '</small>' +
                '</div>' +
                (campaign.description ? '<div class="select2-result-campaign__description">' + campaign.description + '</div>' : '') +
            '</div>'
        );

        return $container;
    }

    // Format selected campaign
    function formatCampaignSelection(campaign) {
        if (!campaign.id) {
            return campaign.text;
        }

        if (campaign.id === '') {
            return 'No Campaign (Individual Ad)';
        }

        return campaign.text;
    }

    // Get badge class based on status
    function getBadgeClass(status) {
        switch(status) {
            case 'active':
                return 'bg-success';
            case 'paused':
                return 'bg-warning';
            case 'completed':
                return 'bg-info';
            case 'draft':
                return 'bg-secondary';
            default:
                return 'bg-secondary';
        }
    }
});
