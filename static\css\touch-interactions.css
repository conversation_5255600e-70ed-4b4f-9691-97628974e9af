/* 
 * Touch Interactions CSS
 * Enhances touch interactions across the site for better mobile experience
 */

/* Touch-friendly target sizes */
.touch-friendly {
    min-height: 44px;
    min-width: 44px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.touch-friendly-enhanced {
    padding: 10px;
    margin: 2px;
}

/* Improved focus states for touch */
.touch-focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(58, 123, 213, 0.5);
    border-color: #3a7bd5;
}

/* Enhanced select elements */
.touch-select {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 16px;
    padding-right: 30px !important;
}

/* Enhanced labels */
.touch-label {
    display: inline-block;
    margin-bottom: 8px;
    font-weight: 500;
    cursor: pointer;
}

/* Fix for iOS 100vh issue */
.is-ios .full-height {
    height: 100vh; /* Fallback */
    height: calc(var(--vh, 1vh) * 100);
}

/* Fix for keyboard open on Android */
.is-android.keyboard-open .fixed-bottom,
.is-android.keyboard-open .enterprise-bottom-nav {
    position: absolute;
}

/* Improved scrolling */
.scrollable,
[data-scrollable],
.table-responsive {
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
    overflow: auto;
}

/* Prevent pull-to-refresh on iOS */
.is-ios {
    overscroll-behavior-y: none;
}

/* Prevent text selection on interactive elements */
button, 
.btn,
.nav-link,
.navbar-brand,
.dropdown-item,
.mobile-menu-item,
.bottom-nav-item {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
}

/* Improved tap targets for checkboxes and radio buttons */
input[type="checkbox"],
input[type="radio"] {
    min-width: 20px;
    min-height: 20px;
}

/* Improved tap targets for form controls */
.form-control,
.form-select,
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="tel"],
input[type="url"],
input[type="search"],
textarea,
select {
    min-height: 44px;
}

/* Improved tap targets for buttons */
.btn {
    min-height: 44px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* Improved tap targets for links */
a {
    touch-action: manipulation;
}

/* Improved tap targets for dropdown toggles */
.dropdown-toggle {
    padding-right: 30px !important;
}

/* Improved tap targets for navbar items */
.navbar-nav .nav-link,
.new-navbar-link {
    min-height: 44px;
    display: flex;
    align-items: center;
}

/* Improved tap targets for mobile menu items */
.mobile-menu-item,
.submenu-item {
    min-height: 44px;
    display: flex;
    align-items: center;
}

/* Improved tap targets for bottom navigation */
.bottom-nav-item {
    min-height: 44px;
}

/* Improved tap targets for card actions */
.card-actions .btn {
    margin: 0 5px;
}

/* Improved tap targets for pagination */
.pagination .page-link {
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Improved tap targets for table actions */
.table .btn {
    margin: 2px;
}

/* Improved tap targets for form actions */
.form-actions .btn {
    margin: 5px;
}

/* Improved tap targets for modal actions */
.modal-footer .btn {
    margin: 5px;
    min-width: 80px;
}

/* Improved tap targets for alert actions */
.alert .btn-close {
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Improved tap targets for accordion headers */
.accordion-button {
    min-height: 44px;
}

/* Improved tap targets for tabs */
.nav-tabs .nav-link {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Improved tap targets for list group items */
.list-group-item {
    min-height: 44px;
}

/* Improved tap targets for close buttons */
.btn-close {
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Improved tap targets for notification actions */
.notification-action {
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Improved tap targets for social icons */
.social-icon {
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Improved tap targets for footer links */
.footer-link {
    min-height: 44px;
    display: flex;
    align-items: center;
}

/* Improved tap targets for newsletter button */
.newsletter-button {
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Improved tap targets for mobile menu close button */
.mobile-menu-close {
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Improved tap targets for mobile menu toggle */
.new-navbar-toggle {
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Improved tap targets for notification bell */
.new-navbar-notification-icon {
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Improved tap targets for user avatar */
.new-navbar-user-avatar {
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Improved tap targets for auth button */
.new-navbar-auth {
    min-height: 44px;
    display: flex;
    align-items: center;
}

/* Improved tap targets for notification dropdown actions */
.new-navbar-notification-footer-buttons button,
.new-navbar-notification-footer-buttons a {
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Improved tap targets for dropdown items */
.new-navbar-dropdown-item {
    min-height: 44px;
    display: flex;
    align-items: center;
}

/* Improved tap targets for mobile menu section title */
.mobile-menu-section-title {
    min-height: 44px;
    display: flex;
    align-items: center;
}

/* Improved tap targets for mobile user info */
.mobile-user-info {
    min-height: 44px;
}

/* Improved tap targets for action dropdown items */
.action-dropdown li {
    min-height: 44px;
    display: flex;
    align-items: center;
    padding: 0 15px;
}

/* Improved tap targets for notification item link */
.notification-item-link {
    min-height: 44px;
}

/* Improved tap targets for analytics filters */
.analytics-filters .form-control,
.analytics-filters .form-select,
.analytics-filters .btn {
    min-height: 44px;
}

/* Improved tap targets for chart controls */
.chart-control {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Improved tap targets for top ads sort */
#top-ads-sort {
    min-height: 44px;
}

/* Improved tap targets for period filter */
#period-filter,
#metric-filter {
    min-height: 44px;
}

/* Improved tap targets for schedule report form */
#schedule-report-form .form-control,
#schedule-report-form .form-select {
    min-height: 44px;
}

/* Improved tap targets for modal buttons */
.modal-footer .btn {
    min-height: 44px;
}

/* Improved tap targets for premium modal */
#premiumFeatureModal .btn {
    min-height: 44px;
}

/* Improved tap targets for premium benefits */
.benefit-item {
    min-height: 44px;
}

/* Improved tap targets for contact info */
.contact-item {
    min-height: 44px;
}

/* Improved tap targets for newsletter input */
.newsletter-input {
    min-height: 44px;
}

/* Improved tap targets for footer bottom links */
.footer-bottom-link {
    min-height: 44px;
    display: inline-flex;
    align-items: center;
}
