{% extends 'qrcode_app/base.html' %}
{% load static %}

{% block title %}Short Links Management{% endblock %}

{% block extra_css %}
<style>
    .short-links-management {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }
    
    .links-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .links-header {
        text-align: center;
        color: white;
        margin-bottom: 3rem;
    }
    
    .links-header h1 {
        font-size: 3rem;
        font-weight: bold;
        margin-bottom: 1rem;
    }
    
    .link-item {
        border: 1px solid #dee2e6;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }
    
    .link-item:hover {
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }
    
    .link-preview {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }
    
    .link-code {
        font-family: 'Courier New', monospace;
        background: #f8f9fa;
        padding: 0.5rem 1rem;
        border-radius: 5px;
        font-weight: bold;
        color: #667eea;
    }
    
    .link-stats {
        display: flex;
        gap: 1rem;
        align-items: center;
    }
    
    .stat-item {
        text-align: center;
    }
    
    .stat-value {
        font-size: 1.2rem;
        font-weight: bold;
        color: #667eea;
    }
    
    .stat-label {
        font-size: 0.8rem;
        color: #6c757d;
    }
    
    .coming-soon {
        text-align: center;
        padding: 3rem;
        color: #6c757d;
    }
    
    .coming-soon i {
        font-size: 4rem;
        margin-bottom: 1rem;
        color: #667eea;
    }
</style>
{% endblock %}

{% block content %}
<div class="short-links-management">
    <div class="container">
        <!-- Header -->
        <div class="links-header">
            <h1><i class="fas fa-link me-3"></i>Short Links Management</h1>
            <p>Manage your professional short URLs and track their performance</p>
        </div>
        
        <!-- Short Links Cards -->
        <div class="links-card">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h4><i class="fas fa-external-link-alt me-2"></i>Your Short Links</h4>
                <button class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Create New Short Link
                </button>
            </div>
            
            {% if short_links %}
                {% for link in short_links %}
                <div class="link-item">
                    <div class="link-preview">
                        <div>
                            <h5 class="mb-1">{{ link.title|default:"Untitled Link" }}</h5>
                            <div class="link-code">{{ link.get_short_url }}</div>
                            <small class="text-muted">→ {{ link.target_url|truncatechars:50 }}</small>
                        </div>
                        
                        <div class="link-stats">
                            <div class="stat-item">
                                <div class="stat-value">{{ link.click_count }}</div>
                                <div class="stat-label">Clicks</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">
                                    {% if link.status == 'active' %}
                                    <span class="badge bg-success">Active</span>
                                    {% elif link.status == 'expired' %}
                                    <span class="badge bg-warning">Expired</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{{ link.get_status_display }}</span>
                                    {% endif %}
                                </div>
                                <div class="stat-label">Status</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            {% if link.expires_at %}
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                Expires: {{ link.expires_at|date:"M d, Y H:i" }}
                            </small>
                            {% endif %}
                            {% if link.password %}
                            <span class="badge bg-warning ms-2">
                                <i class="fas fa-lock"></i> Protected
                            </span>
                            {% endif %}
                        </div>
                        <div>
                            <button class="btn btn-sm btn-outline-primary me-2">
                                <i class="fas fa-chart-bar"></i> Analytics
                            </button>
                            <button class="btn btn-sm btn-outline-secondary me-2">
                                <i class="fas fa-edit"></i> Edit
                            </button>
                            <button class="btn btn-sm btn-outline-danger">
                                <i class="fas fa-trash"></i> Delete
                            </button>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="coming-soon">
                    <i class="fas fa-link"></i>
                    <h4>No Short Links Yet</h4>
                    <p class="mb-4">Create your first professional short link to get started</p>
                    <button class="btn btn-primary btn-lg">
                        <i class="fas fa-plus me-2"></i>Create Your First Short Link
                    </button>
                </div>
            {% endif %}
        </div>
        
        <!-- Features Info -->
        <div class="links-card">
            <h4><i class="fas fa-star me-2"></i>Short Link Features</h4>
            <div class="row">
                <div class="col-md-6">
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Custom domains support</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Password protection</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Expiry date controls</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Click tracking and analytics</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>QR code integration</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Professional branding</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Back to Dashboard -->
        <div class="text-center">
            <a href="{% url 'enterprise_dashboard' %}" class="btn btn-outline-light btn-lg">
                <i class="fas fa-arrow-left me-2"></i>Back to Enterprise Dashboard
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Placeholder for short links management functionality
console.log('Short links management loaded');
</script>
{% endblock %}
