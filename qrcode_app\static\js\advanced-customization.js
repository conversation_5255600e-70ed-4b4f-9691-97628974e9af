/**
 * Advanced QR Code Customization Module
 * 
 * This module provides enterprise-grade customization options for QR codes,
 * including advanced styling, branding, and template features.
 */

// Advanced customization options
const ADVANCED_OPTIONS = {
    // QR Code Shapes
    shapes: {
        standard: {
            name: 'Standard',
            description: 'Classic square QR code'
        },
        rounded: {
            name: 'Rounded',
            description: 'QR code with rounded corners'
        },
        circular: {
            name: 'Circular',
            description: 'QR code with circular pattern'
        },
        dotted: {
            name: 'Dotted',
            description: 'QR code with dot pattern'
        },
        diamond: {
            name: 'Diamond',
            description: 'QR code with diamond pattern'
        },
        hexagonal: {
            name: 'Hexagonal',
            description: 'QR code with hexagonal pattern'
        }
    },
    
    // Eye Styles
    eyeStyles: {
        standard: {
            name: 'Standard',
            description: 'Classic square eyes'
        },
        rounded: {
            name: 'Rounded',
            description: 'Eyes with rounded corners'
        },
        circular: {
            name: 'Circular',
            description: 'Circular eyes'
        },
        leafy: {
            name: 'Leafy',
            description: 'Eyes with leaf-like design'
        },
        diamond: {
            name: '<PERSON>',
            description: 'Diamond-shaped eyes'
        },
        custom: {
            name: 'Custom',
            description: 'Upload custom eye design'
        }
    },
    
    // Frame Styles
    frameStyles: {
        none: {
            name: 'None',
            description: 'No frame'
        },
        simple: {
            name: 'Simple',
            description: 'Simple border frame'
        },
        rounded: {
            name: 'Rounded',
            description: 'Rounded border frame'
        },
        shadow: {
            name: 'Shadow',
            description: 'Frame with shadow effect'
        },
        corporate: {
            name: 'Corporate',
            description: 'Professional corporate frame'
        },
        branded: {
            name: 'Branded',
            description: 'Frame with brand colors'
        },
        custom: {
            name: 'Custom',
            description: 'Upload custom frame design'
        }
    },
    
    // Background Patterns
    backgroundPatterns: {
        solid: {
            name: 'Solid',
            description: 'Solid background color'
        },
        gradient: {
            name: 'Gradient',
            description: 'Gradient background'
        },
        radial: {
            name: 'Radial',
            description: 'Radial gradient background'
        },
        dots: {
            name: 'Dots',
            description: 'Dotted pattern background'
        },
        lines: {
            name: 'Lines',
            description: 'Line pattern background'
        },
        grid: {
            name: 'Grid',
            description: 'Grid pattern background'
        },
        custom: {
            name: 'Custom',
            description: 'Upload custom background'
        }
    },
    
    // Logo Styles
    logoStyles: {
        standard: {
            name: 'Standard',
            description: 'Standard logo placement'
        },
        circle: {
            name: 'Circle',
            description: 'Logo in circular container'
        },
        square: {
            name: 'Square',
            description: 'Logo in square container'
        },
        rounded: {
            name: 'Rounded',
            description: 'Logo with rounded corners'
        },
        shield: {
            name: 'Shield',
            description: 'Logo in shield-shaped container'
        },
        transparent: {
            name: 'Transparent',
            description: 'Logo with transparent background'
        }
    },
    
    // Color Schemes
    colorSchemes: {
        standard: {
            name: 'Standard',
            description: 'Black and white',
            foreground: '#000000',
            background: '#FFFFFF'
        },
        corporate: {
            name: 'Corporate Blue',
            description: 'Professional blue theme',
            foreground: '#1D4ED8',
            background: '#FFFFFF'
        },
        modern: {
            name: 'Modern Gray',
            description: 'Sleek gray theme',
            foreground: '#374151',
            background: '#F9FAFB'
        },
        vibrant: {
            name: 'Vibrant',
            description: 'Vibrant purple theme',
            foreground: '#7C3AED',
            background: '#FFFFFF'
        },
        nature: {
            name: 'Nature',
            description: 'Green nature theme',
            foreground: '#10B981',
            background: '#ECFDF5'
        },
        elegant: {
            name: 'Elegant',
            description: 'Elegant dark theme',
            foreground: '#1F2937',
            background: '#F3F4F6'
        },
        custom: {
            name: 'Custom',
            description: 'Custom color scheme',
            foreground: '#000000',
            background: '#FFFFFF'
        }
    },
    
    // Templates
    templates: {
        standard: {
            name: 'Standard',
            description: 'Classic QR code'
        },
        business: {
            name: 'Business Card',
            description: 'QR code with business card layout'
        },
        event: {
            name: 'Event',
            description: 'QR code for events and tickets'
        },
        product: {
            name: 'Product',
            description: 'QR code for product packaging'
        },
        social: {
            name: 'Social Media',
            description: 'QR code for social media profiles'
        },
        mobile: {
            name: 'Mobile App',
            description: 'QR code for app downloads'
        },
        document: {
            name: 'Document',
            description: 'QR code for documents and PDFs'
        },
        custom: {
            name: 'Custom',
            description: 'Custom template design'
        }
    },
    
    // Animation Effects
    animationEffects: {
        none: {
            name: 'None',
            description: 'No animation'
        },
        fade: {
            name: 'Fade',
            description: 'Fade-in animation'
        },
        pulse: {
            name: 'Pulse',
            description: 'Pulsing animation'
        },
        scan: {
            name: 'Scan',
            description: 'Scanning line animation'
        },
        glow: {
            name: 'Glow',
            description: 'Glowing effect animation'
        },
        custom: {
            name: 'Custom',
            description: 'Custom animation effect'
        }
    }
};

/**
 * Initialize advanced customization options
 */
function initAdvancedCustomization() {
    // Add shape options
    populateSelectOptions('qr-shape', ADVANCED_OPTIONS.shapes);
    
    // Add eye style options
    populateSelectOptions('eye-style', ADVANCED_OPTIONS.eyeStyles);
    
    // Add frame style options
    populateSelectOptions('frame-style', ADVANCED_OPTIONS.frameStyles);
    
    // Add background pattern options
    populateSelectOptions('background-pattern', ADVANCED_OPTIONS.backgroundPatterns);
    
    // Add logo style options
    populateSelectOptions('logo-style', ADVANCED_OPTIONS.logoStyles);
    
    // Add color scheme options
    populateSelectOptions('color-scheme', ADVANCED_OPTIONS.colorSchemes);
    
    // Add template options
    populateSelectOptions('qr-template', ADVANCED_OPTIONS.templates);
    
    // Add animation effect options
    populateSelectOptions('animation-effect', ADVANCED_OPTIONS.animationEffects);
    
    // Initialize event listeners
    initAdvancedEventListeners();
}

/**
 * Populate select element with options
 * @param {string} selectId - ID of select element
 * @param {Object} options - Options object
 */
function populateSelectOptions(selectId, options) {
    const selectElement = document.getElementById(selectId);
    if (!selectElement) return;
    
    // Clear existing options
    selectElement.innerHTML = '';
    
    // Add options
    Object.keys(options).forEach(key => {
        const option = document.createElement('option');
        option.value = key;
        option.textContent = options[key].name;
        option.setAttribute('data-description', options[key].description);
        selectElement.appendChild(option);
    });
    
    // Add tooltip with description
    selectElement.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        const description = selectedOption.getAttribute('data-description');
        if (description) {
            const tooltip = document.createElement('div');
            tooltip.className = 'option-tooltip';
            tooltip.textContent = description;
            
            // Remove any existing tooltips
            const existingTooltip = this.parentNode.querySelector('.option-tooltip');
            if (existingTooltip) {
                existingTooltip.remove();
            }
            
            this.parentNode.appendChild(tooltip);
            
            // Remove tooltip after 3 seconds
            setTimeout(() => {
                if (tooltip.parentNode) {
                    tooltip.remove();
                }
            }, 3000);
        }
    });
}

/**
 * Initialize event listeners for advanced customization options
 */
function initAdvancedEventListeners() {
    // QR Shape change
    const qrShapeSelect = document.getElementById('qr-shape');
    if (qrShapeSelect) {
        qrShapeSelect.addEventListener('change', function() {
            updateQRCodePreview();
        });
    }
    
    // Eye Style change
    const eyeStyleSelect = document.getElementById('eye-style');
    if (eyeStyleSelect) {
        eyeStyleSelect.addEventListener('change', function() {
            updateQRCodePreview();
        });
    }
    
    // Frame Style change
    const frameStyleSelect = document.getElementById('frame-style');
    if (frameStyleSelect) {
        frameStyleSelect.addEventListener('change', function() {
            updateQRCodePreview();
        });
    }
    
    // Background Pattern change
    const backgroundPatternSelect = document.getElementById('background-pattern');
    if (backgroundPatternSelect) {
        backgroundPatternSelect.addEventListener('change', function() {
            updateQRCodePreview();
        });
    }
    
    // Color Scheme change
    const colorSchemeSelect = document.getElementById('color-scheme');
    if (colorSchemeSelect) {
        colorSchemeSelect.addEventListener('change', function() {
            const selectedScheme = ADVANCED_OPTIONS.colorSchemes[this.value];
            if (selectedScheme && selectedScheme.foreground && selectedScheme.background) {
                // Update color pickers
                const foregroundColorPicker = document.getElementById('foreground-color');
                const backgroundColorPicker = document.getElementById('background-color');
                
                if (foregroundColorPicker) {
                    foregroundColorPicker.value = selectedScheme.foreground;
                    foregroundColorPicker.dispatchEvent(new Event('input'));
                }
                
                if (backgroundColorPicker) {
                    backgroundColorPicker.value = selectedScheme.background;
                    backgroundColorPicker.dispatchEvent(new Event('input'));
                }
            }
            
            updateQRCodePreview();
        });
    }
    
    // Animation Effect change
    const animationEffectSelect = document.getElementById('animation-effect');
    if (animationEffectSelect) {
        animationEffectSelect.addEventListener('change', function() {
            updateQRCodePreview();
        });
    }
}

/**
 * Update QR code preview with advanced customization options
 */
function updateQRCodePreview() {
    // Check if live preview is enabled
    const livePreviewToggle = document.getElementById('live-preview-toggle');
    if (livePreviewToggle && !livePreviewToggle.checked) return;
    
    // Get current QR data
    const qrData = getQRData(true); // true = silent mode
    if (!qrData) return;
    
    // Show loading in preview area
    const qrCodeContainer = document.getElementById('qr-code-container');
    if (qrCodeContainer) {
        qrCodeContainer.innerHTML = '<div class="loading-spinner"></div>';
        
        // Generate QR code with current settings
        setTimeout(() => {
            generateQRCodeWithAdvancedOptions(qrData, true); // true = preview mode
        }, 300);
    }
}

/**
 * Generate QR code with advanced customization options
 * @param {string} qrData - QR code data
 * @param {boolean} isPreview - Whether this is a preview
 */
function generateQRCodeWithAdvancedOptions(qrData, isPreview = false) {
    // Get basic customization options
    const fgColor = document.getElementById('foreground-color')?.value || '#000000';
    const bgColor = document.getElementById('background-color')?.value || '#FFFFFF';
    const errorCorrection = document.getElementById('error-correction')?.value || 'H';
    
    // Get advanced customization options
    const qrShape = document.getElementById('qr-shape')?.value || 'standard';
    const eyeStyle = document.getElementById('eye-style')?.value || 'standard';
    const frameStyle = document.getElementById('frame-style')?.value || 'none';
    const backgroundPattern = document.getElementById('background-pattern')?.value || 'solid';
    const logoStyle = document.getElementById('logo-style')?.value || 'standard';
    const animationEffect = document.getElementById('animation-effect')?.value || 'none';
    
    // Call the existing QR code generation function with advanced options
    if (typeof generateQRCodeWithTemplate === 'function') {
        generateQRCodeWithTemplate(qrData, isPreview, {
            qrShape,
            eyeStyle,
            frameStyle,
            backgroundPattern,
            logoStyle,
            animationEffect
        });
    } else {
        console.error('generateQRCodeWithTemplate function not found');
    }
}

// Export functions for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        ADVANCED_OPTIONS,
        initAdvancedCustomization,
        updateQRCodePreview,
        generateQRCodeWithAdvancedOptions
    };
}
