import os
import django
import random
from datetime import timedelta

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'qrcode_project.settings')
django.setup()

# Import models
from django.contrib.auth.models import User
from django.utils import timezone
from ads.models import Ad, Transaction

def create_test_transactions():
    # Get the first user
    user = User.objects.first()
    if not user:
        print('No users found in the database')
        return

    # Get all ads
    ads = Ad.objects.all()
    if not ads:
        print('No ads found in the database')
        return

    # Payment gateways
    payment_gateways = ['mpesa', 'card', 'bank', 'paypal']
    
    # Statuses
    statuses = ['pending', 'paid', 'failed', 'refunded']
    
    # Create transactions
    transactions_created = 0
    for ad in ads:
        # Create 1-3 transactions per ad
        for _ in range(random.randint(1, 3)):
            # Determine status
            status = random.choice(statuses)
            
            # Create transaction
            transaction = Transaction.objects.create(
                user=user,
                ad=ad,
                amount=ad.final_pricing or random.randint(500, 5000),
                status=status,
                payment_gateway=random.choice(payment_gateways),
                transaction_id=f'{random.choice(payment_gateways).upper()}{ad.id}{random.randint(100000, 999999)}',
                timestamp=timezone.now() - timedelta(days=random.randint(0, 30))
            )
            transactions_created += 1
            print(f'Created transaction {transaction.id} for ad: {ad.title} (Status: {status})')
    
    print(f'Successfully created {transactions_created} test transactions')

if __name__ == '__main__':
    create_test_transactions()
