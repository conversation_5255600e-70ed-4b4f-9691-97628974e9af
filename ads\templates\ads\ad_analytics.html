{% extends 'base.html' %}
{% load static %}

{% block title %}Analytics - {{ ad.title }}{% endblock %}

{% block extra_css %}
<!-- Google Fonts - Poppins and Montserrat -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

<!-- Include common ads CSS -->
{% include 'ads/includes/ads_common_css.html' %}
<link rel="stylesheet" href="{% static 'ads/css/analytics.css' %}">

<style>
    /* Ultra-Premium Enterprise Ad Analytics Styling */
    body {
        background: linear-gradient(135deg, #000000 0%, #0a0a0a 10%, #1a1a2e 25%, #16213e 40%, #0f3460 60%, #533483 80%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        position: relative;
        overflow-x: hidden;
    }

    /* Premium animated background with enterprise patterns */
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 50% 50%, rgba(102, 126, 234, 0.7) 0%, transparent 40%),
            radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.3) 0%, transparent 35%),
            radial-gradient(circle at 20% 80%, rgba(118, 75, 162, 0.6) 0%, transparent 45%),
            radial-gradient(circle at 40% 60%, rgba(83, 52, 131, 0.5) 0%, transparent 30%),
            radial-gradient(circle at 60% 40%, rgba(102, 126, 234, 0.4) 0%, transparent 40%);
        z-index: -1;
        animation: enterpriseAnalyticsFloat 85s ease-in-out infinite;
    }

    @keyframes enterpriseAnalyticsFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        25% { transform: translateY(-60px) rotate(5deg); }
        50% { transform: translateY(-50px) rotate(-5deg); }
        75% { transform: translateY(-70px) rotate(2.5deg); }
    }

    /* Enterprise Container */
    .enterprise-container {
        position: relative;
        z-index: 1;
        padding: 2rem 0;
        min-height: 100vh;
    }

    /* Premium Header Section */
    .enterprise-header {
        background: linear-gradient(135deg, rgba(26, 35, 126, 0.95) 0%, rgba(40, 53, 147, 0.95) 50%, rgba(63, 81, 181, 0.95) 100%);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2.5rem;
        margin-bottom: 2rem;
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        position: relative;
        overflow: hidden;
        animation: slideInDown 0.8s ease-out;
    }

    .enterprise-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
        animation: shimmer 3s ease-in-out infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    .enterprise-title {
        font-family: 'Montserrat', sans-serif !important;
        font-size: 2.5rem;
        font-weight: 700;
        color: #ffffff;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 2;
    }

    .enterprise-subtitle {
        font-size: 1.1rem;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 1.5rem;
        font-weight: 400;
        position: relative;
        z-index: 2;
    }

    .enterprise-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 1.5rem;
        margin-top: 1.5rem;
        position: relative;
        z-index: 2;
    }

    .enterprise-meta-item {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        padding: 0.75rem 1.25rem;
        color: rgba(255, 255, 255, 0.9);
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
    }

    .enterprise-meta-item:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-2px);
    }

    .enterprise-meta-item i {
        color: #667eea;
    }

    /* Premium Enterprise Cards */
    .enterprise-card {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        overflow: hidden;
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.15),
            0 0 0 1px rgba(255, 255, 255, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        animation: slideInUp 0.8s ease-out 0.2s both;
        margin-bottom: 2rem;
        transition: all 0.3s ease;
    }

    .enterprise-card:hover {
        transform: translateY(-5px);
        box-shadow:
            0 35px 70px rgba(0, 0, 0, 0.2),
            0 0 0 1px rgba(255, 255, 255, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.4);
    }

    .enterprise-card-header {
        background: linear-gradient(135deg, #1a237e 0%, #283593 100%);
        color: white;
        padding: 1.5rem 2rem;
        font-weight: 600;
        font-size: 1.2rem;
        position: relative;
    }

    .enterprise-card-header::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    }

    .enterprise-card-title {
        font-family: 'Montserrat', sans-serif !important;
        font-weight: 700;
        margin: 0;
        position: relative;
        z-index: 2;
    }

    .enterprise-card-body {
        padding: 2rem;
    }

    /* Premium Statistics */
    .enterprise-stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .enterprise-stat-card {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
        border-radius: 16px;
        padding: 1.5rem;
        transition: all 0.3s ease;
        border: 2px solid transparent;
        position: relative;
        overflow: hidden;
        text-align: center;
    }

    .enterprise-stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
        transition: left 0.5s;
    }

    .enterprise-stat-card:hover::before {
        left: 100%;
    }

    .enterprise-stat-card:hover {
        transform: translateY(-5px);
        border-color: rgba(102, 126, 234, 0.3);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    }

    .enterprise-stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        margin: 0 auto 1rem;
        position: relative;
        z-index: 2;
    }

    .enterprise-stat-icon.impressions {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
    }

    .enterprise-stat-icon.clicks {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);
    }

    .enterprise-stat-icon.ctr {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        box-shadow: 0 8px 20px rgba(245, 158, 11, 0.3);
    }

    .enterprise-stat-icon.conversions {
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        box-shadow: 0 8px 20px rgba(139, 92, 246, 0.3);
    }

    .enterprise-stat-value {
        font-size: 2rem;
        font-weight: 700;
        color: #1a237e;
        margin-bottom: 0.5rem;
    }

    .enterprise-stat-label {
        font-weight: 600;
        color: #667eea;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 0.5rem;
    }

    .enterprise-stat-description {
        font-size: 0.85rem;
        color: #6b7280;
        font-weight: 500;
    }

    /* Premium Chart Styling */
    .chart-placeholder {
        height: 300px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
        border-radius: 16px;
        color: #667eea;
        font-weight: 600;
        border: 2px dashed rgba(102, 126, 234, 0.2);
    }

    .no-data-message {
        text-align: center;
        padding: 2rem;
        color: #667eea;
        font-weight: 600;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
        border-radius: 16px;
        border: 2px dashed rgba(102, 126, 234, 0.2);
    }

    .ctr-value {
        color: #667eea !important;
        font-weight: 700 !important;
    }

    /* Premium Table Styling */
    .enterprise-table {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border: none;
    }

    .enterprise-table thead th {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 0.85rem;
        color: #1a237e;
        border: none;
        padding: 1.2rem 1.5rem;
    }

    .enterprise-table tbody td {
        padding: 1.2rem 1.5rem;
        border: none;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        vertical-align: middle;
        font-weight: 500;
    }

    .enterprise-table tbody tr:hover {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    }
</style>
{% endblock %}

{% block content %}
<div class="enterprise-container">
    <div class="container">
        <!-- Premium Header Section -->
        <div class="enterprise-header">
            <nav aria-label="breadcrumb" class="mb-3">
                <ol class="breadcrumb" style="background: rgba(255, 255, 255, 0.1); border-radius: 12px; padding: 0.75rem 1.5rem;">
                    <li class="breadcrumb-item"><a href="{% url 'index' %}" style="color: rgba(255, 255, 255, 0.8);">Home</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'ads:dashboard' %}" style="color: rgba(255, 255, 255, 0.8);">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'ads:analytics_dashboard' %}" style="color: rgba(255, 255, 255, 0.8);">Analytics</a></li>
                    <li class="breadcrumb-item active" aria-current="page" style="color: white; font-weight: 600;">{{ ad.title }}</li>
                </ol>
            </nav>

            <div class="text-center">
                <h1 class="enterprise-title">{{ ad.title }}</h1>
                <p class="enterprise-subtitle">Comprehensive performance analytics and insights for your advertisement</p>

                <div class="enterprise-meta">
                    <div class="enterprise-meta-item">
                        <i class="fas fa-tag"></i>
                        <span>{{ ad.get_status_display }}</span>
                    </div>
                    <div class="enterprise-meta-item">
                        <i class="fas fa-th-large"></i>
                        <span>{{ ad.ad_type.name }}</span>
                    </div>
                    <div class="enterprise-meta-item">
                        <i class="fas fa-calendar-alt"></i>
                        <span>{{ ad.start_date|date:"M d, Y" }} to {{ ad.end_date|date:"M d, Y" }}</span>
                    </div>
                    <div class="enterprise-meta-item">
                        <i class="fas fa-clock"></i>
                        <span>Created {{ ad.created_at|date:"M d, Y" }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Premium Key Metrics -->
        <div class="enterprise-stats-grid">
            <div class="enterprise-stat-card animate__animated animate__fadeInUp" style="animation-delay: 0.1s;">
                <div class="enterprise-stat-icon impressions">
                    <i class="fas fa-eye"></i>
                </div>
                <div class="enterprise-stat-value">{{ ad.impressions }}</div>
                <div class="enterprise-stat-label">Total Impressions</div>
                <div class="enterprise-stat-description">Number of times your ad was displayed</div>
            </div>

            <div class="enterprise-stat-card animate__animated animate__fadeInUp" style="animation-delay: 0.2s;">
                <div class="enterprise-stat-icon clicks">
                    <i class="fas fa-mouse-pointer"></i>
                </div>
                <div class="enterprise-stat-value">{{ ad.clicks }}</div>
                <div class="enterprise-stat-label">Total Clicks</div>
                <div class="enterprise-stat-description">Number of times users clicked your ad</div>
            </div>

            <div class="enterprise-stat-card animate__animated animate__fadeInUp" style="animation-delay: 0.3s;">
                <div class="enterprise-stat-icon ctr">
                    <i class="fas fa-percentage"></i>
                </div>
                <div class="enterprise-stat-value ctr-value">
                    {% if ad.impressions > 0 %}
                        {% with ctr=ad.clicks|floatformat:0|default:0|stringformat:"s"|add:".0"|floatformat:2 %}
                            {{ ctr }}%
                        {% endwith %}
                    {% else %}
                        0.00%
                    {% endif %}
                </div>
                <div class="enterprise-stat-label">Click-Through Rate</div>
                <div class="enterprise-stat-description">Percentage of impressions that resulted in clicks</div>
            </div>

            <div class="enterprise-stat-card animate__animated animate__fadeInUp" style="animation-delay: 0.4s;">
                <div class="enterprise-stat-icon conversions">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="enterprise-stat-value">{{ ad.conversion_count|default:0 }}</div>
                <div class="enterprise-stat-label">Conversions</div>
                <div class="enterprise-stat-description">Number of successful conversions from this ad</div>
            </div>
        </div>

        <!-- Premium Performance Chart -->
        <div class="enterprise-card animate__animated animate__fadeInUp" style="animation-delay: 0.5s;">
            <div class="enterprise-card-header">
                <h3 class="enterprise-card-title">
                    <i class="fas fa-chart-line me-2"></i>
                    Performance Over Time
                </h3>
            </div>
            <div class="enterprise-card-body">

        {% if chart_data.dates and ad.impressions > 0 %}
            <div style="height: 300px; position: relative;">
                <canvas id="performanceChart" class="chart-canvas"
                    data-dates="{{ chart_data.dates|safe }}"
                    data-impressions="{{ chart_data.impressions|safe }}"
                    data-clicks="{{ chart_data.clicks|safe }}">
                </canvas>
            </div>
        {% else %}
            <div class="chart-placeholder">
                <p><i class="fas fa-chart-line me-2"></i> No performance data available yet</p>
                <small class="text-muted d-block mt-2">Data will appear here as your ad receives impressions and clicks</small>

                {% if ad.status == 'active' %}
                    <div class="mt-3">
                        <p class="text-muted small">Your ad is active and ready to collect data.</p>
                        <p class="text-muted small">Share your ad to start seeing performance metrics.</p>
                    </div>
                {% elif ad.status == 'pending' %}
                    <div class="mt-3 alert alert-warning small">
                        <p class="mb-0">Your ad is pending approval. Once approved and paid for, it will become active and start collecting data.</p>
                    </div>
                {% elif ad.status == 'approved' %}
                    <div class="mt-3 alert alert-info small">
                        <p class="mb-0">Your ad is approved but needs payment to become active. <a href="{% url 'ads:payment_process' ad.slug %}">Complete payment</a> to activate.</p>
                    </div>
                {% elif ad.status == 'paused' %}
                    <div class="mt-3 alert alert-warning small">
                        <p class="mb-0">Your ad is currently paused. <a href="{% url 'ads:resume_ad' ad.slug %}">Resume your ad</a> to start collecting data.</p>
                    </div>
                {% else %}
                    <div class="mt-3">
                        <p class="text-muted small">Current status: {{ ad.get_status_display }}</p>
                    </div>
                {% endif %}
                </div>
            {% endif %}
            </div>
        </div>

        <!-- Premium Daily Analytics Table -->
        <div class="enterprise-card animate__animated animate__fadeInUp" style="animation-delay: 0.6s;">
            <div class="enterprise-card-header">
                <h3 class="enterprise-card-title">
                    <i class="fas fa-table me-2"></i>
                    Daily Analytics
                </h3>
            </div>
            <div class="enterprise-card-body">

                {% if analytics_data %}
                    <div class="table-responsive">
                        <table class="enterprise-table table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Impressions</th>
                            <th>Clicks</th>
                            <th>CTR</th>
                            <th>Unique Views</th>
                            <th>Conversions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for data in analytics_data %}
                            <tr>
                                <td>{{ data.date|date:"M d, Y" }}</td>
                                <td>{{ data.impressions }}</td>
                                <td>{{ data.clicks }}</td>
                                <td class="ctr-value">
                                    {% if data.impressions > 0 %}
                                        {% with ctr=data.clicks|floatformat:0|default:0|stringformat:"s"|add:".0"|floatformat:2 %}
                                            {{ ctr }}%
                                        {% endwith %}
                                    {% else %}
                                        0.00%
                                    {% endif %}
                                </td>
                                <td>{{ data.unique_views }}</td>
                                <td>{{ data.conversion_count }}</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="no-data-message">
                <p><i class="fas fa-info-circle me-2"></i> No analytics data available for this ad yet.</p>
                <small class="text-muted d-block mt-2">Daily analytics will be recorded as users interact with your ad</small>

                {% if ad.status == 'active' %}
                    <div class="mt-3">
                        <p class="text-muted small">Analytics data is collected in real-time and updated daily.</p>
                    </div>
                {% endif %}
                </div>
            </div>
        {% endif %}
        </div>

        <!-- Premium Insights Section -->
        <div class="row">
            <!-- Device Distribution -->
            <div class="col-md-6">
                <div class="enterprise-card animate__animated animate__fadeInUp" style="animation-delay: 0.7s;">
                    <div class="enterprise-card-header">
                        <h3 class="enterprise-card-title">
                            <i class="fas fa-mobile-alt me-2"></i>
                            Device Distribution
                        </h3>
                    </div>
                    <div class="enterprise-card-body">

            <div style="height: 250px; position: relative;">
                <canvas id="deviceChart" class="chart-canvas"
                    data-devices="{{ chart_data.device_data.keys|safe }}"
                    data-counts="{{ chart_data.device_data.values|safe }}">
                </canvas>
            </div>
                        {% if not chart_data.device_data %}
                            <div class="text-center mt-2">
                                <small class="text-muted">Device information will be collected when users view your ad</small>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Geographic Distribution -->
            <div class="col-md-6">
                <div class="enterprise-card animate__animated animate__fadeInUp" style="animation-delay: 0.8s;">
                    <div class="enterprise-card-header">
                        <h3 class="enterprise-card-title">
                            <i class="fas fa-map-marker-alt me-2"></i>
                            Geographic Distribution
                        </h3>
                    </div>
                    <div class="enterprise-card-body">
                        <div style="height: 250px; position: relative;">
                            <canvas id="locationChart" class="chart-canvas"
                                data-locations="{{ chart_data.location_data.keys|safe }}"
                                data-counts="{{ chart_data.location_data.values|safe }}">
                            </canvas>
                        </div>
                        {% if not chart_data.location_data %}
                            <div class="text-center mt-2">
                                <small class="text-muted">Geographic data will be collected when users interact with your ad</small>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Premium Action Buttons -->
        <div class="text-center mt-4 mb-4">
            <a href="{% url 'ads:analytics_dashboard' %}" class="btn btn-outline-light btn-lg me-3">
                <i class="fas fa-arrow-left me-2"></i>Back to Analytics Dashboard
            </a>
            <a href="{% url 'ads:dashboard' %}" class="btn btn-primary btn-lg">
                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<!-- Custom Analytics JS -->
<script src="{% static 'ads/js/analytics.js' %}"></script>
{% endblock %}
