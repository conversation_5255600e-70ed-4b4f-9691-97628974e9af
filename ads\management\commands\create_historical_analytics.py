from django.core.management.base import BaseCommand
from django.utils import timezone
from ads.models import Ad, AdAnalytics
import random
from datetime import timedelta, date

class Command(BaseCommand):
    help = 'Create historical analytics data for ads'

    def handle(self, *args, **options):
        # Get all ads
        ads = Ad.objects.all()

        if not ads:
            self.stdout.write(self.style.ERROR('No ads found'))
            return

        # Use a fixed date range for May 2023 (since the app was created in May 2023)
        end_date = date(2023, 5, 15)  # May 15, 2023
        start_date = date(2023, 5, 1)  # May 1, 2023

        # Create analytics data for each ad
        for ad in ads:
            self.stdout.write(f"Creating historical data for ad: {ad.title}")

            # Reset the total impressions and clicks
            ad.impressions = 0
            ad.clicks = 0

            # Delete existing analytics for this ad
            AdAnalytics.objects.filter(ad=ad).delete()

            # Calculate the number of days to generate data for
            days_to_generate = (end_date - start_date).days + 1

            # Create data for each day in our fixed date range
            for day_offset in range(days_to_generate):
                # Calculate the current date
                current_date = start_date + timedelta(days=day_offset)

                # Generate random data with a trend (more recent days have more activity)
                base_impressions = random.randint(10, 50)
                # Calculate recency factor (0 to 1) based on how recent the day is
                recency_factor = 1 + (day_offset / max(days_to_generate, 1))  # Higher for more recent days

                impressions = int(base_impressions * recency_factor)

                # Clicks are typically a small percentage of impressions
                clicks = int(impressions * random.uniform(0.01, 0.1))

                # Create device data
                device_data = {
                    'desktop': int(impressions * random.uniform(0.4, 0.6)),
                    'mobile': int(impressions * random.uniform(0.3, 0.5)),
                    'tablet': int(impressions * random.uniform(0.05, 0.15)),
                }

                # Ensure the sum matches total impressions
                total_device = sum(device_data.values())
                if total_device != impressions:
                    # Adjust desktop to make the total match
                    device_data['desktop'] += (impressions - total_device)

                # Create location data
                locations = ['Nairobi', 'Mombasa', 'Kisumu', 'Nakuru', 'Eldoret']
                location_data = {}

                remaining_impressions = impressions
                for location in locations[:-1]:
                    if remaining_impressions <= 0:
                        break
                    location_count = int(remaining_impressions * random.uniform(0.1, 0.3))
                    location_data[location] = location_count
                    remaining_impressions -= location_count

                # Assign remaining impressions to the last location
                if remaining_impressions > 0:
                    location_data[locations[-1]] = remaining_impressions

                # Create the analytics record
                analytics = AdAnalytics.objects.create(
                    ad=ad,
                    date=current_date,
                    impressions=impressions,
                    clicks=clicks,
                    unique_views=int(impressions * 0.8),  # Assume 80% of impressions are unique
                    conversion_count=int(clicks * random.uniform(0.1, 0.3)),  # 10-30% of clicks convert
                    device_data=device_data,
                    location_data=location_data
                )

                # Update the ad's total impressions and clicks
                ad.impressions += impressions
                ad.clicks += clicks

            # Save the updated ad
            ad.save()

            self.stdout.write(self.style.SUCCESS(f"Created {days_to_generate} days of analytics for {ad.title} (from {start_date} to {end_date})"))

        self.stdout.write(self.style.SUCCESS('Successfully created historical analytics data'))
