/**
 * Mobile Stability Fix CSS
 * Prevents layout shifts and improves mobile menu stability
 */

/* Prevent layout shifts during page load */
html {
    scroll-behavior: smooth;
}

body {
    /* Prevent content jumps when scrollbar appears/disappears */
    overflow-y: scroll;
    /* Prevent horizontal scrolling */
    overflow-x: hidden;
    /* Ensure consistent width calculation */
    width: 100%;
    /* Prevent iOS text size adjustment */
    -webkit-text-size-adjust: 100%;
}

/* Prevent content jumps when modal opens/closes */
body.modal-open {
    padding-right: 0 !important;
}

/* Prevent content jumps when mobile menu opens/closes */
body.no-scroll {
    position: fixed;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

/* Ensure mobile menu has consistent dimensions */
.mobile-slideout-menu {
    /* Use hardware acceleration for smoother animations */
    transform: translateX(100%) translateZ(0);
    will-change: transform;
    /* Ensure consistent width */
    width: 80%;
    max-width: 320px;
    /* Ensure consistent height */
    height: 100vh;
    /* Ensure consistent positioning */
    position: fixed;
    top: 0;
    right: 0;
    /* Ensure consistent z-index */
    z-index: 9998;
    /* Ensure consistent overflow behavior */
    overflow-y: auto;
    overflow-x: hidden;
    /* Ensure consistent transition */
    transition: transform 0.3s cubic-bezier(0.16, 1, 0.3, 1);
    /* Ensure consistent display */
    display: block;
    /* Ensure consistent visibility */
    visibility: visible;
    /* Ensure consistent opacity */
    opacity: 0;
    /* Ensure consistent backdrop filter */
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
}

/* Ensure mobile menu is visible when active */
.mobile-slideout-menu.visible {
    transform: translateX(0) translateZ(0);
    opacity: 1;
}

/* Ensure mobile menu overlay has consistent dimensions */
.mobile-menu-overlay {
    /* Use hardware acceleration for smoother animations */
    will-change: opacity;
    /* Ensure consistent width */
    width: 100%;
    /* Ensure consistent height */
    height: 100%;
    /* Ensure consistent positioning */
    position: fixed;
    top: 0;
    left: 0;
    /* Ensure consistent z-index */
    z-index: 9997;
    /* Ensure consistent transition */
    transition: opacity 0.3s ease;
    /* Ensure consistent display */
    display: none;
    /* Ensure consistent backdrop filter */
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

/* Ensure mobile menu overlay is visible when active */
.mobile-menu-overlay.active {
    opacity: 1;
}

/* Ensure mobile menu items have consistent dimensions */
.mobile-menu-item {
    /* Ensure consistent height for touch targets */
    min-height: 44px;
    /* Ensure consistent padding */
    padding: 12px 16px;
    /* Ensure consistent display */
    display: flex;
    align-items: center;
}

/* Ensure mobile menu dropdowns have consistent dimensions */
.mobile-menu-submenu {
    /* Ensure consistent max height */
    max-height: 0;
    /* Ensure consistent overflow behavior */
    overflow: hidden;
    /* Ensure consistent transition */
    transition: max-height 0.3s ease, opacity 0.3s ease, transform 0.3s ease;
    /* Ensure consistent opacity */
    opacity: 0;
    /* Ensure consistent transform */
    transform: translateY(-10px);
}

/* Ensure mobile menu dropdowns are visible when active */
.mobile-menu-dropdown.active .mobile-menu-submenu {
    /* Ensure consistent max height */
    max-height: 1000px;
    /* Ensure consistent opacity */
    opacity: 1;
    /* Ensure consistent transform */
    transform: translateY(0);
}

/* Ensure hamburger button has consistent dimensions */
.new-navbar-toggle {
    /* Ensure consistent width */
    width: 36px;
    /* Ensure consistent height */
    height: 36px;
    /* Ensure consistent padding */
    padding: 10px 8px;
}

/* Ensure hamburger bars have consistent dimensions */
.hamburger-bar {
    /* Ensure consistent width */
    width: 100%;
    /* Ensure consistent height */
    height: 2px;
    /* Ensure consistent transition */
    transition: transform 0.3s ease, opacity 0.3s ease;
}

/* Ensure navbar has consistent dimensions */
.new-navbar {
    /* Ensure consistent height */
    height: 60px;
    /* Ensure consistent z-index */
    z-index: 1000;
}

/* Ensure footer has consistent dimensions */
.enterprise-footer {
    /* Ensure consistent margin */
    margin-top: 0;
    /* Ensure consistent z-index */
    z-index: 10;
}

/* Ensure content has consistent dimensions */
.container {
    /* Ensure consistent width */
    width: 100%;
    /* Ensure consistent max width */
    max-width: 100%;
}

/* Ensure images have consistent dimensions */
img {
    /* Prevent layout shifts from images loading */
    height: auto;
    max-width: 100%;
}

/* Ensure ads have consistent dimensions */
.ad-container {
    /* Prevent layout shifts from ads loading */
    min-height: 50px;
    position: relative;
}

/* Ensure buttons have consistent dimensions */
.btn {
    /* Ensure consistent height for touch targets */
    min-height: 44px;
}

/* Ensure form elements have consistent dimensions */
.form-control, .form-select {
    /* Ensure consistent height for touch targets */
    min-height: 44px;
    /* Prevent iOS zoom on focus */
    font-size: 16px;
}

/* Ensure tables have consistent dimensions */
.table-responsive {
    /* Prevent layout shifts from tables loading */
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

/* Ensure cards have consistent dimensions */
.card {
    /* Prevent layout shifts from cards loading */
    position: relative;
    display: flex;
    flex-direction: column;
}

/* Ensure modals have consistent dimensions */
.modal {
    /* Ensure consistent z-index */
    z-index: 1050;
}

/* Ensure modal backdrops have consistent dimensions */
.modal-backdrop {
    /* Ensure consistent z-index */
    z-index: 1040;
}

/* Ensure tooltips have consistent dimensions */
.tooltip {
    /* Ensure consistent z-index */
    z-index: 1070;
}

/* Ensure popovers have consistent dimensions */
.popover {
    /* Ensure consistent z-index */
    z-index: 1060;
}

/* Ensure dropdowns have consistent dimensions */
.dropdown-menu {
    /* Ensure consistent z-index */
    z-index: 1000;
}

/* Ensure notifications have consistent dimensions */
.notification-count {
    /* Ensure consistent dimensions */
    min-width: 18px;
    height: 18px;
}
