{% extends 'base.html' %}
{% load static %}

{% block title %}Edit Ad - {{ ad.title }}{% endblock %}

{% block extra_css %}
<!-- Include common ad styles -->
{% include 'ads/includes/ads_common_css.html' %}
<link rel="stylesheet" href="{% static 'ads/css/dashboard_enterprise.css' %}">
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<!-- Smart Engine CSS -->
<link rel="stylesheet" href="{% static 'ads/css/smart-engine.css' %}" />
<!-- Ad Creation Enterprise CSS -->
<link rel="stylesheet" href="{% static 'ads/css/ad-creation-enterprise.css' %}" />
<!-- Select2 Campaign Dropdown CSS -->
<link rel="stylesheet" href="{% static 'ads/css/select2-campaign-dropdown.css' %}" />
<!-- Ad Create Enterprise Styles (extracted from inline styles) -->
<link rel="stylesheet" href="{% static 'ads/css/ad-create-enterprise-styles.css' %}" />
<!-- Ad Create Form Fixes -->
<link rel="stylesheet" href="{% static 'ads/css/ad-create-form-fixes.css' %}" />
<!-- Soft Alert Styles -->
<link rel="stylesheet" href="{% static 'css/soft-alerts.css' %}" />
<!-- Dashboard Notification Panel Styles -->
<link rel="stylesheet" href="{% static 'css/dashboard-notification-panel.css' %}" />
{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="display-4 text-center">Edit Advertisement</h1>
            <p class="lead text-center">Update your ad campaign</p>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Multi-step form -->
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <div class="d-none d-md-block">
                        <!-- Full tabs for desktop -->
                        <ul class="nav nav-tabs card-header-tabs" id="adCreationTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="step1-tab" data-bs-toggle="tab" data-bs-target="#step1" type="button" role="tab" aria-controls="step1" aria-selected="true">1. Basic Info</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="step2-tab" data-bs-toggle="tab" data-bs-target="#step2" type="button" role="tab" aria-controls="step2" aria-selected="false">2. Content</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="step3-tab" data-bs-toggle="tab" data-bs-target="#step3" type="button" role="tab" aria-controls="step3" aria-selected="false">3. Options</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="step4-tab" data-bs-toggle="tab" data-bs-target="#step4" type="button" role="tab" aria-controls="step4" aria-selected="false">4. Review</button>
                            </li>
                        </ul>
                    </div>
                    <div class="d-md-none">
                        <!-- Simplified tabs for mobile -->
                        <ul class="nav nav-tabs card-header-tabs" id="adCreationTabsMobile" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="step1-tab-mobile" data-bs-toggle="tab" data-bs-target="#step1" type="button" role="tab" aria-controls="step1" aria-selected="true">1</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="step2-tab-mobile" data-bs-toggle="tab" data-bs-target="#step2" type="button" role="tab" aria-controls="step2" aria-selected="false">2</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="step3-tab-mobile" data-bs-toggle="tab" data-bs-target="#step3" type="button" role="tab" aria-controls="step3" aria-selected="false">3</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="step4-tab-mobile" data-bs-toggle="tab" data-bs-target="#step4" type="button" role="tab" aria-controls="step4" aria-selected="false">4</button>
                            </li>
                        </ul>
                        <!-- Mobile step indicator -->
                        <div class="text-center mt-2 text-white">
                            <span id="mobileStepTitle">Step 1: Basic Info</span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data" id="adCreationForm">
                        {% csrf_token %}
                        <div class="tab-content" id="adCreationTabContent">
                            <!-- Step 1: Basic Info -->
                            <div class="tab-pane fade show active" id="step1" role="tabpanel" aria-labelledby="step1-tab">
                                <h3 class="mb-4">Basic Information</h3>
                                <div class="mb-3">
                                    <label for="adTitle" class="form-label">Ad Title</label>
                                    <input type="text" class="form-control" id="adTitle" name="title" value="{{ ad.title }}" required>
                                </div>
                                <div class="mb-3">
                                    <label for="adType" class="form-label">Ad Type</label>
                                    <select class="form-select" id="adType" name="ad_type" required>
                                        <option value="" disabled>Select an ad type</option>
                                        {% for ad_type in ad_types %}
                                        <option value="{{ ad_type.id }}" data-price="{{ ad_type.base_price }}" {% if ad.ad_type.id == ad_type.id %}selected{% endif %}>{{ ad_type.name }} ({{ ad_type.base_price }} KSH)</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="campaign" class="form-label">Campaign <small class="text-muted">(Optional)</small></label>
                                    <select class="form-select campaign-select" id="campaign" name="campaign">
                                        {% if ad.campaign %}
                                            <option value="{{ ad.campaign.id }}" selected>{{ ad.campaign.name }}</option>
                                        {% else %}
                                            <option value="" selected>No Campaign (Individual Ad)</option>
                                        {% endif %}
                                    </select>
                                    <div class="form-text">
                                        <i class="fas fa-info-circle me-1"></i> Campaigns help you organize related ads together for better management and analytics.
                                        <a href="{% url 'ads:campaign_create' %}" class="ms-2">
                                            <i class="fas fa-plus-circle me-1"></i>Create New Campaign
                                        </a>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="durationOption" class="form-label">Ad Duration</label>
                                    <select class="form-select" id="durationOption" name="duration_option">
                                        <option value="7days" {% if ad.end_date|timeuntil:ad.start_date == '7 days' %}selected{% endif %}>7 Days (Default)</option>
                                        <option value="2weeks" {% if ad.end_date|timeuntil:ad.start_date == '14 days' %}selected{% endif %}>2 Weeks</option>
                                        <option value="monthly" {% if ad.end_date|timeuntil:ad.start_date == '30 days' %}selected{% endif %}>Monthly (30 Days)</option>
                                        <option value="custom" {% if not ad.end_date|timeuntil:ad.start_date in '7 days,14 days,30 days' %}selected{% endif %}>Custom Duration</option>
                                    </select>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="startDate" class="form-label">Start Date</label>
                                        <input type="date" class="form-control" id="startDate" name="start_date" value="{{ ad.start_date|date:'Y-m-d' }}" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="startTime" class="form-label">Start Time</label>
                                        <input type="time" class="form-control" id="startTime" name="start_time" value="{{ ad.start_date|date:'H:i' }}" required>
                                    </div>
                                </div>

                                <div id="customDurationFields" class="row" {% if not ad.end_date|timeuntil:ad.start_date in '7 days,14 days,30 days' %}style="display: block;"{% else %}style="display: none;"{% endif %}>
                                    <div class="col-md-6 mb-3">
                                        <label for="endDate" class="form-label">End Date</label>
                                        <input type="date" class="form-control" id="endDate" name="end_date" value="{{ ad.end_date|date:'Y-m-d' }}">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="endTime" class="form-label">End Time</label>
                                        <input type="time" class="form-control" id="endTime" name="end_time" value="{{ ad.end_date|date:'H:i' }}">
                                    </div>
                                </div>

                                <div id="calculatedEndTime" class="mb-3 alert alert-info" {% if ad.end_date|timeuntil:ad.start_date in '7 days,14 days,30 days' %}style="display: block;"{% else %}style="display: none;"{% endif %}>
                                    <strong>Your ad will run until:</strong> <span id="endDateTimeDisplay">{{ ad.end_date|date:"l, F j, Y, g:i A" }}</span>
                                    <small class="d-block mt-1">(Includes a 2-hour bonus time)</small>
                                </div>
                                <div class="mb-3">
                                    <label for="target_location" class="form-label">Geographic Target Location</label>
                                    <input type="text" class="form-control" id="target_location" name="target_location" value="{{ ad.target_location }}" placeholder="e.g., Nairobi, Kenya">
                                    <small class="text-muted">The geographic area where you want to target your ad</small>
                                </div>
                                <div class="mb-3">
                                    <label for="targetAudience" class="form-label">Target Audience</label>
                                    <input type="text" class="form-control" id="targetAudience" name="target_audience" value="{{ ad.target_audience }}" placeholder="e.g., Young professionals, age 25-35">
                                </div>
                                <div class="mb-3">
                                    <label for="ad_location" class="form-label">Ad Placement Location <span class="text-danger">*</span></label>
                                    <select class="form-select" id="ad_location" name="ad_location" required>
                                        <option value="" disabled>Select where to display your ad</option>
                                        {% for location in ad_locations %}
                                            <option value="{{ location.id }}"
                                                    data-multiplier="{{ location.price_multiplier }}"
                                                    data-visibility="{{ location.visibility }}"
                                                    data-impressions="{{ location.daily_impressions }}"
                                                    {% if ad.ad_location.id == location.id %}selected{% endif %}>
                                                {{ location.name }} ({{ location.visibility|title }} visibility)
                                            </option>
                                        {% endfor %}
                                    </select>
                                    <small class="text-muted">Premium locations have higher visibility but cost more</small>
                                </div>
                                <div class="d-flex justify-content-end mobile-btn-container">
                                    <button type="button" class="btn btn-primary next-step" data-next="step2-tab" data-mobile-next="step2-tab-mobile" data-step-title="Step 2: Content">Next: Content</button>
                                </div>
                            </div>

                            <!-- Step 2: Content -->
                            <div class="tab-pane fade" id="step2" role="tabpanel" aria-labelledby="step2-tab">
                                <h3 class="mb-4">Ad Content</h3>
                                <div class="mb-3">
                                    <label for="adContent" class="form-label">Ad Text</label>
                                    <textarea class="form-control" id="adContent" name="content" rows="5" required>{{ ad.content }}</textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="ctaLink" class="form-label">Call to Action Link</label>
                                    <input type="url" class="form-control" id="ctaLink" name="cta_link" value="{{ ad.cta_link }}" placeholder="https://example.com">
                                </div>
                                <div class="mb-3">
                                    <label for="adMedia" class="form-label">Upload Media</label>
                                    {% if ad.media %}
                                    <div class="mb-2">
                                        <p>Current media: <a href="{{ ad.media.url }}" target="_blank">{{ ad.media.name }}</a></p>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="keepMedia" name="keep_media" value="true" checked>
                                            <label class="form-check-label" for="keepMedia">
                                                Keep current media
                                            </label>
                                        </div>
                                    </div>
                                    {% endif %}
                                    <input type="file" class="form-control" id="adMedia" name="media">
                                    <div class="form-text">
                                        Supported formats: JPG, PNG, GIF (max 5MB)
                                        <a href="{% url 'ads:ad_size_guide' %}" target="_blank" class="ms-2">
                                            <i class="fas fa-ruler me-1"></i>View recommended ad sizes
                                        </a>
                                    </div>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="requiresAi" name="requires_ai" value="true" {% if ad.requires_ai %}checked{% endif %}>
                                    <label class="form-check-label" for="requiresAi">
                                        Let AI create content for me (+50 KSH)
                                    </label>
                                </div>
                                <div class="d-flex justify-content-between mobile-btn-container">
                                    <button type="button" class="btn btn-secondary prev-step" data-prev="step1-tab" data-mobile-prev="step1-tab-mobile" data-step-title="Step 1: Basic Info">Previous</button>
                                    <button type="button" class="btn btn-primary next-step" data-next="step3-tab" data-mobile-next="step3-tab-mobile" data-step-title="Step 3: Options">Next: Options</button>
                                </div>
                            </div>

                            <!-- Step 3: Options -->
                            <div class="tab-pane fade" id="step3" role="tabpanel" aria-labelledby="step3-tab">
                                <h3 class="mb-4">Additional Options</h3>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="wantsSocial" name="wants_social" value="true" {% if ad.wants_social %}checked{% endif %}>
                                    <label class="form-check-label" for="wantsSocial">
                                        Push to social media (+30 KSH)
                                    </label>
                                </div>
                                <div class="mb-4">
                                    <h5>Pricing Summary</h5>
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>Base Price:</span>
                                                <span id="basePrice">{{ ad.base_pricing }} KSH</span>
                                            </div>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>Duration:</span>
                                                <span id="durationDays">{{ ad.end_date|timeuntil:ad.start_date }}</span>
                                            </div>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>Location Multiplier:</span>
                                                <span id="locationMultiplier">x{{ ad.ad_location.price_multiplier }}</span>
                                            </div>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>Placement Price:</span>
                                                <span id="locationPrice">{{ ad.base_pricing|floatformat:2 }} KSH</span>
                                            </div>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>AI Content:</span>
                                                <span id="aiPrice">{% if ad.requires_ai %}50.00{% else %}0.00{% endif %} KSH</span>
                                            </div>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>Social Media:</span>
                                                <span id="socialPrice">{% if ad.wants_social %}30.00{% else %}0.00{% endif %} KSH</span>
                                            </div>
                                            <hr>
                                            <div class="d-flex justify-content-between fw-bold">
                                                <span>Total Price:</span>
                                                <span id="totalPrice">{{ ad.final_pricing }} KSH</span>
                                            </div>
                                            <input type="hidden" id="basePricingInput" name="base_pricing" value="{{ ad.base_pricing }}">
                                            <input type="hidden" id="finalPricingInput" name="final_pricing" value="{{ ad.final_pricing }}">
                                        </div>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between mobile-btn-container">
                                    <button type="button" class="btn btn-secondary prev-step" data-prev="step2-tab" data-mobile-prev="step2-tab-mobile" data-step-title="Step 2: Content">Previous</button>
                                    <button type="button" class="btn btn-primary next-step" data-next="step4-tab" data-mobile-next="step4-tab-mobile" data-step-title="Step 4: Review">Next: Review</button>
                                </div>
                            </div>

                            <!-- Step 4: Review -->
                            <div class="tab-pane fade" id="step4" role="tabpanel" aria-labelledby="step4-tab">
                                <h3 class="mb-4">Review Your Ad</h3>
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5 class="mb-0">Ad Summary</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row mb-3">
                                            <div class="col-md-4 fw-bold">Title:</div>
                                            <div class="col-md-8" id="reviewTitle">{{ ad.title }}</div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-md-4 fw-bold">Type:</div>
                                            <div class="col-md-8" id="reviewType">{{ ad.ad_type.name }}</div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-md-4 fw-bold">Duration:</div>
                                            <div class="col-md-8" id="reviewDuration">{{ ad.start_date|date:"Y-m-d" }} to {{ ad.end_date|date:"Y-m-d" }}</div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-md-4 fw-bold">Placement:</div>
                                            <div class="col-md-8" id="reviewLocation">{{ ad.ad_location.name }}</div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-md-4 fw-bold">Content:</div>
                                            <div class="col-md-8" id="reviewContent">{{ ad.content }}</div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-md-4 fw-bold">Est. Daily Views:</div>
                                            <div class="col-md-8" id="reviewImpressions">{{ ad.ad_location.daily_impressions }} views</div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-md-4 fw-bold">Total Price:</div>
                                            <div class="col-md-8" id="reviewPrice">{{ ad.final_pricing }} KSH</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between mobile-btn-container">
                                    <button type="button" class="btn btn-secondary prev-step" data-prev="step3-tab" data-mobile-prev="step3-tab-mobile" data-step-title="Step 3: Options">Previous</button>
                                    <button type="submit" class="btn btn-success" id="submitAd">Update Ad</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Preview Panel -->
            <div class="card">
                <div class="card-header bg-dark text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0 text-white">Ad Preview</h4>
                    <button class="btn btn-sm btn-outline-light d-md-none" type="button" data-bs-toggle="collapse" data-bs-target="#previewCollapse" aria-expanded="false" aria-controls="previewCollapse">
                        <i class="fas fa-eye"></i> Toggle Preview
                    </button>
                </div>
                <div class="card-body collapse show" id="previewCollapse">
                    <div class="ad-preview-container">
                        <div class="ad-preview-title" id="previewTitle">{{ ad.title }}</div>
                        <div class="ad-preview-content" id="previewContent">{{ ad.content }}</div>
                        <div class="ad-preview-image" id="previewImage">
                            {% if ad.media %}
                            <img src="{{ ad.media.url }}" alt="Ad Preview" class="img-fluid">
                            {% else %}
                            <img src="{% static 'img/ad-placeholder.jpg' %}" alt="Ad Preview" class="img-fluid">
                            {% endif %}
                        </div>
                        <div class="ad-preview-cta">
                            <a href="{{ ad.cta_link|default:'#' }}" class="btn btn-primary btn-sm" id="previewCta">Learn More</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Help Panel -->
            <div class="card mt-4">
                <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0 text-white">Tips</h4>
                    <button class="btn btn-sm btn-outline-light d-md-none" type="button" data-bs-toggle="collapse" data-bs-target="#tipsCollapse" aria-expanded="false" aria-controls="tipsCollapse">
                        <i class="fas fa-lightbulb"></i> Toggle Tips
                    </button>
                </div>
                <div class="card-body collapse show" id="tipsCollapse">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item">Keep your ad title short and catchy</li>
                        <li class="list-group-item">Use high-quality images for better engagement</li>
                        <li class="list-group-item">Target your audience specifically for better results</li>
                        <li class="list-group-item">Consider using AI to optimize your ad content</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Make sure Bootstrap is loaded -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<!-- Select2 Library -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<!-- Global variables for JavaScript -->
<script src="{% static 'ads/js/ad-create-enterprise-globals.js' %}"></script>
<script>
    // Set campaign search URL for use in other JS files
    window.campaignSearchUrl = "{% url 'ads:api_search_campaigns' %}";
</script>

<!-- External JavaScript files -->
<!-- Non-AI related scripts -->
<script src="{% static 'ads/js/select2-campaign-dropdown.js' %}"></script>
<script src="{% static 'ads/js/ad-creation-tabs.js' %}"></script>
<script src="{% static 'ads/js/ad-duration-calculator.js' %}"></script>
<script src="{% static 'ads/js/ad-pricing-calculator.js' %}"></script>
<script src="{% static 'ads/js/ad-preview.js' %}"></script>
<script src="{% static 'ads/js/ad-preview-location.js' %}"></script>
<script src="{% static 'ads/js/pro-tips.js' %}"></script>
<script src="{% static 'ads/js/create_ad_button_fix.js' %}"></script>
<!-- Notification Bell Fix Script -->
<script src="{% static 'ads/js/notification-bell-fix.js' %}"></script>

<!-- Mobile Navigation Script -->
<script src="{% static 'ads/js/mobile-tab-navigation.js' %}"></script>
{% endblock %}
