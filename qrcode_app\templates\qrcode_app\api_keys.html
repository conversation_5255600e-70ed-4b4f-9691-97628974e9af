{% extends "base.html" %}
{% load static %}
{% load widget_tweaks %}

{% block title %}API Keys - Enterprise QR{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'qrcode_app/css/api-keys.css' %}">
<style>
    /* Ultra-Premium Enterprise API Keys Styling */
    body {
        background:
            linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 20%, #16213e 40%, #0f3460 60%, #533483 80%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        position: relative;
        overflow-x: hidden;
    }

    /* Premium animated background with corporate patterns */
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 10% 90%, rgba(102, 126, 234, 0.5) 0%, transparent 35%),
            radial-gradient(circle at 90% 10%, rgba(255, 255, 255, 0.2) 0%, transparent 30%),
            radial-gradient(circle at 30% 60%, rgba(118, 75, 162, 0.4) 0%, transparent 40%),
            radial-gradient(circle at 70% 80%, rgba(83, 52, 131, 0.3) 0%, transparent 25%),
            radial-gradient(circle at 50% 30%, rgba(102, 126, 234, 0.2) 0%, transparent 45%);
        z-index: -1;
        animation: premiumApiFloat 30s ease-in-out infinite;
    }

    /* Corporate hexagon pattern overlay */
    body::after {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image:
            radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.05) 2px, transparent 2px),
            radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
        background-size: 60px 60px, 40px 40px;
        z-index: -1;
        opacity: 0.6;
        animation: hexagonPulse 12s ease-in-out infinite;
    }

    @keyframes premiumApiFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
        20% { transform: translateY(-20px) rotate(1deg) scale(1.03); }
        40% { transform: translateY(10px) rotate(-0.5deg) scale(0.97); }
        60% { transform: translateY(-15px) rotate(1.2deg) scale(1.02); }
        80% { transform: translateY(5px) rotate(-0.8deg) scale(0.99); }
    }

    @keyframes hexagonPulse {
        0%, 100% { opacity: 0.4; transform: scale(1); }
        50% { opacity: 0.8; transform: scale(1.05); }
    }

    /* Premium Container */
    .container {
        max-width: 1500px;
        padding: 4rem 2rem;
        position: relative;
        z-index: 1;
    }

    /* Ultra-Premium Page Title */
    h1 {
        color: white;
        font-weight: 900;
        font-size: 3rem;
        text-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
        margin-bottom: 1rem;
        background: linear-gradient(135deg, #ffffff 0%, #e0e0e0 50%, #ffffff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        position: relative;
        animation: titleGlow 4s ease-in-out infinite;
    }

    h1::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.3) 0%, rgba(118, 75, 162, 0.3) 100%);
        -webkit-background-clip: text;
        background-clip: text;
        z-index: -1;
        animation: titleShimmer 6s ease-in-out infinite;
    }

    h1 i {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #ffffff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.4));
        animation: iconRotate 8s ease-in-out infinite;
    }

    @keyframes titleGlow {
        0%, 100% { text-shadow: 0 8px 32px rgba(0, 0, 0, 0.5); }
        50% { text-shadow: 0 8px 32px rgba(102, 126, 234, 0.3), 0 0 60px rgba(255, 255, 255, 0.2); }
    }

    @keyframes titleShimmer {
        0%, 100% { opacity: 0.3; }
        50% { opacity: 0.7; }
    }

    @keyframes iconRotate {
        0%, 100% { transform: rotate(0deg) scale(1); }
        25% { transform: rotate(5deg) scale(1.1); }
        75% { transform: rotate(-3deg) scale(1.05); }
    }

    /* Premium Subtitle */
    .text-muted {
        color: rgba(255, 255, 255, 0.8) !important;
        font-size: 1.2rem;
        font-weight: 500;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        margin-bottom: 3rem;
    }

    /* Ultra-Premium Cards with Glossy Effects */
    .card {
        border-radius: 32px;
        overflow: hidden;
        transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        box-shadow:
            0 30px 60px rgba(0, 0, 0, 0.3),
            0 20px 40px rgba(0, 0, 0, 0.2),
            inset 0 2px 0 rgba(255, 255, 255, 0.4),
            inset 0 -2px 0 rgba(0, 0, 0, 0.1);
        border: 3px solid rgba(255, 255, 255, 0.3);
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.98) 0%,
            rgba(248, 249, 250, 0.95) 30%,
            rgba(255, 255, 255, 0.98) 70%,
            rgba(240, 242, 247, 0.95) 100%);
        backdrop-filter: blur(40px);
        position: relative;
        animation: cardEntryGlossy 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }

    .card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg,
            rgba(102, 126, 234, 0.12) 0%,
            rgba(255, 255, 255, 0.15) 30%,
            rgba(118, 75, 162, 0.12) 70%,
            rgba(255, 255, 255, 0.1) 100%);
        z-index: 0;
        pointer-events: none;
        border-radius: 32px;
    }

    .card::after {
        content: '';
        position: absolute;
        top: -3px;
        left: -3px;
        width: calc(100% + 6px);
        height: calc(100% + 6px);
        background: conic-gradient(from 0deg,
            rgba(102, 126, 234, 0.4) 0deg,
            rgba(255, 255, 255, 0.3) 90deg,
            rgba(118, 75, 162, 0.4) 180deg,
            rgba(255, 255, 255, 0.2) 270deg,
            rgba(102, 126, 234, 0.4) 360deg);
        border-radius: 35px;
        z-index: -1;
        opacity: 0;
        transition: opacity 0.8s ease;
        animation: borderGlow 8s linear infinite;
    }

    .card:hover {
        box-shadow:
            0 50px 100px rgba(0, 0, 0, 0.4),
            0 30px 60px rgba(0, 0, 0, 0.25),
            0 0 80px rgba(102, 126, 234, 0.3),
            inset 0 3px 0 rgba(255, 255, 255, 0.5),
            inset 0 -3px 0 rgba(0, 0, 0, 0.15);
        transform: translateY(-15px) scale(1.03) rotateX(5deg);
        border-color: rgba(255, 255, 255, 0.5);
    }

    .card:hover::after {
        opacity: 1;
    }

    @keyframes cardEntryGlossy {
        0% {
            opacity: 0;
            transform: translateY(60px) scale(0.9) rotateX(20deg);
            filter: blur(20px);
        }
        100% {
            opacity: 1;
            transform: translateY(0) scale(1) rotateX(0deg);
            filter: blur(0px);
        }
    }

    @keyframes borderGlow {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Premium Card Headers with Glossy Effects */
    .card-header {
        padding: 3rem 3.5rem;
        border-bottom: none;
        position: relative;
        overflow: hidden;
        background: linear-gradient(135deg,
            #0a0a0a 0%,
            #1a1a2e 20%,
            #16213e 40%,
            #0f3460 60%,
            #533483 80%,
            #764ba2 100%);
        border-radius: 32px 32px 0 0;
    }

    .card-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.2) 0%,
            rgba(255, 255, 255, 0.1) 30%,
            rgba(255, 255, 255, 0.15) 70%,
            rgba(255, 255, 255, 0.08) 100%);
        z-index: 0;
    }

    .card-header::after {
        content: '';
        position: absolute;
        top: -100%;
        left: -100%;
        width: 300%;
        height: 300%;
        background: conic-gradient(from 0deg,
            rgba(102, 126, 234, 0.4) 0deg,
            rgba(255, 255, 255, 0.3) 90deg,
            rgba(118, 75, 162, 0.4) 180deg,
            rgba(255, 255, 255, 0.2) 270deg,
            rgba(102, 126, 234, 0.4) 360deg);
        z-index: 1;
        animation: headerGlossyRotate 10s linear infinite;
        opacity: 0.6;
    }

    @keyframes headerGlossyRotate {
        0% { transform: rotate(0deg); opacity: 0.4; }
        50% { opacity: 0.8; }
        100% { transform: rotate(360deg); opacity: 0.4; }
    }

    .card-header h2 {
        position: relative;
        z-index: 3;
        margin: 0;
        color: white;
        font-weight: 900;
        font-size: 1.5rem;
        text-shadow:
            0 4px 20px rgba(0, 0, 0, 0.5),
            0 0 40px rgba(255, 255, 255, 0.2);
        letter-spacing: 1.5px;
        text-transform: uppercase;
        background: linear-gradient(135deg, #ffffff 0%, #e0e0e0 50%, #ffffff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        animation: headerTextGlow 3s ease-in-out infinite;
    }

    @keyframes headerTextGlow {
        0%, 100% {
            text-shadow: 0 4px 20px rgba(0, 0, 0, 0.5), 0 0 40px rgba(255, 255, 255, 0.2);
        }
        50% {
            text-shadow: 0 4px 20px rgba(0, 0, 0, 0.5), 0 0 60px rgba(102, 126, 234, 0.4);
        }
    }

    .card-body {
        padding: 3.5rem;
        position: relative;
        z-index: 2;
        background: rgba(255, 255, 255, 0.98);
    }

    /* Premium API Key Cards with Unique Glossy Design */
    .api-key-card {
        border-radius: 24px;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.95) 0%,
            rgba(248, 250, 252, 0.9) 50%,
            rgba(255, 255, 255, 0.95) 100%);
        border: 2px solid rgba(102, 126, 234, 0.2);
        box-shadow:
            0 20px 40px rgba(0, 0, 0, 0.15),
            0 8px 16px rgba(102, 126, 234, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.6);
        transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        position: relative;
        overflow: hidden;
    }

    .api-key-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(45deg,
            rgba(102, 126, 234, 0.05) 0%,
            rgba(255, 255, 255, 0.1) 50%,
            rgba(118, 75, 162, 0.05) 100%);
        z-index: 0;
        border-radius: 24px;
    }

    .api-key-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow:
            0 30px 60px rgba(0, 0, 0, 0.2),
            0 15px 30px rgba(102, 126, 234, 0.15),
            0 0 50px rgba(102, 126, 234, 0.2),
            inset 0 2px 0 rgba(255, 255, 255, 0.7);
        border-color: rgba(102, 126, 234, 0.4);
    }

    /* Premium API Key Value Display */
    .api-key-value {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: 2px solid rgba(102, 126, 234, 0.2);
        border-radius: 16px;
        padding: 1rem 1.5rem;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 0.9rem;
        font-weight: 600;
        color: #2c3e50;
        position: relative;
        overflow: hidden;
        transition: all 0.4s ease;
        box-shadow:
            inset 0 2px 8px rgba(0, 0, 0, 0.1),
            0 4px 15px rgba(102, 126, 234, 0.1);
    }

    .api-key-value::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
            transparent 0%,
            rgba(102, 126, 234, 0.2) 50%,
            transparent 100%);
        transition: left 0.6s ease;
    }

    .api-key-value:hover::before {
        left: 100%;
    }

    /* Premium Status Indicators */
    .api-key-status {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 8px;
        position: relative;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
    }

    .status-active {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        animation: statusPulse 2s ease-in-out infinite;
    }

    .status-inactive {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    }

    @keyframes statusPulse {
        0%, 100% {
            box-shadow: 0 0 10px rgba(16, 185, 129, 0.3);
            transform: scale(1);
        }
        50% {
            box-shadow: 0 0 20px rgba(16, 185, 129, 0.6);
            transform: scale(1.1);
        }
    }

    /* Premium Buttons with Glossy Effects */
    .btn {
        border-radius: 20px;
        padding: 1rem 2.5rem;
        font-weight: 800;
        font-size: 0.95rem;
        letter-spacing: 1px;
        transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        position: relative;
        overflow: hidden;
        text-transform: uppercase;
        border: none;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    }

    .btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
            transparent 0%,
            rgba(255, 255, 255, 0.4) 50%,
            transparent 100%);
        transition: left 0.8s ease;
    }

    .btn:hover::before {
        left: 100%;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
        color: white;
        box-shadow:
            0 12px 35px rgba(102, 126, 234, 0.4),
            0 6px 20px rgba(0, 0, 0, 0.15),
            inset 0 2px 0 rgba(255, 255, 255, 0.3);
        border: 2px solid rgba(255, 255, 255, 0.3);
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 50%, #5a67d8 100%);
        transform: translateY(-5px) scale(1.05);
        box-shadow:
            0 20px 50px rgba(102, 126, 234, 0.5),
            0 10px 30px rgba(0, 0, 0, 0.2),
            inset 0 3px 0 rgba(255, 255, 255, 0.4);
        color: white;
        border-color: rgba(255, 255, 255, 0.5);
    }

    .btn-outline-primary {
        color: #667eea;
        border: 3px solid #667eea;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 249, 250, 0.8) 100%);
        backdrop-filter: blur(20px);
    }

    .btn-outline-primary:hover {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        transform: translateY(-5px) scale(1.05);
        box-shadow:
            0 15px 40px rgba(102, 126, 234, 0.4),
            0 8px 25px rgba(0, 0, 0, 0.15);
        border-color: transparent;
    }

    /* Premium Copy Button */
    .copy-btn {
        cursor: pointer;
        color: #667eea;
        transition: all 0.3s ease;
        padding: 8px;
        border-radius: 8px;
        background: rgba(102, 126, 234, 0.1);
    }

    .copy-btn:hover {
        color: white;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        transform: scale(1.2) rotate(5deg);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }

    /* Premium Rate Limits List */
    .list-unstyled li {
        padding: 0.8rem 0;
        border-bottom: 1px solid rgba(102, 126, 234, 0.1);
        transition: all 0.3s ease;
        border-radius: 8px;
        margin-bottom: 0.5rem;
    }

    .list-unstyled li:hover {
        background: rgba(102, 126, 234, 0.05);
        transform: translateX(10px);
        padding-left: 1rem;
    }

    .list-unstyled li i {
        color: #10b981;
        filter: drop-shadow(0 2px 4px rgba(16, 185, 129, 0.3));
    }

    /* Premium Alert */
    .alert {
        border-radius: 20px;
        border: none;
        box-shadow:
            0 15px 35px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.5);
        backdrop-filter: blur(20px);
        position: relative;
        overflow: hidden;
    }

    .alert-info {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(255, 255, 255, 0.9) 100%);
        border: 2px solid rgba(102, 126, 234, 0.3);
        color: #2c3e50;
    }

    .alert::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
            transparent 0%,
            rgba(102, 126, 234, 0.1) 50%,
            transparent 100%);
        animation: alertShimmer 3s ease-in-out infinite;
    }

    @keyframes alertShimmer {
        0%, 100% { left: -100%; }
        50% { left: 100%; }
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="h3">
                <i class="fas fa-key me-2"></i>API Keys
            </h1>
            <p class="text-muted">Manage your API keys for programmatic access to the QR code generator.</p>
        </div>
        <div class="col-md-4 text-md-end">
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#newApiKeyModal">
                <i class="fas fa-plus me-2"></i>Create API Key
            </button>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-4 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h2 class="h5 mb-0">API Documentation</h2>
                </div>
                <div class="card-body">
                    <p>Use our API to generate QR codes programmatically.</p>
                    <div class="d-grid gap-2">
                        <a href="/api/" class="btn btn-outline-primary">
                            <i class="fas fa-book me-2"></i>API Documentation
                        </a>
                        <a href="#" class="btn btn-outline-secondary">
                            <i class="fas fa-code me-2"></i>Code Samples
                        </a>
                    </div>

                    <hr>

                    <h3 class="h6 mb-3">Rate Limits</h3>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check-circle text-success me-2"></i>100 requests/minute</li>
                        <li><i class="fas fa-check-circle text-success me-2"></i>1,000 requests/hour</li>
                        <li><i class="fas fa-check-circle text-success me-2"></i>10,000 requests/day</li>
                    </ul>

                    <hr>

                    <h3 class="h6 mb-3">Security Tips</h3>
                    <ul>
                        <li>Keep your API keys secure</li>
                        <li>Rotate keys regularly</li>
                        <li>Use HTTPS for all API requests</li>
                        <li>Set appropriate permissions</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="col-lg-8">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white">
                    <h2 class="h5 mb-0">Your API Keys</h2>
                </div>
                <div class="card-body">
                    {% if api_keys %}
                        <div class="row">
                            {% for api_key in api_keys %}
                                <div class="col-md-6 mb-4">
                                    <div class="card api-key-card h-100">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h3 class="h6 mb-0">
                                                    <span class="api-key-status {% if api_key.is_active %}status-active{% else %}status-inactive{% endif %}"></span>
                                                    {{ api_key.name }}
                                                </h3>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline-secondary" type="button" id="dropdownMenuButton{{ api_key.id }}" data-bs-toggle="dropdown" aria-expanded="false">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton{{ api_key.id }}">
                                                        <li><a class="dropdown-item" href="#"><i class="fas fa-edit me-2"></i>Rename</a></li>
                                                        {% if api_key.is_active %}
                                                            <li><a class="dropdown-item text-warning" href="#"><i class="fas fa-pause me-2"></i>Deactivate</a></li>
                                                        {% else %}
                                                            <li><a class="dropdown-item text-success" href="#"><i class="fas fa-play me-2"></i>Activate</a></li>
                                                        {% endif %}
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li><a class="dropdown-item text-danger" href="#"><i class="fas fa-trash me-2"></i>Delete</a></li>
                                                    </ul>
                                                </div>
                                            </div>

                                            <div class="mb-3">
                                                <div class="d-flex justify-content-between align-items-center mb-1">
                                                    <small class="text-muted">API Key</small>
                                                    <i class="fas fa-copy copy-btn" data-clipboard-target="#apiKey{{ api_key.id }}"></i>
                                                </div>
                                                <div class="api-key-value" id="apiKey{{ api_key.id }}">{{ api_key.key }}</div>
                                            </div>

                                            <div class="d-flex justify-content-between text-muted small">
                                                <span>Created: {{ api_key.created_at|date:"M d, Y" }}</span>
                                                {% if api_key.last_used %}
                                                    <span>Last used: {{ api_key.last_used|date:"M d, Y" }}</span>
                                                {% else %}
                                                    <span>Never used</span>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <div class="mb-3">
                                <i class="fas fa-key fa-3x text-muted"></i>
                            </div>
                            <h3 class="h5">No API Keys Found</h3>
                            <p class="text-muted">You haven't created any API keys yet.</p>
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#newApiKeyModal">
                                <i class="fas fa-plus me-2"></i>Create API Key
                            </button>
                        </div>
                    {% endif %}
                </div>
            </div>

            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h2 class="h5 mb-0">API Usage</h2>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>API usage statistics are available for premium users.
                    </div>
                    <div class="text-center">
                        <a href="#" class="btn btn-outline-primary">Upgrade to Premium</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- New API Key Modal -->
<div class="modal fade" id="newApiKeyModal" tabindex="-1" aria-labelledby="newApiKeyModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="newApiKeyModalLabel">Create New API Key</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form method="post" id="apiKeyForm">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="{{ form.name.id_for_label }}" class="form-label">API Key Name</label>
                        {{ form.name|add_class:"form-control" }}
                        {% if form.name.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.name.errors }}
                        </div>
                        {% endif %}
                        <div class="form-text">
                            Give your API key a descriptive name to remember what it's used for.
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="apiKeyForm" class="btn btn-primary">Create API Key</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/clipboard@2.0.8/dist/clipboard.min.js"></script>
<script src="{% static 'qrcode_app/js/api-keys.js' %}"></script>
{% endblock %}
