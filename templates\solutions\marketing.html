{% extends 'base.html' %}
{% load static %}

{% block title %}Enterprise QR | Marketing Solutions{% endblock %}

{% block extra_css %}
<!-- Google Fonts - Poppins and Montserrat -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

<link rel="stylesheet" href="{% static 'css/solutions.css' %}">

<style>
    /* Ultra-Premium Sleek Corporate Solutions Styling */
    body {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 15%, #cbd5e1 30%, #94a3b8 50%, #64748b 70%, #475569 85%, #334155 100%);
        min-height: 100vh;
        font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        position: relative;
        overflow-x: hidden;
    }

    /* Elegant animated background with subtle corporate patterns */
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 25% 75%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),
            radial-gradient(circle at 75% 25%, rgba(255, 255, 255, 0.6) 0%, transparent 40%),
            radial-gradient(circle at 15% 85%, rgba(139, 92, 246, 0.06) 0%, transparent 45%),
            radial-gradient(circle at 85% 15%, rgba(16, 185, 129, 0.05) 0%, transparent 35%),
            radial-gradient(circle at 50% 50%, rgba(59, 130, 246, 0.04) 0%, transparent 60%);
        z-index: -1;
        animation: elegantCorporateFloat 120s ease-in-out infinite;
    }

    @keyframes elegantCorporateFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        25% { transform: translateY(-30px) rotate(1deg); }
        50% { transform: translateY(-20px) rotate(-1deg); }
        75% { transform: translateY(-35px) rotate(0.5deg); }
    }

    /* Override solution hero for corporate elegance */
    .solution-hero {
        background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(51, 65, 85, 0.95) 50%, rgba(71, 85, 105, 0.95) 100%) !important;
        backdrop-filter: blur(25px);
        border-radius: 24px;
        margin: 2rem;
        box-shadow:
            0 20px 40px rgba(0, 0, 0, 0.1),
            0 0 0 1px rgba(255, 255, 255, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.1);
        position: relative;
        overflow: hidden;
        animation: slideInDown 0.8s ease-out;
    }

    .solution-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
        animation: shimmer 3s ease-in-out infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    .solution-title {
        font-family: 'Montserrat', sans-serif !important;
        color: #ffffff !important;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 2;
    }

    .solution-subtitle {
        color: rgba(255, 255, 255, 0.9) !important;
        position: relative;
        z-index: 2;
    }

    .hero-cta {
        position: relative;
        z-index: 2;
    }

    /* Enhanced Mobile Responsiveness */
    @media (max-width: 768px) {
        .solution-hero {
            margin: 1rem;
            padding: 2rem 1.5rem;
            border-radius: 16px;
        }

        .solution-title {
            font-size: 2rem !important;
        }

        .solution-subtitle {
            font-size: 1rem !important;
        }

        .hero-cta .btn {
            margin: 0.25rem;
            padding: 0.75rem 1.5rem;
        }
    }

    @media (max-width: 576px) {
        .solution-hero {
            margin: 0.5rem;
            padding: 1.5rem 1rem;
        }

        .solution-title {
            font-size: 1.6rem !important;
        }

        .solution-subtitle {
            font-size: 0.9rem !important;
        }

        .hero-cta {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .hero-cta .btn {
            width: 100%;
            margin: 0;
        }
    }

    /* Enhanced Body Content Styling */
    .solution-overview,
    .solution-features,
    .solution-use-cases,
    .solution-cta {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
        backdrop-filter: blur(25px);
        border-radius: 24px;
        margin: 2rem;
        padding: 4rem 2rem;
        box-shadow:
            0 15px 35px rgba(0, 0, 0, 0.08),
            0 0 0 1px rgba(255, 255, 255, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.4);
        border: 1px solid rgba(255, 255, 255, 0.2);
        animation: slideInUp 0.8s ease-out;
        position: relative;
        overflow: hidden;
    }

    .solution-overview::before,
    .solution-features::before,
    .solution-use-cases::before,
    .solution-cta::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
        animation: shimmer 4s ease-in-out infinite;
        pointer-events: none;
    }

    .section-title {
        font-family: 'Montserrat', sans-serif !important;
        font-size: 2.2rem;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 1.5rem;
        position: relative;
        z-index: 2;
    }

    .section-description {
        font-size: 1.1rem;
        color: #475569;
        line-height: 1.7;
        margin-bottom: 2rem;
        position: relative;
        z-index: 2;
    }

    .feature-list {
        list-style: none;
        padding: 0;
        position: relative;
        z-index: 2;
    }

    .feature-list li {
        padding: 0.75rem 0;
        font-size: 1rem;
        color: #475569;
        display: flex;
        align-items: center;
    }

    .feature-list li i {
        color: #10b981;
        margin-right: 1rem;
        font-size: 1.1rem;
    }

    .feature-card {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
        backdrop-filter: blur(15px);
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow:
            0 10px 25px rgba(0, 0, 0, 0.05),
            0 0 0 1px rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
        position: relative;
        z-index: 2;
    }

    .feature-card:hover {
        transform: translateY(-8px);
        box-shadow:
            0 20px 40px rgba(0, 0, 0, 0.1),
            0 0 0 1px rgba(255, 255, 255, 0.3);
    }

    .feature-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1.5rem;
        box-shadow: 0 8px 20px rgba(249, 115, 22, 0.3);
    }

    .feature-icon i {
        font-size: 1.5rem;
        color: white;
    }

    .feature-card h3 {
        font-family: 'Montserrat', sans-serif !important;
        font-size: 1.3rem;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 1rem;
    }

    .feature-card p {
        color: #475569;
        line-height: 1.6;
        margin: 0;
    }

    .use-case-card {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
        backdrop-filter: blur(15px);
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow:
            0 10px 25px rgba(0, 0, 0, 0.05),
            0 0 0 1px rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
        display: flex;
        align-items: flex-start;
        position: relative;
        z-index: 2;
    }

    .use-case-card:hover {
        transform: translateY(-5px);
        box-shadow:
            0 15px 30px rgba(0, 0, 0, 0.08),
            0 0 0 1px rgba(255, 255, 255, 0.3);
    }

    .use-case-icon {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1.5rem;
        flex-shrink: 0;
        box-shadow: 0 6px 15px rgba(249, 115, 22, 0.3);
    }

    .use-case-icon i {
        font-size: 1.2rem;
        color: white;
    }

    .use-case-content h3 {
        font-family: 'Montserrat', sans-serif !important;
        font-size: 1.2rem;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 0.75rem;
    }

    .use-case-content p {
        color: #475569;
        line-height: 1.6;
        margin: 0;
    }

    .solution-cta {
        background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(51, 65, 85, 0.95) 100%) !important;
        text-align: center;
    }

    .cta-content h2 {
        font-family: 'Montserrat', sans-serif !important;
        font-size: 2.5rem;
        font-weight: 700;
        color: #ffffff;
        margin-bottom: 1rem;
        position: relative;
        z-index: 2;
    }

    .cta-content p {
        font-size: 1.2rem;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 2rem;
        position: relative;
        z-index: 2;
    }

    .cta-buttons {
        position: relative;
        z-index: 2;
    }

    .cta-buttons .btn {
        margin: 0.5rem;
        padding: 0.875rem 2rem;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block content %}
<div class="solution-hero marketing-hero">
    <div class="solution-hero-content">
        <h1 class="solution-title">
            <i class="fas fa-bullhorn me-3 text-warning"></i>
            Marketing QR Solutions
        </h1>
        <p class="solution-subtitle">Transform your marketing campaigns with interactive QR code technology</p>
        <div class="hero-cta">
            <a href="{% url 'generate_qr_code' %}" class="btn btn-primary btn-lg">Get Started</a>
            <a href="#features" class="btn btn-outline-light btn-lg">Learn More</a>
        </div>
    </div>
    <div class="solution-hero-image">
        <img src="{% static 'img/solutions/marketing-hero.svg' %}" alt="Marketing QR Solutions">
    </div>
</div>

<div class="solution-overview">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h2 class="section-title">Elevate Your Marketing Strategy</h2>
                <p class="section-description">Our marketing QR solutions help businesses bridge the gap between physical and digital marketing, creating interactive experiences that engage customers and provide valuable analytics.</p>
                <p>With Enterprise QR's marketing solutions, you can:</p>
                <ul class="feature-list">
                    <li><i class="fas fa-check-circle"></i> Create interactive print materials</li>
                    <li><i class="fas fa-check-circle"></i> Track campaign performance</li>
                    <li><i class="fas fa-check-circle"></i> Personalize customer experiences</li>
                    <li><i class="fas fa-check-circle"></i> Generate leads and conversions</li>
                    <li><i class="fas fa-check-circle"></i> Build omnichannel campaigns</li>
                </ul>
            </div>
            <div class="col-lg-6">
                <div class="overview-image">
                    <img src="{% static 'img/solutions/marketing-overview.svg' %}" alt="Marketing QR Overview">
                </div>
            </div>
        </div>
    </div>
</div>

<div id="features" class="solution-features">
    <div class="container">
        <h2 class="section-title text-center">Key Features for Marketing Professionals</h2>
        <div class="row">
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                    <h3>Campaign Analytics</h3>
                    <p>Track scan rates, user demographics, location data, and conversion metrics to measure campaign effectiveness.</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-random"></i>
                    </div>
                    <h3>Dynamic Content</h3>
                    <p>Update QR code destinations without reprinting materials, allowing for real-time campaign adjustments.</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-palette"></i>
                    </div>
                    <h3>Brand Integration</h3>
                    <p>Create custom-designed QR codes that incorporate your brand colors, logo, and visual identity.</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-user-friends"></i>
                    </div>
                    <h3>Audience Segmentation</h3>
                    <p>Deliver personalized content based on user behavior, location, and scan history for targeted marketing.</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-bullhorn"></i>
                    </div>
                    <h3>Multi-channel Integration</h3>
                    <p>Seamlessly connect your QR campaigns with email, social media, and other digital marketing channels.</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-clipboard-list"></i>
                    </div>
                    <h3>Lead Generation</h3>
                    <p>Capture customer information and preferences through QR-enabled forms and landing pages.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="solution-use-cases">
    <div class="container">
        <h2 class="section-title text-center">Marketing Use Cases</h2>
        <div class="row">
            <div class="col-lg-6">
                <div class="use-case-card">
                    <div class="use-case-icon">
                        <i class="fas fa-print"></i>
                    </div>
                    <div class="use-case-content">
                        <h3>Print Advertising</h3>
                        <p>Transform static print ads, brochures, and billboards into interactive experiences with QR codes linking to videos, special offers, and digital content.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="use-case-card">
                    <div class="use-case-icon">
                        <i class="fas fa-box"></i>
                    </div>
                    <div class="use-case-content">
                        <h3>Product Packaging</h3>
                        <p>Enhance product packaging with QR codes that provide product information, usage tutorials, authentication verification, and reordering options.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="use-case-card">
                    <div class="use-case-icon">
                        <i class="fas fa-store-alt"></i>
                    </div>
                    <div class="use-case-content">
                        <h3>Retail Displays</h3>
                        <p>Create interactive in-store experiences with QR codes on displays that provide product comparisons, reviews, and special in-store offers.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="use-case-card">
                    <div class="use-case-icon">
                        <i class="fas fa-envelope-open-text"></i>
                    </div>
                    <div class="use-case-content">
                        <h3>Direct Mail Campaigns</h3>
                        <p>Increase direct mail response rates with QR codes that connect recipients to personalized landing pages, video messages, and conversion-focused content.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="solution-cta">
    <div class="container">
        <div class="cta-content">
            <h2>Ready to Transform Your Marketing Campaigns?</h2>
            <p>Join innovative marketers who trust Enterprise QR for their QR code solutions.</p>
            <div class="cta-buttons">
                <a href="{% url 'generate_qr_code' %}" class="btn btn-primary btn-lg">Get Started Now</a>
                <a href="#" class="btn btn-outline-light btn-lg">Contact Sales</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
