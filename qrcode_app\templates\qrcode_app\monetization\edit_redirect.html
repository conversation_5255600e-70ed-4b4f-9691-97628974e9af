{% extends "base.html" %}
{% load static %}

{% block title %}Edit QR Redirect - {{ qr_code.name }}{% endblock %}

{% block extra_css %}
<style>
    .edit-redirect-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }
    
    .edit-card {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }
    
    .qr-info-header {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .current-url {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1.5rem;
        word-break: break-all;
    }
    
    .short-url-display {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1.5rem;
        text-align: center;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .btn-update {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        color: white;
        padding: 0.75rem 2rem;
        border-radius: 25px;
        font-weight: 600;
        transition: transform 0.3s ease;
    }
    
    .btn-update:hover {
        transform: translateY(-2px);
        color: white;
    }
    
    .feature-highlight {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .copy-button {
        background: #28a745;
        border: none;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 5px;
        margin-left: 0.5rem;
        cursor: pointer;
    }
    
    .copy-button:hover {
        background: #218838;
    }
</style>
{% endblock %}

{% block content %}
<div class="edit-redirect-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <!-- QR Code Info Header -->
                <div class="qr-info-header">
                    <h1><i class="fas fa-qrcode me-2"></i>{{ qr_code.name }}</h1>
                    <p class="mb-0">Edit where this QR code redirects - no reprinting needed!</p>
                </div>

                <!-- Feature Highlight -->
                <div class="feature-highlight">
                    <h3><i class="fas fa-magic me-2"></i>Dynamic QR Redirect</h3>
                    <p class="mb-0">Change your QR destination anytime without reprinting. Perfect for events, campaigns, and mistake correction!</p>
                </div>

                <!-- Short URL Display -->
                <div class="short-url-display">
                    <h5><i class="fas fa-link me-2"></i>Your Short URL</h5>
                    <div class="d-flex align-items-center justify-content-center">
                        <code id="shortUrl">{{ short_url }}</code>
                        <button class="copy-button" onclick="copyToClipboard('shortUrl')">
                            <i class="fas fa-copy"></i> Copy
                        </button>
                    </div>
                    <small>Use this URL in your QR code for dynamic redirects</small>
                </div>

                <!-- Current Target -->
                <div class="edit-card">
                    <h4><i class="fas fa-bullseye me-2"></i>Current Destination</h4>
                    <div class="current-url">
                        <strong>Currently redirects to:</strong><br>
                        <a href="{{ current_target }}" target="_blank">{{ current_target }}</a>
                    </div>

                    <!-- Edit Form -->
                    <form method="post" id="redirectForm">
                        {% csrf_token %}
                        <div class="form-group">
                            <label for="{{ form.target_url.id_for_label }}" class="form-label">
                                <i class="fas fa-external-link-alt me-2"></i>{{ form.target_url.label }}
                            </label>
                            {{ form.target_url }}
                            {% if form.target_url.help_text %}
                            <div class="form-text">{{ form.target_url.help_text }}</div>
                            {% endif %}
                            {% if form.target_url.errors %}
                            <div class="text-danger">
                                {% for error in form.target_url.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-between">
                            <button type="submit" class="btn btn-update">
                                <i class="fas fa-save me-2"></i>Update Redirect
                            </button>
                            <a href="{% url 'qr_code_detail' qr_code.id %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to QR Code
                            </a>
                        </div>
                    </form>
                </div>

                <!-- How It Works -->
                <div class="edit-card">
                    <h4><i class="fas fa-info-circle me-2"></i>How Dynamic Redirects Work</h4>
                    <div class="row">
                        <div class="col-md-4 text-center mb-3">
                            <div class="mb-2">
                                <i class="fas fa-qrcode fa-3x text-primary"></i>
                            </div>
                            <h6>1. Print QR Code</h6>
                            <p class="small">Print your QR code with the short URL</p>
                        </div>
                        <div class="col-md-4 text-center mb-3">
                            <div class="mb-2">
                                <i class="fas fa-edit fa-3x text-success"></i>
                            </div>
                            <h6>2. Change Anytime</h6>
                            <p class="small">Update the destination URL whenever you want</p>
                        </div>
                        <div class="col-md-4 text-center mb-3">
                            <div class="mb-2">
                                <i class="fas fa-mobile-alt fa-3x text-info"></i>
                            </div>
                            <h6>3. Users Scan</h6>
                            <p class="small">Scans automatically go to your new destination</p>
                        </div>
                    </div>
                </div>

                <!-- Use Cases -->
                <div class="edit-card">
                    <h4><i class="fas fa-lightbulb me-2"></i>Perfect For</h4>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-calendar text-primary me-2"></i>Event campaigns</li>
                                <li><i class="fas fa-store text-success me-2"></i>Seasonal promotions</li>
                                <li><i class="fas fa-utensils text-warning me-2"></i>Restaurant menus</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-home text-info me-2"></i>Real estate listings</li>
                                <li><i class="fas fa-chart-line text-danger me-2"></i>A/B testing campaigns</li>
                                <li><i class="fas fa-tools text-secondary me-2"></i>Mistake correction</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    const text = element.textContent;
    
    navigator.clipboard.writeText(text).then(function() {
        // Show success feedback
        const button = element.nextElementSibling;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i> Copied!';
        button.style.background = '#28a745';
        
        setTimeout(function() {
            button.innerHTML = originalText;
            button.style.background = '#28a745';
        }, 2000);
    }).catch(function(err) {
        console.error('Could not copy text: ', err);
        alert('Could not copy to clipboard');
    });
}

// Form submission with AJAX
document.getElementById('redirectForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const submitButton = this.querySelector('button[type="submit"]');
    const originalText = submitButton.innerHTML;
    
    // Show loading state
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating...';
    submitButton.disabled = true;
    
    fetch(this.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update current URL display
            document.querySelector('.current-url a').href = data.new_url;
            document.querySelector('.current-url a').textContent = data.new_url;
            
            // Show success message
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-success alert-dismissible fade show';
            alertDiv.innerHTML = `
                <i class="fas fa-check-circle me-2"></i>${data.message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            this.insertBefore(alertDiv, this.firstChild);
            
            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        } else {
            alert('Error: ' + (data.error || 'Unknown error occurred'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the redirect');
    })
    .finally(() => {
        // Restore button state
        submitButton.innerHTML = originalText;
        submitButton.disabled = false;
    });
});
</script>
{% endblock %}
