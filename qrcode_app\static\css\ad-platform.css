/* Ad Platform Styles */
:root {
    --sidebar-width: 250px;
    --header-height: 70px;
    --primary-blue: #3b82f6;
    --primary-dark-blue: #1d4ed8;
    --purple: #8b5cf6;
    --green: #10b981;
    --orange: #f59e0b;
    --red: #ef4444;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
}

.app-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.dashboard-container {
    display: flex;
    flex: 1;
    background-color: var(--gray-50);
}

/* Sidebar Styles */
.dashboard-sidebar {
    width: var(--sidebar-width);
    background-color: white;
    border-right: 1px solid var(--gray-200);
    display: flex;
    flex-direction: column;
    height: calc(100vh - var(--header-height));
    position: sticky;
    top: var(--header-height);
    overflow-y: auto;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--gray-200);
}

.sidebar-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
}

.sidebar-menu {
    list-style: none;
    padding: 1rem 0;
    margin: 0;
}

.sidebar-item {
    margin-bottom: 0.25rem;
}

.sidebar-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: var(--gray-600);
    text-decoration: none;
    transition: all 0.2s ease;
    font-size: 0.9375rem;
}

.sidebar-link i {
    margin-right: 0.75rem;
    font-size: 1.125rem;
    width: 1.25rem;
    text-align: center;
}

.sidebar-link:hover {
    background-color: var(--gray-100);
    color: var(--primary-blue);
}

.sidebar-item.active .sidebar-link {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--primary-blue);
    font-weight: 500;
    border-left: 3px solid var(--primary-blue);
}

.sidebar-footer {
    margin-top: auto;
    padding: 1.5rem;
    border-top: 1px solid var(--gray-200);
}

.help-link {
    display: flex;
    align-items: center;
    color: var(--gray-600);
    text-decoration: none;
    font-size: 0.875rem;
}

.help-link i {
    margin-right: 0.5rem;
    font-size: 1rem;
}

.help-link:hover {
    color: var(--primary-blue);
}

/* Main Content Styles */
.dashboard-content {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.dashboard-header h1 {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--gray-800);
    margin: 0;
}

.dashboard-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.date-range-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background-color: white;
    border: 1px solid var(--gray-300);
    border-radius: 0.375rem;
    font-size: 0.875rem;
    color: var(--gray-700);
    cursor: pointer;
}

.date-range-selector:hover {
    border-color: var(--primary-blue);
}

.primary-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: linear-gradient(135deg, var(--primary-blue), var(--primary-dark-blue));
    color: white;
    border: none;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.primary-btn:hover {
    box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.3);
    transform: translateY(-1px);
}

.secondary-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background-color: white;
    color: var(--primary-blue);
    border: 1px solid var(--primary-blue);
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.secondary-btn:hover {
    background-color: rgba(59, 130, 246, 0.05);
    box-shadow: 0 2px 4px -1px rgba(59, 130, 246, 0.2);
}

/* Welcome Card */
.welcome-card {
    background-color: white;
    border-radius: 0.5rem;
    padding: 2rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 2rem;
}

.welcome-content {
    flex: 1;
}

.welcome-content h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-800);
    margin: 0 0 1rem 0;
}

.welcome-content p {
    font-size: 1rem;
    color: var(--gray-600);
    margin: 0 0 1.5rem 0;
    line-height: 1.5;
}

.welcome-actions {
    display: flex;
    gap: 1rem;
}

.welcome-image {
    flex-shrink: 0;
}

.welcome-image img {
    border-radius: 0.5rem;
    max-width: 100%;
    height: auto;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background-color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
}

.stat-icon.blue {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.stat-icon.purple {
    background: linear-gradient(135deg, #8b5cf6, #6d28d9);
}

.stat-icon.green {
    background: linear-gradient(135deg, #10b981, #059669);
}

.stat-icon.orange {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.stat-content h3 {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--gray-500);
    margin: 0 0 0.5rem 0;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-800);
    margin: 0 0 0.5rem 0;
}

.stat-info {
    font-size: 0.75rem;
    color: var(--gray-500);
    margin: 0;
}

/* Ad Placement Options */
.placement-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.placement-card {
    background-color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    gap: 1rem;
    transition: all 0.3s ease;
    border: 1px solid var(--gray-200);
}

.placement-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-blue);
}

.placement-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
}

.placement-content h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0 0 0.5rem 0;
}

.placement-content p {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin: 0 0 1rem 0;
    line-height: 1.5;
}

.placement-details {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.placement-stat {
    font-size: 0.75rem;
    color: var(--gray-600);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.placement-stat i {
    color: var(--primary-blue);
}

/* Responsive Styles */
@media (max-width: 1200px) {
    .stats-grid, .placement-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 992px) {
    .dashboard-sidebar {
        width: 64px;
    }
    
    .sidebar-link span,
    .help-link span,
    .sidebar-header h3 {
        display: none;
    }
    
    .sidebar-link i {
        margin-right: 0;
    }
    
    .sidebar-header,
    .sidebar-link {
        display: flex;
        justify-content: center;
        padding: 1rem;
    }
    
    .sidebar-item.active .sidebar-link {
        border-left: none;
        border-left: 3px solid var(--primary-blue);
    }
    
    .welcome-card {
        flex-direction: column;
    }
    
    .welcome-image {
        order: -1;
    }
}

@media (max-width: 768px) {
    .dashboard-container {
        flex-direction: column;
    }
    
    .dashboard-sidebar {
        width: 100%;
        height: auto;
        position: static;
    }
    
    .sidebar-menu {
        display: flex;
        overflow-x: auto;
        padding: 0.5rem;
    }
    
    .sidebar-item {
        margin-bottom: 0;
        margin-right: 0.5rem;
    }
    
    .sidebar-link {
        padding: 0.5rem 1rem;
        border-radius: 0.375rem;
    }
    
    .sidebar-header,
    .sidebar-footer {
        display: none;
    }
    
    .dashboard-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .stats-grid, .placement-grid {
        grid-template-columns: 1fr;
    }
}
