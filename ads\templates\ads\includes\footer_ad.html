{% load static %}

<!-- Enterprise Footer Ads Display -->
{% if ads and ads|length > 0 %}
    <div class="enterprise-footer-ads">
        <div class="container">
            <!-- Header with count -->
            <div class="footer-ads-header">
                <div class="footer-ads-label">
                    <i class="fas fa-handshake"></i>
                    <span>Trusted Partners</span>
                    <span class="partner-count">({{ ads|length }})</span>
                    {% if ads|length > 10 %}
                    <span class="partner-overflow-indicator">
                        <i class="fas fa-ellipsis-h"></i>
                        <span class="overflow-text">{% if ads|length > 50 %}50+ Partners{% else %}{{ ads|length }} Partners{% endif %}</span>
                    </span>
                    {% endif %}
                </div>
                {% if ads|length > 4 %}
                <div class="footer-ads-controls">
                    {% if ads|length > 10 %}
                    <div class="footer-pagination">
                        <span id="footer-page-info">1 of ?</span>
                    </div>
                    {% endif %}
                    <button class="footer-nav-btn" id="footer-prev" onclick="scrollFooterAds(-1)">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button class="footer-nav-btn" id="footer-next" onclick="scrollFooterAds(1)">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                    {% if ads|length > 8 %}
                    <button class="footer-nav-btn footer-nav-btn-special" onclick="toggleFooterAutoScroll()" id="footer-auto-toggle" title="Toggle auto-scroll">
                        <i class="fas fa-pause"></i>
                    </button>
                    {% endif %}
                    {% if ads|length > 20 %}
                    <button class="footer-nav-btn footer-nav-btn-special" onclick="jumpToStart()" id="footer-jump-start" title="Jump to start">
                        <i class="fas fa-fast-backward"></i>
                    </button>
                    {% endif %}
                </div>
                {% endif %}
            </div>

            <!-- Scrollable ads container with seamless scrolling -->
            <div class="footer-ads-wrapper" id="footer-ads-wrapper">
                <div class="footer-ads-track" id="footer-ads-track">
                    {% for ad in ads|slice:":50" %}
                    <div class="footer-partner-card" data-ad-id="{{ ad.id }}" data-ad-type="{{ ad.ad_type.name }}" data-ad-location="footer" data-ad-index="{{ forloop.counter0 }}">
                        {% if ad.media and ad.media.name %}
                        <div class="partner-logo">
                            <img src="/media/{{ ad.media }}" alt="{{ ad.title }}" class="partner-image">
                        </div>
                        {% else %}
                        <div class="partner-logo partner-logo-placeholder">
                            <i class="fas fa-building"></i>
                        </div>
                        {% endif %}

                        <div class="partner-info">
                            <h6 class="partner-name">{{ ad.title|truncatechars:25 }}</h6>
                            <p class="partner-description">{{ ad.content|truncatechars:40 }}</p>
                        </div>

                        {% if ad.cta_link %}
                        <a href="{{ ad.cta_link }}" target="_blank" class="partner-link" rel="noopener" onclick="trackAdClick('{{ ad.id }}')">
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                        {% else %}
                        <div class="partner-link partner-link-disabled">
                            <i class="fas fa-link-slash"></i>
                        </div>
                        {% endif %}

                        <div class="sponsored-badge">Ad</div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Track impressions and initialize -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Track impressions
            {% for ad in ads %}
                trackAdImpression('{{ ad.id }}');
            {% endfor %}

            // Initialize footer ads
            initFooterAds();
        });
    </script>
{% endif %}

<style>
/* Enterprise Footer Ads - Compact Partner Display */
.enterprise-footer-ads {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-top: 1px solid #dee2e6;
    padding: 20px 0;
    margin-top: 40px;
    position: relative;
    overflow: hidden;
}

.enterprise-footer-ads::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #007bff, #6f42c1, #e83e8c, #fd7e14);
}

.footer-ads-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 0 15px;
}

.footer-ads-label {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #495057;
    font-weight: 600;
    font-size: 14px;
}

.footer-ads-label i {
    color: #007bff;
    font-size: 16px;
}

.partner-count {
    background: #007bff;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.partner-overflow-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #6c757d;
    font-size: 11px;
    margin-left: 8px;
}

.partner-overflow-indicator i {
    color: #ffc107;
    animation: pulse 2s infinite;
}

.overflow-text {
    font-style: italic;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.footer-pagination {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 12px;
    padding: 4px 8px;
    font-size: 11px;
    color: #6c757d;
    margin-right: 8px;
}

.footer-nav-btn-special {
    background: #28a745;
    color: white;
    border-color: #28a745;
}

.footer-nav-btn-special:hover {
    background: #218838;
    border-color: #1e7e34;
}

.footer-nav-btn-special.active {
    background: #dc3545;
}

.footer-nav-btn-special.active:hover {
    background: #c82333;
}

.footer-ads-controls {
    display: flex;
    gap: 8px;
}

.footer-nav-btn {
    width: 32px;
    height: 32px;
    border: 1px solid #dee2e6;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #6c757d;
}

.footer-nav-btn:hover {
    background: #007bff;
    color: white;
    border-color: #007bff;
    transform: scale(1.1);
}

.footer-nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.footer-ads-wrapper {
    overflow: hidden;
    position: relative;
    width: 100%;
    /* Ensure no white space on right */
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

.footer-ads-track {
    display: flex;
    gap: 15px;
    padding: 0 15px;
    transition: transform 0.5s ease;
    align-items: stretch;
    /* Ensure seamless scrolling for many ads */
    width: max-content;
    will-change: transform;
}

.footer-partner-card {
    flex: 0 0 280px;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 15px;
    position: relative;
    transition: all 0.3s ease;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 12px;
    min-height: 80px;
}

.footer-partner-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.15);
    border-color: #007bff;
}

.partner-logo {
    flex: 0 0 50px;
    height: 50px;
    border-radius: 8px;
    overflow: hidden;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.partner-logo-placeholder {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
}

.partner-logo-placeholder i {
    font-size: 20px;
}

.partner-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.partner-info {
    flex: 1;
    min-width: 0;
}

.partner-name {
    font-size: 14px;
    font-weight: 600;
    margin: 0 0 4px 0;
    color: #212529;
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.partner-description {
    font-size: 12px;
    color: #6c757d;
    margin: 0;
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.partner-link {
    flex: 0 0 auto;
    width: 32px;
    height: 32px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    text-decoration: none;
    transition: all 0.3s ease;
}

.partner-link:hover {
    background: #007bff;
    color: white;
    border-color: #007bff;
    transform: scale(1.1);
}

.partner-link-disabled {
    background: #f8f9fa;
    color: #adb5bd;
    border-color: #e9ecef;
    cursor: not-allowed;
    opacity: 0.6;
}

.partner-link-disabled:hover {
    background: #f8f9fa;
    color: #adb5bd;
    border-color: #e9ecef;
    transform: none;
}

.sponsored-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    background: #6c757d;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 500;
    opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 768px) {
    .footer-partner-card {
        flex: 0 0 260px;
        padding: 12px;
        min-height: 70px;
    }

    .partner-logo {
        flex: 0 0 40px;
        height: 40px;
    }

    .partner-logo-placeholder i {
        font-size: 16px;
    }

    .partner-name {
        font-size: 13px;
    }

    .partner-description {
        font-size: 11px;
    }

    .partner-link {
        width: 28px;
        height: 28px;
    }

    .footer-ads-controls {
        display: none;
    }
}

@media (max-width: 480px) {
    .enterprise-footer-ads {
        padding: 15px 0;
    }

    .footer-partner-card {
        flex: 0 0 240px;
        padding: 10px;
        min-height: 60px;
        gap: 10px;
    }

    .partner-logo {
        flex: 0 0 35px;
        height: 35px;
    }

    .footer-ads-label {
        font-size: 13px;
    }

    .partner-count {
        font-size: 11px;
        padding: 1px 6px;
    }
}

/* Enhanced scrolling animations for many ads */
.footer-ads-track.scrolling {
    transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Performance optimizations for many ads */
.footer-partner-card {
    /* GPU acceleration for smooth scrolling */
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Enhanced controls styling */
.footer-nav-btn-special {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border: none;
    margin-left: 5px;
}

.footer-nav-btn-special:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: scale(1.05);
}

.footer-nav-btn-special.active {
    background: linear-gradient(135deg, #28a745, #1e7e34);
}

/* Improved pagination display */
#footer-page-info {
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
    min-width: 60px;
    text-align: center;
}

/* Loading state for many ads */
.footer-ads-track.loading {
    opacity: 0.7;
    pointer-events: none;
}

/* Responsive enhancements for many ads */
@media (max-width: 1200px) {
    .footer-partner-card {
        flex: 0 0 260px;
    }
}

@media (max-width: 992px) {
    .footer-partner-card {
        flex: 0 0 240px;
    }

    .footer-ads-controls {
        gap: 5px;
    }

    .footer-nav-btn {
        width: 32px;
        height: 32px;
        font-size: 12px;
    }
}

/* Ensure no horizontal overflow */
.enterprise-footer-ads {
    overflow-x: hidden;
    width: 100%;
    box-sizing: border-box;
}

.container {
    overflow-x: hidden;
}
</style>

<script>
let footerCurrentIndex = 0;
let footerTotalAds = 0;
let footerCardsPerView = 4;
let footerAutoScrollInterval = null;
let footerAutoScrollEnabled = true; // Start with auto-scroll enabled
let footerScrollPosition = 0;
let footerCardWidth = 295; // 280px card + 15px gap
let footerMaxAds = 50; // Maximum ads to display

function initFooterAds() {
    const track = document.getElementById('footer-ads-track');
    const wrapper = document.getElementById('footer-ads-wrapper');
    if (!track || !wrapper) return;

    // Clear any existing interval to prevent duplicates
    if (footerAutoScrollInterval) {
        clearInterval(footerAutoScrollInterval);
        footerAutoScrollInterval = null;
    }

    footerTotalAds = Math.min(track.children.length, footerMaxAds);
    updateFooterCardsPerView();
    updateFooterNavButtons();
    updateFooterPagination();

    // Calculate actual card width including gap
    if (track.children.length > 0) {
        const firstCard = track.children[0];
        const cardStyle = window.getComputedStyle(firstCard);
        const cardWidth = firstCard.offsetWidth;
        const gap = parseInt(cardStyle.marginRight) || 15;
        footerCardWidth = cardWidth + gap;
    }

    // Auto-scroll for ads - more aggressive for many ads
    if (footerTotalAds > 1) {
        // Adaptive scroll delay based on number of ads
        let scrollDelay;
        if (footerTotalAds > 30) {
            scrollDelay = 4000; // 4s for 30+ ads (faster for many ads)
        } else if (footerTotalAds > 20) {
            scrollDelay = 6000; // 6s for 20+ ads
        } else if (footerTotalAds > 10) {
            scrollDelay = 8000; // 8s for 10+ ads
        } else {
            scrollDelay = 10000; // 10s for fewer ads
        }

        console.log('Footer ads auto-scroll enabled:', {
            totalAds: footerTotalAds,
            cardsPerView: footerCardsPerView,
            scrollDelay: scrollDelay,
            cardWidth: footerCardWidth
        });

        footerAutoScrollInterval = setInterval(() => {
            if (footerAutoScrollEnabled) {
                scrollFooterAdsSmooth(1);
            }
        }, scrollDelay);

        // Enable auto-scroll by default
        footerAutoScrollEnabled = true;

        // Update toggle button state
        const toggleBtn = document.getElementById('footer-auto-toggle');
        if (toggleBtn) {
            toggleBtn.classList.add('active');
            toggleBtn.innerHTML = '<i class="fas fa-pause"></i>';
            toggleBtn.title = 'Pause auto-scroll';
        }
    }

    // Add mouse hover pause functionality
    wrapper.addEventListener('mouseenter', () => {
        if (footerAutoScrollInterval) {
            clearInterval(footerAutoScrollInterval);
        }
    });

    wrapper.addEventListener('mouseleave', () => {
        if (footerAutoScrollEnabled && footerTotalAds > 1) {
            const scrollDelay = footerTotalAds > 30 ? 4000 : footerTotalAds > 20 ? 6000 : 8000;
            footerAutoScrollInterval = setInterval(() => {
                if (footerAutoScrollEnabled) {
                    scrollFooterAdsSmooth(1);
                }
            }, scrollDelay);
        }
    });
}

function updateFooterCardsPerView() {
    const width = window.innerWidth;
    if (width <= 480) {
        footerCardsPerView = 1;
    } else if (width <= 768) {
        footerCardsPerView = 2;
    } else if (width <= 1024) {
        footerCardsPerView = 3;
    } else {
        footerCardsPerView = 4;
    }
}

function scrollFooterAds(direction) {
    const track = document.getElementById('footer-ads-track');
    if (!track) return;

    footerCurrentIndex += direction;

    // Enhanced looping for seamless scrolling
    if (footerCurrentIndex >= footerTotalAds - footerCardsPerView + 1) {
        footerCurrentIndex = 0;
    } else if (footerCurrentIndex < 0) {
        footerCurrentIndex = Math.max(0, footerTotalAds - footerCardsPerView);
    }

    const offset = footerCurrentIndex * footerCardWidth;
    footerScrollPosition = offset;

    track.style.transform = `translateX(-${offset}px)`;
    updateFooterNavButtons();
    updateFooterPagination();
}

function scrollFooterAdsSmooth(direction) {
    const track = document.getElementById('footer-ads-track');
    if (!track) return;

    // For smooth continuous scrolling, move by smaller increments
    if (footerTotalAds > footerCardsPerView) {
        footerCurrentIndex += direction;

        // Seamless loop - when reaching the end, smoothly transition to start
        if (footerCurrentIndex >= footerTotalAds - footerCardsPerView + 1) {
            footerCurrentIndex = 0;
        } else if (footerCurrentIndex < 0) {
            footerCurrentIndex = Math.max(0, footerTotalAds - footerCardsPerView);
        }

        const offset = footerCurrentIndex * footerCardWidth;
        footerScrollPosition = offset;

        // Add smooth scrolling class for better animation
        track.classList.add('scrolling');
        track.style.transform = `translateX(-${offset}px)`;

        // Remove scrolling class after animation
        setTimeout(() => {
            track.classList.remove('scrolling');
        }, 500);

        updateFooterNavButtons();
        updateFooterPagination();
    }
}

function updateFooterNavButtons() {
    const prevBtn = document.getElementById('footer-prev');
    const nextBtn = document.getElementById('footer-next');

    if (prevBtn && nextBtn) {
        // For seamless scrolling, never disable buttons
        prevBtn.disabled = false;
        nextBtn.disabled = false;

        // Update button opacity based on position
        if (footerCurrentIndex === 0) {
            prevBtn.style.opacity = '0.5';
        } else {
            prevBtn.style.opacity = '1';
        }

        if (footerCurrentIndex >= footerTotalAds - footerCardsPerView) {
            nextBtn.style.opacity = '0.5';
        } else {
            nextBtn.style.opacity = '1';
        }
    }
}

function jumpToStart() {
    const track = document.getElementById('footer-ads-track');
    if (!track) return;

    footerCurrentIndex = 0;
    footerScrollPosition = 0;

    track.classList.add('scrolling');
    track.style.transform = 'translateX(0px)';

    setTimeout(() => {
        track.classList.remove('scrolling');
    }, 500);

    updateFooterNavButtons();
    updateFooterPagination();
}

function updateFooterPagination() {
    const pageInfo = document.getElementById('footer-page-info');
    if (pageInfo && footerTotalAds > 10) {
        const currentPage = Math.floor(footerCurrentIndex / footerCardsPerView) + 1;
        const totalPages = Math.ceil((footerTotalAds - footerCardsPerView) / footerCardsPerView) + 1;
        pageInfo.textContent = `${currentPage} of ${totalPages}`;

        // Also update the page info with partner range
        const startPartner = footerCurrentIndex + 1;
        const endPartner = Math.min(footerCurrentIndex + footerCardsPerView, footerTotalAds);
        pageInfo.title = `Showing partners ${startPartner}-${endPartner} of ${footerTotalAds}`;
    }
}

function toggleFooterAutoScroll() {
    const toggleBtn = document.getElementById('footer-auto-toggle');
    if (!toggleBtn) return;

    footerAutoScrollEnabled = !footerAutoScrollEnabled;

    if (footerAutoScrollEnabled) {
        toggleBtn.classList.add('active');
        toggleBtn.innerHTML = '<i class="fas fa-pause"></i>';
        toggleBtn.title = 'Pause auto-scroll';
    } else {
        toggleBtn.classList.remove('active');
        toggleBtn.innerHTML = '<i class="fas fa-play"></i>';
        toggleBtn.title = 'Start auto-scroll';
    }
}

// Update on window resize
window.addEventListener('resize', () => {
    updateFooterCardsPerView();
    footerCurrentIndex = 0;
    const track = document.getElementById('footer-ads-track');
    if (track) {
        track.style.transform = 'translateX(0)';
    }
    updateFooterNavButtons();
});
</script>
