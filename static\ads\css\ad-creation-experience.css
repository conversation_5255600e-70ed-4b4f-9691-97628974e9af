/* 
 * Ad Creation Experience - Enterprise Grade Styling
 * Provides a sleek, modern, and exciting ad creation flow
 */

/* Step Navigation Styles */
.step-navigation {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
    position: relative;
    z-index: 1;
}

.step-navigation::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background-color: rgba(0, 0, 0, 0.1);
    z-index: -1;
    transform: translateY(-50%);
}

.step-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
    background-color: #fff;
    padding: 0 10px;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #e0e0e0, #f5f5f5);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    color: #757575;
    margin-bottom: 8px;
    position: relative;
    transition: all 0.3s ease;
    border: 2px solid #e0e0e0;
}

.step-number::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3949ab, #5c6bc0);
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.step-label {
    font-size: 14px;
    color: #757575;
    font-weight: 500;
    transition: all 0.3s ease;
}

/* Active Step Styles */
.step-item.active .step-number {
    background: linear-gradient(135deg, #3949ab, #5c6bc0);
    color: white;
    border-color: #3949ab;
    box-shadow: 0 5px 15px rgba(57, 73, 171, 0.3);
    transform: scale(1.1);
}

.step-item.active .step-label {
    color: #3949ab;
    font-weight: 600;
}

/* Completed Step Styles */
.step-item.completed .step-number {
    background: linear-gradient(135deg, #4caf50, #8bc34a);
    color: white;
    border-color: #4caf50;
}

.step-item.completed .step-number::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* Step Content Styles */
.step-content {
    background-color: #fff;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.05);
    padding: 30px;
    margin-bottom: 30px;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    border: 1px solid rgba(0,0,0,0.03);
}

.step-content h3 {
    color: #1a237e;
    margin-bottom: 25px;
    position: relative;
    padding-bottom: 15px;
}

.step-content h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #3949ab, #5c6bc0);
    border-radius: 3px;
}

/* Form Field Animations */
.form-group {
    margin-bottom: 25px;
    transition: all 0.3s ease;
    transform: translateY(0);
    opacity: 1;
}

.form-group.animate-in {
    animation: formFieldFadeIn 0.5s ease forwards;
}

@keyframes formFieldFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Next/Prev Button Styles */
.step-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
}

.btn-next, .btn-prev, .btn-submit {
    border-radius: 30px;
    padding: 12px 30px;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    z-index: 1;
    border: none;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-next, .btn-submit {
    background: linear-gradient(90deg, #3949ab, #5c6bc0);
    color: white;
    box-shadow: 0 4px 15px rgba(57, 73, 171, 0.2);
}

.btn-next:hover, .btn-submit:hover {
    box-shadow: 0 6px 20px rgba(57, 73, 171, 0.3);
    transform: translateY(-2px);
}

.btn-prev {
    background: transparent;
    color: #3949ab;
    border: 1px solid #3949ab;
}

.btn-prev:hover {
    background: rgba(57, 73, 171, 0.05);
    transform: translateY(-2px);
}

/* Button Icon Animation */
.btn-next i, .btn-submit i {
    transition: transform 0.3s ease;
}

.btn-next:hover i, .btn-submit:hover i {
    transform: translateX(5px);
}

.btn-prev i {
    transition: transform 0.3s ease;
}

.btn-prev:hover i {
    transform: translateX(-5px);
}

/* Preview Section Styles */
.preview-section {
    background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
    border-radius: 16px;
    padding: 25px;
    height: 100%;
    position: relative;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.05);
    border: 1px solid rgba(0,0,0,0.03);
}

.preview-section::before {
    content: '';
    position: absolute;
    top: -50px;
    right: -50px;
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, rgba(57, 73, 171, 0.05) 0%, rgba(57, 73, 171, 0) 70%);
    border-radius: 50%;
    z-index: 0;
}

.preview-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(0,0,0,0.05);
}

.preview-title {
    font-size: 18px;
    font-weight: 700;
    color: #1a237e;
    margin-bottom: 5px;
}

.preview-subtitle {
    font-size: 14px;
    color: #757575;
}

.preview-content {
    background-color: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.03);
    transition: all 0.3s ease;
}

.preview-content:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.05);
    transform: translateY(-3px);
}

/* Success Animation */
.success-animation {
    text-align: center;
    padding: 40px 20px;
}

.success-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #4caf50, #8bc34a);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    position: relative;
    animation: successPulse 2s infinite;
}

.success-icon i {
    color: white;
    font-size: 40px;
}

@keyframes successPulse {
    0% {
        box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.4);
    }
    70% {
        box-shadow: 0 0 0 20px rgba(76, 175, 80, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
    }
}

/* Mobile Styles */
@media (max-width: 768px) {
    .step-navigation {
        overflow-x: auto;
        padding-bottom: 10px;
    }
    
    .step-item {
        min-width: 100px;
    }
    
    .preview-section {
        margin-top: 30px;
    }
    
    .step-buttons {
        flex-direction: column;
        gap: 15px;
    }
    
    .btn-prev {
        order: 2;
    }
    
    .btn-next, .btn-submit {
        order: 1;
    }
}

/* Tab Transition Animations */
.tab-pane {
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.tab-pane.fade:not(.show) {
    opacity: 0;
    transform: translateX(-20px);
    display: none;
}

.tab-pane.fade.show {
    opacity: 1;
    transform: translateX(0);
}
