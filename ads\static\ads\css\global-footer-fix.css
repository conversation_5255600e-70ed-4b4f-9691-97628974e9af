/* Global Footer Fix for All Dashboards */

/* Override the default margin-top from enterprise-footer.css */
.enterprise-footer {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
}

/* Fix for dashboard pages */
.enterprise-dashboard {
    min-height: calc(100vh - 65px) !important; /* Adjust for bottom nav height */
    display: flex;
    flex-direction: column;
}

/* Fix for dashboard content to take up available space */
.dashboard-content {
    flex: 1 0 auto;
}

/* Remove any extra padding that might be causing space */
.footer-main {
    padding-bottom: 0 !important;
}

/* Fix for bottom margin on last dashboard section */
.dashboard-section:last-child {
    margin-bottom: 0 !important;
}

/* Fix for mobile bottom navigation */
@media (max-width: 991.98px) {
    .enterprise-footer {
        padding-bottom: 65px !important; /* Match bottom nav height */
    }
}

/* Landscape mode adjustments */
@media (max-width: 767.98px) and (orientation: landscape) {
    .enterprise-footer {
        padding-bottom: 55px !important; /* Match landscape bottom nav height */
    }
}

/* Fix for main content to push footer down */
main {
    flex: 1 0 auto !important;
    display: flex !important;
    flex-direction: column !important;
}

/* Fix for container inside main */
main > .container {
    flex: 1 0 auto !important;
    display: flex !important;
    flex-direction: column !important;
}

/* Fix for row inside container */
main > .container > .row {
    flex: 1 0 auto !important;
}

/* Fix for corporate section */
.corporate-section {
    flex: 1 0 auto !important;
    display: flex !important;
    flex-direction: column !important;
}

/* Fix for content block */
#content {
    flex: 1 0 auto !important;
}

/* Fix for any nested containers */
.dashboard-container {
    flex: 1 0 auto !important;
}

/* Fix for tab content */
.tab-content {
    flex: 1 0 auto !important;
}

/* Fix for tab panes */
.tab-pane {
    flex: 1 0 auto !important;
}

/* Fix for any other containers that might be causing issues */
.content-container,
.page-container,
.app-container {
    flex: 1 0 auto !important;
}

/* Fix for any margin on the last element before the footer */
main > *:last-child,
.container > *:last-child,
.row > *:last-child,
.corporate-section > *:last-child,
.dashboard-section:last-child > *:last-child,
.tab-pane > *:last-child {
    margin-bottom: 0 !important;
}
