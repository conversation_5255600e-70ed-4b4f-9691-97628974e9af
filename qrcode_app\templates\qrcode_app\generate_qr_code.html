{% extends "base.html" %}
{% load static %}
{% load widget_tweaks %}

{% block title %}Generate QR Code | Enterprise QR{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/qr-code-styles.css' %}">
<style>
    /* Ultra-Premium Enterprise Corporate Styling */
    body {
        background:
            linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #0f3460 50%, #533483 75%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        position: relative;
        overflow-x: hidden;
    }

    /* Premium animated background with corporate patterns */
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 15% 85%, rgba(102, 126, 234, 0.4) 0%, transparent 40%),
            radial-gradient(circle at 85% 15%, rgba(255, 255, 255, 0.15) 0%, transparent 35%),
            radial-gradient(circle at 45% 55%, rgba(118, 75, 162, 0.3) 0%, transparent 45%),
            radial-gradient(circle at 75% 75%, rgba(83, 52, 131, 0.25) 0%, transparent 30%);
        z-index: -1;
        animation: premiumBackgroundFloat 25s ease-in-out infinite;
    }

    /* Corporate grid pattern overlay */
    body::after {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image:
            linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
            linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
        background-size: 50px 50px;
        z-index: -1;
        opacity: 0.5;
        animation: gridPulse 8s ease-in-out infinite;
    }

    @keyframes premiumBackgroundFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
        25% { transform: translateY(-15px) rotate(1deg) scale(1.02); }
        50% { transform: translateY(5px) rotate(-0.5deg) scale(0.98); }
        75% { transform: translateY(-8px) rotate(0.8deg) scale(1.01); }
    }

    @keyframes gridPulse {
        0%, 100% { opacity: 0.3; }
        50% { opacity: 0.6; }
    }

    .enterprise-container {
        padding: 4rem 2rem;
        max-width: 1500px;
        margin: 0 auto;
        position: relative;
        z-index: 1;
    }

    /* Premium Corporate Header */
    .page-header {
        margin-bottom: 5rem;
        position: relative;
        padding: 3rem 0;
        text-align: center;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
        border-radius: 30px;
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow:
            0 20px 40px rgba(0, 0, 0, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        border-radius: 30px;
        z-index: -1;
    }

    .page-header::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 200px;
        height: 6px;
        background: linear-gradient(90deg,
            rgba(102, 126, 234, 0.8) 0%,
            rgba(255, 255, 255, 0.9) 50%,
            rgba(118, 75, 162, 0.8) 100%);
        border-radius: 6px;
        box-shadow:
            0 4px 15px rgba(102, 126, 234, 0.4),
            0 0 30px rgba(255, 255, 255, 0.3);
        animation: headerLineGlow 3s ease-in-out infinite;
    }

    @keyframes headerLineGlow {
        0%, 100% {
            box-shadow:
                0 4px 15px rgba(102, 126, 234, 0.4),
                0 0 30px rgba(255, 255, 255, 0.3);
        }
        50% {
            box-shadow:
                0 6px 25px rgba(102, 126, 234, 0.6),
                0 0 50px rgba(255, 255, 255, 0.5);
        }
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes gradientPulse {
        0% {
            background-position: 0% 50%;
        }
        50% {
            background-position: 100% 50%;
        }
        100% {
            background-position: 0% 50%;
        }
    }

    /* Premium Corporate Title */
    .page-title {
        font-weight: 900;
        background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 50%, #ffffff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 2rem;
        font-size: 4rem;
        letter-spacing: -0.03em;
        display: flex;
        align-items: center;
        justify-content: center;
        animation: premiumTitleEntry 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        text-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
        line-height: 1.1;
        position: relative;
    }

    .page-title::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
        -webkit-background-clip: text;
        background-clip: text;
        z-index: -1;
        animation: titleGlow 4s ease-in-out infinite;
    }

    .page-title i {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #ffffff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-right: 1.5rem;
        font-size: 3.5rem;
        animation: premiumIconFloat 4s ease-in-out infinite;
        filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.4));
        position: relative;
    }

    @keyframes premiumTitleEntry {
        0% {
            opacity: 0;
            transform: translateY(30px) scale(0.9);
            filter: blur(10px);
        }
        100% {
            opacity: 1;
            transform: translateY(0) scale(1);
            filter: blur(0px);
        }
    }

    @keyframes premiumIconFloat {
        0%, 100% {
            transform: translateY(0px) rotate(0deg) scale(1);
            filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.4));
        }
        25% {
            transform: translateY(-8px) rotate(2deg) scale(1.05);
            filter: drop-shadow(0 12px 24px rgba(102, 126, 234, 0.3));
        }
        75% {
            transform: translateY(-4px) rotate(-1deg) scale(1.02);
            filter: drop-shadow(0 10px 20px rgba(118, 75, 162, 0.3));
        }
    }

    @keyframes titleGlow {
        0%, 100% { opacity: 0.3; }
        50% { opacity: 0.7; }
    }

    /* Premium Corporate Subtitle */
    .page-subtitle {
        color: rgba(255, 255, 255, 0.95);
        font-size: 1.4rem;
        max-width: 800px;
        margin: 0 auto;
        line-height: 1.8;
        animation: premiumSubtitleEntry 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        animation-delay: 0.3s;
        opacity: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        font-weight: 500;
        letter-spacing: 0.5px;
        position: relative;
    }

    .page-subtitle::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
        z-index: -1;
        border-radius: 15px;
        backdrop-filter: blur(5px);
    }

    @keyframes premiumSubtitleEntry {
        0% {
            opacity: 0;
            transform: translateY(20px);
            filter: blur(5px);
        }
        100% {
            opacity: 1;
            transform: translateY(0);
            filter: blur(0px);
        }
    }

    /* Ultra-Premium Enterprise Card Styling */
    .enterprise-card {
        border-radius: 32px;
        overflow: hidden;
        transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.2),
            0 15px 30px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        border: 2px solid rgba(255, 255, 255, 0.25);
        height: 100%;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.95) 0%,
            rgba(248, 249, 250, 0.9) 50%,
            rgba(255, 255, 255, 0.95) 100%);
        backdrop-filter: blur(30px);
        position: relative;
        animation: cardEntry 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }

    .enterprise-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg,
            rgba(102, 126, 234, 0.08) 0%,
            rgba(255, 255, 255, 0.1) 50%,
            rgba(118, 75, 162, 0.08) 100%);
        z-index: 0;
        pointer-events: none;
        border-radius: 32px;
    }

    .enterprise-card::after {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        width: calc(100% + 4px);
        height: calc(100% + 4px);
        background: linear-gradient(135deg,
            rgba(102, 126, 234, 0.3) 0%,
            rgba(255, 255, 255, 0.2) 50%,
            rgba(118, 75, 162, 0.3) 100%);
        border-radius: 34px;
        z-index: -1;
        opacity: 0;
        transition: opacity 0.6s ease;
    }

    .enterprise-card:hover {
        box-shadow:
            0 40px 80px rgba(0, 0, 0, 0.25),
            0 20px 40px rgba(0, 0, 0, 0.15),
            0 0 60px rgba(102, 126, 234, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.4);
        transform: translateY(-12px) scale(1.03);
        border-color: rgba(255, 255, 255, 0.4);
    }

    .enterprise-card:hover::after {
        opacity: 1;
    }

    @keyframes cardEntry {
        0% {
            opacity: 0;
            transform: translateY(40px) scale(0.95);
            filter: blur(10px);
        }
        100% {
            opacity: 1;
            transform: translateY(0) scale(1);
            filter: blur(0px);
        }
    }

    /* Ultra-Premium Card Header */
    .card-header {
        padding: 3rem 3rem;
        border-bottom: none;
        position: relative;
        overflow: hidden;
        background: linear-gradient(135deg,
            #1a1a2e 0%,
            #16213e 25%,
            #0f3460 50%,
            #533483 75%,
            #764ba2 100%);
        border-radius: 32px 32px 0 0;
    }

    .card-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.15) 0%,
            rgba(255, 255, 255, 0.08) 50%,
            rgba(255, 255, 255, 0.15) 100%);
        z-index: 0;
    }

    .card-header::after {
        content: '';
        position: absolute;
        top: -100%;
        right: -100%;
        width: 200%;
        height: 300%;
        background: conic-gradient(from 0deg,
            rgba(102, 126, 234, 0.3) 0deg,
            rgba(255, 255, 255, 0.2) 90deg,
            rgba(118, 75, 162, 0.3) 180deg,
            rgba(255, 255, 255, 0.1) 270deg,
            rgba(102, 126, 234, 0.3) 360deg);
        z-index: 1;
        animation: premiumHeaderGlow 6s linear infinite;
    }

    @keyframes premiumHeaderGlow {
        0% { transform: rotate(0deg); opacity: 0.4; }
        50% { opacity: 0.8; }
        100% { transform: rotate(360deg); opacity: 0.4; }
    }

    .card-header h2, .card-header h3 {
        position: relative;
        z-index: 3;
        margin: 0;
        color: white;
        font-weight: 800;
        font-size: 1.4rem;
        text-shadow:
            0 4px 20px rgba(0, 0, 0, 0.4),
            0 2px 10px rgba(0, 0, 0, 0.3);
        letter-spacing: 1px;
        text-transform: uppercase;
        background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .card-header i {
        margin-right: 1rem;
        font-size: 1.3rem;
        filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
        animation: headerIconPulse 3s ease-in-out infinite;
    }

    @keyframes headerIconPulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.1); }
    }

    .card-body {
        padding: 2.5rem;
        position: relative;
        z-index: 1;
        background: rgba(255, 255, 255, 0.95);
    }

    /* Enterprise-Grade Preview Container Styling */
    .preview-container {
        height: 100%;
        display: flex;
        flex-direction: column;
        position: relative;
    }

    .preview-wrapper {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 3rem;
        position: relative;
        overflow: hidden;
        border-radius: 24px;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.98) 0%,
            rgba(248, 250, 252, 0.95) 25%,
            rgba(241, 245, 249, 0.92) 50%,
            rgba(248, 250, 252, 0.95) 75%,
            rgba(255, 255, 255, 0.98) 100%);
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.08),
            0 15px 30px rgba(0, 0, 0, 0.04),
            inset 0 2px 4px rgba(255, 255, 255, 0.9),
            inset 0 -2px 4px rgba(0, 0, 0, 0.02);
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.6);
        backdrop-filter: blur(20px) saturate(180%);
        animation: previewContainerEntry 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    }

    @keyframes previewContainerEntry {
        0% {
            opacity: 0;
            transform: translateY(30px) scale(0.95);
            filter: blur(10px);
        }
        100% {
            opacity: 1;
            transform: translateY(0) scale(1);
            filter: blur(0px);
        }
    }

    .qr-watermark {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 10;
        pointer-events: none;
    }

    .qr-watermark span {
        display: inline-block;
        padding: 5px 15px;
        background-color: rgba(74, 108, 247, 0.7);
        color: white;
        font-size: 14px;
        font-weight: 600;
        border-radius: 20px;
        transform: rotate(-45deg);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        letter-spacing: 1px;
        text-transform: uppercase;
    }

    .preview-wrapper::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: radial-gradient(circle at center, rgba(74, 108, 247, 0.05) 0%, rgba(255, 255, 255, 0) 70%);
        z-index: 0;
    }

    .preview-status {
        position: absolute;
        top: 1.5rem;
        right: 1.5rem;
        z-index: 15;
        animation: statusBadgeEntry 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.5s both;
    }

    @keyframes statusBadgeEntry {
        0% {
            opacity: 0;
            transform: translateX(20px) scale(0.8);
        }
        100% {
            opacity: 1;
            transform: translateX(0) scale(1);
        }
    }

    .preview-badge {
        padding: 0.75rem 1.25rem;
        border-radius: 50px;
        font-weight: 700;
        font-size: 0.85rem;
        box-shadow:
            0 8px 25px rgba(74, 108, 247, 0.3),
            0 4px 12px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        display: flex;
        align-items: center;
        gap: 0.75rem;
        background: linear-gradient(135deg, #4a6cf7 0%, #6366f1 25%, #8b5cf6 75%, #a855f7 100%);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        text-transform: uppercase;
        letter-spacing: 0.5px;
        position: relative;
        overflow: hidden;
    }

    .preview-badge::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        animation: badgeShimmer 3s infinite;
    }

    @keyframes badgeShimmer {
        0% { left: -100%; }
        50% { left: 100%; }
        100% { left: 100%; }
    }

    .pulse-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: white;
        display: inline-block;
        animation: pulse-animation 2s infinite;
    }

    @keyframes pulse-animation {
        0% {
            box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
        }
    }

    /* Button pulse animation */
    @keyframes btn-pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(74, 108, 247, 0.5);
        }
        70% {
            box-shadow: 0 0 0 15px rgba(74, 108, 247, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(74, 108, 247, 0);
        }
    }

    .btn-pulse {
        animation: btn-pulse 1.5s ease-out;
    }

    /* Enterprise QR Preview Styling */
    #qr-preview {
        min-height: 380px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        z-index: 5;
        transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        padding: 2.5rem;
        border-radius: 20px;
        background:
            radial-gradient(circle at 30% 20%, rgba(74, 108, 247, 0.05) 0%, transparent 50%),
            radial-gradient(circle at 70% 80%, rgba(139, 92, 246, 0.05) 0%, transparent 50%),
            linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
        backdrop-filter: blur(15px) saturate(120%);
        border: 2px solid rgba(255, 255, 255, 0.6);
        box-shadow:
            inset 0 2px 8px rgba(255, 255, 255, 0.9),
            inset 0 -2px 8px rgba(0, 0, 0, 0.02),
            0 20px 40px rgba(0, 0, 0, 0.06);
        overflow: hidden;
    }

    #qr-preview::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg,
            rgba(74, 108, 247, 0.02) 0%,
            transparent 25%,
            transparent 75%,
            rgba(139, 92, 246, 0.02) 100%);
        pointer-events: none;
        z-index: 1;
    }

    #qr-preview > div {
        position: relative;
        z-index: 2;
        animation: qrCodeEntryProfessional 1s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        transform-origin: center;
    }

    @keyframes qrCodeEntryProfessional {
        0% {
            opacity: 0;
            transform: scale(0.85) rotateY(10deg) translateY(20px);
            filter: blur(8px) brightness(0.8);
        }
        50% {
            opacity: 0.7;
            transform: scale(0.95) rotateY(5deg) translateY(10px);
            filter: blur(4px) brightness(0.9);
        }
        100% {
            opacity: 1;
            transform: scale(1) rotateY(0deg) translateY(0px);
            filter: blur(0px) brightness(1);
        }
    }

    #qr-preview svg {
        max-width: 100%;
        height: auto;
        border-radius: 16px;
        box-shadow:
            0 20px 40px rgba(0, 0, 0, 0.08),
            0 10px 25px rgba(0, 0, 0, 0.04),
            0 0 0 1px rgba(255, 255, 255, 0.9),
            inset 0 1px 0 rgba(255, 255, 255, 0.8);
        transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
        position: relative;
    }

    #qr-preview svg:hover {
        transform: scale(1.02) translateY(-3px) rotateX(2deg);
        box-shadow:
            0 30px 60px rgba(0, 0, 0, 0.12),
            0 20px 40px rgba(0, 0, 0, 0.06),
            0 0 0 1px rgba(255, 255, 255, 0.95),
            inset 0 1px 0 rgba(255, 255, 255, 0.9);
    }

    /* Professional placeholder text */
    #qr-preview p {
        color: #64748b;
        font-size: 1.1rem;
        font-weight: 500;
        text-align: center;
        margin: 0;
        padding: 2rem;
        background: linear-gradient(135deg, rgba(74, 108, 247, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
        border-radius: 12px;
        border: 2px dashed rgba(74, 108, 247, 0.2);
        animation: placeholderPulse 3s ease-in-out infinite;
    }

    @keyframes placeholderPulse {
        0%, 100% {
            opacity: 0.7;
            transform: scale(1);
        }
        50% {
            opacity: 1;
            transform: scale(1.01);
        }
    }

    /* Enterprise Preview Info Styling */
    .preview-info {
        margin-top: 2rem;
        animation: previewInfoEntry 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.3s both;
    }

    @keyframes previewInfoEntry {
        0% {
            opacity: 0;
            transform: translateY(20px);
        }
        100% {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .preview-stats-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .preview-stat-card {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%);
        border: 1px solid rgba(255, 255, 255, 0.6);
        border-radius: 16px;
        padding: 1.5rem 1rem;
        text-align: center;
        backdrop-filter: blur(10px);
        box-shadow:
            0 8px 20px rgba(0, 0, 0, 0.04),
            inset 0 1px 0 rgba(255, 255, 255, 0.8);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
    }

    .preview-stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(74, 108, 247, 0.1), transparent);
        transition: left 0.6s ease;
    }

    .preview-stat-card:hover::before {
        left: 100%;
    }

    .preview-stat-card:hover {
        transform: translateY(-2px);
        box-shadow:
            0 12px 30px rgba(0, 0, 0, 0.08),
            inset 0 1px 0 rgba(255, 255, 255, 0.9);
        border-color: rgba(74, 108, 247, 0.2);
    }

    .stat-icon {
        width: 48px;
        height: 48px;
        margin: 0 auto 1rem;
        background: linear-gradient(135deg, #4a6cf7 0%, #6366f1 100%);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 12px rgba(74, 108, 247, 0.3);
    }

    .stat-icon i {
        color: white;
        font-size: 1.2rem;
    }

    .stat-content {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }

    .stat-label {
        font-size: 0.8rem;
        font-weight: 600;
        color: #64748b;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .stat-value {
        font-size: 1rem;
        font-weight: 700;
        color: #1e293b;
    }

    .preview-actions {
        display: grid;
        gap: 1rem;
    }

    .btn-enterprise {
        position: relative;
        overflow: hidden;
        border-radius: 16px;
        padding: 1.2rem 2rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        border: none;
    }

    .btn-enterprise .btn-content {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
        position: relative;
        z-index: 2;
    }

    .btn-enterprise .btn-shine {
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        transition: left 0.6s ease;
        z-index: 1;
    }

    .btn-enterprise:hover .btn-shine {
        left: 100%;
    }

    .btn-enterprise.btn-primary {
        background: linear-gradient(135deg, #4a6cf7 0%, #6366f1 25%, #8b5cf6 75%, #a855f7 100%);
        color: white;
        box-shadow:
            0 12px 30px rgba(74, 108, 247, 0.4),
            0 6px 15px rgba(0, 0, 0, 0.1);
    }

    .btn-enterprise.btn-primary:hover {
        transform: translateY(-3px) scale(1.02);
        box-shadow:
            0 20px 40px rgba(74, 108, 247, 0.5),
            0 10px 25px rgba(0, 0, 0, 0.15);
    }

    .btn-enterprise.btn-outline-primary {
        background: rgba(255, 255, 255, 0.9);
        color: #4a6cf7;
        border: 2px solid #4a6cf7;
        backdrop-filter: blur(10px);
    }

    .btn-enterprise.btn-outline-primary:hover {
        background: linear-gradient(135deg, #4a6cf7 0%, #6366f1 100%);
        color: white;
        transform: translateY(-3px) scale(1.02);
        box-shadow:
            0 15px 35px rgba(74, 108, 247, 0.4),
            0 8px 20px rgba(0, 0, 0, 0.1);
        border-color: transparent;
    }

    /* Sticky Preview Styling */
    .sticky-preview {
        position: sticky;
        top: 2rem;
        height: fit-content;
        max-height: calc(100vh - 4rem);
        overflow-y: auto;
    }

    /* Elegant form focus effects */
    .form-control:focus, .form-select:focus {
        transform: translateY(-2px);
        transition: transform 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease;
    }

    /* Modern Form Styling */
    .form-label {
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0.8rem;
        font-size: 0.95rem;
        letter-spacing: 0.5px;
        text-transform: uppercase;
    }

    .form-control, .form-select {
        border-radius: 16px;
        padding: 1rem 1.5rem;
        border: 2px solid rgba(102, 126, 234, 0.1);
        transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        font-size: 1rem;
        font-weight: 500;
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow:
            0 0 0 0.25rem rgba(102, 126, 234, 0.15),
            0 8px 25px rgba(102, 126, 234, 0.1);
        transform: translateY(-2px);
        background: rgba(255, 255, 255, 1);
    }

    /* Modern Button Styling */
    .btn {
        border-radius: 16px;
        padding: 1rem 2rem;
        font-weight: 700;
        font-size: 1rem;
        letter-spacing: 0.5px;
        transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        position: relative;
        overflow: hidden;
        text-transform: uppercase;
        border: none;
    }

    .btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        transition: left 0.6s ease;
    }

    .btn:hover::before {
        left: 100%;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        box-shadow:
            0 8px 25px rgba(102, 126, 234, 0.3),
            0 4px 15px rgba(0, 0, 0, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.2);
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        transform: translateY(-4px) scale(1.02);
        box-shadow:
            0 15px 40px rgba(102, 126, 234, 0.4),
            0 8px 25px rgba(0, 0, 0, 0.15);
        border-color: rgba(255, 255, 255, 0.3);
    }

    .btn-outline-primary {
        color: #667eea;
        border: 2px solid #667eea;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
    }

    .btn-outline-primary:hover {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        transform: translateY(-4px) scale(1.02);
        box-shadow:
            0 12px 30px rgba(102, 126, 234, 0.3),
            0 6px 20px rgba(0, 0, 0, 0.1);
        border-color: transparent;
    }

    /* Customization panel styling */
    .customization-panel {
        margin-top: 2rem;
        padding: 1.5rem;
        border-radius: 12px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: 1px solid #e9ecef;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        display: none; /* Hidden by default */
    }

    .customization-panel.show {
        display: block; /* Show when .show class is added */
        animation: fadeIn 0.3s ease-in-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* Customize button hover effect */
    #customize-btn:hover {
        background-color: rgba(74, 108, 247, 0.15);
        transform: translateY(-2px);
        transition: all 0.3s ease;
    }

    .customization-section {
        margin-bottom: 1.5rem;
        padding-bottom: 1.5rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .customization-section:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
    }

    .customization-section h4 {
        font-size: 1.1rem;
        margin-bottom: 1rem;
        color: #495057;
        font-weight: 600;
        position: relative;
        padding-left: 1rem;
    }

    .customization-section h4::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 18px;
        background: linear-gradient(to bottom, #4a6cf7, #6366f1);
        border-radius: 2px;
    }

    /* Modern Responsive Design */
    @media (max-width: 991.98px) {
        .enterprise-container {
            padding: 2rem 1rem;
        }

        .page-title {
            font-size: 2.8rem;
        }

        .page-title i {
            font-size: 2.4rem;
        }

        .page-subtitle {
            font-size: 1.1rem;
            padding: 0 1rem;
        }

        .preview-card {
            margin-top: 3rem;
        }

        .card-body {
            padding: 2rem;
        }

        .preview-wrapper {
            padding: 2rem;
        }
    }

    @media (max-width: 767.98px) {
        .enterprise-container {
            padding: 1.5rem 1rem;
        }

        .page-title {
            font-size: 2.2rem;
            flex-direction: column;
            margin-bottom: 1rem;
        }

        .page-title i {
            margin-right: 0;
            margin-bottom: 0.8rem;
            font-size: 2rem;
        }

        .page-subtitle {
            font-size: 1rem;
        }

        .page-header {
            margin-bottom: 2.5rem;
            padding-bottom: 1.5rem;
        }

        .page-header::after {
            width: 100px;
        }

        .card-body {
            padding: 1.5rem;
        }

        .preview-wrapper {
            padding: 1.5rem;
        }

        .btn {
            padding: 0.8rem 1.5rem;
            font-size: 0.9rem;
        }

        .form-control, .form-select {
            padding: 0.8rem 1.2rem;
        }
    }

    @media (max-width: 575.98px) {
        .page-title {
            font-size: 1.8rem;
        }

        .page-title i {
            font-size: 1.6rem;
        }

        .page-subtitle {
            font-size: 0.9rem;
        }

        .enterprise-card {
            border-radius: 16px;
        }

        .card-header {
            padding: 1.5rem;
        }

        .card-body {
            padding: 1.2rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="enterprise-container">
    <div class="page-header">
        <h1 class="page-title">
            <i class="fas fa-qrcode"></i>
            <span>{% if is_editing %}Edit{% else %}Generate{% endif %} Enterprise QR Code</span>
        </h1>
        <p class="page-subtitle">{% if is_editing %}Modify your existing QR code with real-time preview and advanced customization options{% else %}Create professional QR codes with instant real-time preview and advanced customization options{% endif %}</p>
    </div>

    <div class="row">
        <!-- Left Column: QR Code Form -->
        <div class="col-lg-6">
            <div class="enterprise-card">
                <div class="card-header">
                    <h2 class="h5 mb-0 text-white">
                        <i class="fas fa-edit me-2"></i>QR Code Details
                    </h2>
                </div>
                <div class="card-body">
                    <form method="post" action="{% url 'generate_qr_code' %}" enctype="multipart/form-data" id="qr-form">
                        {% csrf_token %}
                        <!-- This form will submit to the generate_qr_code view, which will redirect to qr_code_detail.html -->
                        <input type="hidden" name="redirect_to_detail" value="true">

                        <!-- Hidden fields to preserve customizations -->
                        <input type="hidden" name="dot_style" id="hidden-dot-style" value="">
                        <input type="hidden" name="corner_style" id="hidden-corner-style" value="">
                        <input type="hidden" name="frame_style" id="hidden-frame-style" value="">
                        <input type="hidden" name="title_text" id="hidden-title-text" value="">
                        <input type="hidden" name="guiding_text" id="hidden-guiding-text" value="">
                        <input type="hidden" name="guiding_text_position" id="hidden-guiding-text-position" value="">

                        <div class="mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">Name</label>
                            {{ form.name|add_class:"form-control" }}
                            {% if form.name.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.name.errors }}
                            </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.qr_type.id_for_label }}" class="form-label">QR Code Type</label>
                            {{ form.qr_type|add_class:"form-select" }}
                            {% if form.qr_type.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.qr_type.errors }}
                            </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.data.id_for_label }}" class="form-label">Data</label>
                            {{ form.data|add_class:"form-control" }}
                            {% if form.data.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.data.errors }}
                            </div>
                            {% endif %}
                            <div class="form-text" id="data-help">
                                Enter the data to encode in the QR code.
                            </div>
                        </div>

                        <!-- File Upload Field -->
                        <div class="mb-3 file-upload-container" id="file-upload-container">
                            <label for="{{ form.uploaded_file.id_for_label }}" class="form-label">
                                <i class="fas fa-upload me-1"></i> Upload File
                            </label>
                            <div class="file-upload-wrapper">
                                {{ form.uploaded_file|add_class:"form-control" }}
                                {% if form.uploaded_file.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.uploaded_file.errors }}
                                </div>
                                {% endif %}
                                <div class="form-text" id="file-upload-help">
                                    Upload a file to generate a QR code that links to it.
                                </div>
                            </div>
                            <div class="file-upload-preview mt-2" id="file-upload-preview"></div>
                        </div>

                        <!-- Type-specific forms -->
                        <div id="url-form" class="qr-type-specific">
                            <div class="mb-3">
                                <label for="url-input" class="form-label">URL</label>
                                <input type="url" class="form-control" id="url-input" placeholder="https://example.com">
                                <div class="form-text">Enter a valid URL starting with http:// or https://</div>
                            </div>
                        </div>

                        <div id="vcard-form" class="qr-type-specific">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="vcard-name" class="form-label">Full Name</label>
                                        <input type="text" class="form-control" id="vcard-name">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="vcard-company" class="form-label">Company</label>
                                        <input type="text" class="form-control" id="vcard-company">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="vcard-phone" class="form-label">Phone</label>
                                        <input type="tel" class="form-control" id="vcard-phone">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="vcard-email" class="form-label">Email</label>
                                        <input type="email" class="form-control" id="vcard-email">
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="vcard-address" class="form-label">Address</label>
                                <input type="text" class="form-control" id="vcard-address">
                            </div>
                            <div class="mb-3">
                                <label for="vcard-website" class="form-label">Website</label>
                                <input type="url" class="form-control" id="vcard-website">
                            </div>
                            <button type="button" class="btn btn-outline-primary" id="generate-vcard">Generate vCard Format</button>
                        </div>

                        <div id="wifi-form" class="qr-type-specific">
                            <div class="mb-3">
                                <label for="wifi-ssid" class="form-label">Network Name (SSID)</label>
                                <input type="text" class="form-control" id="wifi-ssid">
                            </div>
                            <div class="mb-3">
                                <label for="wifi-password" class="form-label">Password</label>
                                <input type="text" class="form-control" id="wifi-password">
                            </div>
                            <div class="mb-3">
                                <label for="wifi-type" class="form-label">Encryption Type</label>
                                <select class="form-select" id="wifi-type">
                                    <option value="WPA">WPA/WPA2</option>
                                    <option value="WEP">WEP</option>
                                    <option value="nopass">No Password</option>
                                </select>
                            </div>
                            <button type="button" class="btn btn-outline-primary" id="generate-wifi">Generate WiFi Format</button>
                        </div>

                        <div class="row mt-4">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.foreground_color.id_for_label }}" class="form-label">Foreground Color</label>
                                    {{ form.foreground_color|add_class:"form-control" }}
                                    {% if form.foreground_color.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.foreground_color.errors }}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.background_color.id_for_label }}" class="form-label">Background Color</label>
                                    {{ form.background_color|add_class:"form-control" }}
                                    {% if form.background_color.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.background_color.errors }}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.logo.id_for_label }}" class="form-label">Logo (Optional)</label>
                            {{ form.logo|add_class:"form-control" }}
                            {% if form.logo.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.logo.errors }}
                            </div>
                            {% endif %}
                            <div class="form-text">
                                Upload a logo to be placed in the center of the QR code.
                            </div>
                        </div>

                        <div class="mb-3 form-check">
                            {{ form.is_encrypted|add_class:"form-check-input" }}
                            <label class="form-check-label" for="{{ form.is_encrypted.id_for_label }}">
                                Encrypt QR Code
                            </label>
                            {% if form.is_encrypted.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.is_encrypted.errors }}
                            </div>
                            {% endif %}
                            <div class="form-text">
                                Enable encryption for sensitive data.
                            </div>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-outline-primary mb-3 w-100" id="customize-btn">
                                <i class="fas fa-palette me-2"></i>Customize QR Code
                            </button>
                            <button type="submit" class="btn btn-primary" id="generate-qr-btn" data-action="create-qr">
                                <i class="fas {% if is_editing %}fa-save{% else %}fa-qrcode{% endif %} me-2"></i>{% if is_editing %}Save QR Code{% else %}Generate QR Code{% endif %}
                            </button>
                        </div>
                    </form>

                    <!-- Customization Panel -->
                    <div class="customization-panel" id="customization-panel">
                        <div class="card mb-3">
                            <div class="card-body">
                                <h3 class="h5 mb-3 d-flex align-items-center">
                                    <i class="fas fa-palette me-2 text-primary"></i>
                                    Advanced Customization Options
                                </h3>

                                <div class="customization-section">
                                    <h4>QR Code Style</h4>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="dot-style-select" class="form-label">Dot Style</label>
                                                <select class="form-select" id="dot-style-select">
                                                    <option value="square">Square (Default)</option>
                                                    <option value="dots">Rounded Dots</option>
                                                    <option value="rounded">Rounded</option>
                                                    <option value="classy">Classy</option>
                                                    <option value="classy-rounded">Classy Rounded</option>
                                                    <option value="extra-rounded">Extra Rounded</option>
                                                    <option value="diamond">Diamond</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="corner-style-select" class="form-label">Corner Style</label>
                                                <select class="form-select" id="corner-style-select">
                                                    <option value="square">Square (Default)</option>
                                                    <option value="dot">Dot</option>
                                                    <option value="rounded">Rounded</option>
                                                    <option value="extra-rounded">Extra Rounded</option>
                                                    <option value="diamond">Diamond</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="customization-section">
                                    <h4>Frame & Presentation</h4>
                                    <div class="mb-3">
                                        <label for="frame-select" class="form-label">Frame Style</label>
                                        <select class="form-select" id="frame-select">
                                            <option value="none">No Frame</option>
                                            <option value="corporate">Corporate</option>
                                            <option value="phone">Mobile Phone</option>
                                            <option value="business-card">Business Card</option>
                                            <option value="ticket">Event Ticket</option>
                                            <option value="elegant">Elegant</option>
                                            <option value="modern">Modern</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="title-text" class="form-label">Title Text</label>
                                        <input type="text" class="form-control" id="title-text" placeholder="Add a title for your QR code">
                                    </div>
                                    <div class="mb-3">
                                        <label for="guiding-text" class="form-label">Guiding Text</label>
                                        <input type="text" class="form-control" id="guiding-text" placeholder="e.g., 'Scan me'">
                                    </div>
                                    <div class="mb-3">
                                        <label for="guiding-text-position" class="form-label">Text Position</label>
                                        <select class="form-select" id="guiding-text-position">
                                            <option value="below">Below QR Code</option>
                                            <option value="above">Above QR Code</option>
                                            <option value="left">Left of QR Code</option>
                                            <option value="right">Right of QR Code</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column: Live QR Code Preview -->
        <div class="col-lg-6">
            <div class="enterprise-card sticky-preview">
                <div class="card-header">
                    <h3 class="h5 mb-0 text-white">
                        <i class="fas fa-eye me-2"></i>Live QR Code Preview
                    </h3>
                </div>
                <div class="card-body">
                    <div class="preview-container">
                        <div class="preview-status">
                            <div class="preview-badge">
                                <span class="pulse-dot"></span> Live Preview
                            </div>
                        </div>

                        <div class="preview-wrapper">
                            <div id="qr-preview" class="position-relative z-1">
                                <p class="text-muted">Start typing to see your QR code appear instantly</p>
                            </div>
                        </div>

                        <div class="preview-info">
                            <div class="preview-stats-grid">
                                <div class="preview-stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-tag"></i>
                                    </div>
                                    <div class="stat-content">
                                        <span class="stat-label">Type</span>
                                        <span class="stat-value" id="preview-type">URL</span>
                                    </div>
                                </div>
                                <div class="preview-stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-expand-arrows-alt"></i>
                                    </div>
                                    <div class="stat-content">
                                        <span class="stat-label">Size</span>
                                        <span class="stat-value">300 x 300 px</span>
                                    </div>
                                </div>
                                <div class="preview-stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-file-code"></i>
                                    </div>
                                    <div class="stat-content">
                                        <span class="stat-label">Format</span>
                                        <span class="stat-value">SVG Vector</span>
                                    </div>
                                </div>
                            </div>

                            <div class="preview-actions">
                                <button type="button" class="btn btn-primary btn-enterprise" id="download-qr" disabled>
                                    <div class="btn-content">
                                        <i class="fas fa-download"></i>
                                        <span>Download QR Code</span>
                                    </div>
                                    <div class="btn-shine"></div>
                                </button>
                                <button type="button" class="btn btn-outline-primary btn-enterprise" id="refresh-preview">
                                    <div class="btn-content">
                                        <i class="fas fa-sync-alt"></i>
                                        <span>Refresh Preview</span>
                                    </div>
                                    <div class="btn-shine"></div>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/qr-code-styling@1.6.0-rc.1/lib/qr-code-styling.min.js"></script>

<!-- Complete QR Code Functionality Fix -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Complete QR functionality script loaded');

    // Initialize QR Code library
    let qrCode;

    // Get all elements
    const customizeBtn = document.getElementById('customize-btn');
    const customizationPanel = document.getElementById('customization-panel');
    const qrPreview = document.getElementById('qr-preview'); // Use the main live preview
    const qrTypeSelect = document.getElementById('id_qr_type');
    const qrDataInput = document.getElementById('id_data');
    const fgColorInput = document.getElementById('id_foreground_color');
    const bgColorInput = document.getElementById('id_background_color');
    const previewTypeDisplay = document.getElementById('preview-type');

    // Customization elements
    const dotStyleSelect = document.getElementById('dot-style-select');
    const cornerStyleSelect = document.getElementById('corner-style-select');
    const frameSelect = document.getElementById('frame-select');
    const titleInput = document.getElementById('title-text');
    const guidingTextInput = document.getElementById('guiding-text');
    const guidingTextPositionSelect = document.getElementById('guiding-text-position');

    console.log('Elements found:', {
        customizeBtn: !!customizeBtn,
        customizationPanel: !!customizationPanel,
        qrPreview: !!qrPreview,
        qrTypeSelect: !!qrTypeSelect,
        qrDataInput: !!qrDataInput,
        dotStyleSelect: !!dotStyleSelect,
        cornerStyleSelect: !!cornerStyleSelect,
        frameSelect: !!frameSelect,
        titleInput: !!titleInput,
        guidingTextInput: !!guidingTextInput
    });

    // Initialize QR Code library
    setTimeout(() => {
        if (typeof QRCodeStyling !== 'undefined') {
            qrCode = new QRCodeStyling({
                width: 300,
                height: 300,
                type: "svg",
                data: "https://www.google.com",
                dotsOptions: {
                    color: "#000000",
                    type: "square"
                },
                backgroundOptions: {
                    color: "#FFFFFF",
                },
                cornersSquareOptions: {
                    color: "#000000",
                    type: "square"
                },
                cornersDotOptions: {
                    color: "#000000",
                    type: "square"
                }
            });

            console.log('QR Code library initialized');
            updateQRCode();
        } else {
            console.error('QRCodeStyling library not loaded');
        }
    }, 1000);

    // Customize button functionality
    if (customizeBtn && customizationPanel) {
        customizeBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Customize button clicked');

            customizationPanel.classList.toggle('show');

            if (customizationPanel.classList.contains('show')) {
                customizeBtn.innerHTML = '<i class="fas fa-times me-2"></i>Hide Customization';
                console.log('Panel shown');
            } else {
                customizeBtn.innerHTML = '<i class="fas fa-palette me-2"></i>Customize QR Code';
                console.log('Panel hidden');
            }
        });
        console.log('Customize button event listener added');
    }

    // QR Type change handler
    if (qrTypeSelect) {
        qrTypeSelect.addEventListener('change', function() {
            console.log('QR type changed to:', qrTypeSelect.value);

            // Update preview type display
            if (previewTypeDisplay) {
                previewTypeDisplay.textContent = qrTypeSelect.options[qrTypeSelect.selectedIndex].text;
            }

            // Set default data based on type
            const defaultData = getDefaultDataForType(qrTypeSelect.value);
            if (qrDataInput) {
                qrDataInput.value = defaultData;
            }

            updateQRCode();
        });
    }

    // Real-time data input handler
    if (qrDataInput) {
        qrDataInput.addEventListener('input', function() {
            console.log('Data input changed:', qrDataInput.value);
            updateQRCode();
        });
    }

    // Color input handlers
    if (fgColorInput) {
        fgColorInput.addEventListener('input', function() {
            console.log('Foreground color changed:', fgColorInput.value);
            updateQRCode();
        });
    }

    if (bgColorInput) {
        bgColorInput.addEventListener('input', function() {
            console.log('Background color changed:', bgColorInput.value);
            updateQRCode();
        });
    }

    // Customization handlers
    if (dotStyleSelect) {
        dotStyleSelect.addEventListener('change', function() {
            console.log('Dot style changed:', dotStyleSelect.value);
            updateQRCode();
        });
    }

    if (cornerStyleSelect) {
        cornerStyleSelect.addEventListener('change', function() {
            console.log('Corner style changed:', cornerStyleSelect.value);
            updateQRCode();
        });
    }

    if (frameSelect) {
        frameSelect.addEventListener('change', function() {
            console.log('Frame style changed:', frameSelect.value);
            updateQRCode();
        });
    }

    if (titleInput) {
        titleInput.addEventListener('input', function() {
            console.log('Title changed:', titleInput.value);
            updateQRCode();
        });
    }

    if (guidingTextInput) {
        guidingTextInput.addEventListener('input', function() {
            console.log('Guiding text changed:', guidingTextInput.value);
            updateQRCode();
        });
    }

    // Function to get default data for QR type
    function getDefaultDataForType(qrType) {
        switch(qrType) {
            case 'URL': return "https://www.google.com";
            case 'TEXT': return "Hello World! This is a test QR code.";
            case 'EMAIL': return "mailto:<EMAIL>?subject=Hello&body=This is a test email";
            case 'PHONE': return "tel:+1234567890";
            case 'WIFI': return "WIFI:S:TestNetwork;T:WPA;P:password123;;";
            case 'VCARD': return "BEGIN:VCARD\nVERSION:3.0\nFN:John Doe\nORG:Test Company\nTEL:+1234567890\nEMAIL:<EMAIL>\nEND:VCARD";
            default: return "https://www.google.com";
        }
    }

    // Function to update QR code
    function updateQRCode() {
        if (!qrCode) {
            console.log('QR code not ready');
            return;
        }

        console.log('Updating QR code...');

        // Get current values
        const data = qrDataInput ? qrDataInput.value || "https://www.google.com" : "https://www.google.com";
        const fgColor = fgColorInput ? fgColorInput.value || "#000000" : "#000000";
        const bgColor = bgColorInput ? bgColorInput.value || "#FFFFFF" : "#FFFFFF";
        const dotStyle = dotStyleSelect ? dotStyleSelect.value || "square" : "square";
        const cornerStyle = cornerStyleSelect ? cornerStyleSelect.value || "square" : "square";
        const frameStyle = frameSelect ? frameSelect.value || "none" : "none";
        const titleText = titleInput ? titleInput.value : "";
        const guidingText = guidingTextInput ? guidingTextInput.value : "";

        console.log('QR Code data:', { data, fgColor, bgColor, dotStyle, cornerStyle, frameStyle, titleText, guidingText });

        // Update QR code options
        qrCode.update({
            data: data,
            dotsOptions: {
                color: fgColor,
                type: dotStyle
            },
            backgroundOptions: {
                color: bgColor,
            },
            cornersSquareOptions: {
                color: fgColor,
                type: cornerStyle
            },
            cornersDotOptions: {
                color: fgColor,
                type: cornerStyle
            }
        });

        // Clear previous content and create container for title, QR, and guiding text
        if (!qrPreview) {
            console.log('QR preview container not found');
            return;
        }

        qrPreview.innerHTML = '';

        // Create wrapper for the complete QR display
        const qrWrapper = document.createElement('div');
        qrWrapper.style.textAlign = 'center';
        qrWrapper.style.fontFamily = 'Arial, sans-serif';

        // Add title above QR code if provided
        if (titleText) {
            const titleElement = document.createElement('div');
            titleElement.textContent = titleText;
            titleElement.style.fontSize = '18px';
            titleElement.style.fontWeight = 'bold';
            titleElement.style.marginBottom = '10px';
            titleElement.style.color = '#333';
            qrWrapper.appendChild(titleElement);
        }

        // Create QR code container with frame styling
        const qrContainer = document.createElement('div');
        qrContainer.style.display = 'inline-block';
        qrContainer.style.margin = '10px';

        // Apply frame styling based on selection
        if (frameStyle && frameStyle !== 'none') {
            switch(frameStyle) {
                case 'corporate':
                    qrContainer.style.border = '3px solid #2563eb';
                    qrContainer.style.borderRadius = '8px';
                    qrContainer.style.padding = '15px';
                    qrContainer.style.background = 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)';
                    qrContainer.style.boxShadow = '0 4px 12px rgba(37, 99, 235, 0.2)';
                    break;
                case 'phone':
                    qrContainer.style.border = '2px solid #1f2937';
                    qrContainer.style.borderRadius = '20px';
                    qrContainer.style.padding = '20px';
                    qrContainer.style.background = '#000000';
                    qrContainer.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.3)';
                    break;
                case 'business-card':
                    qrContainer.style.border = '1px solid #d1d5db';
                    qrContainer.style.borderRadius = '4px';
                    qrContainer.style.padding = '12px';
                    qrContainer.style.background = '#ffffff';
                    qrContainer.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
                    break;
                case 'ticket':
                    qrContainer.style.border = '2px dashed #f59e0b';
                    qrContainer.style.borderRadius = '0';
                    qrContainer.style.padding = '15px';
                    qrContainer.style.background = '#fffbeb';
                    qrContainer.style.position = 'relative';
                    break;
                case 'elegant':
                    qrContainer.style.border = '2px solid #8b5cf6';
                    qrContainer.style.borderRadius = '12px';
                    qrContainer.style.padding = '18px';
                    qrContainer.style.background = 'linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%)';
                    qrContainer.style.boxShadow = '0 6px 20px rgba(139, 92, 246, 0.15)';
                    break;
                case 'modern':
                    qrContainer.style.border = 'none';
                    qrContainer.style.borderRadius = '16px';
                    qrContainer.style.padding = '20px';
                    qrContainer.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                    qrContainer.style.boxShadow = '0 10px 30px rgba(102, 126, 234, 0.3)';
                    break;
                default:
                    // No frame styling
                    break;
            }
        }

        qrWrapper.appendChild(qrContainer);

        // Append QR code to container
        qrCode.append(qrContainer);

        // Add guiding text below QR code if provided
        if (guidingText) {
            const guidingElement = document.createElement('div');
            guidingElement.textContent = guidingText;
            guidingElement.style.fontSize = '14px';
            guidingElement.style.marginTop = '10px';
            guidingElement.style.color = '#666';
            qrWrapper.appendChild(guidingElement);
        }

        // Add the complete wrapper to preview
        qrPreview.appendChild(qrWrapper);

        // Enable download button
        const downloadBtn = document.getElementById('download-qr');
        if (downloadBtn) {
            downloadBtn.disabled = false;
        }

        // Update hidden form fields to preserve customizations
        updateHiddenFields();

        console.log('QR code updated successfully with customizations');

        // Validate QR code data
        validateQRData(data);
    }

    // Function to validate QR code data
    function validateQRData(data) {
        console.log('Validating QR data:', data);

        if (!data || data.trim() === '') {
            console.warn('QR data is empty');
            return false;
        }

        // Check if it's a valid URL
        if (data.startsWith('http://') || data.startsWith('https://')) {
            try {
                new URL(data);
                console.log('✅ Valid URL detected:', data);
                return true;
            } catch (e) {
                console.warn('❌ Invalid URL format:', data);
                return false;
            }
        }

        // Check other formats
        if (data.startsWith('mailto:')) {
            console.log('✅ Email format detected:', data);
            return true;
        }

        if (data.startsWith('tel:')) {
            console.log('✅ Phone format detected:', data);
            return true;
        }

        if (data.startsWith('WIFI:')) {
            console.log('✅ WiFi format detected:', data);
            return true;
        }

        if (data.startsWith('BEGIN:VCARD')) {
            console.log('✅ vCard format detected:', data);
            return true;
        }

        console.log('✅ Plain text format detected:', data);
        return true;
    }

    // Function to update hidden form fields with current customization values
    function updateHiddenFields() {
        const hiddenDotStyle = document.getElementById('hidden-dot-style');
        const hiddenCornerStyle = document.getElementById('hidden-corner-style');
        const hiddenFrameStyle = document.getElementById('hidden-frame-style');
        const hiddenTitleText = document.getElementById('hidden-title-text');
        const hiddenGuidingText = document.getElementById('hidden-guiding-text');
        const hiddenGuidingTextPosition = document.getElementById('hidden-guiding-text-position');

        if (hiddenDotStyle && dotStyleSelect) {
            hiddenDotStyle.value = dotStyleSelect.value || 'square';
        }
        if (hiddenCornerStyle && cornerStyleSelect) {
            hiddenCornerStyle.value = cornerStyleSelect.value || 'square';
        }
        if (hiddenFrameStyle && frameSelect) {
            hiddenFrameStyle.value = frameSelect.value || 'none';
        }
        if (hiddenTitleText && titleInput) {
            hiddenTitleText.value = titleInput.value || '';
        }
        if (hiddenGuidingText && guidingTextInput) {
            hiddenGuidingText.value = guidingTextInput.value || '';
        }
        if (hiddenGuidingTextPosition) {
            const guidingTextPositionSelect = document.getElementById('guiding-text-position');
            if (guidingTextPositionSelect) {
                hiddenGuidingTextPosition.value = guidingTextPositionSelect.value || 'below';
            }
        }

        console.log('Hidden fields updated with current customizations');
    }

    // Refresh button functionality
    const refreshBtn = document.getElementById('refresh-preview');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Refresh button clicked');

            // Add visual feedback
            refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Refreshing...';
            refreshBtn.disabled = true;

            // Refresh the QR code
            setTimeout(() => {
                updateQRCode();
                refreshBtn.innerHTML = '<i class="fas fa-sync-alt me-1"></i>Refresh Preview';
                refreshBtn.disabled = false;
            }, 500);
        });
        console.log('Refresh button event listener added');
    }

    // Download button functionality
    const downloadBtn = document.getElementById('download-qr');
    if (downloadBtn) {
        downloadBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Download button clicked');

            if (!qrCode) {
                console.error('QR code not initialized');
                alert('QR code not ready. Please wait a moment and try again.');
                return;
            }

            if (!qrDataInput || !qrDataInput.value) {
                console.error('No data to download');
                alert('Please enter some data for the QR code before downloading.');
                return;
            }

            // Add visual feedback
            downloadBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Downloading...';
            downloadBtn.disabled = true;

            try {
                // Get the name for the file
                const nameInput = document.getElementById('id_name');
                const fileName = nameInput && nameInput.value ? nameInput.value : 'qrcode';

                // Download the QR code
                qrCode.download({
                    extension: 'png',
                    name: fileName
                });

                console.log('QR code download initiated');

                // Reset button after download
                setTimeout(() => {
                    downloadBtn.innerHTML = '<i class="fas fa-download me-1"></i>Download QR Code';
                    downloadBtn.disabled = false;
                }, 1000);

            } catch (error) {
                console.error('Download error:', error);
                alert('Error downloading QR code. Please try again.');
                downloadBtn.innerHTML = '<i class="fas fa-download me-1"></i>Download QR Code';
                downloadBtn.disabled = false;
            }
        });
        console.log('Download button event listener added');
    }

    // Form submission handler to preserve customizations
    const qrForm = document.getElementById('qr-form');
    if (qrForm) {
        qrForm.addEventListener('submit', function(e) {
            console.log('Form being submitted, updating hidden fields...');

            // Update hidden fields with current customization values
            updateHiddenFields();

            // Log the values being submitted
            const hiddenFields = {
                dotStyle: document.getElementById('hidden-dot-style')?.value,
                cornerStyle: document.getElementById('hidden-corner-style')?.value,
                frameStyle: document.getElementById('hidden-frame-style')?.value,
                titleText: document.getElementById('hidden-title-text')?.value,
                guidingText: document.getElementById('hidden-guiding-text')?.value,
                guidingTextPosition: document.getElementById('hidden-guiding-text-position')?.value
            };

            console.log('Submitting customizations:', hiddenFields);

            // Allow form to submit normally
            return true;
        });

        console.log('Form submission handler added');
    }

    // Initial QR code generation
    if (qrTypeSelect && qrTypeSelect.value) {
        const defaultData = getDefaultDataForType(qrTypeSelect.value);
        if (qrDataInput && !qrDataInput.value) {
            qrDataInput.value = defaultData;
        }
        setTimeout(updateQRCode, 1500);
    }
});
</script>

<script src="{% static 'js/qr-code-preview.js' %}"></script>
{% endblock %}
