/**
 * Remove Footer Notification Bell
 * This script removes any notification bells from the footer
 */

document.addEventListener('DOMContentLoaded', function() {
    // Function to remove any notification bells from the footer
    function removeFooterNotificationBell() {
        // Find any notification bells in the footer
        const footerBells = document.querySelectorAll('footer .notification-bell, footer .social-icons .notification-bell');
        
        if (footerBells.length > 0) {
            console.log('Removing notification bell from footer');
            footerBells.forEach(bell => {
                bell.remove();
            });
        }
    }
    
    // Run immediately
    removeFooterNotificationBell();
    
    // Also run after a short delay to catch any dynamically added bells
    setTimeout(removeFooterNotificationBell, 500);
});
