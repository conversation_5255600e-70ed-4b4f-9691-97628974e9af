/**
 * Global Footer Fix
 * Ensures the footer is properly positioned at the bottom of the page
 * with no extra blue space underneath
 */

document.addEventListener('DOMContentLoaded', function() {
    // Add a class to the body to ensure proper styling
    document.body.classList.add('footer-fixed');
    
    // Function to adjust footer position
    function adjustFooterPosition() {
        const footer = document.querySelector('.enterprise-footer');
        const body = document.body;
        const html = document.documentElement;
        
        if (!footer) return;
        
        // Get the height of the page content
        const pageHeight = Math.max(
            body.scrollHeight, body.offsetHeight,
            html.clientHeight, html.scrollHeight, html.offsetHeight
        );
        
        // Get the viewport height
        const viewportHeight = window.innerHeight;
        
        // If page content is less than viewport, ensure footer is at the bottom
        if (pageHeight <= viewportHeight) {
            footer.style.marginTop = 'auto';
        } else {
            footer.style.marginTop = '0';
        }
    }
    
    // Run on page load
    adjustFooterPosition();
    
    // Run on window resize
    window.addEventListener('resize', adjustFooterPosition);
    
    // Run when content changes (for dynamic content)
    const observer = new MutationObserver(adjustFooterPosition);
    observer.observe(document.body, { childList: true, subtree: true });
});
