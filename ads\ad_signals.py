"""
Signal handlers for ad-related events to generate real-time notifications
"""
from django.db.models.signals import post_save, m2m_changed
from django.dispatch import receiver
from django.urls import reverse
from django.utils import timezone
from django.db.models import F
from django.contrib.auth.models import User

from .models import Ad, AdAnalytics, Transaction, Campaign
from notifications.services import NotificationService

@receiver(post_save, sender=AdAnalytics)
def ad_analytics_updated(sender, instance, created, **kwargs):
    """
    Send notification when ad analytics are updated
    """
    # Get the ad
    ad = instance.ad
    
    # Check if this is a significant increase in impressions or clicks
    if not created and hasattr(instance, '_previous_impressions') and hasattr(instance, '_previous_clicks'):
        impressions_increase = instance.impressions - instance._previous_impressions
        clicks_increase = instance.clicks - instance._previous_clicks
        
        # If impressions increased by at least 50
        if impressions_increase >= 50:
            NotificationService.create_notification(
                user=ad.user,
                title="Ad Performance Update",
                message=f"Your ad '{ad.title}' received {impressions_increase} new impressions today.",
                notification_type="info",
                category="analytics",
                content_object=ad,
                action_url=reverse('ads:ad_analytics', kwargs={'slug': ad.slug})
            )
        
        # If clicks increased by at least 10
        if clicks_increase >= 10:
            NotificationService.create_notification(
                user=ad.user,
                title="Ad Performance Update",
                message=f"Your ad '{ad.title}' received {clicks_increase} new clicks today.",
                notification_type="success",
                category="analytics",
                content_object=ad,
                action_url=reverse('ads:ad_analytics', kwargs={'slug': ad.slug})
            )
        
        # If CTR is exceptionally good (>5%)
        if instance.impressions > 100:
            ctr = (instance.clicks / instance.impressions) * 100
            if ctr > 5:
                NotificationService.create_notification(
                    user=ad.user,
                    title="Exceptional Ad Performance",
                    message=f"Your ad '{ad.title}' is performing exceptionally well with a CTR of {ctr:.2f}%.",
                    notification_type="success",
                    category="analytics",
                    content_object=ad,
                    action_url=reverse('ads:ad_analytics', kwargs={'slug': ad.slug})
                )

@receiver(post_save, sender=Transaction)
def transaction_updated(sender, instance, created, **kwargs):
    """
    Send notification when a transaction is updated
    """
    # Skip if this is a new transaction (already handled in notifications/signals.py)
    if created:
        return
    
    # Get the previous state of the transaction (if available)
    if not hasattr(instance, '_previous_status'):
        return
    
    # Check if status has changed
    if instance._previous_status != instance.status:
        # Status changed, send notification based on new status
        if instance.status == 'approved':
            NotificationService.create_notification(
                user=instance.user,
                title="Payment Approved",
                message=f"Your payment of {instance.amount} KSH for ad '{instance.ad.title}' has been approved. Your ad will be activated shortly.",
                notification_type="success",
                category="payment",
                content_object=instance,
                action_url=reverse('ads:transaction_detail', kwargs={'transaction_id': instance.id})
            )
        elif instance.status == 'failed':
            NotificationService.create_notification(
                user=instance.user,
                title="Payment Failed",
                message=f"Your payment of {instance.amount} KSH for ad '{instance.ad.title}' has failed. Please try again or contact support.",
                notification_type="error",
                category="payment",
                content_object=instance,
                action_url=reverse('ads:transaction_detail', kwargs={'transaction_id': instance.id})
            )
        elif instance.status == 'refunded':
            NotificationService.create_notification(
                user=instance.user,
                title="Payment Refunded",
                message=f"Your payment of {instance.amount} KSH for ad '{instance.ad.title}' has been refunded.",
                notification_type="info",
                category="payment",
                content_object=instance,
                action_url=reverse('ads:transaction_detail', kwargs={'transaction_id': instance.id})
            )

# Function to simulate ad analytics updates for testing
def simulate_ad_analytics_update(ad, impressions_increase=50, clicks_increase=5):
    """
    Simulate an ad analytics update for testing purposes
    
    Args:
        ad: The ad to simulate analytics for
        impressions_increase: Number of impressions to add
        clicks_increase: Number of clicks to add
        
    Returns:
        AdAnalytics: The updated analytics object
    """
    # Get or create analytics for today
    today = timezone.now().date()
    analytics, created = AdAnalytics.objects.get_or_create(
        ad=ad,
        date=today,
        defaults={
            'impressions': 0,
            'clicks': 0,
            'unique_views': 0,
            'conversion_count': 0
        }
    )
    
    # Store previous values
    analytics._previous_impressions = analytics.impressions
    analytics._previous_clicks = analytics.clicks
    
    # Update analytics
    analytics.impressions += impressions_increase
    analytics.clicks += clicks_increase
    analytics.save()
    
    # Update ad totals
    ad.impressions += impressions_increase
    ad.clicks += clicks_increase
    ad.save()
    
    return analytics
