/* Campaign Management Styles */

/* Campaign Dashboard Header */
.campaign-dashboard .dashboard-header {
    background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
}

/* Campaign Cards */
.campaign-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.campaign-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.campaign-card-header {
    padding: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.campaign-card-title {
    font-size: 18px;
    font-weight: 600;
    color: #1a237e;
    margin: 0;
}

.campaign-card-body {
    padding: 20px;
    flex: 1;
}

.campaign-card-footer {
    padding: 15px 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    background-color: rgba(0, 0, 0, 0.02);
}

/* Campaign Status Badges */
.badge.bg-active {
    background-color: #4caf50;
}

.badge.bg-paused {
    background-color: #ff9800;
}

.badge.bg-draft {
    background-color: #9e9e9e;
}

.badge.bg-completed {
    background-color: #3f51b5;
}

/* Campaign Detail Groups */
.detail-group {
    margin-bottom: 15px;
}

.detail-label {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 5px;
    display: block;
}

.detail-value {
    font-size: 16px;
    color: #212529;
    font-weight: 500;
}

/* Campaign Form Styles */
.form-actions {
    padding: 20px;
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    margin-bottom: 30px;
    display: flex;
    justify-content: flex-end;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 40px 20px;
}

.empty-state-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: rgba(26, 35, 126, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: #1a237e;
    font-size: 32px;
}

.empty-state-title {
    font-size: 20px;
    font-weight: 600;
    color: #1a237e;
    margin-bottom: 10px;
}

.empty-state-description {
    font-size: 16px;
    color: #6c757d;
    margin-bottom: 20px;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

/* Campaign Search */
#campaign-search {
    min-width: 200px;
}

/* Campaign Name Link */
.campaign-name {
    color: #1a237e;
    font-weight: 500;
    text-decoration: none;
    transition: color 0.2s ease;
}

.campaign-name:hover {
    color: #3949ab;
    text-decoration: underline;
}

/* Progress Bar */
.progress {
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 10px;
}

.progress-bar {
    background-color: #1a237e;
    border-radius: 10px;
}

/* Tab Navigation */
.nav-tabs {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.nav-tabs .nav-link {
    border: none;
    color: #6c757d;
    padding: 10px 15px;
    font-weight: 500;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
}

.nav-tabs .nav-link:hover {
    color: #1a237e;
    border-bottom-color: rgba(26, 35, 126, 0.3);
}

.nav-tabs .nav-link.active {
    color: #1a237e;
    border-bottom-color: #1a237e;
    background-color: transparent;
}

/* Chart Container */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .campaign-card-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .campaign-card-actions {
        margin-top: 10px;
    }
    
    .chart-container {
        height: 250px;
    }
}

@media (max-width: 768px) {
    .detail-group {
        margin-bottom: 20px;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .form-actions .btn {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .form-actions .btn + .btn {
        margin-left: 0;
    }
    
    .chart-container {
        height: 200px;
    }
}

@media (max-width: 576px) {
    .campaign-card {
        margin-bottom: 20px;
    }
    
    .campaign-dashboard .dashboard-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .campaign-dashboard .dashboard-actions .btn,
    .campaign-dashboard .dashboard-actions .dropdown {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .campaign-dashboard .dashboard-actions .btn + .btn,
    .campaign-dashboard .dashboard-actions .dropdown + .btn,
    .campaign-dashboard .dashboard-actions .btn + .dropdown {
        margin-left: 0;
    }
}
