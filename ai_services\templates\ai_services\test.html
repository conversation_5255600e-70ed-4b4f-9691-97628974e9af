{% extends "base.html" %}
{% load static %}

{% block title %}Test AI Integration{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3>Test AI Integration</h3>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="language" class="form-label">Language</label>
                            <select name="language" id="language" class="form-select">
                                {% for code, name in languages.items %}
                                <option value="{{ code }}">{{ name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="title" class="form-label">Ad Title (Optional)</label>
                            <input type="text" name="title" id="title" class="form-control" placeholder="Enter a title to generate content that aligns with it">
                            <small class="form-text text-muted">If provided, the AI will generate content that aligns with this title.</small>
                        </div>
                        <div class="mb-3">
                            <label for="business_type" class="form-label">Business Type</label>
                            <input type="text" name="business_type" id="business_type" class="form-control" placeholder="e.g., Enterprise Software, Retail Store, Restaurant">
                        </div>
                        <div class="mb-3">
                            <label for="target_audience" class="form-label">Target Audience</label>
                            <input type="text" name="target_audience" id="target_audience" class="form-control" placeholder="e.g., Business Professionals, Young Adults, Families">
                        </div>
                        <div class="mb-3">
                            <label for="tone" class="form-label">Tone</label>
                            <select name="tone" id="tone" class="form-select">
                                <option value="professional">Professional</option>
                                <option value="casual">Casual</option>
                                <option value="friendly">Friendly</option>
                                <option value="formal">Formal</option>
                                <option value="humorous">Humorous</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary">Generate Suggestions</button>
                    </form>

                    {% if error %}
                    <div class="alert alert-danger mt-4">
                        <strong>Error:</strong> {{ error }}
                        <p class="mt-2">The AI service is currently unavailable. Please try again later or contact support if the issue persists.</p>
                    </div>
                    {% endif %}

                    {% if result %}
                    <div class="mt-4">
                        <h4>Generated Suggestions</h4>
                        <div class="row">
                            {% for suggestion in result %}
                            <div class="col-md-12 mb-3">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>{{ suggestion.title }}</h5>
                                    </div>
                                    <div class="card-body">
                                        <p>{{ suggestion.content|linebreaks }}</p>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
