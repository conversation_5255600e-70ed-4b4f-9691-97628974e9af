# Generated by Django 4.2.7 on 2025-05-18 15:33

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ads', '0009_ad_conversion_count_adanalytics_browser_data'),
    ]

    operations = [
        migrations.AddField(
            model_name='ad',
            name='ai_language',
            field=models.Char<PERSON>ield(blank=True, help_text='Language used for AI generation', max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='ad',
            name='ai_suggestion',
            field=models.JSONField(blank=True, help_text='The selected AI suggestion data', null=True),
        ),
        migrations.AddField(
            model_name='ad',
            name='used_ai',
            field=models.Bo<PERSON>anField(default=False, help_text='Whether AI was actually used in the final content'),
        ),
    ]
