/**
 * Ad Preview JavaScript
 * Handles real-time preview updates for ad creation/edit forms
 * This is a standalone version for pages that don't use the consolidated script
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Ad Preview: Initializing...');

    const adTitle = document.getElementById('adTitle');
    const adContent = document.getElementById('adContent');
    const ctaLink = document.getElementById('ctaLink');
    const adMedia = document.getElementById('adMedia');
    const adType = document.getElementById('adType');
    const adLocation = document.getElementById('ad_location');

    // Preview elements
    const previewTitle = document.getElementById('previewTitle');
    const previewContent = document.getElementById('previewContent');
    const previewCta = document.getElementById('previewCta');
    const previewImage = document.querySelector('#previewImage img');

    // Review elements
    const reviewTitle = document.getElementById('reviewTitle');
    const reviewContent = document.getElementById('reviewContent');
    const reviewType = document.getElementById('reviewType');
    const reviewLocation = document.getElementById('reviewLocation');
    const reviewImpressions = document.getElementById('reviewImpressions');

    // Update preview title
    function updatePreviewTitle(value) {
        if (previewTitle) {
            previewTitle.textContent = value || 'Your Ad Title';
        }
    }

    // Update preview content
    function updatePreviewContent(value) {
        if (previewContent) {
            previewContent.textContent = value || 'Your ad content will appear here as you type.';
        }
    }

    // Update preview CTA
    function updatePreviewCta(value) {
        if (previewCta) {
            previewCta.href = value || '#';
        }
    }

    // Update preview image
    function updatePreviewImage(file) {
        if (previewImage && file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImage.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    }

    // Update review title
    function updateReviewTitle(value) {
        if (reviewTitle) {
            reviewTitle.textContent = value || 'Not specified';
        }
    }

    // Update review content
    function updateReviewContent(value) {
        if (reviewContent) {
            reviewContent.textContent = value || 'Not specified';
        }
    }

    // Update review type
    function updateReviewType(value) {
        if (reviewType) {
            reviewType.textContent = value || 'Not specified';
        }
    }

    // Update review location
    function updateReviewLocation(value) {
        if (reviewLocation) {
            reviewLocation.textContent = value || 'Not specified';
        }
    }

    // Update review impressions
    function updateReviewImpressions(value) {
        if (reviewImpressions) {
            reviewImpressions.textContent = (value || 0) + ' views per day';
        }
    }

    // Initialize preview content
    function initializePreviewContent() {
        if (adTitle) updatePreviewTitle(adTitle.value);
        if (adContent) updatePreviewContent(adContent.value);
    }

    // Event listeners
    if (adTitle) {
        adTitle.addEventListener('input', function() {
            updatePreviewTitle(this.value);
            updateReviewTitle(this.value);
        });
    }

    if (adContent) {
        adContent.addEventListener('input', function() {
            updatePreviewContent(this.value);
            updateReviewContent(this.value);
        });
    }

    if (ctaLink) {
        ctaLink.addEventListener('input', function() {
            updatePreviewCta(this.value);
        });
    }

    if (adMedia) {
        adMedia.addEventListener('change', function() {
            updatePreviewImage(this.files[0]);
        });
    }

    if (adType) {
        adType.addEventListener('change', function() {
            updateReviewType(this.options[this.selectedIndex].text);
        });
    }

    if (adLocation) {
        adLocation.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            updateReviewLocation(selectedOption.text);
            updateReviewImpressions(selectedOption.dataset.impressions);
        });
    }

    // Initialize preview
    initializePreviewContent();

    console.log('Ad Preview: Initialized successfully');
});
