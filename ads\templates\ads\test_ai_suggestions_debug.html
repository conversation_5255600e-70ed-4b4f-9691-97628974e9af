<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Suggestions Debug</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h2><i class="fas fa-bug me-2"></i> AI Suggestions Debug</h2>
                <p class="text-muted">Test the AI suggestions functionality step by step</p>
            </div>
        </div>
        
        <!-- Test Form -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cogs me-2"></i> Test Parameters</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="adTitle" class="form-label">Ad Title</label>
                            <input type="text" class="form-control" id="adTitle" placeholder="Enter ad title" value="Stylish Fashion">
                        </div>
                        
                        <div class="mb-3">
                            <label for="businessType" class="form-label">Business Type</label>
                            <input type="text" class="form-control" id="businessType" placeholder="e.g., Fashion Boutique, Tech Startup" value="Fashion Boutique">
                        </div>
                        
                        <div class="mb-3">
                            <label for="targetAudience" class="form-label">Target Audience</label>
                            <input type="text" class="form-control" id="targetAudience" placeholder="e.g., Young Professionals" value="Young Professionals">
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="aiStyle" class="form-label">Style</label>
                                    <select class="form-select" id="aiStyle">
                                        <option value="professional" selected>Professional</option>
                                        <option value="casual">Casual</option>
                                        <option value="persuasive">Persuasive</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="aiLanguage" class="form-label">Language</label>
                                    <select class="form-select" id="aiLanguage">
                                        <option value="english" selected>English</option>
                                        <option value="swahili">Swahili</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <button type="button" id="generateSuggestions" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-magic me-2"></i> Generate Suggestions
                        </button>
                        
                        <div id="formErrorContainer" class="alert alert-danger mt-3" style="display: none;"></div>
                        <div id="formSuccessContainer" class="alert alert-success mt-3" style="display: none;"></div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-lightbulb me-2"></i> AI Suggestions</h5>
                    </div>
                    <div class="card-body">
                        <div id="aiSuggestionsContainer">
                            <div class="ai-suggestions">
                                <div class="text-muted text-center py-4">
                                    <i class="fas fa-magic fa-2x mb-3"></i>
                                    <p>Click "Generate Suggestions" to see AI-powered content</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Debug Info -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle me-2"></i> Debug Information</h5>
                    </div>
                    <div class="card-body">
                        <div id="debugInfo">
                            <p class="text-muted">Debug information will appear here after making a request</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Debug script
        let isGenerating = false;
        
        document.getElementById('generateSuggestions').addEventListener('click', function() {
            if (isGenerating) return;
            
            const adTitle = document.getElementById('adTitle').value.trim();
            const businessType = document.getElementById('businessType').value.trim();
            const targetAudience = document.getElementById('targetAudience').value.trim();
            const style = document.getElementById('aiStyle').value;
            const language = document.getElementById('aiLanguage').value;
            
            // Clear previous messages
            hideError();
            hideSuccess();
            
            if (!adTitle) {
                showError('Please enter an ad title');
                return;
            }
            
            if (!businessType) {
                showError('Please enter a business type');
                return;
            }
            
            isGenerating = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Generating...';
            this.disabled = true;
            
            const requestData = {
                language: language,
                business_type: businessType,
                target_audience: targetAudience,
                tone: style,
                title: adTitle,
                num_suggestions: 3
            };
            
            // Show debug info
            updateDebugInfo('Making request...', requestData);
            
            // Test API call
            fetch('/ads/test/smart-ai/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            })
            .then(response => {
                updateDebugInfo('Response received', { status: response.status, ok: response.ok });
                return response.json();
            })
            .then(data => {
                console.log('Response:', data);
                updateDebugInfo('Response parsed', data);
                
                if (data.success && data.suggestions) {
                    displaySuggestions(data.suggestions);
                    showSuccess(`Generated ${data.suggestions.length} suggestions successfully!`);
                } else {
                    showError('Failed to generate suggestions: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                updateDebugInfo('Error occurred', { error: error.message });
                showError('Error generating suggestions: ' + error.message);
            })
            .finally(() => {
                isGenerating = false;
                this.innerHTML = '<i class="fas fa-magic me-2"></i> Generate Suggestions';
                this.disabled = false;
            });
        });
        
        function displaySuggestions(suggestions) {
            const container = document.querySelector('.ai-suggestions');
            container.innerHTML = '';
            
            suggestions.forEach((suggestion, index) => {
                const card = document.createElement('div');
                card.className = 'ai-suggestion-card mb-3 p-3 border rounded';
                card.innerHTML = `
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="radio" name="ai_suggestion" id="suggestion${index + 1}" value="${index + 1}">
                        <label class="form-check-label fw-bold" for="suggestion${index + 1}">Suggestion ${index + 1}</label>
                    </div>
                    <div class="ai-suggestion-title mb-1 fw-bold text-primary">${suggestion.title || 'No title'}</div>
                    <div class="ai-suggestion-content small text-muted">${suggestion.content || 'No content'}</div>
                    <div class="mt-2">
                        <small class="badge bg-secondary">${suggestion.model || 'Unknown'}</small>
                        ${suggestion.provider ? `<small class="badge bg-info ms-1">${suggestion.provider}</small>` : ''}
                        ${suggestion.cached ? '<small class="badge bg-success ms-1">Cached</small>' : ''}
                    </div>
                `;
                container.appendChild(card);
            });
        }
        
        function updateDebugInfo(step, data) {
            const debugContainer = document.getElementById('debugInfo');
            const timestamp = new Date().toLocaleTimeString();
            
            const debugEntry = document.createElement('div');
            debugEntry.className = 'mb-2 p-2 border-start border-primary border-3 bg-light';
            debugEntry.innerHTML = `
                <div class="fw-bold text-primary">${timestamp} - ${step}</div>
                <pre class="mb-0 small">${JSON.stringify(data, null, 2)}</pre>
            `;
            
            debugContainer.appendChild(debugEntry);
            debugContainer.scrollTop = debugContainer.scrollHeight;
        }
        
        function showError(message) {
            const errorContainer = document.getElementById('formErrorContainer');
            errorContainer.textContent = message;
            errorContainer.style.display = 'block';
        }
        
        function hideError() {
            document.getElementById('formErrorContainer').style.display = 'none';
        }
        
        function showSuccess(message) {
            const successContainer = document.getElementById('formSuccessContainer');
            successContainer.textContent = message;
            successContainer.style.display = 'block';
        }
        
        function hideSuccess() {
            document.getElementById('formSuccessContainer').style.display = 'none';
        }
    </script>
</body>
</html>
