"""
Production-ready analytics enhancements for QR Code Generator
Includes performance optimizations, error handling, and monitoring
"""
import logging
from django.core.cache import cache
from django.db import transaction
from django.db.models import Count
from django.utils import timezone
from django.conf import settings
from datetime import <PERSON><PERSON><PERSON>
from .models import QRScanLog, QRCodeAnalytics, QRCode
from .utils import get_geolocation_from_ip, get_client_ip, parse_device_info

# Configure analytics logger
analytics_logger = logging.getLogger('qr_analytics')

class AnalyticsManager:
    """
    Production-ready analytics manager with error handling and performance optimizations
    """

    @staticmethod
    def log_qr_scan(request, qr_code, scan_type='direct'):
        """
        Log QR scan with comprehensive error handling and performance optimization

        Args:
            request: Django request object
            qr_code: QRCode instance
            scan_type: Type of scan ('direct', 'dynamic_redirect', 'ai_landing')
        """
        try:
            # Get client information
            ip_address = get_client_ip(request)
            user_agent = request.META.get('HTTP_USER_AGENT', '')

            # Skip analytics for bots and crawlers
            if AnalyticsManager._is_bot(user_agent):
                analytics_logger.info(f"Skipped bot scan for QR {qr_code.id}")
                return

            # Get geolocation data with caching
            geo_data = get_geolocation_from_ip(ip_address)

            # Parse device information
            device_info = parse_device_info(user_agent)

            # Create scan log entry with error handling
            with transaction.atomic():
                try:
                    scan_log = QRScanLog.objects.create(
                        code=getattr(qr_code, 'short_code', qr_code.unique_id),
                        ip_address=ip_address,
                        user_agent=user_agent[:1000],  # Truncate long user agents
                        country=geo_data.get('country', '')[:2] if geo_data.get('country') else '',
                        city=geo_data.get('city', '')[:100],
                        org=geo_data.get('organization', '')[:255],
                        latitude=geo_data.get('latitude'),
                        longitude=geo_data.get('longitude')
                    )

                    # Update QR code analytics
                    AnalyticsManager._update_qr_analytics(qr_code, scan_type, device_info, geo_data)

                    analytics_logger.info(f"Logged scan for QR {qr_code.id} from {ip_address}")

                except Exception as e:
                    analytics_logger.error(f"Failed to create scan log: {e}")
                    # Don't let analytics failures break the main functionality

        except Exception as e:
            analytics_logger.error(f"Analytics logging failed for QR {qr_code.id}: {e}")

    @staticmethod
    def _update_qr_analytics(qr_code, scan_type, device_info, geo_data):
        """
        Update QR code analytics with atomic operations
        """
        try:
            # Get or create analytics record
            analytics, created = QRCodeAnalytics.objects.get_or_create(
                qr_code=qr_code,
                defaults={
                    'total_scans': 0,
                    'unique_scans': 0,
                    'last_scanned': timezone.now()
                }
            )

            # Update scan counts
            analytics.total_scans += 1
            analytics.last_scanned = timezone.now()
            analytics.save(update_fields=['total_scans', 'last_scanned'])

        except Exception as e:
            analytics_logger.error(f"Failed to update QR analytics: {e}")

    @staticmethod
    def _is_bot(user_agent):
        """
        Detect if the request is from a bot or crawler
        """
        if not user_agent:
            return False

        bot_indicators = [
            'bot', 'crawler', 'spider', 'scraper', 'facebook', 'twitter',
            'linkedin', 'whatsapp', 'telegram', 'googlebot', 'bingbot'
        ]

        user_agent_lower = user_agent.lower()
        return any(indicator in user_agent_lower for indicator in bot_indicators)

    @staticmethod
    def get_analytics_summary(user, days=30):
        """
        Get analytics summary for a user with caching

        Args:
            user: User instance
            days: Number of days to analyze

        Returns:
            dict: Analytics summary
        """
        cache_key = f"analytics_summary_{user.id}_{days}"
        cached_data = cache.get(cache_key)

        if cached_data:
            return cached_data

        try:
            start_date = timezone.now() - timedelta(days=days)

            # Get user's QR codes
            user_qr_codes = QRCode.objects.filter(user=user)

            # Get scan logs for user's QR codes
            scan_logs = QRScanLog.objects.filter(
                code__in=user_qr_codes.values_list('short_code', flat=True),
                timestamp__gte=start_date
            )

            # Calculate metrics
            total_scans = scan_logs.count()
            unique_countries = scan_logs.values('country').distinct().count()
            unique_cities = scan_logs.values('city').distinct().count()

            # Top countries
            top_countries = scan_logs.values('country').annotate(
                count=Count('id')
            ).order_by('-count')[:5]

            # Top cities
            top_cities = scan_logs.values('city').annotate(
                count=Count('id')
            ).order_by('-count')[:5]

            summary = {
                'total_scans': total_scans,
                'unique_countries': unique_countries,
                'unique_cities': unique_cities,
                'top_countries': list(top_countries),
                'top_cities': list(top_cities),
                'period_days': days,
                'generated_at': timezone.now().isoformat()
            }

            # Cache for 1 hour
            cache.set(cache_key, summary, 3600)

            return summary

        except Exception as e:
            analytics_logger.error(f"Failed to generate analytics summary: {e}")
            return {
                'total_scans': 0,
                'unique_countries': 0,
                'unique_cities': 0,
                'top_countries': [],
                'top_cities': [],
                'period_days': days,
                'error': str(e)
            }

    @staticmethod
    def cleanup_old_logs(days_to_keep=90):
        """
        Clean up old scan logs to maintain database performance

        Args:
            days_to_keep: Number of days of logs to retain
        """
        try:
            cutoff_date = timezone.now() - timedelta(days=days_to_keep)

            # Delete old scan logs
            deleted_count = QRScanLog.objects.filter(
                timestamp__lt=cutoff_date
            ).delete()[0]

            analytics_logger.info(f"Cleaned up {deleted_count} old scan logs")

            return deleted_count

        except Exception as e:
            analytics_logger.error(f"Failed to cleanup old logs: {e}")
            return 0

    @staticmethod
    def get_performance_metrics():
        """
        Get system performance metrics for monitoring

        Returns:
            dict: Performance metrics
        """
        try:
            from django.db import connection

            # Database metrics
            with connection.cursor() as cursor:
                cursor.execute("SELECT COUNT(*) FROM qrcode_app_qrscanlog")
                total_logs = cursor.fetchone()[0]

                cursor.execute("""
                    SELECT COUNT(*) FROM qrcode_app_qrscanlog
                    WHERE timestamp >= %s
                """, [timezone.now() - timedelta(hours=24)])
                logs_24h = cursor.fetchone()[0]

                cursor.execute("""
                    SELECT COUNT(*) FROM qrcode_app_qrscanlog
                    WHERE timestamp >= %s
                """, [timezone.now() - timedelta(hours=1)])
                logs_1h = cursor.fetchone()[0]

            # Cache metrics
            cache_stats = {
                'cache_backend': str(cache.__class__.__name__),
                'cache_location': getattr(cache, '_cache', {}).get('_servers', 'Unknown')
            }

            return {
                'total_scan_logs': total_logs,
                'scans_last_24h': logs_24h,
                'scans_last_hour': logs_1h,
                'cache_info': cache_stats,
                'timestamp': timezone.now().isoformat()
            }

        except Exception as e:
            analytics_logger.error(f"Failed to get performance metrics: {e}")
            return {'error': str(e)}


class AnalyticsMiddleware:
    """
    Middleware for analytics performance monitoring
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Track analytics-related requests
        if '/qr/' in request.path or '/r/' in request.path or '/ai/' in request.path:
            start_time = timezone.now()

            response = self.get_response(request)

            # Log slow analytics requests
            duration = (timezone.now() - start_time).total_seconds()
            if duration > 2.0:  # Log requests taking more than 2 seconds
                analytics_logger.warning(
                    f"Slow analytics request: {request.path} took {duration:.2f}s"
                )

            return response

        return self.get_response(request)


# Production settings recommendations
PRODUCTION_ANALYTICS_SETTINGS = {
    'IPINFO_API_TOKEN': 'your-production-ipinfo-token',
    'ANALYTICS_CACHE_TIMEOUT': 3600,  # 1 hour
    'ANALYTICS_LOG_RETENTION_DAYS': 90,
    'ANALYTICS_CLEANUP_ENABLED': True,
    'ANALYTICS_BOT_FILTERING': True,
    'ANALYTICS_PERFORMANCE_MONITORING': True,
}
