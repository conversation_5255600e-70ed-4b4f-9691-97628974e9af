from rest_framework import viewsets, permissions, status
from rest_framework.decorators import api_view, permission_classes, authentication_classes
from rest_framework.response import Response
from rest_framework.authentication import TokenAuthentication
from django.contrib.auth.models import User
from qrcode_app.models import QRCode, UserProfile, APIKey, QRCodeBatch
from .serializers import (
    UserSerializer, UserProfileSerializer, QRCodeSerializer,
    APIKeySerializer, QRCodeBatchSerializer
)
from .permissions import IsOwnerOrAdmin, IsAdminUser
from .authentication import APIKeyAuthentication

class QRCodeViewSet(viewsets.ModelViewSet):
    """API endpoint for QR codes"""
    serializer_class = QRCodeSerializer
    permission_classes = [permissions.IsAuthenticated, IsOwnerOrAdmin]
    authentication_classes = [TokenAuthentication, APIKeyAuthentication]

    def get_queryset(self):
        user = self.request.user
        if user.profile.role in ['admin', 'superadmin']:
            return QRCode.objects.all()
        return QRCode.objects.filter(user=user)

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

class UserProfileViewSet(viewsets.ModelViewSet):
    """API endpoint for user profiles"""
    serializer_class = UserProfileSerializer
    permission_classes = [permissions.IsAuthenticated, IsOwnerOrAdmin]
    authentication_classes = [TokenAuthentication, APIKeyAuthentication]

    def get_queryset(self):
        user = self.request.user
        if user.profile.role in ['admin', 'superadmin']:
            return UserProfile.objects.all()
        return UserProfile.objects.filter(user=user)

class APIKeyViewSet(viewsets.ModelViewSet):
    """API endpoint for API keys"""
    serializer_class = APIKeySerializer
    permission_classes = [permissions.IsAuthenticated, IsOwnerOrAdmin]
    authentication_classes = [TokenAuthentication, APIKeyAuthentication]

    def get_queryset(self):
        user = self.request.user
        if user.profile.role in ['admin', 'superadmin']:
            return APIKey.objects.all()
        return APIKey.objects.filter(user=user)

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

class QRCodeBatchViewSet(viewsets.ModelViewSet):
    """API endpoint for QR code batches"""
    serializer_class = QRCodeBatchSerializer
    permission_classes = [permissions.IsAuthenticated, IsOwnerOrAdmin]
    authentication_classes = [TokenAuthentication, APIKeyAuthentication]

    def get_queryset(self):
        user = self.request.user
        if user.profile.role in ['admin', 'superadmin']:
            return QRCodeBatch.objects.all()
        return QRCodeBatch.objects.filter(user=user)

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

class UserViewSet(viewsets.ModelViewSet):
    """API endpoint for users (admin only)"""
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated, IsAdminUser]
    authentication_classes = [TokenAuthentication, APIKeyAuthentication]

@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def generate_qr_code(request):
    """Generate a QR code without authentication"""
    serializer = QRCodeSerializer(data=request.data)
    if serializer.is_valid():
        # Create a QR code without saving to database
        qr_data = serializer.validated_data

        # Generate QR code logic here
        # ...

        return Response({'message': 'QR code generated successfully'}, status=status.HTTP_201_CREATED)
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
@authentication_classes([TokenAuthentication, APIKeyAuthentication])
def user_info(request):
    """Get current user info"""
    user = request.user
    profile = UserProfile.objects.get(user=user)

    user_serializer = UserSerializer(user)
    profile_serializer = UserProfileSerializer(profile)

    return Response({
        'user': user_serializer.data,
        'profile': profile_serializer.data
    })
