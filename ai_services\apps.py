"""
AI Services App Configuration
"""
from django.apps import AppConfig
from django.utils.translation import gettext_lazy as _


class AiServicesConfig(AppConfig):
    """AI Services app configuration"""
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'ai_services'
    verbose_name = _('AI Services')

    def ready(self):
        """
        Initialize the app when Django starts.
        This is a good place to load environment variables and run startup tasks.
        """
        import os
        from dotenv import load_dotenv
        load_dotenv()

        # Only run health checks in the main process (not in management commands or other subprocesses)
        if os.environ.get('RUN_MAIN', None) != 'true':
            # Import and run health checks
            try:
                from ai_services.health import run_initial_health_checks, start_background_health_checks

                # Run initial health checks
                run_initial_health_checks()

                # Start background health checks
                start_background_health_checks()
            except Exception as e:
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"Error running AI provider health checks: {str(e)}")
