from django.contrib.auth.models import User
from django.contrib.contenttypes.models import ContentType
from django.db.models import Q
from django.utils import timezone

from .models import Notification


class NotificationService:
    """
    Service class for creating and managing notifications
    """
    
    @staticmethod
    def create_notification(
        user,
        title,
        message,
        notification_type='info',
        category='system',
        content_object=None,
        action_url=None
    ):
        """
        Create a new notification for a user
        
        Args:
            user (User): User to receive the notification
            title (str): Notification title
            message (str): Notification message
            notification_type (str): Type of notification (info, success, warning, error)
            category (str): Category of notification (system, ad, payment, etc.)
            content_object (Model instance, optional): Related object
            action_url (str, optional): URL to redirect to when notification is clicked
            
        Returns:
            Notification: The created notification
        """
        if isinstance(user, int):
            user = User.objects.get(id=user)
            
        notification = Notification(
            user=user,
            title=title,
            message=message,
            notification_type=notification_type,
            category=category,
            action_url=action_url
        )
        
        if content_object:
            content_type = ContentType.objects.get_for_model(content_object)
            notification.content_type = content_type
            notification.object_id = content_object.id
            
        notification.save()
        return notification
    
    @staticmethod
    def create_notification_for_many_users(
        users,
        title,
        message,
        notification_type='info',
        category='system',
        content_object=None,
        action_url=None
    ):
        """
        Create notifications for multiple users
        
        Args:
            users (QuerySet or list): Users to receive the notification
            title (str): Notification title
            message (str): Notification message
            notification_type (str): Type of notification (info, success, warning, error)
            category (str): Category of notification (system, ad, payment, etc.)
            content_object (Model instance, optional): Related object
            action_url (str, optional): URL to redirect to when notification is clicked
            
        Returns:
            list: List of created notifications
        """
        notifications = []
        
        for user in users:
            notification = NotificationService.create_notification(
                user=user,
                title=title,
                message=message,
                notification_type=notification_type,
                category=category,
                content_object=content_object,
                action_url=action_url
            )
            notifications.append(notification)
            
        return notifications
    
    @staticmethod
    def get_user_notifications(user, include_deleted=False, include_read=True):
        """
        Get notifications for a user
        
        Args:
            user (User): User to get notifications for
            include_deleted (bool): Whether to include soft-deleted notifications
            include_read (bool): Whether to include read notifications
            
        Returns:
            QuerySet: Notifications for the user
        """
        if isinstance(user, int):
            user = User.objects.get(id=user)
            
        query = Q(user=user)
        
        if not include_deleted:
            query &= Q(is_deleted=False)
            
        if not include_read:
            query &= Q(is_read=False)
            
        return Notification.objects.filter(query)
    
    @staticmethod
    def mark_all_as_read(user):
        """
        Mark all notifications as read for a user
        
        Args:
            user (User): User to mark notifications as read for
            
        Returns:
            int: Number of notifications marked as read
        """
        if isinstance(user, int):
            user = User.objects.get(id=user)
            
        return Notification.objects.filter(
            user=user,
            is_read=False,
            is_deleted=False
        ).update(
            is_read=True,
            updated_at=timezone.now()
        )
    
    @staticmethod
    def delete_old_notifications(days=30):
        """
        Soft delete notifications older than specified days
        
        Args:
            days (int): Number of days to keep notifications
            
        Returns:
            int: Number of notifications deleted
        """
        cutoff_date = timezone.now() - timezone.timedelta(days=days)
        return Notification.objects.filter(
            created_at__lt=cutoff_date,
            is_deleted=False
        ).update(
            is_deleted=True,
            updated_at=timezone.now()
        )
