import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'qrcode_project.settings')
django.setup()

# Import models
from notifications.models import Notification
from django.contrib.auth import get_user_model

User = get_user_model()

# Get a user
user = User.objects.first()
if not user:
    print("No users found. Please create a user first.")
    exit(1)

print(f"Testing with user: {user.username}")

# Create a test notification
notification = Notification.objects.create(
    user=user,
    title="Test Notification",
    message="This is a test notification",
    notification_type="info",
    category="system"
)

print(f"Created notification: {notification.title}")

# Test muting
notification.mute()
print(f"Muted notification: {notification.is_muted}")

# Test archiving
notification.archive()
print(f"Archived notification: {notification.is_archived}")

# Test filtering
print("\nFiltering notifications:")
print(f"All: {Notification.objects.filter(user=user).count()}")
print(f"Unread: {Notification.objects.filter(user=user, is_read=False).count()}")
print(f"Read: {Notification.objects.filter(user=user, is_read=True).count()}")
print(f"Archived: {Notification.objects.filter(user=user, is_archived=True).count()}")
print(f"Muted: {Notification.objects.filter(user=user, is_muted=True).count()}")

print("\nTest completed successfully!")
