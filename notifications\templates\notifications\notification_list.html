{% extends 'base.html' %}
{% load static %}

{% block title %}Notifications{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'notifications/css/notifications.css' %}">
<style>
    /* Ultra-Premium Notification System Styling */
    body {
        background:
            linear-gradient(135deg, #000000 0%, #0a0a0a 10%, #1a1a2e 25%, #16213e 40%, #0f3460 60%, #533483 80%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        position: relative;
        overflow-x: hidden;
    }

    /* Premium animated background with notification patterns */
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 8% 92%, rgba(102, 126, 234, 0.6) 0%, transparent 30%),
            radial-gradient(circle at 92% 8%, rgba(255, 255, 255, 0.25) 0%, transparent 25%),
            radial-gradient(circle at 25% 75%, rgba(118, 75, 162, 0.5) 0%, transparent 35%),
            radial-gradient(circle at 75% 85%, rgba(83, 52, 131, 0.4) 0%, transparent 20%),
            radial-gradient(circle at 60% 45%, rgba(102, 126, 234, 0.3) 0%, transparent 40%),
            radial-gradient(circle at 40% 25%, rgba(255, 255, 255, 0.15) 0%, transparent 30%);
        z-index: -1;
        animation: notificationFloat 35s ease-in-out infinite;
    }

    /* Corporate notification pattern overlay */
    body::after {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image:
            radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.06) 2px, transparent 2px),
            radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.04) 1px, transparent 1px),
            linear-gradient(45deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
        background-size: 90px 90px, 50px 50px, 30px 30px;
        z-index: -1;
        opacity: 0.7;
        animation: notificationPatternPulse 18s ease-in-out infinite;
    }

    @keyframes notificationFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
        16% { transform: translateY(-25px) rotate(1.5deg) scale(1.04); }
        33% { transform: translateY(15px) rotate(-0.8deg) scale(0.96); }
        50% { transform: translateY(-20px) rotate(1.2deg) scale(1.03); }
        66% { transform: translateY(10px) rotate(-1deg) scale(0.98); }
        83% { transform: translateY(-15px) rotate(0.5deg) scale(1.01); }
    }

    @keyframes notificationPatternPulse {
        0%, 100% { opacity: 0.5; transform: scale(1) rotate(0deg); }
        50% { opacity: 0.9; transform: scale(1.02) rotate(2deg); }
    }

    /* Premium Container */
    .notification-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 4rem 2rem;
        position: relative;
        z-index: 1;
    }

    /* Ultra-Premium Page Header */
    .notification-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 3rem;
        padding: 2rem 0;
        border-bottom: 2px solid rgba(255, 255, 255, 0.1);
    }

    .notification-title {
        font-size: 3rem;
        font-weight: 900;
        color: white;
        margin: 0;
        background: linear-gradient(135deg, #ffffff 0%, #e0e0e0 50%, #ffffff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
        position: relative;
        animation: titleNotificationGlow 5s ease-in-out infinite;
    }

    .notification-title::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.4) 0%, rgba(118, 75, 162, 0.4) 100%);
        -webkit-background-clip: text;
        background-clip: text;
        z-index: -1;
        animation: titleNotificationShimmer 8s ease-in-out infinite;
    }

    @keyframes titleNotificationGlow {
        0%, 100% { text-shadow: 0 8px 32px rgba(0, 0, 0, 0.5); }
        50% { text-shadow: 0 8px 32px rgba(102, 126, 234, 0.4), 0 0 60px rgba(255, 255, 255, 0.3); }
    }

    @keyframes titleNotificationShimmer {
        0%, 100% { opacity: 0.4; }
        50% { opacity: 0.8; }
    }

    .notification-actions {
        display: flex;
        gap: 1rem;
    }

    /* Premium Filter Section */
    .notification-filters {
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.1) 0%,
            rgba(255, 255, 255, 0.05) 100%);
        backdrop-filter: blur(30px);
        border: 2px solid rgba(255, 255, 255, 0.2);
        border-radius: 32px;
        padding: 2.5rem;
        margin-bottom: 3rem;
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        position: relative;
        overflow: hidden;
    }

    .notification-filters::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: conic-gradient(from 0deg,
            rgba(102, 126, 234, 0.15) 0deg,
            rgba(255, 255, 255, 0.08) 90deg,
            rgba(118, 75, 162, 0.15) 180deg,
            rgba(255, 255, 255, 0.05) 270deg,
            rgba(102, 126, 234, 0.15) 360deg);
        z-index: 0;
        animation: filterGlow 12s linear infinite;
        opacity: 0;
        transition: opacity 0.6s ease;
    }

    .notification-filters:hover::before {
        opacity: 1;
    }

    @keyframes filterGlow {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .notification-filters > * {
        position: relative;
        z-index: 1;
    }

    /* Premium Notification List */
    .notification-list {
        display: flex;
        flex-direction: column;
        gap: 2rem;
    }

    /* Ultra-Premium Notification Items */
    .notification-item {
        display: flex;
        align-items: flex-start;
        padding: 2.5rem;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.95) 0%,
            rgba(248, 249, 250, 0.9) 50%,
            rgba(255, 255, 255, 0.95) 100%);
        border-radius: 32px;
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.2),
            0 15px 30px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        border: 2px solid rgba(255, 255, 255, 0.25);
        backdrop-filter: blur(30px);
        transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        position: relative;
        overflow: hidden;
        animation: notificationItemEntry 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }

    .notification-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg,
            rgba(102, 126, 234, 0.08) 0%,
            rgba(255, 255, 255, 0.1) 50%,
            rgba(118, 75, 162, 0.08) 100%);
        z-index: 0;
        pointer-events: none;
        border-radius: 32px;
    }

    .notification-item::after {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        width: calc(100% + 4px);
        height: calc(100% + 4px);
        background: conic-gradient(from 0deg,
            rgba(102, 126, 234, 0.3) 0deg,
            rgba(255, 255, 255, 0.2) 90deg,
            rgba(118, 75, 162, 0.3) 180deg,
            rgba(255, 255, 255, 0.1) 270deg,
            rgba(102, 126, 234, 0.3) 360deg);
        border-radius: 34px;
        z-index: -1;
        opacity: 0;
        transition: opacity 0.6s ease;
        animation: notificationBorderGlow 8s linear infinite;
    }

    .notification-item:hover {
        box-shadow:
            0 40px 80px rgba(0, 0, 0, 0.25),
            0 20px 40px rgba(0, 0, 0, 0.15),
            0 0 60px rgba(102, 126, 234, 0.2),
            inset 0 2px 0 rgba(255, 255, 255, 0.4);
        transform: translateY(-10px) scale(1.02);
        border-color: rgba(255, 255, 255, 0.4);
    }

    .notification-item:hover::after {
        opacity: 1;
    }

    .notification-item.unread {
        background: linear-gradient(135deg,
            rgba(102, 126, 234, 0.15) 0%,
            rgba(255, 255, 255, 0.95) 30%,
            rgba(118, 75, 162, 0.15) 70%,
            rgba(255, 255, 255, 0.95) 100%);
        border-color: rgba(102, 126, 234, 0.4);
        box-shadow:
            0 25px 50px rgba(102, 126, 234, 0.3),
            0 15px 30px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.4);
    }

    @keyframes notificationItemEntry {
        0% {
            opacity: 0;
            transform: translateY(40px) scale(0.95);
            filter: blur(10px);
        }
        100% {
            opacity: 1;
            transform: translateY(0) scale(1);
            filter: blur(0px);
        }
    }

    @keyframes notificationBorderGlow {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .notification-item > * {
        position: relative;
        z-index: 1;
    }

    /* Premium Notification Icons */
    .notification-icon {
        flex-shrink: 0;
        width: 70px;
        height: 70px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 2rem;
        box-shadow:
            0 15px 30px rgba(102, 126, 234, 0.4),
            0 8px 16px rgba(0, 0, 0, 0.2),
            inset 0 2px 0 rgba(255, 255, 255, 0.3);
        transition: all 0.4s ease;
        position: relative;
        overflow: hidden;
    }

    .notification-icon::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: conic-gradient(from 0deg,
            rgba(255, 255, 255, 0.4) 0deg,
            transparent 90deg,
            rgba(255, 255, 255, 0.4) 180deg,
            transparent 270deg,
            rgba(255, 255, 255, 0.4) 360deg);
        animation: iconGlow 3s linear infinite;
    }

    .notification-icon:hover {
        transform: scale(1.1) rotate(5deg);
        box-shadow:
            0 20px 40px rgba(102, 126, 234, 0.5),
            0 12px 24px rgba(0, 0, 0, 0.3),
            inset 0 3px 0 rgba(255, 255, 255, 0.4);
    }

    @keyframes iconGlow {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .notification-icon i {
        font-size: 1.8rem;
        color: white;
        position: relative;
        z-index: 1;
        filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.3));
    }

    /* Notification Type Specific Styling */
    .notification-item.success .notification-icon {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    }

    .notification-item.warning .notification-icon {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    }

    .notification-item.error .notification-icon {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    }

    /* Premium Notification Content */
    .notification-content {
        flex-grow: 1;
        padding-right: 1rem;
    }

    .notification-title {
        font-size: 1.4rem;
        font-weight: 800;
        margin-bottom: 1rem;
        color: #1a1a2e;
        background: linear-gradient(135deg, #1a1a2e 0%, #2c3e50 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        line-height: 1.3;
        text-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .notification-message {
        font-size: 1.1rem;
        color: #4a5568;
        margin-bottom: 1.5rem;
        line-height: 1.6;
        font-weight: 500;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    /* Premium Meta Information */
    .notification-meta {
        display: flex;
        align-items: center;
        gap: 1.5rem;
        font-size: 0.9rem;
        flex-wrap: wrap;
    }

    .notification-category {
        display: inline-flex;
        align-items: center;
        padding: 0.6rem 1.2rem;
        background: linear-gradient(135deg,
            rgba(102, 126, 234, 0.15) 0%,
            rgba(118, 75, 162, 0.15) 100%);
        border: 1px solid rgba(102, 126, 234, 0.3);
        border-radius: 20px;
        color: #667eea;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 0.8rem;
        box-shadow:
            0 4px 12px rgba(102, 126, 234, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
    }

    .notification-category:hover {
        background: linear-gradient(135deg,
            rgba(102, 126, 234, 0.25) 0%,
            rgba(118, 75, 162, 0.25) 100%);
        transform: translateY(-2px);
        box-shadow:
            0 8px 20px rgba(102, 126, 234, 0.3),
            inset 0 2px 0 rgba(255, 255, 255, 0.4);
    }

    .notification-category i {
        margin-right: 0.5rem;
        font-size: 0.9rem;
        filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.2));
    }

    .notification-time {
        display: inline-flex;
        align-items: center;
        padding: 0.6rem 1.2rem;
        background: rgba(255, 255, 255, 0.8);
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 20px;
        color: #6b7280;
        font-weight: 600;
        box-shadow:
            0 4px 12px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.5);
        transition: all 0.3s ease;
    }

    .notification-time:hover {
        background: rgba(255, 255, 255, 0.95);
        transform: translateY(-2px);
        box-shadow:
            0 8px 20px rgba(0, 0, 0, 0.15),
            inset 0 2px 0 rgba(255, 255, 255, 0.7);
    }

    .notification-time i {
        margin-right: 0.5rem;
        color: #9ca3af;
        filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.1));
    }

    /* Premium Action Menu */
    .notification-actions-menu {
        flex-shrink: 0;
        margin-left: 1.5rem;
    }

    .notification-action-btn {
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.9) 0%,
            rgba(248, 249, 250, 0.8) 100%);
        border: 2px solid rgba(102, 126, 234, 0.2);
        color: #667eea;
        cursor: pointer;
        padding: 1rem;
        border-radius: 50%;
        transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        z-index: 5;
        box-shadow:
            0 8px 20px rgba(102, 126, 234, 0.2),
            0 4px 10px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.5);
        backdrop-filter: blur(20px);
        overflow: hidden;
    }

    .notification-action-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: conic-gradient(from 0deg,
            rgba(102, 126, 234, 0.3) 0deg,
            rgba(255, 255, 255, 0.2) 90deg,
            rgba(118, 75, 162, 0.3) 180deg,
            rgba(255, 255, 255, 0.1) 270deg,
            rgba(102, 126, 234, 0.3) 360deg);
        border-radius: 50%;
        z-index: -1;
        opacity: 0;
        transition: opacity 0.4s ease;
        animation: actionBtnGlow 6s linear infinite;
    }

    .notification-action-btn:hover {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-color: rgba(255, 255, 255, 0.4);
        box-shadow:
            0 15px 35px rgba(102, 126, 234, 0.4),
            0 8px 20px rgba(0, 0, 0, 0.2),
            inset 0 2px 0 rgba(255, 255, 255, 0.3);
        transform: translateY(-5px) scale(1.1);
    }

    .notification-action-btn:hover::before {
        opacity: 1;
    }

    .notification-action-btn:active {
        transform: translateY(-2px) scale(1.05);
    }

    @keyframes actionBtnGlow {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .notification-action-btn i {
        font-size: 1.2rem;
        position: relative;
        z-index: 1;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    }

    /* Premium Buttons */
    .btn {
        padding: 1rem 2rem;
        border-radius: 20px;
        font-weight: 700;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 1px;
        transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        border: 2px solid transparent;
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(20px);
    }

    .btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
            transparent 0%,
            rgba(255, 255, 255, 0.3) 50%,
            transparent 100%);
        transition: left 0.6s ease;
    }

    .btn:hover::before {
        left: 100%;
    }

    .btn-outline-primary {
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.9) 0%,
            rgba(248, 249, 250, 0.8) 100%);
        color: #667eea;
        border-color: rgba(102, 126, 234, 0.3);
        box-shadow:
            0 8px 20px rgba(102, 126, 234, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.5);
    }

    .btn-outline-primary:hover {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-color: rgba(255, 255, 255, 0.4);
        transform: translateY(-3px) scale(1.05);
        box-shadow:
            0 15px 35px rgba(102, 126, 234, 0.4),
            inset 0 2px 0 rgba(255, 255, 255, 0.3);
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-color: rgba(255, 255, 255, 0.3);
        box-shadow:
            0 8px 20px rgba(102, 126, 234, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        transform: translateY(-3px) scale(1.05);
        box-shadow:
            0 15px 35px rgba(102, 126, 234, 0.5),
            inset 0 2px 0 rgba(255, 255, 255, 0.3);
        color: white;
    }

    .btn-outline-secondary {
        background: rgba(255, 255, 255, 0.8);
        color: #6b7280;
        border-color: rgba(0, 0, 0, 0.2);
        box-shadow:
            0 4px 12px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.5);
    }

    .btn-outline-secondary:hover {
        background: rgba(255, 255, 255, 0.95);
        color: #374151;
        transform: translateY(-3px) scale(1.05);
        box-shadow:
            0 12px 25px rgba(0, 0, 0, 0.15),
            inset 0 2px 0 rgba(255, 255, 255, 0.7);
    }

    /* Premium Dropdown */
    .notification-dropdown {
        position: relative;
    }

    .notification-dropdown-menu,
    .dropdown-menu {
        position: absolute;
        top: calc(100% + 10px);
        right: 0;
        z-index: 100;
        min-width: 280px;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.95) 0%,
            rgba(248, 249, 250, 0.9) 100%);
        backdrop-filter: blur(30px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 24px;
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.25),
            0 15px 30px rgba(0, 0, 0, 0.15),
            inset 0 1px 0 rgba(255, 255, 255, 0.4);
        padding: 1rem 0;
        display: none;
        transform-origin: top right;
        animation: premiumDropdownFadeIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        overflow: hidden;
    }

    .dropdown-menu::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: conic-gradient(from 0deg,
            rgba(102, 126, 234, 0.1) 0deg,
            rgba(255, 255, 255, 0.05) 90deg,
            rgba(118, 75, 162, 0.1) 180deg,
            rgba(255, 255, 255, 0.03) 270deg,
            rgba(102, 126, 234, 0.1) 360deg);
        border-radius: 24px;
        z-index: 0;
        animation: dropdownGlow 8s linear infinite;
        opacity: 0.6;
    }

    @keyframes premiumDropdownFadeIn {
        from {
            opacity: 0;
            transform: translateY(-20px) scale(0.9);
            filter: blur(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
            filter: blur(0px);
        }
    }

    @keyframes dropdownGlow {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .notification-dropdown-menu.show,
    .dropdown-menu.show {
        display: block !important;
    }

    .dropdown-menu > * {
        position: relative;
        z-index: 1;
    }

    /* Premium Dropdown Items */
    .dropdown-item {
        display: flex;
        align-items: center;
        padding: 1rem 1.5rem;
        color: #374151;
        text-decoration: none;
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        cursor: pointer;
        font-weight: 600;
        font-size: 0.9rem;
        border: none;
        background: transparent;
        width: 100%;
        text-align: left;
        margin: 0.2rem 0.5rem;
        border-radius: 16px;
        position: relative;
        overflow: hidden;
    }

    .dropdown-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
            transparent 0%,
            rgba(102, 126, 234, 0.1) 50%,
            transparent 100%);
        transition: left 0.4s ease;
    }

    .dropdown-item:hover {
        background: linear-gradient(135deg,
            rgba(102, 126, 234, 0.15) 0%,
            rgba(118, 75, 162, 0.15) 100%);
        color: #667eea;
        transform: translateX(8px);
        box-shadow:
            0 4px 12px rgba(102, 126, 234, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }

    .dropdown-item:hover::before {
        left: 100%;
    }

    .dropdown-item.text-danger {
        color: #dc2626;
    }

    .dropdown-item.text-danger:hover {
        background: linear-gradient(135deg,
            rgba(239, 68, 68, 0.15) 0%,
            rgba(220, 38, 38, 0.15) 100%);
        color: #dc2626;
    }

    .dropdown-item i {
        margin-right: 1rem;
        width: 1.2rem;
        text-align: center;
        font-size: 1rem;
        filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.2));
    }

    .dropdown-divider {
        height: 2px;
        margin: 0.8rem 1rem;
        background: linear-gradient(90deg,
            transparent 0%,
            rgba(102, 126, 234, 0.3) 50%,
            transparent 100%);
        border: none;
    }

    /* Premium Form Controls */
    .form-select {
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.9) 0%,
            rgba(248, 249, 250, 0.8) 100%);
        border: 2px solid rgba(102, 126, 234, 0.2);
        border-radius: 16px;
        padding: 1rem 3rem 1rem 1.5rem;
        font-weight: 600;
        color: #374151;
        backdrop-filter: blur(20px);
        box-shadow:
            0 4px 12px rgba(102, 126, 234, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.5);
        transition: all 0.3s ease;
        background-position: right 1rem center;
        background-size: 16px;
    }

    .form-select:focus {
        outline: none;
        border-color: #667eea;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.95) 0%,
            rgba(248, 249, 250, 0.9) 100%);
        box-shadow:
            0 0 0 3px rgba(102, 126, 234, 0.2),
            0 8px 20px rgba(102, 126, 234, 0.2),
            inset 0 2px 0 rgba(255, 255, 255, 0.6);
        transform: translateY(-2px);
    }

    .form-label {
        margin-bottom: 0.8rem;
        font-weight: 700;
        color: white;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 1px;
        text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    }

    /* Premium Empty State */
    .empty-state {
        text-align: center;
        padding: 5rem 2rem;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.1) 0%,
            rgba(255, 255, 255, 0.05) 100%);
        backdrop-filter: blur(30px);
        border: 2px solid rgba(255, 255, 255, 0.2);
        border-radius: 32px;
        margin: 2rem 0;
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }

    .empty-state-icon {
        font-size: 4rem;
        color: rgba(255, 255, 255, 0.6);
        margin-bottom: 2rem;
        filter: drop-shadow(0 4px 15px rgba(0, 0, 0, 0.3));
        animation: emptyStateFloat 4s ease-in-out infinite;
    }

    @keyframes emptyStateFloat {
        0%, 100% { transform: translateY(0px) scale(1); }
        50% { transform: translateY(-10px) scale(1.05); }
    }

    .empty-state-title {
        font-size: 2rem;
        font-weight: 800;
        color: white;
        margin-bottom: 1rem;
        background: linear-gradient(135deg, #ffffff 0%, #e0e0e0 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    }

    .empty-state-description {
        font-size: 1.1rem;
        color: rgba(255, 255, 255, 0.8);
        max-width: 600px;
        margin: 0 auto 2rem;
        line-height: 1.6;
        font-weight: 500;
        text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }

    /* Pagination Styling */
    .pagination {
        justify-content: center;
        margin-top: 3rem;
        gap: 0.5rem;
    }

    .page-link {
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.9) 0%,
            rgba(248, 249, 250, 0.8) 100%);
        border: 2px solid rgba(102, 126, 234, 0.2);
        color: #667eea;
        padding: 1rem 1.5rem;
        border-radius: 16px;
        font-weight: 700;
        transition: all 0.3s ease;
        backdrop-filter: blur(20px);
        box-shadow:
            0 4px 12px rgba(102, 126, 234, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.5);
    }

    .page-link:hover {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-color: rgba(255, 255, 255, 0.4);
        transform: translateY(-3px) scale(1.05);
        box-shadow:
            0 8px 20px rgba(102, 126, 234, 0.3),
            inset 0 2px 0 rgba(255, 255, 255, 0.3);
    }

    .page-item.active .page-link {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-color: rgba(255, 255, 255, 0.4);
        box-shadow:
            0 8px 20px rgba(102, 126, 234, 0.4),
            inset 0 2px 0 rgba(255, 255, 255, 0.3);
    }

    /* Premium Responsive Design */
    @media (max-width: 991px) {
        .notification-container {
            padding: 3rem 1.5rem;
        }

        .notification-title {
            font-size: 2.5rem;
        }

        .notification-filters {
            padding: 2rem;
        }

        .notification-item {
            padding: 2rem;
        }

        .notification-icon {
            width: 60px;
            height: 60px;
            margin-right: 1.5rem;
        }

        .notification-icon i {
            font-size: 1.5rem;
        }
    }

    @media (max-width: 768px) {
        .notification-container {
            padding: 2rem 1rem;
        }

        .notification-title {
            font-size: 2rem;
        }

        .notification-header {
            flex-direction: column;
            gap: 1.5rem;
            text-align: center;
        }

        .notification-actions {
            justify-content: center;
        }

        .notification-filters {
            padding: 1.5rem;
        }

        .notification-filters .col-md-4 {
            margin-bottom: 1.5rem;
        }

        .notification-item {
            flex-direction: column;
            position: relative;
            padding: 2rem 1.5rem;
            text-align: center;
        }

        .notification-icon {
            margin: 0 auto 1.5rem;
            width: 60px;
            height: 60px;
        }

        .notification-actions-menu {
            position: absolute;
            top: 1.5rem;
            right: 1.5rem;
            margin-left: 0;
        }

        .notification-content {
            width: 100%;
            padding-right: 0;
        }

        .notification-meta {
            flex-wrap: wrap;
            gap: 1rem;
            justify-content: center;
        }

        .dropdown-menu {
            min-width: 250px;
            right: -50px;
        }

        .btn {
            padding: 0.8rem 1.5rem;
            font-size: 0.8rem;
        }

        .empty-state {
            padding: 3rem 1rem;
        }

        .empty-state-icon {
            font-size: 3rem;
        }

        .empty-state-title {
            font-size: 1.5rem;
        }
    }

    @media (max-width: 480px) {
        .notification-container {
            padding: 1.5rem 0.5rem;
        }

        .notification-title {
            font-size: 1.8rem;
        }

        .notification-item {
            padding: 1.5rem 1rem;
        }

        .notification-icon {
            width: 50px;
            height: 50px;
        }

        .notification-icon i {
            font-size: 1.2rem;
        }

        .notification-title {
            font-size: 1.2rem;
        }

        .notification-message {
            font-size: 1rem;
        }

        .notification-category,
        .notification-time {
            padding: 0.4rem 0.8rem;
            font-size: 0.7rem;
        }

        .notification-action-btn {
            width: 40px;
            height: 40px;
            padding: 0.8rem;
        }

        .dropdown-menu {
            min-width: 220px;
            right: -30px;
        }

        .btn {
            padding: 0.7rem 1.2rem;
            font-size: 0.75rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-8">
    <div class="flex justify-between items-center mb-8">
        <div>
            <h1 class="text-4xl font-bold text-gray-900 mb-2">Notifications</h1>
            <p class="text-lg text-gray-600">Stay updated with your QR code activities and system updates</p>
        </div>
        <div class="flex gap-3">
            <form method="post" action="{% url 'notifications:mark_all_as_read' %}" id="mark-all-form">
                {% csrf_token %}
                <button type="submit" class="btn btn-secondary icon-button">
                    <span class="icon-success icon-primary"></span>
                    <span>Mark All as Read</span>
                </button>
            </form>
        </div>
    </div>

    <div class="notification-filters">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label for="category" class="form-label">Category</label>
                <select name="category" id="category" class="form-select">
                    <option value="">All Categories</option>
                    {% for value, label in categories %}
                    <option value="{{ value }}" {% if selected_category == value %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-4">
                <label for="status" class="form-label">Status</label>
                <select name="status" id="status" class="form-select">
                    <option value="">All Notifications</option>
                    <option value="unread" {% if selected_status == 'unread' %}selected{% endif %}>Unread</option>
                    <option value="read" {% if selected_status == 'read' %}selected{% endif %}>Read</option>
                    <option value="archived" {% if selected_status == 'archived' %}selected{% endif %}>Archived</option>
                    <option value="muted" {% if selected_status == 'muted' %}selected{% endif %}>Muted</option>
                </select>
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-filter me-2"></i>Filter
                </button>
                {% if selected_category or selected_status %}
                <a href="{% url 'notifications:notification_list' %}" class="btn btn-outline-secondary ms-2">
                    <i class="fas fa-times me-2"></i>Clear
                </a>
                {% endif %}
            </div>
        </form>
    </div>

    <div class="notification-list">
        {% if page_obj %}
            {% for notification in page_obj %}
                <div class="notification-item {{ notification.notification_type }} {% if not notification.is_read %}unread{% endif %}">
                    <div class="notification-icon">
                        {% if notification.notification_type == 'info' %}
                            <i class="fas fa-info"></i>
                        {% elif notification.notification_type == 'success' %}
                            <i class="fas fa-check"></i>
                        {% elif notification.notification_type == 'warning' %}
                            <i class="fas fa-exclamation"></i>
                        {% elif notification.notification_type == 'error' %}
                            <i class="fas fa-times"></i>
                        {% endif %}
                    </div>
                    <div class="notification-content">
                        <h3 class="notification-title">{{ notification.title }}</h3>
                        <p class="notification-message">{{ notification.message }}</p>
                        <div class="notification-meta">
                            <span class="notification-category">
                                {% if notification.category == 'system' %}
                                    <i class="fas fa-cog me-1"></i>
                                {% elif notification.category == 'ad' %}
                                    <i class="fas fa-ad me-1"></i>
                                {% elif notification.category == 'payment' %}
                                    <i class="fas fa-money-bill me-1"></i>
                                {% elif notification.category == 'qr_code' %}
                                    <i class="fas fa-qrcode me-1"></i>
                                {% elif notification.category == 'user' %}
                                    <i class="fas fa-user me-1"></i>
                                {% elif notification.category == 'campaign' %}
                                    <i class="fas fa-bullhorn me-1"></i>
                                {% elif notification.category == 'analytics' %}
                                    <i class="fas fa-chart-bar me-1"></i>
                                {% else %}
                                    <i class="fas fa-bell me-1"></i>
                                {% endif %}
                                {{ notification.get_category_display }}
                            </span>
                            <span class="notification-time">
                                <i class="far fa-clock"></i>
                                {{ notification.created_at|timesince }} ago
                            </span>
                        </div>
                    </div>
                    <div class="notification-actions-menu">
                        <div class="notification-dropdown">
                            <button class="notification-action-btn dropdown-toggle">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div class="dropdown-menu dropdown-menu-end">
                                <a href="{% url 'notifications:notification_detail' pk=notification.pk %}" class="dropdown-item">
                                    <i class="fas fa-eye"></i> View Details
                                </a>
                                {% if notification.is_read %}
                                    <form method="post" action="{% url 'notifications:mark_as_unread' pk=notification.pk %}">
                                        {% csrf_token %}
                                        <button type="submit" class="dropdown-item">
                                            <i class="fas fa-envelope"></i> Mark as Unread
                                        </button>
                                    </form>
                                {% else %}
                                    <form method="post" action="{% url 'notifications:mark_as_read' pk=notification.pk %}">
                                        {% csrf_token %}
                                        <button type="submit" class="dropdown-item">
                                            <i class="fas fa-envelope-open"></i> Mark as Read
                                        </button>
                                    </form>
                                {% endif %}
                                <form method="post" action="{% url 'notifications:archive_notification' pk=notification.pk %}" class="archive-form">
                                    {% csrf_token %}
                                    <button type="submit" class="dropdown-item">
                                        <i class="fas fa-archive"></i> Archive
                                    </button>
                                </form>
                                <form method="post" action="{% url 'notifications:mute_notification' pk=notification.pk %}" class="mute-form">
                                    {% csrf_token %}
                                    <button type="submit" class="dropdown-item">
                                        <i class="fas fa-volume-mute"></i> Mute
                                    </button>
                                </form>
                                <div class="dropdown-divider"></div>
                                <form method="post" action="{% url 'notifications:delete_notification' pk=notification.pk %}" class="delete-form">
                                    {% csrf_token %}
                                    <button type="submit" class="dropdown-item text-danger">
                                        <i class="fas fa-trash"></i> Delete
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}

            {% if page_obj.has_other_pages %}
                <nav aria-label="Notification pagination" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if selected_category %}&category={{ selected_category }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}" aria-label="First">
                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if selected_category %}&category={{ selected_category }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="First">
                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                        {% endif %}

                        {% for i in page_obj.paginator.page_range %}
                            {% if page_obj.number == i %}
                                <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
                            {% elif i > page_obj.number|add:'-3' and i < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ i }}{% if selected_category %}&category={{ selected_category }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}">{{ i }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if selected_category %}&category={{ selected_category }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if selected_category %}&category={{ selected_category }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}" aria-label="Last">
                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Last">
                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="empty-state">
                <div class="empty-state-icon">
                    <i class="fas fa-bell-slash"></i>
                </div>
                <h3 class="empty-state-title">No notifications found</h3>
                <p class="empty-state-description">
                    You don't have any notifications at the moment. Notifications will appear here when there are updates to your ads, payments, or other activities.
                </p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
{% load static %}
<script src="{% static 'js/notification-actions.js' %}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle filter form submission
        const filterForm = document.querySelector('.notification-filters form');
        if (filterForm) {
            // Add event listener to category and status selects to auto-submit on change
            const selects = filterForm.querySelectorAll('select');
            selects.forEach(select => {
                select.addEventListener('change', function() {
                    filterForm.submit();
                });
            });
        }
    });
</script>
{% endblock %}
