from django.core.management.base import BaseCommand
from ads.models import AdType, AdLocation
from decimal import Decimal

class Command(BaseCommand):
    help = 'Creates Featured Ad type and Content Inline location'

    def handle(self, *args, **options):
        # Create Featured Ad type if it doesn't exist
        featured_type, created = AdType.objects.get_or_create(
            name='Featured Ad',
            defaults={
                'description': 'Premium featured advertisement with enhanced visibility',
                'base_price': Decimal('150.00'),
                'is_active': True,
                'is_premium': True
            }
        )
        
        if created:
            self.stdout.write(self.style.SUCCESS(f"Created new ad type: {featured_type.name}"))
        else:
            self.stdout.write(self.style.SUCCESS(f"Using existing ad type: {featured_type.name}"))
        
        # Create Content Inline location if it doesn't exist
        content_inline, created = AdLocation.objects.get_or_create(
            name='Content Inline',
            defaults={
                'description': 'Inline position within content areas for medium visibility',
                'price_multiplier': Decimal('1.5'),
                'visibility': 'medium',
                'is_active': True,
                'is_premium': False,
                'daily_impressions': 3000
            }
        )
        
        if created:
            self.stdout.write(self.style.SUCCESS(f"Created new ad location: {content_inline.name} (Visibility: {content_inline.visibility})"))
        else:
            self.stdout.write(self.style.SUCCESS(f"Using existing ad location: {content_inline.name} (Visibility: {content_inline.visibility})"))
            
            # Update visibility if needed
            if content_inline.visibility != 'medium':
                old_visibility = content_inline.visibility
                content_inline.visibility = 'medium'
                content_inline.save()
                self.stdout.write(self.style.SUCCESS(f"Updated visibility from {old_visibility} to medium"))
        
        self.stdout.write(self.style.SUCCESS("Featured Ad type and Content Inline location setup complete!"))
