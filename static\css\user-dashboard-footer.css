/* 
 * User Dashboard Footer Styles
 * Specific styles for user dashboard footer
 */

/* Fix for enterprise dashboard pages */
.enterprise-dashboard + .enterprise-footer {
    margin-top: 0 !important;
}

/* Fix for dashboard content to take up available space */
.dashboard-content {
    flex: 1 0 auto;
}

/* Ensure enterprise dashboard takes full height */
.enterprise-dashboard {
    min-height: calc(100vh - 65px) !important; /* Adjust for bottom nav height */
    display: flex;
    flex-direction: column;
}

/* Fix for user dashboard sections */
.dashboard-section:last-child {
    margin-bottom: 0 !important;
}

/* Fix for user dashboard cards */
.dashboard-card:last-child {
    margin-bottom: 0 !important;
}

/* Fix for user dashboard stats */
.stats-row:last-child {
    margin-bottom: 0 !important;
}

/* Fix for user dashboard tab content */
.tab-content {
    height: 100%;
}

/* Fix for user dashboard tabs */
.tab-pane:not(#admin) {
    margin-bottom: 0 !important;
}

/* Fix for mobile bottom navigation */
@media (max-width: 991.98px) {
    .enterprise-footer {
        padding-bottom: 65px !important; /* Match bottom nav height */
        margin-bottom: 0 !important;
    }
}

/* Landscape mode adjustments */
@media (max-width: 767.98px) and (orientation: landscape) {
    .enterprise-footer {
        padding-bottom: 55px !important; /* Match landscape bottom nav height */
    }
}

/* Fix for user dashboard empty states */
.empty-state {
    margin-bottom: 0 !important;
}

/* Fix for user dashboard charts */
.chart-container {
    margin-bottom: 0 !important;
}

/* Fix for user dashboard action buttons */
.dashboard-actions {
    margin-bottom: 0 !important;
}
