from django.urls import path
from . import views

app_name = 'notifications'

urlpatterns = [
    # List and detail views
    path('', views.notification_list, name='notification_list'),
    path('<int:pk>/', views.notification_detail, name='notification_detail'),

    # Action views
    path('<int:pk>/mark-as-read/', views.mark_as_read, name='mark_as_read'),
    path('<int:pk>/mark-as-unread/', views.mark_as_unread, name='mark_as_unread'),
    path('<int:pk>/delete/', views.delete_notification, name='delete_notification'),
    path('<int:pk>/archive/', views.archive_notification, name='archive_notification'),
    path('<int:pk>/mute/', views.mute_notification, name='mute_notification'),
    path('mark-all-as-read/', views.mark_all_as_read, name='mark_all_as_read'),
    path('mute-category/', views.mute_category, name='mute_category'),

    # AJAX views
    path('count/', views.notification_count, name='notification_count'),
]
