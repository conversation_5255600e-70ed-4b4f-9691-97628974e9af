from django.db.models.signals import post_save
from django.dispatch import receiver
from django.urls import reverse
from django.contrib.auth.models import User

from .services import NotificationService

# Import models from other apps that will trigger notifications
try:
    from ads.models import Ad, Transaction, Campaign
    ADS_APP_AVAILABLE = True
except ImportError:
    ADS_APP_AVAILABLE = False

try:
    from qrcode_app.models import QRCode
    QRCODE_APP_AVAILABLE = True
except ImportError:
    QRCODE_APP_AVAILABLE = False


# Ad-related notifications
if ADS_APP_AVAILABLE:
    @receiver(post_save, sender=Ad)
    def ad_status_changed(sender, instance, created, **kwargs):
        """
        Send notification when ad status changes
        """
        # Skip if this is a new ad (initial creation)
        if created:
            # Notify user that ad was created
            NotificationService.create_notification(
                user=instance.user,
                title="Ad Created Successfully",
                message=f"Your ad '{instance.title}' has been created and is pending approval.",
                notification_type="success",
                category="ad",
                content_object=instance,
                action_url=reverse('ads:ad_detail', kwargs={'slug': instance.slug})
            )

            # Notify admins about new ad
            admins = User.objects.filter(is_superuser=True)
            NotificationService.create_notification_for_many_users(
                users=admins,
                title="New Ad Submitted",
                message=f"A new ad '{instance.title}' has been submitted by {instance.user.username} and requires approval.",
                notification_type="info",
                category="ad",
                content_object=instance,
                action_url=reverse('ads:admin_view_ad', kwargs={'slug': instance.slug})
            )
            return

        # Get the previous state of the ad (if available)
        if not hasattr(instance, '_previous_status'):
            return

        # Check if status has changed
        if instance._previous_status != instance.status:
            # Status changed, send notification based on new status
            if instance.status == 'approved':
                NotificationService.create_notification(
                    user=instance.user,
                    title="Ad Approved - Payment Required",
                    message=f"Your ad '{instance.title}' has been approved. Please proceed with payment to activate it.",
                    notification_type="success",
                    category="ad",
                    content_object=instance,
                    action_url=reverse('ads:payment_process', kwargs={'slug': instance.slug})
                )
            elif instance.status == 'rejected':
                NotificationService.create_notification(
                    user=instance.user,
                    title="Ad Rejected - Edits Required",
                    message=f"Your ad '{instance.title}' has been rejected. Please review and make necessary changes.",
                    notification_type="error",
                    category="ad",
                    content_object=instance,
                    action_url=reverse('ads:ad_edit', kwargs={'slug': instance.slug})
                )
            elif instance.status == 'active':
                NotificationService.create_notification(
                    user=instance.user,
                    title="Ad Activated",
                    message=f"Your ad '{instance.title}' is now active and will be displayed to users.",
                    notification_type="success",
                    category="ad",
                    content_object=instance,
                    action_url=reverse('ads:ad_detail', kwargs={'slug': instance.slug})
                )
            elif instance.status == 'paused':
                NotificationService.create_notification(
                    user=instance.user,
                    title="Ad Paused",
                    message=f"Your ad '{instance.title}' has been paused and will not be displayed to users.",
                    notification_type="warning",
                    category="ad",
                    content_object=instance,
                    action_url=reverse('ads:ad_detail', kwargs={'slug': instance.slug})
                )
            elif instance.status == 'expired':
                NotificationService.create_notification(
                    user=instance.user,
                    title="Ad Expired",
                    message=f"Your ad '{instance.title}' has expired. You can renew it from the ad details page.",
                    notification_type="warning",
                    category="ad",
                    content_object=instance,
                    action_url=reverse('ads:ad_detail', kwargs={'slug': instance.slug})
                )

    @receiver(post_save, sender=Transaction)
    def transaction_status_changed(sender, instance, created, **kwargs):
        """
        Send notification when transaction status changes
        """
        # Skip if this is a new transaction (initial creation)
        if created:
            # Notify user that payment was received
            NotificationService.create_notification(
                user=instance.user,
                title="Payment Received",
                message=f"Your payment of {instance.amount} KSH for ad '{instance.ad.title}' has been received and is pending approval.",
                notification_type="success",
                category="payment",
                content_object=instance,
                action_url=reverse('ads:transaction_detail', kwargs={'transaction_id': instance.id})
            )

            # Notify admins about new payment
            admins = User.objects.filter(is_superuser=True)
            NotificationService.create_notification_for_many_users(
                users=admins,
                title="New Payment Received",
                message=f"A new payment of {instance.amount} KSH has been received from {instance.user.username} for ad '{instance.ad.title}'.",
                notification_type="info",
                category="payment",
                content_object=instance,
                action_url=reverse('ads:admin_pending_payments')
            )
            return

        # Get the previous state of the transaction (if available)
        if not hasattr(instance, '_previous_status'):
            return

        # Check if status has changed
        if instance._previous_status != instance.status:
            # Status changed, send notification based on new status
            if instance.status == 'approved':
                NotificationService.create_notification(
                    user=instance.user,
                    title="Payment Approved",
                    message=f"Your payment of {instance.amount} KSH for ad '{instance.ad.title}' has been approved. Your ad is now active.",
                    notification_type="success",
                    category="payment",
                    content_object=instance,
                    action_url=reverse('ads:ad_detail', kwargs={'slug': instance.ad.slug})
                )
            elif instance.status == 'rejected':
                NotificationService.create_notification(
                    user=instance.user,
                    title="Payment Rejected",
                    message=f"Your payment of {instance.amount} KSH for ad '{instance.ad.title}' has been rejected. Please contact support for assistance.",
                    notification_type="error",
                    category="payment",
                    content_object=instance,
                    action_url=reverse('ads:transaction_detail', kwargs={'transaction_id': instance.id})
                )

    @receiver(post_save, sender=Campaign)
    def campaign_status_changed(sender, instance, created, **kwargs):
        """
        Send notification when campaign status changes
        """
        # Skip if this is a new campaign (initial creation)
        if created:
            # Notify user that campaign was created
            NotificationService.create_notification(
                user=instance.user,
                title="Campaign Created",
                message=f"Your campaign '{instance.name}' has been created successfully.",
                notification_type="success",
                category="campaign",
                content_object=instance,
                action_url=reverse('ads:campaign_detail', kwargs={'slug': instance.slug})
            )
            return

        # Get the previous state of the campaign (if available)
        if not hasattr(instance, '_previous_status'):
            return

        # Check if status has changed
        if instance._previous_status != instance.status:
            # Status changed, send notification based on new status
            if instance.status == 'active':
                NotificationService.create_notification(
                    user=instance.user,
                    title="Campaign Activated",
                    message=f"Your campaign '{instance.name}' is now active.",
                    notification_type="success",
                    category="campaign",
                    content_object=instance,
                    action_url=reverse('ads:campaign_detail', kwargs={'slug': instance.slug})
                )
            elif instance.status == 'paused':
                NotificationService.create_notification(
                    user=instance.user,
                    title="Campaign Paused",
                    message=f"Your campaign '{instance.name}' has been paused.",
                    notification_type="warning",
                    category="campaign",
                    content_object=instance,
                    action_url=reverse('ads:campaign_detail', kwargs={'slug': instance.slug})
                )
            elif instance.status == 'completed':
                NotificationService.create_notification(
                    user=instance.user,
                    title="Campaign Completed",
                    message=f"Your campaign '{instance.name}' has been completed.",
                    notification_type="info",
                    category="campaign",
                    content_object=instance,
                    action_url=reverse('ads:campaign_detail', kwargs={'slug': instance.slug})
                )


# QR Code-related notifications
if QRCODE_APP_AVAILABLE:
    @receiver(post_save, sender=QRCode)
    def qrcode_created(sender, instance, created, **kwargs):
        """
        Send notification when QR code is created
        """
        if created:
            NotificationService.create_notification(
                user=instance.user,
                title="QR Code Created",
                message=f"Your QR code '{instance.name}' has been created successfully.",
                notification_type="success",
                category="qr_code",
                content_object=instance,
                action_url=reverse('qr_code_detail', kwargs={'pk': instance.pk})
            )
