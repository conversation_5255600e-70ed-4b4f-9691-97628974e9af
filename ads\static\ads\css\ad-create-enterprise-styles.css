/**
 * Ad Create Enterprise Styles
 * Extracted from inline styles in the ad creation template
 */

/* Card Styles */
.dashboard-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-title {
    margin-bottom: 0;
    font-weight: 600;
    color: #212529;
}

.card-body {
    padding: 1.5rem;
}

/* Form Spacing */
.mb-3 {
    margin-bottom: 1rem;
}

.mb-4 {
    margin-bottom: 1.5rem;
}

/* Switch Toggle */
.form-check-input {
    cursor: pointer;
}

.form-switch .form-check-input {
    width: 2.5em;
    margin-left: -2.8em;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280, 0, 0, 0.25%29'/%3e%3c/svg%3e");
    background-position: left center;
    border-radius: 2em;
    transition: background-position 0.15s ease-in-out;
}

.form-switch .form-check-input:checked {
    background-position: right center;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
}

/* Badge Styles */
.badge {
    padding: 0.35em 0.65em;
    font-size: 0.75em;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25rem;
}

.bg-info {
    background-color: #17a2b8;
    color: #fff;
}

/* Alert Styles */
.alert {
    position: relative;
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.25rem;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

/* Custom Duration Fields */
#customDurationFields {
    transition: all 0.3s ease;
}

/* Calculated End Time */
#calculatedEndTime {
    font-size: 0.9rem;
}

#endDateTimeDisplay {
    font-weight: 600;
}

/* Review Section */
#step4 .row {
    margin-bottom: 0.5rem;
}

#step4 .fw-bold {
    color: #495057;
}

/* Pricing Summary */
.card.bg-light {
    background-color: #f8f9fa;
}

.d-flex.justify-content-between {
    display: flex;
    justify-content: space-between;
}

hr {
    margin: 1rem 0;
    color: inherit;
    background-color: currentColor;
    border: 0;
    opacity: 0.25;
}

.fw-bold {
    font-weight: 700;
}

/* Hidden Fields */
.d-none {
    display: none;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .card-header {
        padding: 0.75rem 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .card-title {
        font-size: 1.1rem;
    }
}
