"""
Management command to set up public testing for QR codes using ngrok or custom domain
"""
from django.core.management.base import BaseCommand
from django.conf import settings
from qrcode_app.models import QRCode
import qrcode
from io import BytesIO
import subprocess
import json
import time
import requests


class Command(BaseCommand):
    help = 'Set up public testing for QR codes using ngrok tunnel'

    def add_arguments(self, parser):
        parser.add_argument(
            '--domain',
            type=str,
            help='Custom domain to use (e.g., yourdomain.com)',
        )
        parser.add_argument(
            '--ngrok',
            action='store_true',
            help='Use ngrok to create a public tunnel',
        )
        parser.add_argument(
            '--port',
            type=int,
            default=8000,
            help='Local port to tunnel (default: 8000)',
        )
        parser.add_argument(
            '--update-qr-codes',
            action='store_true',
            help='Update existing QR codes to use the new domain',
        )

    def check_ngrok_installed(self):
        """Check if ngrok is installed"""
        try:
            result = subprocess.run(['ngrok', 'version'], capture_output=True, text=True)
            return result.returncode == 0
        except FileNotFoundError:
            return False

    def start_ngrok_tunnel(self, port):
        """Start ngrok tunnel and return the public URL"""
        try:
            # Start ngrok in background
            self.stdout.write('Starting ngrok tunnel...')
            process = subprocess.Popen(
                ['ngrok', 'http', str(port), '--log=stdout'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Wait a bit for ngrok to start
            time.sleep(3)
            
            # Get the public URL from ngrok API
            try:
                response = requests.get('http://127.0.0.1:4040/api/tunnels')
                if response.status_code == 200:
                    tunnels = response.json()['tunnels']
                    for tunnel in tunnels:
                        if tunnel['proto'] == 'https':
                            return tunnel['public_url'], process
                    # Fallback to http if https not found
                    for tunnel in tunnels:
                        if tunnel['proto'] == 'http':
                            return tunnel['public_url'], process
            except Exception as e:
                self.stdout.write(f'Error getting ngrok URL: {e}')
                
            return None, process
            
        except Exception as e:
            self.stdout.write(f'Error starting ngrok: {e}')
            return None, None

    def update_qr_codes_with_domain(self, domain, use_https=True):
        """Update existing QR codes to use the new domain"""
        protocol = 'https' if use_https else 'http'
        
        # Find QR codes that need updating
        qr_codes_to_update = QRCode.objects.filter(
            data__contains='/qr/'
        ).exclude(
            data__startswith=f'{protocol}://{domain}'
        )
        
        total_count = qr_codes_to_update.count()
        
        if total_count == 0:
            self.stdout.write('No QR codes need updating.')
            return
        
        self.stdout.write(f'Updating {total_count} QR codes to use domain: {domain}')
        
        updated_count = 0
        error_count = 0
        
        for qr_code in qr_codes_to_update:
            try:
                # Extract the path from the current URL
                if '/qr/' in qr_code.data:
                    # Extract the unique ID from the URL
                    parts = qr_code.data.split('/qr/')
                    if len(parts) > 1:
                        unique_path = parts[1]
                        new_url = f'{protocol}://{domain}/qr/{unique_path}'
                        
                        # Update QR code data
                        old_url = qr_code.data
                        qr_code.data = new_url
                        
                        # Regenerate QR code image
                        qr = qrcode.QRCode(
                            version=40,
                            error_correction=qrcode.constants.ERROR_CORRECT_L,
                            box_size=10,
                            border=4,
                        )
                        qr.add_data(new_url)
                        qr.make(fit=True)
                        
                        # Create the QR code image
                        img = qr.make_image(
                            fill_color=qr_code.foreground_color, 
                            back_color=qr_code.background_color
                        )
                        
                        # Save the QR code image
                        buffer = BytesIO()
                        img.save(buffer, format='PNG')
                        
                        # Save to model
                        filename = f"{qr_code.name.replace(' ', '_')}.png"
                        qr_code.image.save(filename, buffer, save=False)
                        qr_code.save()
                        
                        updated_count += 1
                        self.stdout.write(f'✓ Updated: {qr_code.name}')
                        self.stdout.write(f'  {old_url} → {new_url}')
                        
            except Exception as e:
                error_count += 1
                self.stdout.write(f'✗ Error updating {qr_code.name}: {str(e)}')
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully updated {updated_count} QR codes. Errors: {error_count}'
            )
        )

    def handle(self, *args, **options):
        domain = options.get('domain')
        use_ngrok = options.get('ngrok')
        port = options.get('port')
        update_qr_codes = options.get('update_qr_codes')
        
        if use_ngrok:
            # Check if ngrok is installed
            if not self.check_ngrok_installed():
                self.stdout.write(
                    self.style.ERROR(
                        'ngrok is not installed. Please install ngrok from https://ngrok.com/'
                    )
                )
                return
            
            # Start ngrok tunnel
            public_url, process = self.start_ngrok_tunnel(port)
            
            if public_url:
                # Extract domain from URL
                domain = public_url.replace('https://', '').replace('http://', '')
                use_https = public_url.startswith('https://')
                
                self.stdout.write(
                    self.style.SUCCESS(f'✓ ngrok tunnel started: {public_url}')
                )
                self.stdout.write(f'Domain: {domain}')
                
                if update_qr_codes:
                    self.update_qr_codes_with_domain(domain, use_https)
                
                self.stdout.write('\n' + '='*60)
                self.stdout.write('🎉 PUBLIC TESTING SETUP COMPLETE!')
                self.stdout.write('='*60)
                self.stdout.write(f'Public URL: {public_url}')
                self.stdout.write(f'Test your QR codes by scanning them from any device!')
                self.stdout.write('\nTo stop the tunnel, press Ctrl+C')
                self.stdout.write('='*60)
                
                try:
                    # Keep the process running
                    process.wait()
                except KeyboardInterrupt:
                    self.stdout.write('\nStopping ngrok tunnel...')
                    process.terminate()
                    
            else:
                self.stdout.write(
                    self.style.ERROR('Failed to start ngrok tunnel')
                )
                
        elif domain:
            # Use custom domain
            self.stdout.write(f'Setting up custom domain: {domain}')
            
            if update_qr_codes:
                self.update_qr_codes_with_domain(domain, use_https=True)
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'✓ Custom domain configured: https://{domain}'
                )
            )
            
        else:
            self.stdout.write(
                self.style.ERROR(
                    'Please specify either --domain or --ngrok option'
                )
            )
