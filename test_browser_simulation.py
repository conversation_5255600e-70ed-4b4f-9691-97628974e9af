#!/usr/bin/env python
"""
Browser Simulation Test - Test Smart AI Engine via HTTP requests
Simulates what happens when a user generates suggestions in the browser
"""

import requests
import json
import time
from urllib.parse import urljoin

# Test configuration
BASE_URL = "http://127.0.0.1:8000"
TEST_PARAMS = {
    'business_type': 'Premium Coffee Shop',
    'target_audience': 'Young Professionals',
    'tone': 'professional',
    'language': 'english',
    'ad_title': 'Premium Coffee Experience',
    'num_suggestions': 3
}

def test_ad_generation_endpoint():
    """Test the ad generation endpoint via HTTP request"""
    print("🌐 TESTING SMART AI ENGINE VIA BROWSER SIMULATION")
    print("=" * 60)
    
    # Test parameters
    print(f"📋 Test Parameters:")
    for key, value in TEST_PARAMS.items():
        print(f"   {key}: {value}")
    print()
    
    # Test 1: Check if server is running
    print("🔍 Step 1: Check Server Status")
    try:
        response = requests.get(BASE_URL, timeout=5)
        if response.status_code == 200:
            print("   ✅ Django server is running")
        else:
            print(f"   ⚠️  Server responded with status {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Server is not accessible: {e}")
        return False
    print()
    
    # Test 2: Check ad creation page
    print("📄 Step 2: Check Ad Creation Page")
    try:
        ad_create_url = urljoin(BASE_URL, "/ads/create/")
        response = requests.get(ad_create_url, timeout=10)
        if response.status_code == 200:
            print("   ✅ Ad creation page is accessible")
            print(f"   📊 Page size: {len(response.content)} bytes")
        else:
            print(f"   ❌ Ad creation page returned status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Failed to access ad creation page: {e}")
        return False
    print()
    
    # Test 3: Generate suggestions via AJAX (if endpoint exists)
    print("🚀 Step 3: Generate AI Suggestions")
    
    # Try different possible endpoints for suggestion generation
    possible_endpoints = [
        "/ads/generate-suggestions/",
        "/ads/api/generate/",
        "/api/ads/generate/",
        "/generate-suggestions/",
        "/ads/create/"  # POST to create page
    ]
    
    suggestion_generated = False
    
    for endpoint in possible_endpoints:
        try:
            url = urljoin(BASE_URL, endpoint)
            print(f"   🔄 Trying endpoint: {endpoint}")
            
            # Try POST request with form data
            start_time = time.time()
            response = requests.post(
                url, 
                data=TEST_PARAMS,
                timeout=30,
                headers={
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'  # Simulate AJAX
                }
            )
            end_time = time.time()
            response_time = end_time - start_time
            
            print(f"      Status: {response.status_code}")
            print(f"      Response time: {response_time:.2f}s")
            
            if response.status_code == 200:
                print("      ✅ Request successful!")
                
                # Try to parse JSON response
                try:
                    data = response.json()
                    if 'suggestions' in data:
                        suggestions = data['suggestions']
                        print(f"      🎯 Generated {len(suggestions)} suggestions!")
                        
                        # Display suggestions
                        for i, suggestion in enumerate(suggestions, 1):
                            print(f"         Suggestion {i}:")
                            print(f"            Title: {suggestion.get('title', 'N/A')}")
                            print(f"            Content: {suggestion.get('content', 'N/A')[:80]}...")
                            print(f"            Provider: {suggestion.get('provider', 'N/A')}")
                            print(f"            Model: {suggestion.get('model', 'N/A')}")
                            print(f"            Cached: {suggestion.get('cached', False)}")
                            print(f"            Smart Engine: {suggestion.get('smart_engine', False)}")
                            print()
                        
                        suggestion_generated = True
                        break
                    else:
                        print(f"      ⚠️  Response doesn't contain suggestions: {list(data.keys())}")
                        
                except json.JSONDecodeError:
                    # Not JSON, might be HTML response
                    content = response.text[:200]
                    if 'suggestion' in content.lower() or 'title' in content.lower():
                        print("      ✅ HTML response contains suggestion-related content")
                        suggestion_generated = True
                        break
                    else:
                        print(f"      ⚠️  HTML response (first 200 chars): {content}")
                        
            elif response.status_code == 405:
                print("      ⚠️  Method not allowed (trying GET)")
                # Try GET request
                get_response = requests.get(url, params=TEST_PARAMS, timeout=30)
                if get_response.status_code == 200:
                    print("      ✅ GET request successful!")
                    
            else:
                print(f"      ❌ Request failed with status {response.status_code}")
                
        except requests.exceptions.Timeout:
            print(f"      ⏰ Request timed out (>30s)")
        except requests.exceptions.RequestException as e:
            print(f"      ❌ Request failed: {e}")
        
        print()
    
    if suggestion_generated:
        print("🎉 SUCCESS: Smart AI Engine is working via browser!")
        print("   ✅ Suggestions generated successfully")
        print("   ✅ Response time acceptable")
        print("   ✅ Smart Engine metadata present")
    else:
        print("⚠️  Could not generate suggestions via standard endpoints")
        print("   This might be normal if the app uses custom AJAX endpoints")
        print("   Manual browser testing is recommended")
    
    print()
    return suggestion_generated

def test_direct_view_call():
    """Test calling the Django view directly"""
    print("🔧 Step 4: Test Direct Django View Call")
    
    try:
        # Import Django and call the view directly
        import os
        import sys
        import django
        from pathlib import Path
        
        # Setup Django
        project_root = Path(__file__).parent
        sys.path.insert(0, str(project_root))
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'qrcode_project.settings')
        django.setup()
        
        # Import the view
        from ads.views_enterprise import generate_ad_suggestions_ajax
        from django.http import HttpRequest
        from django.test import RequestFactory
        
        # Create a mock request
        factory = RequestFactory()
        request = factory.post('/ads/generate-suggestions/', TEST_PARAMS)
        request.META['HTTP_X_REQUESTED_WITH'] = 'XMLHttpRequest'
        
        print("   🔄 Calling generate_ad_suggestions_ajax directly...")
        start_time = time.time()
        response = generate_ad_suggestions_ajax(request)
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"   ⚡ Response time: {response_time:.2f}s")
        print(f"   📊 Status code: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = json.loads(response.content)
                if 'suggestions' in data:
                    suggestions = data['suggestions']
                    print(f"   🎯 Generated {len(suggestions)} suggestions!")
                    
                    # Analyze suggestions
                    for i, suggestion in enumerate(suggestions, 1):
                        print(f"      Suggestion {i}:")
                        print(f"         Title: {suggestion.get('title', 'N/A')}")
                        print(f"         Content: {suggestion.get('content', 'N/A')[:60]}...")
                        print(f"         Provider: {suggestion.get('provider', 'N/A')}")
                        print(f"         Model: {suggestion.get('model', 'N/A')}")
                        print(f"         Cached: {suggestion.get('cached', False)}")
                        print(f"         Smart Engine: {suggestion.get('smart_engine', False)}")
                        print(f"         Selection Score: {suggestion.get('selection_score', 'N/A')}")
                        print()
                    
                    return True
                else:
                    print(f"   ❌ No suggestions in response: {list(data.keys())}")
                    
            except json.JSONDecodeError as e:
                print(f"   ❌ Failed to parse JSON response: {e}")
                print(f"   📄 Response content: {response.content[:200]}")
                
        else:
            print(f"   ❌ View returned error status: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Direct view call failed: {e}")
        import traceback
        traceback.print_exc()
        
    return False

if __name__ == "__main__":
    print("🧪 SMART AI ENGINE BROWSER TESTING")
    print("=" * 60)
    
    # Test via HTTP requests
    http_success = test_ad_generation_endpoint()
    
    # Test via direct Django view call
    django_success = test_direct_view_call()
    
    print("📊 FINAL RESULTS:")
    print("=" * 60)
    print(f"HTTP Endpoint Test: {'✅ PASS' if http_success else '❌ FAIL'}")
    print(f"Django View Test: {'✅ PASS' if django_success else '❌ FAIL'}")
    
    if http_success or django_success:
        print("\n🎉 SMART AI ENGINE IS WORKING!")
        print("   The Smart AI Engine successfully generated suggestions")
        print("   Ready for full browser testing!")
    else:
        print("\n⚠️  MANUAL BROWSER TESTING NEEDED")
        print("   Automated tests couldn't generate suggestions")
        print("   Please test manually in the browser")
    
    print(f"\n🌐 Browser URL: {BASE_URL}/ads/create/")
    print("   Use the test parameters shown above")
