/* Enterprise-Grade Bottom Navigation */

/* Base Bottom Navigation */
.enterprise-bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 65px;
    z-index: 1050; /* Increased z-index to ensure it appears above most elements but below modals */
    display: none;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

/* Background with blur effect */
.bottom-nav-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(26, 31, 54, 0.95), rgba(18, 22, 40, 0.95));
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    box-shadow: 0 -5px 20px rgba(0, 0, 0, 0.2);
    border-top: 1px solid rgba(58, 123, 213, 0.25);
}

/* Content container */
.bottom-nav-content {
    position: relative;
    display: flex;
    justify-content: space-around;
    align-items: center;
    height: 100%;
    padding: 0 10px;
}

/* Navigation items */
.bottom-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    padding: 8px 0;
    flex: 1;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
}

/* Icon container with 3D effect */
.nav-icon-container {
    width: 38px;
    height: 38px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 4px;
    position: relative;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border-radius: 50%;
}

/* Icon styling */
.bottom-nav-item i {
    font-size: 1.25rem;
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
}

/* Text label */
.bottom-nav-item span {
    font-size: 0.7rem;
    font-weight: 500;
    transition: all 0.3s ease;
    letter-spacing: 0.02em;
    position: relative;
    z-index: 2;
}

/* Active state */
.bottom-nav-item.active {
    color: #fff;
}

.bottom-nav-item.active .nav-icon-container {
    background: linear-gradient(135deg, rgba(58, 123, 213, 0.3), rgba(0, 210, 255, 0.3));
    border-radius: 50%;
    transform: translateY(-8px);
    box-shadow: 0 4px 12px rgba(0, 210, 255, 0.25);
}

.bottom-nav-item.active .nav-icon-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0));
    border-radius: 50% 50% 0 0;
    pointer-events: none;
}

.bottom-nav-item.active i {
    color: #fff;
    text-shadow: 0 0 10px rgba(0, 210, 255, 0.7);
}

.bottom-nav-item.active span {
    transform: translateY(-2px);
    text-shadow: 0 0 8px rgba(0, 210, 255, 0.5);
}

/* Hover state */
.bottom-nav-item:hover {
    color: #fff;
}

.bottom-nav-item:hover .nav-icon-container {
    transform: translateY(-4px);
    background: rgba(255, 255, 255, 0.1);
}

/* Ripple effect on click */
.bottom-nav-item::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%, -50%);
    transform-origin: 50% 50%;
}

.bottom-nav-item:active::after {
    animation: ripple 0.6s ease-out;
}

@keyframes ripple {
    0% {
        transform: scale(0, 0);
        opacity: 0.5;
    }
    100% {
        transform: scale(20, 20);
        opacity: 0;
    }
}

/* Mobile menu trigger special styling */
.mobile-menu-trigger .nav-icon-container {
    background: linear-gradient(135deg, #3a7bd5, #00d2ff);
    border-radius: 50%;
    box-shadow: 0 4px 12px rgba(0, 210, 255, 0.4);
    transform: translateY(-4px);
    position: relative;
    overflow: hidden;
}

.mobile-menu-trigger .nav-icon-container::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.1) 50%,
        rgba(255, 255, 255, 0) 100%);
    animation: shine 3s infinite linear;
    pointer-events: none;
    z-index: 1;
}

.mobile-menu-trigger .nav-icon-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0));
    border-radius: 50% 50% 0 0;
    pointer-events: none;
    z-index: 2;
}

.mobile-menu-trigger i {
    color: #fff;
    position: relative;
    z-index: 3;
}

.mobile-menu-trigger.active .nav-icon-container {
    background: linear-gradient(135deg, #00d2ff, #3a7bd5);
    transform: translateY(-8px) rotate(90deg);
}

@keyframes shine {
    0% {
        transform: translateX(-100%) rotate(45deg);
    }
    100% {
        transform: translateX(100%) rotate(45deg);
    }
}

/* Show bottom navigation only on mobile */
@media (max-width: 991.98px) {
    .enterprise-bottom-nav {
        display: block;
    }

    /* Add padding to body to account for bottom nav */
    body {
        padding-bottom: 65px;
    }

    /* Adjust footer padding */
    .enterprise-footer {
        padding-bottom: 65px;
    }
}

/* Landscape mode adjustments */
@media (max-width: 767.98px) and (orientation: landscape) {
    .enterprise-bottom-nav {
        height: 55px;
    }

    .nav-icon-container {
        width: 30px;
        height: 30px;
        margin-bottom: 2px;
    }

    .bottom-nav-item i {
        font-size: 1rem;
    }

    .bottom-nav-item span {
        font-size: 0.65rem;
    }

    body {
        padding-bottom: 55px;
    }

    .enterprise-footer {
        padding-bottom: 55px;
    }
}

/* Mobile Menu Enhancements */
.mobile-menu {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    width: 85%;
    max-width: 360px;
    background: linear-gradient(135deg, #1a1f36, #121628);
    box-shadow: -5px 0 25px rgba(0, 0, 0, 0.4);
    border-left: 1px solid rgba(58, 123, 213, 0.25);
    z-index: 2000;
    overflow-y: auto;
    transform: translateX(100%);
    transition: transform 0.7s cubic-bezier(0.19, 1, 0.22, 1);
    border-top-left-radius: 16px;
    border-bottom-left-radius: 16px;
    opacity: 0;
}

.mobile-menu.active {
    transform: translateX(0);
    opacity: 1;
}

.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    z-index: 1999;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
}

.mobile-menu-overlay.active {
    opacity: 1;
    pointer-events: auto;
}

.mobile-menu-header {
    background: linear-gradient(135deg, #3a7bd5, #00d2ff);
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.mobile-menu-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.1) 50%,
        rgba(255, 255, 255, 0) 100%);
    animation: shine 6s infinite linear;
    pointer-events: none;
}

.mobile-logo {
    color: #fff;
    font-weight: 700;
    display: flex;
    align-items: center;
    position: relative;
    z-index: 2;
}

.mobile-logo i {
    margin-right: 0.75rem;
    font-size: 1.5rem;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

/* Mobile menu close button - removed */

.mobile-menu-section-title {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 1.25rem 1.5rem 0.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
    background: rgba(0, 0, 0, 0.1);
    margin: 0;
}

.mobile-menu-item {
    color: rgba(255, 255, 255, 0.85);
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    text-decoration: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.mobile-menu-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 3px;
    background: linear-gradient(to bottom, #3a7bd5, #00d2ff);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.mobile-menu-item i {
    margin-right: 1.25rem;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.7);
    transition: all 0.3s ease;
    font-size: 1.1rem;
    background: rgba(58, 123, 213, 0.1);
    border-radius: 8px;
    padding: 0.5rem;
}

/* Grid layout for menu sections */
.mobile-menu-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    padding: 0.75rem;
}

.mobile-menu-grid .mobile-menu-item {
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 1rem;
    flex-direction: column;
    text-align: center;
    height: 100%;
    background: rgba(0, 0, 0, 0.1);
}

.mobile-menu-grid .mobile-menu-item i {
    margin: 0 auto 0.5rem;
    width: 40px;
    height: 40px;
    font-size: 1.25rem;
    border-radius: 10px;
}

.mobile-menu-grid .mobile-menu-item::before {
    width: 100%;
    height: 3px;
    top: auto;
    bottom: 0;
    background: linear-gradient(to right, #3a7bd5, #00d2ff);
}

.mobile-menu-item:hover {
    background-color: rgba(58, 123, 213, 0.15);
    color: #fff;
}

.mobile-menu-item:hover::before {
    opacity: 1;
}

.mobile-menu-item:hover i {
    color: #00d2ff;
    transform: translateX(3px);
}

.mobile-menu-item:active {
    background-color: rgba(58, 123, 213, 0.25);
}

.mobile-menu-item.active:not(.has-submenu) {
    background-color: rgba(58, 123, 213, 0.2);
    color: #fff;
}

.mobile-menu-item.active:not(.has-submenu)::before {
    opacity: 1;
}

.mobile-menu-item.active:not(.has-submenu) i {
    color: #00d2ff;
}

.mobile-user-info {
    display: flex;
    align-items: center;
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
    background: rgba(0, 0, 0, 0.15);
}

.user-avatar {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, #3a7bd5, #00d2ff);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    margin-right: 1rem;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
}

.user-avatar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0));
    border-radius: 50% 50% 0 0;
}

.user-avatar i {
    font-size: 1.25rem;
    position: relative;
    z-index: 2;
}

.mobile-user-name {
    color: #fff;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.mobile-user-role {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.8rem;
    background: rgba(58, 123, 213, 0.2);
    padding: 0.2rem 0.5rem;
    border-radius: 3px;
    display: inline-block;
}

.mobile-menu-divider {
    height: 1px;
    background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
    margin: 0.75rem 1.5rem;
}

/* Submenu styles */
.mobile-menu-item.has-submenu {
    position: relative;
}

.mobile-menu-item.has-submenu::after {
    content: '\f107';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    right: 1.5rem;
    top: 50%;
    transform: translateY(-50%);
    transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
    color: rgba(255, 255, 255, 0.6);
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.mobile-menu-item.has-submenu.active {
    background: rgba(58, 123, 213, 0.15);
    border-bottom: none;
    border-radius: 8px 8px 0 0;
}

.mobile-menu-item.has-submenu.active::before {
    opacity: 1;
}

.mobile-menu-item.has-submenu.active::after {
    transform: translateY(-50%) rotate(180deg);
    color: #00d2ff;
    background: rgba(0, 210, 255, 0.1);
}

.mobile-submenu {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.7s cubic-bezier(0.19, 1, 0.22, 1), opacity 0.3s ease;
    background: rgba(0, 0, 0, 0.15);
    border-radius: 0 0 8px 8px;
    margin: 0 0.75rem 0.75rem;
    box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.1);
    border-left: 1px solid rgba(58, 123, 213, 0.2);
    border-right: 1px solid rgba(58, 123, 213, 0.2);
    border-bottom: 1px solid rgba(58, 123, 213, 0.2);
    opacity: 0;
}

.mobile-menu-item.has-submenu.active + .mobile-submenu {
    opacity: 1;
    max-height: 800px; /* Increased to accommodate more content */
    transition: max-height 0.7s cubic-bezier(0.19, 1, 0.22, 1), opacity 0.3s ease;
}

.submenu-item {
    padding: 0.85rem 1.5rem 0.85rem 3rem;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
    position: relative;
}

.submenu-item::before {
    content: '';
    position: absolute;
    left: 1.5rem;
    top: 50%;
    width: 5px;
    height: 5px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-50%);
    transition: all 0.3s ease;
}

.submenu-item:last-child {
    border-bottom: none;
}

.submenu-item i {
    margin-right: 0.75rem;
    width: 20px;
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.submenu-item:hover {
    background-color: rgba(58, 123, 213, 0.2);
    color: #fff;
    padding-left: 3.25rem;
}

.submenu-item:hover::before {
    background: #00d2ff;
    box-shadow: 0 0 8px rgba(0, 210, 255, 0.5);
}

.submenu-item:hover i {
    color: #00d2ff;
    transform: scale(1.1);
}

.submenu-item:active {
    background-color: rgba(58, 123, 213, 0.3);
}

.submenu-item.active {
    background-color: rgba(58, 123, 213, 0.2);
    color: #fff;
}

.submenu-item.active i {
    color: #00d2ff;
}

.submenu-item.active::before {
    background: #00d2ff;
    box-shadow: 0 0 8px rgba(0, 210, 255, 0.5);
}

/* Menu open body style */
body.menu-open {
    overflow: hidden;
}

/* Ripple effects */
.menu-ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.25);
    transform: scale(0);
    pointer-events: none;
    opacity: 0;
    z-index: 0;
}

.menu-ripple.active {
    animation: menu-ripple 0.5s ease-out;
}

@keyframes menu-ripple {
    0% {
        transform: scale(0);
        opacity: 0.5;
    }
    100% {
        transform: scale(1);
        opacity: 0;
    }
}

.nav-ripple {
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0) 70%);
    transform: scale(0);
    pointer-events: none;
    opacity: 0;
    z-index: 0;
}

.nav-ripple.active {
    animation: nav-ripple 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes nav-ripple {
    0% {
        transform: scale(0);
        opacity: 0.5;
    }
    100% {
        transform: scale(1);
        opacity: 0;
    }
}

.menu-trigger-ripple {
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(0, 210, 255, 0.4) 0%, rgba(0, 210, 255, 0) 70%);
    transform: scale(0);
    pointer-events: none;
    opacity: 0;
    z-index: 0;
}

.menu-trigger-ripple.active {
    animation: trigger-ripple 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes trigger-ripple {
    0% {
        transform: scale(0);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 0;
    }
}
