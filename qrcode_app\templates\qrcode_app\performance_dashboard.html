{% extends "base.html" %}
{% load static %}

{% block title %}Performance Dashboard - Enterprise QR{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'qrcode_app/css/performance-dashboard.css' %}">
<style>
    /* Ultra-Premium Performance Dashboard Styling */
    body {
        background:
            linear-gradient(135deg, #000000 0%, #1a1a2e 15%, #16213e 30%, #0f3460 50%, #533483 75%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        position: relative;
        overflow-x: hidden;
    }

    /* Premium animated background with performance patterns */
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 5% 95%, rgba(102, 126, 234, 0.6) 0%, transparent 30%),
            radial-gradient(circle at 95% 5%, rgba(255, 255, 255, 0.25) 0%, transparent 25%),
            radial-gradient(circle at 25% 70%, rgba(118, 75, 162, 0.5) 0%, transparent 35%),
            radial-gradient(circle at 75% 85%, rgba(83, 52, 131, 0.4) 0%, transparent 20%),
            radial-gradient(circle at 60% 40%, rgba(102, 126, 234, 0.3) 0%, transparent 40%),
            radial-gradient(circle at 40% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 30%);
        z-index: -1;
        animation: performanceFloat 35s ease-in-out infinite;
    }

    /* Corporate data visualization pattern overlay */
    body::after {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image:
            linear-gradient(45deg, rgba(255, 255, 255, 0.02) 25%, transparent 25%),
            linear-gradient(-45deg, rgba(255, 255, 255, 0.02) 25%, transparent 25%),
            linear-gradient(45deg, transparent 75%, rgba(255, 255, 255, 0.02) 75%),
            linear-gradient(-45deg, transparent 75%, rgba(255, 255, 255, 0.02) 75%);
        background-size: 80px 80px;
        background-position: 0 0, 0 40px, 40px -40px, -40px 0px;
        z-index: -1;
        opacity: 0.7;
        animation: dataVisualizationPulse 15s ease-in-out infinite;
    }

    @keyframes performanceFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
        16% { transform: translateY(-25px) rotate(1.5deg) scale(1.04); }
        33% { transform: translateY(15px) rotate(-0.8deg) scale(0.96); }
        50% { transform: translateY(-20px) rotate(1.2deg) scale(1.03); }
        66% { transform: translateY(10px) rotate(-1deg) scale(0.98); }
        83% { transform: translateY(-15px) rotate(0.5deg) scale(1.01); }
    }

    @keyframes dataVisualizationPulse {
        0%, 100% { opacity: 0.5; transform: scale(1) rotate(0deg); }
        50% { opacity: 0.9; transform: scale(1.02) rotate(1deg); }
    }

    /* Premium Container */
    .container {
        max-width: 1600px;
        padding: 4rem 2rem;
        position: relative;
        z-index: 1;
    }

    /* Ultra-Premium Page Title */
    h1 {
        color: white;
        font-weight: 900;
        font-size: 3.2rem;
        text-shadow: 0 10px 40px rgba(0, 0, 0, 0.6);
        margin-bottom: 1.5rem;
        background: linear-gradient(135deg, #ffffff 0%, #e0e0e0 30%, #ffffff 70%, #f0f0f0 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        position: relative;
        animation: dashboardTitleGlow 5s ease-in-out infinite;
    }

    h1::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.4) 0%, rgba(118, 75, 162, 0.4) 100%);
        -webkit-background-clip: text;
        background-clip: text;
        z-index: -1;
        animation: dashboardTitleShimmer 8s ease-in-out infinite;
    }

    h1 i {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #ffffff 70%, #667eea 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.5));
        animation: dashboardIconSpin 10s ease-in-out infinite;
    }

    @keyframes dashboardTitleGlow {
        0%, 100% { text-shadow: 0 10px 40px rgba(0, 0, 0, 0.6); }
        50% { text-shadow: 0 10px 40px rgba(102, 126, 234, 0.4), 0 0 80px rgba(255, 255, 255, 0.3); }
    }

    @keyframes dashboardTitleShimmer {
        0%, 100% { opacity: 0.4; }
        50% { opacity: 0.8; }
    }

    @keyframes dashboardIconSpin {
        0%, 100% { transform: rotate(0deg) scale(1); }
        25% { transform: rotate(10deg) scale(1.15); }
        50% { transform: rotate(0deg) scale(1.1); }
        75% { transform: rotate(-5deg) scale(1.05); }
    }

    /* Premium Time Period Buttons */
    .btn-group .btn {
        border-radius: 16px;
        padding: 0.8rem 1.8rem;
        font-weight: 700;
        font-size: 0.9rem;
        letter-spacing: 0.8px;
        transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        text-transform: uppercase;
        border: 2px solid rgba(255, 255, 255, 0.3);
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
        color: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(20px);
        position: relative;
        overflow: hidden;
        margin: 0 0.3rem;
    }

    .btn-group .btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
            transparent 0%,
            rgba(255, 255, 255, 0.3) 50%,
            transparent 100%);
        transition: left 0.8s ease;
    }

    .btn-group .btn:hover::before {
        left: 100%;
    }

    .btn-group .btn.active,
    .btn-group .btn:hover {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        transform: translateY(-3px) scale(1.05);
        box-shadow:
            0 15px 40px rgba(102, 126, 234, 0.4),
            0 8px 25px rgba(0, 0, 0, 0.2);
        border-color: rgba(255, 255, 255, 0.5);
    }

    /* Ultra-Premium Stats Cards with Glossy Effects */
    .card {
        border-radius: 32px;
        overflow: hidden;
        transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        box-shadow:
            0 35px 70px rgba(0, 0, 0, 0.4),
            0 25px 50px rgba(0, 0, 0, 0.3),
            inset 0 3px 0 rgba(255, 255, 255, 0.5),
            inset 0 -3px 0 rgba(0, 0, 0, 0.15);
        border: 3px solid rgba(255, 255, 255, 0.35);
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.99) 0%,
            rgba(248, 249, 250, 0.96) 25%,
            rgba(255, 255, 255, 0.99) 50%,
            rgba(240, 242, 247, 0.96) 75%,
            rgba(255, 255, 255, 0.99) 100%);
        backdrop-filter: blur(50px);
        position: relative;
        animation: statsCardEntry 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }

    .card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg,
            rgba(102, 126, 234, 0.15) 0%,
            rgba(255, 255, 255, 0.2) 25%,
            rgba(118, 75, 162, 0.15) 50%,
            rgba(255, 255, 255, 0.15) 75%,
            rgba(102, 126, 234, 0.1) 100%);
        z-index: 0;
        pointer-events: none;
        border-radius: 32px;
    }

    .card::after {
        content: '';
        position: absolute;
        top: -3px;
        left: -3px;
        width: calc(100% + 6px);
        height: calc(100% + 6px);
        background: conic-gradient(from 0deg,
            rgba(102, 126, 234, 0.5) 0deg,
            rgba(255, 255, 255, 0.4) 90deg,
            rgba(118, 75, 162, 0.5) 180deg,
            rgba(255, 255, 255, 0.3) 270deg,
            rgba(102, 126, 234, 0.5) 360deg);
        border-radius: 35px;
        z-index: -1;
        opacity: 0;
        transition: opacity 0.8s ease;
        animation: statsCardBorderGlow 10s linear infinite;
    }

    .card:hover {
        box-shadow:
            0 60px 120px rgba(0, 0, 0, 0.5),
            0 40px 80px rgba(0, 0, 0, 0.35),
            0 0 100px rgba(102, 126, 234, 0.4),
            inset 0 4px 0 rgba(255, 255, 255, 0.6),
            inset 0 -4px 0 rgba(0, 0, 0, 0.2);
        transform: translateY(-20px) scale(1.04) rotateX(8deg);
        border-color: rgba(255, 255, 255, 0.6);
    }

    .card:hover::after {
        opacity: 1;
    }

    @keyframes statsCardEntry {
        0% {
            opacity: 0;
            transform: translateY(80px) scale(0.8) rotateX(30deg);
            filter: blur(30px);
        }
        100% {
            opacity: 1;
            transform: translateY(0) scale(1) rotateX(0deg);
            filter: blur(0px);
        }
    }

    @keyframes statsCardBorderGlow {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Premium Card Headers */
    .card-header {
        padding: 3rem 3.5rem;
        border-bottom: none;
        position: relative;
        overflow: hidden;
        background: linear-gradient(135deg,
            #000000 0%,
            #1a1a2e 15%,
            #16213e 30%,
            #0f3460 50%,
            #533483 75%,
            #764ba2 100%);
        border-radius: 32px 32px 0 0;
    }

    .card-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.25) 0%,
            rgba(255, 255, 255, 0.12) 30%,
            rgba(255, 255, 255, 0.18) 70%,
            rgba(255, 255, 255, 0.1) 100%);
        z-index: 0;
    }

    .card-header::after {
        content: '';
        position: absolute;
        top: -150%;
        left: -150%;
        width: 400%;
        height: 400%;
        background: conic-gradient(from 0deg,
            rgba(102, 126, 234, 0.5) 0deg,
            rgba(255, 255, 255, 0.4) 90deg,
            rgba(118, 75, 162, 0.5) 180deg,
            rgba(255, 255, 255, 0.3) 270deg,
            rgba(102, 126, 234, 0.5) 360deg);
        z-index: 1;
        animation: headerDashboardGlossyRotate 12s linear infinite;
        opacity: 0.7;
    }

    @keyframes headerDashboardGlossyRotate {
        0% { transform: rotate(0deg); opacity: 0.5; }
        50% { opacity: 0.9; }
        100% { transform: rotate(360deg); opacity: 0.5; }
    }

    .card-header h5 {
        position: relative;
        z-index: 3;
        margin: 0;
        color: white;
        font-weight: 900;
        font-size: 1.4rem;
        text-shadow:
            0 6px 25px rgba(0, 0, 0, 0.6),
            0 0 50px rgba(255, 255, 255, 0.25);
        letter-spacing: 1.2px;
        text-transform: uppercase;
        background: linear-gradient(135deg, #ffffff 0%, #e0e0e0 50%, #ffffff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        animation: headerDashboardTextGlow 4s ease-in-out infinite;
    }

    @keyframes headerDashboardTextGlow {
        0%, 100% {
            text-shadow: 0 6px 25px rgba(0, 0, 0, 0.6), 0 0 50px rgba(255, 255, 255, 0.25);
        }
        50% {
            text-shadow: 0 6px 25px rgba(0, 0, 0, 0.6), 0 0 70px rgba(102, 126, 234, 0.5);
        }
    }

    .card-body {
        padding: 3.5rem;
        position: relative;
        z-index: 2;
        background: rgba(255, 255, 255, 0.99);
    }

    /* Premium Stat Icons with Unique Animations */
    .stat-icon {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.8rem;
        color: white;
        position: relative;
        overflow: hidden;
        transition: all 0.6s ease;
        box-shadow:
            0 15px 35px rgba(0, 0, 0, 0.3),
            inset 0 2px 0 rgba(255, 255, 255, 0.4);
    }

    .stat-icon::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: conic-gradient(from 0deg,
            rgba(255, 255, 255, 0.4) 0deg,
            transparent 90deg,
            rgba(255, 255, 255, 0.4) 180deg,
            transparent 270deg,
            rgba(255, 255, 255, 0.4) 360deg);
        animation: statIconGlow 3s linear infinite;
    }

    .stat-icon:hover {
        transform: scale(1.15) rotate(10deg);
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.4),
            inset 0 3px 0 rgba(255, 255, 255, 0.5);
    }

    @keyframes statIconGlow {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .bg-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    }

    .bg-success {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
    }

    .bg-info {
        background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%) !important;
    }

    /* Premium Display Numbers */
    .display-4 {
        font-weight: 900;
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        animation: numberPulse 3s ease-in-out infinite;
    }

    @keyframes numberPulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    /* Premium Progress Bars */
    .progress {
        background: linear-gradient(135deg, #e9ecef 0%, #f8f9fa 100%);
        border-radius: 10px;
        box-shadow:
            inset 0 2px 8px rgba(0, 0, 0, 0.1),
            0 2px 8px rgba(0, 0, 0, 0.05);
        overflow: hidden;
        position: relative;
    }

    .progress::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
            transparent 0%,
            rgba(255, 255, 255, 0.3) 50%,
            transparent 100%);
        animation: progressShimmer 2s ease-in-out infinite;
    }

    @keyframes progressShimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    .progress-bar {
        border-radius: 10px;
        position: relative;
        overflow: hidden;
        box-shadow:
            0 2px 8px rgba(0, 0, 0, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }

    .progress-bar::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 50%;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.4) 0%,
            rgba(255, 255, 255, 0.1) 100%);
        border-radius: 10px 10px 0 0;
    }

    /* Premium Badges */
    .badge {
        padding: 0.6rem 1.4rem;
        border-radius: 16px;
        font-weight: 700;
        letter-spacing: 0.8px;
        text-transform: uppercase;
        font-size: 0.75rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        position: relative;
        overflow: hidden;
    }

    .badge::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
            transparent 0%,
            rgba(255, 255, 255, 0.3) 50%,
            transparent 100%);
        animation: badgeShimmer 3s ease-in-out infinite;
    }

    @keyframes badgeShimmer {
        0%, 100% { left: -100%; }
        50% { left: 100%; }
    }

    /* Premium Tables */
    .table {
        background: rgba(255, 255, 255, 0.98);
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    }

    .table th {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 50%, #f8f9fa 100%);
        font-weight: 800;
        text-transform: uppercase;
        letter-spacing: 1px;
        font-size: 0.85rem;
        color: #2c3e50;
        border: none;
        padding: 1.5rem 2rem;
        position: relative;
    }

    .table th::before {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 3px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    }

    .table td {
        padding: 1.5rem 2rem;
        border: none;
        border-bottom: 1px solid rgba(0, 0, 0, 0.08);
        vertical-align: middle;
        transition: all 0.3s ease;
    }

    .table tbody tr:hover {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
        transform: translateX(5px);
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="h3">
                <i class="fas fa-tachometer-alt me-2"></i>Performance Dashboard
            </h1>
        </div>
        <div class="col-md-4 text-md-end">
            <div class="btn-group">
                <button class="btn btn-outline-primary active" id="dailyBtn">Daily</button>
                <button class="btn btn-outline-primary" id="weeklyBtn">Weekly</button>
                <button class="btn btn-outline-primary" id="monthlyBtn">Monthly</button>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Stats Cards -->
        <div class="col-md-4 mb-4">
            <div class="card shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="card-title mb-0">QR Codes</h5>
                        <div class="stat-icon bg-primary">
                            <i class="fas fa-qrcode"></i>
                        </div>
                    </div>
                    <h2 class="display-4 mb-0">{{ qr_codes_count }}</h2>
                    <p class="text-muted">Total QR codes generated</p>
                    <div class="progress" style="height: 5px;">
                        <div class="progress-bar bg-primary" role="progressbar" style="width: 75%;" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4 mb-4">
            <div class="card shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="card-title mb-0">Scans</h5>
                        <div class="stat-icon bg-success">
                            <i class="fas fa-eye"></i>
                        </div>
                    </div>
                    <h2 class="display-4 mb-0">1,254</h2>
                    <p class="text-muted">Total QR code scans</p>
                    <div class="progress" style="height: 5px;">
                        <div class="progress-bar bg-success" role="progressbar" style="width: 65%;" aria-valuenow="65" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4 mb-4">
            <div class="card shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="card-title mb-0">Batches</h5>
                        <div class="stat-icon bg-info">
                            <i class="fas fa-layer-group"></i>
                        </div>
                    </div>
                    <h2 class="display-4 mb-0">{{ batches_count }}</h2>
                    <p class="text-muted">Total batch processes</p>
                    <div class="progress" style="height: 5px;">
                        <div class="progress-bar bg-info" role="progressbar" style="width: 40%;" aria-valuenow="40" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Charts -->
        <div class="col-lg-8 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">Scan Activity</h5>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-secondary active">Day</button>
                            <button class="btn btn-outline-secondary">Week</button>
                            <button class="btn btn-outline-secondary">Month</button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <canvas id="scanActivityChart" height="300"></canvas>
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">QR Code Types</h5>
                </div>
                <div class="card-body">
                    <canvas id="qrTypeChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent QR Codes -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">Recent QR Codes</h5>
                        <a href="{% url 'qr_code_list' %}" class="btn btn-sm btn-outline-primary">View All</a>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Name</th>
                                    <th>Type</th>
                                    <th>Created</th>
                                    <th>Scans</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for qr_code in recent_qr_codes %}
                                <tr>
                                    <td>
                                        <a href="{% url 'qr_code_detail' qr_code.pk %}" class="text-decoration-none">
                                            {{ qr_code.name }}
                                        </a>
                                    </td>
                                    <td><span class="badge bg-info">{{ qr_code.get_qr_type_display }}</span></td>
                                    <td>{{ qr_code.created_at|date:"M d, Y" }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <span class="me-2">{{ qr_code.id|add:42 }}</span>
                                            <div class="progress flex-grow-1" style="height: 5px;">
                                                <div class="progress-bar bg-success" role="progressbar" style="width: {{ qr_code.id|add:20 }}%;" aria-valuenow="{{ qr_code.id|add:20 }}" aria-valuemin="0" aria-valuemax="100"></div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center py-3">No QR codes found</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Geographic Distribution -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">Geographic Distribution</h5>
                </div>
                <div class="card-body">
                    <div id="worldMap" style="height: 300px;"></div>
                </div>
                <div class="card-footer bg-white">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="fw-bold">United States</div>
                            <div class="text-muted">42%</div>
                        </div>
                        <div class="col-4">
                            <div class="fw-bold">Europe</div>
                            <div class="text-muted">28%</div>
                        </div>
                        <div class="col-4">
                            <div class="fw-bold">Asia</div>
                            <div class="text-muted">18%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Device Distribution -->
        <div class="col-lg-4 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">Device Distribution</h5>
                </div>
                <div class="card-body">
                    <canvas id="deviceChart" height="200"></canvas>
                </div>
                <div class="card-footer bg-white">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="fw-bold">Mobile</div>
                            <div class="text-muted">68%</div>
                        </div>
                        <div class="col-4">
                            <div class="fw-bold">Desktop</div>
                            <div class="text-muted">24%</div>
                        </div>
                        <div class="col-4">
                            <div class="fw-bold">Tablet</div>
                            <div class="text-muted">8%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Browser Distribution -->
        <div class="col-lg-4 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">Browser Distribution</h5>
                </div>
                <div class="card-body">
                    <canvas id="browserChart" height="200"></canvas>
                </div>
                <div class="card-footer bg-white">
                    <div class="row text-center">
                        <div class="col-3">
                            <div class="fw-bold">Chrome</div>
                            <div class="text-muted">52%</div>
                        </div>
                        <div class="col-3">
                            <div class="fw-bold">Safari</div>
                            <div class="text-muted">28%</div>
                        </div>
                        <div class="col-3">
                            <div class="fw-bold">Firefox</div>
                            <div class="text-muted">12%</div>
                        </div>
                        <div class="col-3">
                            <div class="fw-bold">Other</div>
                            <div class="text-muted">8%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Conversion Rate -->
        <div class="col-lg-4 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">Conversion Rate</h5>
                </div>
                <div class="card-body text-center">
                    <div class="conversion-gauge">
                        <canvas id="conversionGauge" height="200"></canvas>
                        <div class="conversion-rate">
                            <span class="display-4">3.8</span>
                            <span class="text-muted">%</span>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span class="text-success"><i class="fas fa-arrow-up me-1"></i>0.5%</span>
                            <span class="text-muted ms-1">from last week</span>
                        </div>
                        <a href="#" class="btn btn-sm btn-outline-primary">Details</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="{% static 'qrcode_app/js/performance-dashboard.js' %}"></script>
{% endblock %}
