{% extends 'base.html' %}
{% load static %}

{% block title %}{{ user_obj.username }} | User Management{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'users/css/user_management.css' %}">
{% endblock %}

{% block content %}
<div class="user-detail-container">
    <div class="user-detail-header">
        <div class="d-flex justify-content-between align-items-center">
            <h1 class="section-title">User Details</h1>
            <div class="user-detail-actions">
                <a href="{% url 'users:user_list' %}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Users
                </a>
                <a href="{% url 'users:user_edit' user_id=user_obj.id %}" class="btn btn-primary">
                    <i class="fas fa-edit me-2"></i>Edit User
                </a>
                {% if user_obj != request.user %}
                <a href="{% url 'users:user_delete' user_id=user_obj.id %}" class="btn btn-danger">
                    <i class="fas fa-trash me-2"></i>Delete User
                </a>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="row">
        <!-- User Profile Card -->
        <div class="col-md-4">
            <div class="user-profile-card">
                <div class="user-profile-header">
                    <div class="user-profile-avatar">
                        {% if profile.profile_image %}
                        <img src="{{ profile.profile_image.url }}" alt="{{ user_obj.username }}">
                        {% else %}
                        <div class="default-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        {% endif %}
                    </div>
                    <div class="user-profile-name">
                        <h3>{{ user_obj.get_full_name|default:user_obj.username }}</h3>
                        <span class="badge bg-{{ profile.role }}">{{ profile.get_role_display }}</span>
                    </div>
                </div>
                <div class="user-profile-body">
                    <div class="user-profile-info">
                        <div class="info-item">
                            <div class="info-label">Username</div>
                            <div class="info-value">{{ user_obj.username }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Email</div>
                            <div class="info-value">{{ user_obj.email|default:"Not provided" }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Status</div>
                            <div class="info-value">
                                {% if user_obj.is_active %}
                                <span class="badge bg-success">Active</span>
                                {% else %}
                                <span class="badge bg-danger">Inactive</span>
                                {% endif %}

                                {% if user_obj != request.user %}
                                <a href="{% url 'users:user_toggle_active' user_id=user_obj.id %}" class="btn btn-sm btn-outline-primary ms-2">
                                    {% if user_obj.is_active %}Deactivate{% else %}Activate{% endif %}
                                </a>
                                {% endif %}
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Date Joined</div>
                            <div class="info-value">{{ user_obj.date_joined|date:"F d, Y" }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Last Login</div>
                            <div class="info-value">{{ user_obj.last_login|date:"F d, Y H:i"|default:"Never" }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Details -->
        <div class="col-md-8">
            <div class="user-details-card">
                <div class="card-header">
                    <h4>User Information</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="detail-group">
                                <label>First Name</label>
                                <p>{{ user_obj.first_name|default:"Not provided" }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="detail-group">
                                <label>Last Name</label>
                                <p>{{ user_obj.last_name|default:"Not provided" }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="detail-group">
                                <label>Phone Number</label>
                                <p>{{ profile.phone|default:"Not provided" }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="detail-group">
                                <label>Company</label>
                                <p>{{ profile.company|default:"Not provided" }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="detail-group">
                                <label>Job Title</label>
                                <p>{{ profile.job_title|default:"Not provided" }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="detail-group">
                                <label>User Type</label>
                                <p>
                                    {% if user_obj.is_superuser %}
                                    <span class="badge bg-danger">Superuser</span>
                                    {% elif user_obj.is_staff %}
                                    <span class="badge bg-warning">Staff</span>
                                    {% else %}
                                    <span class="badge bg-info">Regular User</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div class="detail-group">
                                <label>Address</label>
                                <p>{{ profile.address|default:"Not provided"|linebreaks }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div class="detail-group">
                                <label>Bio</label>
                                <p>{{ profile.bio|default:"Not provided"|linebreaks }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Permissions -->
            <div class="user-permissions-card mt-4">
                <div class="card-header">
                    <h4>Permissions & Groups</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="detail-group">
                                <label>User Permissions</label>
                                <div class="permissions-list">
                                    {% if user_obj.user_permissions.all %}
                                    <ul>
                                        {% for permission in user_obj.user_permissions.all %}
                                        <li>{{ permission.name }}</li>
                                        {% endfor %}
                                    </ul>
                                    {% else %}
                                    <p>No specific permissions assigned.</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="detail-group">
                                <label>Groups</label>
                                <div class="groups-list">
                                    {% if groups %}
                                    <ul>
                                        {% for group in groups %}
                                        <li>{{ group.name }}</li>
                                        {% endfor %}
                                    </ul>
                                    {% else %}
                                    <p>Not a member of any groups.</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'users/js/user_management.js' %}"></script>
{% endblock %}
