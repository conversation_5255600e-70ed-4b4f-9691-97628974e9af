// Enterprise Analytics Dashboard JavaScript

// Initialize charts and components when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize date range picker
    initDateRangePicker();

    // Initialize performance chart
    initPerformanceChart();

    // Initialize campaign comparison chart
    initCampaignComparisonChart();

    // Setup tab navigation
    setupTabNavigation();

    // Setup metric toggle buttons
    setupMetricToggle();

    // Setup sort dropdown
    setupSortDropdown();

    // Setup export button
    setupExportButton();

    // Setup campaign metric selector
    setupCampaignMetricSelector();
});

// Initialize date range picker
function initDateRangePicker() {
    $('#daterange-btn').daterangepicker({
        ranges: {
            'Today': [moment(), moment()],
            'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
            'Last 7 Days': [moment().subtract(6, 'days'), moment()],
            'Last 30 Days': [moment().subtract(29, 'days'), moment()],
            'This Month': [moment().startOf('month'), moment().endOf('month')],
            'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
        },
        startDate: moment().subtract(29, 'days'),
        endDate: moment(),
        alwaysShowCalendars: true,
        opens: 'left'
    }, function(start, end, label) {
        // Update the button text
        $('#daterange-btn span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));

        // Fetch new data based on the selected date range
        fetchAnalyticsData(start.format('YYYY-MM-DD'), end.format('YYYY-MM-DD'));
    });

    // Set initial text
    const start = moment().subtract(29, 'days');
    const end = moment();
    $('#daterange-btn span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
}

// Initialize performance chart
function initPerformanceChart() {
    const ctx = document.getElementById('performanceChart').getContext('2d');

    // Sample data - this would be replaced with actual data from the backend
    const labels = ['Jan 1', 'Jan 2', 'Jan 3', 'Jan 4', 'Jan 5', 'Jan 6', 'Jan 7', 'Jan 8', 'Jan 9', 'Jan 10', 'Jan 11', 'Jan 12', 'Jan 13', 'Jan 14'];
    const impressionsData = [120, 150, 180, 220, 250, 300, 350, 400, 380, 420, 450, 500, 550, 600];
    const clicksData = [10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75];
    const ctrData = impressionsData.map((imp, i) => (clicksData[i] / imp * 100).toFixed(2));

    window.performanceChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [
                {
                    label: 'Impressions',
                    data: impressionsData,
                    borderColor: '#2196f3',
                    backgroundColor: 'rgba(33, 150, 243, 0.1)',
                    borderWidth: 2,
                    tension: 0.4,
                    fill: true,
                    pointBackgroundColor: '#2196f3',
                    pointRadius: 3,
                    pointHoverRadius: 5
                },
                {
                    label: 'Clicks',
                    data: clicksData,
                    borderColor: '#9c27b0',
                    backgroundColor: 'rgba(156, 39, 176, 0.1)',
                    borderWidth: 2,
                    tension: 0.4,
                    fill: true,
                    pointBackgroundColor: '#9c27b0',
                    pointRadius: 3,
                    pointHoverRadius: 5,
                    hidden: true
                },
                {
                    label: 'CTR (%)',
                    data: ctrData,
                    borderColor: '#ff9800',
                    backgroundColor: 'rgba(255, 152, 0, 0.1)',
                    borderWidth: 2,
                    tension: 0.4,
                    fill: true,
                    pointBackgroundColor: '#ff9800',
                    pointRadius: 3,
                    pointHoverRadius: 5,
                    hidden: true
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        boxWidth: 6
                    }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    backgroundColor: 'rgba(255, 255, 255, 0.9)',
                    titleColor: '#1a237e',
                    bodyColor: '#1a237e',
                    borderColor: 'rgba(26, 35, 126, 0.1)',
                    borderWidth: 1,
                    padding: 10,
                    boxPadding: 5,
                    usePointStyle: true,
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            if (context.parsed.y !== null) {
                                if (label.includes('CTR')) {
                                    label += context.parsed.y + '%';
                                } else {
                                    label += context.parsed.y;
                                }
                            }
                            return label;
                        }
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    }
                },
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    }
                }
            },
            interaction: {
                mode: 'index',
                intersect: false
            }
        }
    });
}

// Setup tab navigation
function setupTabNavigation() {
    const navLinks = document.querySelectorAll('.nav-link[data-tab]');
    const tabPanes = document.querySelectorAll('.tab-pane');

    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            // Get the tab ID from data attribute
            const tabId = this.getAttribute('data-tab');

            // Remove active class from all links and add to clicked link
            navLinks.forEach(navLink => {
                navLink.parentElement.classList.remove('active');
            });
            this.parentElement.classList.add('active');

            // Hide all tab panes and show the selected one
            tabPanes.forEach(pane => {
                pane.classList.remove('active');
            });
            document.getElementById(tabId).classList.add('active');

            // Initialize tab-specific charts if needed
            if (tabId === 'audience' && !window.audienceChartsInitialized) {
                initAudienceCharts();
                window.audienceChartsInitialized = true;
            } else if (tabId === 'geo' && !window.geoChartInitialized) {
                initGeoCharts();
                window.geoChartInitialized = true;
            } else if (tabId === 'comparison' && !window.comparisonChartInitialized) {
                initComparisonChart();
                window.comparisonChartInitialized = true;
            } else if (tabId === 'system' && !window.systemChartsInitialized) {
                initSystemCharts();
                window.systemChartsInitialized = true;
            }
        });
    });
}

// Setup metric toggle buttons
function setupMetricToggle() {
    const metricButtons = document.querySelectorAll('[data-metric]');

    metricButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            metricButtons.forEach(btn => {
                btn.classList.remove('active');
            });

            // Add active class to clicked button
            this.classList.add('active');

            // Get the metric to display
            const metric = this.getAttribute('data-metric');

            // Update chart visibility
            updateChartVisibility(metric);
        });
    });
}

// Update chart visibility based on selected metric
function updateChartVisibility(metric) {
    if (!window.performanceChart) return;

    const datasets = window.performanceChart.data.datasets;

    datasets.forEach((dataset, index) => {
        if (dataset.label.toLowerCase().includes(metric.toLowerCase())) {
            dataset.hidden = false;
        } else {
            dataset.hidden = true;
        }
    });

    window.performanceChart.update();
}

// Setup sort dropdown
function setupSortDropdown() {
    const sortOptions = document.querySelectorAll('[data-sort]');

    sortOptions.forEach(option => {
        option.addEventListener('click', function(e) {
            e.preventDefault();

            // Remove active class from all options
            sortOptions.forEach(opt => {
                opt.classList.remove('active');
            });

            // Add active class to clicked option
            this.classList.add('active');

            // Get the sort criteria
            const sortBy = this.getAttribute('data-sort');

            // Update dropdown button text
            document.getElementById('sortDropdown').textContent = 'Sort By: ' + this.textContent;

            // Sort the table
            sortTable(sortBy);
        });
    });
}

// Sort table based on criteria
function sortTable(sortBy) {
    // This would be implemented to sort the table rows based on the selected criteria
    console.log('Sorting table by:', sortBy);
    // In a real implementation, this would reorder the table rows
}

// Setup export button
function setupExportButton() {
    document.getElementById('export-data-btn').addEventListener('click', function() {
        exportAnalyticsData();
    });
}

// Export analytics data
function exportAnalyticsData() {
    // This would be implemented to export the analytics data
    console.log('Exporting analytics data...');
    // In a real implementation, this would trigger a download of the data in CSV or Excel format
    alert('Analytics data export feature will be implemented in the next phase.');
}

// Initialize campaign comparison chart
function initCampaignComparisonChart() {
    const campaignComparisonElement = document.getElementById('campaignComparisonChart');
    if (!campaignComparisonElement) return;

    // Sample data - this would be replaced with actual data from the backend
    const campaignData = {
        labels: ['Campaign 1', 'Campaign 2', 'Campaign 3', 'Campaign 4', 'Campaign 5'],
        impressions: [1200, 1500, 800, 2000, 1300],
        clicks: [120, 150, 80, 200, 130],
        ctr: [10, 10, 10, 10, 10],
        conversions: [24, 30, 16, 40, 26]
    };

    const campaignCtx = campaignComparisonElement.getContext('2d');
    const campaignMetricSelector = document.getElementById('campaign-metric-selector');

    // Default to clicks if not specified
    let selectedCampaignMetric = campaignMetricSelector ? campaignMetricSelector.value : 'clicks';

    // Function to update the chart based on selected metric
    const updateCampaignChart = (metric) => {
        let chartData, chartLabel, chartColor;

        switch(metric) {
            case 'impressions':
                chartData = campaignData.impressions;
                chartLabel = 'Impressions';
                chartColor = 'rgba(75, 192, 192, 0.7)';
                break;
            case 'ctr':
                chartData = campaignData.ctr;
                chartLabel = 'CTR (%)';
                chartColor = 'rgba(153, 102, 255, 0.7)';
                break;
            case 'conversions':
                chartData = campaignData.conversions;
                chartLabel = 'Conversions';
                chartColor = 'rgba(255, 99, 132, 0.7)';
                break;
            default: // clicks
                chartData = campaignData.clicks;
                chartLabel = 'Clicks';
                chartColor = 'rgba(255, 159, 64, 0.7)';
        }

        // Create or update chart
        if (window.campaignChart) {
            window.campaignChart.data.datasets[0].data = chartData;
            window.campaignChart.data.datasets[0].label = chartLabel;
            window.campaignChart.data.datasets[0].backgroundColor = chartColor;
            window.campaignChart.update();
        } else {
            window.campaignChart = new Chart(campaignCtx, {
                type: 'bar',
                data: {
                    labels: campaignData.labels,
                    datasets: [{
                        label: chartLabel,
                        data: chartData,
                        backgroundColor: chartColor,
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                            backgroundColor: 'rgba(255, 255, 255, 0.9)',
                            titleColor: '#1a237e',
                            bodyColor: '#1a237e',
                            borderColor: 'rgba(26, 35, 126, 0.1)',
                            borderWidth: 1,
                            padding: 10,
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.y !== null) {
                                        if (metric === 'ctr') {
                                            label += context.parsed.y + '%';
                                        } else {
                                            label += context.parsed.y;
                                        }
                                    }
                                    return label;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    if (metric === 'ctr') {
                                        return value + '%';
                                    }
                                    return value;
                                }
                            }
                        }
                    }
                }
            });
        }
    };

    // Initialize chart with default metric
    updateCampaignChart(selectedCampaignMetric);

    // Store the update function for later use
    window.updateCampaignChart = updateCampaignChart;
}

// Setup campaign metric selector
function setupCampaignMetricSelector() {
    const campaignMetricSelector = document.getElementById('campaign-metric-selector');
    if (!campaignMetricSelector) return;

    campaignMetricSelector.addEventListener('change', function() {
        if (window.updateCampaignChart) {
            window.updateCampaignChart(this.value);
        }
    });
}

// Initialize audience charts
function initAudienceCharts() {
    // Initialize device chart
    initDeviceChart();

    // Initialize browser chart
    initBrowserChart();
}

// Initialize device chart
function initDeviceChart() {
    const deviceChartElement = document.getElementById('deviceChart');
    if (!deviceChartElement) return;

    // Get device data from the page
    let deviceData;
    try {
        deviceData = JSON.parse(document.getElementById('device_data_json').textContent);
    } catch (e) {
        console.error('Error parsing device data:', e);
        return;
    }

    if (!deviceData || Object.keys(deviceData).length === 0) {
        console.log('No device data available');
        return;
    }

    // Prepare data for chart
    const labels = Object.keys(deviceData);
    const data = Object.values(deviceData);

    // Define colors for each device type
    const backgroundColors = {
        desktop: 'rgba(57, 73, 171, 0.7)',
        mobile: 'rgba(67, 160, 71, 0.7)',
        tablet: 'rgba(251, 140, 0, 0.7)',
        unknown: 'rgba(189, 189, 189, 0.7)'
    };

    // Create colors array based on labels
    const colors = labels.map(label => backgroundColors[label.toLowerCase()] || 'rgba(189, 189, 189, 0.7)');

    // Create the chart
    const ctx = deviceChartElement.getContext('2d');
    window.deviceChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels.map(l => l.charAt(0).toUpperCase() + l.slice(1)),
            datasets: [{
                data: data,
                backgroundColor: colors,
                borderColor: 'white',
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.raw || 0;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = Math.round((value / total) * 100);
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
}

// Initialize browser chart
function initBrowserChart() {
    const browserChartElement = document.getElementById('browserChart');
    if (!browserChartElement) return;

    // Get browser data from the page
    let browserData;
    try {
        browserData = JSON.parse(document.getElementById('browser_data_json').textContent);
    } catch (e) {
        console.error('Error parsing browser data:', e);
        return;
    }

    if (!browserData || Object.keys(browserData).length === 0) {
        console.log('No browser data available');
        return;
    }

    // Prepare data for chart
    const labels = Object.keys(browserData);
    const data = Object.values(browserData);

    // Define colors for browsers
    const backgroundColors = [
        'rgba(57, 73, 171, 0.7)',
        'rgba(67, 160, 71, 0.7)',
        'rgba(251, 140, 0, 0.7)',
        'rgba(229, 57, 53, 0.7)',
        'rgba(156, 39, 176, 0.7)'
    ];

    // Create the chart
    const ctx = browserChartElement.getContext('2d');
    window.browserChart = new Chart(ctx, {
        type: 'pie',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: backgroundColors,
                borderColor: 'white',
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.raw || 0;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = Math.round((value / total) * 100);
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
}

// Initialize geography charts
function initGeoCharts() {
    // Setup view toggle
    setupGeoViewToggle();

    // Initialize location chart
    initLocationChart();

    // Initialize world map
    initWorldMap();

    // Populate geography tables
    populateGeographyTables();
}

// Setup geography view toggle
function setupGeoViewToggle() {
    const viewButtons = document.querySelectorAll('[data-view]');
    const views = document.querySelectorAll('.geo-view');

    if (viewButtons.length === 0) return;

    viewButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons and views
            viewButtons.forEach(b => b.classList.remove('active'));
            views.forEach(v => v.classList.remove('active'));

            // Add active class to clicked button
            this.classList.add('active');

            // Show corresponding view
            const viewId = `geo-${this.getAttribute('data-view')}-view`;
            document.getElementById(viewId).classList.add('active');
        });
    });
}

// Initialize location chart
function initLocationChart() {
    const locationChartElement = document.getElementById('locationChart');
    if (!locationChartElement) return;

    // Get location data from the page
    let locationData;
    try {
        locationData = JSON.parse(document.getElementById('location_data_json').textContent);
    } catch (e) {
        console.error('Error parsing location data:', e);
        return;
    }

    if (!locationData || Object.keys(locationData).length === 0) {
        console.log('No location data available');
        return;
    }

    // Sort data by impressions (descending)
    const sortedData = Object.entries(locationData)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 10); // Limit to top 10 locations

    // Prepare data for chart
    const labels = sortedData.map(item => item[0]);
    const data = sortedData.map(item => item[1]);
    const totalImpressions = data.reduce((a, b) => a + b, 0);

    // Calculate percentages for each location
    const percentages = data.map(value => ((value / totalImpressions) * 100).toFixed(1));

    // Generate gradient colors
    const backgroundColors = data.map((value, index) => {
        // Calculate color intensity based on value
        const intensity = Math.max(0.3, Math.min(0.9, value / Math.max(...data)));
        const hue = 230; // Blue hue
        return `hsla(${hue}, 70%, ${50 + (index * 3)}%, ${intensity})`;
    });

    // Create the chart
    const ctx = locationChartElement.getContext('2d');
    window.locationChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'Impressions by Location',
                data: data,
                backgroundColor: backgroundColors,
                borderColor: 'rgba(57, 73, 171, 0.8)',
                borderWidth: 1,
                borderRadius: 4,
                barPercentage: 0.7,
                categoryPercentage: 0.8
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            indexAxis: 'y',
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(26, 35, 126, 0.9)',
                    titleFont: {
                        size: 14,
                        weight: 'bold'
                    },
                    bodyFont: {
                        size: 13
                    },
                    padding: 12,
                    cornerRadius: 6,
                    callbacks: {
                        label: function(context) {
                            const value = context.raw || 0;
                            const index = context.dataIndex;
                            return [
                                `Impressions: ${value.toLocaleString()}`,
                                `Percentage: ${percentages[index]}%`
                            ];
                        }
                    }
                }
            },
            scales: {
                x: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)',
                        drawBorder: false
                    },
                    ticks: {
                        font: {
                            size: 11
                        },
                        color: '#6c757d'
                    },
                    title: {
                        display: true,
                        text: 'Impressions',
                        font: {
                            size: 13,
                            weight: 'bold'
                        },
                        color: '#495057',
                        padding: {top: 10, bottom: 0}
                    }
                },
                y: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        font: {
                            size: 12
                        },
                        color: '#495057'
                    }
                }
            },
            animation: {
                duration: 2000,
                easing: 'easeOutQuart'
            }
        }
    });

    // Add percentage labels to the right of each bar
    const originalDraw = window.locationChart.draw;
    window.locationChart.draw = function() {
        originalDraw.apply(this, arguments);

        const chart = this;
        const ctx = chart.ctx;
        const meta = chart.getDatasetMeta(0);

        ctx.save();
        ctx.font = '11px Arial';
        ctx.fillStyle = '#6c757d';
        ctx.textAlign = 'left';

        meta.data.forEach((bar, index) => {
            const percentage = percentages[index];
            const barWidth = bar.width;
            const barX = bar.x;
            const barY = bar.y;

            ctx.fillText(`${percentage}%`, barX + barWidth + 5, barY + 4);
        });

        ctx.restore();
    };
}

// Initialize world map
function initWorldMap() {
    const worldMapElement = document.getElementById('world-map');
    if (!worldMapElement) return;

    // Get location data from the page
    let locationData;
    try {
        locationData = JSON.parse(document.getElementById('location_data_json').textContent);
    } catch (e) {
        console.error('Error parsing location data:', e);
        return;
    }

    if (!locationData || Object.keys(locationData).length === 0) {
        console.log('No location data available for map');
        return;
    }

    // Prepare data for jVectorMap
    const mapData = {};
    const totalImpressions = Object.values(locationData).reduce((a, b) => a + b, 0);

    // Process location data to country codes
    Object.entries(locationData).forEach(([location, impressions]) => {
        const countryCode = getCountryCode(location);
        if (countryCode !== 'unknown') {
            // If country already exists, add to its count
            if (mapData[countryCode]) {
                mapData[countryCode] += impressions;
            } else {
                mapData[countryCode] = impressions;
            }
        }
    });

    // Calculate percentages for each country
    const percentageData = {};
    Object.entries(mapData).forEach(([countryCode, impressions]) => {
        percentageData[countryCode] = (impressions / totalImpressions) * 100;
    });

    // Create the map
    $(worldMapElement).vectorMap({
        map: 'world_mill',
        backgroundColor: 'transparent',
        zoomOnScroll: true,
        regionStyle: {
            initial: {
                fill: '#e9ecef',
                "fill-opacity": 1,
                stroke: 'none',
                "stroke-width": 0,
                "stroke-opacity": 0
            },
            hover: {
                "fill-opacity": 0.8,
                cursor: 'pointer'
            },
            selected: {
                fill: '#3949ab'
            },
            selectedHover: {}
        },
        series: {
            regions: [{
                values: percentageData,
                scale: ['#c5cae9', '#3949ab', '#1a237e'],
                normalizeFunction: 'polynomial',
                legend: {
                    vertical: true,
                    title: 'Audience Distribution (%)'
                }
            }]
        },
        onRegionTipShow: function(e, el, code) {
            if (percentageData[code]) {
                const impressions = mapData[code];
                const percentage = percentageData[code].toFixed(1);
                el.html(el.html() + ': ' + percentage + '% (' + impressions + ' impressions)');
            } else {
                el.html(el.html() + ': No data');
            }
        }
    });

    // Add legend
    const legend = document.createElement('div');
    legend.className = 'map-legend';
    legend.innerHTML = `
        <div class="legend-title mb-2">Audience Distribution</div>
        <div class="legend-items">
            <div class="legend-item">
                <div class="legend-color" style="background: #c5cae9;"></div>
                <div>Low</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #3949ab;"></div>
                <div>Medium</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #1a237e;"></div>
                <div>High</div>
            </div>
        </div>
    `;
    worldMapElement.parentNode.appendChild(legend);
}

// Populate geography tables
function populateGeographyTables() {
    // Get location data from the page
    let locationData;
    try {
        locationData = JSON.parse(document.getElementById('location_data_json').textContent);
    } catch (e) {
        console.error('Error parsing location data for tables:', e);
        return;
    }

    if (!locationData || Object.keys(locationData).length === 0) {
        console.log('No location data available for tables');
        return;
    }

    // Calculate total impressions
    const totalImpressions = Object.values(locationData).reduce((a, b) => a + b, 0);

    // Populate cities table
    const citiesTableBody = document.getElementById('cities-table-body');
    if (citiesTableBody) {
        // Sort locations by impressions (descending)
        const sortedLocations = Object.entries(locationData)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 5); // Top 5 cities

        // Clear existing rows
        citiesTableBody.innerHTML = '';

        // Add rows for each city
        sortedLocations.forEach(([city, impressions]) => {
            const percentage = ((impressions / totalImpressions) * 100).toFixed(1);
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${city}</td>
                <td>${impressions}</td>
                <td>${percentage}%</td>
            `;
            citiesTableBody.appendChild(row);
        });
    }

    // For a real implementation, we would have separate region data
    // For now, just use the same data for regions
    const regionsTableBody = document.getElementById('regions-table-body');
    if (regionsTableBody) {
        // Sort locations by impressions (descending)
        const sortedRegions = Object.entries(locationData)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 5); // Top 5 regions

        // Clear existing rows
        regionsTableBody.innerHTML = '';

        // Add rows for each region
        sortedRegions.forEach(([region, impressions]) => {
            const percentage = ((impressions / totalImpressions) * 100).toFixed(1);
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${region} Region</td>
                <td>${impressions}</td>
                <td>${percentage}%</td>
            `;
            regionsTableBody.appendChild(row);
        });
    }
}

// Fetch analytics data based on date range
function fetchAnalyticsData(startDate, endDate) {
    // This would be implemented to fetch data from the server
    console.log('Fetching analytics data for date range:', startDate, 'to', endDate);
    // In a real implementation, this would make an AJAX request to the server

    // For now, just show a loading message
    const chartContainer = document.querySelector('.chart-container');
    chartContainer.innerHTML = '<div class="text-center py-5"><div class="spinner-border text-primary" role="status"></div><p class="mt-3">Loading data...</p></div>';

    // Simulate loading delay
    setTimeout(() => {
        // Restore the chart
        chartContainer.innerHTML = '<canvas id="performanceChart" height="300"></canvas>';
        initPerformanceChart();
    }, 1500);
}
