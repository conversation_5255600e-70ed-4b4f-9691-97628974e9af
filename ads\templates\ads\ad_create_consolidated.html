{% extends 'base.html' %}
{% load static %}

{% block title %}Create New Ad | Enterprise Dashboard{% endblock %}

{% block extra_css %}
<!-- Google Fonts - Poppins and Montserrat -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

<!-- Include common ad styles -->
{% include 'ads/includes/ads_common_css.html' %}
<link rel="stylesheet" href="{% static 'ads/css/dashboard_enterprise.css' %}">
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<!-- Smart Engine CSS -->
<link rel="stylesheet" href="{% static 'ads/css/smart-engine.css' %}" />
<!-- Ad Creation Enterprise CSS -->
<link rel="stylesheet" href="{% static 'ads/css/ad-creation-enterprise.css' %}" />
<!-- Select2 Campaign Dropdown CSS -->
<link rel="stylesheet" href="{% static 'ads/css/select2-campaign-dropdown.css' %}" />
<!-- Ad Create Consolidated Styles -->
<link rel="stylesheet" href="{% static 'ads/css/ad-create-consolidated.css' %}" />
<!-- Soft Alert Styles -->
<link rel="stylesheet" href="{% static 'css/soft-alerts.css' %}" />
<!-- Dashboard Notification Panel Styles -->
<link rel="stylesheet" href="{% static 'css/dashboard-notification-panel.css' %}" />
<!-- Enhanced UX Styles -->
<link rel="stylesheet" href="{% static 'ads/css/ad-create-enhanced-ux.css' %}" />
<!-- Tooltips and Animations -->
<link href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css" rel="stylesheet">
<!-- Tippy.js for advanced tooltips -->
<link rel="stylesheet" href="https://unpkg.com/tippy.js@6/dist/tippy.css" />

<style>
    /* Ultra-Premium Enterprise Ad Creation Styling */
    body {
        background: linear-gradient(135deg, #000000 0%, #0a0a0a 10%, #1a1a2e 25%, #16213e 40%, #0f3460 60%, #533483 80%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        position: relative;
        overflow-x: hidden;
    }

    /* Premium animated background with enterprise patterns */
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 35% 65%, rgba(102, 126, 234, 0.7) 0%, transparent 40%),
            radial-gradient(circle at 65% 35%, rgba(255, 255, 255, 0.3) 0%, transparent 35%),
            radial-gradient(circle at 55% 55%, rgba(118, 75, 162, 0.6) 0%, transparent 45%),
            radial-gradient(circle at 45% 45%, rgba(83, 52, 131, 0.5) 0%, transparent 30%),
            radial-gradient(circle at 25% 75%, rgba(102, 126, 234, 0.4) 0%, transparent 40%);
        z-index: -1;
        animation: enterpriseAdFloat 70s ease-in-out infinite;
    }

    @keyframes enterpriseAdFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        25% { transform: translateY(-45px) rotate(3.5deg); }
        50% { transform: translateY(-35px) rotate(-3.5deg); }
        75% { transform: translateY(-55px) rotate(1.8deg); }
    }

    /* Override existing enterprise dashboard styling */
    .enterprise-dashboard {
        background: transparent !important;
        position: relative;
        z-index: 1;
        min-height: 100vh;
    }

    /* Premium Header Enhancement */
    .dashboard-header {
        background: linear-gradient(135deg, rgba(26, 35, 126, 0.95) 0%, rgba(40, 53, 147, 0.95) 50%, rgba(63, 81, 181, 0.95) 100%);
        backdrop-filter: blur(20px);
        border-radius: 0 0 20px 20px;
        padding: 2rem 0;
        margin-bottom: 2rem;
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        position: relative;
        overflow: hidden;
        animation: slideInDown 0.8s ease-out;
    }

    .dashboard-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
        animation: shimmer 3s ease-in-out infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    .welcome-title {
        font-family: 'Montserrat', sans-serif !important;
        font-size: 2.2rem;
        font-weight: 700;
        color: #ffffff;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 2;
    }

    .welcome-subtitle {
        font-size: 1rem;
        color: rgba(255, 255, 255, 0.9);
        font-weight: 400;
        position: relative;
        z-index: 2;
    }

    /* Premium Sidebar Enhancement */
    .dashboard-sidebar {
        position: relative;
        z-index: 2;
    }

    .sidebar-container {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        overflow: hidden;
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.15),
            0 0 0 1px rgba(255, 255, 255, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        animation: slideInLeft 0.8s ease-out 0.2s both;
        margin-bottom: 2rem;
    }

    .sidebar-header {
        background: linear-gradient(135deg, #1a237e 0%, #283593 100%);
        color: white;
        padding: 1.5rem;
        font-weight: 600;
        font-size: 1.1rem;
        position: relative;
    }

    .sidebar-header::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    }

    .nav-list {
        list-style: none;
        padding: 1rem;
        margin: 0;
    }

    .nav-item {
        margin-bottom: 0.5rem;
    }

    .nav-link {
        display: flex;
        align-items: center;
        padding: 1rem 1.5rem;
        color: #1a237e !important;
        text-decoration: none;
        border-radius: 12px;
        transition: all 0.3s ease;
        font-weight: 600 !important;
        position: relative;
        overflow: hidden;
        background: rgba(255, 255, 255, 0.8);
        border: 2px solid rgba(102, 126, 234, 0.1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .nav-link span {
        color: #1a237e !important;
        font-weight: 600 !important;
        font-size: 0.95rem;
        opacity: 1 !important;
        visibility: visible !important;
    }

    .nav-link i {
        color: #667eea !important;
        margin-right: 0.75rem;
        font-size: 1.1rem;
        opacity: 1 !important;
        visibility: visible !important;
    }

    .nav-link::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.15), transparent);
        transition: left 0.5s;
        z-index: 0;
    }

    .nav-link:hover::before {
        left: 100%;
    }

    .nav-link:hover {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.15) 100%);
        color: #667eea !important;
        transform: translateX(8px);
        border: 2px solid rgba(102, 126, 234, 0.3);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
    }

    .nav-link:hover span {
        color: #667eea !important;
        font-weight: 700 !important;
    }

    .nav-link:hover i {
        color: #667eea !important;
        transform: scale(1.1);
    }

    .nav-item.active .nav-link {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white !important;
        transform: translateX(8px);
        border: 2px solid #667eea;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }

    .nav-item.active .nav-link span {
        color: white !important;
        font-weight: 700 !important;
    }

    .nav-item.active .nav-link i {
        color: white !important;
    }

    /* Ensure sidebar title is visible */
    .sidebar-title {
        color: white !important;
        font-weight: 700 !important;
        font-size: 1.1rem !important;
        opacity: 1 !important;
        visibility: visible !important;
    }

    /* Premium Main Content Area */
    .dashboard-main {
        position: relative;
        z-index: 2;
    }

    .dashboard-card {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        overflow: hidden;
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.15),
            0 0 0 1px rgba(255, 255, 255, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        animation: slideInRight 0.8s ease-out 0.4s both;
        margin-bottom: 2rem;
    }

    .dashboard-card .card-header {
        background: linear-gradient(135deg, #1a237e 0%, #283593 100%);
        color: white;
        padding: 1.5rem 2rem;
        border-bottom: none;
        position: relative;
    }

    .dashboard-card .card-header::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    }

    .card-title {
        font-family: 'Montserrat', sans-serif !important;
        font-size: 1.3rem;
        font-weight: 700;
        margin: 0;
        position: relative;
        z-index: 2;
    }

    .dashboard-card .card-body {
        padding: 2rem;
        background: transparent;
    }

    /* Premium Navigation Tabs */
    .nav-tabs {
        border-bottom: none;
        margin-bottom: 2rem;
    }

    .nav-tabs .nav-link {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
        border: 2px solid rgba(102, 126, 234, 0.2);
        border-radius: 12px;
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;
        padding: 0.75rem 1.5rem;
        color: #1a237e;
        font-weight: 600;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .nav-tabs .nav-link::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
        transition: left 0.5s;
    }

    .nav-tabs .nav-link:hover::before {
        left: 100%;
    }

    .nav-tabs .nav-link:hover {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        border-color: rgba(102, 126, 234, 0.4);
        color: #667eea;
        transform: translateY(-2px);
    }

    .nav-tabs .nav-link.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-color: #667eea;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }

    /* Premium Form Elements */
    .form-group-enhanced {
        margin-bottom: 2rem;
    }

    .form-label {
        font-weight: 600;
        color: #1a237e;
        margin-bottom: 0.75rem;
        font-size: 1rem;
        display: flex;
        align-items: center;
    }

    .required-asterisk {
        color: #ef4444;
        margin-left: 0.25rem;
    }

    .enhanced-input,
    .form-control,
    .form-select {
        border: 2px solid rgba(102, 126, 234, 0.2);
        border-radius: 12px;
        padding: 0.875rem 1.25rem;
        font-weight: 500;
        background: rgba(255, 255, 255, 0.9);
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        font-size: 1rem;
    }

    .enhanced-input:focus,
    .form-control:focus,
    .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 8px 25px rgba(0, 0, 0, 0.1);
        outline: none;
        background: rgba(255, 255, 255, 1);
    }

    .input-wrapper {
        position: relative;
    }

    .input-feedback {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 0.5rem;
        font-size: 0.85rem;
    }

    .character-count {
        color: #667eea;
        font-weight: 500;
    }

    .form-help {
        margin-top: 0.5rem;
        font-size: 0.9rem;
        color: #667eea;
        font-weight: 500;
    }

    /* Premium Step Welcome Cards */
    .step-welcome-card {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        border: 2px solid rgba(102, 126, 234, 0.2);
        border-radius: 16px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .step-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        margin-right: 1rem;
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
    }

    .step-content h5 {
        font-weight: 700;
        color: #1a237e;
        margin-bottom: 0.5rem;
    }

    .step-content p {
        color: #667eea;
        font-weight: 500;
        margin: 0;
    }
</style>
{% endblock %}

{% block content %}
<!-- Enterprise Dashboard Container -->
<div class="enterprise-dashboard">
    <!-- Context-aware Header -->
    <div class="dashboard-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="dashboard-welcome animate__animated animate__fadeInLeft">
                        <h1 class="welcome-title">
                            <i class="fas fa-magic me-2 text-warning"></i>
                            Create Your Perfect Ad
                            <span class="badge bg-success ms-2 pulse-animation">AI-Powered</span>
                        </h1>
                        <p class="welcome-subtitle">
                            Transform your ideas into high-converting advertisements with our intelligent AI engine.
                            <strong class="text-warning">Save time</strong> and <strong class="text-success">boost performance</strong> with smart automation
                        </p>
                        <div class="welcome-stats mt-3 mb-3">
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="stat-item">
                                        <div class="stat-number">Growing</div>
                                        <div class="stat-label">Community</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-item">
                                        <div class="stat-number">Quality</div>
                                        <div class="stat-label">Results</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-item">
                                        <div class="stat-number">AI-Powered</div>
                                        <div class="stat-label">Platform</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <a href="{% url 'ads:dashboard' %}" class="btn btn-sm btn-outline-light mt-2">
                            <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
                        </a>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="dashboard-actions">
                        <div class="action-item">
                            <button class="btn-icon" title="Notifications">
                                <i class="fas fa-bell"></i>
                                {% if notifications_count > 0 %}
                                <span class="notification-badge">{{ notifications_count }}</span>
                                {% endif %}
                            </button>
                        </div>
                        <div class="action-item">
                            <a href="{% url 'ads:campaign_list' %}" class="btn btn-light">
                                <i class="fas fa-bullhorn me-2"></i> View Campaigns
                            </a>
                        </div>
                        <div class="action-item">
                            <a href="{% url 'ads:ad_list' %}" class="btn btn-light">
                                <i class="fas fa-list me-2"></i> View Ads
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Dashboard Content -->
    <div class="dashboard-content">
        <div class="container-fluid">
            <div class="row">
                <!-- Sidebar Navigation -->
                <div class="col-lg-2 dashboard-sidebar">
                    <div class="sidebar-container">
                        <div class="sidebar-header">
                            <h2 class="sidebar-title">Ad Creation</h2>
                        </div>
                        <nav class="sidebar-nav">
                            <ul class="nav-list">
                                <li class="nav-item active">
                                    <a href="#" class="nav-link">
                                        <i class="fas fa-plus-circle"></i>
                                        <span>Create Ad</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{% url 'ads:ad_list' %}" class="nav-link">
                                        <i class="fas fa-list"></i>
                                        <span>My Ads</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{% url 'ads:campaign_list' %}" class="nav-link">
                                        <i class="fas fa-bullhorn"></i>
                                        <span>Campaigns</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{% url 'ads:analytics_enterprise' %}" class="nav-link">
                                        <i class="fas fa-chart-bar"></i>
                                        <span>Analytics</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{% url 'ads:dashboard' %}" class="nav-link">
                                        <i class="fas fa-tachometer-alt"></i>
                                        <span>Dashboard</span>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>

                <!-- Main Content Area -->
                <div class="col-lg-10 dashboard-main">
                    <div class="row">
                        <div class="col-lg-8">
                            <!-- Multi-step form -->
                            <div class="dashboard-card">
                                <div class="card-header">
                                    <h3 class="card-title">Create Your Advertisement</h3>
                                    <div class="card-actions">
                                        <div class="progress">
                                            <div class="progress-bar" role="progressbar" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <!-- Tabs Navigation -->
                                    <ul class="nav nav-tabs" id="adCreationTabs" role="tablist">
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link active" id="step1-tab" data-bs-toggle="tab" data-bs-target="#step1" type="button" role="tab" aria-controls="step1" aria-selected="true">1. Basic Info</button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="step2-tab" data-bs-toggle="tab" data-bs-target="#step2" type="button" role="tab" aria-controls="step2" aria-selected="false">2. Content</button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="step3-tab" data-bs-toggle="tab" data-bs-target="#step3" type="button" role="tab" aria-controls="step3" aria-selected="false">3. Options</button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="step4-tab" data-bs-toggle="tab" data-bs-target="#step4" type="button" role="tab" aria-controls="step4" aria-selected="false">4. Review</button>
                                        </li>
                                    </ul>

                                    <form method="post" enctype="multipart/form-data" id="adCreationForm" class="mt-4">
                                        {% csrf_token %}
                                        <div id="formErrorContainer" class="alert alert-danger" style="display: none;"></div>
                                        <div class="tab-content" id="adCreationTabContent">
                                            <!-- Step 1: Basic Info -->
                                            <div class="tab-pane fade show active" id="step1" role="tabpanel" aria-labelledby="step1-tab">
                                                <!-- Welcome Message for Step 1 -->
                                                <div class="step-welcome-card mb-4">
                                                    <div class="d-flex align-items-center">
                                                        <div class="step-icon">
                                                            <i class="fas fa-info-circle"></i>
                                                        </div>
                                                        <div class="step-content">
                                                            <h5 class="mb-1">Let's Start with the Basics</h5>
                                                            <p class="mb-0 text-muted">Tell us about your ad and we'll help you create something amazing!</p>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="mb-4 form-group-enhanced">
                                                    <label for="adTitle" class="form-label">
                                                        <i class="fas fa-heading me-2 text-primary"></i>
                                                        Ad Title
                                                        <span class="required-asterisk">*</span>
                                                        <i class="fas fa-question-circle ms-1 text-muted"
                                                           data-tippy-content="Create a catchy title that grabs attention. Keep it under 60 characters for best results. Our AI will help optimize it!"
                                                           data-tippy-placement="top"></i>
                                                    </label>
                                                    <div class="input-wrapper">
                                                        <input type="text"
                                                               class="form-control enhanced-input"
                                                               id="adTitle"
                                                               name="title"
                                                               placeholder="Enter your ad title (e.g., 'Best Coffee in Town')"
                                                               maxlength="60"
                                                               required>
                                                        <div class="input-feedback">
                                                            <span class="character-count">0/60</span>
                                                            <span class="input-status"></span>
                                                        </div>
                                                    </div>
                                                    <div class="form-help">
                                                        <i class="fas fa-lightbulb me-1 text-warning"></i>
                                                        <strong>Pro Tip:</strong> Great titles are specific, benefit-focused, and create curiosity
                                                    </div>
                                                </div>

                                                <div class="mb-4 form-group-enhanced">
                                                    <label for="businessType" class="form-label">
                                                        <i class="fas fa-building me-2 text-primary"></i>
                                                        Business Type
                                                        <span class="required-asterisk">*</span>
                                                        <i class="fas fa-question-circle ms-1 text-muted"
                                                           data-tippy-content="Describe your business type so our AI can generate industry-specific content that resonates with your audience"
                                                           data-tippy-placement="top"></i>
                                                    </label>
                                                    <div class="input-wrapper">
                                                        <input type="text"
                                                               class="form-control enhanced-input"
                                                               id="businessType"
                                                               name="business_type"
                                                               placeholder="e.g., Coffee Shop, Fashion Boutique, Tech Startup"
                                                               list="businessTypeSuggestions"
                                                               required>
                                                        <datalist id="businessTypeSuggestions">
                                                            <option value="Restaurant">
                                                            <option value="Coffee Shop">
                                                            <option value="Fashion Boutique">
                                                            <option value="Tech Startup">
                                                            <option value="Fitness Center">
                                                            <option value="Beauty Salon">
                                                            <option value="Real Estate">
                                                            <option value="Automotive">
                                                            <option value="Healthcare">
                                                            <option value="Education">
                                                        </datalist>
                                                        <div class="input-feedback">
                                                            <span class="input-status"></span>
                                                        </div>
                                                    </div>
                                                    <div class="form-help">
                                                        <i class="fas fa-robot me-1 text-success"></i>
                                                        <strong>AI Boost:</strong> Our AI uses this to create industry-specific, high-converting content
                                                    </div>
                                                </div>

                                                <!-- AI Smart Engine Section -->
                                                <div class="mb-4">
                                                    <!-- Smart Engine Feature Card -->
                                                    <div class="smart-engine-feature-card">
                                                        <div class="feature-header">
                                                            <div class="d-flex align-items-center justify-content-between">
                                                                <div class="feature-title">
                                                                    <i class="fas fa-brain me-2 text-primary"></i>
                                                                    <strong>AI Smart Engine</strong>
                                                                    <span class="badge bg-gradient-primary ms-2">PREMIUM</span>
                                                                </div>
                                                                <div class="feature-toggle">
                                                                    <div class="ai-toggle-container">
                                                                        <input class="ai-toggle-input" type="checkbox" id="useSmartEngine" name="use_smart_engine" checked>
                                                                        <label class="ai-toggle-label" for="useSmartEngine">
                                                                            <div class="ai-toggle-slider">
                                                                                <div class="ai-toggle-thumb">
                                                                                    <i class="fas fa-brain toggle-icon"></i>
                                                                                </div>
                                                                                <div class="ai-toggle-track">
                                                                                    <span class="toggle-text-off">OFF</span>
                                                                                    <span class="toggle-text-on">AI ON</span>
                                                                                </div>
                                                                            </div>
                                                                        </label>
                                                                        <div class="ai-toggle-status">
                                                                            <span class="status-text">AI Smart Engine</span>
                                                                            <div class="status-indicator">
                                                                                <div class="status-dot"></div>
                                                                                <span class="status-label">Ready</span>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="feature-description">
                                                            <p class="mb-2">
                                                                <i class="fas fa-magic me-1 text-warning"></i>
                                                                Generate professional, high-converting ad content powered by advanced AI
                                                            </p>
                                                            <div class="feature-benefits">
                                                                <div class="row">
                                                                    <div class="col-md-4">
                                                                        <div class="benefit-item">
                                                                            <i class="fas fa-clock text-success"></i>
                                                                            <span>Save 80% Time</span>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-md-4">
                                                                        <div class="benefit-item">
                                                                            <i class="fas fa-chart-line text-primary"></i>
                                                                            <span>3x Better CTR</span>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-md-4">
                                                                        <div class="benefit-item">
                                                                            <i class="fas fa-target text-warning"></i>
                                                                            <span>Industry-Specific</span>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="feature-pricing">
                                                            <div class="d-flex align-items-center justify-content-between">
                                                                <div class="pricing-info">
                                                                    <span class="price-label">Premium Feature:</span>
                                                                    <span class="price-amount">+20 KSH</span>
                                                                    <span class="price-note">(Worth 200 KSH value)</span>
                                                                </div>
                                                                <div class="status-indicator">
                                                                    {% if ai_provider_status.mistral.available %}
                                                                        <span class="badge bg-success ai-status-indicator fw-bold" data-engine="Smart Engine">
                                                                            <i class="fas fa-check-circle me-1"></i> ONLINE
                                                                        </span>
                                                                    {% else %}
                                                                        <span class="badge bg-danger ai-status-indicator fw-bold" data-engine="Smart Engine">
                                                                            <i class="fas fa-times-circle me-1"></i> OFFLINE
                                                                        </span>
                                                                    {% endif %}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <!-- Hidden element to store user balance -->
                                                    <div id="userBalance" class="d-none" data-balance="{{ user_balance|default:'100.00' }}"></div>

                                                    <div id="smartEngineOptions" class="mt-3" style="display: block !important; visibility: visible !important;">
                                                        <div class="mb-3">
                                                            <label for="aiLanguage" class="form-label">Language</label>
                                                            <select class="form-select" id="aiLanguage" name="ai_language">
                                                                <option value="english" selected>English</option>
                                                                <option value="swahili">Swahili</option>
                                                                <option value="sheng">Sheng</option>
                                                            </select>
                                                        </div>

                                                        <!-- AI Customization Options -->
                                                        <!-- Creativity Level Row -->
                                                        <div class="mb-3">
                                                            <label class="form-label fw-bold text-primary mb-2">Creativity Level</label>
                                                            <div class="creativity-options d-flex justify-content-between">
                                                                <div class="form-check form-check-inline me-4">
                                                                    <input class="form-check-input" type="radio" name="aiCreativityRadio" id="aiCreativityConservative" value="0.3" checked>
                                                                    <label class="form-check-label px-3 py-2 rounded border" for="aiCreativityConservative">
                                                                        <i class="fas fa-briefcase me-1"></i> Conservative
                                                                    </label>
                                                                </div>
                                                                <div class="form-check form-check-inline me-4">
                                                                    <input class="form-check-input" type="radio" name="aiCreativityRadio" id="aiCreativityBalanced" value="0.7">
                                                                    <label class="form-check-label px-3 py-2 rounded border bg-light" for="aiCreativityBalanced">
                                                                        <i class="fas fa-balance-scale me-1"></i> Balanced
                                                                    </label>
                                                                </div>
                                                                <div class="form-check form-check-inline">
                                                                    <input class="form-check-input" type="radio" name="aiCreativityRadio" id="aiCreativityCreative" value="1.0">
                                                                    <label class="form-check-label px-3 py-2 rounded border bg-primary text-white" for="aiCreativityCreative">
                                                                        <i class="fas fa-lightbulb me-1"></i> Creative
                                                                    </label>
                                                                </div>
                                                            </div>
                                                            <!-- Hidden input to store the value for compatibility -->
                                                            <input type="hidden" id="aiCreativity" value="0.3">
                                                        </div>

                                                        <!-- Length and Style Row -->
                                                        <div class="row mb-3">
                                                            <div class="col-md-6">
                                                                <label for="aiLength" class="form-label">Length</label>
                                                                <select class="form-select" id="aiLength">
                                                                    <option value="short">Short</option>
                                                                    <option value="medium" selected>Medium</option>
                                                                    <option value="long">Long</option>
                                                                </select>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <label for="aiStyle" class="form-label">Style</label>
                                                                <select class="form-select" id="aiStyle">
                                                                    <option value="standard" selected>Standard</option>
                                                                    <option value="professional">Professional</option>
                                                                    <option value="casual">Casual</option>
                                                                    <option value="persuasive">Persuasive</option>
                                                                    <option value="informative">Informative</option>
                                                                </select>
                                                            </div>
                                                        </div>

                                                        <!-- Error container for AI suggestions -->
                                                        <div id="formErrorContainer" class="alert alert-danger mt-2" style="display: none;"></div>


                                                        <!-- Enhanced Generate Suggestions Button -->
                                                        <div class="generate-suggestions-section">
                                                            <div class="text-center mb-3">
                                                                <h6 class="text-muted mb-2">
                                                                    <i class="fas fa-sparkles me-1"></i>
                                                                    Ready to create amazing content?
                                                                </h6>
                                                                <p class="small text-muted mb-3">
                                                                    Our AI will analyze your business and generate 3 professional ad variations
                                                                </p>
                                                            </div>

                                                            <button type="button"
                                                                    id="generateSuggestions"
                                                                    class="btn btn-gradient-primary btn-lg w-100 generate-btn-enhanced"
                                                                    style="display: block !important;"
                                                                    data-action="use-ai"
                                                                    onclick="console.log('Button clicked!'); if(window.generateSuggestions) { console.log('Calling generateSuggestions'); window.generateSuggestions(); } else { console.error('generateSuggestions function not found'); }"
                                                                    data-tippy-content="Click to generate 3 AI-powered ad suggestions tailored to your business">
                                                                <div class="btn-content">
                                                                    <i class="fas fa-magic me-2"></i>
                                                                    <span class="btn-text">Generate AI Suggestions</span>
                                                                    <div class="btn-loading-spinner" style="display: none;">
                                                                        <div class="loading-spinner"></div>
                                                                    </div>
                                                                </div>
                                                                <div class="btn-subtitle">
                                                                    <small>Powered by Advanced AI • Takes 2-3 seconds</small>
                                                                </div>
                                                            </button>

                                                            <div class="generation-benefits mt-3">
                                                                <div class="row text-center">
                                                                    <div class="col-4">
                                                                        <div class="benefit-mini">
                                                                            <i class="fas fa-bolt text-warning"></i>
                                                                            <span>Instant</span>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-4">
                                                                        <div class="benefit-mini">
                                                                            <i class="fas fa-bullseye text-success"></i>
                                                                            <span>Targeted</span>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-4">
                                                                        <div class="benefit-mini">
                                                                            <i class="fas fa-trophy text-primary"></i>
                                                                            <span>Professional</span>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <!-- AI Provider Status Info -->
                                                        <div class="mt-2 mb-2">
                                                            <small class="text-muted">
                                                                Using: <span id="best-ai-provider" class="badge {% if best_ai_provider %}bg-success{% else %}bg-danger{% endif %}">
                                                                    {% if best_ai_provider %}{{ best_ai_provider }}{% else %}Basic Engine{% endif %}
                                                                </span>
                                                                <span class="ms-2">
                                                                    {% if best_ai_provider %}
                                                                        <i class="fas fa-info-circle"></i> Suggestions will be generated using our {{ best_ai_provider }}.
                                                                    {% else %}
                                                                        <i class="fas fa-exclamation-triangle"></i> Using Basic Engine with pre-generated suggestions.
                                                                    {% endif %}
                                                                </span>
                                                            </small>
                                                        </div>

                                                        <!-- No backup buttons - we only use the main Generate Suggestions button -->

                                                        <div id="aiSuggestionsContainer" class="mt-3" style="display: block !important; visibility: visible !important; opacity: 1 !important;">
                                                            <h5>AI Suggestions</h5>
                                                            <div class="ai-suggestions" style="display: block !important; visibility: visible !important;">
                                                                <!-- Suggestion 1 -->
                                                                <div class="ai-suggestion-card mb-3 p-3 border rounded" style="display: block !important; visibility: visible !important; opacity: 1 !important;">
                                                                    <div class="form-check mb-2">
                                                                        <input class="form-check-input ai-suggestion-select" type="radio" name="ai_suggestion" id="suggestion1" value="1">
                                                                        <label class="form-check-label fw-bold" for="suggestion1">Suggestion 1</label>
                                                                        <button type="button" class="btn btn-sm btn-primary float-end apply-suggestion-btn">
                                                                            <i class="fas fa-check me-1"></i> Apply
                                                                        </button>
                                                                    </div>
                                                                    <div class="ai-suggestion-title mb-1">Title will appear here</div>
                                                                    <div class="ai-suggestion-content small text-muted">Content will appear here</div>
                                                                </div>

                                                                <!-- Suggestion 2 -->
                                                                <div class="ai-suggestion-card mb-3 p-3 border rounded" style="display: block !important; visibility: visible !important; opacity: 1 !important;">
                                                                    <div class="form-check mb-2">
                                                                        <input class="form-check-input ai-suggestion-select" type="radio" name="ai_suggestion" id="suggestion2" value="2">
                                                                        <label class="form-check-label fw-bold" for="suggestion2">Suggestion 2</label>
                                                                        <button type="button" class="btn btn-sm btn-primary float-end apply-suggestion-btn">
                                                                            <i class="fas fa-check me-1"></i> Apply
                                                                        </button>
                                                                    </div>
                                                                    <div class="ai-suggestion-title mb-1">Title will appear here</div>
                                                                    <div class="ai-suggestion-content small text-muted">Content will appear here</div>
                                                                </div>

                                                                <!-- Suggestion 3 -->
                                                                <div class="ai-suggestion-card mb-3 p-3 border rounded" style="display: block !important; visibility: visible !important; opacity: 1 !important;">
                                                                    <div class="form-check mb-2">
                                                                        <input class="form-check-input ai-suggestion-select" type="radio" name="ai_suggestion" id="suggestion3" value="3">
                                                                        <label class="form-check-label fw-bold" for="suggestion3">Suggestion 3</label>
                                                                        <button type="button" class="btn btn-sm btn-primary float-end apply-suggestion-btn">
                                                                            <i class="fas fa-check me-1"></i> Apply
                                                                        </button>
                                                                    </div>
                                                                    <div class="ai-suggestion-title mb-1">Title will appear here</div>
                                                                    <div class="ai-suggestion-content small text-muted">Content will appear here</div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <!-- Template for suggestion cards (hidden) -->
                                                        <script type="text/template" id="suggestionCardsTemplate">
                                                            <h5>AI Suggestions</h5>
                                                            <div class="ai-suggestions">
                                                                <!-- Suggestion 1 -->
                                                                <div class="ai-suggestion-card mb-3 p-3 border rounded">
                                                                    <div class="form-check mb-2">
                                                                        <input class="form-check-input ai-suggestion-select" type="radio" name="ai_suggestion" id="suggestion1" value="1">
                                                                        <label class="form-check-label fw-bold" for="suggestion1">Suggestion 1</label>
                                                                        <button type="button" class="btn btn-sm btn-primary float-end apply-suggestion-btn">
                                                                            <i class="fas fa-check me-1"></i> Apply
                                                                        </button>
                                                                    </div>
                                                                    <div class="ai-suggestion-title mb-1">Title will appear here</div>
                                                                    <div class="ai-suggestion-content small text-muted">Content will appear here</div>
                                                                </div>

                                                                <!-- Suggestion 2 -->
                                                                <div class="ai-suggestion-card mb-3 p-3 border rounded">
                                                                    <div class="form-check mb-2">
                                                                        <input class="form-check-input ai-suggestion-select" type="radio" name="ai_suggestion" id="suggestion2" value="2">
                                                                        <label class="form-check-label fw-bold" for="suggestion2">Suggestion 2</label>
                                                                        <button type="button" class="btn btn-sm btn-primary float-end apply-suggestion-btn">
                                                                            <i class="fas fa-check me-1"></i> Apply
                                                                        </button>
                                                                    </div>
                                                                    <div class="ai-suggestion-title mb-1">Title will appear here</div>
                                                                    <div class="ai-suggestion-content small text-muted">Content will appear here</div>
                                                                </div>

                                                                <!-- Suggestion 3 -->
                                                                <div class="ai-suggestion-card mb-3 p-3 border rounded">
                                                                    <div class="form-check mb-2">
                                                                        <input class="form-check-input ai-suggestion-select" type="radio" name="ai_suggestion" id="suggestion3" value="3">
                                                                        <label class="form-check-label fw-bold" for="suggestion3">Suggestion 3</label>
                                                                        <button type="button" class="btn btn-sm btn-primary float-end apply-suggestion-btn">
                                                                            <i class="fas fa-check me-1"></i> Apply
                                                                        </button>
                                                                    </div>
                                                                    <div class="ai-suggestion-title mb-1">Title will appear here</div>
                                                                    <div class="ai-suggestion-content small text-muted">Content will appear here</div>
                                                                </div>
                                                            </div>
                                                        </script>
                                                    </div>
                                                </div>
                                                <div class="mb-4">
                                                    <label for="adType" class="form-label">Ad Type</label>
                                                    <select class="form-select" id="adType" name="ad_type" required>
                                                        <option value="" selected disabled>Select an ad type</option>
                                                        {% for ad_type in ad_types %}
                                                        <option value="{{ ad_type.id }}" data-price="{{ ad_type.base_price }}">{{ ad_type.name }} ({{ ad_type.base_price }} KSH)</option>
                                                        {% endfor %}
                                                    </select>
                                                </div>
                                                <div class="mb-4">
                                                    <label for="campaign" class="form-label">Campaign <small class="text-muted">(Optional)</small></label>
                                                    <select class="form-select campaign-select" id="campaign" name="campaign">
                                                        {% if selected_campaign %}
                                                            <option value="{{ selected_campaign.id }}" selected>{{ selected_campaign.name }}</option>
                                                        {% else %}
                                                            <option value="" selected>No Campaign (Individual Ad)</option>
                                                        {% endif %}
                                                    </select>
                                                    <div class="form-text">
                                                        <i class="fas fa-info-circle me-1"></i> Campaigns help you organize related ads together for better management and analytics.
                                                        <a href="{% url 'ads:campaign_create' %}" class="ms-2">
                                                            <i class="fas fa-plus-circle me-1"></i>Create New Campaign
                                                        </a>
                                                    </div>
                                                </div>
                                                <div class="mb-4">
                                                    <label for="durationOption" class="form-label">Ad Duration</label>
                                                    <select class="form-select" id="durationOption" name="duration_option">
                                                        <option value="7days" selected>7 Days (Default)</option>
                                                        <option value="2weeks">2 Weeks</option>
                                                        <option value="monthly">Monthly (30 Days)</option>
                                                        <option value="yearly">Yearly (365 Days)</option>
                                                        <option value="custom">Custom Duration (1+ days)</option>
                                                    </select>
                                                </div>

                                                <div class="row">
                                                    <div class="col-md-6 mb-4">
                                                        <label for="startDate" class="form-label">Start Date</label>
                                                        <input type="date" class="form-control" id="startDate" name="start_date" required>
                                                    </div>
                                                    <div class="col-md-6 mb-4">
                                                        <label for="startTime" class="form-label">Start Time</label>
                                                        <input type="time" class="form-control" id="startTime" name="start_time" required>
                                                    </div>
                                                </div>

                                                <div id="customDurationFields" class="row">
                                                    <div class="col-md-6 mb-4">
                                                        <label for="endDate" class="form-label">End Date</label>
                                                        <input type="date" class="form-control" id="endDate" name="end_date">
                                                    </div>
                                                    <div class="col-md-6 mb-4">
                                                        <label for="endTime" class="form-label">End Time</label>
                                                        <input type="time" class="form-control" id="endTime" name="end_time">
                                                    </div>
                                                    <div class="col-12">
                                                        <div class="form-text">
                                                            <i class="fas fa-info-circle me-1"></i> Custom duration must be at least 1 day. The end date will be calculated automatically.
                                                        </div>
                                                    </div>
                                                </div>

                                                <div id="calculatedEndTime" class="mb-4 alert alert-info">
                                                    <strong>Your ad will run until:</strong> <span id="endDateTimeDisplay"></span>
                                                    <small class="d-block mt-1">(Includes a 2-hour bonus time)</small>
                                                    <small class="d-block mt-1"><i class="fas fa-bell me-1"></i> You will be notified 1 hour before your ad ends.</small>
                                                </div>

                                                <div class="mb-4">
                                                    <label for="target_location" class="form-label">Geographic Target Location</label>
                                                    <input type="text" class="form-control" id="target_location" name="target_location" placeholder="e.g., Nairobi, Kenya">
                                                    <small class="text-muted">The geographic area where you want to target your ad</small>
                                                </div>

                                                <div class="mb-4">
                                                    <label for="targetAudience" class="form-label">Target Audience</label>
                                                    <input type="text" class="form-control" id="targetAudience" name="target_audience" placeholder="e.g., Young professionals, age 25-35">
                                                </div>

                                                <div class="mb-4">
                                                    <label for="ad_location" class="form-label">Ad Placement Location <span class="text-danger">*</span></label>
                                                    <select class="form-select" id="ad_location" name="ad_location" required>
                                                        <option value="" selected disabled>Select where to display your ad</option>
                                                        {% for location in ad_locations %}
                                                            <option value="{{ location.id }}"
                                                                    data-multiplier="{{ location.price_multiplier }}"
                                                                    data-visibility="{{ location.visibility }}"
                                                                    data-impressions="{{ location.daily_impressions }}">
                                                                {{ location.name }} ({{ location.visibility|title }} visibility)
                                                            </option>
                                                        {% endfor %}
                                                    </select>
                                                    <small class="text-muted">Premium locations have higher visibility but cost more</small>
                                                </div>

                                                <div class="d-flex justify-content-end">
                                                    <button type="button" class="btn btn-primary next-step" data-next="step2-tab" data-step-title="Step 2: Content">
                                                        Next: Content <i class="fas fa-arrow-right ms-2"></i>
                                                    </button>
                                                </div>
                                            </div>

                                            <!-- Step 2: Content -->
                                            <div class="tab-pane fade" id="step2" role="tabpanel" aria-labelledby="step2-tab">
                                                <!-- Welcome Message for Step 2 -->
                                                <div class="step-welcome-card mb-4">
                                                    <div class="d-flex align-items-center">
                                                        <div class="step-icon">
                                                            <i class="fas fa-edit"></i>
                                                        </div>
                                                        <div class="step-content">
                                                            <h5 class="mb-1">Create Compelling Content</h5>
                                                            <p class="mb-0 text-muted">Write engaging ad content that converts visitors into customers!</p>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Ad Content Section -->
                                                <div class="mb-4 form-group-enhanced">
                                                    <label for="adContent" class="form-label">
                                                        <i class="fas fa-pen-fancy me-2 text-primary"></i>
                                                        Ad Content
                                                        <span class="required-asterisk">*</span>
                                                        <i class="fas fa-question-circle ms-1 text-muted"
                                                           data-tippy-content="Write compelling ad content that grabs attention and encourages action. Focus on benefits, not just features!"
                                                           data-tippy-placement="top"></i>
                                                    </label>
                                                    <div class="input-wrapper">
                                                        <textarea class="form-control enhanced-input"
                                                                  id="adContent"
                                                                  name="content"
                                                                  rows="5"
                                                                  placeholder="Write your compelling ad content here... Focus on benefits and include a clear call to action!"
                                                                  maxlength="500"
                                                                  required></textarea>
                                                        <div class="input-feedback">
                                                            <span class="character-count">0/500</span>
                                                            <span class="input-status"></span>
                                                        </div>
                                                    </div>
                                                    <div class="form-help">
                                                        <i class="fas fa-magic me-1 text-success"></i>
                                                        <strong>AI Tip:</strong> Great ad content answers "What's in it for me?" and creates urgency
                                                    </div>
                                                </div>

                                                <!-- Call to Action Link Section -->
                                                <div class="mb-4 form-group-enhanced">
                                                    <label for="ctaLink" class="form-label">
                                                        <i class="fas fa-external-link-alt me-2 text-primary"></i>
                                                        Call to Action Link
                                                        <i class="fas fa-question-circle ms-1 text-muted"
                                                           data-tippy-content="Add a link where users will go when they click your ad. This could be your website, landing page, or product page."
                                                           data-tippy-placement="top"></i>
                                                    </label>
                                                    <div class="input-wrapper">
                                                        <input type="url"
                                                               class="form-control enhanced-input"
                                                               id="ctaLink"
                                                               name="cta_link"
                                                               placeholder="https://your-website.com/landing-page">
                                                        <div class="input-feedback">
                                                            <span class="input-status"></span>
                                                        </div>
                                                    </div>
                                                    <div class="form-help">
                                                        <i class="fas fa-chart-line me-1 text-warning"></i>
                                                        <strong>Pro Tip:</strong> Use dedicated landing pages for better conversion tracking
                                                    </div>
                                                </div>

                                                <!-- Media Upload Section -->
                                                <div class="mb-4 form-group-enhanced">
                                                    <label for="adMedia" class="form-label">
                                                        <i class="fas fa-image me-2 text-primary"></i>
                                                        Upload Media
                                                        <i class="fas fa-question-circle ms-1 text-muted"
                                                           data-tippy-content="Upload high-quality images or videos to make your ad more engaging. Visual content increases click-through rates by up to 300%!"
                                                           data-tippy-placement="top"></i>
                                                    </label>
                                                    <div class="media-upload-container">
                                                        <div class="upload-area" id="uploadArea">
                                                            <div class="upload-icon">
                                                                <i class="fas fa-cloud-upload-alt"></i>
                                                            </div>
                                                            <div class="upload-text">
                                                                <h6>Drag & Drop or Click to Upload</h6>
                                                                <p class="text-muted mb-0">JPG, PNG, GIF up to 5MB</p>
                                                            </div>
                                                            <input type="file"
                                                                   class="form-control d-none"
                                                                   id="adMedia"
                                                                   name="media"
                                                                   accept="image/*">
                                                        </div>
                                                        <div class="upload-preview" id="uploadPreview" style="display: none;">
                                                            <img id="previewImage" src="" alt="Preview" class="preview-img">
                                                            <div class="preview-actions">
                                                                <button type="button" class="btn btn-sm btn-outline-danger" id="removeImage">
                                                                    <i class="fas fa-trash"></i> Remove
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="form-help">
                                                        <i class="fas fa-star me-1 text-warning"></i>
                                                        <strong>Best Practice:</strong> Use bright, high-contrast images with clear focal points
                                                        <a href="{% url 'ads:ad_size_guide' %}" target="_blank" class="ms-2">
                                                            <i class="fas fa-ruler me-1"></i>View size guide
                                                        </a>
                                                    </div>
                                                </div>
                                                <div class="mb-4" id="aiContentCheckboxContainer">
                                                    <!-- Smart Engine reminder alert - only shown when Smart Engine is not enabled -->
                                                    <div class="alert alert-soft-warning d-flex align-items-center" id="smartEngineReminder" role="alert">
                                                        <i class="fas fa-lightbulb me-2"></i>
                                                        <div>
                                                            <strong>Want AI-powered content?</strong>
                                                            <span>Enable Smart Engine in Basic Info to create professional content.</span>
                                                            <button type="button" class="btn btn-sm btn-outline-warning ms-2" id="goToSmartEngineBtn">
                                                                <i class="fas fa-magic me-1"></i>Enable Smart Engine
                                                            </button>
                                                        </div>
                                                    </div>

                                                    <!-- Hidden checkbox for backward compatibility -->
                                                    <input type="checkbox" id="requiresAi" name="requires_ai" value="true" style="display: none;">
                                                </div>
                                                <div class="d-flex justify-content-between">
                                                    <button type="button" class="btn btn-secondary prev-step" data-prev="step1-tab" data-step-title="Step 1: Basic Info">
                                                        <i class="fas fa-arrow-left me-2"></i> Previous
                                                    </button>
                                                    <button type="button" class="btn btn-primary next-step" data-next="step3-tab" data-step-title="Step 3: Options">
                                                        Next: Options <i class="fas fa-arrow-right ms-2"></i>
                                                    </button>
                                                </div>
                                            </div>

                                            <!-- Step 3: Options -->
                                            <div class="tab-pane fade" id="step3" role="tabpanel" aria-labelledby="step3-tab">
                                                <!-- Welcome Message for Step 3 -->
                                                <div class="step-welcome-card mb-4">
                                                    <div class="d-flex align-items-center">
                                                        <div class="step-icon">
                                                            <i class="fas fa-cogs"></i>
                                                        </div>
                                                        <div class="step-content">
                                                            <h5 class="mb-1">Boost Your Reach</h5>
                                                            <p class="mb-0 text-muted">Add premium features to maximize your ad's impact and reach!</p>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Social Media Boost Section -->
                                                <div class="mb-4">
                                                    <div class="premium-feature-card">
                                                        <div class="feature-header">
                                                            <div class="d-flex align-items-center justify-content-between">
                                                                <div class="feature-info">
                                                                    <h6 class="feature-title">
                                                                        <i class="fab fa-facebook me-2 text-primary"></i>
                                                                        <i class="fab fa-twitter me-2 text-info"></i>
                                                                        <i class="fab fa-instagram me-2 text-danger"></i>
                                                                        Social Media Boost
                                                                        <span class="badge bg-success ms-2">+300% Reach</span>
                                                                    </h6>
                                                                    <p class="feature-description mb-2">
                                                                        Automatically share your ad across multiple social media platforms for maximum exposure
                                                                    </p>
                                                                    <div class="feature-benefits">
                                                                        <span class="benefit-tag"><i class="fas fa-check"></i> Facebook</span>
                                                                        <span class="benefit-tag"><i class="fas fa-check"></i> Twitter</span>
                                                                        <span class="benefit-tag"><i class="fas fa-check"></i> Instagram</span>
                                                                        <span class="benefit-tag"><i class="fas fa-check"></i> LinkedIn</span>
                                                                    </div>
                                                                </div>
                                                                <div class="feature-toggle">
                                                                    <div class="premium-toggle-container">
                                                                        <input class="premium-toggle-input" type="checkbox" id="wantsSocial" name="wants_social" value="true">
                                                                        <label class="premium-toggle-label" for="wantsSocial">
                                                                            <div class="premium-toggle-slider">
                                                                                <div class="premium-toggle-thumb">
                                                                                    <i class="fas fa-share-alt"></i>
                                                                                </div>
                                                                            </div>
                                                                        </label>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="feature-pricing">
                                                            <div class="pricing-details">
                                                                <span class="price-label">Premium Add-on:</span>
                                                                <span class="price-amount">+10 KSH</span>
                                                                <span class="price-note">(Worth 50 KSH value)</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Enhanced Pricing Summary -->
                                                <div class="mb-4">
                                                    <div class="pricing-summary-card">
                                                        <div class="pricing-header">
                                                            <h5 class="mb-0">
                                                                <i class="fas fa-calculator me-2 text-primary"></i>
                                                                Investment Summary
                                                            </h5>
                                                            <span class="pricing-badge">Live Calculation</span>
                                                        </div>
                                                        <div class="pricing-body">
                                                            <div class="pricing-item">
                                                                <div class="pricing-label">
                                                                    <i class="fas fa-tag me-2"></i>
                                                                    Base Price:
                                                                </div>
                                                                <div class="pricing-value" id="basePrice">0.00 KSH</div>
                                                            </div>
                                                            <div class="pricing-item">
                                                                <div class="pricing-label">
                                                                    <i class="fas fa-calendar me-2"></i>
                                                                    Duration:
                                                                </div>
                                                                <div class="pricing-value" id="durationDays">0 days</div>
                                                            </div>
                                                            <div class="pricing-item">
                                                                <div class="pricing-label">
                                                                    <i class="fas fa-map-marker-alt me-2"></i>
                                                                    Location Multiplier:
                                                                </div>
                                                                <div class="pricing-value" id="locationMultiplier">x1.0</div>
                                                            </div>
                                                            <div class="pricing-item">
                                                                <div class="pricing-label">
                                                                    <i class="fas fa-bullseye me-2"></i>
                                                                    Placement Price:
                                                                </div>
                                                                <div class="pricing-value" id="locationPrice">0.00 KSH</div>
                                                            </div>
                                                            <div class="pricing-item">
                                                                <div class="pricing-label">
                                                                    <i class="fas fa-brain me-2"></i>
                                                                    AI Content:
                                                                </div>
                                                                <div class="pricing-value" id="aiPrice">0.00 KSH</div>
                                                            </div>
                                                            <div class="pricing-item">
                                                                <div class="pricing-label">
                                                                    <i class="fas fa-share-alt me-2"></i>
                                                                    Social Media:
                                                                </div>
                                                                <div class="pricing-value" id="socialPrice">0.00 KSH</div>
                                                            </div>
                                                            <div class="pricing-divider"></div>
                                                            <div class="pricing-total">
                                                                <div class="total-label">
                                                                    <i class="fas fa-coins me-2"></i>
                                                                    Total Investment:
                                                                </div>
                                                                <div class="total-value" id="totalPrice">0.00 KSH</div>
                                                            </div>
                                                            <div class="roi-indicator">
                                                                <i class="fas fa-chart-line me-1"></i>
                                                                Expected ROI: <strong class="text-success">300-500%</strong>
                                                            </div>
                                                        </div>

                                                        <!-- Hidden inputs -->
                                                        <input type="hidden" id="basePricingInput" name="base_pricing" value="0.00">
                                                        <input type="hidden" id="finalPricingInput" name="final_pricing" value="0.00">
                                                        <input type="hidden" id="usedAiInput" name="used_ai" value="false">
                                                        <input type="hidden" id="aiSuggestionData" name="ai_suggestion_data" value="">
                                                        <!-- Debug field to see the value -->
                                                        <div class="d-none">Used AI: <span id="usedAiDebug">false</span></div>
                                                    </div>
                                                </div>
                                                <div class="d-flex justify-content-between">
                                                    <button type="button" class="btn btn-secondary prev-step" data-prev="step2-tab" data-step-title="Step 2: Content">
                                                        <i class="fas fa-arrow-left me-2"></i> Previous
                                                    </button>
                                                    <button type="button" class="btn btn-primary next-step" data-next="step4-tab" data-step-title="Step 4: Review">
                                                        Next: Review <i class="fas fa-arrow-right ms-2"></i>
                                                    </button>
                                                </div>
                                            </div>

                                            <!-- Step 4: Review -->
                                            <div class="tab-pane fade" id="step4" role="tabpanel" aria-labelledby="step4-tab">
                                                <!-- Welcome Message for Step 4 -->
                                                <div class="step-welcome-card mb-4">
                                                    <div class="d-flex align-items-center">
                                                        <div class="step-icon">
                                                            <i class="fas fa-check-circle"></i>
                                                        </div>
                                                        <div class="step-content">
                                                            <h5 class="mb-1">Final Review</h5>
                                                            <p class="mb-0 text-muted">Review your ad details and launch your campaign to success!</p>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Enhanced Ad Summary -->
                                                <div class="review-summary-card mb-4">
                                                    <div class="summary-header">
                                                        <h5 class="mb-0">
                                                            <i class="fas fa-clipboard-check me-2 text-primary"></i>
                                                            Campaign Summary
                                                        </h5>
                                                        <span class="summary-badge">Ready to Launch</span>
                                                    </div>
                                                    <div class="summary-body">
                                                        <!-- Ad Title Review -->
                                                        <div class="review-item">
                                                            <div class="review-icon">
                                                                <i class="fas fa-heading text-primary"></i>
                                                            </div>
                                                            <div class="review-content">
                                                                <div class="review-label">Ad Title</div>
                                                                <div class="review-value" id="reviewTitle">Your ad title will appear here</div>
                                                            </div>
                                                            <div class="review-status">
                                                                <i class="fas fa-check-circle text-success"></i>
                                                            </div>
                                                        </div>

                                                        <!-- Ad Type Review -->
                                                        <div class="review-item">
                                                            <div class="review-icon">
                                                                <i class="fas fa-tag text-info"></i>
                                                            </div>
                                                            <div class="review-content">
                                                                <div class="review-label">Ad Type</div>
                                                                <div class="review-value" id="reviewType">Standard Display Ad</div>
                                                            </div>
                                                            <div class="review-status">
                                                                <i class="fas fa-check-circle text-success"></i>
                                                            </div>
                                                        </div>

                                                        <!-- Duration Review -->
                                                        <div class="review-item">
                                                            <div class="review-icon">
                                                                <i class="fas fa-calendar text-warning"></i>
                                                            </div>
                                                            <div class="review-content">
                                                                <div class="review-label">Campaign Duration</div>
                                                                <div class="review-value" id="reviewDuration">7 days</div>
                                                            </div>
                                                            <div class="review-status">
                                                                <i class="fas fa-check-circle text-success"></i>
                                                            </div>
                                                        </div>

                                                        <!-- Placement Review -->
                                                        <div class="review-item">
                                                            <div class="review-icon">
                                                                <i class="fas fa-map-marker-alt text-danger"></i>
                                                            </div>
                                                            <div class="review-content">
                                                                <div class="review-label">Ad Placement</div>
                                                                <div class="review-value" id="reviewLocation">Premium location</div>
                                                            </div>
                                                            <div class="review-status">
                                                                <i class="fas fa-check-circle text-success"></i>
                                                            </div>
                                                        </div>

                                                        <!-- Content Review -->
                                                        <div class="review-item">
                                                            <div class="review-icon">
                                                                <i class="fas fa-file-alt text-secondary"></i>
                                                            </div>
                                                            <div class="review-content">
                                                                <div class="review-label">Ad Content</div>
                                                                <div class="review-value content-preview" id="reviewContent">Your ad content will appear here</div>
                                                            </div>
                                                            <div class="review-status">
                                                                <i class="fas fa-check-circle text-success"></i>
                                                            </div>
                                                        </div>

                                                        <!-- Performance Metrics -->
                                                        <div class="performance-metrics">
                                                            <div class="metric-item">
                                                                <div class="metric-icon">
                                                                    <i class="fas fa-eye"></i>
                                                                </div>
                                                                <div class="metric-content">
                                                                    <div class="metric-value" id="reviewImpressions">Quality</div>
                                                                    <div class="metric-label">Reach</div>
                                                                </div>
                                                            </div>
                                                            <div class="metric-item">
                                                                <div class="metric-icon">
                                                                    <i class="fas fa-mouse-pointer"></i>
                                                                </div>
                                                                <div class="metric-content">
                                                                    <div class="metric-value">Strong</div>
                                                                    <div class="metric-label">Engagement</div>
                                                                </div>
                                                            </div>
                                                            <div class="metric-item">
                                                                <div class="metric-icon">
                                                                    <i class="fas fa-chart-line"></i>
                                                                </div>
                                                                <div class="metric-content">
                                                                    <div class="metric-value">Proven</div>
                                                                    <div class="metric-label">Results</div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <!-- Investment Summary -->
                                                        <div class="investment-summary">
                                                            <div class="investment-header">
                                                                <h6><i class="fas fa-coins me-2"></i>Total Investment</h6>
                                                            </div>
                                                            <div class="investment-amount" id="reviewPrice">0.00 KSH</div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Launch Confidence Indicators -->
                                                <div class="confidence-indicators mb-4">
                                                    <h6 class="mb-3">
                                                        <i class="fas fa-rocket me-2 text-primary"></i>
                                                        Launch Readiness
                                                    </h6>
                                                    <div class="row">
                                                        <div class="col-md-4">
                                                            <div class="confidence-item">
                                                                <div class="confidence-icon success">
                                                                    <i class="fas fa-check"></i>
                                                                </div>
                                                                <div class="confidence-text">
                                                                    <strong>Content Quality</strong>
                                                                    <small>Optimized for engagement</small>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="confidence-item">
                                                                <div class="confidence-icon success">
                                                                    <i class="fas fa-check"></i>
                                                                </div>
                                                                <div class="confidence-text">
                                                                    <strong>Target Audience</strong>
                                                                    <small>Well-defined reach</small>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="confidence-item">
                                                                <div class="confidence-icon success">
                                                                    <i class="fas fa-check"></i>
                                                                </div>
                                                                <div class="confidence-text">
                                                                    <strong>Budget Allocation</strong>
                                                                    <small>Cost-effective setup</small>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="d-flex justify-content-between">
                                                    <button type="button" class="btn btn-secondary prev-step" data-prev="step3-tab" data-step-title="Step 3: Options">
                                                        <i class="fas fa-arrow-left me-2"></i> Previous
                                                    </button>
                                                    <button type="button" class="btn btn-outline-info btn-sm me-2" onclick="window.setUsedAiTrue()">
                                                        <i class="fas fa-check-circle me-1"></i> Set Used AI True
                                                    </button>
                                                    <button type="submit" class="btn btn-success" id="submitAd">
                                                        <i class="fas fa-check me-2"></i> Create Ad
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4">
                            <!-- Enhanced Preview Panel -->
                            <div class="dashboard-card preview-panel-enhanced">
                                <div class="card-header">
                                    <h3 class="card-title">
                                        <i class="fas fa-eye me-2 text-primary"></i>
                                        Live Ad Preview
                                    </h3>
                                    <div class="preview-controls">
                                        <button class="btn btn-sm btn-outline-primary" id="refreshPreview" title="Refresh Preview">
                                            <i class="fas fa-sync-alt"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <!-- Preview Location Selector -->
                                    <div class="mb-4">
                                        <label for="previewLocationType" class="form-label">
                                            <i class="fas fa-map-marker-alt me-1 text-secondary"></i>
                                            Preview Location:
                                        </label>
                                        <select class="form-select preview-location-select" id="previewLocationType">
                                            <option value="default">📱 Default (Mobile-Friendly)</option>
                                            <option value="header">🔝 Header Banner</option>
                                            <option value="sidebar">📋 Sidebar</option>
                                            <option value="content">📄 Content Area</option>
                                            <option value="footer">🔻 Footer</option>
                                            <option value="popup">🎯 Popup Modal</option>
                                        </select>
                                    </div>

                                    <!-- Enhanced Preview Container -->
                                    <div class="ad-preview-container-enhanced">
                                        <!-- Preview Location Label -->
                                        <div class="preview-location-badge" id="previewLocationLabel">
                                            <i class="fas fa-mobile-alt me-1"></i>
                                            Default Preview
                                        </div>

                                        <!-- Main Preview Wrapper -->
                                        <div class="ad-preview-wrapper-enhanced" id="previewWrapper">
                                            <!-- Ad Title Section -->
                                            <div class="ad-preview-title-section">
                                                <div class="ad-preview-title-enhanced" id="previewTitle">
                                                    Your Ad Title Will Appear Here
                                                </div>
                                                <div class="ad-preview-meta">
                                                    <span class="preview-badge">SPONSORED</span>
                                                </div>
                                            </div>

                                            <!-- Ad Content Section -->
                                            <div class="ad-preview-content-section">
                                                <div class="ad-preview-content-enhanced" id="previewContent">
                                                    Your compelling ad content will appear here as you type. This preview updates in real-time to show exactly how your ad will look to potential customers.
                                                </div>
                                            </div>

                                            <!-- Ad Image Section -->
                                            <div class="ad-preview-image-section" id="previewImageSection">
                                                <div class="ad-preview-image-enhanced" id="previewImage">
                                                    <img src="{% static 'img/ad-placeholder.jpg' %}" alt="Ad Preview" class="preview-image">
                                                    <div class="image-overlay">
                                                        <i class="fas fa-image"></i>
                                                        <span>Upload Image</span>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Call to Action Section -->
                                            <div class="ad-preview-cta-section">
                                                <a href="#" class="ad-preview-cta-enhanced" id="previewCta">
                                                    <i class="fas fa-external-link-alt me-1"></i>
                                                    Learn More
                                                </a>
                                            </div>
                                        </div>

                                        <!-- Preview Information -->
                                        <div class="preview-info-panel">
                                            <div class="preview-stats">
                                                <div class="stat-item">
                                                    <span class="stat-label">Size:</span>
                                                    <span class="stat-value" id="previewSizeInfo">300x250px</span>
                                                </div>
                                                <div class="stat-item">
                                                    <span class="stat-label">Type:</span>
                                                    <span class="stat-value" id="previewTypeInfo">Display Ad</span>
                                                </div>
                                                <div class="stat-item">
                                                    <span class="stat-label">Format:</span>
                                                    <span class="stat-value" id="previewFormatInfo">Responsive</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Preview Tips -->
                                    <div class="preview-tips mt-3">
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle me-2"></i>
                                            <strong>Preview Tips:</strong> This preview shows how your ad will appear to users. Content automatically adjusts to fit different screen sizes and placements.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Pro Tips Panel -->
                            <div class="dashboard-card mt-3">
                                <div class="card-header d-flex justify-content-between align-items-center" id="proTipsHeader">
                                    <h3 class="card-title mb-0">
                                        <i class="fas fa-lightbulb me-2"></i>Pro Tips
                                    </h3>
                                    <button class="btn btn-link p-0" type="button" data-bs-toggle="collapse" data-bs-target="#proTipsContent" aria-expanded="false" aria-controls="proTipsContent">
                                        <i class="fas fa-chevron-down"></i>
                                    </button>
                                </div>
                                <div id="proTipsContent" class="collapse">
                                    <div class="card-body">
                                        <ul class="list-group list-group-flush">
                                            <li class="list-group-item">
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                Keep your ad title short and catchy (under 60 characters)
                                            </li>
                                            <li class="list-group-item">
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                Use high-quality images for better engagement
                                            </li>
                                            <li class="list-group-item">
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                Target your audience specifically for better results
                                            </li>
                                            <li class="list-group-item">
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                Consider using AI Smart Engine to optimize content
                                            </li>
                                            <li class="list-group-item">
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                Test different ad placements for maximum visibility
                                            </li>
                                            <li class="list-group-item">
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                Include a clear call-to-action in your ad
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<!-- Make sure Bootstrap is loaded -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<!-- Select2 Library -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<!-- Global variables for JavaScript -->
<script src="{% static 'ads/js/ad-create-enterprise-globals.js' %}"></script>
<script>
    // Set campaign search URL for use in other JS files
    window.campaignSearchUrl = "{% url 'ads:api_search_campaigns' %}";
</script>

<!-- External JavaScript files -->
<!-- Ad Create Consolidated Script - Includes pro tips functionality -->
<script src="{% static 'ads/js/ad-create-consolidated.js' %}"></script>
<!-- Select2 Campaign Dropdown (kept separate as it's used elsewhere) -->
<script src="{% static 'ads/js/select2-campaign-dropdown.js' %}"></script>
<!-- Notification Bell Fix Script -->
<script src="{% static 'ads/js/notification-bell-fix.js' %}"></script>

<!-- AI Suggestions Consolidated Script - Production version -->
<script src="{% static 'ads/js/ai-suggestions-consolidated.js' %}"></script>

<!-- Tippy.js for advanced tooltips -->
<script src="https://unpkg.com/@popperjs/core@2"></script>
<script src="https://unpkg.com/tippy.js@6"></script>

<!-- Enhanced UX JavaScript -->
<script src="{% static 'ads/js/ad-create-enhanced-ux.js' %}"></script>

<!-- Smart Engine CSS -->
<link rel="stylesheet" href="{% static 'ads/css/smart-engine.css' %}">
{% endblock %}

{% block extra_styles %}
<!-- All styles moved to external CSS file: ad-create-enterprise-styles.css -->
<link rel="stylesheet" href="{% static 'ads/css/footer-margin-fix.css' %}">
{% endblock %}