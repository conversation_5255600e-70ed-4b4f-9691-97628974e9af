from django.core.management.base import BaseCommand
from django.utils import timezone
from django.contrib.auth.models import User
from ads.models import Ad, AdType, AdLocation
from datetime import timedelta
from decimal import Decimal

class Command(BaseCommand):
    help = 'Creates a test featured ad for the Content Inline location'

    def handle(self, *args, **options):
        # Get or create a test user
        try:
            user = User.objects.get(username='peter')
            self.stdout.write(self.style.SUCCESS(f"Using existing user: {user.username}"))
        except User.DoesNotExist:
            self.stdout.write(self.style.ERROR('Superuser "peter" not found. Please create it first.'))
            return
            
        # Get Featured Ad type
        try:
            featured_type = AdType.objects.get(name='Featured Ad')
            self.stdout.write(self.style.SUCCESS(f"Using existing ad type: {featured_type.name}"))
        except AdType.DoesNotExist:
            self.stdout.write(self.style.ERROR('Featured Ad type not found. Please run create_featured_ad_type command first.'))
            return
        
        # Get Content Inline location
        try:
            content_inline = AdLocation.objects.get(name='Content Inline')
            self.stdout.write(self.style.SUCCESS(f"Using existing ad location: {content_inline.name}"))
        except AdLocation.DoesNotExist:
            self.stdout.write(self.style.ERROR('Content Inline location not found. Please run create_featured_ad_type command first.'))
            return
        
        # Create test featured ad
        ad_title = 'Enterprise AI Solutions'
        
        # Delete existing test ad with this title if it exists
        existing_ad = Ad.objects.filter(title=ad_title, ad_location=content_inline)
        if existing_ad.exists():
            existing_ad.delete()
            self.stdout.write(self.style.WARNING(f"Deleted existing ad with title: {ad_title}"))
        
        # Create new test ad
        ad = Ad.objects.create(
            user=user,
            ad_type=featured_type,
            ad_location=content_inline,
            title=ad_title,
            content='Transform your business with our enterprise-grade AI solutions. Automate processes, gain insights, and drive innovation with cutting-edge artificial intelligence technology.',
            cta_link='https://example.com/enterprise-ai',
            target_location='Kenya',
            target_audience='Business',
            start_date=timezone.now(),
            end_date=timezone.now() + timedelta(days=30),
            status='approved',
            base_pricing=Decimal('150.00'),
            final_pricing=Decimal('150.00') * content_inline.price_multiplier,
        )
        
        self.stdout.write(self.style.SUCCESS(f"Created Featured Ad: {ad.title} for {content_inline.name} location"))
