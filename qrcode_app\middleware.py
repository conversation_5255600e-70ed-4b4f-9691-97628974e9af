"""
Middleware for QR Code Generator application
MODULE 6: Usage Limits & Tiered Features - Plan enforcement middleware
"""

from django.contrib.auth import login
from django.contrib.auth.models import User
from django.conf import settings
from django.http import HttpResponseForbidden, JsonResponse
from django.shortcuts import redirect
from django.contrib import messages
from django.urls import reverse
from django.utils import timezone
import logging

logger = logging.getLogger(__name__)


class AutoLoginMiddleware:
    """
    Middleware to automatically log in a superuser in development mode
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Only auto-login in DEBUG mode and if user is not already authenticated
        if settings.DEBUG and not request.user.is_authenticated:
            # Skip auto-login for admin URLs to avoid conflicts
            if not request.path.startswith('/admin/'):
                try:
                    # Try to get the 'admin' user first, then 'peter' as fallback
                    try:
                        auto_user = User.objects.get(username='admin', is_superuser=True)
                    except User.DoesNotExist:
                        auto_user = User.objects.get(username='peter', is_superuser=True)

                    # Log in the user automatically with explicit backend
                    login(request, auto_user, backend='django.contrib.auth.backends.ModelBackend')

                except User.DoesNotExist:
                    # No superuser found, continue without auto-login
                    pass

        response = self.get_response(request)
        return response


class EnforcePlanLimitsMiddleware:
    """
    MODULE 6: Middleware to enforce subscription plan limits across the application
    """

    def __init__(self, get_response):
        self.get_response = get_response

        # Define protected paths and their required features
        self.protected_paths = {
            # QR Code creation limits
            '/generate-qr-code/': {'feature': 'qr_creation', 'limit_check': 'can_create_qr_code'},
            '/qr/create/': {'feature': 'qr_creation', 'limit_check': 'can_create_qr_code'},

            # AI Landing Pages
            '/monetization/create-ai-page/': {'feature': 'ai', 'limit_check': 'can_create_ai_page'},

            # Webhook Integration
            '/monetization/webhooks/create/': {'feature': 'webhooks', 'limit_check': 'can_create_webhook'},
            '/monetization/webhooks/': {'feature': 'webhooks', 'plan_check': True},

            # Scan Alerts
            '/alerts/create/': {'feature': 'alerts', 'limit_check': 'can_create_scan_alert'},
            '/alerts/': {'feature': 'alerts', 'plan_check': True},

            # Dynamic Redirects
            '/monetization/edit-redirect/': {'feature': 'webhooks', 'limit_check': 'can_create_dynamic_redirect'},

            # Advanced Analytics
            '/analytics/advanced/': {'feature': 'advanced_analytics', 'plan_check': True},
            '/qr-map/': {'feature': 'advanced_analytics', 'plan_check': True},

            # Batch Processing
            '/batch-processing/': {'feature': 'batch_processing', 'plan_check': True},
            '/bulk-generation/': {'feature': 'batch_processing', 'plan_check': True},

            # API Access
            '/api/': {'feature': 'api_access', 'plan_check': True},

            # Branding
            '/branding/': {'feature': 'branding', 'plan_check': True},
        }

    def __call__(self, request):
        # Skip middleware for non-authenticated users
        if not request.user.is_authenticated:
            return self.get_response(request)

        # Skip middleware for superusers
        if request.user.is_superuser:
            return self.get_response(request)

        # Check if current path requires plan limits
        response = self.check_plan_limits(request)
        if response:
            return response

        return self.get_response(request)

    def check_plan_limits(self, request):
        """Check if user's plan allows access to the requested feature"""
        from .models import Subscription, Plan

        path = request.path

        # Check if path matches any protected paths
        protection_config = None
        for protected_path, config in self.protected_paths.items():
            if path.startswith(protected_path):
                protection_config = config
                break

        if not protection_config:
            return None

        # Get user's subscription
        try:
            subscription = Subscription.objects.get(user=request.user)
        except Subscription.DoesNotExist:
            # Create a default free subscription for users without one
            default_plan = Plan.objects.filter(is_default=True).first()
            if default_plan:
                subscription = Subscription.objects.create(user=request.user, plan=default_plan)
            else:
                return self.handle_no_plan_error(request)

        if not subscription.plan:
            return self.handle_no_plan_error(request)

        # Check feature access
        feature = protection_config.get('feature')
        if feature and not subscription.plan.is_feature_enabled(feature):
            return self.handle_feature_not_available(request, feature)

        # Check specific limits
        limit_check = protection_config.get('limit_check')
        if limit_check:
            limit_method = getattr(subscription, limit_check, None)
            if limit_method and not limit_method():
                return self.handle_limit_exceeded(request, limit_check)

        return None

    def handle_no_plan_error(self, request):
        """Handle case where user has no subscription plan"""
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'success': False,
                'error': 'No subscription plan found. Please contact support.',
                'redirect_url': reverse('pricing')
            }, status=403)

        messages.error(request, 'No subscription plan found. Please contact support.')
        return redirect('pricing')

    def handle_feature_not_available(self, request, feature):
        """Handle case where feature is not available on user's plan"""
        feature_names = {
            'ai': 'AI Landing Pages',
            'webhooks': 'Webhook Integration',
            'alerts': 'Scan Alerts',
            'advanced_analytics': 'Advanced Analytics',
            'branding': 'Custom Branding',
            'batch_processing': 'Batch Processing',
            'api_access': 'API Access'
        }

        feature_name = feature_names.get(feature, feature.title())

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'success': False,
                'error': f'{feature_name} is not available on your current plan.',
                'redirect_url': reverse('pricing')
            }, status=403)

        messages.warning(request, f'{feature_name} is not available on your current plan. Please upgrade to access this feature.')
        return redirect('pricing')

    def handle_limit_exceeded(self, request, limit_check):
        """Handle case where user has exceeded their plan limits"""
        limit_messages = {
            'can_create_qr_code': 'You have reached your QR code creation limit for this plan.',
            'can_create_ai_page': 'You have reached your AI landing page limit for this plan.',
            'can_create_webhook': 'You have reached your webhook endpoint limit for this plan.',
            'can_create_scan_alert': 'You have reached your scan alert limit for this plan.',
            'can_create_dynamic_redirect': 'You have reached your dynamic redirect limit for this plan.',
            'can_scan_this_month': 'You have reached your monthly scan limit for this plan.'
        }

        message = limit_messages.get(limit_check, 'You have reached a limit for your current plan.')

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'success': False,
                'error': message,
                'redirect_url': reverse('pricing')
            }, status=403)

        messages.warning(request, f'{message} Please upgrade to continue.')
        return redirect('pricing')
