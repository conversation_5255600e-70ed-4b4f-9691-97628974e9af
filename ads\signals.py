"""
Signal handlers for the ads app
"""
from django.db.models.signals import pre_save, post_save
from django.dispatch import receiver
from django.contrib.auth.models import User

from .models import Ad, Transaction, Campaign

# Store previous state for status change detection
@receiver(pre_save, sender=Ad)
def store_previous_ad_status(sender, instance, **kwargs):
    """
    Store the previous status of an ad before it's saved
    """
    if instance.pk:
        try:
            previous = Ad.objects.get(pk=instance.pk)
            instance._previous_status = previous.status
        except Ad.DoesNotExist:
            instance._previous_status = None
    else:
        instance._previous_status = None

@receiver(pre_save, sender=Transaction)
def store_previous_transaction_status(sender, instance, **kwargs):
    """
    Store the previous status of a transaction before it's saved
    """
    if instance.pk:
        try:
            previous = Transaction.objects.get(pk=instance.pk)
            instance._previous_status = previous.status
        except Transaction.DoesNotExist:
            instance._previous_status = None
    else:
        instance._previous_status = None

@receiver(pre_save, sender=Campaign)
def store_previous_campaign_status(sender, instance, **kwargs):
    """
    Store the previous status of a campaign before it's saved
    """
    if instance.pk:
        try:
            previous = Campaign.objects.get(pk=instance.pk)
            instance._previous_status = previous.status
        except Campaign.DoesNotExist:
            instance._previous_status = None
    else:
        instance._previous_status = None
