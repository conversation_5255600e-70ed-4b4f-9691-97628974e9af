"""
AI Services Tests
"""
import json
from unittest.mock import patch, MagicMock
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User

class AIServicesTestCase(TestCase):
    """Test case for AI Services"""

    def setUp(self):
        """Set up test data"""
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        self.client.login(username='testuser', password='testpassword')

        # Mock suggestions
        self.mock_suggestions = [
            {
                "title": "Test Title 1",
                "content": "Test Content 1"
            },
            {
                "title": "Test Title 2",
                "content": "Test Content 2"
            },
            {
                "title": "Test Title 3",
                "content": "Test Content 3"
            }
        ]

    @patch('ai_services.clients.MistralAIClient.generate_ad_suggestions')
    def test_generate_ad_suggestions(self, mock_generate):
        """Test generate_ad_suggestions view"""
        # Mock the generate_ad_suggestions method
        mock_generate.return_value = self.mock_suggestions

        # Make a POST request to the view
        url = reverse('ai_services:generate_ad_suggestions')
        data = {
            'language': 'english',
            'business_type': 'Test Business',
            'target_audience': 'Test Audience',
            'tone': 'professional'
        }
        response = self.client.post(
            url,
            data=json.dumps(data),
            content_type='application/json'
        )

        # Check that the response is successful
        self.assertEqual(response.status_code, 200)

        # Check that the response contains the mock suggestions
        response_data = json.loads(response.content)
        self.assertTrue(response_data['success'])
        self.assertEqual(response_data['suggestions'], self.mock_suggestions)

        # Check that the generate_ad_suggestions method was called with the correct arguments
        mock_generate.assert_called_once_with(
            language='english',
            business_type='Test Business',
            target_audience='Test Audience',
            tone='professional',
            num_suggestions=3
        )

    def test_generate_ad_suggestions_unauthenticated(self):
        """Test generate_ad_suggestions view with unauthenticated user"""
        # Logout the user
        self.client.logout()

        # Make a POST request to the view
        url = reverse('ai_services:generate_ad_suggestions')
        data = {
            'language': 'english',
            'business_type': 'Test Business',
            'target_audience': 'Test Audience',
            'tone': 'professional'
        }
        response = self.client.post(
            url,
            data=json.dumps(data),
            content_type='application/json'
        )

        # Check that the response is a redirect to the login page
        self.assertEqual(response.status_code, 302)
        self.assertTrue(response.url.startswith('/accounts/login/'))
