from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from ads.models import AdType, AdLocation, Ad
from decimal import Decimal
import random
from datetime import timedelta

class Command(BaseCommand):
    help = 'Creates sample ads with Kenyan context for testing'

    def handle(self, *args, **kwargs):
        # Check if we have a user to assign ads to
        try:
            user = User.objects.get(username='peter')  # Using the superuser account
        except User.DoesNotExist:
            self.stdout.write(self.style.ERROR('User "peter" not found. Please create this user first.'))
            return

        # Get all ad types and locations
        ad_types = AdType.objects.all()
        ad_locations = AdLocation.objects.all()

        if not ad_types:
            self.stdout.write(self.style.ERROR('No ad types found. Please create ad types first.'))
            return

        if not ad_locations:
            self.stdout.write(self.style.ERROR('No ad locations found. Please create ad locations first.'))
            return

        # Sample Kenyan ads data
        kenyan_ads = [
            {
                'title': 'Safaricom Home Fiber - Fast Internet for Your Home',
                'content': 'Experience lightning-fast internet speeds with Safaricom Home Fiber. '
                          'Starting at just KSh 2,999 per month for 8Mbps. Perfect for streaming, '
                          'working from home, and online learning. Available in Nairobi, Mombasa, '
                          'Kisumu, and other major towns.',
                'cta_link': 'https://www.safaricom.co.ke/personal/internet/home-fibre',
                'target_location': 'Nairobi, Kenya',
                'target_audience': 'Homeowners, Professionals, 25-45 years',
                'status': 'active',
            },
            {
                'title': 'KCB Bank - Affordable Home Loans',
                'content': 'Make your dream home a reality with KCB Bank home loans. '
                          'Competitive interest rates starting from 13% p.a. Flexible repayment '
                          'periods of up to 25 years. No hidden charges. Visit any KCB branch today!',
                'cta_link': 'https://ke.kcbgroup.com/personal-banking/get-a-loan/mortgages/kcb-mortgage',
                'target_location': 'Nationwide, Kenya',
                'target_audience': 'Working professionals, 30-50 years',
                'status': 'active',
            },
            {
                'title': 'Naivas Supermarket - Weekly Offers',
                'content': 'Shop at Naivas for the best deals this week! '
                          'Get 20% off on all fresh produce, 15% off on household items, and amazing '
                          'discounts on electronics. Valid until Sunday. Visit your nearest Naivas store today!',
                'cta_link': 'https://www.naivas.co.ke/offers',
                'target_location': 'Nairobi, Mombasa, Kisumu, Kenya',
                'target_audience': 'Shoppers, Families, 25-60 years',
                'status': 'active',
            },
            {
                'title': 'Jumia Kenya - End of Month Sale',
                'content': 'Jumia End of Month Sale is here! Up to 70% off on electronics, fashion, '
                          'home appliances, and more. Free delivery on orders above KSh 1,500. '
                          'Limited time offer. Shop now before stocks run out!',
                'cta_link': 'https://www.jumia.co.ke/mlp-end-month-sale/',
                'target_location': 'Nationwide, Kenya',
                'target_audience': 'Online shoppers, 18-40 years',
                'status': 'active',
            },
            {
                'title': 'Equity Bank - Eazzy Banking App',
                'content': 'Bank from anywhere, anytime with the Equity Eazzy Banking App. '
                          'Transfer money, pay bills, apply for loans, and more - all from your smartphone. '
                          'Download the app today and enjoy convenient banking!',
                'cta_link': 'https://equitygroupholdings.com/ke/eazzy-banking',
                'target_location': 'Nationwide, Kenya',
                'target_audience': 'Bank customers, Smartphone users, 18-65 years',
                'status': 'active',
            },
            {
                'title': 'Two Rivers Mall - Weekend Family Fun',
                'content': 'Bring your family to Two Rivers Mall this weekend for fun activities! '
                          'Enjoy the water park, gaming arcade, and special discounts at selected stores. '
                          'Free face painting for kids on Saturday and Sunday from 10am to 4pm.',
                'cta_link': 'https://www.tworivers.co.ke/events',
                'target_location': 'Nairobi, Kenya',
                'target_audience': 'Families with children, 25-45 years',
                'status': 'active',
            },
            {
                'title': 'Tusker - Proudly Kenyan',
                'content': 'Celebrate Kenyan moments with a Tusker! The authentic taste of Kenya since 1922. '
                          'Available at all major supermarkets, bars, and restaurants nationwide. '
                          'Remember to drink responsibly. Not for sale to persons under 18 years.',
                'cta_link': 'https://www.eabl.com/en/brands/tusker',
                'target_location': 'Nationwide, Kenya',
                'target_audience': 'Adults, 21-55 years',
                'status': 'active',
            },
            {
                'title': 'Maasai Mara Safari - Special Offer',
                'content': 'Experience the magic of the Maasai Mara with our special offer! '
                          '3 days, 2 nights safari package starting at KSh 25,000 per person. '
                          'Includes accommodation, meals, game drives, and transport from Nairobi. '
                          'Book now for the wildebeest migration season!',
                'cta_link': 'https://www.kenyasafaris.com/maasai-mara-safari',
                'target_location': 'Nairobi, Kenya',
                'target_audience': 'Tourists, Local travelers, 25-65 years',
                'status': 'active',
            },
        ]

        # Create ads
        created_count = 0
        for i, ad_data in enumerate(kenyan_ads):
            # Assign different ad types and locations to each ad
            ad_type = ad_types[i % len(ad_types)]
            ad_location = ad_locations[i % len(ad_locations)]
            
            # Calculate base and final pricing
            base_price = ad_type.base_price
            final_price = base_price * ad_location.price_multiplier
            
            # Set random start and end dates
            start_date = timezone.now() + timedelta(days=random.randint(1, 7))
            end_date = start_date + timedelta(days=random.randint(14, 30))
            
            # Create the ad
            ad = Ad.objects.create(
                user=user,
                ad_type=ad_type,
                ad_location=ad_location,
                title=ad_data['title'],
                content=ad_data['content'],
                cta_link=ad_data['cta_link'],
                target_location=ad_data['target_location'],
                target_audience=ad_data['target_audience'],
                start_date=start_date,
                end_date=end_date,
                requires_ai=random.choice([True, False]),
                wants_social=random.choice([True, False]),
                base_pricing=base_price,
                final_pricing=final_price,
                status=ad_data['status'],
                impressions=random.randint(100, 5000),
                clicks=random.randint(10, 500)
            )
            
            created_count += 1
            self.stdout.write(self.style.SUCCESS(
                f'Created ad: {ad.title} - Type: {ad_type.name}, Location: {ad_location.name}'
            ))
        
        self.stdout.write(self.style.SUCCESS(f'Successfully created {created_count} sample ads'))
