{% extends 'base.html' %}
{% load static %}

{% block title %}Edit Webhook{% endblock %}

{% block extra_css %}
<style>
.webhook-form-container {
    padding: 2rem 0;
}

.form-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.form-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    text-align: center;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-check {
    margin-bottom: 1rem;
}

.form-check-input {
    margin-top: 0.25rem;
}

.form-check-label {
    font-weight: 500;
}

.help-text {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.btn-update {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-update:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    color: white;
}

.btn-cancel {
    background: #6c757d;
    border: none;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    margin-right: 1rem;
}

.webhook-stats {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.stat-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-weight: 500;
    color: #6c757d;
}

.stat-value {
    font-weight: 600;
    color: #333;
}

.success-rate {
    font-weight: bold;
}

.success-high { color: #28a745; }
.success-medium { color: #ffc107; }
.success-low { color: #dc3545; }

.error-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.error-list li {
    background: #f8d7da;
    color: #721c24;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    margin-bottom: 0.5rem;
}

.webhook-url-display {
    font-family: 'Courier New', monospace;
    background: #f8f9fa;
    padding: 0.75rem;
    border-radius: 8px;
    font-size: 0.9rem;
    word-break: break-all;
    margin-bottom: 1rem;
}
</style>
{% endblock %}

{% block content %}
<div class="container webhook-form-container">
    <!-- Header -->
    <div class="form-header">
        <h1 class="mb-3">
            <i class="fas fa-edit me-3"></i>Edit Webhook
        </h1>
        <p class="mb-0">Update your webhook configuration and settings</p>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Form -->
            <div class="form-card">
                <form method="post" id="webhookForm">
                    {% csrf_token %}
                    
                    <!-- QR Code Selection -->
                    <div class="form-group">
                        <label for="{{ form.qr_code.id_for_label }}" class="form-label">
                            {{ form.qr_code.label }}
                        </label>
                        {{ form.qr_code }}
                        {% if form.qr_code.help_text %}
                            <div class="help-text">{{ form.qr_code.help_text }}</div>
                        {% endif %}
                        {% if form.qr_code.errors %}
                            <ul class="error-list">
                                {% for error in form.qr_code.errors %}
                                    <li>{{ error }}</li>
                                {% endfor %}
                            </ul>
                        {% endif %}
                    </div>

                    <!-- Webhook URL -->
                    <div class="form-group">
                        <label for="{{ form.url.id_for_label }}" class="form-label">
                            {{ form.url.label }}
                        </label>
                        {{ form.url }}
                        {% if form.url.help_text %}
                            <div class="help-text">{{ form.url.help_text }}</div>
                        {% endif %}
                        {% if form.url.errors %}
                            <ul class="error-list">
                                {% for error in form.url.errors %}
                                    <li>{{ error }}</li>
                                {% endfor %}
                            </ul>
                        {% endif %}
                    </div>

                    <!-- Trigger Options -->
                    <div class="form-group">
                        <label class="form-label">Trigger Events</label>
                        
                        <div class="form-check">
                            {{ form.trigger_on_scan }}
                            <label class="form-check-label" for="{{ form.trigger_on_scan.id_for_label }}">
                                {{ form.trigger_on_scan.label }}
                            </label>
                            {% if form.trigger_on_scan.help_text %}
                                <div class="help-text">{{ form.trigger_on_scan.help_text }}</div>
                            {% endif %}
                        </div>

                        <div class="form-check">
                            {{ form.trigger_on_alert }}
                            <label class="form-check-label" for="{{ form.trigger_on_alert.id_for_label }}">
                                {{ form.trigger_on_alert.label }}
                            </label>
                            {% if form.trigger_on_alert.help_text %}
                                <div class="help-text">{{ form.trigger_on_alert.help_text }}</div>
                            {% endif %}
                        </div>

                        {% if form.trigger_on_scan.errors or form.trigger_on_alert.errors %}
                            <ul class="error-list">
                                {% for error in form.trigger_on_scan.errors %}
                                    <li>{{ error }}</li>
                                {% endfor %}
                                {% for error in form.trigger_on_alert.errors %}
                                    <li>{{ error }}</li>
                                {% endfor %}
                            </ul>
                        {% endif %}
                    </div>

                    <!-- Active Status -->
                    <div class="form-group">
                        <div class="form-check">
                            {{ form.active }}
                            <label class="form-check-label" for="{{ form.active.id_for_label }}">
                                {{ form.active.label }}
                            </label>
                            {% if form.active.help_text %}
                                <div class="help-text">{{ form.active.help_text }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Secret Key -->
                    <div class="form-group">
                        <label for="{{ form.secret_key.id_for_label }}" class="form-label">
                            {{ form.secret_key.label }}
                        </label>
                        {{ form.secret_key }}
                        {% if form.secret_key.help_text %}
                            <div class="help-text">{{ form.secret_key.help_text }}</div>
                        {% endif %}
                        {% if form.secret_key.errors %}
                            <ul class="error-list">
                                {% for error in form.secret_key.errors %}
                                    <li>{{ error }}</li>
                                {% endfor %}
                            </ul>
                        {% endif %}
                    </div>

                    <!-- Form Errors -->
                    {% if form.non_field_errors %}
                        <ul class="error-list">
                            {% for error in form.non_field_errors %}
                                <li>{{ error }}</li>
                            {% endfor %}
                        </ul>
                    {% endif %}

                    <!-- Submit Buttons -->
                    <div class="form-group">
                        <a href="{% url 'webhook_dashboard' %}" class="btn btn-cancel">Cancel</a>
                        <button type="submit" class="btn btn-update">
                            <i class="fas fa-save me-2"></i>Update Webhook
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Webhook Statistics -->
            <div class="webhook-stats">
                <h5 class="mb-3">
                    <i class="fas fa-chart-bar me-2"></i>Webhook Statistics
                </h5>
                
                <div class="stat-row">
                    <span class="stat-label">Total Calls:</span>
                    <span class="stat-value">{{ webhook.total_calls }}</span>
                </div>
                
                <div class="stat-row">
                    <span class="stat-label">Successful Calls:</span>
                    <span class="stat-value">{{ webhook.successful_calls }}</span>
                </div>
                
                <div class="stat-row">
                    <span class="stat-label">Success Rate:</span>
                    <span class="stat-value">
                        <span class="success-rate {% if webhook.get_success_rate >= 90 %}success-high{% elif webhook.get_success_rate >= 70 %}success-medium{% else %}success-low{% endif %}">
                            {{ webhook.get_success_rate|floatformat:1 }}%
                        </span>
                    </span>
                </div>
                
                <div class="stat-row">
                    <span class="stat-label">Last Called:</span>
                    <span class="stat-value">
                        {% if webhook.last_called %}
                            {{ webhook.last_called|timesince }} ago
                        {% else %}
                            Never
                        {% endif %}
                    </span>
                </div>
                
                <div class="stat-row">
                    <span class="stat-label">Created:</span>
                    <span class="stat-value">{{ webhook.created_at|date:"M d, Y" }}</span>
                </div>

                {% if webhook.last_error %}
                <div class="mt-3">
                    <h6 class="text-danger">Last Error:</h6>
                    <small class="text-muted">{{ webhook.last_error|truncatechars:100 }}</small>
                </div>
                {% endif %}
            </div>

            <!-- Test Webhook -->
            <div class="webhook-stats">
                <h5 class="mb-3">
                    <i class="fas fa-play me-2"></i>Test Webhook
                </h5>
                <p class="text-muted mb-3">Send a test payload to verify your webhook is working correctly.</p>
                <a href="{% url 'test_webhook' webhook.id %}" class="btn btn-outline-success btn-sm">
                    <i class="fas fa-play me-1"></i>Send Test
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.getElementById('webhookForm').addEventListener('submit', function(e) {
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating...';
    submitBtn.disabled = true;
});

// Auto-generate secret key
function generateSecretKey() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < 32; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    document.getElementById('{{ form.secret_key.id_for_label }}').value = result;
}

// Add generate button next to secret key field
document.addEventListener('DOMContentLoaded', function() {
    const secretKeyField = document.getElementById('{{ form.secret_key.id_for_label }}');
    if (secretKeyField) {
        const generateBtn = document.createElement('button');
        generateBtn.type = 'button';
        generateBtn.className = 'btn btn-outline-secondary btn-sm mt-2';
        generateBtn.innerHTML = '<i class="fas fa-key me-1"></i>Generate New';
        generateBtn.onclick = generateSecretKey;
        secretKeyField.parentNode.appendChild(generateBtn);
    }
});
</script>
{% endblock %}
