document.addEventListener('DOMContentLoaded', function() {
    // Initialize clipboard.js
    const clipboard = new ClipboardJS('.copy-btn');
    
    clipboard.on('success', function(e) {
        const originalIcon = e.trigger.innerHTML;
        e.trigger.innerHTML = '<i class="fas fa-check"></i>';
        
        setTimeout(function() {
            e.trigger.innerHTML = originalIcon;
        }, 2000);
        
        e.clearSelection();
    });
    
    // Handle API key actions
    document.querySelectorAll('.dropdown-item').forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            
            const action = this.textContent.trim();
            const keyId = this.closest('.dropdown-menu').getAttribute('aria-labelledby').replace('dropdownMenuButton', '');
            
            if (action.includes('Delete')) {
                if (confirm('Are you sure you want to delete this API key? This action cannot be undone.')) {
                    // Send delete request
                    console.log('Delete API key:', keyId);
                }
            } else if (action.includes('Deactivate')) {
                // Send deactivate request
                console.log('Deactivate API key:', keyId);
            } else if (action.includes('Activate')) {
                // Send activate request
                console.log('Activate API key:', keyId);
            } else if (action.includes('Rename')) {
                const newName = prompt('Enter a new name for this API key:');
                if (newName) {
                    // Send rename request
                    console.log('Rename API key:', keyId, 'to', newName);
                }
            }
        });
    });
});
