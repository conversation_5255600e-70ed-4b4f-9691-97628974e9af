# Generated by Django 5.1.7 on 2025-05-28 15:56

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('qrcode_app', '0007_merge_20250528_1734'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='qrcode',
            name='approved_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='qrcode',
            name='approved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_qr_codes', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='qrcode',
            name='expires_at',
            field=models.DateTimeField(blank=True, help_text='QR code expiry date/time', null=True),
        ),
        migrations.AddField(
            model_name='qrcode',
            name='max_scans',
            field=models.PositiveIntegerField(blank=True, help_text='Maximum number of scans allowed', null=True),
        ),
        migrations.AddField(
            model_name='qrcode',
            name='rejection_reason',
            field=models.TextField(blank=True, help_text='Reason for rejection', null=True),
        ),
        migrations.AddField(
            model_name='qrcode',
            name='requires_approval',
            field=models.BooleanField(default=False, help_text='Requires admin approval before activation'),
        ),
        migrations.AddField(
            model_name='qrcode',
            name='status',
            field=models.CharField(choices=[('active', 'Active'), ('disabled', 'Disabled'), ('expired', 'Expired'), ('pending', 'Pending Approval'), ('rejected', 'Rejected')], default='active', max_length=20),
        ),
        migrations.AddField(
            model_name='qrcodescan',
            name='asn',
            field=models.CharField(blank=True, help_text='Autonomous System Number', max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='qrcodescan',
            name='organization',
            field=models.CharField(blank=True, help_text='ISP or organization name from IPinfo', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='qrcodescan',
            name='privacy_flags',
            field=models.JSONField(blank=True, default=dict, help_text='Privacy flags (VPN, proxy, etc.)'),
        ),
        migrations.CreateModel(
            name='QRCodeBranding',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text="Brand name (e.g., 'Company Logo')", max_length=100)),
                ('logo_url', models.URLField(blank=True, help_text='URL to brand logo', null=True)),
                ('logo_image', models.ImageField(blank=True, help_text='Upload brand logo', null=True, upload_to='brand_logos/')),
                ('primary_color', models.CharField(default='#000000', help_text='Primary brand color (hex)', max_length=7)),
                ('secondary_color', models.CharField(default='#ffffff', help_text='Secondary brand color (hex)', max_length=7)),
                ('accent_color', models.CharField(default='#007bff', help_text='Accent color for buttons/links', max_length=7)),
                ('theme_style', models.CharField(choices=[('minimal', 'Minimal'), ('corporate', 'Corporate'), ('modern', 'Modern'), ('custom', 'Custom')], default='minimal', max_length=20)),
                ('custom_css', models.TextField(blank=True, help_text='Custom CSS for advanced styling', null=True)),
                ('company_name', models.CharField(blank=True, max_length=100, null=True)),
                ('tagline', models.CharField(blank=True, max_length=200, null=True)),
                ('footer_text', models.CharField(blank=True, max_length=200, null=True)),
                ('show_powered_by', models.BooleanField(default=True, help_text="Show 'Powered by QR Generator'")),
                ('redirect_delay', models.PositiveIntegerField(default=3, help_text='Redirect delay in seconds')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='qr_brandings', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'QR Code Branding',
                'verbose_name_plural': 'QR Code Brandings',
                'unique_together': {('user', 'name')},
            },
        ),
        migrations.AddField(
            model_name='qrcode',
            name='branding',
            field=models.ForeignKey(blank=True, help_text='Custom branding for this QR code', null=True, on_delete=django.db.models.deletion.SET_NULL, to='qrcode_app.qrcodebranding'),
        ),
        migrations.CreateModel(
            name='QRLink',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.SlugField(help_text="Short code (e.g., 'abc123')", max_length=20, unique=True)),
                ('target_url', models.URLField(help_text='Destination URL')),
                ('custom_domain', models.CharField(blank=True, help_text="Custom domain (e.g., 'go.company.com')", max_length=100, null=True)),
                ('title', models.CharField(blank=True, help_text='Link title for analytics', max_length=200, null=True)),
                ('description', models.TextField(blank=True, help_text='Link description', null=True)),
                ('status', models.CharField(choices=[('active', 'Active'), ('disabled', 'Disabled'), ('expired', 'Expired'), ('pending', 'Pending Approval')], default='active', max_length=20)),
                ('password', models.CharField(blank=True, help_text='Password protection', max_length=100, null=True)),
                ('expires_at', models.DateTimeField(blank=True, help_text='Expiry date/time', null=True)),
                ('max_clicks', models.PositiveIntegerField(blank=True, help_text='Maximum number of clicks', null=True)),
                ('click_count', models.PositiveIntegerField(default=0)),
                ('last_clicked', models.DateTimeField(blank=True, null=True)),
                ('requires_approval', models.BooleanField(default=False)),
                ('approved_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_links', to=settings.AUTH_USER_MODEL)),
                ('branding', models.ForeignKey(blank=True, help_text='Custom branding for landing page', null=True, on_delete=django.db.models.deletion.SET_NULL, to='qrcode_app.qrcodebranding')),
                ('qr_code', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='short_link', to='qrcode_app.qrcode')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='qr_links', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'QR Short Link',
                'verbose_name_plural': 'QR Short Links',
                'ordering': ['-created_at'],
            },
        ),
    ]
