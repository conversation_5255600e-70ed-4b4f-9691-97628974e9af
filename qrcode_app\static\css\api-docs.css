/* API Documentation Styles */
:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #60a5fa;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #6366f1;
    --text-dark: #1f2937;
    --text-medium: #4b5563;
    --text-light: #9ca3af;
    --bg-light: #f9fafb;
    --bg-medium: #f3f4f6;
    --border-color: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --transition: all 0.2s ease;
    --method-get: #10b981;
    --method-post: #3b82f6;
    --method-put: #f59e0b;
    --method-delete: #ef4444;
    --method-patch: #8b5cf6;
}

/* API Documentation Layout */
.api-docs-container {
    display: flex;
    min-height: calc(100vh - 70px);
    background-color: var(--bg-light);
}

/* Sidebar */
.api-docs-sidebar {
    width: 280px;
    background-color: white;
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    height: calc(100vh - 70px);
    position: sticky;
    top: 70px;
    overflow-y: auto;
}

.api-docs-sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.api-docs-sidebar-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0;
}

.api-docs-sidebar-content {
    padding: 1rem 0;
}

.api-docs-nav {
    list-style: none;
    padding: 0;
    margin: 0;
}

.api-docs-nav li {
    margin-bottom: 0.25rem;
}

.api-docs-nav a {
    display: block;
    padding: 0.75rem 1.5rem;
    color: var(--text-medium);
    text-decoration: none;
    transition: var(--transition);
    font-size: 0.9375rem;
}

.api-docs-nav a:hover {
    background-color: var(--bg-light);
    color: var(--primary-color);
}

.api-docs-nav a.active {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--primary-color);
    font-weight: 500;
    border-left: 3px solid var(--primary-color);
}

.api-docs-nav-header {
    padding: 0.75rem 1.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-light);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-top: 1rem;
}

/* Main Content */
.api-docs-content {
    flex: 1;
    padding: 2rem;
    max-width: 900px;
    margin: 0 auto;
}

.api-docs-section {
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid var(--border-color);
}

.api-docs-section:last-child {
    border-bottom: none;
}

.api-docs-section h1 {
    font-size: 2.25rem;
    font-weight: 700;
    color: var(--text-dark);
    margin: 0 0 1rem 0;
}

.api-docs-section h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 2rem 0 1rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.api-docs-section h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 1.5rem 0 1rem 0;
}

.api-docs-section h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 1.25rem 0 0.75rem 0;
}

.api-docs-section p {
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-medium);
    margin: 0 0 1rem 0;
}

.api-docs-section ul, .api-docs-section ol {
    margin: 0 0 1.5rem 0;
    padding-left: 1.5rem;
}

.api-docs-section li {
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-medium);
    margin-bottom: 0.5rem;
}

.api-docs-version {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    background-color: var(--bg-medium);
    color: var(--text-medium);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 1.5rem;
}

/* API Endpoint */
.api-endpoint {
    background-color: var(--bg-medium);
    padding: 1rem;
    border-radius: var(--radius-md);
    margin-bottom: 1.5rem;
}

.api-endpoint code {
    font-family: 'Courier New', Courier, monospace;
    font-size: 0.9375rem;
    color: var(--text-dark);
}

/* API Features */
.api-features {
    list-style: none;
    padding: 0;
    margin: 0 0 1.5rem 0;
}

.api-features li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    font-size: 1rem;
    color: var(--text-medium);
}

.api-features li i {
    color: var(--success-color);
    font-size: 1.125rem;
}

/* Code Blocks */
.code-block {
    background-color: var(--text-dark);
    border-radius: var(--radius-md);
    padding: 1rem;
    margin-bottom: 1.5rem;
    overflow-x: auto;
}

.code-block pre {
    margin: 0;
    font-family: 'Courier New', Courier, monospace;
    font-size: 0.875rem;
    line-height: 1.6;
    color: white;
}

.code-block code {
    font-family: 'Courier New', Courier, monospace;
}

/* API Endpoint Cards */
.api-endpoint-card {
    background-color: white;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    margin-bottom: 1.5rem;
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.api-endpoint-card .endpoint-method {
    display: inline-block;
    padding: 0.5rem 1rem;
    font-weight: 600;
    font-size: 0.875rem;
    color: white;
    text-transform: uppercase;
}

.api-endpoint-card .endpoint-method.get {
    background-color: var(--method-get);
}

.api-endpoint-card .endpoint-method.post {
    background-color: var(--method-post);
}

.api-endpoint-card .endpoint-method.put {
    background-color: var(--method-put);
}

.api-endpoint-card .endpoint-method.delete {
    background-color: var(--method-delete);
}

.api-endpoint-card .endpoint-method.patch {
    background-color: var(--method-patch);
}

.api-endpoint-card .endpoint-path {
    display: inline-block;
    padding: 0.5rem 1rem;
    font-family: 'Courier New', Courier, monospace;
    font-size: 0.9375rem;
    font-weight: 500;
    color: var(--text-dark);
}

.api-endpoint-card .endpoint-description {
    padding: 0.75rem 1rem;
    font-size: 0.9375rem;
    color: var(--text-medium);
    border-top: 1px solid var(--border-color);
    border-bottom: 1px solid var(--border-color);
    background-color: var(--bg-light);
}

.api-endpoint-card .endpoint-details {
    padding: 1rem;
}

/* Client Libraries */
.client-libraries {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.client-library-card {
    background-color: white;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.client-library-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.client-library-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background-color: var(--bg-medium);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--primary-color);
}

.client-library-info h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 0.5rem 0;
}

.client-library-info p {
    font-size: 0.875rem;
    color: var(--text-medium);
    margin: 0 0 0.75rem 0;
}

.client-library-link {
    font-size: 0.875rem;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
}

.client-library-link:hover {
    text-decoration: underline;
}

/* Code Examples */
.code-example {
    margin-bottom: 2rem;
}

.code-example h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 0.75rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* API Key Management */
.api-key-management {
    background-color: white;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    padding: 1.5rem;
    margin-top: 1.5rem;
    border: 1px solid var(--border-color);
}

.api-key-management h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 0.75rem 0;
}

.api-key-management p {
    font-size: 0.9375rem;
    color: var(--text-medium);
    margin: 0 0 1rem 0;
}

.api-key-management .primary-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
}

.api-key-management .primary-btn:hover {
    box-shadow: 0 4px 6px -1px rgba(37, 99, 235, 0.3);
    transform: translateY(-1px);
}

/* API Keys Management */
.api-keys-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.api-keys-header {
    margin-bottom: 2rem;
}

.api-keys-header h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-dark);
    margin: 0 0 0.5rem 0;
}

.api-keys-header p {
    font-size: 1rem;
    color: var(--text-medium);
    margin: 0;
}

.api-keys-section {
    background-color: white;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    padding: 1.5rem;
    margin-bottom: 2rem;
    border: 1px solid var(--border-color);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.section-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0;
}

.api-keys-table-container {
    overflow-x: auto;
}

.api-keys-table {
    width: 100%;
    border-collapse: collapse;
}

.api-keys-table th,
.api-keys-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.api-keys-table th {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-medium);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.api-keys-table td {
    font-size: 0.875rem;
    color: var(--text-dark);
}

.api-key-display-row {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.masked-key {
    font-family: 'Courier New', Courier, monospace;
}

.api-key-status {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
}

.api-key-status.active {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.api-key-status.revoked {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    width: 28px;
    height: 28px;
    border-radius: 6px;
    background-color: var(--bg-light);
    border: none;
    color: var(--text-medium);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
}

.action-btn:hover {
    background-color: var(--bg-medium);
    color: var(--text-dark);
}

.action-btn.revoke-btn:hover {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.action-btn.delete-btn:hover {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.icon-btn {
    width: 28px;
    height: 28px;
    border-radius: 6px;
    background-color: var(--bg-light);
    border: none;
    color: var(--text-medium);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
}

.icon-btn:hover {
    background-color: var(--bg-medium);
    color: var(--text-dark);
}

.icon-btn.copied {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.empty-state {
    text-align: center;
}

.empty-state-content {
    padding: 3rem 1rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.empty-state-content i {
    font-size: 3rem;
    color: var(--text-light);
}

.empty-state-content p {
    font-size: 1rem;
    color: var(--text-medium);
    margin: 0;
}

/* API Usage Stats */
.api-usage-stats {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.api-usage-card {
    background-color: var(--bg-light);
    border-radius: var(--radius-md);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border: 1px solid var(--border-color);
}

.api-usage-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background-color: rgba(59, 130, 246, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: var(--primary-color);
}

.api-usage-info h3 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-medium);
    margin: 0 0 0.5rem 0;
}

.api-usage-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin: 0 0 0.25rem 0;
}

.api-usage-period {
    font-size: 0.75rem;
    color: var(--text-light);
    margin: 0;
}

/* API Docs Cards */
.api-docs-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.api-docs-card {
    background-color: white;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    padding: 1.5rem;
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.api-docs-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.api-docs-card-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(59, 130, 246, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.125rem;
    color: var(--primary-color);
    flex-shrink: 0;
}

.api-docs-card-content {
    flex-grow: 1;
}

.api-docs-card-content h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 0.5rem 0;
}

.api-docs-card-content p {
    font-size: 0.875rem;
    color: var(--text-medium);
    margin: 0 0 0.75rem 0;
}

.api-docs-card-link {
    font-size: 0.875rem;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
}

.api-docs-card-link:hover {
    text-decoration: underline;
}

/* API Key Created Modal */
.api-key-created-message {
    text-align: center;
    margin-bottom: 1.5rem;
}

.api-key-created-message i {
    font-size: 3rem;
    color: var(--success-color);
    margin-bottom: 1rem;
}

.api-key-created-message p {
    font-size: 1.125rem;
    color: var(--text-dark);
    margin: 0;
}

.api-key-display label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-medium);
    margin-bottom: 0.5rem;
}

.api-key-value-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
}

.api-key-value-container input {
    flex-grow: 1;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-family: 'Courier New', Courier, monospace;
    font-size: 0.875rem;
    color: var(--text-dark);
    background-color: var(--bg-light);
}

.api-key-warning {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--warning-color);
    margin: 0;
}

.api-key-warning i {
    font-size: 1rem;
    margin-top: 0.125rem;
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    color: var(--text-dark);
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    outline: none;
}

.form-help {
    font-size: 0.75rem;
    color: var(--text-light);
    margin: 0.5rem 0 0 0;
}

.checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 0.5rem;
}

.checkbox-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.checkbox-item input[type="checkbox"] {
    width: auto;
}

.checkbox-item label {
    margin-bottom: 0;
    font-weight: 400;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.95);
    width: 500px;
    max-width: 90%;
    background-color: white;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    z-index: 1001;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal.active {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
    visibility: visible;
}

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-light);
    font-size: 1.25rem;
    cursor: pointer;
    transition: var(--transition);
}

.modal-close:hover {
    color: var(--text-dark);
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.primary-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.primary-btn:hover {
    box-shadow: 0 4px 6px -1px rgba(37, 99, 235, 0.3);
    transform: translateY(-1px);
}

.secondary-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background-color: white;
    color: var(--text-dark);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.secondary-btn:hover {
    background-color: var(--bg-light);
}

/* Responsive Styles */
@media (max-width: 992px) {
    .api-docs-container {
        flex-direction: column;
    }

    .api-docs-sidebar {
        width: 100%;
        height: auto;
        position: static;
    }

    .api-docs-content {
        padding: 1.5rem;
    }

    .api-keys-container {
        padding: 1.5rem;
    }

    .api-usage-stats,
    .api-docs-cards {
        grid-template-columns: 1fr;
    }
}
