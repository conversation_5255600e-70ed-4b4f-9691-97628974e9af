{% extends "base.html" %}
{% load static %}

{% block title %}Dynamic QR Redirects - QR Pro{% endblock %}

{% block extra_css %}
<style>
.dynamic-redirects-dashboard {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 2rem 0;
}

.dashboard-header {
    text-align: center;
    color: white;
    margin-bottom: 3rem;
}

.dashboard-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.dashboard-header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1rem;
    opacity: 0.8;
}

.redirects-section {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.section-header {
    display: flex;
    justify-content-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f8f9fa;
}

.redirect-card {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.redirect-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.redirect-info {
    display: flex;
    justify-content: between;
    align-items: center;
    gap: 1rem;
}

.redirect-details h5 {
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.redirect-url {
    color: #3498db;
    text-decoration: none;
    font-weight: 500;
}

.redirect-url:hover {
    text-decoration: underline;
}

.redirect-stats {
    display: flex;
    gap: 1rem;
    margin-top: 0.5rem;
}

.stat-badge {
    background: #e3f2fd;
    color: #1976d2;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.redirect-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-edit {
    background: #27ae60;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: background 0.3s ease;
}

.btn-edit:hover {
    background: #219a52;
}

.btn-view {
    background: #3498db;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: background 0.3s ease;
}

.btn-view:hover {
    background: #2980b9;
}

.empty-state {
    text-align: center;
    padding: 3rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.create-redirect-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: transform 0.3s ease;
}

.create-redirect-btn:hover {
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

.eligible-qr-card {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.eligible-qr-card h6 {
    color: #856404;
    margin-bottom: 0.5rem;
}

.eligible-qr-card p {
    color: #856404;
    margin-bottom: 0.75rem;
    font-size: 0.9rem;
}

/* Pagination Styles */
.pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}

.pagination {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    gap: 0.5rem;
}

.pagination .page-item {
    display: flex;
}

.pagination .page-link {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 0.75rem;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    text-decoration: none;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    min-width: 40px;
    height: 40px;
}

.pagination .page-link:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border-color: transparent;
}

.pagination .page-item.disabled .page-link {
    opacity: 0.5;
    cursor: not-allowed;
    background: rgba(255, 255, 255, 0.05);
}

.pagination .page-item.disabled .page-link:hover {
    transform: none;
    background: rgba(255, 255, 255, 0.05);
}

.pagination-info {
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

@media (max-width: 768px) {
    .redirect-info {
        flex-direction: column;
        align-items: flex-start;
    }

    .redirect-actions {
        width: 100%;
        justify-content: flex-end;
    }

    .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="dynamic-redirects-dashboard">
    <div class="container">
        <!-- Header -->
        <div class="dashboard-header">
            <h1><i class="fas fa-exchange-alt me-3"></i>Dynamic QR Redirects</h1>
            <p>Change where your QR codes redirect without reprinting them</p>
        </div>

        <!-- Stats Grid -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{ total_redirects }}</div>
                <div class="stat-label">Active Redirects</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ total_clicks }}</div>
                <div class="stat-label">Total Clicks</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ eligible_qr_codes.count }}</div>
                <div class="stat-label">Eligible QR Codes</div>
            </div>
        </div>

        <!-- Active Redirects -->
        <div class="redirects-section">
            <div class="section-header">
                <h3><i class="fas fa-cog me-2"></i>Active Dynamic Redirects</h3>
                {% if qr_codes_with_redirects.paginator.count > 5 %}
                <div class="search-container">
                    <form method="GET" class="d-flex gap-2">
                        <input type="text" name="search" value="{{ request.GET.search }}" placeholder="Search redirects..." class="form-control form-control-sm" style="max-width: 200px;">
                        <button type="submit" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-search"></i>
                        </button>
                        {% if request.GET.search %}
                        <a href="?" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-times"></i>
                        </a>
                        {% endif %}
                        <!-- Preserve pagination parameters -->
                        {% if current_eligible_page != 1 %}
                        <input type="hidden" name="eligible_page" value="{{ current_eligible_page }}">
                        {% endif %}
                    </form>
                </div>
                {% endif %}
            </div>

            {% if qr_codes_with_redirects %}
                <!-- Pagination Info -->
                <div class="pagination-info">
                    Showing {{ qr_codes_with_redirects.start_index }}-{{ qr_codes_with_redirects.end_index }} of {{ qr_codes_with_redirects.paginator.count }} active redirects
                </div>

                {% for qr_code in qr_codes_with_redirects %}
                <div class="redirect-card">
                    <div class="redirect-info">
                        <div class="redirect-details flex-grow-1">
                            <h5>{{ qr_code.name }}</h5>
                            <a href="{{ qr_code.dynamic_redirect.current_url }}" target="_blank" class="redirect-url">
                                {{ qr_code.dynamic_redirect.current_url|truncatechars:60 }}
                            </a>
                            <div class="redirect-stats">
                                <span class="stat-badge">{{ qr_code.dynamic_redirect.total_clicks }} clicks</span>
                                <span class="stat-badge">{{ qr_code.dynamic_redirect.redirect_count }}/{{ qr_code.dynamic_redirect.max_redirects }} changes</span>
                            </div>
                        </div>
                        <div class="redirect-actions">
                            <a href="{% url 'update_dynamic_redirect' qr_code.dynamic_redirect.id %}" class="btn-edit">
                                <i class="fas fa-edit me-1"></i>Edit
                            </a>
                            <a href="{% url 'qr_code_detail' qr_code.id %}" class="btn-view">
                                <i class="fas fa-eye me-1"></i>View QR
                            </a>
                        </div>
                    </div>
                </div>
                {% endfor %}

                <!-- Pagination Controls for Active Redirects -->
                {% if qr_codes_with_redirects.paginator.num_pages > 1 %}
                <div class="pagination-container">
                    <nav aria-label="Active redirects pagination">
                        <ul class="pagination">
                            {% if qr_codes_with_redirects.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?redirects_page=1{% if current_eligible_page != 1 %}&eligible_page={{ current_eligible_page }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="First">
                                        <i class="fas fa-angle-double-left"></i>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?redirects_page={{ qr_codes_with_redirects.previous_page_number }}{% if current_eligible_page != 1 %}&eligible_page={{ current_eligible_page }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="Previous">
                                        <i class="fas fa-angle-left"></i>
                                    </a>
                                </li>
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link"><i class="fas fa-angle-double-left"></i></span>
                                </li>
                                <li class="page-item disabled">
                                    <span class="page-link"><i class="fas fa-angle-left"></i></span>
                                </li>
                            {% endif %}

                            {% for num in qr_codes_with_redirects.paginator.page_range %}
                                {% if num == qr_codes_with_redirects.number %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ num }}</span>
                                    </li>
                                {% elif num > qr_codes_with_redirects.number|add:'-3' and num < qr_codes_with_redirects.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?redirects_page={{ num }}{% if current_eligible_page != 1 %}&eligible_page={{ current_eligible_page }}{% endif %}">{{ num }}</a>
                                    </li>
                                {% endif %}
                            {% endfor %}

                            {% if qr_codes_with_redirects.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?redirects_page={{ qr_codes_with_redirects.next_page_number }}{% if current_eligible_page != 1 %}&eligible_page={{ current_eligible_page }}{% endif %}" aria-label="Next">
                                        <i class="fas fa-angle-right"></i>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?redirects_page={{ qr_codes_with_redirects.paginator.num_pages }}{% if current_eligible_page != 1 %}&eligible_page={{ current_eligible_page }}{% endif %}" aria-label="Last">
                                        <i class="fas fa-angle-double-right"></i>
                                    </a>
                                </li>
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link"><i class="fas fa-angle-right"></i></span>
                                </li>
                                <li class="page-item disabled">
                                    <span class="page-link"><i class="fas fa-angle-double-right"></i></span>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
                {% endif %}
            {% else %}
                <div class="empty-state">
                    <i class="fas fa-exchange-alt"></i>
                    <h4>No Dynamic Redirects Yet</h4>
                    <p>Create your first dynamic redirect to start changing QR destinations on the fly!</p>
                </div>
            {% endif %}
        </div>

        <!-- Eligible QR Codes -->
        {% if eligible_qr_codes %}
        <div class="redirects-section">
            <div class="section-header">
                <h3><i class="fas fa-plus-circle me-2"></i>Create New Dynamic Redirects</h3>
                <p class="text-muted mb-0">Convert your existing URL QR codes to dynamic redirects</p>
            </div>

            <!-- Pagination Info for Eligible QR Codes -->
            <div class="pagination-info" style="color: #6c757d;">
                Showing {{ eligible_qr_codes.start_index }}-{{ eligible_qr_codes.end_index }} of {{ eligible_qr_codes.paginator.count }} eligible QR codes
            </div>

            {% for qr_code in eligible_qr_codes %}
            <div class="eligible-qr-card">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6>{{ qr_code.name }}</h6>
                        <p class="mb-0">Currently redirects to: {{ qr_code.data|truncatechars:50 }}</p>
                    </div>
                    <a href="{% url 'create_dynamic_redirect' qr_code.id %}" class="create-redirect-btn">
                        <i class="fas fa-magic"></i>Make Dynamic
                    </a>
                </div>
            </div>
            {% endfor %}

            <!-- Pagination Controls for Eligible QR Codes -->
            {% if eligible_qr_codes.paginator.num_pages > 1 %}
            <div class="pagination-container" style="border-top: 1px solid #dee2e6;">
                <nav aria-label="Eligible QR codes pagination">
                    <ul class="pagination">
                        {% if eligible_qr_codes.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?eligible_page=1{% if current_redirects_page != 1 %}&redirects_page={{ current_redirects_page }}{% endif %}" aria-label="First" style="background: rgba(0,0,0,0.1); color: #495057;">
                                    <i class="fas fa-angle-double-left"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?eligible_page={{ eligible_qr_codes.previous_page_number }}{% if current_redirects_page != 1 %}&redirects_page={{ current_redirects_page }}{% endif %}" aria-label="Previous" style="background: rgba(0,0,0,0.1); color: #495057;">
                                    <i class="fas fa-angle-left"></i>
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item disabled">
                                <span class="page-link" style="background: rgba(0,0,0,0.05); color: #6c757d;"><i class="fas fa-angle-double-left"></i></span>
                            </li>
                            <li class="page-item disabled">
                                <span class="page-link" style="background: rgba(0,0,0,0.05); color: #6c757d;"><i class="fas fa-angle-left"></i></span>
                            </li>
                        {% endif %}

                        {% for num in eligible_qr_codes.paginator.page_range %}
                            {% if num == eligible_qr_codes.number %}
                                <li class="page-item active">
                                    <span class="page-link" style="background: linear-gradient(45deg, #667eea, #764ba2); color: white;">{{ num }}</span>
                                </li>
                            {% elif num > eligible_qr_codes.number|add:'-3' and num < eligible_qr_codes.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?eligible_page={{ num }}{% if current_redirects_page != 1 %}&redirects_page={{ current_redirects_page }}{% endif %}" style="background: rgba(0,0,0,0.1); color: #495057;">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if eligible_qr_codes.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?eligible_page={{ eligible_qr_codes.next_page_number }}{% if current_redirects_page != 1 %}&redirects_page={{ current_redirects_page }}{% endif %}" aria-label="Next" style="background: rgba(0,0,0,0.1); color: #495057;">
                                    <i class="fas fa-angle-right"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?eligible_page={{ eligible_qr_codes.paginator.num_pages }}{% if current_redirects_page != 1 %}&redirects_page={{ current_redirects_page }}{% endif %}" aria-label="Last" style="background: rgba(0,0,0,0.1); color: #495057;">
                                    <i class="fas fa-angle-double-right"></i>
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item disabled">
                                <span class="page-link" style="background: rgba(0,0,0,0.05); color: #6c757d;"><i class="fas fa-angle-right"></i></span>
                            </li>
                            <li class="page-item disabled">
                                <span class="page-link" style="background: rgba(0,0,0,0.05); color: #6c757d;"><i class="fas fa-angle-double-right"></i></span>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}
        </div>
        {% endif %}

        <!-- Back to Dashboard -->
        <div class="text-center mt-4">
            <a href="{% url 'monetization_dashboard' %}" class="btn btn-outline-light btn-lg">
                <i class="fas fa-arrow-left me-2"></i>Back to QR Pro Dashboard
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth animations
    const cards = document.querySelectorAll('.redirect-card, .eligible-qr-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>
{% endblock %}
