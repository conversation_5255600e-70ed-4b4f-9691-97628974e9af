/* Analytics Dashboard Styles */
.analytics-container {
    padding: 30px 0;
}

.analytics-header {
    background: linear-gradient(135deg, #3949ab, #1e88e5);
    color: white;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.analytics-header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 100%;
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" fill="rgba(255,255,255,0.1)"><path d="M0 0 L100 0 L100 100 Z"></path></svg>');
    background-size: cover;
}

.analytics-title {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 10px;
}

.analytics-subtitle {
    font-size: 16px;
    opacity: 0.9;
    max-width: 70%;
}

.ad-meta {
    display: flex;
    flex-wrap: wrap;
    margin-top: 20px;
    gap: 20px;
}

.ad-meta-item {
    background: rgba(255, 255, 255, 0.15);
    padding: 8px 15px;
    border-radius: 30px;
    font-size: 14px;
    display: flex;
    align-items: center;
}

.ad-meta-item i {
    margin-right: 8px;
}

.stats-row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    flex: 1;
    min-width: 200px;
    background: white;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-card::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 4px;
}

.stat-card.impressions::after {
    background: linear-gradient(90deg, #3949ab, #1e88e5);
}

.stat-card.clicks::after {
    background: linear-gradient(90deg, #43a047, #2e7d32);
}

.stat-card.ctr::after {
    background: linear-gradient(90deg, #5e35b1, #7b1fa2);
}

.stat-icon {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 24px;
    opacity: 0.2;
}

.stat-value {
    font-size: 36px;
    font-weight: 700;
    margin: 15px 0 5px;
}

.stat-label {
    font-size: 14px;
    color: #666;
    margin: 0;
}

.chart-container {
    background: white;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.chart-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.chart-controls {
    display: flex;
    gap: 10px;
}

.chart-control {
    background: #f5f5f5;
    border: none;
    padding: 8px 15px;
    border-radius: 5px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.chart-control:hover, .chart-control.active {
    background: #e0e0e0;
}

.data-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-top: 20px;
}

.data-table th {
    background: #f5f5f5;
    padding: 15px;
    text-align: left;
    font-weight: 600;
    color: #333;
    border-bottom: 2px solid #e0e0e0;
}

.data-table td {
    padding: 15px;
    border-bottom: 1px solid #eee;
}

.data-table tr:last-child td {
    border-bottom: none;
}

.data-table tr:hover td {
    background: #f9f9f9;
}

.insights-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 30px;
}

.insight-card {
    flex: 1;
    min-width: 300px;
    background: white;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.insight-title {
    font-size: 18px;
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.insight-title i {
    margin-right: 10px;
    color: #3949ab;
}

.action-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 30px;
}

.action-btn {
    padding: 12px 25px;
    border-radius: 30px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
}

.action-btn i {
    margin-right: 8px;
}

.action-btn.primary {
    background: linear-gradient(135deg, #3949ab, #1e88e5);
    color: white;
    box-shadow: 0 4px 15px rgba(57, 73, 171, 0.3);
}

.action-btn.primary:hover {
    box-shadow: 0 6px 20px rgba(57, 73, 171, 0.4);
    transform: translateY(-2px);
}

.action-btn.secondary {
    background: white;
    color: #3949ab;
    border: 1px solid #e0e0e0;
}

.action-btn.secondary:hover {
    background: #f5f5f5;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .analytics-header::before {
        display: none;
    }
    
    .analytics-subtitle {
        max-width: 100%;
    }
    
    .stats-row {
        flex-direction: column;
    }
    
    .stat-card {
        width: 100%;
    }
    
    .insights-container {
        flex-direction: column;
    }
    
    .insight-card {
        width: 100%;
    }
}
