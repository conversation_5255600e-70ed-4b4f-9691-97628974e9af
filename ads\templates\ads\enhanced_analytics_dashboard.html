{% extends 'base.html' %}
{% load static %}

{% block title %}Enhanced Analytics Dashboard | Enterprise QR{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'ads/css/enhanced_analytics.css' %}">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css">
{% endblock %}

{% block content %}
<div class="enhanced-analytics-container">
    <!-- Header Section -->
    <div class="analytics-header">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <nav aria-label="breadcrumb" class="analytics-breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'index' %}">Home</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'ads:dashboard' %}">Dashboard</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Enhanced Analytics</li>
                        </ol>
                    </nav>

                    <h1 class="analytics-title">Enhanced Analytics Dashboard</h1>
                    <p class="analytics-subtitle">Advanced metrics and insights for your advertising campaigns</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container">
        <!-- Filters Section -->
        <div class="analytics-filters-card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-filter"></i> Analytics Filters
                </h3>
                <button class="btn btn-sm btn-outline-primary" id="toggle-filters">
                    <i class="fas fa-chevron-down"></i>
                </button>
            </div>
            <div class="card-body" id="filters-body">
                <form id="analytics-filters-form" method="get" action="{% url 'ads:enhanced_analytics' %}">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="date-range">Date Range</label>
                                <div class="input-group">
                                    <input type="text" id="date-range" name="date_range" class="form-control" value="{{ date_range|default:'Last 30 Days' }}">
                                    <div class="input-group-append">
                                        <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="campaign-filter">Campaign</label>
                                <select id="campaign-filter" name="campaign" class="form-control">
                                    <option value="">All Campaigns</option>
                                    {% for campaign in campaigns %}
                                    <option value="{{ campaign.id }}" {% if selected_campaign == campaign.id %}selected{% endif %}>{{ campaign.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="ad-type-filter">Ad Type</label>
                                <select id="ad-type-filter" name="ad_type" class="form-control">
                                    <option value="">All Types</option>
                                    {% for ad_type in ad_types %}
                                    <option value="{{ ad_type.id }}" {% if selected_ad_type == ad_type.id %}selected{% endif %}>{{ ad_type.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <button type="submit" class="btn btn-primary btn-block">
                                    <i class="fas fa-search"></i> Apply
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- KPI Cards -->
        <div class="kpi-cards">
            <div class="kpi-card impressions">
                <div class="kpi-icon">
                    <i class="fas fa-eye"></i>
                </div>
                <div class="kpi-content">
                    <div class="kpi-value">{{ total_impressions|intcomma }}</div>
                    <div class="kpi-label">Impressions</div>
                    <div class="kpi-trend {% if impression_trend > 0 %}positive{% elif impression_trend < 0 %}negative{% endif %}">
                        <i class="fas fa-{% if impression_trend > 0 %}arrow-up{% elif impression_trend < 0 %}arrow-down{% else %}minus{% endif %}"></i>
                        <span>{{ impression_trend|floatformat:1 }}%</span>
                    </div>
                </div>
            </div>
            <div class="kpi-card clicks">
                <div class="kpi-icon">
                    <i class="fas fa-mouse-pointer"></i>
                </div>
                <div class="kpi-content">
                    <div class="kpi-value">{{ total_clicks|intcomma }}</div>
                    <div class="kpi-label">Clicks</div>
                    <div class="kpi-trend {% if click_trend > 0 %}positive{% elif click_trend < 0 %}negative{% endif %}">
                        <i class="fas fa-{% if click_trend > 0 %}arrow-up{% elif click_trend < 0 %}arrow-down{% else %}minus{% endif %}"></i>
                        <span>{{ click_trend|floatformat:1 }}%</span>
                    </div>
                </div>
            </div>
            <div class="kpi-card ctr">
                <div class="kpi-icon">
                    <i class="fas fa-percentage"></i>
                </div>
                <div class="kpi-content">
                    <div class="kpi-value">{{ ctr|floatformat:2 }}%</div>
                    <div class="kpi-label">CTR</div>
                    <div class="kpi-trend {% if ctr_trend > 0 %}positive{% elif ctr_trend < 0 %}negative{% endif %}">
                        <i class="fas fa-{% if ctr_trend > 0 %}arrow-up{% elif ctr_trend < 0 %}arrow-down{% else %}minus{% endif %}"></i>
                        <span>{{ ctr_trend|floatformat:1 }}%</span>
                    </div>
                </div>
            </div>
            <div class="kpi-card conversions">
                <div class="kpi-icon">
                    <i class="fas fa-exchange-alt"></i>
                </div>
                <div class="kpi-content">
                    <div class="kpi-value">{{ total_conversions|default:0|intcomma }}</div>
                    <div class="kpi-label">Conversions</div>
                    <div class="kpi-trend {% if conversion_trend > 0 %}positive{% elif conversion_trend < 0 %}negative{% endif %}">
                        <i class="fas fa-{% if conversion_trend > 0 %}arrow-up{% elif conversion_trend < 0 %}arrow-down{% else %}minus{% endif %}"></i>
                        <span>{{ conversion_trend|floatformat:1 }}%</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Charts Row -->
        <div class="row">
            <div class="col-lg-8">
                <!-- Performance Trends Chart -->
                <div class="analytics-card">
                    <div class="card-header">
                        <h3 class="card-title">Performance Trends</h3>
                        <div class="card-actions">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-outline-primary active" data-metric="impressions">Impressions</button>
                                <button type="button" class="btn btn-sm btn-outline-primary" data-metric="clicks">Clicks</button>
                                <button type="button" class="btn btn-sm btn-outline-primary" data-metric="ctr">CTR</button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <canvas id="performanceTrendsChart" height="300"></canvas>
                    </div>
                </div>

                <!-- Campaign Performance Comparison -->
                <div class="analytics-card">
                    <div class="card-header">
                        <h3 class="card-title">Campaign Performance</h3>
                        <div class="card-actions">
                            <select class="form-control form-control-sm" id="campaign-metric-selector">
                                <option value="impressions">Impressions</option>
                                <option value="clicks">Clicks</option>
                                <option value="ctr">CTR</option>
                                <option value="conversions">Conversions</option>
                            </select>
                        </div>
                    </div>
                    <div class="card-body">
                        <canvas id="campaignComparisonChart" height="300"></canvas>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Audience Demographics -->
                <div class="analytics-card">
                    <div class="card-header">
                        <h3 class="card-title">Audience Demographics</h3>
                    </div>
                    <div class="card-body">
                        <div class="demographics-section">
                            <h4 class="section-title">Devices</h4>
                            <canvas id="deviceChart" height="200"></canvas>
                        </div>
                        <div class="demographics-section">
                            <h4 class="section-title">Browsers</h4>
                            <canvas id="browserChart" height="200"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Geographic Distribution -->
                <div class="analytics-card">
                    <div class="card-header">
                        <h3 class="card-title">Geographic Distribution</h3>
                    </div>
                    <div class="card-body">
                        <div id="geoMap" style="height: 300px;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Performing Ads Table -->
        <div class="analytics-card">
            <div class="card-header">
                <h3 class="card-title">Top Performing Ads</h3>
                <div class="card-actions">
                    <div class="input-group">
                        <input type="text" class="form-control form-control-sm" placeholder="Search ads..." id="ad-search">
                        <div class="input-group-append">
                            <button class="btn btn-sm btn-outline-primary" type="button">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="top-ads-table">
                        <thead>
                            <tr>
                                <th>Ad Title</th>
                                <th>Campaign</th>
                                <th>Type</th>
                                <th>Impressions</th>
                                <th>Clicks</th>
                                <th>CTR</th>
                                <th>Conversions</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for ad in top_ads %}
                            <tr>
                                <td>{{ ad.title }}</td>
                                <td>{{ ad.campaign.name|default:"—" }}</td>
                                <td>{{ ad.ad_type.name }}</td>
                                <td>{{ ad.impressions }}</td>
                                <td>{{ ad.clicks }}</td>
                                <td>{{ ad.ctr|floatformat:2 }}%</td>
                                <td>{{ ad.conversions|default:0 }}</td>
                                <td><span class="status-badge status-{{ ad.status }}">{{ ad.get_status_display }}</span></td>
                                <td>
                                    <a href="{% url 'ads:ad_analytics' ad.slug %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-chart-line"></i>
                                    </a>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="9" class="text-center py-4">
                                    <div class="empty-state">
                                        <i class="fas fa-chart-bar"></i>
                                        <p>No ads data available</p>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Export & Reports Section -->
        <div class="analytics-card">
            <div class="card-header">
                <h3 class="card-title">Reports & Exports</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="export-section">
                            <h4 class="section-title">Export Data</h4>
                            <p>Download your analytics data in various formats</p>
                            <div class="export-buttons">
                                <a href="{% url 'ads:export_analytics' %}?format=csv&{{ request.GET.urlencode }}" class="btn btn-outline-primary">
                                    <i class="fas fa-file-csv"></i> CSV
                                </a>
                                <a href="{% url 'ads:export_analytics' %}?format=excel&{{ request.GET.urlencode }}" class="btn btn-outline-success">
                                    <i class="fas fa-file-excel"></i> Excel
                                </a>
                                <a href="{% url 'ads:export_analytics' %}?format=pdf&{{ request.GET.urlencode }}" class="btn btn-outline-danger">
                                    <i class="fas fa-file-pdf"></i> PDF
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="report-section">
                            <h4 class="section-title">Scheduled Reports</h4>
                            <p>Set up automated reports delivered to your email</p>
                            <button class="btn btn-primary" data-toggle="modal" data-target="#scheduleReportModal">
                                <i class="fas fa-calendar-alt"></i> Schedule Report
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Schedule Report Modal -->
<div class="modal fade" id="scheduleReportModal" tabindex="-1" role="dialog" aria-labelledby="scheduleReportModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="scheduleReportModalLabel">Schedule Automated Report</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="schedule-report-form" action="{% url 'ads:schedule_report' %}" method="post">
                    {% csrf_token %}
                    <div class="form-group">
                        <label for="report-name">Report Name</label>
                        <input type="text" class="form-control" id="report-name" name="report_name" required>
                    </div>
                    <div class="form-group">
                        <label for="report-frequency">Frequency</label>
                        <select class="form-control" id="report-frequency" name="frequency" required>
                            <option value="daily">Daily</option>
                            <option value="weekly" selected>Weekly</option>
                            <option value="monthly">Monthly</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="report-format">Format</label>
                        <select class="form-control" id="report-format" name="format" required>
                            <option value="pdf">PDF</option>
                            <option value="excel">Excel</option>
                            <option value="csv">CSV</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="report-email">Email Recipients</label>
                        <input type="email" class="form-control" id="report-email" name="email" value="{{ request.user.email }}" required>
                        <small class="form-text text-muted">Separate multiple emails with commas</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="submit" form="schedule-report-form" class="btn btn-primary">Schedule Report</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/moment/min/moment.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/jsvectormap"></script>
<script src="https://cdn.jsdelivr.net/npm/jsvectormap/dist/maps/world.js"></script>
<script src="{% static 'ads/js/enhanced_analytics.js' %}"></script>
<script>
    // Chart data from backend
    const chartData = {{ chart_data_json|safe }};
    const deviceData = {{ device_data_json|safe }};
    const browserData = {{ browser_data_json|safe }};
    const locationData = {{ location_data_json|safe }};
    const campaignData = {{ campaign_data_json|safe }};
</script>
{% endblock %}
