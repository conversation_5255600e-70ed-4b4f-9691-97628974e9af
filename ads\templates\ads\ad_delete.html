{% extends 'base.html' %}
{% load static %}

{% block title %}Delete Ad - {{ ad.title }}{% endblock %}

{% block extra_css %}
<style>
    .delete-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem 1rem;
    }
    
    .delete-card {
        border-radius: 10px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.05);
        overflow: hidden;
        transition: all 0.3s ease;
        margin-bottom: 2rem;
    }
    
    .delete-header {
        background-color: #dc3545;
        color: white;
        padding: 1.5rem;
        border-bottom: 1px solid rgba(0,0,0,0.1);
    }
    
    .delete-body {
        padding: 2rem;
        background-color: white;
    }
    
    .delete-warning {
        background-color: #fff8f8;
        border-left: 4px solid #dc3545;
        padding: 1rem;
        margin-bottom: 1.5rem;
        border-radius: 0 4px 4px 0;
    }
    
    .ad-details-list {
        list-style: none;
        padding: 0;
        margin: 0 0 1.5rem 0;
    }
    
    .ad-details-item {
        display: flex;
        margin-bottom: 0.75rem;
        padding-bottom: 0.75rem;
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }
    
    .ad-details-label {
        font-weight: 600;
        width: 30%;
        color: #495057;
    }
    
    .ad-details-value {
        width: 70%;
    }
    
    .ad-status {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        border-radius: 50px;
        font-size: 0.85rem;
        font-weight: 500;
    }
    
    .status-draft {
        background-color: #e9ecef;
        color: #495057;
    }
    
    .status-pending {
        background-color: #fff3cd;
        color: #856404;
    }
    
    .status-active {
        background-color: #d4edda;
        color: #155724;
    }
    
    .status-paused {
        background-color: #cce5ff;
        color: #004085;
    }
    
    .status-rejected {
        background-color: #f8d7da;
        color: #721c24;
    }
    
    .status-expired {
        background-color: #e2e3e5;
        color: #383d41;
    }
    
    .action-buttons {
        display: flex;
        gap: 1rem;
        margin-top: 1.5rem;
    }
    
    .btn-delete {
        background-color: #dc3545;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 5px;
        font-weight: 500;
        transition: all 0.2s ease;
    }
    
    .btn-delete:hover {
        background-color: #c82333;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(220, 53, 69, 0.2);
    }
    
    .btn-cancel {
        background-color: #f8f9fa;
        color: #212529;
        border: 1px solid #dee2e6;
        padding: 0.75rem 1.5rem;
        border-radius: 5px;
        font-weight: 500;
        transition: all 0.2s ease;
    }
    
    .btn-cancel:hover {
        background-color: #e2e6ea;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    /* Responsive styles */
    @media (max-width: 767.98px) {
        .delete-header {
            padding: 1.25rem;
        }
        
        .delete-body {
            padding: 1.5rem;
        }
        
        .ad-details-item {
            flex-direction: column;
        }
        
        .ad-details-label, .ad-details-value {
            width: 100%;
        }
        
        .ad-details-label {
            margin-bottom: 0.25rem;
        }
        
        .action-buttons {
            flex-direction: column;
        }
        
        .btn-delete, .btn-cancel {
            width: 100%;
            text-align: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="delete-container">
    <div class="delete-card animate__animated animate__fadeIn">
        <div class="delete-header">
            <h2 class="mb-0">Delete Advertisement</h2>
        </div>
        <div class="delete-body">
            <div class="delete-warning">
                <h4 class="mb-2"><i class="fas fa-exclamation-triangle"></i> Warning</h4>
                <p class="mb-0">You are about to delete this advertisement. This action cannot be undone. Are you sure you want to proceed?</p>
            </div>
            
            <h4 class="mb-3">Ad Details</h4>
            <ul class="ad-details-list">
                <li class="ad-details-item">
                    <div class="ad-details-label">Title</div>
                    <div class="ad-details-value">{{ ad.title }}</div>
                </li>
                <li class="ad-details-item">
                    <div class="ad-details-label">Type</div>
                    <div class="ad-details-value">{{ ad.ad_type.name }}</div>
                </li>
                <li class="ad-details-item">
                    <div class="ad-details-label">Status</div>
                    <div class="ad-details-value">
                        <span class="ad-status status-{{ ad.status }}">
                            {{ ad.get_status_display }}
                        </span>
                    </div>
                </li>
                <li class="ad-details-item">
                    <div class="ad-details-label">Placement</div>
                    <div class="ad-details-value">{{ ad.ad_location.name|default:"Standard placement" }}</div>
                </li>
                <li class="ad-details-item">
                    <div class="ad-details-label">Created</div>
                    <div class="ad-details-value">{{ ad.created_at|date:"F j, Y, g:i a" }}</div>
                </li>
                <li class="ad-details-item">
                    <div class="ad-details-label">Duration</div>
                    <div class="ad-details-value">{{ ad.start_date|date:"M d, Y" }} to {{ ad.end_date|date:"M d, Y" }}</div>
                </li>
                <li class="ad-details-item">
                    <div class="ad-details-label">Price</div>
                    <div class="ad-details-value">{{ ad.final_pricing }} KSH</div>
                </li>
            </ul>
            
            <form method="post">
                {% csrf_token %}
                <div class="action-buttons">
                    <button type="submit" class="btn-delete">
                        <i class="fas fa-trash-alt"></i> Confirm Delete
                    </button>
                    <a href="{% url 'ads:ad_detail' ad.slug %}" class="btn-cancel">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add confirmation dialog
        const deleteForm = document.querySelector('form');
        deleteForm.addEventListener('submit', function(e) {
            const confirmed = confirm('Are you absolutely sure you want to delete this advertisement? This action cannot be undone.');
            if (!confirmed) {
                e.preventDefault();
            }
        });
        
        // Add hover effects for desktop
        if (window.innerWidth > 768) {
            const deleteCard = document.querySelector('.delete-card');
            deleteCard.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
                this.style.boxShadow = '0 8px 30px rgba(0,0,0,0.1)';
            });
            
            deleteCard.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '0 4px 20px rgba(0,0,0,0.05)';
            });
        }
    });
</script>
{% endblock %}
