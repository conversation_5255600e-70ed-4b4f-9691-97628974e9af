from django.apps import AppConfig


class QrcodeAppConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'qrcode_app'

    verbose_name = 'QR Code Generator'

    def ready(self):
        """
        Import signals when the app is ready.
        This ensures that the signal handlers are registered.
        """
        import qrcode_app.signals
        import qrcode_app.scan_signals
