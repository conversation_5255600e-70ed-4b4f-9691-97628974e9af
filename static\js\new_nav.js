/**
 * new_nav.js - Enhanced Navigation with Smooth Transitions
 *
 * Provides smooth dropdown transitions and enhanced navigation interactions
 */

console.log('🚀 NEW_NAV.JS: Enhanced navigation loading...');

document.addEventListener('DOMContentLoaded', function() {
    console.log('✅ Enhanced navigation JavaScript ready');

    // Initialize smooth dropdown transitions
    initSmoothDropdowns();

    // Initialize enhanced mobile navigation
    initEnhancedMobileNav();

    console.log('🎯 Enhanced navigation fully initialized');
});

/**
 * Initialize smooth dropdown transitions for desktop
 */
function initSmoothDropdowns() {
    const navItems = document.querySelectorAll('.nav-item');

    navItems.forEach(navItem => {
        const dropdownMenu = navItem.querySelector('.dropdown-menu');

        if (dropdownMenu) {
            let hoverTimeout;

            // Enhanced hover enter
            navItem.addEventListener('mouseenter', function() {
                clearTimeout(hoverTimeout);

                // Add smooth transition class
                dropdownMenu.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
                dropdownMenu.style.opacity = '1';
                dropdownMenu.style.visibility = 'visible';
                dropdownMenu.style.transform = 'translateY(0)';

                // Add active state
                navItem.classList.add('dropdown-active');
            });

            // Enhanced hover leave with delay
            navItem.addEventListener('mouseleave', function() {
                hoverTimeout = setTimeout(() => {
                    dropdownMenu.style.opacity = '0';
                    dropdownMenu.style.visibility = 'hidden';
                    dropdownMenu.style.transform = 'translateY(-10px)';

                    // Remove active state
                    navItem.classList.remove('dropdown-active');
                }, 150); // Small delay for better UX
            });
        }
    });

    console.log('✅ Smooth dropdown transitions initialized');
}

/**
 * Initialize enhanced mobile navigation
 */
function initEnhancedMobileNav() {
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    const userMenu = document.querySelector('.user-menu');

    if (hamburger && navMenu) {
        hamburger.addEventListener('click', function() {
            // Add smooth transition classes
            navMenu.style.transition = 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
            if (userMenu) {
                userMenu.style.transition = 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
            }

            // Toggle mobile menu
            navMenu.classList.toggle('mobile-open');
            if (userMenu) {
                userMenu.classList.toggle('mobile-open');
            }

            // Add hamburger animation
            hamburger.classList.toggle('active');
        });
    }

    // Enhanced mobile dropdown functionality
    const mobileDropdownLinks = document.querySelectorAll('.nav-menu .nav-link');
    mobileDropdownLinks.forEach(link => {
        if (link.textContent.includes('▼')) {
            link.addEventListener('click', function(e) {
                e.preventDefault();

                const navItem = link.closest('.nav-item');
                const dropdownMenu = navItem.querySelector('.dropdown-menu');

                if (dropdownMenu) {
                    // Add smooth transition
                    dropdownMenu.style.transition = 'all 0.3s ease';

                    // Toggle dropdown
                    navItem.classList.toggle('mobile-dropdown-open');
                }
            });
        }
    });

    console.log('✅ Enhanced mobile navigation initialized');
}


