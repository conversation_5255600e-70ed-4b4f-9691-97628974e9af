"""
AI Services URLs
"""
from django.urls import path
from django.contrib.admin.views.decorators import staff_member_required
from django.shortcuts import render
from django.conf import settings
from ai_services import views

app_name = 'ai_services'

def ai_services_dashboard(request):
    """AI Services Dashboard View"""
    # Get current AI provider
    ai_provider = getattr(settings, 'AI_PROVIDER', 'mistral')

    # Get API keys and settings
    ai_engine_url = getattr(settings, 'AI_ENGINE_URL', 'http://localhost:8001')
    ai_engine_api_token = getattr(settings, 'AI_ENGINE_API_TOKEN', '')
    mistral_api_key = getattr(settings, 'MISTRAL_API_KEY', '')
    openai_api_key = getattr(settings, 'OPENAI_API_KEY', '')

    # Mask API keys and tokens
    if ai_engine_api_token:
        ai_engine_api_token = ai_engine_api_token[:4] + '*' * (len(ai_engine_api_token) - 8) + ai_engine_api_token[-4:] if len(ai_engine_api_token) > 8 else "***"

    if mistral_api_key:
        mistral_api_key = mistral_api_key[:4] + '*' * (len(mistral_api_key) - 8) + mistral_api_key[-4:] if len(mistral_api_key) > 8 else "***"

    if openai_api_key:
        openai_api_key = openai_api_key[:4] + '*' * (len(openai_api_key) - 8) + openai_api_key[-4:] if len(openai_api_key) > 8 else "***"

    # Context for the template
    context = {
        'title': 'AI Services Settings',
        'ai_provider': ai_provider,
        'ai_engine_url': ai_engine_url,
        'ai_engine_api_token': ai_engine_api_token,
        'mistral_api_key': mistral_api_key,
        'openai_api_key': openai_api_key,
        'mistral_model': getattr(settings, 'MISTRAL_MODEL', 'mistral-tiny'),
        'openai_model': getattr(settings, 'OPENAI_MODEL', 'gpt-4-turbo'),
    }

    return render(request, 'admin/ai_services/dashboard.html', context)

urlpatterns = [
    path('generate-ad-suggestions/', views.generate_ad_suggestions, name='generate_ad_suggestions'),
    path('admin/dashboard/', staff_member_required(ai_services_dashboard), name='admin_dashboard'),
    path('admin/status/', views.ai_provider_status_admin, name='admin_status'),
    path('admin/diagnostics/', views.ai_diagnostics_admin, name='admin_diagnostics'),
    path('test/', views.test_ai_integration, name='test_ai_integration'),
    path('debug/', views.debug_api_response, name='debug_api_response'),
    path('api/provider-status/', views.get_ai_provider_status, name='ai_provider_status'),
    path('api/diagnostics/', staff_member_required(views.get_ai_provider_status), name='api_diagnostics'),
    path('api/check-mistral-api/', staff_member_required(views.check_mistral_api_admin), name='check_mistral_api'),
]
