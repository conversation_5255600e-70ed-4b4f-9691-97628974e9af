import sqlite3

# Connect to the database
conn = sqlite3.connect('db.sqlite3')
cursor = conn.cursor()

# Check if the table already exists
cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='qrcode_app_userprofile';")
if cursor.fetchone():
    print("Table qrcode_app_userprofile already exists.")
else:
    # Create the UserProfile table
    cursor.execute('''
    CREATE TABLE "qrcode_app_userprofile" (
        "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
        "role" varchar(20) NOT NULL,
        "company" varchar(100) NULL,
        "phone" varchar(20) NULL,
        "address" text NULL,
        "profile_image" varchar(100) NULL,
        "api_key" varchar(32) NOT NULL,
        "created_at" datetime NOT NULL,
        "updated_at" datetime NOT NULL,
        "user_id" integer NOT NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED
    );
    ''')
    
    # Create indexes
    cursor.execute('''
    CREATE UNIQUE INDEX "qrcode_app_userprofile_user_id_key" ON "qrcode_app_userprofile" ("user_id");
    ''')
    cursor.execute('''
    CREATE UNIQUE INDEX "qrcode_app_userprofile_api_key_key" ON "qrcode_app_userprofile" ("api_key");
    ''')
    
    # Create a UserProfile for the superuser
    cursor.execute('''
    INSERT INTO qrcode_app_userprofile (role, company, phone, address, profile_image, api_key, created_at, updated_at, user_id)
    SELECT 'superadmin', 'Codegx Technology', NULL, NULL, NULL, '00000000-0000-0000-0000-000000000000', datetime('now'), datetime('now'), id
    FROM auth_user
    WHERE username = 'peter';
    ''')
    
    # Commit the changes
    conn.commit()
    print("Table qrcode_app_userprofile created successfully.")

# Close the connection
conn.close()
