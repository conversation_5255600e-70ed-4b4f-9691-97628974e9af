{% extends 'base.html' %}
{% load static %}

{% block title %}Enterprise QR | Education Solutions{% endblock %}

{% block extra_css %}
<!-- Google Fonts - Poppins and Montserrat -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

<link rel="stylesheet" href="{% static 'css/solutions.css' %}">

<style>
    /* Ultra-Premium Sleek Corporate Solutions Styling */
    body {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 15%, #cbd5e1 30%, #94a3b8 50%, #64748b 70%, #475569 85%, #334155 100%);
        min-height: 100vh;
        font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        position: relative;
        overflow-x: hidden;
    }

    /* Elegant animated background with subtle corporate patterns */
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 25% 75%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),
            radial-gradient(circle at 75% 25%, rgba(255, 255, 255, 0.6) 0%, transparent 40%),
            radial-gradient(circle at 15% 85%, rgba(139, 92, 246, 0.06) 0%, transparent 45%),
            radial-gradient(circle at 85% 15%, rgba(16, 185, 129, 0.05) 0%, transparent 35%),
            radial-gradient(circle at 50% 50%, rgba(59, 130, 246, 0.04) 0%, transparent 60%);
        z-index: -1;
        animation: elegantCorporateFloat 120s ease-in-out infinite;
    }

    @keyframes elegantCorporateFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        25% { transform: translateY(-30px) rotate(1deg); }
        50% { transform: translateY(-20px) rotate(-1deg); }
        75% { transform: translateY(-35px) rotate(0.5deg); }
    }

    /* Override solution hero for corporate elegance */
    .solution-hero {
        background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(51, 65, 85, 0.95) 50%, rgba(71, 85, 105, 0.95) 100%) !important;
        backdrop-filter: blur(25px);
        border-radius: 24px;
        margin: 2rem;
        box-shadow:
            0 20px 40px rgba(0, 0, 0, 0.1),
            0 0 0 1px rgba(255, 255, 255, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.1);
        position: relative;
        overflow: hidden;
        animation: slideInDown 0.8s ease-out;
    }

    .solution-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
        animation: shimmer 3s ease-in-out infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    .solution-title {
        font-family: 'Montserrat', sans-serif !important;
        color: #ffffff !important;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 2;
    }

    .solution-subtitle {
        color: rgba(255, 255, 255, 0.9) !important;
        position: relative;
        z-index: 2;
    }

    .hero-cta {
        position: relative;
        z-index: 2;
    }

    /* Enhanced Mobile Responsiveness */
    @media (max-width: 768px) {
        .solution-hero {
            margin: 1rem;
            padding: 2rem 1.5rem;
            border-radius: 16px;
        }

        .solution-title {
            font-size: 2rem !important;
        }

        .solution-subtitle {
            font-size: 1rem !important;
        }

        .hero-cta .btn {
            margin: 0.25rem;
            padding: 0.75rem 1.5rem;
        }
    }

    @media (max-width: 576px) {
        .solution-hero {
            margin: 0.5rem;
            padding: 1.5rem 1rem;
        }

        .solution-title {
            font-size: 1.6rem !important;
        }

        .solution-subtitle {
            font-size: 0.9rem !important;
        }

        .hero-cta {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .hero-cta .btn {
            width: 100%;
            margin: 0;
        }
    }

    /* Enhanced Body Content Styling */
    .solution-overview,
    .solution-features,
    .solution-use-cases,
    .solution-cta {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
        backdrop-filter: blur(25px);
        border-radius: 24px;
        margin: 2rem;
        padding: 4rem 2rem;
        box-shadow:
            0 15px 35px rgba(0, 0, 0, 0.08),
            0 0 0 1px rgba(255, 255, 255, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.4);
        border: 1px solid rgba(255, 255, 255, 0.2);
        animation: slideInUp 0.8s ease-out;
        position: relative;
        overflow: hidden;
    }

    .solution-overview::before,
    .solution-features::before,
    .solution-use-cases::before,
    .solution-cta::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
        animation: shimmer 4s ease-in-out infinite;
        pointer-events: none;
    }

    .section-title {
        font-family: 'Montserrat', sans-serif !important;
        font-size: 2.2rem;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 1.5rem;
        position: relative;
        z-index: 2;
    }

    .section-description {
        font-size: 1.1rem;
        color: #475569;
        line-height: 1.7;
        margin-bottom: 2rem;
        position: relative;
        z-index: 2;
    }

    .feature-list {
        list-style: none;
        padding: 0;
        position: relative;
        z-index: 2;
    }

    .feature-list li {
        padding: 0.75rem 0;
        font-size: 1rem;
        color: #475569;
        display: flex;
        align-items: center;
    }

    .feature-list li i {
        color: #10b981;
        margin-right: 1rem;
        font-size: 1.1rem;
    }

    .feature-card {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
        backdrop-filter: blur(15px);
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow:
            0 10px 25px rgba(0, 0, 0, 0.05),
            0 0 0 1px rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
        position: relative;
        z-index: 2;
    }

    .feature-card:hover {
        transform: translateY(-8px);
        box-shadow:
            0 20px 40px rgba(0, 0, 0, 0.1),
            0 0 0 1px rgba(255, 255, 255, 0.3);
    }

    .feature-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1.5rem;
        box-shadow: 0 8px 20px rgba(6, 182, 212, 0.3);
    }

    .feature-icon i {
        font-size: 1.5rem;
        color: white;
    }

    .feature-card h3 {
        font-family: 'Montserrat', sans-serif !important;
        font-size: 1.3rem;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 1rem;
    }

    .feature-card p {
        color: #475569;
        line-height: 1.6;
        margin: 0;
    }

    .use-case-card {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
        backdrop-filter: blur(15px);
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow:
            0 10px 25px rgba(0, 0, 0, 0.05),
            0 0 0 1px rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
        display: flex;
        align-items: flex-start;
        position: relative;
        z-index: 2;
    }

    .use-case-card:hover {
        transform: translateY(-5px);
        box-shadow:
            0 15px 30px rgba(0, 0, 0, 0.08),
            0 0 0 1px rgba(255, 255, 255, 0.3);
    }

    .use-case-icon {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1.5rem;
        flex-shrink: 0;
        box-shadow: 0 6px 15px rgba(6, 182, 212, 0.3);
    }

    .use-case-icon i {
        font-size: 1.2rem;
        color: white;
    }

    .use-case-content h3 {
        font-family: 'Montserrat', sans-serif !important;
        font-size: 1.2rem;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 0.75rem;
    }

    .use-case-content p {
        color: #475569;
        line-height: 1.6;
        margin: 0;
    }

    .solution-cta {
        background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(51, 65, 85, 0.95) 100%) !important;
        text-align: center;
    }

    .cta-content h2 {
        font-family: 'Montserrat', sans-serif !important;
        font-size: 2.5rem;
        font-weight: 700;
        color: #ffffff;
        margin-bottom: 1rem;
        position: relative;
        z-index: 2;
    }

    .cta-content p {
        font-size: 1.2rem;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 2rem;
        position: relative;
        z-index: 2;
    }

    .cta-buttons {
        position: relative;
        z-index: 2;
    }

    .cta-buttons .btn {
        margin: 0.5rem;
        padding: 0.875rem 2rem;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block content %}
<div class="solution-hero education-hero">
    <div class="solution-hero-content">
        <h1 class="solution-title">
            <i class="fas fa-graduation-cap me-3 text-warning"></i>
            Education QR Solutions
        </h1>
        <p class="solution-subtitle">Transform learning experiences with interactive QR code technology</p>
        <div class="hero-cta">
            <a href="{% url 'generate_qr_code' %}" class="btn btn-primary btn-lg">Get Started</a>
            <a href="#features" class="btn btn-outline-light btn-lg">Learn More</a>
        </div>
    </div>
    <div class="solution-hero-image">
        <img src="{% static 'img/solutions/education-hero.svg' %}" alt="Education QR Solutions">
    </div>
</div>

<div class="solution-overview">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h2 class="section-title">Revolutionize the Learning Experience</h2>
                <p class="section-description">Our education QR solutions help schools, universities, and educational institutions create interactive learning environments, streamline administrative processes, and enhance student engagement.</p>
                <p>With Enterprise QR's education solutions, you can:</p>
                <ul class="feature-list">
                    <li><i class="fas fa-check-circle"></i> Create interactive learning materials</li>
                    <li><i class="fas fa-check-circle"></i> Streamline attendance tracking</li>
                    <li><i class="fas fa-check-circle"></i> Enhance campus navigation</li>
                    <li><i class="fas fa-check-circle"></i> Simplify library resource access</li>
                    <li><i class="fas fa-check-circle"></i> Facilitate secure testing procedures</li>
                </ul>
            </div>
            <div class="col-lg-6">
                <div class="overview-image">
                    <img src="{% static 'img/solutions/education-overview.svg' %}" alt="Education QR Overview">
                </div>
            </div>
        </div>
    </div>
</div>

<div id="features" class="solution-features">
    <div class="container">
        <h2 class="section-title text-center">Key Features for Educational Institutions</h2>
        <div class="row">
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-book-open"></i>
                    </div>
                    <h3>Interactive Learning</h3>
                    <p>Transform static textbooks and worksheets into interactive learning experiences with QR codes linking to videos, quizzes, and supplementary materials.</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-user-check"></i>
                    </div>
                    <h3>Attendance Systems</h3>
                    <p>Implement efficient, contactless attendance tracking for classes, events, and campus activities using QR code check-ins.</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-id-card"></i>
                    </div>
                    <h3>Student ID Integration</h3>
                    <p>Enhance student ID cards with QR codes for access control, library checkouts, cafeteria payments, and more.</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-clipboard-check"></i>
                    </div>
                    <h3>Assessment Tools</h3>
                    <p>Create secure, efficient testing procedures with QR codes for exam distribution, submission, and verification.</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <h3>Event Management</h3>
                    <p>Streamline campus events with QR codes for registration, check-in, and access to schedules and materials.</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <h3>Analytics & Reporting</h3>
                    <p>Gain insights into student engagement, resource utilization, and learning outcomes through comprehensive QR code analytics.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="solution-use-cases">
    <div class="container">
        <h2 class="section-title text-center">Education Use Cases</h2>
        <div class="row">
            <div class="col-lg-6">
                <div class="use-case-card">
                    <div class="use-case-icon">
                        <i class="fas fa-chalkboard-teacher"></i>
                    </div>
                    <div class="use-case-content">
                        <h3>Classroom Materials</h3>
                        <p>Enhance learning materials with QR codes that link to interactive content, video explanations, and additional resources for different learning styles.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="use-case-card">
                    <div class="use-case-icon">
                        <i class="fas fa-university"></i>
                    </div>
                    <div class="use-case-content">
                        <h3>Campus Tours</h3>
                        <p>Create self-guided campus tours with QR codes that provide information about buildings, departments, and facilities for prospective students and visitors.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="use-case-card">
                    <div class="use-case-icon">
                        <i class="fas fa-book"></i>
                    </div>
                    <div class="use-case-content">
                        <h3>Library Resources</h3>
                        <p>Simplify access to digital library resources, research databases, and supplementary materials with QR codes integrated into physical books and study areas.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="use-case-card">
                    <div class="use-case-icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <div class="use-case-content">
                        <h3>Alumni Engagement</h3>
                        <p>Maintain connections with alumni through QR codes in publications and at events that link to donation portals, networking opportunities, and campus news.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="solution-cta">
    <div class="container">
        <div class="cta-content">
            <h2>Ready to Transform Your Educational Institution?</h2>
            <p>Join forward-thinking schools and universities that trust Enterprise QR for their QR code solutions.</p>
            <div class="cta-buttons">
                <a href="{% url 'generate_qr_code' %}" class="btn btn-primary btn-lg">Get Started Now</a>
                <a href="#" class="btn btn-outline-light btn-lg">Contact Sales</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
