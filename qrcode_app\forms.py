from django import forms
from .models import QRCode, UserProfile, APIKey, QRCodeBatch, ScanAlert, WebhookEndpoint
from django.contrib.auth.models import User
from django.contrib.auth.forms import AuthenticationForm, UserCreationForm

class QRCodeForm(forms.ModelForm):
    """Form for creating QR codes"""
    uploaded_file = forms.FileField(required=False, help_text="Upload a file for PDF, Image, Document, Audio, or Video QR codes")

    class Meta:
        model = QRCode
        fields = ['name', 'qr_type', 'data', 'foreground_color', 'background_color', 'logo', 'uploaded_file', 'is_encrypted']
        widgets = {
            'foreground_color': forms.TextInput(attrs={'type': 'color'}),
            'background_color': forms.TextInput(attrs={'type': 'color'}),
            'data': forms.Textarea(attrs={'rows': 4, 'placeholder': 'Enter data or URL for the QR code'}),
        }

    def __init__(self, *args, **kwargs):
        super(QRCodeForm, self).__init__(*args, **kwargs)
        self.fields['data'].required = False
        self.fields['uploaded_file'].required = False

    def clean(self):
        cleaned_data = super().clean()
        qr_type = cleaned_data.get('qr_type')
        data = cleaned_data.get('data')
        uploaded_file = cleaned_data.get('uploaded_file')

        # Check if file upload is required for this QR type
        file_types = ['PDF', 'IMAGE', 'DOCUMENT', 'AUDIO', 'VIDEO']

        if qr_type in file_types and not uploaded_file and not self.instance.uploaded_file:
            if not data:
                self.add_error('uploaded_file', f'Please upload a file for {qr_type} QR code or provide a URL.')

        # For non-file types, data is required
        if qr_type not in file_types and not data:
            self.add_error('data', 'This field is required for this QR code type.')

        return cleaned_data

class UserProfileForm(forms.ModelForm):
    """Form for user profile"""
    first_name = forms.CharField(max_length=30, required=False)
    last_name = forms.CharField(max_length=30, required=False)
    email = forms.EmailField(required=False)

    class Meta:
        model = UserProfile
        fields = ['company', 'phone', 'address', 'profile_image']

    def __init__(self, *args, **kwargs):
        super(UserProfileForm, self).__init__(*args, **kwargs)
        if self.instance and self.instance.user:
            self.fields['first_name'].initial = self.instance.user.first_name
            self.fields['last_name'].initial = self.instance.user.last_name
            self.fields['email'].initial = self.instance.user.email

    def save(self, commit=True):
        profile = super(UserProfileForm, self).save(commit=False)

        # Update User model fields
        user = profile.user
        user.first_name = self.cleaned_data['first_name']
        user.last_name = self.cleaned_data['last_name']
        user.email = self.cleaned_data['email']
        user.save()

        if commit:
            profile.save()

        return profile

class APIKeyForm(forms.ModelForm):
    """Form for API keys"""
    class Meta:
        model = APIKey
        fields = ['name']

class QRCodeBatchForm(forms.ModelForm):
    """Form for batch QR code generation"""
    csv_file = forms.FileField(help_text="Upload a CSV file with QR code data")

    class Meta:
        model = QRCodeBatch
        fields = ['name', 'description']

class CustomLoginForm(AuthenticationForm):
    """Custom login form with Bootstrap styling"""
    username = forms.CharField(
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Username'})
    )
    password = forms.CharField(
        widget=forms.PasswordInput(attrs={'class': 'form-control', 'placeholder': 'Password'})
    )

class UserRegistrationForm(UserCreationForm):
    """Form for creating new users with profiles"""
    email = forms.EmailField(required=True)
    first_name = forms.CharField(max_length=30, required=False)
    last_name = forms.CharField(max_length=30, required=False)
    role = forms.ChoiceField(choices=UserProfile.UserRole.choices, initial=UserProfile.UserRole.USER)
    company = forms.CharField(max_length=100, required=False)
    phone = forms.CharField(max_length=20, required=False)

    class Meta:
        model = User
        fields = ('username', 'email', 'first_name', 'last_name', 'password1', 'password2')

    def __init__(self, *args, **kwargs):
        self.request_user = kwargs.pop('request_user', None)
        super(UserRegistrationForm, self).__init__(*args, **kwargs)

        # Only superadmins can create admin/superadmin users
        if self.request_user:
            try:
                profile = UserProfile.objects.get(user=self.request_user)
                if not profile.is_superadmin():
                    # Remove admin/superadmin options from choices
                    self.fields['role'].choices = [
                        choice for choice in UserProfile.UserRole.choices
                        if choice[0] not in ['admin', 'superadmin']
                    ]
            except UserProfile.DoesNotExist:
                # Remove admin/superadmin options from choices
                self.fields['role'].choices = [
                    choice for choice in UserProfile.UserRole.choices
                    if choice[0] not in ['admin', 'superadmin']
                ]

    def save(self, commit=True):
        user = super(UserRegistrationForm, self).save(commit=False)
        user.email = self.cleaned_data['email']
        user.first_name = self.cleaned_data['first_name']
        user.last_name = self.cleaned_data['last_name']

        if commit:
            user.save()

            # Get or create user profile
            try:
                profile = UserProfile.objects.get(user=user)
                # Update existing profile
                profile.role = self.cleaned_data['role']
                profile.company = self.cleaned_data['company']
                profile.phone = self.cleaned_data['phone']
            except UserProfile.DoesNotExist:
                # Create new profile
                profile = UserProfile(
                    user=user,
                    role=self.cleaned_data['role'],
                    company=self.cleaned_data['company'],
                    phone=self.cleaned_data['phone']
                )

            profile.save()

        return user


class QRRedirectForm(forms.ModelForm):
    """
    MODULE 2: Form for editing QR redirect destination (Dynamic QR functionality)
    Allows users to change where their QR code redirects without reprinting
    """
    class Meta:
        model = QRCode
        fields = ['target_url']
        widgets = {
            'target_url': forms.URLInput(attrs={
                'class': 'form-control',
                'placeholder': 'https://example.com',
                'required': True
            })
        }
        labels = {
            'target_url': 'Destination URL'
        }
        help_texts = {
            'target_url': 'Enter the new URL where this QR code should redirect. You can change this anytime without reprinting the QR code.'
        }


class AIPagePromptForm(forms.Form):
    """
    MODULE 3: Form for creating AI-generated landing pages
    """
    from .models import AILandingPage

    page_type = forms.ChoiceField(
        choices=AILandingPage.PageType.choices,
        initial=AILandingPage.PageType.CUSTOM,
        widget=forms.Select(attrs={
            'class': 'form-control',
            'id': 'pageType'
        }),
        label='Page Type',
        help_text='Choose the type of landing page you want to create'
    )

    title = forms.CharField(
        max_length=255,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter a title for your landing page',
            'id': 'pageTitle'
        }),
        label='Page Title',
        help_text='A catchy title for your landing page'
    )

    prompt = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 6,
            'placeholder': 'Describe what you want your landing page to include. Be specific about content, style, and any special requirements...',
            'id': 'pagePrompt'
        }),
        label='Describe Your Page',
        help_text='Tell our AI what you want your landing page to say and include. The more details you provide, the better the result!'
    )

    primary_color = forms.CharField(
        max_length=7,
        initial='#667eea',
        widget=forms.TextInput(attrs={
            'type': 'color',
            'class': 'form-control form-control-color',
            'id': 'primaryColor'
        }),
        label='Primary Color',
        help_text='Main color for your landing page'
    )

    secondary_color = forms.CharField(
        max_length=7,
        initial='#764ba2',
        widget=forms.TextInput(attrs={
            'type': 'color',
            'class': 'form-control form-control-color',
            'id': 'secondaryColor'
        }),
        label='Secondary Color',
        help_text='Accent color for your landing page'
    )

    def clean_prompt(self):
        prompt = self.cleaned_data.get('prompt')
        if len(prompt) < 10:
            raise forms.ValidationError('Please provide a more detailed description (at least 10 characters).')
        return prompt


class ScanAlertForm(forms.ModelForm):
    """
    MODULE 4: Form for creating and managing scan alerts
    """
    class Meta:
        model = ScanAlert
        fields = ['qr_code', 'alert_type', 'keyword', 'email', 'is_active', 'send_immediately', 'max_alerts_per_day']
        widgets = {
            'qr_code': forms.Select(attrs={
                'class': 'form-control',
                'id': 'qrCode'
            }),
            'alert_type': forms.Select(attrs={
                'class': 'form-control',
                'id': 'alertType'
            }),
            'keyword': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'e.g., Nigeria, VPN, Mobile, Google',
                'id': 'keyword'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': '<EMAIL>',
                'id': 'email'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input',
                'id': 'isActive'
            }),
            'send_immediately': forms.CheckboxInput(attrs={
                'class': 'form-check-input',
                'id': 'sendImmediately'
            }),
            'max_alerts_per_day': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1',
                'max': '100',
                'id': 'maxAlertsPerDay'
            })
        }
        labels = {
            'qr_code': 'QR Code (Optional)',
            'alert_type': 'Alert Type',
            'keyword': 'Keyword to Match',
            'email': 'Email Address',
            'is_active': 'Enable Alert',
            'send_immediately': 'Send Immediately',
            'max_alerts_per_day': 'Max Alerts Per Day'
        }
        help_texts = {
            'qr_code': 'Leave blank to monitor all your QR codes',
            'alert_type': 'Type of alert trigger',
            'keyword': 'Keyword to search for in location, organization, device, etc.',
            'email': 'Email address to receive alerts',
            'is_active': 'Enable or disable this alert',
            'send_immediately': 'Send alerts immediately or batch them daily',
            'max_alerts_per_day': 'Maximum number of alerts to send per day (1-100)'
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super(ScanAlertForm, self).__init__(*args, **kwargs)

        # Filter QR codes to only show user's QR codes
        if self.user:
            self.fields['qr_code'].queryset = QRCode.objects.filter(user=self.user)
            # Set default email to user's email
            if not self.instance.pk:
                self.fields['email'].initial = self.user.email

    def clean_keyword(self):
        keyword = self.cleaned_data.get('keyword')
        if len(keyword) < 2:
            raise forms.ValidationError('Keyword must be at least 2 characters long.')
        return keyword.strip()

    def clean_max_alerts_per_day(self):
        max_alerts = self.cleaned_data.get('max_alerts_per_day')
        if max_alerts < 1 or max_alerts > 100:
            raise forms.ValidationError('Max alerts per day must be between 1 and 100.')
        return max_alerts

    def save(self, commit=True):
        alert = super(ScanAlertForm, self).save(commit=False)
        if self.user:
            alert.user = self.user
        if commit:
            alert.save()
        return alert


class WebhookEndpointForm(forms.ModelForm):
    """
    MODULE 5: Form for creating and managing webhook endpoints
    """
    class Meta:
        model = WebhookEndpoint
        fields = ['qr_code', 'url', 'trigger_on_scan', 'trigger_on_alert', 'active', 'secret_key']
        widgets = {
            'qr_code': forms.Select(attrs={
                'class': 'form-control form-select',
                'id': 'qrCode'
            }),
            'url': forms.URLInput(attrs={
                'class': 'form-control',
                'placeholder': 'https://hooks.zapier.com/hooks/catch/...',
                'id': 'webhookUrl'
            }),
            'trigger_on_scan': forms.CheckboxInput(attrs={
                'class': 'form-check-input',
                'id': 'triggerOnScan'
            }),
            'trigger_on_alert': forms.CheckboxInput(attrs={
                'class': 'form-check-input',
                'id': 'triggerOnAlert'
            }),
            'active': forms.CheckboxInput(attrs={
                'class': 'form-check-input',
                'id': 'active',
                'checked': True
            }),
            'secret_key': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Optional secret key for webhook verification',
                'id': 'secretKey'
            })
        }
        labels = {
            'qr_code': 'QR Code (Optional)',
            'url': 'Webhook URL',
            'trigger_on_scan': 'Trigger on QR Scan',
            'trigger_on_alert': 'Trigger on Scan Alert',
            'active': 'Enable Webhook',
            'secret_key': 'Secret Key (Optional)'
        }
        help_texts = {
            'qr_code': 'Leave blank to monitor all your QR codes',
            'url': 'Zapier, CRM, or any webhook URL to receive scan data',
            'trigger_on_scan': 'Send webhook when QR code is scanned',
            'trigger_on_alert': 'Send webhook when scan alert is triggered',
            'active': 'Enable or disable this webhook',
            'secret_key': 'Optional secret key for webhook verification (sent in X-Webhook-Secret header)'
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super(WebhookEndpointForm, self).__init__(*args, **kwargs)

        # Filter QR codes to only show user's QR codes
        if self.user:
            self.fields['qr_code'].queryset = QRCode.objects.filter(user=self.user)

    def clean_url(self):
        url = self.cleaned_data.get('url')
        if not url.startswith(('http://', 'https://')):
            raise forms.ValidationError('Webhook URL must start with http:// or https://')
        return url

    def clean(self):
        cleaned_data = super().clean()
        trigger_on_scan = cleaned_data.get('trigger_on_scan')
        trigger_on_alert = cleaned_data.get('trigger_on_alert')

        # At least one trigger must be enabled
        if not trigger_on_scan and not trigger_on_alert:
            raise forms.ValidationError('At least one trigger (scan or alert) must be enabled.')

        return cleaned_data

    def save(self, commit=True):
        webhook = super(WebhookEndpointForm, self).save(commit=False)
        if self.user:
            webhook.user = self.user
        if commit:
            webhook.save()
        return webhook
