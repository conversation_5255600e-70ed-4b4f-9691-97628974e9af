{% extends "base.html" %}
{% load static %}

{% block title %}Monetization Dashboard - QR Generator Pro{% endblock %}

{% block extra_css %}
<style>
    .monetization-dashboard {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .dashboard-header {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        text-align: center;
    }

    .premium-badge {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-size: 0.9rem;
        font-weight: 600;
        display: inline-block;
        margin-bottom: 1rem;
    }

    .feature-card {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
    }

    .feature-card:hover {
        transform: translateY(-5px);
    }

    .feature-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        background: linear-gradient(135deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        text-align: center;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: #667eea;
        margin-bottom: 0.5rem;
    }

    .upgrade-cta {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        text-align: center;
        margin-bottom: 2rem;
    }

    .btn-premium {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        color: white;
        padding: 0.75rem 2rem;
        border-radius: 25px;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: transform 0.3s ease;
    }

    .btn-premium:hover {
        transform: translateY(-2px);
        color: white;
    }

    /* Enhanced QR Generator Pro Branding */
    .pro-branding-section {
        background: linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 249, 250, 0.9) 100%);
        border-radius: 20px;
        padding: 3rem;
        margin-bottom: 2rem;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(20px);
        position: relative;
        overflow: hidden;
    }

    .pro-logo-container {
        display: flex;
        align-items: center;
        gap: 2rem;
        margin-bottom: 2rem;
        position: relative;
        z-index: 1;
    }

    .pro-logo-icon {
        position: relative;
        width: 80px;
        height: 80px;
        background: linear-gradient(145deg, #667eea, #764ba2);
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2.5rem;
        color: white;
        box-shadow: 0 15px 30px rgba(102, 126, 234, 0.3);
    }

    .logo-glow {
        position: absolute;
        inset: -2px;
        background: conic-gradient(from 0deg, #667eea, #764ba2, #667eea);
        border-radius: 22px;
        z-index: -1;
        opacity: 0.7;
        animation: logoRotate 3s linear infinite;
    }

    @keyframes logoRotate {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .pro-title {
        font-size: 3rem;
        font-weight: 900;
        margin: 0;
        color: #1a1a2e;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .brand-name {
        background: linear-gradient(135deg, #1a1a2e 0%, #2c3e50 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .pro-badge {
        background: linear-gradient(145deg, #FFD700, #FFA500);
        color: #1a1a2e;
        padding: 0.5rem 1rem;
        border-radius: 15px;
        font-size: 1.2rem;
        font-weight: 800;
        box-shadow: 0 8px 20px rgba(255, 215, 0, 0.3);
        animation: badgePulse 2s ease-in-out infinite;
    }

    @keyframes badgePulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    .pro-tagline {
        font-size: 1.2rem;
        color: #6b7280;
        margin: 0.5rem 0 0 0;
        font-weight: 500;
    }

    .value-props {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        position: relative;
        z-index: 1;
    }

    .value-prop {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.7);
        border-radius: 12px;
        border: 1px solid rgba(102, 126, 234, 0.2);
        transition: all 0.3s ease;
    }

    .value-prop:hover {
        background: rgba(255, 255, 255, 0.9);
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.2);
    }

    .value-prop i {
        color: #667eea;
        font-size: 1.2rem;
    }

    .value-prop span {
        color: #374151;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block content %}
<div class="monetization-dashboard">
    <div class="container">
        <!-- Header -->
        <div class="dashboard-header">
            {% if is_premium %}
            <div class="premium-badge">
                <i class="fas fa-crown me-2"></i>Premium User
            </div>
            <h1>🚀 QR Generator Pro Dashboard</h1>
            <p>Welcome to your premium monetization dashboard</p>
            {% else %}
            <!-- Enhanced QR Generator Pro Branding -->
            <div class="pro-branding-section">
                <div class="pro-logo-container">
                    <div class="pro-logo-icon">
                        <i class="fas fa-qrcode"></i>
                        <div class="logo-glow"></div>
                    </div>
                    <div class="pro-brand-text">
                        <h1 class="pro-title">
                            <span class="brand-name">QR Generator</span>
                            <span class="pro-badge">PRO</span>
                        </h1>
                        <p class="pro-tagline">Transform your QR codes into a powerful business tool</p>
                    </div>
                </div>

                <!-- Value Proposition -->
                <div class="value-props">
                    <div class="value-prop">
                        <i class="fas fa-magic"></i>
                        <span>AI-Powered Landing Pages</span>
                    </div>
                    <div class="value-prop">
                        <i class="fas fa-chart-line"></i>
                        <span>Advanced Analytics</span>
                    </div>
                    <div class="value-prop">
                        <i class="fas fa-palette"></i>
                        <span>Custom Branding</span>
                    </div>
                    <div class="value-prop">
                        <i class="fas fa-shield-alt"></i>
                        <span>Enterprise Security</span>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        {% if not is_premium %}
        <!-- Enhanced Upgrade CTA -->
        <div class="upgrade-cta-enhanced">
            <div class="cta-background">
                <div class="cta-pattern"></div>
                <div class="cta-glow"></div>
            </div>
            <div class="cta-content">
                <div class="cta-header">
                    <div class="cta-icon">
                        <i class="fas fa-rocket"></i>
                        <div class="icon-pulse"></div>
                    </div>
                    <div class="cta-text">
                        <h2>Upgrade to QR Generator Pro</h2>
                        <p>Join thousands of businesses using our premium features to grow their reach</p>
                    </div>
                </div>

                <div class="cta-features">
                    <div class="feature-highlight">
                        <i class="fas fa-magic"></i>
                        <span>AI Landing Pages</span>
                    </div>
                    <div class="feature-highlight">
                        <i class="fas fa-exchange-alt"></i>
                        <span>Dynamic Redirects</span>
                    </div>
                    <div class="feature-highlight">
                        <i class="fas fa-palette"></i>
                        <span>Branded Pages</span>
                    </div>
                    <div class="feature-highlight">
                        <i class="fas fa-chart-bar"></i>
                        <span>Advanced Analytics</span>
                    </div>
                </div>

                <div class="cta-actions">
                    <a href="{% url 'pricing' %}" class="btn-cta-primary">
                        <i class="fas fa-crown"></i>
                        <span>View Pricing Plans</span>
                        <div class="btn-shine"></div>
                    </a>
                    <a href="{% url 'upgrade_to_premium' %}" class="btn-cta-secondary">
                        <i class="fas fa-magic"></i>
                        <span>Try QR Pro FREE</span>
                        <div class="btn-glow"></div>
                    </a>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Stats Grid -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{ total_qr_codes }}</div>
                <div>Total QR Codes</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ branded_qr_codes }}</div>
                <div>Branded QR Codes</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ dynamic_redirects }}</div>
                <div>Dynamic Redirects</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ recent_scans|length }}</div>
                <div>Recent Scans</div>
            </div>
        </div>

        <!-- Feature Cards -->
        <div class="row">
            <div class="col-md-6">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-palette"></i>
                    </div>
                    <h3>Branded Landing Pages</h3>
                    <p>Remove ads and add your logo, colors, and branding to QR landing pages.</p>
                    {% if is_premium %}
                    <a href="{% url 'branding_management' %}" class="btn-premium">
                        <i class="fas fa-brush me-2"></i>Manage Branding
                    </a>
                    {% else %}
                    <span class="text-muted">Premium Feature</span>
                    {% endif %}
                </div>
            </div>

            <div class="col-md-6">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <h3>Dynamic QR Redirects</h3>
                    <p>Change where your QR codes redirect without reprinting them.</p>
                    {% if is_premium %}
                    <a href="{% url 'dynamic_redirect_dashboard' %}" class="btn-premium">
                        <i class="fas fa-cog me-2"></i>Manage Redirects
                    </a>
                    {% else %}
                    <span class="text-muted">Premium Feature</span>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3>Advanced Analytics</h3>
                    <p>Track scans by location, device, organization, and time.</p>
                    {% if is_premium %}
                    <a href="{% url 'qr_map' %}" class="btn-premium">
                        <i class="fas fa-map me-2"></i>View Analytics
                    </a>
                    {% else %}
                    <span class="text-muted">Premium Feature</span>
                    {% endif %}
                </div>
            </div>

            <div class="col-md-6">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-bell"></i>
                    </div>
                    <h3>Scan Alerts</h3>
                    <p>Get notified when specific organizations or countries scan your QR codes.</p>
                    <span class="text-muted">Coming Soon</span>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        {% if recent_scans %}
        <div class="feature-card">
            <h3><i class="fas fa-history me-2"></i>Recent Scan Activity</h3>
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>QR Code</th>
                            <th>Location</th>
                            <th>Organization</th>
                            <th>Time</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for scan in recent_scans %}
                        <tr>
                            <td>{{ scan.code }}</td>
                            <td>{{ scan.city }}, {{ scan.country }}</td>
                            <td>{{ scan.org|default:"Unknown" }}</td>
                            <td>{{ scan.timestamp|timesince }} ago</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
