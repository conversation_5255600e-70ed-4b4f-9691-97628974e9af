{% extends "base.html" %}
{% load static %}

{% block title %}Monetization Dashboard - QR Generator Pro{% endblock %}

{% block extra_css %}
<style>
    .monetization-dashboard {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .dashboard-header {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        text-align: center;
    }

    .premium-badge {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-size: 0.9rem;
        font-weight: 600;
        display: inline-block;
        margin-bottom: 1rem;
    }

    .feature-card {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
    }

    .feature-card:hover {
        transform: translateY(-5px);
    }

    .feature-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        background: linear-gradient(135deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        text-align: center;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: #667eea;
        margin-bottom: 0.5rem;
    }

    .upgrade-cta {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        text-align: center;
        margin-bottom: 2rem;
    }

    .btn-premium {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        color: white;
        padding: 0.75rem 2rem;
        border-radius: 25px;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: transform 0.3s ease;
    }

    .btn-premium:hover {
        transform: translateY(-2px);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="monetization-dashboard">
    <div class="container">
        <!-- Header -->
        <div class="dashboard-header">
            {% if is_premium %}
            <div class="premium-badge">
                <i class="fas fa-crown me-2"></i>Premium User
            </div>
            <h1>🚀 QR Generator Pro Dashboard</h1>
            <p>Welcome to your premium monetization dashboard</p>
            {% else %}
            <h1>💰 Unlock QR Generator Pro</h1>
            <p>Transform your QR codes into a powerful business tool</p>
            {% endif %}
        </div>

        {% if not is_premium %}
        <!-- Upgrade CTA -->
        <div class="upgrade-cta">
            <h2><i class="fas fa-rocket me-2"></i>Upgrade to QR Pro</h2>
            <p>Get AI landing pages, dynamic redirects, branded pages, and advanced analytics</p>
            <div class="btn-group">
                <a href="{% url 'pricing' %}" class="btn btn-light btn-lg">
                    <i class="fas fa-crown me-2"></i>View Pricing Plans
                </a>
                <a href="{% url 'upgrade_to_premium' %}" class="btn btn-outline-light btn-lg">
                    <i class="fas fa-magic me-2"></i>Try QR Pro FREE
                </a>
            </div>
        </div>
        {% endif %}

        <!-- Stats Grid -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{ total_qr_codes }}</div>
                <div>Total QR Codes</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ branded_qr_codes }}</div>
                <div>Branded QR Codes</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ dynamic_redirects }}</div>
                <div>Dynamic Redirects</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ recent_scans|length }}</div>
                <div>Recent Scans</div>
            </div>
        </div>

        <!-- Feature Cards -->
        <div class="row">
            <div class="col-md-6">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-palette"></i>
                    </div>
                    <h3>Branded Landing Pages</h3>
                    <p>Remove ads and add your logo, colors, and branding to QR landing pages.</p>
                    {% if is_premium %}
                    <a href="{% url 'branding_management' %}" class="btn-premium">
                        <i class="fas fa-brush me-2"></i>Manage Branding
                    </a>
                    {% else %}
                    <span class="text-muted">Premium Feature</span>
                    {% endif %}
                </div>
            </div>

            <div class="col-md-6">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <h3>Dynamic QR Redirects</h3>
                    <p>Change where your QR codes redirect without reprinting them.</p>
                    {% if is_premium %}
                    <a href="{% url 'dynamic_redirect_dashboard' %}" class="btn-premium">
                        <i class="fas fa-cog me-2"></i>Manage Redirects
                    </a>
                    {% else %}
                    <span class="text-muted">Premium Feature</span>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3>Advanced Analytics</h3>
                    <p>Track scans by location, device, organization, and time.</p>
                    {% if is_premium %}
                    <a href="{% url 'qr_map' %}" class="btn-premium">
                        <i class="fas fa-map me-2"></i>View Analytics
                    </a>
                    {% else %}
                    <span class="text-muted">Premium Feature</span>
                    {% endif %}
                </div>
            </div>

            <div class="col-md-6">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-bell"></i>
                    </div>
                    <h3>Scan Alerts</h3>
                    <p>Get notified when specific organizations or countries scan your QR codes.</p>
                    <span class="text-muted">Coming Soon</span>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        {% if recent_scans %}
        <div class="feature-card">
            <h3><i class="fas fa-history me-2"></i>Recent Scan Activity</h3>
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>QR Code</th>
                            <th>Location</th>
                            <th>Organization</th>
                            <th>Time</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for scan in recent_scans %}
                        <tr>
                            <td>{{ scan.code }}</td>
                            <td>{{ scan.city }}, {{ scan.country }}</td>
                            <td>{{ scan.org|default:"Unknown" }}</td>
                            <td>{{ scan.timestamp|timesince }} ago</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
