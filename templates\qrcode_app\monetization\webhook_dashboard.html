{% extends 'base.html' %}
{% load static %}

{% block title %}Webhook Integration Dashboard{% endblock %}

{% block extra_css %}
<style>
.webhook-dashboard {
    padding: 2rem 0;
}

.webhook-stats {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 1.5rem;
    text-align: center;
    backdrop-filter: blur(10px);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.webhook-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.webhook-card:hover {
    transform: translateY(-5px);
}

.webhook-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 1rem;
}

.webhook-url {
    font-family: 'Courier New', monospace;
    background: #f8f9fa;
    padding: 0.5rem;
    border-radius: 5px;
    font-size: 0.9rem;
    word-break: break-all;
}

.webhook-status {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
}

.status-active {
    background: #d4edda;
    color: #155724;
}

.status-inactive {
    background: #f8d7da;
    color: #721c24;
}

.webhook-stats-mini {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.mini-stat {
    text-align: center;
    flex: 1;
}

.mini-stat-number {
    font-size: 1.2rem;
    font-weight: bold;
    color: #667eea;
}

.mini-stat-label {
    font-size: 0.8rem;
    color: #6c757d;
}

.success-rate {
    font-weight: bold;
}

.success-high { color: #28a745; }
.success-medium { color: #ffc107; }
.success-low { color: #dc3545; }

.btn-webhook {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-webhook:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    color: white;
}

.empty-state {
    text-align: center;
    padding: 3rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.webhook-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 20px;
}

.progress-bar-custom {
    height: 8px;
    border-radius: 4px;
    background: #e9ecef;
    overflow: hidden;
    margin-top: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    transition: width 0.3s ease;
}
</style>
{% endblock %}

{% block content %}
<div class="container webhook-dashboard">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="display-4 mb-2">
                <i class="fas fa-plug me-3"></i>Webhook Integration
            </h1>
            <p class="lead text-muted">Connect your QR scans to external services like Zapier, CRM systems, and automation platforms.</p>
        </div>
    </div>

    <!-- Statistics -->
    <div class="webhook-stats">
        <div class="row">
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number">{{ total_webhooks }}</div>
                    <div class="stat-label">Total Webhooks</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number">{{ active_webhooks }}</div>
                    <div class="stat-label">Active Webhooks</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number">{{ total_calls|floatformat:0 }}</div>
                    <div class="stat-label">Total Calls</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number">{{ overall_success_rate }}%</div>
                    <div class="stat-label">Success Rate</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <a href="{% url 'create_webhook' %}" class="btn btn-webhook">
                <i class="fas fa-plus me-2"></i>Create New Webhook
            </a>
        </div>
    </div>

    <!-- Webhooks List -->
    {% if webhooks %}
        <div class="row">
            {% for webhook in webhooks %}
            <div class="col-12">
                <div class="webhook-card">
                    <div class="webhook-header">
                        <div class="flex-grow-1">
                            <h5 class="mb-1">
                                {% if webhook.qr_code %}
                                    {{ webhook.qr_code.name }}
                                {% else %}
                                    All QR Codes
                                {% endif %}
                                <span class="webhook-status {% if webhook.active %}status-active{% else %}status-inactive{% endif %}">
                                    {% if webhook.active %}Active{% else %}Inactive{% endif %}
                                </span>
                            </h5>
                            <div class="webhook-url">{{ webhook.url }}</div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <strong>Triggers:</strong>
                            {% if webhook.trigger_on_scan %}
                                <span class="badge bg-primary me-1">QR Scans</span>
                            {% endif %}
                            {% if webhook.trigger_on_alert %}
                                <span class="badge bg-warning me-1">Scan Alerts</span>
                            {% endif %}
                        </div>
                        <div class="col-md-6 text-end">
                            <small class="text-muted">
                                Created: {{ webhook.created_at|date:"M d, Y" }}
                            </small>
                        </div>
                    </div>

                    <div class="webhook-stats-mini">
                        <div class="mini-stat">
                            <div class="mini-stat-number">{{ webhook.total_calls }}</div>
                            <div class="mini-stat-label">Total Calls</div>
                        </div>
                        <div class="mini-stat">
                            <div class="mini-stat-number">{{ webhook.successful_calls }}</div>
                            <div class="mini-stat-label">Successful</div>
                        </div>
                        <div class="mini-stat">
                            <div class="mini-stat-number">
                                <span class="success-rate {% if webhook.get_success_rate >= 90 %}success-high{% elif webhook.get_success_rate >= 70 %}success-medium{% else %}success-low{% endif %}">
                                    {{ webhook.get_success_rate|floatformat:1 }}%
                                </span>
                            </div>
                            <div class="mini-stat-label">Success Rate</div>
                        </div>
                        <div class="mini-stat">
                            <div class="mini-stat-number">
                                {% if webhook.last_called %}
                                    {{ webhook.last_called|timesince }} ago
                                {% else %}
                                    Never
                                {% endif %}
                            </div>
                            <div class="mini-stat-label">Last Called</div>
                        </div>
                    </div>

                    {% if webhook.get_success_rate < 100 and webhook.total_calls > 0 %}
                    <div class="progress-bar-custom">
                        <div class="progress-fill" style="width: {{ webhook.get_success_rate }}%"></div>
                    </div>
                    {% endif %}

                    <div class="webhook-actions">
                        <a href="{% url 'test_webhook' webhook.id %}" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-play me-1"></i>Test
                        </a>
                        <a href="{% url 'edit_webhook' webhook.id %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit me-1"></i>Edit
                        </a>
                        <a href="{% url 'delete_webhook' webhook.id %}" class="btn btn-outline-danger btn-sm" 
                           onclick="return confirm('Are you sure you want to delete this webhook?')">
                            <i class="fas fa-trash me-1"></i>Delete
                        </a>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if webhooks_paginator.num_pages > 1 %}
        <div class="row mt-4">
            <div class="col-12">
                <nav aria-label="Webhooks pagination">
                    <ul class="pagination justify-content-center">
                        {% if webhooks.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ webhooks.previous_page_number }}">Previous</a>
                            </li>
                        {% endif %}
                        
                        {% for num in webhooks_paginator.page_range %}
                            {% if num == webhooks.number %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% else %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if webhooks.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ webhooks.next_page_number }}">Next</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
        </div>
        {% endif %}

    {% else %}
        <!-- Empty State -->
        <div class="empty-state">
            <i class="fas fa-plug"></i>
            <h3>No Webhooks Yet</h3>
            <p>Connect your QR scans to external services by creating your first webhook.</p>
            <a href="{% url 'create_webhook' %}" class="btn btn-webhook mt-3">
                <i class="fas fa-plus me-2"></i>Create Your First Webhook
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-refresh webhook statistics every 30 seconds
setInterval(function() {
    // Only refresh if there are active webhooks
    if ({{ active_webhooks }} > 0) {
        location.reload();
    }
}, 30000);

// Test webhook with AJAX
function testWebhook(webhookId) {
    fetch(`/monetization/webhooks/test/${webhookId}/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ Webhook test successful!');
        } else {
            alert('❌ Webhook test failed: ' + data.error);
        }
        location.reload();
    })
    .catch(error => {
        alert('❌ Error testing webhook: ' + error);
    });
}
</script>
{% endblock %}
