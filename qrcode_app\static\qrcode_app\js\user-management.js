document.addEventListener('DOMContentLoaded', function() {
    // Role filter functionality
    const roleFilter = document.getElementById('roleFilter');
    if (roleFilter) {
        roleFilter.addEventListener('change', function() {
            filterUsers();
        });
    }

    // Search functionality
    const userSearch = document.getElementById('userSearch');
    if (userSearch) {
        userSearch.addEventListener('input', function() {
            filterUsers();
        });
    }

    // Filter users based on role and search term
    function filterUsers() {
        const role = roleFilter ? roleFilter.value : '';
        const searchTerm = userSearch ? userSearch.value.toLowerCase() : '';
        
        const rows = document.querySelectorAll('tbody tr');
        
        rows.forEach(row => {
            const userRole = row.querySelector('td:nth-child(3) .badge').textContent.trim().toLowerCase();
            const userName = row.querySelector('td:nth-child(1)').textContent.toLowerCase();
            const userEmail = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
            const userCompany = row.querySelector('td:nth-child(4)').textContent.toLowerCase();
            
            const roleMatch = role === '' || userRole.includes(role.toLowerCase());
            const searchMatch = searchTerm === '' || 
                               userName.includes(searchTerm) || 
                               userEmail.includes(searchTerm) || 
                               userCompany.includes(searchTerm);
            
            row.style.display = roleMatch && searchMatch ? '' : 'none';
        });
    }

    // Edit user button functionality
    const editUserBtns = document.querySelectorAll('.edit-user-btn');
    editUserBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const userId = this.getAttribute('data-user-id');
            // In a real application, you would fetch user data from the server
            // For now, we'll just show the modal with empty fields
            document.getElementById('editUserId').value = userId;
            
            // Show the modal
            const editUserModal = new bootstrap.Modal(document.getElementById('editUserModal'));
            editUserModal.show();
        });
    });

    // Delete user button functionality
    const deleteUserBtns = document.querySelectorAll('.delete-user-btn');
    deleteUserBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const userId = this.getAttribute('data-user-id');
            document.getElementById('deleteUserId').value = userId;
            
            // Show the modal
            const deleteUserModal = new bootstrap.Modal(document.getElementById('deleteUserModal'));
            deleteUserModal.show();
        });
    });

    // Add user form submission
    const saveUserBtn = document.getElementById('saveUserBtn');
    if (saveUserBtn) {
        saveUserBtn.addEventListener('click', function() {
            const form = document.getElementById('addUserForm');
            
            // In a real application, you would validate the form and submit it to the server
            // For now, we'll just close the modal
            const addUserModal = bootstrap.Modal.getInstance(document.getElementById('addUserModal'));
            addUserModal.hide();
            
            // Show success message
            alert('User added successfully!');
        });
    }

    // Edit user form submission
    const updateUserBtn = document.getElementById('updateUserBtn');
    if (updateUserBtn) {
        updateUserBtn.addEventListener('click', function() {
            const form = document.getElementById('editUserForm');
            const userId = document.getElementById('editUserId').value;
            
            // In a real application, you would validate the form and submit it to the server
            // For now, we'll just close the modal
            const editUserModal = bootstrap.Modal.getInstance(document.getElementById('editUserModal'));
            editUserModal.hide();
            
            // Show success message
            alert('User updated successfully!');
        });
    }

    // Delete user confirmation
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener('click', function() {
            const userId = document.getElementById('deleteUserId').value;
            
            // In a real application, you would send a delete request to the server
            // For now, we'll just close the modal
            const deleteUserModal = bootstrap.Modal.getInstance(document.getElementById('deleteUserModal'));
            deleteUserModal.hide();
            
            // Show success message
            alert('User deleted successfully!');
        });
    }
});
