"""
Management command to update existing QR codes to use landing URLs
"""
from django.core.management.base import BaseCommand
from django.urls import reverse
from qrcode_app.models import QRCode
import qrcode
from io import BytesIO


class Command(BaseCommand):
    help = 'Update existing QR codes to use landing URLs for better analytics'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be updated without making changes',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        # Find QR codes that need updating
        qr_codes_to_update = QRCode.objects.filter(
            qr_type='URL',
            original_url__isnull=True
        ).exclude(
            data__startswith='http://127.0.0.1:8000/qr/'
        ).exclude(
            data__startswith='http://localhost:8000/qr/'
        )
        
        total_count = qr_codes_to_update.count()
        
        if total_count == 0:
            self.stdout.write(
                self.style.SUCCESS('No QR codes need updating.')
            )
            return
        
        self.stdout.write(
            f'Found {total_count} QR codes that need updating to use landing URLs.'
        )
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN - No changes will be made.')
            )
            for qr_code in qr_codes_to_update:
                self.stdout.write(f'Would update: {qr_code.name} - {qr_code.data}')
            return
        
        updated_count = 0
        error_count = 0
        
        for qr_code in qr_codes_to_update:
            try:
                # Check if this is a valid HTTP URL
                if qr_code.data.startswith(('http://', 'https://')):
                    # Store original URL
                    qr_code.original_url = qr_code.data
                    
                    # Generate landing URL
                    landing_path = reverse('qr_landing', kwargs={'unique_id': qr_code.unique_id})
                    landing_url = f'http://127.0.0.1:8000{landing_path}'
                    
                    # Update QR code data
                    old_data = qr_code.data
                    qr_code.data = landing_url
                    
                    # Regenerate QR code image with new landing URL
                    qr = qrcode.QRCode(
                        version=40,
                        error_correction=qrcode.constants.ERROR_CORRECT_L,
                        box_size=10,
                        border=4,
                    )
                    qr.add_data(landing_url)
                    qr.make(fit=True)
                    
                    # Create the QR code image
                    img = qr.make_image(
                        fill_color=qr_code.foreground_color, 
                        back_color=qr_code.background_color
                    )
                    
                    # Save the QR code image
                    buffer = BytesIO()
                    img.save(buffer, format='PNG')
                    
                    # Save to model
                    filename = f"{qr_code.name.replace(' ', '_')}.png"
                    qr_code.image.save(filename, buffer, save=False)
                    qr_code.save()
                    
                    updated_count += 1
                    self.stdout.write(
                        f'Updated: {qr_code.name} - {old_data} -> {landing_url}'
                    )
                else:
                    self.stdout.write(
                        self.style.WARNING(f'Skipped non-HTTP URL: {qr_code.name} - {qr_code.data}')
                    )
                    
            except Exception as e:
                error_count += 1
                self.stdout.write(
                    self.style.ERROR(f'Error updating {qr_code.name}: {str(e)}')
                )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully updated {updated_count} QR codes. '
                f'Errors: {error_count}'
            )
        )
