{% extends 'base.html' %}
{% load static %}

{% block title %}
{% if campaign %}Edit Campaign{% else %}Create Campaign{% endif %}
{% endblock %}

{% block extra_css %}
<!-- Include common ads CSS -->
{% include 'ads/includes/ads_common_css.html' %}
<link rel="stylesheet" href="{% static 'ads/css/dashboard_enterprise.css' %}">
<link rel="stylesheet" href="{% static 'ads/css/campaign_management.css' %}">
<!-- Date Time Picker -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<style>
    /* Additional styles specific to this page */
</style>
{% endblock %}

{% block content %}
<!-- Campaign Form Container -->
<div class="enterprise-dashboard campaign-dashboard">
    <!-- Context-aware Header -->
    <div class="dashboard-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="dashboard-welcome">
                        <h1 class="welcome-title">{% if campaign %}Edit Campaign{% else %}Create Campaign{% endif %}</h1>
                        <p class="welcome-subtitle">{% if campaign %}Update your campaign details{% else %}Set up a new advertising campaign{% endif %}</p>
                        <a href="{% url 'ads:campaign_list' %}" class="btn btn-sm btn-outline-light mt-2">
                            <i class="fas fa-arrow-left me-1"></i> Back to Campaigns
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="dashboard-content">
        <div class="container-fluid">
            <div class="row">
                <!-- Sidebar Navigation -->
                <div class="col-lg-2 dashboard-sidebar">
                    <div class="sidebar-container">
                        <div class="sidebar-header">
                            <h2 class="sidebar-title">Campaign Form</h2>
                        </div>
                        <nav class="sidebar-nav">
                            <ul class="nav-list">
                                <li class="nav-item active">
                                    <a href="#campaign-details" class="nav-link">
                                        <i class="fas fa-info-circle"></i>
                                        <span>Campaign Details</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="#campaign-schedule" class="nav-link">
                                        <i class="fas fa-calendar-alt"></i>
                                        <span>Schedule</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="#campaign-targeting" class="nav-link">
                                        <i class="fas fa-bullseye"></i>
                                        <span>Targeting</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="#campaign-budget" class="nav-link">
                                        <i class="fas fa-money-bill-wave"></i>
                                        <span>Budget</span>
                                    </a>
                                </li>
                                {% if campaign %}
                                <li class="nav-item nav-divider">
                                    <span class="divider-label">Management</span>
                                </li>
                                <li class="nav-item">
                                    <a href="#campaign-ads" class="nav-link">
                                        <i class="fas fa-ad"></i>
                                        <span>Campaign Ads</span>
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                </div>

                <!-- Main Form Area -->
                <div class="col-lg-10 dashboard-main">
                    <form method="post" id="campaign-form">
                        {% csrf_token %}

                        <!-- Campaign Details Section -->
                        <div class="dashboard-section" id="campaign-details">
                            <div class="dashboard-card">
                                <div class="card-header">
                                    <h3 class="card-title">Campaign Details</h3>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="name" class="form-label">Campaign Name*</label>
                                                <input type="text" class="form-control" id="name" name="name" value="{{ campaign.name|default:'' }}" required>
                                                <div class="form-text">Choose a descriptive name for your campaign</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="status" class="form-label">Status</label>
                                                <select class="form-select" id="status" name="status">
                                                    <option value="draft" {% if campaign.status == 'draft' or not campaign %}selected{% endif %}>Draft</option>
                                                    <option value="active" {% if campaign.status == 'active' %}selected{% endif %}>Active</option>
                                                    <option value="paused" {% if campaign.status == 'paused' %}selected{% endif %}>Paused</option>
                                                    <option value="completed" {% if campaign.status == 'completed' %}selected{% endif %}>Completed</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="description" class="form-label">Description</label>
                                        <textarea class="form-control" id="description" name="description" rows="4">{{ campaign.description|default:'' }}</textarea>
                                        <div class="form-text">Provide details about the campaign's goals and strategy</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Campaign Schedule Section -->
                        <div class="dashboard-section" id="campaign-schedule">
                            <div class="dashboard-card">
                                <div class="card-header">
                                    <h3 class="card-title">Campaign Schedule</h3>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="start_date" class="form-label">Start Date & Time*</label>
                                                <input type="text" class="form-control datepicker" id="start_date" name="start_date" value="{{ campaign.start_date|date:'Y-m-d H:i'|default:'' }}" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="end_date" class="form-label">End Date & Time*</label>
                                                <input type="text" class="form-control datepicker" id="end_date" name="end_date" value="{{ campaign.end_date|date:'Y-m-d H:i'|default:'' }}" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="auto_activate" name="auto_activate" {% if auto_activate %}checked{% endif %}>
                                            <label class="form-check-label" for="auto_activate">
                                                Automatically activate campaign on start date
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Campaign Targeting Section -->
                        <div class="dashboard-section" id="campaign-targeting">
                            <div class="dashboard-card">
                                <div class="card-header">
                                    <h3 class="card-title">Campaign Targeting</h3>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="target_audience" class="form-label">Target Audience</label>
                                                <input type="text" class="form-control" id="target_audience" name="target_audience" value="{{ campaign.target_audience|default:'' }}">
                                                <div class="form-text">Describe your target audience (e.g., "Young professionals, 25-34")</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="target_location" class="form-label">Target Location</label>
                                                <input type="text" class="form-control" id="target_location" name="target_location" value="{{ campaign.target_location|default:'' }}">
                                                <div class="form-text">Specify geographic targeting (e.g., "Nairobi, Kenya")</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Campaign Budget Section -->
                        <div class="dashboard-section" id="campaign-budget">
                            <div class="dashboard-card">
                                <div class="card-header">
                                    <h3 class="card-title">Campaign Budget</h3>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="budget" class="form-label">Total Budget*</label>
                                                <div class="input-group">
                                                    <span class="input-group-text">$</span>
                                                    <input type="number" class="form-control" id="budget" name="budget" value="{{ campaign.budget|default:'0.00' }}" step="0.01" min="0" required>
                                                </div>
                                                <div class="form-text">Set the maximum amount you want to spend on this campaign</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {% if campaign %}
                        <!-- Campaign Ads Section -->
                        <div class="dashboard-section" id="campaign-ads">
                            <div class="dashboard-card">
                                <div class="card-header">
                                    <h3 class="card-title">Campaign Ads</h3>
                                    <div class="card-actions">
                                        <a href="{% url 'ads:ad_create_consolidated' %}?campaign={{ campaign.id }}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-plus me-1"></i> Add New Ad
                                        </a>
                                    </div>
                                </div>
                                <div class="card-body">
                                    {% if campaign.ads.all %}
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Ad Title</th>
                                                    <th>Type</th>
                                                    <th>Status</th>
                                                    <th>Impressions</th>
                                                    <th>Clicks</th>
                                                    <th>CTR</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for ad in campaign.ads.all %}
                                                <tr>
                                                    <td>{{ ad.title }}</td>
                                                    <td>{{ ad.ad_type.name }}</td>
                                                    <td><span class="badge bg-{{ ad.status }}">{{ ad.get_status_display }}</span></td>
                                                    <td>{{ ad.impressions }}</td>
                                                    <td>{{ ad.clicks }}</td>
                                                    <td>{{ ad.ctr|floatformat:2 }}%</td>
                                                    <td>
                                                        <div class="btn-group">
                                                            <a href="{% url 'ads:ad_detail' ad.slug %}" class="btn btn-sm btn-outline-primary">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                            <a href="{% url 'ads:ad_edit' ad.slug %}" class="btn btn-sm btn-outline-secondary">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                    {% else %}
                                    <div class="empty-state">
                                        <div class="empty-state-icon">
                                            <i class="fas fa-ad"></i>
                                        </div>
                                        <h3 class="empty-state-title">No ads in this campaign</h3>
                                        <p class="empty-state-description">Add ads to this campaign to start promoting your products or services.</p>
                                        <a href="{% url 'ads:ad_create' %}?campaign={{ campaign.id }}" class="btn btn-primary">
                                            <i class="fas fa-plus me-2"></i> Add New Ad
                                        </a>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Form Actions -->
                        <div class="dashboard-section">
                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i> {% if campaign %}Update Campaign{% else %}Create Campaign{% endif %}
                                </button>
                                <a href="{% url 'ads:campaign_list' %}" class="btn btn-outline-secondary ms-2">Cancel</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Flatpickr Date Time Picker -->
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<!-- Enterprise Dashboard JS -->
<script src="{% static 'ads/js/dashboard_enterprise.js' %}"></script>
<!-- Campaign Management JS -->
<script src="{% static 'ads/js/campaign_management.js' %}"></script>
<script>
    // Initialize date pickers
    document.addEventListener('DOMContentLoaded', function() {
        flatpickr(".datepicker", {
            enableTime: true,
            dateFormat: "Y-m-d H:i",
            time_24hr: true
        });
    });
</script>
{% endblock %}
