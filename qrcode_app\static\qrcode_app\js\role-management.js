document.addEventListener('DOMContentLoaded', function() {
    // Role selection functionality
    const roleLinks = document.querySelectorAll('.list-group-item');
    const selectedRoleName = document.getElementById('selectedRoleName');
    const deleteRoleBtn = document.getElementById('deleteRoleBtn');
    const savePermissionsBtn = document.getElementById('savePermissionsBtn');
    
    // Permission checkboxes
    const qrCreateCheck = document.getElementById('qrCreate');
    const qrReadCheck = document.getElementById('qrRead');
    const qrUpdateCheck = document.getElementById('qrUpdate');
    const qrDeleteCheck = document.getElementById('qrDelete');
    const userCreateCheck = document.getElementById('userCreate');
    const userReadCheck = document.getElementById('userRead');
    const userUpdateCheck = document.getElementById('userUpdate');
    const userDeleteCheck = document.getElementById('userDelete');
    const apiKeysCheck = document.getElementById('apiKeys');
    const apiAccessCheck = document.getElementById('apiAccess');
    const apiWebhooksCheck = document.getElementById('apiWebhooks');
    const batchProcessingCheck = document.getElementById('batchProcessing');
    const analyticsCheck = document.getElementById('analytics');
    const customizationCheck = document.getElementById('customization');
    
    // Role definitions (in a real app, this would come from the server)
    const roles = {
        guest: {
            name: 'Guest',
            permissions: {
                qrCreate: true,
                qrRead: true,
                qrUpdate: true,
                qrDelete: true,
                userCreate: false,
                userRead: false,
                userUpdate: false,
                userDelete: false,
                apiKeys: true,
                apiAccess: true,
                apiWebhooks: false,
                batchProcessing: false,
                analytics: false,
                customization: false
            },
            isSystem: true
        },
        user: {
            name: 'User',
            permissions: {
                qrCreate: true,
                qrRead: true,
                qrUpdate: true,
                qrDelete: true,
                userCreate: false,
                userRead: false,
                userUpdate: false,
                userDelete: false,
                apiKeys: true,
                apiAccess: true,
                apiWebhooks: false,
                batchProcessing: false,
                analytics: false,
                customization: false
            },
            isSystem: true
        },
        premium: {
            name: 'Premium User',
            permissions: {
                qrCreate: true,
                qrRead: true,
                qrUpdate: true,
                qrDelete: true,
                userCreate: false,
                userRead: false,
                userUpdate: false,
                userDelete: false,
                apiKeys: true,
                apiAccess: true,
                apiWebhooks: true,
                batchProcessing: true,
                analytics: true,
                customization: true
            },
            isSystem: true
        },
        admin: {
            name: 'Administrator',
            permissions: {
                qrCreate: true,
                qrRead: true,
                qrUpdate: true,
                qrDelete: true,
                userCreate: true,
                userRead: true,
                userUpdate: true,
                userDelete: true,
                apiKeys: true,
                apiAccess: true,
                apiWebhooks: true,
                batchProcessing: true,
                analytics: true,
                customization: true
            },
            isSystem: true
        },
        superadmin: {
            name: 'Super Administrator',
            permissions: {
                qrCreate: true,
                qrRead: true,
                qrUpdate: true,
                qrDelete: true,
                userCreate: true,
                userRead: true,
                userUpdate: true,
                userDelete: true,
                apiKeys: true,
                apiAccess: true,
                apiWebhooks: true,
                batchProcessing: true,
                analytics: true,
                customization: true
            },
            isSystem: true
        },
        manager: {
            name: 'Manager',
            permissions: {
                qrCreate: true,
                qrRead: true,
                qrUpdate: true,
                qrDelete: true,
                userCreate: true,
                userRead: true,
                userUpdate: true,
                userDelete: false,
                apiKeys: true,
                apiAccess: true,
                apiWebhooks: false,
                batchProcessing: true,
                analytics: true,
                customization: false
            },
            isSystem: false
        },
        analyst: {
            name: 'Analyst',
            permissions: {
                qrCreate: true,
                qrRead: true,
                qrUpdate: true,
                qrDelete: false,
                userCreate: false,
                userRead: true,
                userUpdate: false,
                userDelete: false,
                apiKeys: true,
                apiAccess: true,
                apiWebhooks: false,
                batchProcessing: false,
                analytics: true,
                customization: false
            },
            isSystem: false
        }
    };
    
    // Set up role selection
    roleLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active class from all links
            roleLinks.forEach(l => l.classList.remove('active'));
            
            // Add active class to clicked link
            this.classList.add('active');
            
            // Get role from data attribute
            const role = this.getAttribute('data-role');
            
            // Update selected role name
            selectedRoleName.textContent = roles[role].name;
            
            // Update permissions checkboxes
            updatePermissionsForm(role);
            
            // Enable/disable buttons based on whether it's a system role
            if (roles[role].isSystem) {
                deleteRoleBtn.disabled = true;
                savePermissionsBtn.disabled = true;
                
                // Disable all checkboxes for system roles
                disableAllCheckboxes();
            } else {
                deleteRoleBtn.disabled = false;
                savePermissionsBtn.disabled = false;
                
                // Enable all checkboxes for custom roles
                enableAllCheckboxes();
            }
        });
    });
    
    // Update permissions form based on selected role
    function updatePermissionsForm(role) {
        const permissions = roles[role].permissions;
        
        qrCreateCheck.checked = permissions.qrCreate;
        qrReadCheck.checked = permissions.qrRead;
        qrUpdateCheck.checked = permissions.qrUpdate;
        qrDeleteCheck.checked = permissions.qrDelete;
        userCreateCheck.checked = permissions.userCreate;
        userReadCheck.checked = permissions.userRead;
        userUpdateCheck.checked = permissions.userUpdate;
        userDeleteCheck.checked = permissions.userDelete;
        apiKeysCheck.checked = permissions.apiKeys;
        apiAccessCheck.checked = permissions.apiAccess;
        apiWebhooksCheck.checked = permissions.apiWebhooks;
        batchProcessingCheck.checked = permissions.batchProcessing;
        analyticsCheck.checked = permissions.analytics;
        customizationCheck.checked = permissions.customization;
    }
    
    // Disable all checkboxes
    function disableAllCheckboxes() {
        const checkboxes = document.querySelectorAll('.form-check-input');
        checkboxes.forEach(checkbox => {
            checkbox.disabled = true;
        });
    }
    
    // Enable all checkboxes
    function enableAllCheckboxes() {
        const checkboxes = document.querySelectorAll('.form-check-input');
        checkboxes.forEach(checkbox => {
            checkbox.disabled = false;
        });
    }
    
    // Create role button functionality
    const createRoleBtn = document.getElementById('createRoleBtn');
    if (createRoleBtn) {
        createRoleBtn.addEventListener('click', function() {
            const roleName = document.getElementById('roleName').value;
            const roleDescription = document.getElementById('roleDescription').value;
            const baseRole = document.getElementById('baseRole').value;
            
            if (!roleName) {
                alert('Please enter a role name');
                return;
            }
            
            // In a real application, you would send this data to the server
            // For now, we'll just close the modal
            const addRoleModal = bootstrap.Modal.getInstance(document.getElementById('addRoleModal'));
            addRoleModal.hide();
            
            // Show success message
            alert('Role created successfully!');
        });
    }
    
    // Save permissions button functionality
    if (savePermissionsBtn) {
        savePermissionsBtn.addEventListener('click', function(e) {
            e.preventDefault();
            
            // In a real application, you would send the updated permissions to the server
            // For now, we'll just show a success message
            alert('Permissions saved successfully!');
        });
    }
    
    // Delete role button functionality
    if (deleteRoleBtn) {
        deleteRoleBtn.addEventListener('click', function() {
            const roleName = selectedRoleName.textContent;
            
            if (confirm(`Are you sure you want to delete the "${roleName}" role? This action cannot be undone.`)) {
                // In a real application, you would send a delete request to the server
                // For now, we'll just show a success message
                alert('Role deleted successfully!');
            }
        });
    }
});
