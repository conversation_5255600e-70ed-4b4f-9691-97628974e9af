document.addEventListener('DOMContentLoaded', function() {
    // Initialize performance chart
    initPerformanceChart();
    
    // Initialize sidebar navigation
    initSidebarNavigation();
    
    // Initialize chart controls
    initChartControls();
});

// Initialize the performance chart
function initPerformanceChart() {
    const ctx = document.getElementById('performanceChart').getContext('2d');
    
    // Sample data for the chart
    const labels = [
        'Aug 1', 'Aug 2', 'Aug 3', 'Aug 4', 'Aug 5', 'Aug 6', 'Aug 7',
        'Aug 8', 'Aug 9', 'Aug 10', 'Aug 11', 'Aug 12', 'Aug 13', 'Aug 14',
        'Aug 15', 'Aug 16', 'Aug 17', 'Aug 18', 'Aug 19', 'Aug 20', 'Aug 21',
        'Aug 22', 'Aug 23', 'Aug 24', 'Aug 25', 'Aug 26', 'Aug 27', 'Aug 28',
        'Aug 29', 'Aug 30'
    ];
    
    const impressionsData = [
        8245, 7890, 8120, 8456, 9123, 9456, 9789,
        10123, 10456, 10789, 11023, 11456, 11789, 12123,
        12456, 12789, 13123, 13456, 13789, 14123, 14456,
        14789, 15123, 15456, 15789, 16123, 16456, 16789,
        17123, 17456
    ];
    
    const clicksData = [
        645, 590, 620, 656, 723, 756, 789,
        823, 856, 889, 923, 956, 989, 1023,
        1056, 1089, 1123, 1156, 1189, 1223, 1256,
        1289, 1323, 1356, 1389, 1423, 1456, 1489,
        1523, 1556
    ];
    
    const conversionsData = [
        125, 118, 124, 131, 145, 151, 158,
        165, 171, 178, 185, 191, 198, 205,
        211, 218, 225, 231, 238, 245, 251,
        258, 265, 271, 278, 285, 291, 298,
        305, 311
    ];
    
    const revenueData = [
        1245, 1190, 1220, 1256, 1323, 1356, 1389,
        1423, 1456, 1489, 1523, 1556, 1589, 1623,
        1656, 1689, 1723, 1756, 1789, 1823, 1856,
        1889, 1923, 1956, 1989, 2023, 2056, 2089,
        2123, 2156
    ];
    
    // Create gradient for the chart
    const gradient = ctx.createLinearGradient(0, 0, 0, 400);
    gradient.addColorStop(0, 'rgba(59, 130, 246, 0.5)');
    gradient.addColorStop(1, 'rgba(59, 130, 246, 0.0)');
    
    // Create the chart
    window.performanceChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Impressions',
                data: impressionsData,
                borderColor: '#3b82f6',
                backgroundColor: gradient,
                borderWidth: 2,
                pointBackgroundColor: '#3b82f6',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 4,
                pointHoverRadius: 6,
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    backgroundColor: '#ffffff',
                    titleColor: '#1f2937',
                    bodyColor: '#4b5563',
                    borderColor: '#e5e7eb',
                    borderWidth: 1,
                    padding: 12,
                    boxPadding: 6,
                    usePointStyle: true,
                    callbacks: {
                        labelPointStyle: function(context) {
                            return {
                                pointStyle: 'circle',
                                rotation: 0
                            };
                        }
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        color: '#9ca3af',
                        font: {
                            size: 10
                        },
                        maxRotation: 0,
                        callback: function(value, index, values) {
                            // Show only every 5th label to avoid crowding
                            return index % 5 === 0 ? this.getLabelForValue(value) : '';
                        }
                    }
                },
                y: {
                    grid: {
                        color: '#f3f4f6'
                    },
                    ticks: {
                        color: '#9ca3af',
                        font: {
                            size: 10
                        },
                        callback: function(value) {
                            if (value >= 1000) {
                                return value / 1000 + 'k';
                            }
                            return value;
                        }
                    }
                }
            }
        }
    });
}

// Initialize sidebar navigation
function initSidebarNavigation() {
    const sidebarLinks = document.querySelectorAll('.sidebar-link');
    
    sidebarLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Prevent default only if the link is pointing to a section on the same page
            const targetId = this.getAttribute('href');
            if (targetId.startsWith('#')) {
                e.preventDefault();
                
                // Remove active class from all links
                sidebarLinks.forEach(link => {
                    link.parentElement.classList.remove('active');
                });
                
                // Add active class to clicked link
                this.parentElement.classList.add('active');
                
                // Scroll to the target section
                const targetSection = document.querySelector(targetId);
                if (targetSection) {
                    targetSection.scrollIntoView({ behavior: 'smooth' });
                }
            }
        });
    });
}

// Initialize chart controls
function initChartControls() {
    const chartControls = document.querySelectorAll('.chart-control');
    
    chartControls.forEach(control => {
        control.addEventListener('click', function() {
            // Remove active class from all controls
            chartControls.forEach(ctrl => {
                ctrl.classList.remove('active');
            });
            
            // Add active class to clicked control
            this.classList.add('active');
            
            // Update chart data based on selected control
            updateChartData(this.textContent.trim());
        });
    });
}

// Update chart data based on selected metric
function updateChartData(metric) {
    if (!window.performanceChart) return;
    
    const chart = window.performanceChart;
    
    // Sample data for different metrics
    const impressionsData = [
        8245, 7890, 8120, 8456, 9123, 9456, 9789,
        10123, 10456, 10789, 11023, 11456, 11789, 12123,
        12456, 12789, 13123, 13456, 13789, 14123, 14456,
        14789, 15123, 15456, 15789, 16123, 16456, 16789,
        17123, 17456
    ];
    
    const clicksData = [
        645, 590, 620, 656, 723, 756, 789,
        823, 856, 889, 923, 956, 989, 1023,
        1056, 1089, 1123, 1156, 1189, 1223, 1256,
        1289, 1323, 1356, 1389, 1423, 1456, 1489,
        1523, 1556
    ];
    
    const conversionsData = [
        125, 118, 124, 131, 145, 151, 158,
        165, 171, 178, 185, 191, 198, 205,
        211, 218, 225, 231, 238, 245, 251,
        258, 265, 271, 278, 285, 291, 298,
        305, 311
    ];
    
    const revenueData = [
        1245, 1190, 1220, 1256, 1323, 1356, 1389,
        1423, 1456, 1489, 1523, 1556, 1589, 1623,
        1656, 1689, 1723, 1756, 1789, 1823, 1856,
        1889, 1923, 1956, 1989, 2023, 2056, 2089,
        2123, 2156
    ];
    
    // Update chart data and color based on selected metric
    switch (metric) {
        case 'Impressions':
            chart.data.datasets[0].data = impressionsData;
            chart.data.datasets[0].borderColor = '#3b82f6';
            chart.data.datasets[0].pointBackgroundColor = '#3b82f6';
            updateGradient('#3b82f6');
            break;
        case 'Clicks':
            chart.data.datasets[0].data = clicksData;
            chart.data.datasets[0].borderColor = '#8b5cf6';
            chart.data.datasets[0].pointBackgroundColor = '#8b5cf6';
            updateGradient('#8b5cf6');
            break;
        case 'Conversions':
            chart.data.datasets[0].data = conversionsData;
            chart.data.datasets[0].borderColor = '#10b981';
            chart.data.datasets[0].pointBackgroundColor = '#10b981';
            updateGradient('#10b981');
            break;
        case 'Revenue':
            chart.data.datasets[0].data = revenueData;
            chart.data.datasets[0].borderColor = '#f59e0b';
            chart.data.datasets[0].pointBackgroundColor = '#f59e0b';
            updateGradient('#f59e0b');
            break;
    }
    
    // Update chart label
    chart.data.datasets[0].label = metric;
    
    // Update chart
    chart.update();
}

// Update gradient color for chart
function updateGradient(color) {
    const ctx = document.getElementById('performanceChart').getContext('2d');
    const gradient = ctx.createLinearGradient(0, 0, 0, 400);
    gradient.addColorStop(0, color.replace(')', ', 0.5)').replace('rgb', 'rgba'));
    gradient.addColorStop(1, color.replace(')', ', 0.0)').replace('rgb', 'rgba'));
    
    window.performanceChart.data.datasets[0].backgroundColor = gradient;
}
