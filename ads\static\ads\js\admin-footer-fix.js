// Add admin-user class to body for admin users
document.addEventListener('DOMContentLoaded', function() {
    // Check if user is admin by looking for admin-specific elements
    const adminElements = document.querySelectorAll('.nav-item.nav-divider .divider-label');

    adminElements.forEach(function(element) {
        if (element.textContent.trim() === 'Admin') {
            document.body.classList.add('admin-user');
        }
    });

    // Also check for admin tab in sidebar
    const adminTab = document.querySelector('a[href="#admin"]');
    if (adminTab) {
        document.body.classList.add('admin-user');
    }

    // Check for system analytics tab
    const systemTab = document.querySelector('a[href="#system"]');
    if (systemTab) {
        document.body.classList.add('admin-user');
    }

    // Check for admin-specific links
    const adminLinks = document.querySelectorAll('a[href*="admin_"]');
    if (adminLinks.length > 0) {
        document.body.classList.add('admin-user');
    }

    // Check for superuser-specific elements
    const superuserElements = document.querySelectorAll('[data-superuser="true"]');
    if (superuserElements.length > 0) {
        document.body.classList.add('admin-user');
    }

    // Add a data attribute to the admin tab content for easier CSS targeting
    const adminTabContent = document.getElementById('admin');
    if (adminTabContent) {
        adminTabContent.setAttribute('data-admin-content', 'true');
    }
});
