# Generated by Django 4.2.7 on 2025-05-15 22:38

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255, verbose_name='Title')),
                ('message', models.TextField(verbose_name='Message')),
                ('notification_type', models.CharField(choices=[('info', 'Information'), ('success', 'Success'), ('warning', 'Warning'), ('error', 'Error')], default='info', max_length=10, verbose_name='Type')),
                ('category', models.Char<PERSON>ield(choices=[('system', 'System'), ('ad', 'Advertisement'), ('payment', 'Payment'), ('qr_code', 'QR Code'), ('user', 'User'), ('campaign', 'Campaign'), ('analytics', 'Analytics'), ('other', 'Other')], default='system', max_length=20, verbose_name='Category')),
                ('object_id', models.PositiveIntegerField(blank=True, null=True, verbose_name='Object ID')),
                ('action_url', models.CharField(blank=True, max_length=255, null=True, verbose_name='Action URL')),
                ('is_read', models.BooleanField(default=False, verbose_name='Read')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Deleted')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('content_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype', verbose_name='Content Type')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'Notification',
                'verbose_name_plural': 'Notifications',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', '-created_at'], name='notificatio_user_id_05b4bc_idx'), models.Index(fields=['user', 'is_read'], name='notificatio_user_id_427e4b_idx'), models.Index(fields=['user', 'category'], name='notificatio_user_id_4652f6_idx')],
            },
        ),
    ]
