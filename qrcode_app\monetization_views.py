"""
Monetization views for QR Generator Pro
Handles premium features like dynamic redirects, branded landing pages, and subscription management
"""
from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.utils import timezone
from django.db.models import Count, Sum
from django.core.paginator import <PERSON><PERSON>ator, EmptyPage, PageNotAnInteger

from .models import QRCode, DynamicQRRedirect, UserProfile, QRCodeBranding, QRScanLog, AILandingPage, ScanAlert, WebhookEndpoint
from .forms import QRCodeForm, QRRedirectForm, ScanAlertForm, WebhookEndpointForm


@login_required
def dynamic_redirect_dashboard(request):
    """
    Dashboard for managing dynamic QR redirects (Premium feature)
    """
    user = request.user

    # Check premium access
    try:
        user_profile = UserProfile.objects.get(user=user)
        if not user_profile.is_premium():
            messages.warning(request, 'Dynamic redirects require premium access.')
            return redirect('pricing')
    except UserProfile.DoesNotExist:
        messages.warning(request, 'Dynamic redirects require premium access.')
        return redirect('pricing')

    # Get search query
    search_query = request.GET.get('search', '').strip()

    # Get user's QR codes with dynamic redirects (with ordering for consistent pagination)
    qr_codes_with_redirects_list = QRCode.objects.filter(
        user=user,
        dynamic_redirect__isnull=False
    ).select_related('dynamic_redirect').order_by('-created_at')

    # Apply search filter if provided
    if search_query:
        qr_codes_with_redirects_list = qr_codes_with_redirects_list.filter(
            name__icontains=search_query
        )

    # Get QR codes that could have dynamic redirects (with ordering)
    eligible_qr_codes_list = QRCode.objects.filter(
        user=user,
        qr_type='URL',
        dynamic_redirect__isnull=True
    ).order_by('-created_at')

    # Pagination for active redirects
    redirects_per_page = 10  # Show 10 redirects per page
    redirects_paginator = Paginator(qr_codes_with_redirects_list, redirects_per_page)
    redirects_page = request.GET.get('redirects_page', 1)

    try:
        qr_codes_with_redirects = redirects_paginator.page(redirects_page)
    except PageNotAnInteger:
        qr_codes_with_redirects = redirects_paginator.page(1)
    except EmptyPage:
        qr_codes_with_redirects = redirects_paginator.page(redirects_paginator.num_pages)

    # Pagination for eligible QR codes
    eligible_per_page = 5  # Show 5 eligible QR codes per page
    eligible_paginator = Paginator(eligible_qr_codes_list, eligible_per_page)
    eligible_page = request.GET.get('eligible_page', 1)

    try:
        eligible_qr_codes = eligible_paginator.page(eligible_page)
    except PageNotAnInteger:
        eligible_qr_codes = eligible_paginator.page(1)
    except EmptyPage:
        eligible_qr_codes = eligible_paginator.page(eligible_paginator.num_pages)

    # Analytics
    total_redirects = DynamicQRRedirect.objects.filter(qr_code__user=user).count()
    total_clicks = DynamicQRRedirect.objects.filter(qr_code__user=user).aggregate(
        total=Sum('total_clicks')
    )['total'] or 0

    context = {
        'qr_codes_with_redirects': qr_codes_with_redirects,
        'eligible_qr_codes': eligible_qr_codes,
        'total_redirects': total_redirects,
        'total_clicks': total_clicks,
        'user_profile': user_profile,
        # Pagination info
        'redirects_paginator': redirects_paginator,
        'eligible_paginator': eligible_paginator,
        'current_redirects_page': redirects_page,
        'current_eligible_page': eligible_page,
        # Search info
        'search_query': search_query,
    }

    return render(request, 'qrcode_app/monetization/dynamic_redirects.html', context)


@login_required
@require_http_methods(["POST"])
def create_dynamic_redirect(request, qr_code_id):
    """
    Create a dynamic redirect for a QR code (Premium feature)
    """
    user = request.user

    # Check premium access
    try:
        user_profile = UserProfile.objects.get(user=user)
        if not user_profile.is_premium():
            return JsonResponse({'error': 'Premium access required'}, status=403)
    except UserProfile.DoesNotExist:
        return JsonResponse({'error': 'Premium access required'}, status=403)

    # Get QR code
    qr_code = get_object_or_404(QRCode, id=qr_code_id, user=user)

    # Check if dynamic redirect already exists
    if hasattr(qr_code, 'dynamic_redirect'):
        return JsonResponse({'error': 'Dynamic redirect already exists'}, status=400)

    # Create dynamic redirect
    current_url = request.POST.get('current_url', qr_code.original_url or qr_code.data)

    dynamic_redirect = DynamicQRRedirect.objects.create(
        qr_code=qr_code,
        current_url=current_url,
        enable_analytics=True,  # Premium users get analytics by default
    )

    messages.success(request, f'Dynamic redirect created for "{qr_code.name}". You can now change the destination without reprinting!')

    return JsonResponse({
        'success': True,
        'redirect_id': dynamic_redirect.id,
        'message': 'Dynamic redirect created successfully'
    })


@login_required
@require_http_methods(["POST"])
def update_dynamic_redirect(request, redirect_id):
    """
    Update a dynamic redirect URL (Premium feature)
    """
    user = request.user

    # Get dynamic redirect
    dynamic_redirect = get_object_or_404(
        DynamicQRRedirect,
        id=redirect_id,
        qr_code__user=user
    )

    new_url = request.POST.get('new_url')
    if not new_url:
        return JsonResponse({'error': 'New URL is required'}, status=400)

    try:
        dynamic_redirect.change_url(new_url)
        messages.success(request, f'URL updated successfully! QR code now redirects to: {new_url}')

        return JsonResponse({
            'success': True,
            'new_url': new_url,
            'redirect_count': dynamic_redirect.redirect_count,
            'remaining_changes': dynamic_redirect.max_redirects - dynamic_redirect.redirect_count
        })
    except ValueError as e:
        return JsonResponse({'error': str(e)}, status=400)


@login_required
def monetization_dashboard(request):
    """
    Main monetization dashboard showing premium features and usage
    """
    user = request.user

    try:
        user_profile = UserProfile.objects.get(user=user)
    except UserProfile.DoesNotExist:
        user_profile = None

    # Get user's QR codes and premium features usage
    total_qr_codes = QRCode.objects.filter(user=user).count()
    branded_qr_codes = QRCode.objects.filter(user=user, branding__isnull=False).count()
    dynamic_redirects = DynamicQRRedirect.objects.filter(qr_code__user=user).count()

    # Recent scans analytics
    recent_scans = QRScanLog.objects.filter(code__in=user.qr_codes.values_list('unique_id', flat=True))[:10]

    # Branding themes
    branding_themes = user.qr_brandings.all()[:5] if user_profile and user_profile.is_premium() else []

    context = {
        'user_profile': user_profile,
        'total_qr_codes': total_qr_codes,
        'branded_qr_codes': branded_qr_codes,
        'dynamic_redirects': dynamic_redirects,
        'recent_scans': recent_scans,
        'branding_themes': branding_themes,
        'is_premium': user_profile.is_premium() if user_profile else False,
    }

    return render(request, 'qrcode_app/monetization/dashboard.html', context)


@login_required
def upgrade_to_premium(request):
    """
    Upgrade user to premium (simplified for demo)
    """
    user = request.user

    try:
        user_profile = UserProfile.objects.get(user=user)
        if user_profile.role in ['user', 'guest']:
            user_profile.role = 'premium'
            user_profile.save()
            messages.success(request, '🎉 Welcome to QR Generator Pro! You now have access to all premium features.')
        else:
            messages.info(request, 'You already have premium access!')
    except UserProfile.DoesNotExist:
        # Create premium profile
        user_profile = UserProfile.objects.create(
            user=user,
            role='premium'
        )
        messages.success(request, '🎉 Welcome to QR Generator Pro! You now have access to all premium features.')

    return redirect('monetization_dashboard')


@login_required
def edit_qr_redirect(request, qr_code_id):
    """
    MODULE 2: Edit QR redirect destination (Simple dynamic redirect editing)
    """
    user = request.user

    # Get QR code
    qr_code = get_object_or_404(QRCode, id=qr_code_id, user=user)

    if request.method == 'POST':
        form = QRRedirectForm(request.POST, instance=qr_code)
        if form.is_valid():
            # Generate short code if it doesn't exist
            if not qr_code.short_code:
                qr_code.generate_short_code()

            form.save()
            messages.success(request, f'✅ QR redirect updated! Your QR code now redirects to: {qr_code.target_url}')

            # If this is an AJAX request, return JSON
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'success': True,
                    'new_url': qr_code.target_url,
                    'short_url': qr_code.get_short_redirect_url(request),
                    'message': 'QR redirect updated successfully!'
                })

            return redirect('qr_code_detail', pk=qr_code.id)
    else:
        # Pre-populate with current target URL or original data
        initial_data = {
            'target_url': qr_code.target_url or qr_code.original_url or qr_code.data
        }
        form = QRRedirectForm(instance=qr_code, initial=initial_data)

    context = {
        'form': form,
        'qr_code': qr_code,
        'short_url': qr_code.get_short_redirect_url(request),
        'current_target': qr_code.get_target_url(),
    }

    return render(request, 'qrcode_app/monetization/edit_redirect.html', context)


@login_required
def create_ai_landing_page(request, qr_code_id):
    """
    MODULE 3: Create AI-generated landing page for QR code (Premium feature)
    """
    user = request.user

    # Check premium access
    try:
        user_profile = UserProfile.objects.get(user=user)
        if not user_profile.is_premium():
            messages.warning(request, 'AI Landing Pages require premium access.')
            return redirect('pricing')
    except UserProfile.DoesNotExist:
        messages.warning(request, 'AI Landing Pages require premium access.')
        return redirect('pricing')

    # Get QR code
    qr_code = get_object_or_404(QRCode, id=qr_code_id, user=user)

    # Check if AI landing page already exists
    if hasattr(qr_code, 'ai_landing_page'):
        messages.info(request, f'AI Landing Page already exists for "{qr_code.name}". You can edit it from the QR details page.')
        return redirect('qr_code_detail', pk=qr_code.id)

    if request.method == 'POST':
        # Get form data
        business_type = request.POST.get('business_type', '')
        page_title = request.POST.get('page_title', '')
        description = request.POST.get('description', '')
        primary_color = request.POST.get('primary_color', '#3498db')
        secondary_color = request.POST.get('secondary_color', '#2c3e50')

        if not business_type or not page_title:
            messages.error(request, 'Business type and page title are required.')
            return render(request, 'qrcode_app/monetization/create_ai_page.html', {
                'qr_code': qr_code,
                'user_profile': user_profile
            })

        try:
            # Create AI landing page
            ai_page = AILandingPage.objects.create(
                qr_code=qr_code,
                title=page_title,
                business_type=business_type,
                description=description,
                primary_color=primary_color,
                secondary_color=secondary_color,
                status=AILandingPage.PageStatus.DRAFT
            )

            # Generate AI content
            from .ai_landing_utils import generate_ai_landing_page

            prompt = f"Create a landing page for {business_type}: {page_title}. {description}"
            html_content, metadata = generate_ai_landing_page(
                prompt=prompt,
                page_type='BUSINESS',
                primary_color=primary_color,
                secondary_color=secondary_color
            )

            # Update AI page with generated content
            ai_page.content = html_content
            ai_page.metadata = metadata
            ai_page.status = AILandingPage.PageStatus.ACTIVE
            ai_page.is_published = True
            ai_page.save()

            messages.success(request, f'🎉 AI Landing Page created for "{qr_code.name}"! Your QR code now shows a professional landing page.')

            # If AJAX request, return JSON
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'success': True,
                    'ai_page_id': ai_page.id,
                    'preview_url': f'/ai/{qr_code.short_code or qr_code.unique_id}/',
                    'message': 'AI Landing Page created successfully!'
                })

            return redirect('qr_code_detail', pk=qr_code.id)

        except Exception as e:
            messages.error(request, f'Error creating AI Landing Page: {str(e)}')

            # If AJAX request, return JSON error
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'success': False,
                    'error': str(e)
                }, status=500)

    context = {
        'qr_code': qr_code,
        'user_profile': user_profile,
    }

    return render(request, 'qrcode_app/monetization/create_ai_page.html', context)


@login_required
def webhook_dashboard(request):
    """
    MODULE 5: Webhook Integration Dashboard - Manage webhook endpoints
    """
    user = request.user

    # Check premium access
    try:
        user_profile = UserProfile.objects.get(user=user)
        if not user_profile.is_premium():
            messages.warning(request, 'Webhook Integration requires premium access.')
            return redirect('pricing')
    except UserProfile.DoesNotExist:
        messages.warning(request, 'Webhook Integration requires premium access.')
        return redirect('pricing')

    # Get user's webhooks with pagination
    webhooks_list = WebhookEndpoint.objects.filter(user=user).order_by('-created_at')

    # Pagination for webhooks
    webhooks_per_page = 10
    webhooks_paginator = Paginator(webhooks_list, webhooks_per_page)
    webhooks_page = request.GET.get('page', 1)

    try:
        webhooks = webhooks_paginator.page(webhooks_page)
    except PageNotAnInteger:
        webhooks = webhooks_paginator.page(1)
    except EmptyPage:
        webhooks = webhooks_paginator.page(webhooks_paginator.num_pages)

    # Calculate statistics
    total_webhooks = webhooks_list.count()
    active_webhooks = webhooks_list.filter(active=True).count()
    total_calls = sum(webhook.total_calls for webhook in webhooks_list)
    successful_calls = sum(webhook.successful_calls for webhook in webhooks_list)

    # Calculate overall success rate
    overall_success_rate = 0
    if total_calls > 0:
        overall_success_rate = round((successful_calls / total_calls) * 100, 1)

    context = {
        'webhooks': webhooks,
        'user_profile': user_profile,
        'total_webhooks': total_webhooks,
        'active_webhooks': active_webhooks,
        'total_calls': total_calls,
        'successful_calls': successful_calls,
        'overall_success_rate': overall_success_rate,
        'webhooks_paginator': webhooks_paginator,
        'current_page': webhooks_page,
    }

    return render(request, 'qrcode_app/monetization/webhook_dashboard.html', context)


@login_required
def create_webhook(request):
    """
    MODULE 5: Create new webhook endpoint
    """
    user = request.user

    # Check premium access
    try:
        user_profile = UserProfile.objects.get(user=user)
        if not user_profile.is_premium():
            messages.warning(request, 'Webhook Integration requires premium access.')
            return redirect('pricing')
    except UserProfile.DoesNotExist:
        messages.warning(request, 'Webhook Integration requires premium access.')
        return redirect('pricing')

    if request.method == 'POST':
        form = WebhookEndpointForm(request.POST, user=user)
        if form.is_valid():
            webhook = form.save()
            messages.success(request, f'🎉 Webhook created successfully! Your QR scans will now be sent to {webhook.url[:50]}...')

            # If AJAX request, return JSON
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'success': True,
                    'webhook_id': webhook.id,
                    'message': 'Webhook created successfully!'
                })

            return redirect('webhook_dashboard')
        else:
            # If AJAX request, return JSON error
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'success': False,
                    'errors': form.errors
                }, status=400)
    else:
        form = WebhookEndpointForm(user=user)

    context = {
        'form': form,
        'user_profile': user_profile,
    }

    return render(request, 'qrcode_app/monetization/create_webhook.html', context)


@login_required
def edit_webhook(request, webhook_id):
    """
    MODULE 5: Edit existing webhook endpoint
    """
    user = request.user
    webhook = get_object_or_404(WebhookEndpoint, id=webhook_id, user=user)

    # Check premium access
    try:
        user_profile = UserProfile.objects.get(user=user)
        if not user_profile.is_premium():
            messages.warning(request, 'Webhook Integration requires premium access.')
            return redirect('pricing')
    except UserProfile.DoesNotExist:
        messages.warning(request, 'Webhook Integration requires premium access.')
        return redirect('pricing')

    if request.method == 'POST':
        form = WebhookEndpointForm(request.POST, instance=webhook, user=user)
        if form.is_valid():
            webhook = form.save()
            messages.success(request, f'✅ Webhook updated successfully!')

            # If AJAX request, return JSON
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'success': True,
                    'message': 'Webhook updated successfully!'
                })

            return redirect('webhook_dashboard')
        else:
            # If AJAX request, return JSON error
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'success': False,
                    'errors': form.errors
                }, status=400)
    else:
        form = WebhookEndpointForm(instance=webhook, user=user)

    context = {
        'form': form,
        'webhook': webhook,
        'user_profile': user_profile,
    }

    return render(request, 'qrcode_app/monetization/edit_webhook.html', context)


@login_required
@require_http_methods(["POST"])
def delete_webhook(request, webhook_id):
    """
    MODULE 5: Delete webhook endpoint
    """
    user = request.user
    webhook = get_object_or_404(WebhookEndpoint, id=webhook_id, user=user)

    webhook_url = webhook.url[:50]
    webhook.delete()

    messages.success(request, f'🗑️ Webhook deleted successfully: {webhook_url}...')

    # If AJAX request, return JSON
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({
            'success': True,
            'message': 'Webhook deleted successfully!'
        })

    return redirect('webhook_dashboard')


@login_required
@require_http_methods(["POST"])
def test_webhook(request, webhook_id):
    """
    MODULE 5: Test webhook endpoint with sample data
    """
    user = request.user
    webhook = get_object_or_404(WebhookEndpoint, id=webhook_id, user=user)

    # Create Zapier-compatible test payload
    test_payload = {
        "qr_code": "test123",
        "qr_name": "Test QR Code",
        "qr_type": "URL",
        "qr_data": "https://example.com",
        "ip": "*************",
        "location": "New York, United States",
        "org": "Test Organization",
        "device_type": "Mobile",
        "browser": "Chrome",
        "country_code": "US",
        "timestamp": "2025-05-29T10:30:00Z",
        "user_agent": "Mozilla/5.0 (Test User Agent)",
        "user_id": user.id,
        "username": user.username,
        "user_email": user.email
    }

    # Fire test webhook
    try:
        import requests

        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'QR-Generator-Test-Webhook/1.0'
        }

        if webhook.secret_key:
            headers['X-Webhook-Secret'] = webhook.secret_key

        response = requests.post(
            webhook.url,
            json=test_payload,
            headers=headers,
            timeout=10
        )

        if response.status_code in [200, 201, 202, 204]:
            webhook.record_call_success()
            messages.success(request, f'✅ Test webhook successful! Response: {response.status_code}')
            success = True
            error_message = None
        else:
            error_message = f"HTTP {response.status_code}: {response.text[:200]}"
            webhook.record_call_failure(error_message)
            messages.error(request, f'❌ Test webhook failed: {error_message}')
            success = False

    except requests.exceptions.Timeout:
        error_message = "Request timeout after 10 seconds"
        webhook.record_call_failure(error_message)
        messages.error(request, f'❌ Test webhook timeout: {error_message}')
        success = False

    except requests.exceptions.ConnectionError:
        error_message = "Connection error - unable to reach webhook URL"
        webhook.record_call_failure(error_message)
        messages.error(request, f'❌ Test webhook connection error: {error_message}')
        success = False

    except Exception as e:
        error_message = f"Unexpected error: {str(e)}"
        webhook.record_call_failure(error_message)
        messages.error(request, f'❌ Test webhook error: {error_message}')
        success = False

    # If AJAX request, return JSON
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({
            'success': success,
            'message': 'Test completed',
            'error': error_message if not success else None
        })

    return redirect('webhook_dashboard')
