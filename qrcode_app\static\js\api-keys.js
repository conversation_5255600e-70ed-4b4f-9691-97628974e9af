/**
 * API Key Management JavaScript
 * 
 * This file handles the functionality for the API key management page,
 * including creating, viewing, and revoking API keys.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize the UI
    initUI();
    
    // Initialize modals
    initModals();
    
    // Initialize API key creation
    initApiKeyCreation();
    
    // Load API keys
    loadApiKeys();
});

/**
 * Initialize the user interface
 */
function initUI() {
    // Update user name in header
    updateUserInfo();
}

/**
 * Update user info in the header
 */
function updateUserInfo() {
    if (typeof rbac !== 'undefined' && rbac.currentUser) {
        const userNameElement = document.querySelector('.user-name');
        if (userNameElement) {
            userNameElement.textContent = rbac.currentUser.name;
        }
    } else {
        const userInfo = JSON.parse(localStorage.getItem('user_info'));
        if (userInfo && userInfo.name) {
            const userNameElement = document.querySelector('.user-name');
            if (userNameElement) {
                userNameElement.textContent = userInfo.name;
            }
        }
    }
}

/**
 * Initialize modals
 */
function initModals() {
    const createApiKeyModal = document.getElementById('create-api-key-modal');
    const apiKeyCreatedModal = document.getElementById('api-key-created-modal');
    const modalOverlay = document.querySelector('.modal-overlay');
    
    // Close modals when clicking close button or overlay
    document.querySelectorAll('.modal-close, .modal-overlay, [data-action="cancel"], [data-action="done"]').forEach(element => {
        element.addEventListener('click', function() {
            createApiKeyModal.classList.remove('active');
            apiKeyCreatedModal.classList.remove('active');
            modalOverlay.classList.remove('active');
        });
    });
    
    // Open create API key modal
    document.getElementById('create-api-key-btn').addEventListener('click', function() {
        createApiKeyModal.classList.add('active');
        modalOverlay.classList.add('active');
    });
    
    // Also handle empty state button
    const emptyCreateKeyBtn = document.getElementById('empty-create-key-btn');
    if (emptyCreateKeyBtn) {
        emptyCreateKeyBtn.addEventListener('click', function() {
            createApiKeyModal.classList.add('active');
            modalOverlay.classList.add('active');
        });
    }
    
    // Copy API key to clipboard
    document.getElementById('copy-api-key-btn').addEventListener('click', function() {
        const apiKeyInput = document.getElementById('new-api-key-value');
        apiKeyInput.select();
        document.execCommand('copy');
        
        // Show copied message
        this.innerHTML = '<i class="fas fa-check"></i>';
        this.classList.add('copied');
        
        // Reset after 2 seconds
        setTimeout(() => {
            this.innerHTML = '<i class="fas fa-copy"></i>';
            this.classList.remove('copied');
        }, 2000);
    });
}

/**
 * Initialize API key creation
 */
function initApiKeyCreation() {
    const createButton = document.querySelector('[data-action="create"]');
    
    if (createButton) {
        createButton.addEventListener('click', function() {
            const form = document.getElementById('create-api-key-form');
            const name = document.getElementById('api-key-name').value;
            const expiration = document.getElementById('api-key-expiration').value;
            const permissions = Array.from(document.querySelectorAll('input[name="permissions"]:checked')).map(input => input.value);
            
            if (!name) {
                showNotification('API key name is required', 'error');
                return;
            }
            
            // Generate a mock API key
            const apiKey = generateMockApiKey();
            
            // Store API key in localStorage
            storeApiKey({
                id: Date.now().toString(),
                name: name,
                key: apiKey,
                expiration: expiration,
                permissions: permissions,
                created: new Date().toISOString(),
                lastUsed: null,
                status: 'active'
            });
            
            // Display the API key
            document.getElementById('new-api-key-value').value = apiKey;
            
            // Close create modal and open created modal
            document.getElementById('create-api-key-modal').classList.remove('active');
            document.getElementById('api-key-created-modal').classList.add('active');
            
            // Reset form
            form.reset();
            
            // Reload API keys
            loadApiKeys();
        });
    }
}

/**
 * Generate a mock API key
 * @returns {string} - Mock API key
 */
function generateMockApiKey() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let key = 'eqr_';
    
    for (let i = 0; i < 32; i++) {
        key += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    return key;
}

/**
 * Store API key in localStorage
 * @param {Object} apiKey - API key object
 */
function storeApiKey(apiKey) {
    const apiKeys = JSON.parse(localStorage.getItem('api_keys')) || [];
    apiKeys.push(apiKey);
    localStorage.setItem('api_keys', JSON.stringify(apiKeys));
}

/**
 * Load API keys from localStorage
 */
function loadApiKeys() {
    const apiKeys = JSON.parse(localStorage.getItem('api_keys')) || [];
    const tableBody = document.getElementById('api-keys-table-body');
    
    // Clear existing rows
    tableBody.innerHTML = '';
    
    if (apiKeys.length === 0) {
        // Show empty state
        tableBody.innerHTML = `
            <tr class="empty-state">
                <td colspan="6">
                    <div class="empty-state-content">
                        <i class="fas fa-key"></i>
                        <p>You don't have any API keys yet.</p>
                        <button id="empty-create-key-btn" class="primary-btn">
                            <i class="fas fa-plus"></i>
                            <span>Create API Key</span>
                        </button>
                    </div>
                </td>
            </tr>
        `;
        
        // Re-initialize empty state button
        const emptyCreateKeyBtn = document.getElementById('empty-create-key-btn');
        if (emptyCreateKeyBtn) {
            emptyCreateKeyBtn.addEventListener('click', function() {
                document.getElementById('create-api-key-modal').classList.add('active');
                document.querySelector('.modal-overlay').classList.add('active');
            });
        }
        
        return;
    }
    
    // Add API key rows
    apiKeys.forEach(apiKey => {
        const row = document.createElement('tr');
        
        // Format dates
        const created = new Date(apiKey.created);
        const formattedCreated = created.toLocaleDateString() + ' ' + created.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        
        const lastUsed = apiKey.lastUsed ? new Date(apiKey.lastUsed) : null;
        const formattedLastUsed = lastUsed ? lastUsed.toLocaleDateString() + ' ' + lastUsed.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : 'Never';
        
        // Create masked key
        const maskedKey = apiKey.key.substring(0, 8) + '••••••••••••••••••••••••';
        
        row.innerHTML = `
            <td>${apiKey.name}</td>
            <td>
                <div class="api-key-display-row">
                    <span class="masked-key">${maskedKey}</span>
                    <button class="icon-btn copy-key-btn" data-key="${apiKey.key}" title="Copy to clipboard">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
            </td>
            <td>${formattedCreated}</td>
            <td>${formattedLastUsed}</td>
            <td><span class="api-key-status ${apiKey.status}">${capitalizeFirstLetter(apiKey.status)}</span></td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn revoke-btn" data-key-id="${apiKey.id}" title="Revoke API Key">
                        <i class="fas fa-ban"></i>
                    </button>
                    <button class="action-btn delete-btn" data-key-id="${apiKey.id}" title="Delete API Key">
                        <i class="fas fa-trash-alt"></i>
                    </button>
                </div>
            </td>
        `;
        
        tableBody.appendChild(row);
    });
    
    // Add event listeners to copy buttons
    document.querySelectorAll('.copy-key-btn').forEach(button => {
        button.addEventListener('click', function() {
            const key = this.getAttribute('data-key');
            
            // Create temporary input element
            const tempInput = document.createElement('input');
            tempInput.value = key;
            document.body.appendChild(tempInput);
            
            // Select and copy
            tempInput.select();
            document.execCommand('copy');
            
            // Remove temporary element
            document.body.removeChild(tempInput);
            
            // Show copied message
            this.innerHTML = '<i class="fas fa-check"></i>';
            this.classList.add('copied');
            
            // Reset after 2 seconds
            setTimeout(() => {
                this.innerHTML = '<i class="fas fa-copy"></i>';
                this.classList.remove('copied');
            }, 2000);
        });
    });
    
    // Add event listeners to revoke buttons
    document.querySelectorAll('.revoke-btn').forEach(button => {
        button.addEventListener('click', function() {
            const keyId = this.getAttribute('data-key-id');
            
            if (confirm('Are you sure you want to revoke this API key? This action cannot be undone.')) {
                revokeApiKey(keyId);
            }
        });
    });
    
    // Add event listeners to delete buttons
    document.querySelectorAll('.delete-btn').forEach(button => {
        button.addEventListener('click', function() {
            const keyId = this.getAttribute('data-key-id');
            
            if (confirm('Are you sure you want to delete this API key? This action cannot be undone.')) {
                deleteApiKey(keyId);
            }
        });
    });
}

/**
 * Revoke an API key
 * @param {string} keyId - API key ID
 */
function revokeApiKey(keyId) {
    const apiKeys = JSON.parse(localStorage.getItem('api_keys')) || [];
    const keyIndex = apiKeys.findIndex(key => key.id === keyId);
    
    if (keyIndex !== -1) {
        apiKeys[keyIndex].status = 'revoked';
        localStorage.setItem('api_keys', JSON.stringify(apiKeys));
        
        // Reload API keys
        loadApiKeys();
        
        // Show notification
        showNotification('API key revoked successfully', 'success');
    }
}

/**
 * Delete an API key
 * @param {string} keyId - API key ID
 */
function deleteApiKey(keyId) {
    const apiKeys = JSON.parse(localStorage.getItem('api_keys')) || [];
    const filteredKeys = apiKeys.filter(key => key.id !== keyId);
    
    localStorage.setItem('api_keys', JSON.stringify(filteredKeys));
    
    // Reload API keys
    loadApiKeys();
    
    // Show notification
    showNotification('API key deleted successfully', 'success');
}

/**
 * Show a notification
 * @param {string} message - Notification message
 * @param {string} type - Notification type (success, error, info)
 */
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    
    // Add icon based on type
    let icon = 'info-circle';
    if (type === 'success') icon = 'check-circle';
    if (type === 'error') icon = 'exclamation-circle';
    
    notification.innerHTML = `
        <i class="fas fa-${icon}"></i>
        <span>${message}</span>
        <button class="notification-close"><i class="fas fa-times"></i></button>
    `;
    
    // Add to the DOM
    document.body.appendChild(notification);
    
    // Add styles if not already added
    if (!document.getElementById('notification-styles')) {
        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            .notification {
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem;
                background-color: white;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                display: flex;
                align-items: center;
                gap: 0.75rem;
                z-index: 1000;
                max-width: 350px;
                transform: translateX(120%);
                transition: transform 0.3s ease;
                border-left: 4px solid #3b82f6;
            }
            
            .notification.show {
                transform: translateX(0);
            }
            
            .notification i {
                font-size: 1.25rem;
                flex-shrink: 0;
            }
            
            .notification span {
                font-size: 0.875rem;
                color: #374151;
                flex-grow: 1;
            }
            
            .notification-close {
                background: none;
                border: none;
                color: #9ca3af;
                cursor: pointer;
                font-size: 0.875rem;
                padding: 0.25rem;
                transition: color 0.2s ease;
            }
            
            .notification-close:hover {
                color: #4b5563;
            }
            
            .notification-success {
                border-left-color: #10b981;
            }
            
            .notification-success i {
                color: #10b981;
            }
            
            .notification-error {
                border-left-color: #ef4444;
            }
            
            .notification-error i {
                color: #ef4444;
            }
            
            .notification-info i {
                color: #3b82f6;
            }
        `;
        document.head.appendChild(style);
    }
    
    // Show the notification
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);
    
    // Set up close button
    const closeButton = notification.querySelector('.notification-close');
    closeButton.addEventListener('click', () => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    });
    
    // Auto-close after 5 seconds
    setTimeout(() => {
        if (document.body.contains(notification)) {
            notification.classList.remove('show');
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    notification.remove();
                }
            }, 300);
        }
    }, 5000);
}

/**
 * Capitalize the first letter of a string
 * @param {string} string - String to capitalize
 * @returns {string} - Capitalized string
 */
function capitalizeFirstLetter(string) {
    return string.charAt(0).toUpperCase() + string.slice(1);
}
