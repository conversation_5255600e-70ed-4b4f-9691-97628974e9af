// Global Footer Fix
document.addEventListener('DOMContentLoaded', function() {
    // Function to fix footer positioning
    function fixFooterPosition() {
        const body = document.body;
        const footer = document.querySelector('.enterprise-footer, .footer-main, footer');
        const main = document.querySelector('main');
        
        if (!footer || !main) return;
        
        // Get the viewport height
        const viewportHeight = window.innerHeight;
        
        // Get the height of all content
        const contentHeight = main.offsetHeight + footer.offsetHeight;
        
        // If content is less than viewport, make main take up remaining space
        if (contentHeight < viewportHeight) {
            main.style.flex = '1 0 auto';
        } else {
            main.style.flex = '0 0 auto';
        }
        
        // Remove any margin from the footer
        footer.style.marginTop = '0';
        footer.style.marginBottom = '0';
    }
    
    // Run on page load
    fixFooterPosition();
    
    // Run on window resize
    window.addEventListener('resize', fixFooterPosition);
    
    // Run after any dynamic content loads
    window.addEventListener('load', fixFooterPosition);
    
    // Run after AJAX requests complete
    const originalXHR = window.XMLHttpRequest;
    window.XMLHttpRequest = function() {
        const xhr = new originalXHR();
        const originalOnReadyStateChange = xhr.onreadystatechange;
        
        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4) {
                setTimeout(fixFooterPosition, 100);
            }
            if (originalOnReadyStateChange) {
                originalOnReadyStateChange.apply(this, arguments);
            }
        };
        
        return xhr;
    };
});
