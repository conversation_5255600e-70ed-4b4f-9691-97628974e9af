"""
Test script for offline mode
Run this script to test the offline mode functionality
"""
import os
import sys
import logging
import json
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the project root to the Python path
sys.path.append(str(Path(__file__).parent.parent))

# Set environment variable to force offline mode
os.environ['USE_LOCAL_MODE'] = 'true'

# Import the AI client
from ai_services.clients import get_ai_client, OfflineAIClient
from ai_services.network import check_internet_connection, get_best_available_provider

def test_offline_mode():
    """Test the offline mode functionality"""
    logger.info("Testing offline mode...")
    
    # Check internet connection (should return False in offline mode)
    internet_available = check_internet_connection()
    logger.info(f"Internet available: {internet_available}")
    
    # Get the best available provider (should return 'offline' in offline mode)
    best_provider = get_best_available_provider()
    logger.info(f"Best available provider: {best_provider}")
    
    # Get the AI client (should return OfflineAIClient in offline mode)
    client = get_ai_client()
    logger.info(f"AI client: {client.__class__.__name__}")
    
    # Generate ad suggestions
    suggestions = client.generate_ad_suggestions(
        language="english",
        business_type="restaurant",
        target_audience="food lovers",
        tone="professional",
        num_suggestions=3,
        ad_title="Taste of Kenya"
    )
    
    # Print the suggestions
    logger.info(f"Generated {len(suggestions)} suggestions:")
    for i, suggestion in enumerate(suggestions):
        logger.info(f"Suggestion {i+1}:")
        logger.info(f"  Title: {suggestion['title']}")
        logger.info(f"  Content: {suggestion['content']}")
        logger.info(f"  Model: {suggestion.get('model', 'unknown')}")
        logger.info(f"  AI Generated: {suggestion.get('ai_generated', False)}")
        logger.info(f"  Offline: {suggestion.get('offline', False)}")
    
    # Return the suggestions for further inspection
    return suggestions

if __name__ == "__main__":
    # Run the test
    suggestions = test_offline_mode()
    
    # Save the suggestions to a file for inspection
    output_file = Path(__file__).parent / "offline_test_results.json"
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(suggestions, f, indent=2)
    
    logger.info(f"Test results saved to {output_file}")
