{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Enterprise QR | New Navbar Test</title>

    <!-- Favicon -->
    <link rel="shortcut icon" href="{% static 'favicon.ico' %}" type="image/x-icon">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- New Navbar CSS -->
    <link rel="stylesheet" href="{% static 'css/new_nav.css' %}">
    <link rel="stylesheet" href="{% static 'css/new_nav_dropdowns.css' %}">
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <!-- Include the new navbar -->
    {% include 'includes/new_navbar.html' %}

    <main>
        <div class="container py-5">
            <div class="row">
                <div class="col-md-12">
                    <div class="card shadow-sm">
                        <div class="card-body">
                            <h1 class="mb-4">New Enterprise-Grade Navbar</h1>
                            <p class="lead">This page showcases our new premium, enterprise-grade navigation system with elegant animations, responsive design, and professional aesthetics.</p>
                            
                            <h2 class="mt-5 mb-3">Key Features</h2>
                            <ul class="list-group list-group-flush mb-4">
                                <li class="list-group-item">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    Sleek, modern design with professional aesthetics
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    Responsive layout that works perfectly on all devices
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    Elegant hover animations and transitions
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    Dropdown menus that work on hover for desktop and click for mobile
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    Notification system with pulsating badge for multiple notifications
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    Premium styling with glossy accents and professional color scheme
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    Scroll effect that changes navbar appearance when scrolling
                                </li>
                            </ul>
                            
                            <h2 class="mt-5 mb-3">Implementation Details</h2>
                            <p>The new navbar has been implemented with a focus on:</p>
                            <ul>
                                <li>Clean, modular code organization</li>
                                <li>Separation of concerns (HTML, CSS, JavaScript)</li>
                                <li>Accessibility best practices</li>
                                <li>Performance optimization</li>
                                <li>Consistent theming using CSS variables</li>
                                <li>Smooth animations and transitions</li>
                            </ul>
                            
                            <div class="alert alert-info mt-5">
                                <i class="fas fa-info-circle me-2"></i>
                                Try scrolling down to see the navbar's scroll effect, and test the responsive behavior by resizing your browser window.
                            </div>
                            
                            <div class="mt-5 pt-5">
                                <h3>Scroll down to see more content...</h3>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Additional content to enable scrolling -->
                    <div class="card shadow-sm mt-5">
                        <div class="card-body">
                            <h2>Navbar Components</h2>
                            <p>The new navbar includes the following components:</p>
                            
                            <h4 class="mt-4">1. Brand/Logo</h4>
                            <p>A sleek, modern logo with gradient background and subtle hover effect.</p>
                            
                            <h4 class="mt-4">2. Main Navigation</h4>
                            <p>Primary navigation links with icons and active state indicators.</p>
                            
                            <h4 class="mt-4">3. Dropdown Menus</h4>
                            <p>Elegant dropdown menus with icons, dividers, and premium badges.</p>
                            
                            <h4 class="mt-4">4. Notification System</h4>
                            <p>Comprehensive notification system with unread indicators, timestamps, and action buttons.</p>
                            
                            <h4 class="mt-4">5. User Profile</h4>
                            <p>User profile dropdown with quick access to profile settings and logout.</p>
                            
                            <h4 class="mt-4">6. Mobile Toggle</h4>
                            <p>Responsive mobile toggle that transforms the navigation for smaller screens.</p>
                        </div>
                    </div>
                    
                    <div class="card shadow-sm mt-5 mb-5">
                        <div class="card-body">
                            <h2>Technical Implementation</h2>
                            <p>The navbar is implemented using:</p>
                            
                            <h4 class="mt-4">CSS Features</h4>
                            <ul>
                                <li>CSS Variables for consistent theming</li>
                                <li>Flexbox for layout</li>
                                <li>CSS Transitions and Animations</li>
                                <li>Media Queries for responsive design</li>
                                <li>CSS Gradients and Shadows for depth</li>
                            </ul>
                            
                            <h4 class="mt-4">JavaScript Features</h4>
                            <ul>
                                <li>Event delegation for performance</li>
                                <li>Responsive behavior detection</li>
                                <li>Scroll event handling</li>
                                <li>Dropdown menu management</li>
                                <li>Notification counter animation</li>
                            </ul>
                            
                            <div class="alert alert-success mt-5">
                                <i class="fas fa-thumbs-up me-2"></i>
                                The new navbar is ready to be integrated into the main application without changing any existing logic.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{% static 'js/new_nav.js' %}"></script>
</body>
</html>
