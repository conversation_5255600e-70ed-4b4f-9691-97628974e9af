/**
 * Ad Creation Form JavaScript
 * Handles the multi-step form, preview, and pricing calculations
 */

document.addEventListener('DOMContentLoaded', function() {
    // Form elements
    const adCreationForm = document.getElementById('adCreationForm');
    const adCreationTabs = document.getElementById('adCreationTabs');
    const progressBar = document.querySelector('.progress-bar');
    const nextButtons = document.querySelectorAll('.btn-next');
    const prevButtons = document.querySelectorAll('.btn-prev');
    const submitButton = document.getElementById('submitAd');
    
    // Form fields
    const adTitleInput = document.getElementById('adTitle');
    const adTypeSelect = document.getElementById('adType');
    const adContentTextarea = document.getElementById('adContent');
    const ctaLinkInput = document.getElementById('ctaLink');
    const adMediaInput = document.getElementById('adMedia');
    const durationOptionSelect = document.getElementById('durationOption');
    const startDateInput = document.getElementById('startDate');
    const startTimeInput = document.getElementById('startTime');
    const endDateInput = document.getElementById('endDate');
    const endTimeInput = document.getElementById('endTime');
    const customDurationFields = document.getElementById('customDurationFields');
    const endDateTimeDisplay = document.getElementById('endDateTimeDisplay');
    const adLocationSelect = document.getElementById('ad_location');
    const useSmartEngineCheckbox = document.getElementById('useSmartEngine');
    const requiresAiCheckbox = document.getElementById('requiresAi');
    const wantsSocialCheckbox = document.getElementById('wantsSocial');
    
    // Preview elements
    const previewLocationTypeSelect = document.getElementById('previewLocationType');
    const previewLocationLabel = document.getElementById('previewLocationLabel');
    const previewWrapper = document.getElementById('previewWrapper');
    const previewTitle = document.getElementById('previewTitle');
    const previewContent = document.getElementById('previewContent');
    const previewImage = document.getElementById('previewImage');
    const previewCta = document.getElementById('previewCta');
    const previewSizeInfo = document.getElementById('previewSizeInfo');
    
    // Pricing elements
    const basePriceElement = document.getElementById('basePrice');
    const durationDaysElement = document.getElementById('durationDays');
    const locationMultiplierElement = document.getElementById('locationMultiplier');
    const locationPriceElement = document.getElementById('locationPrice');
    const aiPriceElement = document.getElementById('aiPrice');
    const socialPriceElement = document.getElementById('socialPrice');
    const totalPriceElement = document.getElementById('totalPrice');
    const basePricingInput = document.getElementById('basePricingInput');
    const finalPricingInput = document.getElementById('finalPricingInput');
    
    // Review elements
    const reviewTitle = document.getElementById('reviewTitle');
    const reviewType = document.getElementById('reviewType');
    const reviewDuration = document.getElementById('reviewDuration');
    const reviewLocation = document.getElementById('reviewLocation');
    const reviewContent = document.getElementById('reviewContent');
    const reviewImpressions = document.getElementById('reviewImpressions');
    const reviewPrice = document.getElementById('reviewPrice');
    
    // Constants
    const AI_PRICE = 50; // KSH
    const SOCIAL_PRICE = 30; // KSH
    
    // Variables
    let basePrice = 0;
    let durationDays = 7;
    let locationMultiplier = 1;
    let locationPrice = 0;
    let aiPrice = 0;
    let socialPrice = 0;
    let totalPrice = 0;
    
    // Initialize form
    initForm();
    
    // Initialize preview
    initPreview();
    
    // Initialize pricing
    initPricing();
    
    // Function to initialize form
    function initForm() {
        // Set default start date to today
        const today = new Date();
        const formattedDate = today.toISOString().split('T')[0];
        const formattedTime = today.getHours().toString().padStart(2, '0') + ':' + 
                             today.getMinutes().toString().padStart(2, '0');
        
        if (startDateInput) {
            startDateInput.value = formattedDate;
        }
        
        if (startTimeInput) {
            startTimeInput.value = formattedTime;
        }
        
        // Handle duration option change
        if (durationOptionSelect) {
            durationOptionSelect.addEventListener('change', function() {
                if (this.value === 'custom') {
                    if (customDurationFields) {
                        customDurationFields.style.display = 'flex';
                    }
                } else {
                    if (customDurationFields) {
                        customDurationFields.style.display = 'none';
                    }
                    
                    // Set duration days based on selection
                    switch (this.value) {
                        case '7days':
                            durationDays = 7;
                            break;
                        case '2weeks':
                            durationDays = 14;
                            break;
                        case 'monthly':
                            durationDays = 30;
                            break;
                        case 'yearly':
                            durationDays = 365;
                            break;
                        default:
                            durationDays = 7;
                    }
                    
                    // Update end date display
                    updateEndDateDisplay();
                }
                
                // Update pricing
                updatePricing();
            });
        }
        
        // Handle custom duration changes
        if (endDateInput) {
            endDateInput.addEventListener('change', function() {
                calculateCustomDuration();
            });
        }
        
        if (endTimeInput) {
            endTimeInput.addEventListener('change', function() {
                calculateCustomDuration();
            });
        }
        
        // Handle next button clicks
        nextButtons.forEach(button => {
            button.addEventListener('click', function() {
                const nextStep = this.dataset.next;
                const nextTab = document.getElementById(nextStep + '-tab');
                
                if (validateCurrentStep()) {
                    if (nextTab) {
                        nextTab.click();
                        updateProgressBar();
                    }
                }
            });
        });
        
        // Handle previous button clicks
        prevButtons.forEach(button => {
            button.addEventListener('click', function() {
                const prevStep = this.dataset.prev;
                const prevTab = document.getElementById(prevStep + '-tab');
                
                if (prevTab) {
                    prevTab.click();
                    updateProgressBar();
                }
            });
        });
        
        // Handle tab changes
        if (adCreationTabs) {
            adCreationTabs.addEventListener('shown.bs.tab', function(event) {
                updateProgressBar();
                
                // If on review tab, update review content
                if (event.target.id === 'step4-tab') {
                    updateReviewContent();
                }
            });
        }
        
        // Handle form submission
        if (adCreationForm) {
            adCreationForm.addEventListener('submit', function(event) {
                if (!validateAllSteps()) {
                    event.preventDefault();
                }
            });
        }
        
        // Update end date display initially
        updateEndDateDisplay();
    }
    
    // Function to initialize preview
    function initPreview() {
        // Handle title input changes
        if (adTitleInput) {
            adTitleInput.addEventListener('input', function() {
                if (previewTitle) {
                    previewTitle.textContent = this.value || 'Your Ad Title';
                }
            });
        }
        
        // Handle content input changes
        if (adContentTextarea) {
            adContentTextarea.addEventListener('input', function() {
                if (previewContent) {
                    previewContent.textContent = this.value || 'Your ad content will appear here as you type.';
                }
            });
        }
        
        // Handle CTA link changes
        if (ctaLinkInput) {
            ctaLinkInput.addEventListener('input', function() {
                if (previewCta) {
                    previewCta.href = this.value || '#';
                }
            });
        }
        
        // Handle media upload preview
        if (adMediaInput) {
            adMediaInput.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    const reader = new FileReader();
                    
                    reader.onload = function(e) {
                        if (previewImage) {
                            const img = previewImage.querySelector('img');
                            if (img) {
                                img.src = e.target.result;
                            }
                        }
                    };
                    
                    reader.readAsDataURL(this.files[0]);
                }
            });
        }
        
        // Handle preview location type changes
        if (previewLocationTypeSelect) {
            previewLocationTypeSelect.addEventListener('change', function() {
                updatePreviewLocation(this.value);
            });
        }
    }
    
    // Function to initialize pricing
    function initPricing() {
        // Handle ad type changes
        if (adTypeSelect) {
            adTypeSelect.addEventListener('change', function() {
                const selectedOption = this.options[this.selectedIndex];
                if (selectedOption) {
                    basePrice = parseFloat(selectedOption.dataset.price || 0);
                    updatePricing();
                }
            });
        }
        
        // Handle ad location changes
        if (adLocationSelect) {
            adLocationSelect.addEventListener('change', function() {
                const selectedOption = this.options[this.selectedIndex];
                if (selectedOption) {
                    locationMultiplier = parseFloat(selectedOption.dataset.multiplier || 1);
                    updatePricing();
                    
                    // Update impressions in review
                    if (reviewImpressions) {
                        const impressions = selectedOption.dataset.impressions || '0';
                        reviewImpressions.textContent = impressions + ' views per day';
                    }
                }
            });
        }
        
        // Handle AI checkbox changes
        if (useSmartEngineCheckbox) {
            useSmartEngineCheckbox.addEventListener('change', function() {
                updatePricing();
            });
        }
        
        if (requiresAiCheckbox) {
            requiresAiCheckbox.addEventListener('change', function() {
                updatePricing();
            });
        }
        
        // Handle social media checkbox changes
        if (wantsSocialCheckbox) {
            wantsSocialCheckbox.addEventListener('change', function() {
                updatePricing();
            });
        }
    }
    
    // Function to update pricing
    function updatePricing() {
        // Calculate location price
        locationPrice = basePrice * locationMultiplier;
        
        // Calculate AI price
        aiPrice = (useSmartEngineCheckbox && useSmartEngineCheckbox.checked) || 
                 (requiresAiCheckbox && requiresAiCheckbox.checked) ? AI_PRICE : 0;
        
        // Calculate social price
        socialPrice = wantsSocialCheckbox && wantsSocialCheckbox.checked ? SOCIAL_PRICE : 0;
        
        // Calculate total price
        totalPrice = locationPrice + aiPrice + socialPrice;
        
        // Update pricing display
        if (basePriceElement) {
            basePriceElement.textContent = basePrice.toFixed(2) + ' KSH';
        }
        
        if (durationDaysElement) {
            durationDaysElement.textContent = durationDays + ' days';
        }
        
        if (locationMultiplierElement) {
            locationMultiplierElement.textContent = 'x' + locationMultiplier.toFixed(1);
        }
        
        if (locationPriceElement) {
            locationPriceElement.textContent = locationPrice.toFixed(2) + ' KSH';
        }
        
        if (aiPriceElement) {
            aiPriceElement.textContent = aiPrice.toFixed(2) + ' KSH';
        }
        
        if (socialPriceElement) {
            socialPriceElement.textContent = socialPrice.toFixed(2) + ' KSH';
        }
        
        if (totalPriceElement) {
            totalPriceElement.textContent = totalPrice.toFixed(2) + ' KSH';
        }
        
        // Update hidden inputs
        if (basePricingInput) {
            basePricingInput.value = basePrice.toFixed(2);
        }
        
        if (finalPricingInput) {
            finalPricingInput.value = totalPrice.toFixed(2);
        }
    }
    
    // Function to update progress bar
    function updateProgressBar() {
        if (!progressBar) return;
        
        const activeTab = document.querySelector('.nav-link.active');
        if (!activeTab) return;
        
        const tabId = activeTab.id;
        let progress = 0;
        
        switch (tabId) {
            case 'step1-tab':
                progress = 25;
                break;
            case 'step2-tab':
                progress = 50;
                break;
            case 'step3-tab':
                progress = 75;
                break;
            case 'step4-tab':
                progress = 100;
                break;
            default:
                progress = 0;
        }
        
        progressBar.style.width = progress + '%';
        progressBar.setAttribute('aria-valuenow', progress);
    }
    
    // Function to update end date display
    function updateEndDateDisplay() {
        if (!endDateTimeDisplay || !startDateInput || !startTimeInput) return;
        
        const startDate = new Date(startDateInput.value + 'T' + startTimeInput.value);
        if (isNaN(startDate.getTime())) return;
        
        const endDate = new Date(startDate);
        endDate.setDate(endDate.getDate() + durationDays);
        
        // Add 2 hours bonus time
        endDate.setHours(endDate.getHours() + 2);
        
        const options = { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };
        
        endDateTimeDisplay.textContent = endDate.toLocaleDateString('en-US', options);
    }
    
    // Function to calculate custom duration
    function calculateCustomDuration() {
        if (!startDateInput || !startTimeInput || !endDateInput || !endTimeInput) return;
        
        const startDate = new Date(startDateInput.value + 'T' + startTimeInput.value);
        const endDate = new Date(endDateInput.value + 'T' + endTimeInput.value);
        
        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) return;
        
        // Calculate difference in days
        const diffTime = Math.abs(endDate - startDate);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        // Ensure minimum 1 day
        durationDays = Math.max(1, diffDays);
        
        // Update pricing
        updatePricing();
    }
    
    // Function to update preview location
    function updatePreviewLocation(locationType) {
        if (!previewLocationLabel || !previewWrapper || !previewSizeInfo) return;
        
        // Update location label
        previewLocationLabel.textContent = locationType.charAt(0).toUpperCase() + locationType.slice(1) + ' Preview';
        
        // Update preview wrapper styles
        switch (locationType) {
            case 'header':
                previewWrapper.style.width = '100%';
                previewWrapper.style.height = '90px';
                previewSizeInfo.textContent = 'Header size: 728x90px';
                break;
            case 'sidebar':
                previewWrapper.style.width = '300px';
                previewWrapper.style.height = '600px';
                previewSizeInfo.textContent = 'Sidebar size: 300x600px';
                break;
            case 'content':
                previewWrapper.style.width = '100%';
                previewWrapper.style.height = '250px';
                previewSizeInfo.textContent = 'Content size: 728x250px';
                break;
            case 'footer':
                previewWrapper.style.width = '100%';
                previewWrapper.style.height = '90px';
                previewSizeInfo.textContent = 'Footer size: 728x90px';
                break;
            default:
                previewWrapper.style.width = '300px';
                previewWrapper.style.height = '250px';
                previewSizeInfo.textContent = 'Standard size: 300x250px';
        }
    }
    
    // Function to update review content
    function updateReviewContent() {
        // Update title
        if (reviewTitle && adTitleInput) {
            reviewTitle.textContent = adTitleInput.value || 'Not specified';
        }
        
        // Update type
        if (reviewType && adTypeSelect) {
            const selectedOption = adTypeSelect.options[adTypeSelect.selectedIndex];
            reviewType.textContent = selectedOption ? selectedOption.text : 'Not selected';
        }
        
        // Update duration
        if (reviewDuration) {
            reviewDuration.textContent = durationDays + ' days';
        }
        
        // Update location
        if (reviewLocation && adLocationSelect) {
            const selectedOption = adLocationSelect.options[adLocationSelect.selectedIndex];
            reviewLocation.textContent = selectedOption ? selectedOption.text : 'Not selected';
        }
        
        // Update content
        if (reviewContent && adContentTextarea) {
            const content = adContentTextarea.value;
            reviewContent.textContent = content ? (content.length > 100 ? content.substring(0, 100) + '...' : content) : 'Not specified';
        }
        
        // Update price
        if (reviewPrice) {
            reviewPrice.textContent = totalPrice.toFixed(2) + ' KSH';
        }
    }
    
    // Function to validate current step
    function validateCurrentStep() {
        const activeTab = document.querySelector('.tab-pane.active');
        if (!activeTab) return true;
        
        const tabId = activeTab.id;
        let isValid = true;
        
        switch (tabId) {
            case 'step1':
                // Validate basic info
                if (adTitleInput && !adTitleInput.value.trim()) {
                    alert('Please enter an ad title');
                    adTitleInput.focus();
                    isValid = false;
                } else if (adTypeSelect && adTypeSelect.value === '') {
                    alert('Please select an ad type');
                    adTypeSelect.focus();
                    isValid = false;
                }
                break;
            case 'step2':
                // Validate content
                if (adContentTextarea && !adContentTextarea.value.trim()) {
                    alert('Please enter ad content');
                    adContentTextarea.focus();
                    isValid = false;
                }
                break;
            case 'step3':
                // Validate options
                if (adLocationSelect && adLocationSelect.value === '') {
                    alert('Please select an ad placement location');
                    adLocationSelect.focus();
                    isValid = false;
                }
                break;
        }
        
        return isValid;
    }
    
    // Function to validate all steps
    function validateAllSteps() {
        // Validate basic info
        if (adTitleInput && !adTitleInput.value.trim()) {
            alert('Please enter an ad title');
            document.getElementById('step1-tab').click();
            adTitleInput.focus();
            return false;
        }
        
        if (adTypeSelect && adTypeSelect.value === '') {
            alert('Please select an ad type');
            document.getElementById('step1-tab').click();
            adTypeSelect.focus();
            return false;
        }
        
        // Validate content
        if (adContentTextarea && !adContentTextarea.value.trim()) {
            alert('Please enter ad content');
            document.getElementById('step2-tab').click();
            adContentTextarea.focus();
            return false;
        }
        
        // Validate options
        if (adLocationSelect && adLocationSelect.value === '') {
            alert('Please select an ad placement location');
            document.getElementById('step3-tab').click();
            adLocationSelect.focus();
            return false;
        }
        
        return true;
    }
    
    // Make updatePricing available globally
    window.updatePricing = updatePricing;
});
