/**
 * Enterprise QR - New Navigation Bar Dropdowns
 * Styles for dropdown menus and notification panels
 */

/* Dropdown container */
.new-navbar-dropdown {
    position: absolute;
    top: calc(100% + 0.5rem);
    left: 0;
    min-width: 240px;
    background-color: var(--dropdown-bg);
    border-radius: var(--radius-lg);
    box-shadow: 0 15px 25px -5px var(--dropdown-shadow),
                0 8px 10px -6px var(--dropdown-shadow),
                0 0 0 1px rgba(0, 0, 0, 0.05);
    border: 1px solid var(--dropdown-border);
    padding: var(--spacing-sm) 0;
    display: none;
    z-index: 1001;
    transform-origin: top center;
    overflow: hidden;
    backdrop-filter: blur(10px);
    transform: translateY(10px);
    transition: transform var(--transition-fast) ease, opacity var(--transition-fast) ease;
    opacity: 0;
    text-align: left; /* Ensure all content is left-aligned */
}



/* Show dropdown */
.new-navbar-dropdown.show {
    display: block;
    transform: translateY(0);
    opacity: 1;
}

/* Dropdown items container */
.new-navbar-dropdown-menu {
    list-style: none;
    margin: 0;
    padding: 0;
    width: 100%; /* Ensure full width */
    text-align: left; /* Force left alignment */
}

/* Dropdown item */
.new-navbar-dropdown-item {
    display: flex;
    align-items: center;
    justify-content: flex-start; /* Align to the left */
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--dropdown-text);
    font-size: 0.875rem;
    text-decoration: none;
    transition: all var(--transition-fast) ease;
    position: relative;
    margin: 0 var(--spacing-xs);
    border-radius: var(--radius-md);
    text-align: left; /* Ensure text is left-aligned */
    width: calc(100% - var(--spacing-xs) * 2); /* Ensure full width minus margins */
}

.new-navbar-dropdown-item:hover {
    background-color: var(--dropdown-hover);
    color: var(--accent-primary);
    padding-left: calc(var(--spacing-md) + 3px);
}

.new-navbar-dropdown-item.active {
    color: var(--accent-primary);
    font-weight: 600;
    background-color: rgba(79, 156, 249, 0.08);
}

/* Dropdown item icon */
.new-navbar-dropdown-icon {
    margin-right: var(--spacing-md); /* Increased margin between icon and text */
    font-size: 0.875rem;
    color: inherit;
    opacity: 0.8;
    transition: transform var(--transition-fast) ease;
    min-width: 1rem; /* Ensure consistent width for icons */
    text-align: center; /* Center the icon in its space */
    flex-shrink: 0; /* Prevent icon from shrinking */
}

/* Ensure text in dropdown items is properly aligned */
.new-navbar-dropdown-item span {
    flex-grow: 1; /* Allow text to take available space */
    text-align: left; /* Ensure text is left-aligned */
}

.new-navbar-dropdown-item:hover .new-navbar-dropdown-icon {
    transform: translateY(-1px);
    opacity: 1;
}

/* Dropdown divider */
.new-navbar-dropdown-divider {
    height: 1px;
    margin: var(--spacing-sm) var(--spacing-md);
    background: linear-gradient(to right, var(--dropdown-border), transparent);
    width: calc(100% - var(--spacing-md) * 2); /* Ensure proper width */
    text-align: left; /* Ensure left alignment */
}

/* Dropdown header */
.new-navbar-dropdown-header {
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--dropdown-text);
    font-size: 0.7rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.08em;
    opacity: 0.6;
    margin-top: var(--spacing-xs);
    text-align: left; /* Ensure header is left-aligned */
}

/* Dropdown footer */
.new-navbar-dropdown-footer {
    padding: var(--spacing-sm) var(--spacing-md);
    border-top: 1px solid var(--dropdown-border);
    text-align: center;
}

.new-navbar-dropdown-footer a {
    display: block;
    color: var(--accent-primary);
    font-size: 0.875rem;
    text-decoration: none;
    transition: color var(--transition-fast) ease;
}

.new-navbar-dropdown-footer a:hover {
    color: var(--accent-secondary);
}

/* Notification dropdown */
.new-navbar-notification-dropdown {
    position: absolute;
    top: calc(100% + 0.5rem);
    right: 0;
    width: 340px;
    background-color: var(--dropdown-bg);
    border-radius: var(--radius-lg);
    box-shadow: 0 15px 25px -5px var(--dropdown-shadow),
                0 8px 10px -6px var(--dropdown-shadow),
                0 0 0 1px rgba(0, 0, 0, 0.05);
    border: 1px solid var(--dropdown-border);
    padding: 0;
    display: none;
    z-index: 1001;
    transform-origin: top right;
    overflow: hidden;
    backdrop-filter: blur(10px);
    transform: translateY(10px);
    transition: transform var(--transition-fast) ease, opacity var(--transition-fast) ease;
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
}

.new-navbar-notification-dropdown.show,
.new-navbar-notification:hover .new-navbar-notification-dropdown {
    display: block !important;
    transform: translateY(0) !important;
    opacity: 1 !important;
    visibility: visible !important;
    pointer-events: auto !important;
    transition-delay: 0s !important;
}

/* Add a transition delay when hiding the dropdown */
.new-navbar-notification-dropdown {
    transition-delay: 0.2s !important;
}

/* Notification header */
.new-navbar-notification-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--dropdown-border);
    background: linear-gradient(to right, rgba(79, 156, 249, 0.05), transparent);
}

.new-navbar-notification-title {
    font-size: 0.9rem;
    font-weight: 700;
    color: var(--dropdown-text);
    margin: 0;
    letter-spacing: 0.3px;
    position: relative;
    padding-left: 8px;
}

.new-navbar-notification-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 16px;
    background: linear-gradient(to bottom, var(--accent-primary), var(--accent-secondary));
    border-radius: var(--radius-sm);
}

.new-navbar-notification-actions {
    display: flex;
    align-items: center;
}

.new-navbar-notification-action {
    background: none;
    border: none;
    color: var(--dropdown-text);
    opacity: 0.7;
    font-size: 0.75rem;
    cursor: pointer;
    padding: var(--spacing-xs) var(--spacing-sm);
    transition: all var(--transition-fast) ease;
    border-radius: var(--radius-sm);
}

.new-navbar-notification-action:hover {
    opacity: 1;
    background-color: rgba(0, 0, 0, 0.05);
}

/* Notification body */
.new-navbar-notification-body {
    max-height: 350px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

/* Empty notification state */
.new-navbar-notification-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
    text-align: center;
}

.new-navbar-notification-empty i {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--text-muted);
    opacity: 0.7;
    background: linear-gradient(135deg, rgba(58, 123, 213, 0.1), rgba(0, 210, 255, 0.1));
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.new-navbar-notification-empty p {
    margin: 0.25rem 0;
    color: var(--text-dark);
    font-weight: 500;
}

.new-navbar-notification-empty-message {
    font-size: 0.85rem;
    color: var(--text-muted) !important;
    font-weight: normal !important;
    max-width: 200px;
}

.new-navbar-notification-body::-webkit-scrollbar {
    width: 6px;
}

.new-navbar-notification-body::-webkit-scrollbar-track {
    background: transparent;
}

.new-navbar-notification-body::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

/* Notification item */
.new-navbar-notification-item {
    display: flex;
    align-items: flex-start;
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--dropdown-border);
    transition: all var(--transition-fast) ease;
    position: relative;
}

.new-navbar-notification-item:hover {
    background-color: var(--dropdown-hover);
}

.new-navbar-notification-item.unread {
    background-color: rgba(79, 156, 249, 0.08);
}

.new-navbar-notification-item.unread::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 60%;
    background: linear-gradient(to bottom, var(--accent-primary), var(--accent-secondary));
    border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
    opacity: 0.7;
}

/* Notification icon */
.new-navbar-notification-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.25rem;
    height: 2.25rem;
    margin-right: var(--spacing-sm);
    border-radius: var(--radius-full);
    background-color: var(--accent-primary);
    color: white;
    font-size: 0.875rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
    position: relative;
    overflow: hidden;
}

.new-navbar-notification-icon::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 40%;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), transparent);
    pointer-events: none;
}

.new-navbar-notification-icon.info {
    background: linear-gradient(135deg, var(--accent-primary), #3178e0);
}

.new-navbar-notification-icon.success {
    background: linear-gradient(135deg, var(--accent-tertiary), #0ca06e);
}

.new-navbar-notification-icon.warning {
    background: linear-gradient(135deg, var(--accent-quaternary), #e08c0b);
}

.new-navbar-notification-icon.danger {
    background: linear-gradient(135deg, var(--notification-bg), #d03333);
}

/* Notification content */
.new-navbar-notification-content {
    flex: 1;
}

.new-navbar-notification-message {
    font-size: 0.875rem;
    color: var(--dropdown-text);
    margin: 0 0 var(--spacing-xs);
    line-height: 1.4;
}

.new-navbar-notification-meta {
    display: flex;
    align-items: center;
    font-size: 0.7rem;
    color: var(--dropdown-text);
    opacity: 0.7;
    letter-spacing: 0.2px;
}

.new-navbar-notification-time {
    margin-right: var(--spacing-sm);
    display: flex;
    align-items: center;
}

.new-navbar-notification-time i {
    margin-right: 3px;
    font-size: 0.65rem;
}

/* Notification actions */
.new-navbar-notification-item-actions {
    margin-left: var(--spacing-sm);
    opacity: 0;
    transition: opacity var(--transition-fast) ease;
    display: flex;
    align-items: center;
}

.new-navbar-notification-item:hover .new-navbar-notification-item-actions {
    opacity: 1;
}

.new-navbar-notification-item-action {
    background: none;
    border: none;
    color: var(--dropdown-text);
    opacity: 0.7;
    font-size: 0.75rem;
    cursor: pointer;
    padding: var(--spacing-xs);
    transition: all var(--transition-fast) ease;
    border-radius: var(--radius-sm);
    margin-left: 2px;
}

.new-navbar-notification-item-action:hover {
    opacity: 1;
    background-color: rgba(0, 0, 0, 0.05);
}

/* Notification footer */
.new-navbar-notification-footer {
    padding: var(--spacing-md);
    border-top: 1px solid var(--dropdown-border);
    text-align: center;
    background: linear-gradient(to right, rgba(79, 156, 249, 0.05), rgba(79, 156, 249, 0.02));
    position: sticky;
    bottom: 0;
    z-index: 5;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.new-navbar-notification-footer-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
}

.mark-all-read-form {
    flex: 1;
}

.mark-all-read-btn {
    display: inline-block;
    background: linear-gradient(to right, var(--accent-primary), var(--accent-secondary));
    color: white;
    font-size: 0.8rem;
    font-weight: 600;
    padding: 0.6rem 0.5rem;
    width: 100%;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast) ease;
    box-shadow: 0 2px 5px rgba(79, 156, 249, 0.3);
    letter-spacing: 0.3px;
    text-align: center;
    position: relative;
    z-index: 10;
}

.mark-all-read-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(79, 156, 249, 0.4);
}

.mark-all-read-btn:active {
    transform: translateY(1px);
    box-shadow: 0 1px 3px rgba(79, 156, 249, 0.3);
}

.view-all-btn {
    display: inline-block;
    background: transparent;
    color: var(--accent-primary);
    font-size: 0.8rem;
    font-weight: 600;
    padding: 0.6rem 0.5rem;
    border: 1px solid var(--accent-primary);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast) ease;
    text-decoration: none;
    flex: 1;
    text-align: center;
    position: relative;
    z-index: 10;
}

.view-all-btn:hover {
    background-color: rgba(79, 156, 249, 0.1);
    color: var(--accent-secondary);
    transform: translateY(-1px);
}

.view-all-btn:active {
    transform: translateY(1px);
    background-color: rgba(79, 156, 249, 0.2);
}

/* Mobile responsive styles */
@media (max-width: 991.98px) {
    .new-navbar-dropdown {
        position: static;
        width: 100%;
        box-shadow: none;
        border: none;
        border-radius: 0;
        background-color: rgba(255, 255, 255, 0.05);
        margin: var(--spacing-xs) 0;
        padding: 0;
        text-align: left; /* Ensure left alignment on mobile */
    }

    .new-navbar-dropdown-item {
        padding: var(--spacing-sm) var(--spacing-lg);
        color: var(--text-secondary);
        justify-content: flex-start; /* Ensure left alignment on mobile too */
        text-align: left;
    }

    .new-navbar-dropdown-item:hover {
        background-color: rgba(255, 255, 255, 0.1);
        color: var(--text-hover);
    }

    /* Ensure consistent icon spacing on mobile */
    .new-navbar-dropdown-icon {
        margin-right: var(--spacing-md);
    }

    .new-navbar-dropdown-divider {
        background-color: rgba(255, 255, 255, 0.1);
    }

    .new-navbar-dropdown-header {
        color: var(--text-secondary);
        text-align: left; /* Ensure left alignment on mobile */
        padding-left: var(--spacing-lg); /* Consistent padding with items */
    }

    .new-navbar-notification-dropdown {
        position: fixed;
        top: var(--nav-height);
        left: 0;
        right: 0;
        width: 100%;
        border-radius: 0;
        max-height: calc(100vh - var(--nav-height));
        overflow-y: auto;
    }
}
