{% extends 'base.html' %}
{% load static %}

{% block title %}Enterprise | Ads Dashboard{% endblock %}

{% block extra_css %}
<!-- Include enterprise common CSS -->
{% include 'ads/includes/enterprise_common_css.html' %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
<link rel="stylesheet" href="{% static 'ads/css/dashboard_enterprise.css' %}">
<link rel="stylesheet" href="{% static 'ads/css/footer-fix.css' %}">
<link rel="stylesheet" href="{% static 'css/dashboard-welcome-fix.css' %}">

<style>
    /* Ultra-Premium Enterprise Dashboard Styling */
    body {
        background:
            linear-gradient(135deg, #000000 0%, #0a0a0a 10%, #1a1a2e 25%, #16213e 40%, #0f3460 60%, #533483 80%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
        position: relative;
        overflow-x: hidden;
    }

    /* Premium animated background with enterprise dashboard patterns */
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 10% 90%, rgba(102, 126, 234, 0.7) 0%, transparent 40%),
            radial-gradient(circle at 90% 10%, rgba(255, 255, 255, 0.3) 0%, transparent 35%),
            radial-gradient(circle at 30% 70%, rgba(118, 75, 162, 0.6) 0%, transparent 45%),
            radial-gradient(circle at 70% 30%, rgba(83, 52, 131, 0.5) 0%, transparent 30%),
            radial-gradient(circle at 50% 50%, rgba(102, 126, 234, 0.4) 0%, transparent 40%);
        z-index: -1;
        animation: enterpriseDashboardFloat 45s ease-in-out infinite;
    }

    /* Corporate enterprise pattern overlay */
    body::after {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image:
            radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.08) 2px, transparent 2px),
            radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.06) 1px, transparent 1px),
            linear-gradient(45deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px),
            linear-gradient(-45deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
        background-size: 140px 140px, 80px 80px, 60px 60px, 60px 60px;
        z-index: -1;
        opacity: 0.8;
        animation: enterprisePatternPulse 30s ease-in-out infinite;
    }

    @keyframes enterpriseDashboardFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
        16% { transform: translateY(-35px) rotate(2.5deg) scale(1.06); }
        33% { transform: translateY(25px) rotate(-1.5deg) scale(0.94); }
        50% { transform: translateY(-30px) rotate(2deg) scale(1.04); }
        66% { transform: translateY(20px) rotate(-1deg) scale(0.97); }
        83% { transform: translateY(-20px) rotate(1.5deg) scale(1.02); }
    }

    @keyframes enterprisePatternPulse {
        0%, 100% { opacity: 0.6; transform: scale(1) rotate(0deg); }
        50% { opacity: 1; transform: scale(1.04) rotate(4deg); }
    }

    /* Ultra-Premium Enterprise Dashboard Container */
    .enterprise-dashboard {
        position: relative;
        z-index: 1;
        min-height: 100vh;
    }

    /* Ultra-Premium Dashboard Header - Enhanced Visibility */
    .dashboard-header {
        background: linear-gradient(135deg,
            rgba(0, 0, 0, 0.8) 0%,
            rgba(26, 26, 46, 0.9) 25%,
            rgba(22, 33, 62, 0.85) 50%,
            rgba(15, 52, 96, 0.9) 75%,
            rgba(83, 52, 131, 0.8) 100%);
        backdrop-filter: blur(40px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 0 0 32px 32px;
        padding: 3rem 0;
        position: relative;
        overflow: hidden;
        margin-bottom: 2rem;
        box-shadow:
            0 20px 40px rgba(0, 0, 0, 0.3),
            0 10px 20px rgba(0, 0, 0, 0.2),
            inset 0 2px 0 rgba(255, 255, 255, 0.2);
    }

    .dashboard-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: conic-gradient(from 0deg,
            rgba(102, 126, 234, 0.4) 0deg,
            rgba(255, 255, 255, 0.2) 90deg,
            rgba(118, 75, 162, 0.4) 180deg,
            rgba(255, 255, 255, 0.15) 270deg,
            rgba(102, 126, 234, 0.4) 360deg);
        z-index: 0;
        animation: dashboardHeaderGlow 15s linear infinite;
        opacity: 0.8;
    }

    /* Premium header pattern overlay */
    .dashboard-header::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image:
            radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.15) 2px, transparent 2px),
            radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
            linear-gradient(45deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
        background-size: 100px 100px, 60px 60px, 40px 40px;
        z-index: 1;
        opacity: 0.6;
        animation: headerPatternFloat 25s ease-in-out infinite;
    }

    @keyframes dashboardHeaderGlow {
        0% { transform: rotate(0deg) scale(1); }
        50% { transform: rotate(180deg) scale(1.05); }
        100% { transform: rotate(360deg) scale(1); }
    }

    @keyframes headerPatternFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        33% { transform: translateY(-10px) rotate(2deg); }
        66% { transform: translateY(5px) rotate(-1deg); }
    }

    .dashboard-header > * {
        position: relative;
        z-index: 2;
    }

    /* Ultra-Premium Welcome Section - Enhanced Visibility */
    .dashboard-welcome {
        margin-bottom: 1.5rem;
        text-align: center;
        padding: 1rem 0;
    }

    .welcome-title {
        font-size: 3.5rem;
        font-weight: 900;
        color: white;
        background: linear-gradient(135deg,
            #ffffff 0%,
            #f0f9ff 25%,
            #e0f2fe 50%,
            #f0f9ff 75%,
            #ffffff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow:
            0 0 30px rgba(255, 255, 255, 0.8),
            0 0 60px rgba(102, 126, 234, 0.6),
            0 8px 32px rgba(0, 0, 0, 0.5);
        animation: welcomeTitleGlow 4s ease-in-out infinite;
        margin-bottom: 1rem;
        letter-spacing: -1px;
        line-height: 1.1;
        filter: drop-shadow(0 4px 20px rgba(255, 255, 255, 0.3));
    }

    @keyframes welcomeTitleGlow {
        0%, 100% {
            text-shadow:
                0 0 30px rgba(255, 255, 255, 0.8),
                0 0 60px rgba(102, 126, 234, 0.6),
                0 8px 32px rgba(0, 0, 0, 0.5);
            transform: scale(1);
        }
        50% {
            text-shadow:
                0 0 40px rgba(255, 255, 255, 1),
                0 0 80px rgba(102, 126, 234, 0.8),
                0 0 120px rgba(118, 75, 162, 0.6),
                0 8px 32px rgba(0, 0, 0, 0.5);
            transform: scale(1.02);
        }
    }

    .welcome-subtitle {
        font-size: 1.3rem;
        color: rgba(255, 255, 255, 0.95);
        font-weight: 600;
        text-shadow:
            0 2px 8px rgba(0, 0, 0, 0.5),
            0 0 20px rgba(255, 255, 255, 0.3);
        margin: 0;
        letter-spacing: 0.5px;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.95) 0%,
            rgba(240, 249, 255, 0.9) 50%,
            rgba(255, 255, 255, 0.95) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        animation: subtitleGlow 6s ease-in-out infinite;
    }

    @keyframes subtitleGlow {
        0%, 100% {
            text-shadow:
                0 2px 8px rgba(0, 0, 0, 0.5),
                0 0 20px rgba(255, 255, 255, 0.3);
        }
        50% {
            text-shadow:
                0 2px 8px rgba(0, 0, 0, 0.5),
                0 0 30px rgba(255, 255, 255, 0.5),
                0 0 50px rgba(102, 126, 234, 0.4);
        }
    }

    /* Ultra-Premium Dashboard Actions - Enhanced Visibility */
    .dashboard-actions {
        display: flex;
        align-items: center;
        gap: 1.5rem;
        justify-content: center;
        margin-top: 2rem;
        padding: 1.5rem;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.1) 0%,
            rgba(255, 255, 255, 0.05) 100%);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 24px;
        backdrop-filter: blur(20px);
        box-shadow:
            0 8px 20px rgba(0, 0, 0, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }

    .action-item {
        position: relative;
    }

    .btn-icon {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.9) 0%,
            rgba(248, 249, 250, 0.8) 100%);
        border: 2px solid rgba(102, 126, 234, 0.3);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #667eea;
        font-size: 1.2rem;
        transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        box-shadow:
            0 8px 20px rgba(102, 126, 234, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.5);
        backdrop-filter: blur(20px);
        position: relative;
        overflow: hidden;
    }

    .btn-icon::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: conic-gradient(from 0deg,
            rgba(102, 126, 234, 0.3) 0deg,
            rgba(255, 255, 255, 0.2) 90deg,
            rgba(118, 75, 162, 0.3) 180deg,
            rgba(255, 255, 255, 0.1) 270deg,
            rgba(102, 126, 234, 0.3) 360deg);
        border-radius: 50%;
        z-index: -1;
        opacity: 0;
        transition: opacity 0.4s ease;
        animation: btnIconGlow 6s linear infinite;
    }

    .btn-icon:hover {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-color: rgba(255, 255, 255, 0.4);
        transform: translateY(-5px) scale(1.1);
        box-shadow:
            0 15px 35px rgba(102, 126, 234, 0.4),
            inset 0 2px 0 rgba(255, 255, 255, 0.3);
    }

    .btn-icon:hover::before {
        opacity: 1;
    }

    @keyframes btnIconGlow {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .notification-badge {
        position: absolute;
        top: -5px;
        right: -5px;
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        color: white;
        font-size: 0.7rem;
        font-weight: 700;
        padding: 0.2rem 0.5rem;
        border-radius: 10px;
        min-width: 20px;
        text-align: center;
        box-shadow:
            0 4px 12px rgba(239, 68, 68, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        animation: notificationPulse 2s ease-in-out infinite;
    }

    @keyframes notificationPulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.1); }
    }

    .create-ad-btn {
        padding: 1.5rem 3rem;
        background: linear-gradient(135deg,
            #667eea 0%,
            #764ba2 50%,
            #667eea 100%);
        color: white;
        border: 3px solid rgba(255, 255, 255, 0.4);
        border-radius: 30px;
        font-weight: 800;
        font-size: 1.1rem;
        text-transform: uppercase;
        letter-spacing: 2px;
        transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        box-shadow:
            0 15px 30px rgba(102, 126, 234, 0.4),
            0 8px 16px rgba(0, 0, 0, 0.2),
            inset 0 2px 0 rgba(255, 255, 255, 0.3);
        position: relative;
        overflow: hidden;
        text-decoration: none;
        background-size: 200% 200%;
        animation: createBtnGradient 3s ease-in-out infinite;
        filter: drop-shadow(0 0 20px rgba(102, 126, 234, 0.6));
    }

    @keyframes createBtnGradient {
        0%, 100% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
    }

    .create-ad-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
            transparent 0%,
            rgba(255, 255, 255, 0.4) 50%,
            transparent 100%);
        transition: left 0.6s ease;
    }

    .create-ad-btn::after {
        content: '✨';
        position: absolute;
        top: 50%;
        right: 1rem;
        transform: translateY(-50%);
        font-size: 1.2rem;
        animation: sparkle 2s ease-in-out infinite;
    }

    @keyframes sparkle {
        0%, 100% { transform: translateY(-50%) scale(1) rotate(0deg); opacity: 0.8; }
        50% { transform: translateY(-50%) scale(1.2) rotate(180deg); opacity: 1; }
    }

    .create-ad-btn:hover::before {
        left: 100%;
    }

    .create-ad-btn:hover {
        background: linear-gradient(135deg,
            #5a67d8 0%,
            #6b46c1 50%,
            #5a67d8 100%);
        transform: translateY(-5px) scale(1.08);
        box-shadow:
            0 25px 50px rgba(102, 126, 234, 0.6),
            0 15px 30px rgba(0, 0, 0, 0.3),
            inset 0 3px 0 rgba(255, 255, 255, 0.4);
        color: white;
        text-decoration: none;
        border-color: rgba(255, 255, 255, 0.6);
        filter: drop-shadow(0 0 30px rgba(102, 126, 234, 0.8));
    }

    /* Ultra-Premium Dashboard Sidebar */
    .dashboard-sidebar {
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.1) 0%,
            rgba(255, 255, 255, 0.05) 100%);
        backdrop-filter: blur(30px);
        border-right: 2px solid rgba(255, 255, 255, 0.2);
        min-height: 100vh;
        position: relative;
        overflow: hidden;
    }

    .dashboard-sidebar::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: conic-gradient(from 0deg,
            rgba(102, 126, 234, 0.1) 0deg,
            rgba(255, 255, 255, 0.05) 90deg,
            rgba(118, 75, 162, 0.1) 180deg,
            rgba(255, 255, 255, 0.02) 270deg,
            rgba(102, 126, 234, 0.1) 360deg);
        z-index: 0;
        animation: sidebarGlow 20s linear infinite;
        opacity: 0.6;
    }

    @keyframes sidebarGlow {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .sidebar-container {
        position: relative;
        z-index: 1;
        padding: 2rem 0;
    }

    .sidebar-header {
        padding: 0 1.5rem 2rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        margin-bottom: 2rem;
    }

    .sidebar-title {
        font-size: 1.5rem;
        font-weight: 800;
        color: white;
        background: linear-gradient(135deg, #ffffff 0%, #e0e0e0 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin: 0;
        text-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: space-between;
        transition: all 0.3s ease;
        user-select: none;
    }

    .sidebar-toggle-icon {
        font-size: 1rem;
        transition: transform 0.3s ease;
        display: none; /* Hidden on desktop */
    }

    .sidebar-title:hover {
        text-shadow: 0 4px 15px rgba(102, 126, 234, 0.5);
    }

    /* Premium Navigation */
    .nav-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .nav-item {
        margin-bottom: 0.5rem;
    }

    .nav-link {
        display: flex;
        align-items: center;
        padding: 1rem 1.5rem;
        color: rgba(255, 255, 255, 0.8);
        text-decoration: none;
        border-radius: 0 25px 25px 0;
        margin-right: 1rem;
        transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        position: relative;
        overflow: hidden;
        font-weight: 600;
    }

    .nav-link::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
            transparent 0%,
            rgba(102, 126, 234, 0.2) 50%,
            transparent 100%);
        transition: left 0.6s ease;
    }

    .nav-link:hover::before,
    .nav-item.active .nav-link::before {
        left: 100%;
    }

    .nav-link i {
        font-size: 1.1rem;
        margin-right: 1rem;
        width: 20px;
        text-align: center;
    }

    .nav-link:hover,
    .nav-item.active .nav-link {
        background: linear-gradient(135deg,
            rgba(102, 126, 234, 0.2) 0%,
            rgba(255, 255, 255, 0.1) 100%);
        color: white;
        transform: translateX(8px);
        box-shadow:
            0 8px 20px rgba(102, 126, 234, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .nav-divider {
        margin: 1.5rem 0 1rem;
        padding: 0 1.5rem;
    }

    .divider-label {
        font-size: 0.8rem;
        font-weight: 700;
        color: rgba(255, 255, 255, 0.6);
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    /* Premium Ad Types Sidebar */
    .sidebar-ad-types {
        padding: 0 1rem;
        margin-top: 1rem;
    }

    .sidebar-ad-type {
        display: flex;
        align-items: center;
        padding: 1rem;
        margin-bottom: 0.8rem;
        background: linear-gradient(135deg,
            rgba(102, 126, 234, 0.08) 0%,
            rgba(255, 255, 255, 0.05) 100%);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(20px);
    }

    .sidebar-ad-type::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
            transparent 0%,
            rgba(102, 126, 234, 0.15) 50%,
            transparent 100%);
        transition: left 0.6s ease;
    }

    .sidebar-ad-type:hover::before {
        left: 100%;
    }

    .sidebar-ad-type:hover {
        background: linear-gradient(135deg,
            rgba(102, 126, 234, 0.15) 0%,
            rgba(255, 255, 255, 0.1) 100%);
        transform: translateY(-3px) translateX(5px);
        box-shadow:
            0 8px 20px rgba(102, 126, 234, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.3);
    }

    .sidebar-ad-type > * {
        position: relative;
        z-index: 1;
    }

    .sidebar-ad-type i {
        font-size: 1.2rem;
        margin-right: 1rem;
        color: #667eea;
        width: 30px;
        height: 30px;
        background: linear-gradient(135deg,
            rgba(102, 126, 234, 0.2) 0%,
            rgba(255, 255, 255, 0.1) 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow:
            0 4px 12px rgba(102, 126, 234, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        flex-shrink: 0;
    }

    .ad-type-info {
        display: flex;
        flex-direction: column;
        flex: 1;
    }

    .ad-type-name {
        font-size: 0.9rem;
        font-weight: 700;
        color: white;
        margin-bottom: 0.2rem;
        text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    }

    .ad-type-price {
        font-size: 0.8rem;
        color: rgba(255, 255, 255, 0.7);
        font-weight: 600;
    }

    /* Ultra-Premium Dashboard Content */
    .dashboard-content {
        padding: 2rem 0;
        position: relative;
        z-index: 1;
    }

    .dashboard-main {
        padding-left: 2rem;
    }

    /* Premium Dashboard Section */
    .dashboard-section {
        margin-bottom: 3rem;
    }

    /* Ultra-Premium Stats Cards */
    .stats-row {
        margin-bottom: 3rem;
    }

    .stat-card {
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.95) 0%,
            rgba(248, 249, 250, 0.9) 50%,
            rgba(255, 255, 255, 0.95) 100%);
        border-radius: 24px;
        padding: 2rem;
        box-shadow:
            0 20px 40px rgba(0, 0, 0, 0.15),
            0 10px 20px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        border: 2px solid rgba(255, 255, 255, 0.25);
        backdrop-filter: blur(30px);
        transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        position: relative;
        overflow: hidden;
        text-align: center;
        margin-bottom: 1.5rem;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        width: calc(100% + 4px);
        height: calc(100% + 4px);
        background: conic-gradient(from 0deg,
            rgba(102, 126, 234, 0.3) 0deg,
            rgba(255, 255, 255, 0.2) 90deg,
            rgba(118, 75, 162, 0.3) 180deg,
            rgba(255, 255, 255, 0.1) 270deg,
            rgba(102, 126, 234, 0.3) 360deg);
        border-radius: 26px;
        z-index: -1;
        opacity: 0;
        transition: opacity 0.5s ease;
        animation: statCardBorderGlow 8s linear infinite;
    }

    .stat-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow:
            0 30px 60px rgba(0, 0, 0, 0.2),
            0 15px 30px rgba(0, 0, 0, 0.15),
            0 0 50px rgba(102, 126, 234, 0.2),
            inset 0 2px 0 rgba(255, 255, 255, 0.4);
        border-color: rgba(255, 255, 255, 0.4);
    }

    .stat-card:hover::before {
        opacity: 1;
    }

    @keyframes statCardBorderGlow {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .stat-card > * {
        position: relative;
        z-index: 1;
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        font-size: 1.5rem;
        color: white;
        box-shadow:
            0 12px 24px rgba(102, 126, 234, 0.3),
            inset 0 2px 0 rgba(255, 255, 255, 0.3);
        animation: statIconFloat 4s ease-in-out infinite;
    }

    @keyframes statIconFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-6px) rotate(5deg); }
    }

    .stat-value {
        font-size: 2.5rem;
        font-weight: 900;
        color: #1a1a2e;
        background: linear-gradient(135deg, #1a1a2e 0%, #2c3e50 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 0.5rem;
        animation: statValuePulse 3s ease-in-out infinite;
    }

    @keyframes statValuePulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.03); }
    }

    .stat-label {
        font-size: 0.9rem;
        color: #6b7280;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 1px;
        margin-bottom: 0.5rem;
    }

    .stat-trend {
        font-size: 0.8rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.3rem;
    }

    .stat-trend i {
        font-size: 0.7rem;
    }

    /* Type-specific stat card colors */
    .stat-card.active .stat-icon {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    }

    .stat-card.pending .stat-icon {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    }

    .stat-card.impressions .stat-icon {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    }

    .stat-card.clicks .stat-icon {
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    }

    /* Ultra-Premium Dashboard Cards */
    .dashboard-card {
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.95) 0%,
            rgba(248, 249, 250, 0.9) 50%,
            rgba(255, 255, 255, 0.95) 100%);
        border-radius: 24px;
        box-shadow:
            0 20px 40px rgba(0, 0, 0, 0.15),
            0 10px 20px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        border: 2px solid rgba(255, 255, 255, 0.25);
        backdrop-filter: blur(30px);
        margin-bottom: 2rem;
        overflow: hidden;
        position: relative;
        transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .dashboard-card::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        width: calc(100% + 4px);
        height: calc(100% + 4px);
        background: conic-gradient(from 0deg,
            rgba(102, 126, 234, 0.2) 0deg,
            rgba(255, 255, 255, 0.1) 90deg,
            rgba(118, 75, 162, 0.2) 180deg,
            rgba(255, 255, 255, 0.05) 270deg,
            rgba(102, 126, 234, 0.2) 360deg);
        border-radius: 26px;
        z-index: -1;
        opacity: 0;
        transition: opacity 0.5s ease;
        animation: dashboardCardBorderGlow 10s linear infinite;
    }

    .dashboard-card:hover {
        transform: translateY(-5px) scale(1.01);
        box-shadow:
            0 30px 60px rgba(0, 0, 0, 0.2),
            0 15px 30px rgba(0, 0, 0, 0.15),
            0 0 50px rgba(102, 126, 234, 0.15),
            inset 0 2px 0 rgba(255, 255, 255, 0.4);
        border-color: rgba(255, 255, 255, 0.4);
    }

    .dashboard-card:hover::before {
        opacity: 1;
    }

    @keyframes dashboardCardBorderGlow {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .dashboard-card > * {
        position: relative;
        z-index: 1;
    }

    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 2rem 2rem 1rem;
        border-bottom: 2px solid rgba(102, 126, 234, 0.1);
        margin-bottom: 1.5rem;
        background: linear-gradient(135deg,
            rgba(102, 126, 234, 0.05) 0%,
            rgba(255, 255, 255, 0.1) 100%);
    }

    .card-title {
        font-size: 1.4rem;
        font-weight: 800;
        color: #1a1a2e;
        background: linear-gradient(135deg, #1a1a2e 0%, #2c3e50 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin: 0;
    }

    .card-actions .btn {
        padding: 0.6rem 1.2rem;
        border-radius: 16px;
        font-weight: 700;
        font-size: 0.8rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
    }

    .btn-outline-primary {
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.9) 0%,
            rgba(248, 249, 250, 0.8) 100%);
        color: #667eea;
        border: 2px solid rgba(102, 126, 234, 0.3);
        box-shadow:
            0 4px 12px rgba(102, 126, 234, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.5);
    }

    .btn-outline-primary:hover {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-color: rgba(255, 255, 255, 0.4);
        transform: translateY(-2px) scale(1.05);
        box-shadow:
            0 8px 20px rgba(102, 126, 234, 0.4),
            inset 0 2px 0 rgba(255, 255, 255, 0.3);
    }

    /* Premium Recent Ads List */
    .recent-ads-container {
        padding: 0 2rem 2rem;
    }

    .recent-ads-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .recent-ad-item {
        display: flex;
        align-items: center;
        padding: 1.5rem;
        margin-bottom: 1rem;
        background: linear-gradient(135deg,
            rgba(102, 126, 234, 0.05) 0%,
            rgba(255, 255, 255, 0.1) 100%);
        border: 1px solid rgba(102, 126, 234, 0.2);
        border-radius: 16px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .recent-ad-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
            transparent 0%,
            rgba(102, 126, 234, 0.1) 50%,
            transparent 100%);
        transition: left 0.6s ease;
    }

    .recent-ad-item:hover {
        background: linear-gradient(135deg,
            rgba(102, 126, 234, 0.1) 0%,
            rgba(255, 255, 255, 0.15) 100%);
        transform: translateX(8px);
        box-shadow:
            0 8px 20px rgba(102, 126, 234, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }

    .recent-ad-item:hover::before {
        left: 100%;
    }

    .recent-ad-item > * {
        position: relative;
        z-index: 1;
    }

    .ad-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        color: white;
        font-size: 1rem;
        box-shadow:
            0 6px 12px rgba(102, 126, 234, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        flex-shrink: 0;
    }

    .ad-info {
        flex: 1;
        margin-right: 1rem;
    }

    .ad-title {
        font-size: 1rem;
        font-weight: 700;
        color: #1a1a2e;
        margin-bottom: 0.3rem;
    }

    .ad-meta {
        display: flex;
        gap: 1rem;
        align-items: center;
    }

    .ad-date {
        color: #6b7280;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .ad-status {
        padding: 0.2rem 0.6rem;
        border-radius: 10px;
        font-size: 0.7rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-active {
        background: linear-gradient(135deg,
            rgba(16, 185, 129, 0.2) 0%,
            rgba(5, 150, 105, 0.2) 100%);
        color: #10b981;
        border: 1px solid rgba(16, 185, 129, 0.3);
    }

    .status-pending {
        background: linear-gradient(135deg,
            rgba(245, 158, 11, 0.2) 0%,
            rgba(217, 119, 6, 0.2) 100%);
        color: #f59e0b;
        border: 1px solid rgba(245, 158, 11, 0.3);
    }

    .status-draft {
        background: linear-gradient(135deg,
            rgba(107, 114, 128, 0.2) 0%,
            rgba(75, 85, 99, 0.2) 100%);
        color: #6b7280;
        border: 1px solid rgba(107, 114, 128, 0.3);
    }

    .ad-metrics {
        display: flex;
        gap: 1rem;
        margin-right: 1rem;
    }

    .metric {
        text-align: center;
    }

    .metric-value {
        font-size: 1.1rem;
        font-weight: 800;
        color: #1a1a2e;
        display: block;
    }

    .metric-label {
        font-size: 0.7rem;
        color: #6b7280;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .ad-actions .btn {
        padding: 0.5rem;
        border-radius: 10px;
        width: 35px;
        height: 35px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Premium Performance Metrics */
    .performance-metrics {
        padding: 0 2rem 1rem;
    }

    .metric-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .metric-card {
        background: linear-gradient(135deg,
            rgba(102, 126, 234, 0.05) 0%,
            rgba(255, 255, 255, 0.1) 100%);
        border: 1px solid rgba(102, 126, 234, 0.2);
        border-radius: 16px;
        padding: 1.5rem;
        text-align: center;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .metric-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
            transparent 0%,
            rgba(102, 126, 234, 0.1) 50%,
            transparent 100%);
        transition: left 0.6s ease;
    }

    .metric-card:hover::before {
        left: 100%;
    }

    .metric-card:hover {
        background: linear-gradient(135deg,
            rgba(102, 126, 234, 0.1) 0%,
            rgba(255, 255, 255, 0.15) 100%);
        transform: translateY(-3px);
        box-shadow:
            0 8px 20px rgba(102, 126, 234, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }

    .metric-card > * {
        position: relative;
        z-index: 1;
    }

    .metric-card .metric-value {
        font-size: 1.8rem;
        font-weight: 900;
        color: #1a1a2e;
        margin-bottom: 0.5rem;
    }

    .metric-card .metric-label {
        font-size: 0.8rem;
        color: #6b7280;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 0.5rem;
    }

    .metric-trend {
        font-size: 0.7rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.3rem;
    }

    /* Empty State */
    .empty-state {
        text-align: center;
        padding: 3rem 2rem;
        background: linear-gradient(135deg,
            rgba(102, 126, 234, 0.05) 0%,
            rgba(255, 255, 255, 0.1) 100%);
        border: 1px solid rgba(102, 126, 234, 0.2);
        border-radius: 20px;
        margin: 0 2rem 2rem;
    }

    .empty-state-icon {
        font-size: 2.5rem;
        color: rgba(102, 126, 234, 0.6);
        margin-bottom: 1.5rem;
        filter: drop-shadow(0 4px 15px rgba(102, 126, 234, 0.3));
    }

    .empty-state-title {
        font-size: 1.3rem;
        font-weight: 800;
        color: #1a1a2e;
        margin-bottom: 1rem;
    }

    .empty-state-description {
        color: #6b7280;
        margin-bottom: 2rem;
        font-weight: 500;
    }

    /* Premium Responsive Design */
    @media (max-width: 1199px) {
        .dashboard-main {
            padding-left: 1rem;
        }

        .dashboard-sidebar {
            margin-bottom: 2rem;
        }
    }

    @media (max-width: 991px) {
        .welcome-title {
            font-size: 2rem;
        }

        .dashboard-actions {
            justify-content: center;
            margin-top: 1rem;
        }

        .stats-row .col-md-3 {
            margin-bottom: 1rem;
        }

        .dashboard-main {
            padding-left: 0;
        }
    }

    @media (max-width: 768px) {
        .welcome-title {
            font-size: 1.8rem;
        }

        .dashboard-header {
            padding: 1.5rem 0;
        }

        /* Fix mobile layout spacing */
        .dashboard-sidebar {
            margin-bottom: 0.5rem; /* Minimal space between sidebar and content */
            padding-bottom: 0; /* Remove bottom padding */
        }

        .dashboard-main {
            margin-top: 0; /* Remove any top margin */
            padding-top: 0; /* Remove any top padding */
        }

        .dashboard-content {
            padding-top: 0; /* Remove top padding from main container */
        }

        .sidebar-container {
            margin-bottom: 0; /* Remove bottom margin from sidebar container */
        }

        .row {
            margin-bottom: 0; /* Reduce row spacing on mobile */
        }

        /* Ensure sidebar content doesn't add extra space when collapsed */
        .sidebar-content:not(.expanded) {
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .btn-icon {
            width: 45px;
            height: 45px;
            font-size: 1rem;
        }

        .create-ad-btn {
            padding: 0.8rem 1.5rem;
            font-size: 0.8rem;
        }

        .stat-card {
            padding: 1.5rem;
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            font-size: 1.2rem;
        }

        .stat-value {
            font-size: 2rem;
        }

        .card-header {
            padding: 1.5rem 1.5rem 1rem;
        }

        .recent-ads-container,
        .performance-metrics {
            padding: 0 1.5rem 1.5rem;
        }

        .recent-ad-item {
            padding: 1rem;
        }

        .ad-metrics {
            display: none;
        }

        .metric-row {
            grid-template-columns: 1fr;
        }

        /* Mobile Collapsible Sidebar */
        .sidebar-toggle-icon {
            display: block !important;
        }

        /* Main collapsible container for all sidebar content */
        .sidebar-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .sidebar-content.expanded {
            max-height: 5000px !important; /* Force large height when expanded */
            overflow: visible !important; /* Ensure no clipping when expanded */
        }

        /* Arrow icon changes dynamically via JavaScript - no rotation needed */

        .nav-list {
            opacity: 1; /* Always visible - remove initial hiding */
            transform: translateY(0);
            transition: all 0.3s ease 0.1s;
        }

        .nav-item {
            transform: translateX(0); /* Always visible - remove initial hiding */
            opacity: 1;
            transition: all 0.3s ease;
        }

        /* Add subtle animation when expanding (optional enhancement) */
        .sidebar-content:not(.expanded) .nav-item {
            transform: translateX(-5px);
        }

        .sidebar-content.expanded .nav-item {
            transform: translateX(0);
        }

        .sidebar-content.expanded .nav-item:nth-child(1) { transition-delay: 0.1s; }
        .sidebar-content.expanded .nav-item:nth-child(2) { transition-delay: 0.15s; }
        .sidebar-content.expanded .nav-item:nth-child(3) { transition-delay: 0.2s; }
        .sidebar-content.expanded .nav-item:nth-child(4) { transition-delay: 0.25s; }
        .sidebar-content.expanded .nav-item:nth-child(5) { transition-delay: 0.3s; }
        .sidebar-content.expanded .nav-item:nth-child(6) { transition-delay: 0.35s; }
        .sidebar-content.expanded .nav-item:nth-child(7) { transition-delay: 0.4s; }
        .sidebar-content.expanded .nav-item:nth-child(8) { transition-delay: 0.45s; }
        .sidebar-content.expanded .nav-item:nth-child(9) { transition-delay: 0.5s; }
        .sidebar-content.expanded .nav-item:nth-child(10) { transition-delay: 0.55s; }
        .sidebar-content.expanded .nav-item:nth-child(11) { transition-delay: 0.6s; }

        .sidebar-ad-types {
            opacity: 1; /* Always visible - remove initial hiding */
            transform: translateY(0);
            transition: all 0.3s ease 0.2s;
        }

        /* Ad Types Header */
        .ad-types-header {
            opacity: 1; /* Always visible - remove initial hiding */
            transform: translateY(0);
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }

        /* Mobile 2-column layout for ad types */
        .sidebar-ad-types {
            display: grid;
            grid-template-columns: 1fr 1fr; /* 2 columns for ad types */
            gap: 0.5rem;
        }

        .sidebar-ad-types .ad-types-header {
            grid-column: 1 / -1; /* Header spans full width */
        }

        .sidebar-ad-type {
            transform: translateX(0); /* Always visible - remove initial hiding */
            opacity: 1;
            transition: all 0.3s ease;
            padding: 0.5rem;
            font-size: 0.8rem; /* Smaller text for mobile 2-column layout */
        }

        .sidebar-ad-type i {
            font-size: 1rem;
            margin-bottom: 0.25rem;
        }

        .ad-type-name {
            font-size: 0.75rem;
            font-weight: 600;
        }

        .ad-type-price {
            font-size: 0.7rem;
            color: #4ade80;
        }

        .sidebar-content.expanded .sidebar-ad-type:nth-child(2) { transition-delay: 0.7s; }  /* First ad type (after header) */
        .sidebar-content.expanded .sidebar-ad-type:nth-child(3) { transition-delay: 0.75s; }
        .sidebar-content.expanded .sidebar-ad-type:nth-child(4) { transition-delay: 0.8s; }
        .sidebar-content.expanded .sidebar-ad-type:nth-child(5) { transition-delay: 0.85s; }
        .sidebar-content.expanded .sidebar-ad-type:nth-child(6) { transition-delay: 0.9s; }

        .sidebar-content.expanded .sidebar-ad-type {
            transform: translateX(0);
            opacity: 1;
        }

        /* Ensure all nav items are visible with proper spacing */
        .nav-item {
            margin-bottom: 0.25rem;
        }

        /* Container height already set above - removed duplicate rule */
    }

    @media (max-width: 480px) {
        .welcome-title {
            font-size: 1.5rem;
        }

        .dashboard-actions {
            flex-direction: column;
            gap: 0.5rem;
        }

        .stat-card {
            padding: 1rem;
        }

        .stat-icon {
            width: 40px;
            height: 40px;
            font-size: 1rem;
        }

        .stat-value {
            font-size: 1.5rem;
        }

        .recent-ad-item {
            flex-direction: column;
            text-align: center;
            gap: 1rem;
        }

        .ad-info {
            margin-right: 0;
        }

        .nav-link {
            padding: 0.8rem 1rem;
            font-size: 0.9rem;
        }

        .sidebar-ad-type {
            padding: 0.8rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Include dashboard notification -->
{% include 'ads/includes/dashboard_notification.html' %}

<!-- Enterprise Dashboard Container -->
<div class="enterprise-dashboard">
    <!-- Context-aware Header -->
    <div class="dashboard-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="dashboard-welcome">
                        <h1 class="welcome-title">Welcome, {{ user.first_name|default:user.username }}</h1>
                        <p class="welcome-subtitle">Here's an overview of your advertising campaigns</p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="dashboard-actions">
                        <div class="action-item">
                            <button class="btn-icon" title="Notifications">
                                <i class="fas fa-bell"></i>
                                {% if notifications_count > 0 %}
                                <span class="notification-badge">{{ notifications_count }}</span>
                                {% endif %}
                            </button>
                        </div>
                        <div class="action-item">
                            <button class="btn-icon" title="Help">
                                <i class="fas fa-question-circle"></i>
                            </button>
                        </div>
                        <div class="action-item">
                            <a href="{% url 'ads:ad_create_consolidated' %}" class="btn btn-primary create-ad-btn">
                                <i class="fas fa-plus-circle me-2"></i> Create New Ad
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Dashboard Content -->
    <div class="dashboard-content">
        <div class="container-fluid">
            <div class="row">
                <!-- Sidebar Navigation -->
                <div class="col-lg-2 dashboard-sidebar">
                    <div class="sidebar-container">
                        <div class="sidebar-header">
                            <h2 class="sidebar-title" id="sidebar-toggle">
                                Dashboard
                                <i class="fas fa-chevron-down sidebar-toggle-icon"></i>
                            </h2>
                        </div>
                        <div class="sidebar-content" id="sidebar-content">
                            <nav class="sidebar-nav" id="sidebar-nav">
                                <ul class="nav-list" id="nav-list">
                                <li class="nav-item active">
                                    <a href="#overview" class="nav-link" data-tab="overview">
                                        <i class="fas fa-home"></i>
                                        <span>Overview</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="#campaigns" class="nav-link" data-tab="campaigns">
                                        <i class="fas fa-bullhorn"></i>
                                        <span>Campaigns</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{% url 'ads:campaign_list' %}" class="nav-link">
                                        <i class="fas fa-tasks"></i>
                                        <span>Campaign Management</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{% url 'ads:analytics_enterprise' %}" class="nav-link">
                                        <i class="fas fa-chart-bar"></i>
                                        <span>Analytics</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{% url 'ads:analytics_enterprise' %}#performance" class="nav-link">
                                        <i class="fas fa-tachometer-alt"></i>
                                        <span>Performance</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{% url 'ads:analytics_enterprise' %}#comparison" class="nav-link">
                                        <i class="fas fa-balance-scale"></i>
                                        <span>Campaign Comparison</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{% url 'ads:analytics_enterprise' %}#conversions" class="nav-link">
                                        <i class="fas fa-exchange-alt"></i>
                                        <span>Conversions</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="#transactions" class="nav-link" data-tab="transactions">
                                        <i class="fas fa-receipt"></i>
                                        <span>Transactions</span>
                                    </a>
                                </li>
                                {% if user.is_superuser %}
                                <li class="nav-item nav-divider">
                                    <span class="divider-label">Admin</span>
                                </li>
                                <li class="nav-item">
                                    <a href="#admin" class="nav-link" data-tab="admin">
                                        <i class="fas fa-user-shield"></i>
                                        <span>Admin Panel</span>
                                    </a>
                                </li>
                                {% endif %}
                                </ul>
                            </nav>

                            <!-- Ad Types Section (moved outside nav-list for valid HTML) -->
                            <div class="sidebar-ad-types">
                                <div class="ad-types-header">
                                    <span class="divider-label">Ad Types</span>
                                </div>
                                <div class="sidebar-ad-type">
                                    <i class="fas fa-image"></i>
                                    <div class="ad-type-info">
                                        <span class="ad-type-name">Banner Ad</span>
                                        <span class="ad-type-price">500.00 KSH</span>
                                    </div>
                                </div>
                                <div class="sidebar-ad-type">
                                    <i class="fas fa-columns"></i>
                                    <div class="ad-type-info">
                                        <span class="ad-type-name">Sidebar Ad</span>
                                        <span class="ad-type-price">350.00 KSH</span>
                                    </div>
                                </div>
                                <div class="sidebar-ad-type">
                                    <i class="fas fa-star"></i>
                                    <div class="ad-type-info">
                                        <span class="ad-type-name">Featured Ad</span>
                                        <span class="ad-type-price">1000.00 KSH</span>
                                    </div>
                                </div>
                                <div class="sidebar-ad-type">
                                    <i class="fas fa-window-restore"></i>
                                    <div class="ad-type-info">
                                        <span class="ad-type-name">Pop-up Ad</span>
                                        <span class="ad-type-price">750.00 KSH</span>
                                    </div>
                                </div>
                                <div class="sidebar-ad-type">
                                    <i class="fas fa-envelope"></i>
                                    <div class="ad-type-info">
                                        <span class="ad-type-name">Newsletter Ad</span>
                                        <span class="ad-type-price">600.00 KSH</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Main Content Area -->
                <div class="col-lg-10 dashboard-main">
                    <!-- Tab Content -->
                    <div class="tab-content">
                        <!-- Overview Tab -->
                        <div class="tab-pane active" id="overview">
                            <!-- KPI Stats Row -->
                            <div class="dashboard-section">
                                <div class="row stats-row">
                                    <div class="col-md-3 col-sm-6">
                                        <div class="stat-card active">
                                            <div class="stat-icon">
                                                <i class="fas fa-bullhorn"></i>
                                            </div>
                                            <div class="stat-value">{{ active_ads_count }}</div>
                                            <div class="stat-label">Active Ads</div>
                                            <div class="stat-trend">
                                                <i class="fas fa-arrow-up"></i> 12% from last month
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="stat-card pending">
                                            <div class="stat-icon">
                                                <i class="fas fa-clock"></i>
                                            </div>
                                            <div class="stat-value">{{ pending_ads_count }}</div>
                                            <div class="stat-label">Pending Ads</div>
                                            <div class="stat-trend">
                                                <i class="fas fa-minus"></i> No change
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="stat-card impressions">
                                            <div class="stat-icon">
                                                <i class="fas fa-eye"></i>
                                            </div>
                                            <div class="stat-value">{{ total_impressions }}</div>
                                            <div class="stat-label">Impressions</div>
                                            <div class="stat-trend">
                                                <i class="fas fa-arrow-up"></i> 8% from last month
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="stat-card clicks">
                                            <div class="stat-icon">
                                                <i class="fas fa-mouse-pointer"></i>
                                            </div>
                                            <div class="stat-value">{{ total_clicks }}</div>
                                            <div class="stat-label">Clicks</div>
                                            <div class="stat-trend">
                                                <i class="fas fa-arrow-up"></i> 5% from last month
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Recent Activity and Performance -->
                            <div class="dashboard-section">
                                <div class="row">
                                    <!-- Recent Ads -->
                                    <div class="col-lg-7">
                                        <div class="dashboard-card">
                                            <div class="card-header">
                                                <h3 class="card-title">Recent Advertisements</h3>
                                                <div class="card-actions">
                                                    <a href="{% url 'ads:ad_list' %}" class="btn btn-sm btn-outline-primary">View All</a>
                                                </div>
                                            </div>

                                            {% if recent_ads %}
                                                <div class="recent-ads-container">
                                                    <ul class="recent-ads-list">
                                                        {% for ad in recent_ads %}
                                                            <li class="recent-ad-item">
                                                                <div class="ad-icon">
                                                                    <i class="fas fa-ad"></i>
                                                                </div>
                                                                <div class="ad-info">
                                                                    <div class="ad-title">{{ ad.title }}</div>
                                                                    <div class="ad-meta">
                                                                        <span class="ad-date">{{ ad.created_at|date:"M d, Y" }}</span>
                                                                        <span class="ad-status status-{{ ad.status }}">
                                                                            {{ ad.get_status_display }}
                                                                        </span>
                                                                    </div>
                                                                </div>
                                                                <div class="ad-metrics">
                                                                    <div class="metric">
                                                                        <span class="metric-value">{{ ad.impressions|default:"0" }}</span>
                                                                        <span class="metric-label">Views</span>
                                                                    </div>
                                                                    <div class="metric">
                                                                        <span class="metric-value">{{ ad.clicks|default:"0" }}</span>
                                                                        <span class="metric-label">Clicks</span>
                                                                    </div>
                                                                </div>
                                                                <div class="ad-actions">
                                                                    <a href="{% url 'ads:ad_detail' ad.slug %}" class="btn btn-sm btn-outline-primary">
                                                                        <i class="fas fa-eye"></i>
                                                                    </a>
                                                                </div>
                                                            </li>
                                                        {% endfor %}
                                                    </ul>
                                                </div>
                                            {% else %}
                                                <div class="empty-state">
                                                    <div class="empty-state-icon">
                                                        <i class="fas fa-ad"></i>
                                                    </div>
                                                    <h3 class="empty-state-title">No advertisements yet</h3>
                                                    <p class="empty-state-description">
                                                        Create your first ad to get started with your advertising campaigns.
                                                    </p>
                                                    <a href="{% url 'ads:ad_create_consolidated' %}" class="btn btn-primary">Create New Ad</a>
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <!-- Performance Overview -->
                                    <div class="col-lg-5">
                                        <div class="dashboard-card">
                                            <div class="card-header">
                                                <h3 class="card-title">Performance Overview</h3>
                                                <div class="card-actions">
                                                    <div class="time-selector">
                                                        <select class="form-select form-select-sm">
                                                            <option>Last 7 days</option>
                                                            <option>Last 30 days</option>
                                                            <option>Last 90 days</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="performance-metrics">
                                                <div class="metric-row">
                                                    <div class="metric-card">
                                                        <div class="metric-value">{{ total_impressions }}</div>
                                                        <div class="metric-label">Impressions</div>
                                                        <div class="metric-trend">
                                                            <i class="fas fa-{% if impression_trend > 0 %}arrow-up{% elif impression_trend < 0 %}arrow-down{% else %}minus{% endif %}"></i>
                                                            <span>{{ impression_trend|default:0|floatformat:1 }}%</span>
                                                        </div>
                                                    </div>
                                                    <div class="metric-card">
                                                        <div class="metric-value">{{ total_clicks }}</div>
                                                        <div class="metric-label">Clicks</div>
                                                        <div class="metric-trend">
                                                            <i class="fas fa-{% if click_trend > 0 %}arrow-up{% elif click_trend < 0 %}arrow-down{% else %}minus{% endif %}"></i>
                                                            <span>{{ click_trend|default:0|floatformat:1 }}%</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="metric-row">
                                                    <div class="metric-card">
                                                        <div class="metric-value">{{ ctr|floatformat:2 }}%</div>
                                                        <div class="metric-label">CTR</div>
                                                        <div class="metric-trend">
                                                            <i class="fas fa-{% if ctr_trend > 0 %}arrow-up{% elif ctr_trend < 0 %}arrow-down{% else %}minus{% endif %}"></i>
                                                            <span>{{ ctr_trend|default:0|floatformat:1 }}%</span>
                                                        </div>
                                                    </div>
                                                    <div class="metric-card">
                                                        <div class="metric-value">{{ total_conversions|default:0 }}</div>
                                                        <div class="metric-label">Conversions</div>
                                                        <div class="metric-trend">
                                                            <i class="fas fa-{% if conversion_trend > 0 %}arrow-up{% elif conversion_trend < 0 %}arrow-down{% else %}minus{% endif %}"></i>
                                                            <span>{{ conversion_trend|default:0|floatformat:1 }}%</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="text-center mt-3">
                                                <a href="{% url 'ads:analytics_enterprise' %}" class="btn btn-outline-primary">View Enterprise Analytics</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Other tabs will be implemented in the next phase -->

                        {% if user.is_superuser %}
                        <!-- Admin Tab -->
                        <div class="tab-pane" id="admin" data-superuser="true">
                            <div class="dashboard-section">
                                <div class="row">
                                    <div class="col-12">
                                        <div class="dashboard-card">
                                            <div class="card-header">
                                                <h3 class="card-title">Admin Dashboard</h3>
                                                <div class="card-actions">
                                                    <a href="{% url 'admin:index' %}" class="btn btn-sm btn-outline-primary" target="_blank">
                                                        <i class="fas fa-external-link-alt me-1"></i> Django Admin
                                                    </a>
                                                </div>
                                            </div>

                                            <div class="admin-dashboard-content p-4">
                                                <div class="row">
                                                    <!-- Admin Quick Actions -->
                                                    <div class="col-lg-4">
                                                        <div class="admin-section">
                                                            <h4 class="admin-section-title">Quick Actions</h4>
                                                            <div class="admin-action-buttons">
                                                                <a href="{% url 'ads:admin_pending_ads' %}" class="btn btn-primary btn-lg w-100 mb-3">
                                                                    <i class="fas fa-tasks me-2"></i> Pending Ads
                                                                </a>
                                                                <a href="{% url 'ads:admin_pending_payments' %}" class="btn btn-success btn-lg w-100 mb-3">
                                                                    <i class="fas fa-money-bill-wave me-2"></i> Pending Payments
                                                                </a>
                                                                <a href="{% url 'ads:admin_active_ads' %}" class="btn btn-info btn-lg w-100 mb-3">
                                                                    <i class="fas fa-bullhorn me-2"></i> Active Ads
                                                                </a>
                                                                <a href="{% url 'ads:admin_all_ads' %}" class="btn btn-secondary btn-lg w-100 mb-3">
                                                                    <i class="fas fa-list me-2"></i> All Ads
                                                                </a>
                                                                <a href="{% url 'ads:admin_transactions' %}" class="btn btn-warning btn-lg w-100">
                                                                    <i class="fas fa-receipt me-2"></i> Transactions
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- Admin Stats -->
                                                    <div class="col-lg-8">
                                                        <div class="admin-section">
                                                            <h4 class="admin-section-title">System Overview</h4>
                                                            <div class="row">
                                                                <div class="col-md-6 mb-4">
                                                                    <div class="admin-stat-card">
                                                                        <div class="admin-stat-icon">
                                                                            <i class="fas fa-users"></i>
                                                                        </div>
                                                                        <div class="admin-stat-info">
                                                                            <div class="admin-stat-value">{{ user_count }}</div>
                                                                            <div class="admin-stat-label">Total Users</div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-6 mb-4">
                                                                    <div class="admin-stat-card">
                                                                        <div class="admin-stat-icon">
                                                                            <i class="fas fa-ad"></i>
                                                                        </div>
                                                                        <div class="admin-stat-info">
                                                                            <div class="admin-stat-value">{{ all_ads_count }}</div>
                                                                            <div class="admin-stat-label">Total Ads</div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-6 mb-4">
                                                                    <div class="admin-stat-card">
                                                                        <div class="admin-stat-icon">
                                                                            <i class="fas fa-money-bill-wave"></i>
                                                                        </div>
                                                                        <div class="admin-stat-info">
                                                                            <div class="admin-stat-value">${{ total_revenue|floatformat:2 }}</div>
                                                                            <div class="admin-stat-label">Total Revenue</div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-6 mb-4">
                                                                    <div class="admin-stat-card">
                                                                        <div class="admin-stat-icon">
                                                                            <i class="fas fa-tasks"></i>
                                                                        </div>
                                                                        <div class="admin-stat-info">
                                                                            <div class="admin-stat-value">{{ pending_approval_count }}</div>
                                                                            <div class="admin-stat-label">Pending Approvals</div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Recent Activity -->
                                                <div class="admin-section mt-4">
                                                    <h4 class="admin-section-title">Recent Activity</h4>
                                                    <div class="admin-activity-list">
                                                        {% if recent_admin_activities %}
                                                            <div class="table-responsive">
                                                                <table class="table table-hover">
                                                                    <thead>
                                                                        <tr>
                                                                            <th>User</th>
                                                                            <th>Action</th>
                                                                            <th>Item</th>
                                                                            <th>Date</th>
                                                                            <th>Actions</th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>
                                                                        {% for activity in recent_admin_activities %}
                                                                            <tr>
                                                                                <td>{{ activity.user.username }}</td>
                                                                                <td>{{ activity.action }}</td>
                                                                                <td>{{ activity.item }}</td>
                                                                                <td>{{ activity.timestamp|date:"M d, Y H:i" }}</td>
                                                                                <td>
                                                                                    <a href="#" class="btn btn-sm btn-outline-primary">
                                                                                        <i class="fas fa-eye"></i>
                                                                                    </a>
                                                                                </td>
                                                                            </tr>
                                                                        {% endfor %}
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        {% else %}
                                                            <div class="empty-state">
                                                                <div class="empty-state-icon">
                                                                    <i class="fas fa-history"></i>
                                                                </div>
                                                                <h3 class="empty-state-title">No recent activity</h3>
                                                                <p class="empty-state-description">
                                                                    Recent administrative actions will appear here.
                                                                </p>
                                                            </div>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'ads/js/dashboard_enterprise.js' %}"></script>
<!-- Admin Footer Fix JS -->
<script src="{% static 'ads/js/admin-footer-fix.js' %}"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Mobile Sidebar Toggle Functionality
    const sidebarToggle = document.getElementById('sidebar-toggle');
    const sidebarContent = document.getElementById('sidebar-content');
    const toggleIcon = document.querySelector('.sidebar-toggle-icon');

    if (sidebarToggle && sidebarContent && toggleIcon) {
        sidebarToggle.addEventListener('click', function() {
            // Check if we're on mobile (768px or less)
            if (window.innerWidth <= 768) {
                // Toggle the expanded class
                sidebarContent.classList.toggle('expanded');

                // Change arrow icon dynamically
                if (sidebarContent.classList.contains('expanded')) {
                    // Show up arrow when expanded
                    toggleIcon.className = 'fas fa-chevron-up sidebar-toggle-icon';
                } else {
                    // Show down arrow when collapsed
                    toggleIcon.className = 'fas fa-chevron-down sidebar-toggle-icon';
                }

                // Add a subtle haptic feedback effect
                if (navigator.vibrate) {
                    navigator.vibrate(50);
                }
            }
        });

        // Handle window resize to reset sidebar state on desktop
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                // Remove mobile classes when switching to desktop
                sidebarContent.classList.remove('expanded');
                // Reset to down arrow on desktop
                toggleIcon.className = 'fas fa-chevron-down sidebar-toggle-icon';
            }
        });

        // Initialize sidebar state based on screen size
        function initializeSidebar() {
            if (window.innerWidth <= 768) {
                // Start with sidebar expanded on mobile so navigation is visible
                sidebarContent.classList.add('expanded');
                // Set initial up arrow since we start expanded
                toggleIcon.className = 'fas fa-chevron-up sidebar-toggle-icon';
            }
        }

        // Initialize on page load
        initializeSidebar();

        // Add smooth scroll behavior for navigation links
        const navLinks = document.querySelectorAll('.nav-link[data-tab]');
        navLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                // On mobile, collapse sidebar after clicking a link
                if (window.innerWidth <= 768) {
                    setTimeout(() => {
                        sidebarContent.classList.remove('expanded');
                        // Reset to down arrow when auto-collapsing
                        toggleIcon.className = 'fas fa-chevron-down sidebar-toggle-icon';
                    }, 300);
                }
            });
        });
    }
});
</script>

{% endblock %}
