from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from ads.models import Ad, AdType, AdLocation
from django.utils import timezone
from datetime import timedelta
from decimal import Decimal

class Command(BaseCommand):
    help = 'Creates test ads for demonstration purposes'

    def handle(self, *args, **options):
        # Get or create a test user
        try:
            user = User.objects.get(username='peter')
            self.stdout.write(self.style.SUCCESS(f"Using existing user: {user.username}"))
        except User.DoesNotExist:
            self.stdout.write(self.style.ERROR('Superuser "peter" not found. Please create it first.'))
            return

        # Get or create ad types
        banner_type, created = AdType.objects.get_or_create(
            name='Banner',
            defaults={
                'description': 'Standard banner advertisement',
                'base_price': 100.00,
                'is_active': True,
                'is_premium': True
            }
        )

        # Get or create Premium Header location
        premium_header, created = AdLocation.objects.get_or_create(
            name='Premium Header',
            defaults={
                'description': 'Premium header position at the top of all pages',
                'price_multiplier': 2.0,
                'is_active': True,
                'visibility': 'high',
                'is_premium': True,
                'daily_impressions': 5000
            }
        )

        if created:
            self.stdout.write(self.style.SUCCESS(f"Created Premium Header location"))
        else:
            self.stdout.write(self.style.SUCCESS(f"Using existing Premium Header location"))

        # Create multiple test ads for Premium Header
        ad_titles = [
            'Enterprise Cloud Solutions',
            'Premium Business Analytics',
            'Advanced Security Services',
            'Digital Transformation Suite'
        ]

        ad_contents = [
            'Accelerate your business with our enterprise-grade cloud solutions. Scalable, secure, and reliable infrastructure for modern businesses.',
            'Gain actionable insights with our premium analytics platform. Turn data into decisions with real-time dashboards and AI-powered recommendations.',
            'Protect your business with advanced security services. Enterprise-grade protection against modern threats with 24/7 monitoring.',
            'Transform your business with our digital solutions. Streamline operations, enhance customer experience, and drive innovation.'
        ]

        ad_cta_links = [
            'https://example.com/cloud',
            'https://example.com/analytics',
            'https://example.com/security',
            'https://example.com/digital'
        ]

        # Delete existing test ads for Premium Header
        existing_ads = Ad.objects.filter(ad_location=premium_header, title__in=ad_titles)
        if existing_ads.exists():
            count = existing_ads.count()
            existing_ads.delete()
            self.stdout.write(self.style.WARNING(f"Deleted {count} existing Premium Header ads"))

        # Create new test ads
        for i in range(len(ad_titles)):
            ad = Ad.objects.create(
                user=user,
                ad_type=banner_type,
                ad_location=premium_header,
                title=ad_titles[i],
                content=ad_contents[i],
                cta_link=ad_cta_links[i],
                target_location='Kenya',
                target_audience='Business',
                start_date=timezone.now(),
                end_date=timezone.now() + timedelta(days=30),
                status='approved',
                base_pricing=Decimal('200.00'),
                final_pricing=Decimal('200.00') * premium_header.price_multiplier,
            )
            self.stdout.write(self.style.SUCCESS(f"Created Premium Header ad: {ad.title}"))

        self.stdout.write(self.style.SUCCESS(f"Successfully created {len(ad_titles)} test ads for Premium Header"))
