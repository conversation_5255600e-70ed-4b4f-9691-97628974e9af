from django.core.management.base import BaseCommand
from ads.models import AdType, AdLocation
from decimal import Decimal

class Command(BaseCommand):
    help = 'Tests the ad location pricing calculation'

    def handle(self, *args, **kwargs):
        self.stdout.write(self.style.SUCCESS('Testing Ad Location Pricing Calculation'))
        self.stdout.write('-' * 50)
        
        # Get all ad types and locations
        ad_types = AdType.objects.all()
        ad_locations = AdLocation.objects.all()
        
        if not ad_types:
            self.stdout.write(self.style.ERROR('No ad types found. Please create ad types first.'))
            return
            
        if not ad_locations:
            self.stdout.write(self.style.ERROR('No ad locations found. Please create ad locations first.'))
            return
            
        # Print header
        self.stdout.write(f"{'Ad Type':<20} {'Base Price':<15} {'Location':<20} {'Multiplier':<15} {'Final Price':<15}")
        self.stdout.write('-' * 85)
        
        # Test each combination
        for ad_type in ad_types:
            for location in ad_locations:
                base_price = ad_type.base_price
                multiplier = location.price_multiplier
                final_price = base_price * multiplier
                
                self.stdout.write(
                    f"{ad_type.name:<20} "
                    f"{base_price:<15} "
                    f"{location.name:<20} "
                    f"{multiplier:<15} "
                    f"{final_price:<15}"
                )
            
            # Add a separator between ad types
            self.stdout.write('-' * 85)
        
        self.stdout.write(self.style.SUCCESS('Pricing calculation test completed successfully'))
