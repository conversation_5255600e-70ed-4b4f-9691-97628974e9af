/**
 * Production Loader
 * Dynamically loads optimized production scripts and styles
 *
 * @version 1.0.0
 * <AUTHOR> QR Team
 * @license MIT
 */

(function() {
    // Configuration
    const config = {
        // Base paths - Fix for Django static files
        cssBasePath: '/static/css/',
        jsBasePath: '/static/js/',

        // Fallback paths if the above don't work
        fallbackCssBasePath: 'static/css/',
        fallbackJsBasePath: 'static/js/',

        // CSS files to load
        cssFiles: [
            'ad-creation-production.css',
            'modal-production.css'
        ],

        // JS files to load
        jsFiles: [
            'ad-creation-production.js'
        ],

        // Files to disable (prevent conflicts) - cleaned up removed files
        disableFiles: [
            'mobile-navigation.js',
            'mobile-navigation.css',
            'bottom-navigation.js',
            'bottom-navigation.css'
        ],

        // Load timeout in milliseconds
        loadTimeout: 10000
    };

    // Track loaded resources
    const loadedResources = {
        css: [],
        js: []
    };

    // Track disabled resources
    const disabledResources = [];

    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        console.log('%cProduction Loader: Initializing', 'background: #4CAF50; color: white; padding: 2px 5px; border-radius: 3px;');

        // Debug info
        console.log('%cProduction Loader: Config', 'background: #2196F3; color: white; padding: 2px 5px; border-radius: 3px;', JSON.stringify(config));

        // Disable conflicting files first
        disableConflictingFiles();

        // Load CSS files
        loadCSSFiles();

        // Load JS files
        loadJSFiles();

        // Log successful initialization
        console.log('%cProduction Loader: Initialized successfully', 'background: #4CAF50; color: white; padding: 2px 5px; border-radius: 3px;');


    });

    // Also initialize on window load to ensure everything is loaded
    window.addEventListener('load', function() {
        console.log('%cProduction Loader: Window loaded, reinitializing', 'background: #2196F3; color: white; padding: 2px 5px; border-radius: 3px;');

        // Load CSS files
        loadCSSFiles();

        // Load JS files
        loadJSFiles();

        // Log successful initialization
        console.log('%cProduction Loader: Reinitialized successfully', 'background: #2196F3; color: white; padding: 2px 5px; border-radius: 3px;');
    });

    /**
     * Get base URL for static files
     */
    function getBaseURL() {
        // Try to find the correct base URL by checking for common static files
        return '';
    }

    /**
     * Load CSS files
     */
    function loadCSSFiles() {
        config.cssFiles.forEach(file => {
            // Try primary path first
            loadCSS(config.cssBasePath + file, function(success) {
                // If primary path fails, try fallback path
                if (!success) {
                    console.log(`%cProduction Loader: Primary path failed, trying fallback for ${file}`, 'background: #FF9800; color: white; padding: 2px 5px; border-radius: 3px;');
                    loadCSS(config.fallbackCssBasePath + file);
                }
            });
        });
    }

    /**
     * Load JS files
     */
    function loadJSFiles() {
        config.jsFiles.forEach((file, index) => {
            // Try primary path first
            loadJS(config.jsBasePath + file, function(success) {
                // If primary path fails, try fallback path
                if (!success) {
                    console.log(`%cProduction Loader: Primary path failed, trying fallback for ${file}`, 'background: #FF9800; color: white; padding: 2px 5px; border-radius: 3px;');
                    loadJS(config.fallbackJsBasePath + file);
                }
            });


        });
    }

    /**
     * Load CSS file
     * @param {string} url - URL of the CSS file to load
     * @param {function} callback - Optional callback function(success)
     */
    function loadCSS(url, callback) {
        // Check if already loaded
        if (loadedResources.css.includes(url)) {
            console.log(`%cProduction Loader: CSS already loaded - ${url}`, 'color: #9E9E9E;');
            if (callback) callback(true);
            return;
        }

        // Check if file exists
        fetch(url)
            .then(response => {
                if (!response.ok) {
                    console.error(`%cProduction Loader: CSS file not found - ${url} (${response.status})`, 'background: #F44336; color: white; padding: 2px 5px; border-radius: 3px;');
                    if (callback) callback(false);
                    return;
                }

                // Create link element
                const link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = url;

                // Add load event listener
                link.addEventListener('load', () => {
                    console.log(`%cProduction Loader: CSS loaded - ${url}`, 'background: #4CAF50; color: white; padding: 2px 5px; border-radius: 3px;');
                    loadedResources.css.push(url);
                    if (callback) callback(true);
                });

                // Add error event listener
                link.addEventListener('error', () => {
                    console.error(`%cProduction Loader: Failed to load CSS - ${url}`, 'background: #F44336; color: white; padding: 2px 5px; border-radius: 3px;');
                    if (callback) callback(false);
                });

                // Add timeout
                const timeout = setTimeout(() => {
                    console.error(`%cProduction Loader: CSS load timeout - ${url}`, 'background: #FF9800; color: white; padding: 2px 5px; border-radius: 3px;');
                    if (callback) callback(false);
                }, config.loadTimeout);

                link.addEventListener('load', () => {
                    clearTimeout(timeout);
                });

                // Append to head
                document.head.appendChild(link);
                console.log(`%cProduction Loader: CSS appended to head - ${url}`, 'color: #2196F3;');
            })
            .catch(error => {
                console.error(`%cProduction Loader: Error checking CSS file - ${url}`, 'background: #F44336; color: white; padding: 2px 5px; border-radius: 3px;', error);
                if (callback) callback(false);
            });
    }

    /**
     * Load JS file
     * @param {string} url - URL of the JS file to load
     * @param {function} callback - Optional callback function(success)
     */
    function loadJS(url, callback) {
        // Check if already loaded
        if (loadedResources.js.includes(url)) {
            console.log(`%cProduction Loader: JS already loaded - ${url}`, 'color: #9E9E9E;');
            if (callback) callback(true);
            return;
        }

        // Check if file exists
        fetch(url)
            .then(response => {
                if (!response.ok) {
                    console.error(`%cProduction Loader: JS file not found - ${url} (${response.status})`, 'background: #F44336; color: white; padding: 2px 5px; border-radius: 3px;');
                    if (callback) callback(false);
                    return;
                }

                // Create script element
                const script = document.createElement('script');
                script.src = url;
                script.async = true;

                // Add load event listener
                script.addEventListener('load', () => {
                    console.log(`%cProduction Loader: JS loaded - ${url}`, 'background: #4CAF50; color: white; padding: 2px 5px; border-radius: 3px;');
                    loadedResources.js.push(url);
                    if (callback) callback(true);
                });

                // Add error event listener
                script.addEventListener('error', () => {
                    console.error(`%cProduction Loader: Failed to load JS - ${url}`, 'background: #F44336; color: white; padding: 2px 5px; border-radius: 3px;');
                    if (callback) callback(false);
                });

                // Add timeout
                const timeout = setTimeout(() => {
                    console.error(`%cProduction Loader: JS load timeout - ${url}`, 'background: #FF9800; color: white; padding: 2px 5px; border-radius: 3px;');
                    if (callback) callback(false);
                }, config.loadTimeout);

                script.addEventListener('load', () => {
                    clearTimeout(timeout);
                });

                // Append to body
                document.body.appendChild(script);
                console.log(`%cProduction Loader: JS appended to body - ${url}`, 'color: #2196F3;');
            })
            .catch(error => {
                console.error(`%cProduction Loader: Error checking JS file - ${url}`, 'background: #F44336; color: white; padding: 2px 5px; border-radius: 3px;', error);
                if (callback) callback(false);
            });
    }

    /**
     * Check if resource is loaded
     */
    function isResourceLoaded(url, type) {
        return loadedResources[type].includes(url);
    }

    /**
     * Disable conflicting files to prevent double initialization
     */
    function disableConflictingFiles() {
        console.log('%cProduction Loader: Disabling conflicting files', 'color: #FFC107;');

        // Find and disable conflicting script tags
        config.disableFiles.forEach(file => {
            const fileExt = file.split('.').pop().toLowerCase();
            const isJs = fileExt === 'js';
            const isCss = fileExt === 'css';

            if (isJs) {
                // Find script tags
                const scripts = document.querySelectorAll('script[src*="' + file + '"]');
                scripts.forEach(script => {
                    // Disable by removing src attribute
                    const originalSrc = script.getAttribute('src');
                    script.removeAttribute('src');

                    // Add to disabled list
                    disabledResources.push({
                        type: 'js',
                        file: file,
                        originalSrc: originalSrc,
                        element: script
                    });

                    console.log('%cProduction Loader: Disabled JS file: ' + file, 'color: #FF5722;');
                });
            } else if (isCss) {
                // Find link tags
                const links = document.querySelectorAll('link[href*="' + file + '"]');
                links.forEach(link => {
                    // Disable by setting disabled attribute
                    const originalHref = link.getAttribute('href');
                    link.setAttribute('disabled', 'disabled');
                    link.setAttribute('data-disabled-by-loader', 'true');

                    // Add to disabled list
                    disabledResources.push({
                        type: 'css',
                        file: file,
                        originalHref: originalHref,
                        element: link
                    });

                    console.log('%cProduction Loader: Disabled CSS file: ' + file, 'color: #FF5722;');
                });
            }
        });
    }

    /**
     * Get loaded resources
     */
    function getLoadedResources() {
        return loadedResources;
    }

    /**
     * Get disabled resources
     */
    function getDisabledResources() {
        return disabledResources;
    }

    // Expose public API
    window.productionLoader = {
        loadCSS,
        loadJS,
        isResourceLoaded,
        getLoadedResources,
        getDisabledResources
    };
})();
