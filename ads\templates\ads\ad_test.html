{% extends 'base.html' %}
{% load static %}
{% load ads_filters %}

{% block title %}Ad Display Test - Enterprise QR{% endblock %}

{% block extra_css %}
<style>
    .test-section {
        margin: 40px 0;
        padding: 20px;
        border: 2px solid #ddd;
        border-radius: 8px;
        background: #f9f9f9;
    }

    .test-title {
        color: #333;
        margin-bottom: 20px;
        font-weight: bold;
    }

    .ad-debug {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        padding: 10px;
        margin: 10px 0;
        border-radius: 4px;
        font-family: monospace;
        font-size: 12px;
    }

    .no-ads {
        background: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
        padding: 15px;
        border-radius: 4px;
        text-align: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1 class="text-center mb-4">Ad Display System Test</h1>

    <!-- Premium Header Ad Test -->
    <div class="test-section">
        <h2 class="test-title">Premium Header Ad Test (All Available)</h2>
        <div class="ad-debug">
            Testing: {% display_premium_header_ad max_ads=10 %}
        </div>
        {% display_premium_header_ad max_ads=10 %}
        {% if not ads %}
            <div class="no-ads">No Premium Header ads found</div>
        {% endif %}
    </div>

    <!-- Content Inline Ad Test -->
    <div class="test-section">
        <h2 class="test-title">Content Inline Ad Test</h2>
        <div class="ad-debug">
            Testing: {% display_content_inline_ad %}
        </div>
        {% display_content_inline_ad %}
        {% if not ads %}
            <div class="no-ads">No Content Inline ads found</div>
        {% endif %}
    </div>

    <!-- Manual Ad Display Test -->
    <div class="test-section">
        <h2 class="test-title">Manual Header Ad Test</h2>
        <div class="ad-debug">
            Testing: {% display_ads location='header' max_ads=3 show_placeholder=True %}
        </div>
        {% display_ads location='header' max_ads=3 show_placeholder=True %}
    </div>

    <!-- Sidebar Ad Test -->
    <div class="test-section">
        <h2 class="test-title">Sidebar Ad Test (All Sidebar Locations)</h2>
        <div class="ad-debug">
            Testing: {% display_sidebar_ads max_ads=5 %}
        </div>
        {% display_sidebar_ads max_ads=5 %}
    </div>

    <!-- Header Ad Test -->
    <div class="test-section">
        <h2 class="test-title">Enterprise Header Banner Test (Smart Layout)</h2>
        <div class="ad-debug">
            Testing: {% display_header_ads max_ads=10 %} - Smart layout: Grid for ≤5 ads, Carousel for 6+ ads
            <br><strong>Current behavior:</strong>
            <span id="layout-mode">Loading...</span>
        </div>
        {% display_header_ads max_ads=10 %}

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const totalAds = document.querySelectorAll('[data-ad-location="header"]').length;
                const layoutMode = document.getElementById('layout-mode');

                if (totalAds >= 6) {
                    layoutMode.innerHTML = `<span style="color: #007bff;">Carousel Mode</span> - ${totalAds} ads rotating with auto-play`;
                } else {
                    layoutMode.innerHTML = `<span style="color: #28a745;">Grid Mode</span> - ${totalAds} ads with expand/collapse`;
                }
            });
        </script>
    </div>

    <!-- Footer Ad Test -->
    <div class="test-section">
        <h2 class="test-title">Footer Ad Test</h2>
        <div class="ad-debug">
            Testing: {% display_footer_ads max_ads=3 %}
        </div>
        {% display_footer_ads max_ads=3 %}
    </div>

    <!-- Homepage Featured Ad Test -->
    <div class="test-section">
        <h2 class="test-title">Homepage Featured Ad Test</h2>
        <div class="ad-debug">
            Testing: {% display_homepage_featured_ad %}
        </div>
        {% display_homepage_featured_ad %}
    </div>

    <!-- Popup Ad Test -->
    <div class="test-section">
        <h2 class="test-title">Popup Ad Test</h2>
        <div class="ad-debug">
            Testing: {% display_popup_ads %} - Should appear as popup after 8 seconds (less intrusive)
            <br><strong>Note:</strong> Popup ads appear in bottom-right corner with close button
        </div>
        {% display_popup_ads %}

        <div class="popup-test-info">
            <p><strong>Popup Ad Behavior:</strong></p>
            <ul>
                <li>Appears after 8-second delay (less intrusive)</li>
                <li>Shows in bottom-right corner</li>
                <li>Has close button (×)</li>
                <li>Remembers if closed (won't show again)</li>
                <li>Auto-tracks impressions and clicks</li>
            </ul>
            <button onclick="localStorage.clear(); location.reload();" class="btn btn-sm btn-secondary">
                Reset Popup Memory & Reload
            </button>
        </div>
    </div>

    <!-- Database Info -->
    <div class="test-section">
        <h2 class="test-title">Database Information</h2>
        <div class="ad-debug">
            <p>This page tests the ad display system to verify ads are showing correctly.</p>
            <p>If ads don't appear above, there may be an issue with:</p>
            <ul>
                <li>Template tag loading</li>
                <li>Database queries</li>
                <li>CSS hiding ads</li>
                <li>JavaScript conflicts</li>
            </ul>
        </div>
    </div>
</div>
{% endblock %}
