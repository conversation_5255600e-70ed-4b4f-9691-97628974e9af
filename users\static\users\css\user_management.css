/* User Management Styles */

/* Common Styles */
.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(90deg, #1a1a1a, #333);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
    display: inline-block;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, #3a7bd5, #00d2ff);
    border-radius: 2px;
}

.section-subtitle {
    font-size: 1.2rem;
    color: #505050;
    margin-bottom: 2rem;
    max-width: 800px;
    line-height: 1.6;
}

/* User List Page */
.user-management-container {
    padding: 2rem 0;
}

.user-management-header {
    margin-bottom: 2rem;
}

.user-filters-container {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.user-stats-container {
    margin-bottom: 2rem;
}

.user-stat-card {
    background-color: #fff;
    border-radius: 10px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    height: 100%;
    transition: all 0.3s ease;
}

.user-stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.user-stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(58, 123, 213, 0.1), rgba(0, 210, 255, 0.1));
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: #3a7bd5;
    margin-right: 1rem;
}

.user-stat-content {
    flex: 1;
}

.user-stat-value {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    color: #333;
}

.user-stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0;
}

.users-table-container {
    background-color: #fff;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.pagination-container {
    margin-top: 2rem;
}

.empty-state {
    padding: 3rem 0;
    text-align: center;
}

.empty-state-icon {
    font-size: 3rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

/* User Detail Page */
.user-detail-container {
    padding: 2rem 0;
}

.user-detail-header {
    margin-bottom: 2rem;
}

.user-profile-card {
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    margin-bottom: 2rem;
}

.user-profile-header {
    background: linear-gradient(135deg, #3a7bd5, #00d2ff);
    padding: 2rem;
    color: #fff;
    text-align: center;
}

.user-profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    margin: 0 auto 1.5rem;
    overflow: hidden;
    border: 4px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.user-profile-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.default-avatar {
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
}

.user-profile-name h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.user-profile-body {
    padding: 1.5rem;
}

.user-profile-info .info-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.user-profile-info .info-item:last-child {
    border-bottom: none;
}

.user-profile-info .info-label {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.user-profile-info .info-value {
    font-weight: 500;
    color: #333;
}

.user-details-card,
.user-permissions-card {
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.card-header {
    background-color: #f8f9fa;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #f0f0f0;
}

.card-header h4 {
    margin-bottom: 0;
    font-weight: 600;
    color: #333;
}

.card-body {
    padding: 1.5rem;
}

.detail-group {
    margin-bottom: 1.5rem;
}

.detail-group label {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
    display: block;
}

.detail-group p {
    font-weight: 500;
    color: #333;
    margin-bottom: 0;
}

.permissions-list ul,
.groups-list ul {
    padding-left: 1.5rem;
    margin-bottom: 0;
}

/* User Form Page */
.user-form-container {
    padding: 2rem 0;
}

.user-form-header {
    margin-bottom: 2rem;
}

.user-form-content {
    margin-bottom: 2rem;
}

.form-actions {
    margin-top: 2rem;
    display: flex;
    gap: 1rem;
}

/* User Delete Page */
.user-delete-container {
    padding: 2rem 0;
}

.user-delete-header {
    margin-bottom: 2rem;
}

.delete-warning {
    text-align: center;
    margin-bottom: 2rem;
}

.warning-icon {
    font-size: 3rem;
    color: #dc3545;
    margin-bottom: 1rem;
}

.user-summary {
    display: flex;
    align-items: center;
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.user-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 1.5rem;
    border: 3px solid #fff;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-info h4 {
    margin-bottom: 0.5rem;
}

.user-info p {
    margin-bottom: 0.25rem;
}

.delete-form {
    text-align: center;
}

/* Responsive Styles */
@media (max-width: 767.98px) {
    .user-summary {
        flex-direction: column;
        text-align: center;
    }
    
    .user-avatar {
        margin-right: 0;
        margin-bottom: 1rem;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .form-actions .btn {
        margin-bottom: 0.5rem;
    }
}
