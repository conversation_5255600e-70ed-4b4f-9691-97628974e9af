{% extends 'base.html' %}
{% load static %}

{% block title %}Ad Display Demo{% endblock %}

{% block extra_css %}
<!-- Include common ads CSS -->
{% include 'ads/includes/ads_common_css.html' %}
<style>
    .demo-section {
        margin-bottom: 40px;
        padding: 20px;
        border-radius: 8px;
        background-color: #f8f9fa;
    }

    .demo-title {
        font-weight: 700;
        color: #1a237e;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid rgba(0,0,0,0.1);
    }

    .demo-description {
        margin-bottom: 20px;
    }

    .demo-content {
        min-height: 200px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .demo-sidebar {
        background-color: #f1f3f9;
        padding: 20px;
        border-radius: 8px;
        height: 100%;
    }

    .demo-sidebar-title {
        font-weight: 600;
        color: #1a237e;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid rgba(0,0,0,0.1);
    }

    .demo-footer {
        background-color: #1a237e;
        color: white;
        padding: 30px 0;
        margin-top: 40px;
    }

    .demo-footer-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .demo-footer-links {
        display: flex;
        gap: 20px;
    }

    .demo-footer-links a {
        color: white;
        text-decoration: none;
    }

    .demo-footer-links a:hover {
        text-decoration: underline;
    }

    .demo-metrics {
        background-color: #e3f2fd;
        padding: 15px;
        border-radius: 8px;
        margin-top: 20px;
    }

    .demo-metrics-title {
        font-weight: 600;
        color: #1a237e;
        margin-bottom: 10px;
    }

    .demo-metrics-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 5px;
        padding-bottom: 5px;
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }

    .demo-metrics-label {
        font-weight: 500;
    }

    .demo-metrics-value {
        font-weight: 600;
        color: #1a237e;
    }

    .demo-ad-container {
        margin-bottom: 20px;
    }

    .demo-button {
        display: inline-block;
        padding: 10px 20px;
        background-color: #1a237e;
        color: white;
        border-radius: 4px;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .demo-button:hover {
        background-color: #3949ab;
        transform: translateY(-2px);
    }
</style>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="ads-page-header">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <nav aria-label="breadcrumb" class="ads-breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'index' %}">Home</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'ads:dashboard' %}">Dashboard</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Ad Display Demo</li>
                    </ol>
                </nav>

                <h1 class="display-6 text-center mb-1 ads-page-title">Ad Display Demo</h1>
                <p class="lead text-center mb-2 ads-page-subtitle">See ads in action with real-time tracking</p>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="demo-section">
                <h2 class="demo-title">Header Ad</h2>
                <p class="demo-description">This demonstrates an ad displayed in the header position.</p>

                <div class="demo-ad-container">
                    <!-- Include the ad display template for header location -->
                    {% include 'ads/includes/ad_display.html' with location='header' max_ads=1 ads=ads_by_location.header show_placeholder=True display_style='carousel' %}
                </div>

                <div class="demo-metrics">
                    <h4 class="demo-metrics-title">Real-time Metrics</h4>
                    <div class="demo-metrics-item">
                        <div class="demo-metrics-label">Impressions:</div>
                        <div class="demo-metrics-value" id="header-impressions">0</div>
                    </div>
                    <div class="demo-metrics-item">
                        <div class="demo-metrics-label">Clicks:</div>
                        <div class="demo-metrics-value" id="header-clicks">0</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="demo-section">
                <h2 class="demo-title">Content Ad</h2>
                <p class="demo-description">This demonstrates an ad displayed within the main content.</p>

                <div class="demo-content">
                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod, nisl eget aliquam ultricies, nunc nisl aliquet nunc, quis aliquam nisl nunc quis nisl. Nullam euismod, nisl eget aliquam ultricies, nunc nisl aliquet nunc, quis aliquam nisl nunc quis nisl.</p>
                </div>

                <div class="demo-ad-container">
                    <!-- Include the ad display template for content location -->
                    {% include 'ads/includes/ad_display.html' with location='content' max_ads=4 ads=ads_by_location.content show_placeholder=True display_style='grid' %}
                </div>

                <div class="demo-content">
                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod, nisl eget aliquam ultricies, nunc nisl aliquet nunc, quis aliquam nisl nunc quis nisl. Nullam euismod, nisl eget aliquam ultricies, nunc nisl aliquet nunc, quis aliquam nisl nunc quis nisl.</p>
                </div>

                <div class="demo-metrics">
                    <h4 class="demo-metrics-title">Real-time Metrics</h4>
                    <div class="demo-metrics-item">
                        <div class="demo-metrics-label">Impressions:</div>
                        <div class="demo-metrics-value" id="content-impressions">0</div>
                    </div>
                    <div class="demo-metrics-item">
                        <div class="demo-metrics-label">Clicks:</div>
                        <div class="demo-metrics-value" id="content-clicks">0</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="demo-sidebar">
                <h3 class="demo-sidebar-title">Sidebar</h3>

                <div class="demo-ad-container">
                    <!-- Include the ad display template for sidebar location -->
                    {% include 'ads/includes/ad_display.html' with location='sidebar' max_ads=3 ads=ads_by_location.sidebar show_placeholder=True display_style='stack' %}
                </div>

                <div class="demo-metrics">
                    <h4 class="demo-metrics-title">Real-time Metrics</h4>
                    <div class="demo-metrics-item">
                        <div class="demo-metrics-label">Impressions:</div>
                        <div class="demo-metrics-value" id="sidebar-impressions">0</div>
                    </div>
                    <div class="demo-metrics-item">
                        <div class="demo-metrics-label">Clicks:</div>
                        <div class="demo-metrics-value" id="sidebar-clicks">0</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-6 offset-md-3">
            <div class="card mb-4">
                <div class="card-header">
                    <h4>Ad Rotation Settings</h4>
                </div>
                <div class="card-body">
                    <form method="get" action="{% url 'ads:ad_demo' %}" class="row g-3">
                        <div class="col-md-6">
                            <label for="rotation" class="form-label">Rotation Strategy:</label>
                            <select name="rotation" id="rotation" class="form-select">
                                <option value="random" {% if request.GET.rotation == 'random' or not request.GET.rotation %}selected{% endif %}>Random</option>
                                <option value="newest" {% if request.GET.rotation == 'newest' %}selected{% endif %}>Newest First</option>
                                <option value="oldest" {% if request.GET.rotation == 'oldest' %}selected{% endif %}>Oldest First</option>
                                <option value="highest_ctr" {% if request.GET.rotation == 'highest_ctr' %}selected{% endif %}>Highest CTR</option>
                            </select>
                        </div>

                        <div class="col-md-6">
                            <label for="show_special_ads" class="form-label">Special Ad Types:</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="show_popup" name="show_popup" value="true" {% if request.GET.show_popup %}checked{% endif %}>
                                <label class="form-check-label" for="show_popup">Show Popup Ad</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="show_interstitial" name="show_interstitial" value="true" {% if request.GET.show_interstitial %}checked{% endif %}>
                                <label class="form-check-label" for="show_interstitial">Show Interstitial Ad</label>
                            </div>
                        </div>

                        <div class="col-12 mt-3">
                            <button type="submit" class="btn btn-primary">Apply Settings</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-12 text-center">
            <a href="{% url 'ads:admin_active_ads' %}" class="demo-button">
                <i class="fas fa-chart-bar me-2"></i> View Admin Dashboard
            </a>
        </div>
    </div>
</div>

<!-- Footer with Ad -->
<div class="demo-footer">
    <div class="container">
        <div class="demo-footer-content">
            <div>
                <h3>QR Code Generator</h3>
                <p>Generate elegant, corporate QR codes for your business.</p>
            </div>

            <div class="demo-footer-links">
                <a href="#">Home</a>
                <a href="#">About</a>
                <a href="#">Services</a>
                <a href="#">Contact</a>
            </div>
        </div>

        <div class="demo-ad-container mt-4">
            <!-- Include the ad display template for footer location -->
            {% include 'ads/includes/ad_display.html' with location='footer' max_ads=1 ads=ads_by_location.footer show_placeholder=True display_style='sticky' %}
        </div>

        <!-- Popup Ad Demo -->
        {% if ads_by_location.popup and request.GET.show_popup %}
            {% include 'ads/includes/ad_display.html' with location='popup' max_ads=1 ads=ads_by_location.popup show_placeholder=False display_style='popup' %}
        {% endif %}

        <!-- Interstitial Ad Demo -->
        {% if ads_by_location.interstitial and request.GET.show_interstitial %}
            {% include 'ads/includes/ad_display.html' with location='interstitial' max_ads=1 ads=ads_by_location.interstitial show_placeholder=False display_style='interstitial' %}
        {% endif %}

        <div class="demo-metrics">
            <h4 class="demo-metrics-title">Real-time Metrics</h4>
            <div class="row">
                <div class="col-md-4">
                    <div class="demo-metrics-item">
                        <div class="demo-metrics-label">Impressions:</div>
                        <div class="demo-metrics-value" id="footer-impressions">0</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="demo-metrics-item">
                        <div class="demo-metrics-label">Clicks:</div>
                        <div class="demo-metrics-value" id="footer-clicks">0</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="demo-metrics-item">
                        <div class="demo-metrics-label">CTR:</div>
                        <div class="demo-metrics-value" id="footer-ctr">0.00%</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'ads/js/ad-tracker.js' %}"></script>
<script>
    // Simulate metrics for demo purposes
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize counters
        const metrics = {
            header: { impressions: 0, clicks: 0 },
            content: { impressions: 0, clicks: 0 },
            sidebar: { impressions: 0, clicks: 0 },
            footer: { impressions: 0, clicks: 0 }
        };

        // Update metrics display
        function updateMetrics() {
            for (const location in metrics) {
                document.getElementById(`${location}-impressions`).textContent = metrics[location].impressions;
                document.getElementById(`${location}-clicks`).textContent = metrics[location].clicks;

                // Calculate CTR if element exists
                const ctrElement = document.getElementById(`${location}-ctr`);
                if (ctrElement && metrics[location].impressions > 0) {
                    const ctr = (metrics[location].clicks / metrics[location].impressions) * 100;
                    ctrElement.textContent = ctr.toFixed(2) + '%';
                }
            }
        }

        // Simulate impressions
        setTimeout(() => {
            metrics.header.impressions++;
            metrics.sidebar.impressions++;
            updateMetrics();
        }, 1000);

        setTimeout(() => {
            metrics.content.impressions++;
            updateMetrics();
        }, 3000);

        setTimeout(() => {
            metrics.footer.impressions++;
            updateMetrics();
        }, 5000);

        // Add click handlers to ads
        const adLinks = document.querySelectorAll('.ad-link');
        adLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                // Prevent actual navigation for demo
                e.preventDefault();

                // Determine which ad was clicked
                const adContainer = this.closest('.ad-container');
                if (adContainer.classList.contains('ad-location-header')) {
                    metrics.header.clicks++;
                } else if (adContainer.classList.contains('ad-location-content')) {
                    metrics.content.clicks++;
                } else if (adContainer.classList.contains('ad-location-sidebar')) {
                    metrics.sidebar.clicks++;
                } else if (adContainer.classList.contains('ad-location-footer')) {
                    metrics.footer.clicks++;
                }

                updateMetrics();

                // Show a message
                alert('Ad click tracked! In a real scenario, this would navigate to the advertiser\'s website.');
            });
        });
    });
</script>
{% endblock %}
