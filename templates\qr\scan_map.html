{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}🗺 QR Scan Map{% endblock %}

{% block extrahead %}
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<style>
    .map-dashboard {
        padding: 20px;
    }

    .dashboard-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        padding: 30px;
        border-radius: 12px;
        margin-bottom: 30px;
        box-shadow: 0 8px 32px rgba(40, 167, 69, 0.3);
    }

    .dashboard-header h1 {
        margin: 0;
        font-size: 2.5rem;
        font-weight: 700;
    }

    .dashboard-header p {
        margin: 10px 0 0 0;
        opacity: 0.9;
        font-size: 1.1rem;
    }

    .map-container {
        background: white;
        border-radius: 12px;
        padding: 25px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        margin-bottom: 25px;
    }

    #map {
        height: 600px;
        border-radius: 8px;
        border: 2px solid #e9ecef;
    }

    .map-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        text-align: center;
        border-left: 4px solid #28a745;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    }

    .stat-value {
        font-size: 2rem;
        font-weight: 700;
        color: #28a745;
        margin-bottom: 5px;
    }

    .stat-label {
        color: #666;
        font-size: 0.9rem;
        font-weight: 500;
    }

    .map-controls {
        background: white;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 25px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    .control-group {
        display: flex;
        gap: 15px;
        align-items: center;
        flex-wrap: wrap;
    }

    .control-group label {
        font-weight: 600;
        color: #333;
    }

    .control-group select,
    .control-group button {
        padding: 8px 15px;
        border: 1px solid #ddd;
        border-radius: 6px;
        font-size: 0.9rem;
    }

    .control-group button {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .control-group button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }

    .leaflet-popup-content {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .popup-content {
        min-width: 200px;
    }

    .popup-content strong {
        color: #28a745;
    }

    .popup-content .scan-time {
        color: #666;
        font-size: 0.85rem;
        margin-top: 5px;
    }

    .admin-actions {
        background: white;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    .admin-actions h3 {
        margin-bottom: 15px;
        color: #333;
        font-size: 1.2rem;
    }

    .admin-actions a {
        display: inline-block;
        margin-right: 15px;
        margin-bottom: 10px;
        padding: 10px 20px;
        background: #f8f9fa;
        color: #333;
        text-decoration: none;
        border-radius: 6px;
        transition: all 0.3s ease;
        border: 1px solid #dee2e6;
    }

    .admin-actions a:hover {
        background: #e9ecef;
        transform: translateY(-2px);
    }

    .admin-actions a.primary {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        border: none;
    }

    .admin-actions a.primary:hover {
        background: linear-gradient(135deg, #218838, #1ea085);
    }

    @media (max-width: 768px) {
        .map-stats {
            grid-template-columns: 1fr;
        }

        .dashboard-header h1 {
            font-size: 2rem;
        }

        #map {
            height: 400px;
        }

        .control-group {
            flex-direction: column;
            align-items: stretch;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="map-dashboard">
    <!-- Dashboard Header -->
    <div class="dashboard-header">
        <h1>🗺 QR Scan Map</h1>
        <p>Interactive map showing real-time QR scan locations with IPinfo geolocation data</p>
        <small style="opacity: 0.8;">Available at: /qr-admin-map/ (Admin-only)</small>
    </div>

    <!-- Map Statistics -->
    <div class="map-stats">
        <div class="stat-card">
            <div class="stat-value">{{ scans.count }}</div>
            <div class="stat-label">Total Mapped Scans</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{{ scans|length }}</div>
            <div class="stat-label">Visible Markers</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{% for scan in scans %}{% if scan.country %}{{ scan.country|upper }}{% if not forloop.last %}, {% endif %}{% endif %}{% endfor %}</div>
            <div class="stat-label">Countries</div>
        </div>
    </div>

    <!-- Map Controls -->
    <div class="map-controls">
        <div class="control-group">
            <label for="timeFilter">Time Filter:</label>
            <select id="timeFilter">
                <option value="all">All Time</option>
                <option value="24h">Last 24 Hours</option>
                <option value="7d">Last 7 Days</option>
                <option value="30d">Last 30 Days</option>
            </select>

            <button onclick="refreshMap()">🔄 Refresh Map</button>
            <button onclick="fitMapToMarkers()">🎯 Fit to Markers</button>
            <button onclick="toggleClustering()">📍 Toggle Clustering</button>
            <button onclick="toggleAutoRefresh()" id="autoRefreshBtn">⏰ Auto-Refresh: OFF</button>
        </div>
    </div>

    <!-- Map Container -->
    <div class="map-container">
        <div id="map"></div>
    </div>

    <!-- Admin Actions -->
    <div class="admin-actions">
        <h3>⚙️ Admin Actions</h3>
        <a href="{% url 'qr_dashboard' %}" class="primary">📊 Analytics Dashboard</a>
        <a href="{% url 'admin:qrcode_app_qrscanlog_changelist' %}">📋 View All Scan Logs</a>
        <a href="{% url 'admin:index' %}">🏠 Back to Admin</a>
    </div>
</div>

<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
<script>
    // Initialize the map
    const map = L.map('map').setView([20, 0], 2);

    // Add OpenStreetMap tiles
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);

    // Global variables
    let markers = [];
    let validScans = [];

    // Function to determine marker color based on organization
    function getMarkerColor(org) {
        if (!org || org === 'Unknown') return 'blue';
        const orgLower = org.toLowerCase();
        const redKeywords = ['gov', 'government', 'mil', 'military', 'defense', 'army', 'navy', 'police', 'public', 'federal', 'state'];
        return redKeywords.some(word => orgLower.includes(word)) ? 'red' : 'blue';
    }

    // Function to create colored markers
    function createMarker(scan) {
        const color = getMarkerColor(scan.org);
        const marker = L.circleMarker([scan.lat, scan.lng], {
            radius: 8,
            fillColor: color,
            color: color,
            weight: 1,
            opacity: 1,
            fillOpacity: 0.8
        }).addTo(map);

        marker.bindPopup(`
            <div class="popup-content">
                <strong>Code:</strong> ${scan.code}<br/>
                <strong>City:</strong> ${scan.city}, ${scan.country}<br/>
                <strong>Organization:</strong> ${scan.org}<br/>
                <strong>IP:</strong> ${scan.ip_address}<br/>
                <div class="scan-time"><strong>Time:</strong> ${scan.timestamp}</div>
            </div>
        `);

        return marker;
    }

    // Function to load scans via AJAX
    function loadScans() {
        fetch("{% url 'qr_map_data' %}")
            .then(response => response.json())
            .then(data => {
                // Clear old markers
                markers.forEach(m => map.removeLayer(m));
                markers = [];

                // Filter valid scans
                validScans = data.filter(scan => scan.latitude !== null && scan.longitude !== null);

                // Add new markers
                validScans.forEach(scan => {
                    const marker = createMarker(scan);
                    markers.push(marker);
                });

                // Fit map to markers if any exist
                if (markers.length > 0) {
                    fitMapToMarkers();
                }

                console.log(`Loaded ${markers.length} scan locations`);
            })
            .catch(error => {
                console.error('Error loading scan data:', error);
            });
    }

    // Fit map to show all markers
    function fitMapToMarkers() {
        if (markers.length > 0) {
            const group = new L.featureGroup(markers);
            map.fitBounds(group.getBounds().pad(0.1));
        }
    }

    // Refresh map function (now uses AJAX)
    function refreshMap() {
        loadScans();
    }

    // Toggle clustering (placeholder for future implementation)
    function toggleClustering() {
        alert('Clustering feature coming soon! This will group nearby markers for better visualization.');
    }

    // Time filter functionality with real filtering
    document.getElementById('timeFilter').addEventListener('change', function() {
        const selectedValue = this.value;
        console.log('Time filter changed to:', selectedValue);
        filterScansByTime(selectedValue);
    });

    // Filter scans by time period
    function filterScansByTime(period) {
        const now = new Date();
        let cutoffTime;

        switch(period) {
            case '24h':
                cutoffTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
                break;
            case '7d':
                cutoffTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                break;
            case '30d':
                cutoffTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                break;
            default:
                cutoffTime = null;
        }

        // Clear existing markers
        markers.forEach(marker => map.removeLayer(marker));
        markers.length = 0;

        // Add filtered markers
        validScans.forEach(scan => {
            // Parse scan timestamp (you may need to adjust this based on your date format)
            const scanTime = new Date(scan.timestamp || 0);

            if (!cutoffTime || scanTime >= cutoffTime) {
                const marker = L.marker([scan.lat, scan.lng]).addTo(map);
                marker.bindPopup(scan.popup);
                markers.push(marker);
            }
        });

        // Fit map to filtered markers
        if (markers.length > 0) {
            fitMapToMarkers();
        }

        console.log(`Filtered to ${markers.length} scans for period: ${period}`);
    }

    // Auto-refresh functionality
    let autoRefreshInterval = null;
    let autoRefreshEnabled = false;

    function toggleAutoRefresh() {
        const btn = document.getElementById('autoRefreshBtn');

        if (autoRefreshEnabled) {
            // Disable auto-refresh
            clearInterval(autoRefreshInterval);
            autoRefreshEnabled = false;
            btn.textContent = '⏰ Auto-Refresh: OFF';
            btn.style.background = 'linear-gradient(135deg, #28a745, #20c997)';
        } else {
            // Enable auto-refresh (every 60 seconds using AJAX)
            autoRefreshInterval = setInterval(() => {
                console.log('Auto-refreshing map...');
                loadScans();
            }, 60000);
            autoRefreshEnabled = true;
            btn.textContent = '⏰ Auto-Refresh: ON (60s)';
            btn.style.background = 'linear-gradient(135deg, #dc3545, #fd7e14)';
        }
    }



    // Add legend for marker colors
    const legend = L.control({position: 'bottomright'});
    legend.onAdd = function (map) {
        const div = L.DomUtil.create('div', 'info legend');
        div.innerHTML = `
            <div style="background: white; padding: 10px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.2);">
                <h4 style="margin: 0 0 10px 0;">Organization Types</h4>
                <div style="margin: 5px 0;"><span style="color: red; font-size: 20px;">●</span> Government/Military</div>
                <div style="margin: 5px 0;"><span style="color: blue; font-size: 20px;">●</span> General/Commercial</div>
            </div>
        `;
        return div;
    };
    legend.addTo(map);

    // Initial load
    loadScans();

    console.log('QR Scan Map initialized with real-time AJAX updates and organization highlighting');
</script>
{% endblock %}
