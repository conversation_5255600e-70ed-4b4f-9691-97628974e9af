"""
Utility functions for QR code functionality including geolocation services
"""
import requests
import re
from django.conf import settings
from django.core.cache import cache


def get_client_ip(request):
    """Get the real IP address of the client"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def detect_scanner_type(user_agent_string):
    """
    Detect if the request comes from a third-party QR scanner app or native camera

    Args:
        user_agent_string (str): The User-Agent header from the request

    Returns:
        str: 'native_camera', 'third_party_app', or 'unknown'
    """
    if not user_agent_string:
        return 'unknown'

    user_agent_lower = user_agent_string.lower()

    # Known third-party QR scanner app indicators
    scanner_indicators = [
        'qr', 'barcode', 'scan', 'scanner', 'reader',
        'zxing', 'qrcode', 'bar-code', 'qr-code',
        'com.google.zxing', 'qr_scanner', 'barcode_scanner'
    ]

    # Check for scanner app indicators
    for indicator in scanner_indicators:
        if indicator in user_agent_lower:
            return 'third_party_app'

    # Simple user agent parsing without external dependencies
    try:
        user_agent_lower = user_agent_string.lower()

        # Check if it's a mobile device
        is_mobile = any(mobile_indicator in user_agent_lower for mobile_indicator in [
            'mobile', 'android', 'iphone', 'ipad', 'ipod', 'blackberry', 'windows phone'
        ])

        # Check if it's a known mobile browser
        is_mobile_browser = any(browser in user_agent_lower for browser in [
            'safari', 'chrome', 'firefox', 'edge', 'opera'
        ])

        # If it's a mobile browser without scanner indicators, it's likely the native camera
        if is_mobile and is_mobile_browser:
            return 'native_camera'

        # Desktop browsers are likely not scanning QR codes
        if not is_mobile:
            return 'unknown'

    except Exception:
        pass

    return 'unknown'


def get_geolocation_from_ip(ip_address):
    """
    Get geolocation data from IP address using multiple services with fallback

    Args:
        ip_address (str): IP address to geolocate

    Returns:
        dict: Geolocation data with keys: country, city, region, latitude, longitude, etc.
    """
    # Check cache first (cache for 1 hour)
    cache_key = f"geo_ip_{ip_address}"
    cached_data = cache.get(cache_key)
    if cached_data:
        return cached_data

    # Default response
    geo_data = {
        'country': 'Unknown',
        'city': 'Unknown',
        'region': 'Unknown',
        'latitude': None,
        'longitude': None,
        'timezone': 'Unknown',
        'postal_code': 'Unknown',
        'organization': 'Unknown',  # Enterprise feature: ISP/Organization
        'asn': None,  # Enterprise feature: Autonomous System Number
        'privacy_flags': {}  # Enterprise feature: VPN, proxy detection
    }

    # Skip localhost and private IPs
    if ip_address in ['127.0.0.1', 'localhost'] or ip_address.startswith('192.168.') or ip_address.startswith('10.'):
        return geo_data

    # Try ipinfo.io first (use environment variable for production)
    try:
        # Get API token from environment variable for production security
        api_token = getattr(settings, 'IPINFO_API_TOKEN', '082682c8d0e7a8')
        response = requests.get(
            f'https://ipinfo.io/{ip_address}/json?token={api_token}',
            timeout=5
        )
        if response.status_code == 200:
            data = response.json()
            # Parse privacy flags (enterprise feature)
            privacy_flags = {}
            if data.get('privacy'):
                privacy_data = data['privacy']
                privacy_flags = {
                    'vpn': privacy_data.get('vpn', False),
                    'proxy': privacy_data.get('proxy', False),
                    'tor': privacy_data.get('tor', False),
                    'relay': privacy_data.get('relay', False),
                    'hosting': privacy_data.get('hosting', False)
                }

            geo_data.update({
                'country': data.get('country', 'Unknown'),
                'city': data.get('city', 'Unknown'),
                'region': data.get('region', 'Unknown'),
                'timezone': data.get('timezone', 'Unknown'),
                'postal_code': data.get('postal', 'Unknown'),
                'organization': data.get('org', 'Unknown'),  # Enterprise: ISP/Organization
                'asn': data.get('asn', {}).get('asn') if isinstance(data.get('asn'), dict) else None,  # Enterprise: ASN
                'privacy_flags': privacy_flags  # Enterprise: Privacy detection
            })

            # Parse location coordinates
            if data.get('loc'):
                try:
                    lat, lon = data['loc'].split(',')
                    geo_data['latitude'] = float(lat)
                    geo_data['longitude'] = float(lon)
                except (ValueError, IndexError):
                    pass

            # Cache successful result
            cache.set(cache_key, geo_data, 3600)  # Cache for 1 hour
            return geo_data
    except Exception as e:
        print(f"ipinfo.io failed: {e}")

    # Fallback to ipapi.co
    try:
        response = requests.get(f'https://ipapi.co/{ip_address}/json/', timeout=5)
        if response.status_code == 200:
            data = response.json()
            geo_data.update({
                'country': data.get('country_name', 'Unknown'),
                'city': data.get('city', 'Unknown'),
                'region': data.get('region', 'Unknown'),
                'latitude': data.get('latitude'),
                'longitude': data.get('longitude'),
                'timezone': data.get('timezone', 'Unknown'),
                'postal_code': data.get('postal', 'Unknown')
            })

            # Cache successful result
            cache.set(cache_key, geo_data, 3600)  # Cache for 1 hour
            return geo_data
    except Exception as e:
        print(f"ipapi.co failed: {e}")

    # Cache even failed results (for 5 minutes) to avoid repeated API calls
    cache.set(cache_key, geo_data, 300)
    return geo_data


def parse_device_info(user_agent_string):
    """
    Parse device information from user agent string

    Args:
        user_agent_string (str): The User-Agent header from the request

    Returns:
        dict: Device information with keys: device_type, os, browser
    """
    if not user_agent_string:
        return {
            'device_type': 'Unknown',
            'os': 'Unknown',
            'browser': 'Unknown'
        }

    try:
        user_agent_lower = user_agent_string.lower()

        # Simple device type detection
        if 'ipad' in user_agent_lower or 'tablet' in user_agent_lower:
            device_type = 'Tablet'
        elif any(mobile_indicator in user_agent_lower for mobile_indicator in [
            'mobile', 'android', 'iphone', 'ipod', 'blackberry', 'windows phone'
        ]):
            device_type = 'Mobile'
        else:
            device_type = 'Desktop'

        # Simple OS detection
        if 'windows' in user_agent_lower:
            os = 'Windows'
        elif 'mac os' in user_agent_lower or 'macos' in user_agent_lower:
            os = 'macOS'
        elif 'android' in user_agent_lower:
            os = 'Android'
        elif 'ios' in user_agent_lower or 'iphone' in user_agent_lower or 'ipad' in user_agent_lower:
            os = 'iOS'
        elif 'linux' in user_agent_lower:
            os = 'Linux'
        else:
            os = 'Unknown'

        # Simple browser detection
        if 'chrome' in user_agent_lower:
            browser = 'Chrome'
        elif 'firefox' in user_agent_lower:
            browser = 'Firefox'
        elif 'safari' in user_agent_lower:
            browser = 'Safari'
        elif 'edge' in user_agent_lower:
            browser = 'Edge'
        elif 'opera' in user_agent_lower:
            browser = 'Opera'
        else:
            browser = 'Unknown'

        return {
            'device_type': device_type,
            'os': os,
            'browser': browser
        }
    except Exception:
        return {
            'device_type': 'Unknown',
            'os': 'Unknown',
            'browser': 'Unknown'
        }


def generate_qr_landing_url(request, qr_code):
    """
    Generate the landing URL for a QR code

    Args:
        request: Django request object
        qr_code: QRCode model instance

    Returns:
        str: Full landing URL
    """
    from django.urls import reverse
    from django.conf import settings

    landing_path = reverse('qr_landing', kwargs={'unique_id': qr_code.unique_id})

    # Check if we have a custom domain configured for QR codes
    if hasattr(settings, 'QR_CODE_DOMAIN') and settings.QR_CODE_DOMAIN:
        # Use custom domain for production
        protocol = 'https' if getattr(settings, 'QR_CODE_USE_HTTPS', True) else 'http'
        return f"{protocol}://{settings.QR_CODE_DOMAIN}{landing_path}"
    else:
        # Fallback to request-based URL (for development)
        return request.build_absolute_uri(landing_path)


def should_show_scanner_warning(user_agent_string):
    """
    Determine if scanner warning should be shown based on user agent

    Args:
        user_agent_string (str): The User-Agent header from the request

    Returns:
        bool: True if warning should be shown
    """
    scanner_type = detect_scanner_type(user_agent_string)
    return scanner_type == 'third_party_app'
