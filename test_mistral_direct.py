import os
import requests
import json
from mistralai.client import MistralClient
from mistralai.models.chat_completion import ChatMessage

# Get the Mistral API key from the .env file or use a default value
try:
    with open('.env', 'r') as f:
        for line in f:
            if line.startswith('MISTRAL_API_KEY='):
                api_key = line.strip().split('=')[1]
                break
        else:
            api_key = "1sYVPQRGnnXu9cw"  # Default API key
except FileNotFoundError:
    api_key = "1sYVPQRGnnXu9cw"  # Default API key

# Get the Mistral model from the .env file or use a default value
try:
    with open('.env', 'r') as f:
        for line in f:
            if line.startswith('MISTRAL_MODEL='):
                model = line.strip().split('=')[1]
                break
        else:
            model = "mistral-tiny"  # Default model
except FileNotFoundError:
    model = "mistral-tiny"  # Default model

print(f"Using API key: {api_key[:4]}...{api_key[-4:]}")
print(f"Using model: {model}")

try:
    print("Initializing MistralClient...")
    # Initialize the client
    client = MistralClient(api_key=api_key)
    print("Client initialized successfully")

    # Test a simple chat completion to verify the API key works
    print("Testing API key with a simple chat completion...")
    test_response = client.chat(
        model=model,
        messages=[ChatMessage(role="user", content="Hello, are you working?")],
        temperature=0.7,
        max_tokens=10
    )
    print(f"Test response: {test_response.choices[0].message.content}")

    # Test direct API call to check if Mistral is online
    print("\nTesting direct API call to Mistral...")
    api_url = "https://api.mistral.ai/v1/models"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    print(f"Making API request to: {api_url}")
    response = requests.get(api_url, headers=headers, timeout=5)

    print(f"Response status code: {response.status_code}")

    if response.status_code == 200:
        models_data = response.json()

        # Check if our model is in the list of available models
        available_models = []
        if 'data' in models_data and isinstance(models_data['data'], list):
            available_models = [model_data.get('id') for model_data in models_data.get('data', []) if model_data.get('id')]

        is_available = model in available_models

        print(f"Model {model} {'is' if is_available else 'is not'} available")
        print(f"Available models: {', '.join(available_models[:5])}{'...' if len(available_models) > 5 else ''}")

        if is_available:
            print("\nMistral API is ONLINE and the model is available")
        else:
            print("\nMistral API is ONLINE but the model is not available")
    else:
        print(f"\nMistral API is OFFLINE or not responding correctly")
        print(f"Response content: {response.text}")

    # Create a prompt for ad generation
    prompt = """
    Generate a creative and professional advertisement for a coffee shop.
    The advertisement should include a catchy title and compelling content.
    The advertisement should be in English language.
    The business type is: coffee shop
    The target audience is: young professionals
    The tone should be: professional

    Format the advertisement as:
    Title: [catchy title]
    Content: [compelling content that is concise and persuasive]

    Keep the title under 60 characters and make it attention-grabbing.
    Make the ad sound professional, engaging, and persuasive.
    """

    # Create a message
    messages = [
        ChatMessage(role="user", content=prompt)
    ]

    # Generate a response
    print("Generating response...")
    response = client.chat(
        model=model,
        messages=messages,
        temperature=0.7,
        max_tokens=150
    )

    # Print the response
    print("\nResponse:")
    content = response.choices[0].message.content
    print(content)

    # Try to parse the response
    print("\nParsed response:")
    if "Title:" in content and "Content:" in content:
        # Split by "Title:" and "Content:"
        parts = content.split("Title:")
        if len(parts) > 1:
            title_content = parts[1].strip()
            title_content_parts = title_content.split("Content:")

            if len(title_content_parts) > 1:
                title = title_content_parts[0].strip()
                content_text = title_content_parts[1].strip()

                print(f"Title: {title}")
                print(f"Content: {content_text}")
            else:
                print("Could not parse content")
        else:
            print("Could not parse title")
    else:
        print("Response does not contain Title: and Content: markers")

except Exception as e:
    print(f"Error: {str(e)}")
    import traceback
    traceback.print_exc()
