/* Advanced Customization Styles */
:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #60a5fa;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #6366f1;
    --text-dark: #1f2937;
    --text-medium: #4b5563;
    --text-light: #9ca3af;
    --bg-light: #f9fafb;
    --bg-medium: #f3f4f6;
    --border-color: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --transition: all 0.2s ease;
}

/* Advanced Options Container */
.advanced-options-container {
    background-color: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    margin-bottom: 2rem;
    overflow: hidden;
}

.advanced-options-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background-color: var(--bg-light);
    border-bottom: 1px solid var(--border-color);
}

.advanced-options-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.advanced-options-title i {
    color: var(--primary-color);
}

.advanced-options-toggle {
    background: none;
    border: none;
    color: var(--text-medium);
    cursor: pointer;
    font-size: 1.25rem;
    transition: var(--transition);
}

.advanced-options-toggle:hover {
    color: var(--primary-color);
}

.advanced-options-content {
    padding: 1.5rem;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.advanced-options-content.collapsed {
    display: none;
}

/* Option Groups */
.option-group {
    margin-bottom: 1.5rem;
}

.option-group-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 0.75rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.option-group-title i {
    color: var(--primary-color);
    font-size: 1rem;
}

/* Form Controls */
.form-control {
    margin-bottom: 1.25rem;
}

.form-control label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-medium);
    margin-bottom: 0.5rem;
}

.form-control select,
.form-control input {
    width: 100%;
    padding: 0.625rem 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    color: var(--text-dark);
    background-color: white;
    transition: var(--transition);
}

.form-control select:focus,
.form-control input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    outline: none;
}

.form-control select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%234b5563'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 1rem;
    padding-right: 2.5rem;
}

.form-control .color-picker-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.form-control .color-picker-wrapper input[type="color"] {
    width: 2.5rem;
    height: 2.5rem;
    padding: 0.25rem;
    border-radius: var(--radius-md);
    cursor: pointer;
}

.form-control .color-picker-wrapper input[type="text"] {
    flex: 1;
    margin-left: 0.75rem;
}

/* Option Tooltip */
.option-tooltip {
    position: absolute;
    top: calc(100% + 0.5rem);
    left: 0;
    width: 100%;
    padding: 0.5rem 0.75rem;
    background-color: var(--text-dark);
    color: white;
    font-size: 0.75rem;
    border-radius: var(--radius-sm);
    z-index: 10;
    animation: fadeIn 0.2s ease;
}

.option-tooltip::before {
    content: '';
    position: absolute;
    top: -0.25rem;
    left: 1rem;
    width: 0.5rem;
    height: 0.5rem;
    background-color: var(--text-dark);
    transform: rotate(45deg);
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(0.5rem);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Preview Section */
.preview-section {
    background-color: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.preview-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0;
}

.preview-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.live-preview-toggle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.live-preview-toggle label {
    font-size: 0.875rem;
    color: var(--text-medium);
    cursor: pointer;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 2.5rem;
    height: 1.25rem;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--text-light);
    transition: var(--transition);
    border-radius: 1.25rem;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 0.875rem;
    width: 0.875rem;
    left: 0.1875rem;
    bottom: 0.1875rem;
    background-color: white;
    transition: var(--transition);
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: var(--primary-color);
}

input:focus + .toggle-slider {
    box-shadow: 0 0 1px var(--primary-color);
}

input:checked + .toggle-slider:before {
    transform: translateX(1.25rem);
}

/* QR Code Preview */
.qr-preview-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
    background-color: var(--bg-light);
    border-radius: var(--radius-md);
    padding: 1.5rem;
}

/* Loading Spinner */
.loading-spinner {
    width: 2.5rem;
    height: 2.5rem;
    border: 0.25rem solid var(--bg-medium);
    border-top: 0.25rem solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* QR Code Animation Effects */
.qr-animation-fade {
    animation: fadeIn 1s ease;
}

.qr-animation-pulse {
    animation: pulse 2s infinite;
}

.qr-animation-scan {
    position: relative;
    overflow: hidden;
}

.qr-animation-scan::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 4px;
    background-color: var(--primary-light);
    animation: scan 2s linear infinite;
}

.qr-animation-glow {
    animation: glow 2s infinite alternate;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes scan {
    0% {
        top: 0;
        left: -100%;
    }
    100% {
        top: 100%;
        left: 100%;
    }
}

@keyframes glow {
    0% {
        box-shadow: 0 0 5px rgba(37, 99, 235, 0.5);
    }
    100% {
        box-shadow: 0 0 20px rgba(37, 99, 235, 0.8);
    }
}

/* Responsive Styles */
@media (max-width: 768px) {
    .advanced-options-content {
        grid-template-columns: 1fr;
    }
    
    .preview-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .preview-controls {
        width: 100%;
        justify-content: space-between;
    }
}
