{% extends 'base.html' %}
{% load static %}

{% block title %}Campaign: {{ campaign.name }}{% endblock %}

{% block extra_css %}
<!-- Include common ads CSS -->
{% include 'ads/includes/ads_common_css.html' %}
<link rel="stylesheet" href="{% static 'ads/css/dashboard_enterprise.css' %}">
<link rel="stylesheet" href="{% static 'ads/css/campaign_management.css' %}">
<!-- Chart.js -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.css">
<style>
    /* Additional styles specific to this page */
</style>
{% endblock %}

{% block content %}
<!-- Campaign Detail Container -->
<div class="enterprise-dashboard campaign-dashboard">
    <!-- Context-aware Header -->
    <div class="dashboard-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="dashboard-welcome">
                        <h1 class="welcome-title">{{ campaign.name }}</h1>
                        <p class="welcome-subtitle">Campaign Details and Performance</p>
                        <a href="{% url 'ads:campaign_list' %}" class="btn btn-sm btn-outline-light mt-2">
                            <i class="fas fa-arrow-left me-1"></i> Back to Campaigns
                        </a>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="dashboard-actions">
                        <a href="{% url 'ads:campaign_edit' campaign.slug %}" class="btn btn-light me-2">
                            <i class="fas fa-edit me-1"></i> Edit Campaign
                        </a>
                        <div class="dropdown d-inline-block">
                            <button class="btn btn-light dropdown-toggle" type="button" id="campaignActionsDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-cog me-1"></i> Actions
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="campaignActionsDropdown">
                                <li>
                                    <a class="dropdown-item" href="{% url 'ads:ad_create_consolidated' %}?campaign={{ campaign.id }}">
                                        <i class="fas fa-plus me-2"></i> Add New Ad
                                    </a>
                                </li>
                                {% if campaign.status == 'active' %}
                                <li>
                                    <a class="dropdown-item" href="{% url 'ads:campaign_pause' campaign.slug %}">
                                        <i class="fas fa-pause me-2"></i> Pause Campaign
                                    </a>
                                </li>
                                {% elif campaign.status == 'paused' %}
                                <li>
                                    <a class="dropdown-item" href="{% url 'ads:campaign_activate' campaign.slug %}">
                                        <i class="fas fa-play me-2"></i> Activate Campaign
                                    </a>
                                </li>
                                {% elif campaign.status == 'draft' %}
                                <li>
                                    <a class="dropdown-item" href="{% url 'ads:campaign_activate' campaign.slug %}">
                                        <i class="fas fa-play me-2"></i> Launch Campaign
                                    </a>
                                </li>
                                {% endif %}
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <button class="dropdown-item text-danger" data-bs-toggle="modal" data-bs-target="#deleteCampaignModal">
                                        <i class="fas fa-trash me-2"></i> Delete Campaign
                                    </button>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="dashboard-content">
        <div class="container-fluid">
            <div class="row">
                <!-- Sidebar Navigation -->
                <div class="col-lg-2 dashboard-sidebar">
                    <div class="sidebar-container">
                        <div class="sidebar-header">
                            <h2 class="sidebar-title">Campaign</h2>
                        </div>
                        <nav class="sidebar-nav">
                            <ul class="nav-list">
                                <li class="nav-item active">
                                    <a href="#overview" class="nav-link" data-bs-toggle="tab">
                                        <i class="fas fa-home"></i>
                                        <span>Overview</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="#ads" class="nav-link" data-bs-toggle="tab">
                                        <i class="fas fa-ad"></i>
                                        <span>Campaign Ads</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="#performance" class="nav-link" data-bs-toggle="tab">
                                        <i class="fas fa-chart-line"></i>
                                        <span>Performance</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="#settings" class="nav-link" data-bs-toggle="tab">
                                        <i class="fas fa-cog"></i>
                                        <span>Settings</span>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>

                <!-- Main Content Area -->
                <div class="col-lg-10 dashboard-main">
                    <div class="tab-content">
                        <!-- Overview Tab -->
                        <div class="tab-pane fade show active" id="overview">
                            <!-- Campaign Stats -->
                            <div class="dashboard-section">
                                <div class="row stats-row">
                                    <div class="col-md-3 col-sm-6">
                                        <div class="stat-card">
                                            <div class="stat-icon">
                                                <i class="fas fa-ad"></i>
                                            </div>
                                            <div class="stat-value">{{ campaign.ads_count }}</div>
                                            <div class="stat-label">Total Ads</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="stat-card active">
                                            <div class="stat-icon">
                                                <i class="fas fa-play-circle"></i>
                                            </div>
                                            <div class="stat-value">{{ campaign.active_ads_count }}</div>
                                            <div class="stat-label">Active Ads</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="stat-card impressions">
                                            <div class="stat-icon">
                                                <i class="fas fa-eye"></i>
                                            </div>
                                            <div class="stat-value">{{ campaign.total_impressions }}</div>
                                            <div class="stat-label">Total Impressions</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="stat-card clicks">
                                            <div class="stat-icon">
                                                <i class="fas fa-mouse-pointer"></i>
                                            </div>
                                            <div class="stat-value">{{ campaign.total_clicks }}</div>
                                            <div class="stat-label">Total Clicks</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Campaign Details -->
                            <div class="dashboard-section">
                                <div class="dashboard-card">
                                    <div class="card-header">
                                        <h3 class="card-title">Campaign Details</h3>
                                        <div class="card-actions">
                                            <span class="badge bg-{{ campaign.status }}">{{ campaign.get_status_display }}</span>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="detail-group">
                                                    <label class="detail-label">Campaign Name</label>
                                                    <div class="detail-value">{{ campaign.name }}</div>
                                                </div>
                                                <div class="detail-group">
                                                    <label class="detail-label">Start Date</label>
                                                    <div class="detail-value">{{ campaign.start_date|date:"F j, Y, g:i a" }}</div>
                                                </div>
                                                <div class="detail-group">
                                                    <label class="detail-label">End Date</label>
                                                    <div class="detail-value">{{ campaign.end_date|date:"F j, Y, g:i a" }}</div>
                                                </div>
                                                <div class="detail-group">
                                                    <label class="detail-label">Budget</label>
                                                    <div class="detail-value">${{ campaign.budget }}</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="detail-group">
                                                    <label class="detail-label">Target Audience</label>
                                                    <div class="detail-value">{{ campaign.target_audience|default:"Not specified" }}</div>
                                                </div>
                                                <div class="detail-group">
                                                    <label class="detail-label">Target Location</label>
                                                    <div class="detail-value">{{ campaign.target_location|default:"Not specified" }}</div>
                                                </div>
                                                <div class="detail-group">
                                                    <label class="detail-label">Created</label>
                                                    <div class="detail-value">{{ campaign.created_at|date:"F j, Y" }}</div>
                                                </div>
                                                <div class="detail-group">
                                                    <label class="detail-label">Last Updated</label>
                                                    <div class="detail-value">{{ campaign.updated_at|date:"F j, Y" }}</div>
                                                </div>
                                            </div>
                                        </div>
                                        {% if campaign.description %}
                                        <div class="detail-group mt-3">
                                            <label class="detail-label">Description</label>
                                            <div class="detail-value">{{ campaign.description }}</div>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Performance Overview -->
                            <div class="dashboard-section">
                                <div class="dashboard-card">
                                    <div class="card-header">
                                        <h3 class="card-title">Performance Overview</h3>
                                    </div>
                                    <div class="card-body">
                                        <div class="chart-container">
                                            <canvas id="campaignPerformanceChart" height="300"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Ads Tab -->
                        <div class="tab-pane fade" id="ads">
                            <div class="dashboard-section">
                                <div class="dashboard-card">
                                    <div class="card-header">
                                        <h3 class="card-title">Campaign Ads</h3>
                                        <div class="card-actions">
                                            <a href="{% url 'ads:ad_create_consolidated' %}?campaign={{ campaign.id }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-plus me-1"></i> Add New Ad
                                            </a>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        {% if campaign.ads.all %}
                                        <div class="table-responsive">
                                            <table class="table table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>Ad Title</th>
                                                        <th>Type</th>
                                                        <th>Status</th>
                                                        <th>Impressions</th>
                                                        <th>Clicks</th>
                                                        <th>CTR</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {% for ad in campaign.ads.all %}
                                                    <tr>
                                                        <td>{{ ad.title }}</td>
                                                        <td>{{ ad.ad_type.name }}</td>
                                                        <td><span class="badge bg-{{ ad.status }}">{{ ad.get_status_display }}</span></td>
                                                        <td>{{ ad.impressions }}</td>
                                                        <td>{{ ad.clicks }}</td>
                                                        <td>{{ ad.ctr|floatformat:2 }}%</td>
                                                        <td>
                                                            <div class="btn-group">
                                                                <a href="{% url 'ads:ad_detail' ad.slug %}" class="btn btn-sm btn-outline-primary">
                                                                    <i class="fas fa-eye"></i>
                                                                </a>
                                                                <a href="{% url 'ads:ad_edit' ad.slug %}" class="btn btn-sm btn-outline-secondary">
                                                                    <i class="fas fa-edit"></i>
                                                                </a>
                                                                <a href="{% url 'ads:ad_analytics' ad.slug %}" class="btn btn-sm btn-outline-info">
                                                                    <i class="fas fa-chart-bar"></i>
                                                                </a>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    {% endfor %}
                                                </tbody>
                                            </table>
                                        </div>
                                        {% else %}
                                        <div class="empty-state">
                                            <div class="empty-state-icon">
                                                <i class="fas fa-ad"></i>
                                            </div>
                                            <h3 class="empty-state-title">No ads in this campaign</h3>
                                            <p class="empty-state-description">Add ads to this campaign to start promoting your products or services.</p>
                                            <a href="{% url 'ads:ad_create' %}?campaign={{ campaign.id }}" class="btn btn-primary">
                                                <i class="fas fa-plus me-2"></i> Add New Ad
                                            </a>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Performance Tab -->
                        <div class="tab-pane fade" id="performance">
                            <!-- Performance metrics will be added in the next phase -->
                            <div class="dashboard-section">
                                <div class="dashboard-card">
                                    <div class="card-header">
                                        <h3 class="card-title">Detailed Performance</h3>
                                    </div>
                                    <div class="card-body">
                                        <p class="text-center">Detailed performance metrics will be available in the next phase.</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Settings Tab -->
                        <div class="tab-pane fade" id="settings">
                            <div class="dashboard-section">
                                <div class="dashboard-card">
                                    <div class="card-header">
                                        <h3 class="card-title">Campaign Settings</h3>
                                    </div>
                                    <div class="card-body">
                                        <a href="{% url 'ads:campaign_edit' campaign.slug %}" class="btn btn-primary">
                                            <i class="fas fa-edit me-2"></i> Edit Campaign
                                        </a>
                                        <button class="btn btn-danger ms-2" data-bs-toggle="modal" data-bs-target="#deleteCampaignModal">
                                            <i class="fas fa-trash me-2"></i> Delete Campaign
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Campaign Modal -->
<div class="modal fade" id="deleteCampaignModal" tabindex="-1" aria-labelledby="deleteCampaignModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteCampaignModalLabel">Confirm Deletion</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the campaign "{{ campaign.name }}"?</p>
                <p class="text-danger">This action cannot be undone and will remove all campaign data.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form action="{% url 'ads:campaign_delete' campaign.slug %}" method="post">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">Delete Campaign</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
<!-- Enterprise Dashboard JS -->
<script src="{% static 'ads/js/dashboard_enterprise.js' %}"></script>
<!-- Campaign Management JS -->
<script src="{% static 'ads/js/campaign_management.js' %}"></script>

<!-- Campaign data for JavaScript -->
<div id="campaign-analytics-data" style="display: none;" data-campaign-id="{{ campaign.id }}">
    {% if campaign_analytics %}
    {{ campaign_analytics_json|safe }}
    {% else %}
    {}
    {% endif %}
</div>
{% endblock %}
