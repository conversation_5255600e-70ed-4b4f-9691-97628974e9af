/**
 * Generate Suggestions Fix
 * This script fixes issues with the Generate Suggestions button
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get elements
    const useSmartEngine = document.getElementById('useSmartEngine');
    const smartEngineOptions = document.getElementById('smartEngineOptions');
    const aiSuggestionsContainer = document.getElementById('aiSuggestionsContainer');
    const generateSuggestions = document.getElementById('generateSuggestions');
    const backupGenerateSuggestions = document.getElementById('backupGenerateSuggestions');

    // If elements don't exist, exit early
    if (!useSmartEngine || !generateSuggestions) return;

    // Store the original click handler
    const originalClickHandler = generateSuggestions.onclick;

    // Remove the original click handler
    generateSuggestions.onclick = null;

    // Function to trigger suggestion generation
    function triggerGenerateSuggestions() {
        // Dispatch a custom event that smart-engine.js can listen for
        const customEvent = new CustomEvent('generateSuggestions', {
            bubbles: true,
            detail: { source: 'generate-suggestions-fix.js' }
        });
        generateSuggestions.dispatchEvent(customEvent);
    }

    // Make the function globally available
    window.triggerGenerateSuggestions = triggerGenerateSuggestions;

    // Add our own click handler
    generateSuggestions.addEventListener('click', function(e) {
        console.log('Generate Suggestions button clicked');

        // Simply trigger the function
        triggerGenerateSuggestions();
    });

    // Make sure the button is visible when Smart Engine is toggled on
    useSmartEngine.addEventListener('change', function() {
        if (this.checked) {
            // Show the main button
            generateSuggestions.style.display = 'block';

            // Show the backup button
            if (backupGenerateSuggestions) {
                backupGenerateSuggestions.style.display = 'block';
            }

            // Show the options
            if (smartEngineOptions) {
                smartEngineOptions.style.display = 'block';
            }
        } else {
            // Hide the main button
            generateSuggestions.style.display = 'none';

            // Hide the backup button
            if (backupGenerateSuggestions) {
                backupGenerateSuggestions.style.display = 'none';
            }

            // Hide the options
            if (smartEngineOptions) {
                smartEngineOptions.style.display = 'none';
            }

            // Hide the suggestions container
            if (aiSuggestionsContainer) {
                aiSuggestionsContainer.style.display = 'none';
            }
        }
    });

    // Initial state - hide buttons if Smart Engine is not checked
    if (!useSmartEngine.checked) {
        // Hide the main button
        generateSuggestions.style.display = 'none';

        // Hide the backup button
        if (backupGenerateSuggestions) {
            backupGenerateSuggestions.style.display = 'none';
        }

        // Hide the options
        if (smartEngineOptions) {
            smartEngineOptions.style.display = 'none';
        }
    }
});
