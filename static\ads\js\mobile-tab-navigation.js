/**
 * Mobile Tab Navigation
 * Handles synchronization between desktop and mobile tabs
 * and provides mobile-specific navigation enhancements
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get tab elements
    const mobileTabButtons = document.querySelectorAll('[id$="-tab-mobile"]');
    const desktopTabButtons = document.querySelectorAll('[id$="-tab"]:not([id$="-tab-mobile"])');
    const mobileStepTitle = document.getElementById('mobileStepTitle');

    // Sync desktop and mobile tabs
    desktopTabButtons.forEach(button => {
        button.addEventListener('shown.bs.tab', function(e) {
            const mobileTabId = e.target.id.replace('-tab', '-tab-mobile');
            const mobileTab = document.getElementById(mobileTabId);
            if (mobileTab && !mobileTab.classList.contains('active')) {
                mobileTab.click();
            }
        });
    });

    mobileTabButtons.forEach(button => {
        button.addEventListener('shown.bs.tab', function(e) {
            const desktopTabId = e.target.id.replace('-tab-mobile', '-tab');
            const desktopTab = document.getElementById(desktopTabId);
            if (desktopTab && !desktopTab.classList.contains('active')) {
                desktopTab.click();
            }
        });
    });

    // Update mobile step title when changing steps
    const nextButtons = document.querySelectorAll('.next-step');
    const prevButtons = document.querySelectorAll('.prev-step');

    nextButtons.forEach(button => {
        button.addEventListener('click', function() {
            if (mobileStepTitle && this.dataset.stepTitle) {
                mobileStepTitle.textContent = this.dataset.stepTitle;
            }

            // For mobile navigation
            if (window.innerWidth < 768 && this.dataset.mobileNext) {
                const mobileTab = document.getElementById(this.dataset.mobileNext);
                if (mobileTab) {
                    mobileTab.click();
                }
            } else if (this.dataset.next) {
                const desktopTab = document.getElementById(this.dataset.next);
                if (desktopTab) {
                    desktopTab.click();
                }
            }
        });
    });

    prevButtons.forEach(button => {
        button.addEventListener('click', function() {
            if (mobileStepTitle && this.dataset.stepTitle) {
                mobileStepTitle.textContent = this.dataset.stepTitle;
            }

            // For mobile navigation
            if (window.innerWidth < 768 && this.dataset.mobilePrev) {
                const mobileTab = document.getElementById(this.dataset.mobilePrev);
                if (mobileTab) {
                    mobileTab.click();
                }
            } else if (this.dataset.prev) {
                const desktopTab = document.getElementById(this.dataset.prev);
                if (desktopTab) {
                    desktopTab.click();
                }
            }
        });
    });

    // Auto-collapse preview on mobile when changing tabs
    const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');
    const previewCollapse = document.getElementById('previewCollapse');
    const tipsCollapse = document.getElementById('tipsCollapse');

    if (window.innerWidth < 768) {
        // Initially collapse on mobile
        if (previewCollapse) {
            previewCollapse.classList.remove('show');
        }
        if (tipsCollapse) {
            tipsCollapse.classList.remove('show');
        }

        // Collapse when changing tabs
        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                if (previewCollapse && previewCollapse.classList.contains('show')) {
                    previewCollapse.classList.remove('show');
                }
                if (tipsCollapse && tipsCollapse.classList.contains('show')) {
                    tipsCollapse.classList.remove('show');
                }
            });
        });
    }
});
