"""
MODULE 6: Management command to create Stripe products and prices for existing plans
"""

import stripe
from django.core.management.base import BaseCommand
from django.conf import settings
from qrcode_app.models import Plan, StripeProduct

# Configure Stripe
stripe.api_key = settings.STRIPE_SECRET_KEY


class Command(BaseCommand):
    help = 'Create Stripe products and prices for existing plans'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be created without actually creating',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force recreation of existing Stripe products',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        force = options['force']
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No Stripe products will be created')
            )
        
        plans = Plan.objects.filter(is_active=True).order_by('sort_order')
        
        for plan in plans:
            self.stdout.write(f'\nProcessing plan: {plan.name}')
            
            # Check if StripeProduct already exists
            try:
                stripe_product = StripeProduct.objects.get(plan=plan)
                if not force:
                    self.stdout.write(
                        self.style.WARNING(f'  Stripe product already exists for {plan.name}. Use --force to recreate.')
                    )
                    continue
                else:
                    self.stdout.write(f'  Forcing recreation of Stripe product for {plan.name}')
                    if not dry_run:
                        stripe_product.delete()
            except StripeProduct.DoesNotExist:
                pass
            
            if dry_run:
                self.stdout.write(f'  Would create Stripe product for {plan.name}')
                self.stdout.write(f'    Monthly price: ${plan.price}')
                self.stdout.write(f'    Yearly price: ${plan.yearly_price}')
                continue
            
            try:
                # Create Stripe product
                stripe_product_data = {
                    'name': f'QR Generator - {plan.name}',
                    'description': plan.description,
                    'metadata': {
                        'plan_id': str(plan.id),
                        'plan_type': plan.plan_type,
                    }
                }
                
                stripe_product = stripe.Product.create(**stripe_product_data)
                self.stdout.write(
                    self.style.SUCCESS(f'  ✅ Created Stripe product: {stripe_product.id}')
                )
                
                # Create monthly price (if not free)
                monthly_price_id = None
                if plan.price > 0:
                    monthly_price = stripe.Price.create(
                        product=stripe_product.id,
                        unit_amount=int(plan.price * 100),  # Convert to cents
                        currency='usd',
                        recurring={'interval': 'month'},
                        metadata={
                            'plan_id': str(plan.id),
                            'billing_cycle': 'monthly',
                        }
                    )
                    monthly_price_id = monthly_price.id
                    self.stdout.write(
                        self.style.SUCCESS(f'  ✅ Created monthly price: {monthly_price_id}')
                    )
                
                # Create yearly price (if not free and yearly price is set)
                yearly_price_id = None
                if plan.yearly_price > 0:
                    yearly_price = stripe.Price.create(
                        product=stripe_product.id,
                        unit_amount=int(plan.yearly_price * 100),  # Convert to cents
                        currency='usd',
                        recurring={'interval': 'year'},
                        metadata={
                            'plan_id': str(plan.id),
                            'billing_cycle': 'yearly',
                        }
                    )
                    yearly_price_id = yearly_price.id
                    self.stdout.write(
                        self.style.SUCCESS(f'  ✅ Created yearly price: {yearly_price_id}')
                    )
                
                # Create StripeProduct record
                stripe_product_record = StripeProduct.objects.create(
                    plan=plan,
                    stripe_product_id=stripe_product.id,
                    stripe_price_id=monthly_price_id or '',
                    stripe_yearly_price_id=yearly_price_id or '',
                    is_active=True
                )
                
                self.stdout.write(
                    self.style.SUCCESS(f'  ✅ Created StripeProduct record for {plan.name}')
                )
                
            except stripe.error.StripeError as e:
                self.stdout.write(
                    self.style.ERROR(f'  ❌ Stripe error for {plan.name}: {e}')
                )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'  ❌ Error creating Stripe product for {plan.name}: {e}')
                )
        
        if not dry_run:
            self.stdout.write(
                self.style.SUCCESS('\n🎉 Stripe products setup complete!')
            )
            self.stdout.write(
                'Next steps:'
            )
            self.stdout.write(
                '1. Update your Stripe webhook endpoint URL in Stripe Dashboard'
            )
            self.stdout.write(
                '2. Configure webhook events: checkout.session.completed, customer.subscription.updated, customer.subscription.deleted'
            )
            self.stdout.write(
                '3. Test the billing flow with Stripe test cards'
            )
        else:
            self.stdout.write(
                self.style.SUCCESS('\nDry run complete. Use --force to recreate existing products.')
            )
