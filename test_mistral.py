"""
Test script for Mistral AI client
"""
import os
import sys
import django
import logging

# Configure logging
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'qrcode_project.settings')
django.setup()

# Import the Mistral client
from ai_services.clients import MistralAIClient

def test_mistral():
    """Test the Mistral AI client"""
    print("Testing Mistral AI client...")

    try:
        # Create a Mistral AI client
        client = MistralAIClient()
        print(f"Client type: {type(client).__name__}")

        # Generate suggestions
        suggestions = client.generate_ad_suggestions(
            language="english",
            business_type="mobile app",
            target_audience="young professionals",
            tone="professional",
            num_suggestions=3,
            ad_title="Mobile App Development"
        )

        # Print the suggestions
        print("\nGenerated suggestions:")
        for i, suggestion in enumerate(suggestions):
            print(f"\nSuggestion {i+1}:")
            print(f"Title: {suggestion.get('title', 'No title')}")
            print(f"Content: {suggestion.get('content', 'No content')}")

        return True
    except Exception as e:
        print(f"\nError: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_mistral()
    if success:
        print("\nTest completed successfully!")
    else:
        print("\nTest failed!")
        sys.exit(1)
