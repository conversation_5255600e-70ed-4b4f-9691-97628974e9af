from django.contrib import admin
from .models import AdType, AdLocation, Ad, Transaction, AdAnalytics

# Import custom admin site
from qrcode_project.admin_sites import ads_admin_site

# Register with default admin
@admin.register(AdType)
class AdTypeAdmin(admin.ModelAdmin):
    list_display = ('name', 'base_price', 'is_active', 'created_at')
    list_filter = ('is_active', 'is_premium')
    search_fields = ('name', 'description')

@admin.register(AdLocation)
class AdLocationAdmin(admin.ModelAdmin):
    list_display = ('name', 'visibility', 'price_multiplier', 'daily_impressions', 'is_premium', 'is_active')
    list_filter = ('visibility', 'is_premium', 'is_active')
    search_fields = ('name', 'description')
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'description', 'image')
        }),
        ('Pricing & Visibility', {
            'fields': ('price_multiplier', 'visibility', 'is_premium', 'daily_impressions')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
    )

@admin.register(Ad)
class AdAdmin(admin.ModelAdmin):
    list_display = ('title', 'user', 'ad_type', 'ad_location', 'status', 'start_date', 'end_date', 'impressions', 'clicks')
    list_filter = ('status', 'ad_type', 'ad_location', 'start_date', 'end_date')
    search_fields = ('title', 'content', 'user__username', 'user__email')
    readonly_fields = ('impressions', 'clicks', 'created_at', 'updated_at', 'slug', 'final_pricing')
    fieldsets = (
        ('Basic Information', {
            'fields': ('user', 'ad_type', 'title', 'content', 'media', 'slug')
        }),
        ('Placement & Targeting', {
            'fields': ('ad_location', 'target_location', 'target_audience')
        }),
        ('Campaign Schedule', {
            'fields': ('start_date', 'end_date')
        }),
        ('Call to Action', {
            'fields': ('cta_link',)
        }),
        ('Options & Pricing', {
            'fields': ('requires_ai', 'wants_social', 'base_pricing', 'final_pricing')
        }),
        ('Status', {
            'fields': ('status', 'rejection_reason')
        }),
        ('Statistics', {
            'fields': ('impressions', 'clicks', 'created_at', 'updated_at')
        }),
    )

@admin.register(Transaction)
class TransactionAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'ad', 'amount', 'status', 'payment_gateway', 'timestamp')
    list_filter = ('status', 'payment_gateway', 'timestamp')
    search_fields = ('user__username', 'user__email', 'ad__title', 'transaction_id')
    readonly_fields = ('timestamp', 'updated_at')

@admin.register(AdAnalytics)
class AdAnalyticsAdmin(admin.ModelAdmin):
    list_display = ('ad', 'date', 'impressions', 'clicks', 'unique_views', 'conversion_count')
    list_filter = ('date',)
    search_fields = ('ad__title',)
    readonly_fields = ('created_at',)

# Also register with custom ads admin site
ads_admin_site.register(AdType, AdTypeAdmin)
ads_admin_site.register(AdLocation, AdLocationAdmin)
ads_admin_site.register(Ad, AdAdmin)
ads_admin_site.register(Transaction, TransactionAdmin)
ads_admin_site.register(AdAnalytics, AdAnalyticsAdmin)
