"""
MODULE 4: <PERSON> Scan Alerts - Utilities for processing scan events and triggering alerts
"""
import logging
import requests
from django.core.mail import send_mail
from django.conf import settings
from django.utils import timezone
from django.db import models
from .models import ScanEvent, Scan<PERSON>lert, WebhookEndpoint
from .utils import get_geolocation_from_ip, get_client_ip, parse_device_info

# Configure logger
logger = logging.getLogger('scan_alerts')

def log_scan_event(request, qr_code):
    """
    Log a scan event with IPinfo data and trigger any matching alerts

    Args:
        request: Django request object
        qr_code: QRCode instance that was scanned

    Returns:
        ScanEvent instance
    """
    try:
        # Get client information
        ip_address = get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')

        # Get geolocation data
        geo_data = get_geolocation_from_ip(ip_address)

        # Parse device information
        device_info = parse_device_info(user_agent)

        # Create location string
        location_parts = []
        if geo_data.get('city'):
            location_parts.append(geo_data['city'])
        if geo_data.get('country'):
            location_parts.append(geo_data['country'])
        location = ', '.join(location_parts) if location_parts else 'Unknown'

        # Create scan event
        scan_event = ScanEvent.objects.create(
            qr_code=qr_code,
            ip=ip_address,
            location=location,
            org=geo_data.get('organization', ''),
            user_agent=user_agent,
            device_type=device_info.get('device_type', ''),
            browser=device_info.get('browser', ''),
            country_code=geo_data.get('country', '')[:2] if geo_data.get('country') else ''
        )

        # MODULE 6: Check scan limits before processing
        if not check_scan_limits(qr_code.user):
            logger.warning(f"Scan limit exceeded for user {qr_code.user.username}")
            return None

        # Process alerts for this scan event
        process_scan_alerts(scan_event)

        # MODULE 5: Fire webhooks for this scan event
        fire_webhooks(qr_code, scan_event)

        # MODULE 6: Increment scan count for user's subscription
        increment_user_scan_count(qr_code.user)

        logger.info(f"Logged scan event for QR {qr_code.id} from {location}")
        return scan_event

    except Exception as e:
        logger.error(f"Error logging scan event: {e}")
        return None


def process_scan_alerts(scan_event):
    """
    Process scan alerts for a given scan event

    Args:
        scan_event: ScanEvent instance to process
    """
    try:
        # Get all active alerts for this user
        user = scan_event.qr_code.user
        alerts = ScanAlert.objects.filter(
            user=user,
            is_active=True
        )

        alerts_triggered = 0

        for alert in alerts:
            # Check if this alert matches the scan event
            if alert.matches_scan_event(scan_event):
                # Check if we can send an alert (daily limit)
                if alert.can_send_alert():
                    # Send the alert
                    if send_scan_alert(alert, scan_event):
                        alert.record_alert_sent()
                        alerts_triggered += 1

                        # Fire alert webhooks
                        fire_alert_webhooks(alert, scan_event)

                        logger.info(f"Alert triggered: {alert.keyword} for QR {scan_event.qr_code.name}")
                else:
                    logger.warning(f"Alert {alert.id} hit daily limit, skipping")

        # Update scan event with alert count
        scan_event.alerts_triggered = alerts_triggered
        scan_event.processed_for_alerts = True
        scan_event.save(update_fields=['alerts_triggered', 'processed_for_alerts'])

    except Exception as e:
        logger.error(f"Error processing scan alerts: {e}")


def send_scan_alert(alert, scan_event):
    """
    Send an email alert for a scan event

    Args:
        alert: ScanAlert instance
        scan_event: ScanEvent instance that triggered the alert

    Returns:
        bool: True if email was sent successfully
    """
    try:
        qr_code = scan_event.qr_code

        # Prepare email content
        subject = f"🚨 QR Scan Alert: '{alert.keyword}' detected"

        message = f"""
QR Scan Alert Triggered!

Alert Details:
- Keyword: {alert.keyword}
- QR Code: {qr_code.name}
- Alert Type: {alert.get_alert_type_display()}

Scan Details:
- Time: {scan_event.timestamp.strftime('%Y-%m-%d %H:%M:%S')}
- Location: {scan_event.location}
- Organization: {scan_event.org or 'Unknown'}
- Device: {scan_event.device_type} - {scan_event.browser}
- IP Address: {scan_event.ip}

QR Code Information:
- Name: {qr_code.name}
- Type: {qr_code.get_qr_type_display()}
- Created: {qr_code.created_at.strftime('%Y-%m-%d')}

This alert has been triggered {alert.triggered_count + 1} times total.

---
QR Generator Pro - Smart Scan Alerts
Manage your alerts: {settings.SITE_URL if hasattr(settings, 'SITE_URL') else 'http://localhost:8000'}/monetization/scan-alerts/
        """.strip()

        # Send email
        send_mail(
            subject=subject,
            message=message,
            from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>'),
            recipient_list=[alert.email],
            fail_silently=False
        )

        logger.info(f"Alert email sent to {alert.email} for keyword '{alert.keyword}'")
        return True

    except Exception as e:
        logger.error(f"Error sending scan alert email: {e}")
        return False


def create_default_alerts_for_user(user):
    """
    Create some default scan alerts for new premium users

    Args:
        user: User instance
    """
    try:
        # Only create if user doesn't have any alerts yet
        if not ScanAlert.objects.filter(user=user).exists():

            # Default alerts for common use cases
            default_alerts = [
                {
                    'keyword': 'Nigeria',
                    'alert_type': ScanAlert.AlertType.LOCATION,
                    'email': user.email,
                },
                {
                    'keyword': 'VPN',
                    'alert_type': ScanAlert.AlertType.ORGANIZATION,
                    'email': user.email,
                },
                {
                    'keyword': 'Mobile',
                    'alert_type': ScanAlert.AlertType.DEVICE,
                    'email': user.email,
                }
            ]

            for alert_data in default_alerts:
                ScanAlert.objects.create(
                    user=user,
                    **alert_data
                )

            logger.info(f"Created {len(default_alerts)} default alerts for user {user.username}")

    except Exception as e:
        logger.error(f"Error creating default alerts: {e}")


def get_alert_statistics(user, days=30):
    """
    Get alert statistics for a user

    Args:
        user: User instance
        days: Number of days to analyze

    Returns:
        dict: Alert statistics
    """
    try:
        from datetime import timedelta

        start_date = timezone.now() - timedelta(days=days)

        # Get user's alerts
        alerts = ScanAlert.objects.filter(user=user)

        # Get scan events in date range
        scan_events = ScanEvent.objects.filter(
            qr_code__user=user,
            timestamp__gte=start_date
        )

        stats = {
            'total_alerts': alerts.count(),
            'active_alerts': alerts.filter(is_active=True).count(),
            'total_triggers': sum(alert.triggered_count for alert in alerts),
            'scan_events_period': scan_events.count(),
            'alerts_triggered_period': scan_events.filter(alerts_triggered__gt=0).count(),
            'top_keywords': list(alerts.order_by('-triggered_count')[:5].values_list('keyword', 'triggered_count')),
            'recent_triggers': list(alerts.filter(last_triggered__isnull=False).order_by('-last_triggered')[:5].values(
                'keyword', 'last_triggered', 'triggered_count'
            ))
        }

        return stats

    except Exception as e:
        logger.error(f"Error getting alert statistics: {e}")
        return {}


def cleanup_old_scan_events(days_to_keep=90):
    """
    Clean up old scan events to maintain database performance

    Args:
        days_to_keep: Number of days of scan events to retain

    Returns:
        int: Number of events deleted
    """
    try:
        from datetime import timedelta

        cutoff_date = timezone.now() - timedelta(days=days_to_keep)

        # Delete old scan events
        deleted_count = ScanEvent.objects.filter(
            timestamp__lt=cutoff_date
        ).delete()[0]

        logger.info(f"Cleaned up {deleted_count} old scan events")
        return deleted_count

    except Exception as e:
        logger.error(f"Error cleaning up scan events: {e}")
        return 0


def fire_webhooks(qr_code, scan_event):
    """
    MODULE 5: Fire webhooks for scan events to external services (Zapier, CRM, etc.)

    Args:
        qr_code: QRCode instance that was scanned
        scan_event: ScanEvent instance with scan details
    """
    try:
        # Get active webhooks for this user
        webhooks = WebhookEndpoint.objects.filter(
            user=qr_code.user,
            active=True,
            trigger_on_scan=True
        )

        webhooks_fired = 0

        for webhook in webhooks:
            # Check if webhook is for specific QR code
            if webhook.qr_code and webhook.qr_code != qr_code:
                continue

            # Prepare Zapier-compatible webhook payload
            payload = {
                "qr_code": getattr(qr_code, 'short_code', qr_code.unique_id),
                "qr_name": qr_code.name,
                "qr_type": qr_code.qr_type,
                "qr_data": qr_code.data,
                "ip": scan_event.ip,
                "location": scan_event.location,
                "org": scan_event.org,
                "device_type": scan_event.device_type,
                "browser": scan_event.browser,
                "country_code": scan_event.country_code,
                "timestamp": scan_event.timestamp.isoformat(),
                "user_agent": scan_event.user_agent,
                "user_id": qr_code.user.id,
                "username": qr_code.user.username,
                "user_email": qr_code.user.email
            }

            # Add secret key to headers if provided
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'QR-Generator-Webhook/1.0'
            }

            if webhook.secret_key:
                headers['X-Webhook-Secret'] = webhook.secret_key

            # Fire the webhook
            try:
                response = requests.post(
                    webhook.url,
                    json=payload,
                    headers=headers,
                    timeout=10  # 10 second timeout
                )

                # Check if webhook was successful
                if response.status_code in [200, 201, 202, 204]:
                    webhook.record_call_success()
                    webhooks_fired += 1
                    logger.info(f"Webhook fired successfully to {webhook.url[:50]}...")
                else:
                    error_msg = f"HTTP {response.status_code}: {response.text[:200]}"
                    webhook.record_call_failure(error_msg)
                    logger.warning(f"Webhook failed with status {response.status_code}: {webhook.url[:50]}...")

            except requests.exceptions.Timeout:
                error_msg = "Request timeout after 10 seconds"
                webhook.record_call_failure(error_msg)
                logger.warning(f"Webhook timeout: {webhook.url[:50]}...")

            except requests.exceptions.ConnectionError:
                error_msg = "Connection error - unable to reach webhook URL"
                webhook.record_call_failure(error_msg)
                logger.warning(f"Webhook connection error: {webhook.url[:50]}...")

            except Exception as e:
                error_msg = f"Unexpected error: {str(e)}"
                webhook.record_call_failure(error_msg)
                logger.error(f"Webhook error for {webhook.url[:50]}...: {e}")

        if webhooks_fired > 0:
            logger.info(f"Fired {webhooks_fired} webhooks for QR {qr_code.name}")

    except Exception as e:
        logger.error(f"Error firing webhooks: {e}")


def fire_alert_webhooks(alert, scan_event):
    """
    MODULE 5: Fire webhooks when scan alerts are triggered

    Args:
        alert: ScanAlert instance that was triggered
        scan_event: ScanEvent instance that triggered the alert
    """
    try:
        # Get webhooks that trigger on alerts
        webhooks = WebhookEndpoint.objects.filter(
            user=alert.user,
            active=True,
            trigger_on_alert=True
        )

        # Filter by QR code if webhook is specific
        if alert.qr_code:
            webhooks = webhooks.filter(
                models.Q(qr_code=alert.qr_code) | models.Q(qr_code__isnull=True)
            )

        for webhook in webhooks:
            # Prepare alert webhook payload
            payload = {
                "event_type": "scan_alert",
                "timestamp": timezone.now().isoformat(),
                "alert": {
                    "id": alert.id,
                    "keyword": alert.keyword,
                    "alert_type": alert.alert_type,
                    "triggered_count": alert.triggered_count,
                    "email": alert.email
                },
                "qr_code": {
                    "id": scan_event.qr_code.id,
                    "name": scan_event.qr_code.name,
                    "code": getattr(scan_event.qr_code, 'short_code', scan_event.qr_code.unique_id)
                },
                "scan_details": {
                    "ip": scan_event.ip,
                    "location": scan_event.location,
                    "organization": scan_event.org,
                    "device_type": scan_event.device_type,
                    "browser": scan_event.browser,
                    "country_code": scan_event.country_code
                }
            }

            # Fire the webhook (similar to scan webhook)
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'QR-Generator-Alert-Webhook/1.0'
            }

            if webhook.secret_key:
                headers['X-Webhook-Secret'] = webhook.secret_key

            try:
                response = requests.post(
                    webhook.url,
                    json=payload,
                    headers=headers,
                    timeout=10
                )

                if response.status_code in [200, 201, 202, 204]:
                    webhook.record_call_success()
                    logger.info(f"Alert webhook fired successfully to {webhook.url[:50]}...")
                else:
                    error_msg = f"HTTP {response.status_code}: {response.text[:200]}"
                    webhook.record_call_failure(error_msg)

            except Exception as e:
                webhook.record_call_failure(str(e))
                logger.error(f"Alert webhook error: {e}")

    except Exception as e:
        logger.error(f"Error firing alert webhooks: {e}")


def check_scan_limits(user):
    """
    MODULE 6: Check if user can perform more scans this month

    Args:
        user: User instance

    Returns:
        bool: True if user can scan, False if limit exceeded
    """
    try:
        from .models import Subscription, Plan

        # Get user's subscription
        try:
            subscription = Subscription.objects.get(user=user)
        except Subscription.DoesNotExist:
            # Create default subscription if none exists
            default_plan = Plan.objects.filter(is_default=True).first()
            if default_plan:
                subscription = Subscription.objects.create(user=user, plan=default_plan)
            else:
                logger.warning(f"No default plan found for user {user.username}")
                return False

        if not subscription.plan:
            logger.warning(f"User {user.username} has no subscription plan")
            return False

        # Check if user can scan this month
        return subscription.can_scan_this_month()

    except Exception as e:
        logger.error(f"Error checking scan limits for user {user.username}: {e}")
        return True  # Allow scan on error to avoid blocking legitimate users


def increment_user_scan_count(user):
    """
    MODULE 6: Increment user's monthly scan count

    Args:
        user: User instance
    """
    try:
        from .models import Subscription

        # Get user's subscription
        try:
            subscription = Subscription.objects.get(user=user)
            subscription.increment_scan_count()
            logger.debug(f"Incremented scan count for user {user.username}: {subscription.scans_this_month}")
        except Subscription.DoesNotExist:
            logger.warning(f"No subscription found for user {user.username} when incrementing scan count")

    except Exception as e:
        logger.error(f"Error incrementing scan count for user {user.username}: {e}")


def get_user_usage_summary(user):
    """
    MODULE 6: Get comprehensive usage summary for a user

    Args:
        user: User instance

    Returns:
        dict: Usage summary with current usage and limits
    """
    try:
        from .models import Subscription

        try:
            subscription = Subscription.objects.get(user=user)
            return subscription.get_usage_summary()
        except Subscription.DoesNotExist:
            return {
                'error': 'No subscription found',
                'qr_codes': {'current': 0, 'limit': 0, 'percentage': 0},
                'scans': {'current': 0, 'limit': 0, 'percentage': 0}
            }

    except Exception as e:
        logger.error(f"Error getting usage summary for user {user.username}: {e}")
        return {'error': str(e)}


def reset_monthly_scans_for_all_users():
    """
    MODULE 6: Reset monthly scan counts for all users (for cron job)

    This function should be called monthly to reset scan counts
    """
    try:
        from .models import Subscription

        subscriptions = Subscription.objects.all()
        reset_count = 0

        for subscription in subscriptions:
            subscription.reset_monthly_scans()
            reset_count += 1

        logger.info(f"Reset monthly scans for {reset_count} subscriptions")
        return reset_count

    except Exception as e:
        logger.error(f"Error resetting monthly scans: {e}")
        return 0


def check_feature_access(user, feature_name):
    """
    MODULE 6: Check if user has access to a specific feature

    Args:
        user: User instance
        feature_name: Feature name (ai, webhooks, alerts, etc.)

    Returns:
        bool: True if user has access, False otherwise
    """
    try:
        from .models import Subscription

        try:
            subscription = Subscription.objects.get(user=user)
            if not subscription.plan:
                return False
            return subscription.plan.is_feature_enabled(feature_name)
        except Subscription.DoesNotExist:
            return False

    except Exception as e:
        logger.error(f"Error checking feature access for user {user.username}: {e}")
        return False


def enforce_creation_limits(user, resource_type):
    """
    MODULE 6: Check if user can create more resources of a specific type

    Args:
        user: User instance
        resource_type: Type of resource (qr_code, webhook, ai_page, etc.)

    Returns:
        bool: True if user can create more, False if limit exceeded
    """
    try:
        from .models import Subscription

        try:
            subscription = Subscription.objects.get(user=user)
            if not subscription.plan:
                return False

            # Map resource types to limit check methods
            limit_checks = {
                'qr_code': subscription.can_create_qr_code,
                'webhook': subscription.can_create_webhook,
                'ai_page': subscription.can_create_ai_page,
                'scan_alert': subscription.can_create_scan_alert,
                'dynamic_redirect': subscription.can_create_dynamic_redirect
            }

            limit_check = limit_checks.get(resource_type)
            if limit_check:
                return limit_check()
            else:
                logger.warning(f"Unknown resource type: {resource_type}")
                return True  # Allow creation for unknown types

        except Subscription.DoesNotExist:
            return False

    except Exception as e:
        logger.error(f"Error checking creation limits for user {user.username}: {e}")
        return True  # Allow creation on error
