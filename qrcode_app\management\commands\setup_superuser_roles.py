from django.core.management.base import BaseCommand
from django.contrib.auth.models import User, Group, Permission
from django.contrib.contenttypes.models import ContentType
from qrcode_app.models import UserProfile, QRCode

class Command(BaseCommand):
    help = 'Set up superuser with both administrator and superuser roles'

    def handle(self, *args, **options):
        # Find all superusers
        superusers = User.objects.filter(is_superuser=True)
        
        if not superusers.exists():
            self.stdout.write(self.style.WARNING('No superusers found in the system'))
            return
            
        # Get or create the necessary groups
        admin_group, _ = Group.objects.get_or_create(name='Administrators')
        superadmin_group, _ = Group.objects.get_or_create(name='SuperAdministrators')
        
        # Get the performance dashboard permission
        try:
            content_type = ContentType.objects.get_for_model(QRCode)
            performance_perm = Permission.objects.get(
                codename='view_performance_dashboard',
                content_type=content_type,
            )
            
            # Assign permission to admin and superadmin groups
            if performance_perm not in admin_group.permissions.all():
                admin_group.permissions.add(performance_perm)
                self.stdout.write(self.style.SUCCESS('Added performance dashboard permission to Administrators group'))
                
            if performance_perm not in superadmin_group.permissions.all():
                superadmin_group.permissions.add(performance_perm)
                self.stdout.write(self.style.SUCCESS('Added performance dashboard permission to SuperAdministrators group'))
        except (ContentType.DoesNotExist, Permission.DoesNotExist) as e:
            self.stdout.write(self.style.ERROR(f'Error setting up permissions: {e}'))
        
        # Process each superuser
        for user in superusers:
            self.stdout.write(f'Processing superuser: {user.username}')
            
            # Add user to both groups
            if admin_group not in user.groups.all():
                user.groups.add(admin_group)
                self.stdout.write(self.style.SUCCESS(f'Added {user.username} to Administrators group'))
                
            if superadmin_group not in user.groups.all():
                user.groups.add(superadmin_group)
                self.stdout.write(self.style.SUCCESS(f'Added {user.username} to SuperAdministrators group'))
            
            # Get or create UserProfile with superadmin role
            try:
                profile = UserProfile.objects.get(user=user)
                old_role = profile.role
                profile.role = 'superadmin'
                profile.save()
                self.stdout.write(self.style.SUCCESS(f'Updated {user.username} role from {old_role} to superadmin'))
            except UserProfile.DoesNotExist:
                profile = UserProfile.objects.create(
                    user=user,
                    role='superadmin'
                )
                self.stdout.write(self.style.SUCCESS(f'Created new profile for {user.username} with superadmin role'))
                
        self.stdout.write(self.style.SUCCESS('Superuser roles setup completed successfully'))
