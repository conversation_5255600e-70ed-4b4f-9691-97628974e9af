# GitHub Actions Workflows

This directory contains GitHub Actions workflows for CI/CD.

## CI Workflow (`ci.yml`)

The CI workflow runs on every push to the `main` branch and on pull requests to the `main` branch. It performs the following steps:

1. **Build and Test**:
   - Checks out the repository
   - Sets up Python 3.11
   - Installs dependencies
   - Checks code formatting with Black
   - Lints code with Flake8
   - Runs Django tests

2. **Build Docker Images**:
   - Builds the Django web image
   - Builds the AI Engine image
   - Tests Docker Compose configuration
   - Runs security scans on the images

## CD Workflow (`cd.yml`)

The CD workflow runs on every push to the `main` branch and can also be triggered manually. It performs the following steps:

1. **Deploy to VPS**:
   - Checks out the repository
   - Sets up SSH with the private key from secrets
   - Adds the server's host key to known_hosts
   - Connects to the server via SSH
   - Pulls the latest code
   - Rebuilds and restarts the Docker containers
   - Verifies the deployment

2. **Notifications**:
   - Sends a Slack notification on successful deployment
   - Sends a Slack notification on failed deployment

## Required Secrets

The following secrets need to be set in the GitHub repository settings:

- `SSH_KEY`: The private SSH key for connecting to the server
- `SERVER_IP`: The IP address of the server
- `USERNAME`: The username for SSH login
- `SLACK_WEBHOOK`: The webhook URL for Slack notifications (optional)

## Manual Deployment

To trigger a deployment manually:

1. Go to the "Actions" tab in the GitHub repository
2. Select the "CD - Deploy to VPS" workflow
3. Click "Run workflow"
4. Select the branch to deploy (usually `main`)
5. Click "Run workflow"
