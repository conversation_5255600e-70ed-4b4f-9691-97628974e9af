from rest_framework import authentication
from rest_framework import exceptions
from qrcode_app.models import APIKey
from django.utils import timezone

class APIKeyAuthentication(authentication.BaseAuthentication):
    """
    Custom authentication using API keys.
    """
    def authenticate(self, request):
        api_key = request.META.get('HTTP_X_API_KEY')
        if not api_key:
            return None
        
        try:
            key = APIKey.objects.get(key=api_key, is_active=True)
            
            # Update last used timestamp
            key.last_used = timezone.now()
            key.save(update_fields=['last_used'])
            
            return (key.user, None)
        except APIKey.DoesNotExist:
            raise exceptions.AuthenticationFailed('Invalid API key')
