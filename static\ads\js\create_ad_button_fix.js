/**
 * Create Ad Button Fix JavaScript
 * Handles form validation and submission for ad creation/edit forms
 * This is a standalone version for pages that don't use the consolidated script
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Create Ad Button Fix: Initializing...');
    
    const submitButton = document.getElementById('submitAd');
    if (!submitButton) {
        console.warn('Create Ad Button Fix: Submit button not found');
        return;
    }

    console.log('Create Ad Button Fix: Found submit button');
    
    // Remove any existing event listeners to prevent conflicts
    const newButton = submitButton.cloneNode(true);
    submitButton.parentNode.replaceChild(newButton, submitButton);
    
    newButton.addEventListener('click', function(e) {
        console.log('Create Ad Button Fix: Submit button clicked');
        
        // Validate form before submission
        const adTitle = document.getElementById('adTitle');
        const adContent = document.getElementById('adContent');
        const adType = document.getElementById('adType');
        const adLocation = document.getElementById('ad_location');

        let isValid = true;
        const errors = [];

        // Clear previous validation states
        [adTitle, adContent, adType, adLocation].forEach(el => {
            if (el) el.classList.remove('is-invalid');
        });

        if (!adTitle || !adTitle.value.trim()) {
            isValid = false;
            errors.push('Ad title is required');
            if (adTitle) adTitle.classList.add('is-invalid');
        } else if (adTitle) {
            adTitle.classList.remove('is-invalid');
        }

        if (!adContent || !adContent.value.trim()) {
            isValid = false;
            errors.push('Ad content is required');
            if (adContent) adContent.classList.add('is-invalid');
        } else if (adContent) {
            adContent.classList.remove('is-invalid');
        }

        if (!adType || !adType.value) {
            isValid = false;
            errors.push('Ad type is required');
            if (adType) adType.classList.add('is-invalid');
        } else if (adType) {
            adType.classList.remove('is-invalid');
        }

        if (!adLocation || !adLocation.value) {
            isValid = false;
            errors.push('Ad placement location is required');
            if (adLocation) adLocation.classList.add('is-invalid');
        } else if (adLocation) {
            adLocation.classList.remove('is-invalid');
        }

        if (!isValid) {
            e.preventDefault(); // Only prevent if validation fails
            
            // Show errors in a more user-friendly way
            const errorContainer = document.getElementById('formErrorContainer');
            if (errorContainer) {
                errorContainer.innerHTML = '<strong>Please fix the following errors:</strong><ul><li>' + 
                                         errors.join('</li><li>') + '</li></ul>';
                errorContainer.style.display = 'block';
                errorContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
            } else {
                alert('Please fix the following errors:\n• ' + errors.join('\n• '));
            }
            
            // Focus on first invalid field
            const firstInvalid = document.querySelector('.is-invalid');
            if (firstInvalid) {
                firstInvalid.focus();
                
                // Navigate to the tab containing the invalid field
                const tabPane = firstInvalid.closest('.tab-pane');
                if (tabPane) {
                    const tabId = tabPane.id;
                    const tabButton = document.querySelector(`[data-bs-target="#${tabId}"]`);
                    if (tabButton && typeof bootstrap !== 'undefined') {
                        const bsTab = new bootstrap.Tab(tabButton);
                        bsTab.show();
                    }
                }
            }
            
            return false;
        } else {
            // Clear any error messages
            const errorContainer = document.getElementById('formErrorContainer');
            if (errorContainer) {
                errorContainer.style.display = 'none';
            }
            
            // Remove all invalid classes
            document.querySelectorAll('.is-invalid').forEach(el => {
                el.classList.remove('is-invalid');
            });
            
            console.log('Create Ad Button Fix: Form is valid, allowing submission');
            // Don't prevent default - let form submit normally
        }
    });

    console.log('Create Ad Button Fix: Initialized successfully');
});
