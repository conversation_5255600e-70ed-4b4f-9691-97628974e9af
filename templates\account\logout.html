{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Sign Out" %} - Enterprise QR{% endblock %}

{% block extra_css %}
<style>
    /* Enterprise Authentication Styles */
    .auth-container {
        min-height: 100vh;
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        position: relative;
        overflow: hidden;
        display: flex;
        align-items: center;
        padding: 40px 0;
    }

    .auth-background {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 1;
    }

    .auth-grid-pattern {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: 
            linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
            linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
        background-size: 40px 40px;
        animation: authGridMove 25s linear infinite;
    }

    @keyframes authGridMove {
        0% { transform: translate(0, 0); }
        100% { transform: translate(40px, 40px); }
    }

    .floating-qr {
        position: absolute;
        width: 60px;
        height: 60px;
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        justify-content: center;
        color: rgba(255, 255, 255, 0.7);
        font-size: 24px;
    }

    .floating-qr-1 {
        top: 15%;
        left: 10%;
        animation: floatQR1 8s ease-in-out infinite;
    }

    .floating-qr-2 {
        top: 70%;
        right: 15%;
        animation: floatQR2 10s ease-in-out infinite;
    }

    .floating-qr-3 {
        bottom: 20%;
        left: 20%;
        animation: floatQR3 12s ease-in-out infinite;
    }

    @keyframes floatQR1 {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(5deg); }
    }

    @keyframes floatQR2 {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-30px) rotate(-5deg); }
    }

    @keyframes floatQR3 {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-25px) rotate(3deg); }
    }

    .auth-card {
        position: relative;
        z-index: 2;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 20px;
        box-shadow: 
            0 20px 60px rgba(0, 0, 0, 0.1),
            inset 0 2px 0 rgba(255, 255, 255, 0.5),
            inset 0 -2px 0 rgba(0, 0, 0, 0.05);
        overflow: hidden;
        max-width: 450px;
        width: 100%;
        margin: 0 auto;
    }

    .auth-header {
        text-align: center;
        padding: 40px 40px 20px;
        background: linear-gradient(135deg, rgba(108, 117, 125, 0.1) 0%, rgba(73, 80, 87, 0.1) 100%);
        border-bottom: 1px solid rgba(108, 117, 125, 0.1);
    }

    .auth-logo {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        box-shadow: 0 10px 30px rgba(108, 117, 125, 0.3);
        transform: rotate(45deg);
    }

    .auth-logo i {
        font-size: 32px;
        color: white;
        transform: rotate(-45deg);
    }

    .auth-title {
        font-size: 2rem;
        font-weight: 800;
        color: #2c3e50;
        margin-bottom: 10px;
    }

    .auth-subtitle {
        color: #6c757d;
        font-size: 1rem;
        margin-bottom: 0;
        line-height: 1.5;
    }

    .auth-body {
        padding: 40px;
        text-align: center;
    }

    .enterprise-badge {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        background: linear-gradient(135deg, rgba(108, 117, 125, 0.1) 0%, rgba(73, 80, 87, 0.1) 100%);
        border: 2px solid rgba(108, 117, 125, 0.3);
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 700;
        color: #6c757d;
        margin-bottom: 30px;
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.2);
    }

    .logout-message {
        background: rgba(40, 167, 69, 0.1);
        border: 2px solid rgba(40, 167, 69, 0.2);
        border-radius: 12px;
        padding: 30px;
        margin-bottom: 30px;
        color: #28a745;
        font-weight: 500;
    }

    .logout-message i {
        font-size: 48px;
        margin-bottom: 15px;
        display: block;
    }

    .logout-message h5 {
        color: #28a745;
        font-weight: 700;
        margin-bottom: 10px;
    }

    .btn-enterprise {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        padding: 15px 30px;
        border-radius: 12px;
        font-weight: 700;
        font-size: 16px;
        transition: all 0.3s ease;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        position: relative;
        overflow: hidden;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 10px;
        margin: 0 10px 10px 0;
    }

    .btn-enterprise:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-enterprise::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
    }

    .btn-enterprise:hover::before {
        left: 100%;
    }

    .btn-secondary {
        background: rgba(108, 117, 125, 0.1);
        border: 2px solid rgba(108, 117, 125, 0.3);
        color: #6c757d;
        padding: 15px 30px;
        border-radius: 12px;
        font-weight: 600;
        font-size: 16px;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 10px;
        backdrop-filter: blur(10px);
    }

    .btn-secondary:hover {
        background: rgba(108, 117, 125, 0.2);
        border-color: rgba(108, 117, 125, 0.5);
        transform: translateY(-1px);
        color: #6c757d;
        text-decoration: none;
    }

    .action-buttons {
        display: flex;
        justify-content: center;
        gap: 15px;
        flex-wrap: wrap;
        margin-bottom: 30px;
    }

    .security-info {
        background: rgba(102, 126, 234, 0.05);
        border-radius: 12px;
        padding: 20px;
        margin-top: 30px;
        text-align: left;
    }

    .security-info h6 {
        color: #2c3e50;
        font-weight: 700;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .security-feature {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 8px;
        font-size: 14px;
        color: #6c757d;
    }

    .security-feature i {
        color: #28a745;
        font-size: 12px;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .auth-container {
            padding: 20px;
        }

        .auth-card {
            margin: 20px;
        }

        .auth-header,
        .auth-body {
            padding: 30px 25px;
        }

        .auth-title {
            font-size: 1.5rem;
        }

        .floating-qr {
            display: none;
        }

        .action-buttons {
            flex-direction: column;
            align-items: center;
        }

        .btn-enterprise,
        .btn-secondary {
            width: 100%;
            max-width: 300px;
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="auth-container">
    <!-- Animated Background -->
    <div class="auth-background">
        <div class="auth-grid-pattern"></div>
        <div class="auth-floating-elements">
            <div class="floating-qr floating-qr-1">
                <i class="fas fa-sign-out-alt"></i>
            </div>
            <div class="floating-qr floating-qr-2">
                <i class="fas fa-shield-check"></i>
            </div>
            <div class="floating-qr floating-qr-3">
                <i class="fas fa-lock"></i>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="auth-card">
            <!-- Header -->
            <div class="auth-header">
                <div class="auth-logo">
                    <i class="fas fa-sign-out-alt"></i>
                </div>
                <h1 class="auth-title">{% trans "Sign Out" %}</h1>
                <p class="auth-subtitle">{% trans "Are you sure you want to sign out of your Enterprise QR account?" %}</p>
            </div>

            <!-- Body -->
            <div class="auth-body">
                <!-- Enterprise Badge -->
                <div class="enterprise-badge">
                    <i class="fas fa-shield-check"></i>
                    <span>{% trans "Secure Session" %}</span>
                </div>

                <!-- Logout Confirmation -->
                <div class="logout-message">
                    <i class="fas fa-info-circle"></i>
                    <h5>{% trans "Session Security" %}</h5>
                    <p>{% trans "Signing out will securely end your current session and protect your account data." %}</p>
                </div>

                <!-- Action Buttons -->
                <div class="action-buttons">
                    <form method="post" action="{% url 'account_logout' %}" style="display: inline;">
                        {% csrf_token %}
                        {% if redirect_field_value %}
                        <input type="hidden" name="{{ redirect_field_name }}" value="{{ redirect_field_value }}"/>
                        {% endif %}
                        <button type="submit" class="btn-enterprise">
                            <i class="fas fa-sign-out-alt"></i>
                            {% trans "Yes, Sign Me Out" %}
                        </button>
                    </form>
                    
                    <a href="{% url 'index' %}" class="btn-secondary">
                        <i class="fas fa-arrow-left"></i>
                        {% trans "Cancel & Stay Signed In" %}
                    </a>
                </div>

                <!-- Security Information -->
                <div class="security-info">
                    <h6>
                        <i class="fas fa-info-circle"></i>
                        {% trans "What happens when you sign out?" %}
                    </h6>
                    <div class="security-feature">
                        <i class="fas fa-check"></i>
                        <span>{% trans "Your session will be securely terminated" %}</span>
                    </div>
                    <div class="security-feature">
                        <i class="fas fa-check"></i>
                        <span>{% trans "All temporary data will be cleared" %}</span>
                    </div>
                    <div class="security-feature">
                        <i class="fas fa-check"></i>
                        <span>{% trans "Your account data remains safe and secure" %}</span>
                    </div>
                    <div class="security-feature">
                        <i class="fas fa-check"></i>
                        <span>{% trans "You can sign back in anytime" %}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
