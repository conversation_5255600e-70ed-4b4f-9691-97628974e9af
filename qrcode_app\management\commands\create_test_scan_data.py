"""
Management command to create test QR scan data with coordinates for map testing
"""
from django.core.management.base import BaseCommand
from qrcode_app.models import QRScanLog
import random
from datetime import datetime, timedelta


class Command(BaseCommand):
    help = 'Create test QR scan data with coordinates for map testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--count',
            type=int,
            default=20,
            help='Number of test scans to create (default: 20)',
        )
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing scan data before creating new data',
        )

    def handle(self, *args, **options):
        count = options['count']
        clear = options['clear']

        if clear:
            self.stdout.write('Clearing existing scan data...')
            QRScanLog.objects.all().delete()
            self.stdout.write(self.style.SUCCESS('Existing scan data cleared.'))

        self.stdout.write(f'Creating {count} test scan records...')

        # Test data with various locations and organization types
        test_locations = [
            # Government/Military locations (should show as red markers)
            {'city': 'Washington', 'country': 'US', 'lat': 38.9072, 'lng': -77.0369, 'org': 'US Government'},
            {'city': 'London', 'country': 'GB', 'lat': 51.5074, 'lng': -0.1278, 'org': 'UK Government'},
            {'city': 'Ottawa', 'country': 'CA', 'lat': 45.4215, 'lng': -75.6972, 'org': 'Government of Canada'},
            {'city': 'Canberra', 'country': 'AU', 'lat': -35.2809, 'lng': 149.1300, 'org': 'Australian Department of Defense'},
            {'city': 'Berlin', 'country': 'DE', 'lat': 52.5200, 'lng': 13.4050, 'org': 'German Federal Police'},
            
            # Commercial/General locations (should show as blue markers)
            {'city': 'New York', 'country': 'US', 'lat': 40.7128, 'lng': -74.0060, 'org': 'Verizon Communications'},
            {'city': 'San Francisco', 'country': 'US', 'lat': 37.7749, 'lng': -122.4194, 'org': 'Google LLC'},
            {'city': 'Seattle', 'country': 'US', 'lat': 47.6062, 'lng': -122.3321, 'org': 'Amazon.com Inc'},
            {'city': 'Tokyo', 'country': 'JP', 'lat': 35.6762, 'lng': 139.6503, 'org': 'Sony Corporation'},
            {'city': 'Seoul', 'country': 'KR', 'lat': 37.5665, 'lng': 126.9780, 'org': 'Samsung Electronics'},
            {'city': 'Mumbai', 'country': 'IN', 'lat': 19.0760, 'lng': 72.8777, 'org': 'Tata Consultancy Services'},
            {'city': 'São Paulo', 'country': 'BR', 'lat': -23.5505, 'lng': -46.6333, 'org': 'Petrobras'},
            {'city': 'Lagos', 'country': 'NG', 'lat': 6.5244, 'lng': 3.3792, 'org': 'MTN Nigeria'},
            {'city': 'Cairo', 'country': 'EG', 'lat': 30.0444, 'lng': 31.2357, 'org': 'Orange Egypt'},
            {'city': 'Sydney', 'country': 'AU', 'lat': -33.8688, 'lng': 151.2093, 'org': 'Telstra Corporation'},
            
            # More government/military (red markers)
            {'city': 'Paris', 'country': 'FR', 'lat': 48.8566, 'lng': 2.3522, 'org': 'French Ministry of Defense'},
            {'city': 'Rome', 'country': 'IT', 'lat': 41.9028, 'lng': 12.4964, 'org': 'Italian State Police'},
            {'city': 'Madrid', 'country': 'ES', 'lat': 40.4168, 'lng': -3.7038, 'org': 'Spanish Civil Guard'},
            
            # More commercial (blue markers)
            {'city': 'Singapore', 'country': 'SG', 'lat': 1.3521, 'lng': 103.8198, 'org': 'Singapore Airlines'},
            {'city': 'Dubai', 'country': 'AE', 'lat': 25.2048, 'lng': 55.2708, 'org': 'Emirates Group'},
        ]

        # QR code identifiers
        qr_codes = [
            'test-qr-001', 'test-qr-002', 'test-qr-003', 'test-qr-004', 'test-qr-005',
            'landing-page-001', 'landing-page-002', 'product-info-001', 'contact-card-001',
            'wifi-access-001', 'event-ticket-001', 'menu-qr-001', 'feedback-form-001'
        ]

        # User agents for variety
        user_agents = [
            'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
            'Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (iPad; CPU OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1'
        ]

        created_count = 0
        for i in range(count):
            # Select random location and QR code
            location = random.choice(test_locations)
            qr_code = random.choice(qr_codes)
            user_agent = random.choice(user_agents)
            
            # Generate random IP address
            ip_address = f"{random.randint(1, 255)}.{random.randint(1, 255)}.{random.randint(1, 255)}.{random.randint(1, 255)}"
            
            # Create timestamp within last 30 days
            days_ago = random.randint(0, 30)
            hours_ago = random.randint(0, 23)
            minutes_ago = random.randint(0, 59)
            timestamp = datetime.now() - timedelta(days=days_ago, hours=hours_ago, minutes=minutes_ago)

            try:
                QRScanLog.objects.create(
                    code=qr_code,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    country=location['country'],
                    city=location['city'],
                    org=location['org'],
                    latitude=location['lat'],
                    longitude=location['lng'],
                    timestamp=timestamp
                )
                created_count += 1
                
                # Show progress
                if created_count % 5 == 0:
                    self.stdout.write(f'Created {created_count}/{count} scan records...')
                    
            except Exception as e:
                self.stdout.write(f'Error creating scan record: {e}')

        self.stdout.write(
            self.style.SUCCESS(f'Successfully created {created_count} test scan records!')
        )
        self.stdout.write('\n' + '='*60)
        self.stdout.write('🗺️  TEST DATA CREATED FOR QR SCAN MAP')
        self.stdout.write('='*60)
        self.stdout.write(f'✅ {created_count} scan records with coordinates')
        self.stdout.write('🔴 Government/Military orgs (red markers)')
        self.stdout.write('🔵 Commercial orgs (blue markers)')
        self.stdout.write('\nView the map at: /qr-admin-map/')
        self.stdout.write('JSON data at: /qr-admin-map-data/')
        self.stdout.write('='*60)
