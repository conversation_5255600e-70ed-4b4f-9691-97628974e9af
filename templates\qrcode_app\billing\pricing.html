{% extends 'base.html' %}
{% load static %}

{% block title %}Pricing Plans{% endblock %}

{% block extra_css %}
<style>
.pricing-section {
    padding: 4rem 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.pricing-header {
    text-align: center;
    margin-bottom: 3rem;
}

.pricing-header h1 {
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 1rem;
}

.pricing-header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

.pricing-cards {
    padding: 2rem 0;
}

.pricing-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
}

.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 50px rgba(0,0,0,0.15);
}

.pricing-card.popular {
    border: 3px solid #667eea;
    transform: scale(1.05);
}

.pricing-card.popular::before {
    content: 'Most Popular';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    background: #667eea;
    color: white;
    text-align: center;
    padding: 0.5rem;
    font-weight: bold;
    font-size: 0.9rem;
}

.pricing-card.current {
    border: 3px solid #28a745;
}

.pricing-card.current::before {
    content: 'Current Plan';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    background: #28a745;
    color: white;
    text-align: center;
    padding: 0.5rem;
    font-weight: bold;
    font-size: 0.9rem;
}

.plan-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-top: 1rem;
}

.plan-name {
    font-size: 1.5rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 0.5rem;
}

.plan-price {
    font-size: 3rem;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.plan-price .currency {
    font-size: 1.5rem;
    vertical-align: top;
}

.plan-price .period {
    font-size: 1rem;
    color: #6c757d;
    font-weight: normal;
}

.plan-description {
    color: #6c757d;
    font-size: 0.9rem;
}

.plan-features {
    list-style: none;
    padding: 0;
    margin: 2rem 0;
}

.plan-features li {
    padding: 0.5rem 0;
    display: flex;
    align-items: center;
}

.plan-features li i {
    color: #28a745;
    margin-right: 0.75rem;
    width: 16px;
}

.plan-features li.unavailable {
    color: #6c757d;
}

.plan-features li.unavailable i {
    color: #dc3545;
}

.plan-actions {
    text-align: center;
    margin-top: 2rem;
}

.btn-plan {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 1rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    width: 100%;
}

.btn-plan:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
    color: white;
}

.btn-plan.current-plan {
    background: #28a745;
    cursor: default;
}

.btn-plan.current-plan:hover {
    transform: none;
    box-shadow: none;
}

.billing-toggle {
    text-align: center;
    margin-bottom: 3rem;
}

.toggle-switch {
    display: inline-flex;
    align-items: center;
    background: rgba(255,255,255,0.2);
    border-radius: 25px;
    padding: 0.5rem;
}

.toggle-option {
    padding: 0.5rem 1.5rem;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: white;
    font-weight: 500;
}

.toggle-option.active {
    background: white;
    color: #667eea;
}

.savings-badge {
    background: #28a745;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
    margin-left: 0.5rem;
}

.billing-portal-link {
    text-align: center;
    margin-top: 2rem;
}

.billing-portal-link a {
    color: white;
    text-decoration: none;
    font-weight: 500;
}

.billing-portal-link a:hover {
    text-decoration: underline;
}
</style>
{% endblock %}

{% block content %}
<div class="container py-12">
    <!-- Header -->
    <div class="text-center mb-12">
        <h1 class="text-5xl font-bold text-gray-900 mb-4">Choose Your Plan</h1>
        <p class="text-xl text-gray-600 max-w-2xl mx-auto">Unlock powerful QR code features and grow your business with our enterprise-grade platform</p>
    </div>

    <!-- Billing Toggle -->
    <div class="flex justify-center mb-12">
        <div class="bg-gray-100 rounded-xl p-1 flex">
            <div class="toggle-option active bg-white shadow-sm rounded-lg px-6 py-3 cursor-pointer transition-all" data-billing="monthly">
                <span class="font-medium text-gray-900">Monthly</span>
            </div>
            <div class="toggle-option px-6 py-3 cursor-pointer transition-all rounded-lg" data-billing="yearly">
                <span class="font-medium text-gray-600">Yearly</span>
                <span class="badge badge-success ml-2">Save 20%</span>
            </div>
        </div>
    </div>

    <!-- Billing Portal Link -->
    {% if current_subscription and current_subscription.stripe_customer_id %}
    <div class="text-center mb-8">
        <a href="{% url 'billing_portal' %}" class="btn btn-secondary icon-button">
            <span class="icon-settings icon-primary"></span>
            <span>Manage Billing & Subscription</span>
        </a>
    </div>
    {% endif %}
</div>

<div class="container">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
        {% for plan in plans %}
        <div class="relative">
            {% if plan.plan_type == 'PRO' %}
            <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span class="badge badge-primary px-4 py-2 text-sm font-semibold">Most Popular</span>
            </div>
            {% endif %}

            <div class="card {% if plan.plan_type == 'PRO' %}ring-2 ring-primary-500{% endif %} {% if current_subscription and current_subscription.plan == plan %}ring-2 ring-success-500{% endif %} h-full">
                <div class="card-header text-center">
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">{{ plan.name }}</h3>
                    <div class="mb-4">
                        <span class="text-4xl font-bold text-primary-600">$</span>
                        <span class="text-5xl font-bold text-primary-600 amount" data-monthly="{{ plan.price }}" data-yearly="{{ plan.yearly_price }}">{{ plan.price }}</span>
                        <span class="text-gray-500 period">/month</span>
                    </div>
                    <p class="text-gray-600">{{ plan.description }}</p>
                </div>

                <div class="card-body">
                    <ul class="space-y-4">
                        {% if plan.max_qr_codes >= 10000 %}
                            <li class="flex items-center">
                                <span class="icon-success icon-success mr-3"></span>
                                <span class="text-gray-700">Unlimited QR Codes</span>
                            </li>
                        {% else %}
                            <li class="flex items-center">
                                <span class="icon-success icon-success mr-3"></span>
                                <span class="text-gray-700">Up to {{ plan.max_qr_codes }} QR Codes</span>
                            </li>
                        {% endif %}

                        {% if plan.max_scans_per_month >= 1000000 %}
                            <li class="flex items-center">
                                <span class="icon-success icon-success mr-3"></span>
                                <span class="text-gray-700">Unlimited Scans</span>
                            </li>
                        {% else %}
                            <li class="flex items-center">
                                <span class="icon-success icon-success mr-3"></span>
                                <span class="text-gray-700">{{ plan.max_scans_per_month|floatformat:0 }} Scans/Month</span>
                            </li>
                        {% endif %}

                        {% if plan.analytics_enabled %}
                            <li class="flex items-center">
                                <span class="icon-analytics icon-success mr-3"></span>
                                <span class="text-gray-700">Basic Analytics</span>
                            </li>
                        {% else %}
                            <li class="flex items-center opacity-50">
                                <span class="icon-error icon-error mr-3"></span>
                                <span class="text-gray-500">Basic Analytics</span>
                            </li>
                        {% endif %}

                        {% if plan.ai_enabled %}
                            <li class="flex items-center">
                                <span class="icon-premium icon-success mr-3"></span>
                                <span class="text-gray-700">AI Landing Pages</span>
                            </li>
                        {% else %}
                            <li class="flex items-center opacity-50">
                                <span class="icon-error icon-error mr-3"></span>
                                <span class="text-gray-500">AI Landing Pages</span>
                            </li>
                        {% endif %}

                        {% if plan.webhooks_enabled %}
                            <li class="flex items-center">
                                <span class="icon-api icon-success mr-3"></span>
                                <span class="text-gray-700">Webhook Integration</span>
                            </li>
                        {% else %}
                            <li class="flex items-center opacity-50">
                                <span class="icon-error icon-error mr-3"></span>
                                <span class="text-gray-500">Webhook Integration</span>
                            </li>
                        {% endif %}

                        {% if plan.alerts_enabled %}
                            <li class="flex items-center">
                                <span class="icon-notification icon-success mr-3"></span>
                                <span class="text-gray-700">Scan Alerts</span>
                            </li>
                        {% else %}
                            <li class="flex items-center opacity-50">
                                <span class="icon-error icon-error mr-3"></span>
                                <span class="text-gray-500">Scan Alerts</span>
                            </li>
                        {% endif %}

                        {% if plan.advanced_analytics_enabled %}
                            <li class="flex items-center">
                                <span class="icon-analytics icon-success mr-3"></span>
                                <span class="text-gray-700">Advanced Analytics</span>
                            </li>
                        {% else %}
                            <li class="flex items-center opacity-50">
                                <span class="icon-error icon-error mr-3"></span>
                                <span class="text-gray-500">Advanced Analytics</span>
                            </li>
                        {% endif %}

                        {% if plan.branding_enabled %}
                            <li class="flex items-center">
                                <span class="icon-premium icon-success mr-3"></span>
                                <span class="text-gray-700">Custom Branding</span>
                            </li>
                        {% else %}
                            <li class="flex items-center opacity-50">
                                <span class="icon-error icon-error mr-3"></span>
                                <span class="text-gray-500">Custom Branding</span>
                            </li>
                        {% endif %}

                        {% if plan.api_access_enabled %}
                            <li class="flex items-center">
                                <span class="icon-api icon-success mr-3"></span>
                                <span class="text-gray-700">API Access</span>
                            </li>
                        {% else %}
                            <li class="flex items-center opacity-50">
                                <span class="icon-error icon-error mr-3"></span>
                                <span class="text-gray-500">API Access</span>
                            </li>
                        {% endif %}

                        {% if plan.priority_support_enabled %}
                            <li class="flex items-center">
                                <span class="icon-premium icon-success mr-3"></span>
                                <span class="text-gray-700">Priority Support</span>
                            </li>
                        {% else %}
                            <li class="flex items-center opacity-50">
                                <span class="icon-error icon-error mr-3"></span>
                                <span class="text-gray-500">Priority Support</span>
                            </li>
                        {% endif %}
                    </ul>
                </div>

                <div class="card-footer">
                    {% if current_subscription and current_subscription.plan == plan %}
                        <button class="btn btn-success w-full" disabled>
                            <span class="icon-success icon-white"></span>
                            <span>Current Plan</span>
                        </button>
                    {% elif plan.price == 0 %}
                        <a href="{% url 'create_checkout_session' plan.id %}" class="btn btn-primary w-full icon-button">
                            <span class="icon-generate icon-white"></span>
                            <span>Get Started Free</span>
                        </a>
                    {% else %}
                        <a href="{% url 'create_checkout_session' plan.id %}" class="btn {% if plan.plan_type == 'PRO' %}btn-primary{% else %}btn-secondary{% endif %} w-full icon-button checkout-btn" data-plan-id="{{ plan.id }}">
                            <span class="icon-premium icon-white"></span>
                            <span>Choose {{ plan.name }}</span>
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Billing toggle functionality
document.addEventListener('DOMContentLoaded', function() {
    const toggleOptions = document.querySelectorAll('.toggle-option');
    const priceAmounts = document.querySelectorAll('.amount');
    const periods = document.querySelectorAll('.period');
    const checkoutBtns = document.querySelectorAll('.checkout-btn');

    let currentBilling = 'monthly';

    toggleOptions.forEach(option => {
        option.addEventListener('click', function() {
            // Update active state
            toggleOptions.forEach(opt => {
                opt.classList.remove('active', 'bg-white', 'shadow-sm');
                opt.querySelector('span').classList.remove('text-gray-900');
                opt.querySelector('span').classList.add('text-gray-600');
            });
            this.classList.add('active', 'bg-white', 'shadow-sm');
            this.querySelector('span').classList.remove('text-gray-600');
            this.querySelector('span').classList.add('text-gray-900');

            // Update billing cycle
            currentBilling = this.dataset.billing;

            // Update prices
            priceAmounts.forEach(amount => {
                const monthlyPrice = parseFloat(amount.dataset.monthly);
                const yearlyPrice = parseFloat(amount.dataset.yearly);

                if (currentBilling === 'yearly') {
                    amount.textContent = (yearlyPrice / 12).toFixed(2);
                } else {
                    amount.textContent = monthlyPrice.toFixed(2);
                }
            });

            // Update periods
            periods.forEach(period => {
                period.textContent = currentBilling === 'yearly' ? '/month (billed yearly)' : '/month';
            });
        });
    });

    // Update checkout links with billing cycle
    checkoutBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const planId = this.dataset.planId;
            const url = `/billing/checkout/${planId}/?billing=${currentBilling}`;
            window.location.href = url;
        });
    });
});
</script>
{% endblock %}
