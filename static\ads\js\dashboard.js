// Function to format numbers with commas
function formatNumber(num) {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

// Function to animate counting up
function animateCounter(element, target, duration = 1500) {
    const start = 0;
    const increment = target / (duration / 16);
    let current = start;

    const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
            clearInterval(timer);
            element.textContent = formatNumber(Math.round(target));
        } else {
            element.textContent = formatNumber(Math.round(current));
        }
    }, 16);
}

document.addEventListener('DOMContentLoaded', function() {
    // Add animations with delays
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach((card, index) => {
        card.classList.add('animate__animated', 'animate__fadeInUp');
        card.style.animationDelay = `${index * 0.1}s`;
    });

    // Add animations to dashboard cards
    const dashboardCards = document.querySelectorAll('.dashboard-card');
    dashboardCards.forEach((card, index) => {
        card.classList.add('animate__animated', 'animate__fadeIn');
        card.style.animationDelay = `${0.3 + (index * 0.1)}s`;
    });

    // Add animations to create ad CTA
    const createAdCta = document.querySelector('.create-ad-cta');
    if (createAdCta) {
        createAdCta.classList.add('animate__animated', 'animate__fadeIn');
        createAdCta.style.animationDelay = '0.2s';
    }

    // Animate stat values
    setTimeout(() => {
        document.querySelectorAll('.stat-value').forEach(el => {
            const text = el.textContent.trim();
            if (!text.includes('%') && !isNaN(parseInt(text))) {
                const value = parseInt(text);
                el.textContent = '0';
                animateCounter(el, value);
            }
        });
    }, 500);

    // Format numbers in the top ads table
    document.querySelectorAll('table td:nth-child(2), table td:nth-child(3)').forEach(el => {
        const text = el.textContent.trim();
        if (!isNaN(parseInt(text))) {
            el.textContent = formatNumber(parseInt(text));
        }
    });

    // Add subtle animation to action cards on hover
    const actionCards = document.querySelectorAll('.action-card');
    actionCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.classList.add('animate__animated', 'animate__pulse');
        });

        card.addEventListener('mouseleave', function() {
            this.classList.remove('animate__animated', 'animate__pulse');
        });
    });

    // Add subtle animation to recent ad items
    const recentAdItems = document.querySelectorAll('.recent-ad-item');
    recentAdItems.forEach((item, index) => {
        item.classList.add('animate__animated', 'animate__fadeInRight');
        item.style.animationDelay = `${0.4 + (index * 0.1)}s`;
    });
});
