/* Enterprise QR Code Generator - Corporate Footer & Body Styles */

/* Corporate Body Styles */
body {
    font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: #f5f7fa;
    color: #333;
    line-height: 1.6;
    overflow-x: hidden;
}

.corporate-body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

main {
    flex: 1 0 auto;
    padding-top: 2rem;
    padding-bottom: 3rem;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

/* Section Styles */
.corporate-section {
    padding: 3rem 0;
    position: relative;
}

.section-title {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: #1a1a1a;
    position: relative;
    padding-bottom: 0.75rem;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 3px;
    background-color: #0078d4;
}

.section-subtitle {
    font-size: 1.1rem;
    color: #505050;
    margin-bottom: 2rem;
    max-width: 700px;
}

/* Card Styles */
.corporate-card {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: none;
    overflow: hidden;
    height: 100%;
}

.corporate-card:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
    transform: translateY(-5px);
}

.card-header {
    padding: 1.25rem 1.5rem;
    background-color: #fff;
    border-bottom: 1px solid #e5e5e5;
}

.card-header h5 {
    margin-bottom: 0;
    font-weight: 600;
    color: #1a1a1a;
}

.card-body {
    padding: 1.5rem;
}

/* Corporate Footer Styles */
.corporate-footer {
    background-color: #1a1a1a;
    color: rgba(255, 255, 255, 0.7);
    padding: 4rem 0 2rem;
    position: relative;
    z-index: 10;
    flex-shrink: 0;
}

.footer-heading {
    color: #fff;
    font-weight: 600;
    margin-bottom: 1.5rem;
    position: relative;
    padding-bottom: 0.75rem;
}

.footer-heading::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 2px;
    background-color: #0078d4;
}

.footer-link {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    display: block;
    padding: 0.5rem 0;
    transition: all 0.2s ease;
}

.footer-link:hover {
    color: #fff;
    transform: translateX(5px);
    text-decoration: none;
}

.footer-social {
    display: flex;
    margin-top: 1.5rem;
}

.social-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    transition: all 0.3s ease;
}

.social-icon:hover {
    background-color: #0078d4;
    transform: translateY(-3px);
    color: #fff;
}

.footer-bottom {
    margin-top: 3rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Mobile Responsiveness */
@media (max-width: 991.98px) {
    .corporate-footer {
        padding: 3rem 0 5rem; /* Extra padding for mobile nav */
    }

    .footer-heading {
        margin-top: 1.5rem;
    }

    /* Improve mobile navigation spacing */
    .enhanced-navbar .nav-link {
        padding: 0.75rem 1rem;
    }

    /* Ensure dropdown menus are properly visible */
    .enhanced-dropdown-menu {
        width: 100%;
        max-height: 80vh;
        overflow-y: auto;
    }
}

@media (max-width: 767.98px) {
    .section-title {
        font-size: 1.75rem;
    }

    .section-subtitle {
        font-size: 1rem;
    }

    .corporate-section {
        padding: 2rem 0;
    }

    .corporate-footer {
        text-align: center;
    }

    .footer-heading::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .footer-social {
        justify-content: center;
    }

    .footer-link:hover {
        transform: none;
    }

    .footer-bottom {
        text-align: center;
    }

    /* Improve touch targets for mobile */
    .footer-link {
        padding: 0.75rem 0;
        margin-bottom: 0.25rem;
        display: inline-block;
    }

    /* Ensure buttons are properly sized for touch */
    .btn {
        padding: 0.75rem 1.25rem;
        min-height: 44px; /* Minimum touch target size */
    }

    /* Improve form elements on mobile */
    input, select, textarea {
        font-size: 16px !important; /* Prevent iOS zoom on focus */
        min-height: 44px; /* Minimum touch target size */
    }
}

/* Ensure content is visible on mobile */
@media (max-width: 575.98px) {
    .container {
        padding: 0 1rem;
    }

    .corporate-card {
        margin-bottom: 1.5rem;
    }

    .corporate-footer {
        padding: 2rem 0 5rem;
    }

    /* Improve spacing for small screens */
    .row {
        margin-left: -0.5rem;
        margin-right: -0.5rem;
    }

    .row > [class*="col-"] {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }

    /* Ensure text is readable on small screens */
    body {
        font-size: 16px;
    }

    h1 {
        font-size: 1.75rem;
    }

    h2 {
        font-size: 1.5rem;
    }

    h3 {
        font-size: 1.25rem;
    }
}

/* Tablet Optimizations */
@media (min-width: 768px) and (max-width: 991.98px) {
    .corporate-footer .col-md-4 {
        flex: 0 0 50%;
        max-width: 50%;
    }

    /* Improve tablet layout */
    .corporate-card {
        height: 100%;
    }

    /* Ensure proper spacing on tablets */
    .corporate-section {
        padding: 2.5rem 0;
    }
}

/* Landscape phone orientation */
@media (max-width: 767.98px) and (orientation: landscape) {
    .corporate-footer {
        padding-bottom: 6rem; /* Extra space for bottom nav in landscape */
    }

    /* Adjust content for landscape */
    .container {
        max-width: 100%;
    }

    /* Improve footer layout in landscape */
    .footer-social {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
    }

    .social-icon {
        margin: 0.25rem;
    }
}
