/* Enterprise Dashboard Styles - Elegant, Corporate, Luxury */

/* Global Dashboard Structure */
.enterprise-dashboard {
    background-color: #f8f9fa;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Dashboard Header */
.dashboard-header {
    background: linear-gradient(135deg, #1a237e 0%, #283593 100%);
    padding: 20px 0;
    color: white;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 10;
}

.dashboard-welcome {
    padding: 10px 0;
}

.welcome-title {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 5px;
    color: white;
}

.welcome-subtitle {
    font-size: 14px;
    opacity: 0.9;
    margin-bottom: 0;
}

.dashboard-actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 15px;
}

.action-item {
    position: relative;
}

.btn-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    border: none;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.btn-icon:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: #ff5252;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    border: 2px solid #1a237e;
}

.create-ad-btn {
    background: linear-gradient(90deg, #ff6b6b, #ff8e8e);
    border: none;
    border-radius: 30px;
    padding: 10px 20px;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
    transition: all 0.3s ease;
}

.create-ad-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
    background: linear-gradient(90deg, #ff5252, #ff7676);
}

/* Dashboard Content */
.dashboard-content {
    flex: 1;
    padding: 30px 0;
}

/* Sidebar Navigation */
.dashboard-sidebar {
    padding-right: 0;
}

.sidebar-container {
    background: #1a237e; /* Changed from light to dark background */
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    height: calc(100vh - 140px);
    position: sticky;
    top: 90px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #1a237e;
}

.sidebar-container::-webkit-scrollbar {
    width: 6px;
}

.sidebar-container::-webkit-scrollbar-track {
    background: #283593; /* Darker blue to match the sidebar */
}

.sidebar-container::-webkit-scrollbar-thumb {
    background-color: #5c6bc0; /* Lighter blue for better contrast */
    border-radius: 6px;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    background-color: #1a237e;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
}

.sidebar-title {
    font-size: 18px;
    font-weight: 700;
    color: white;
    margin: 0;
}

.sidebar-nav {
    padding: 15px 0;
}

.nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    margin-bottom: 5px;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: rgba(255, 255, 255, 0.85); /* Light text for dark background */
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
    font-weight: 600;
    margin: 0 10px;
    border-radius: 8px;
}

.nav-link i {
    font-size: 18px;
    margin-right: 12px;
    width: 20px;
    text-align: center;
    color: rgba(255, 255, 255, 0.7); /* Light icon color */
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1); /* Subtle light hover */
    color: white;
    font-weight: 700;
}

.nav-item.active .nav-link {
    background-color: rgba(255, 255, 255, 0.15); /* Slightly lighter than hover */
    color: white;
    border-left-color: #ff5252;
    font-weight: 700;
}

.nav-item.active .nav-link i {
    color: white;
}

.nav-divider {
    padding: 10px 20px;
    margin-top: 15px;
}

.divider-label {
    font-size: 12px;
    text-transform: uppercase;
    color: white; /* Light text for dark background */
    font-weight: 700;
    letter-spacing: 1px;
    background-color: rgba(255, 255, 255, 0.15); /* Light background for contrast */
    padding: 5px 10px;
    border-radius: 4px;
    display: inline-block;
}

/* Main Content Area */
.dashboard-main {
    padding-left: 30px;
}

.dashboard-section {
    margin-bottom: 30px;
}

/* Stats Cards */
.stats-row {
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    padding: 25px;
    height: 100%;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border-top: 4px solid transparent;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-card.active {
    border-top-color: #4caf50;
}

.stat-card.pending {
    border-top-color: #ff9800;
}

.stat-card.impressions {
    border-top-color: #2196f3;
}

.stat-card.clicks {
    border-top-color: #9c27b0;
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    font-size: 20px;
    transition: all 0.3s ease;
}

.stat-card.active .stat-icon {
    background-color: rgba(76, 175, 80, 0.1);
    color: #4caf50;
}

.stat-card.pending .stat-icon {
    background-color: rgba(255, 152, 0, 0.1);
    color: #ff9800;
}

.stat-card.impressions .stat-icon {
    background-color: rgba(33, 150, 243, 0.1);
    color: #2196f3;
}

.stat-card.clicks .stat-icon {
    background-color: rgba(156, 39, 176, 0.1);
    color: #9c27b0;
}

.stat-card.conversions {
    border-top-color: #ff5252;
}

.stat-card.conversions .stat-icon {
    background-color: rgba(255, 82, 82, 0.1);
    color: #ff5252;
}

.stat-card.ctr {
    border-top-color: #ff9800;
}

.stat-card.ctr .stat-icon {
    background-color: rgba(255, 152, 0, 0.1);
    color: #ff9800;
}

.stat-value {
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 5px;
    color: #1a237e;
}

.stat-label {
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 10px;
    font-weight: 500;
}

.stat-trend {
    font-size: 12px;
    color: #6c757d;
}

.stat-trend i {
    margin-right: 3px;
}

.stat-trend i.fa-arrow-up,
.trend-up i.fa-arrow-up,
.metric-trend i.fa-arrow-up {
    color: #4caf50;
}

.stat-trend i.fa-arrow-down,
.trend-down i.fa-arrow-down,
.metric-trend i.fa-arrow-down {
    color: #f44336;
}

.stat-trend i.fa-minus,
.trend-neutral i.fa-minus,
.metric-trend i.fa-minus {
    color: #9e9e9e;
}

.trend-up {
    color: #4caf50;
}

.trend-down {
    color: #f44336;
}

.trend-neutral {
    color: #9e9e9e;
}

.metric-trend {
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
}

/* Dashboard Cards */
.dashboard-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    margin-bottom: 30px;
    transition: all 0.3s ease;
    height: 100%;
}

.dashboard-card:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.card-title {
    font-size: 18px;
    font-weight: 700;
    color: #1a237e;
    margin: 0;
}

.card-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Recent Ads List */
.recent-ads-container {
    padding: 15px 25px 25px;
}

.recent-ads-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.recent-ad-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.recent-ad-item:last-child {
    border-bottom: none;
}

.recent-ad-item:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.ad-icon {
    width: 45px;
    height: 45px;
    border-radius: 10px;
    background-color: rgba(26, 35, 126, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: #1a237e;
    font-size: 18px;
}

.ad-info {
    flex: 1;
}

.ad-title {
    font-weight: 600;
    color: #1a237e;
    margin-bottom: 5px;
}

.ad-meta {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #6c757d;
}

.ad-date {
    margin-right: 10px;
}

.ad-status {
    display: inline-flex;
    align-items: center;
    padding: 2px 8px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 600;
}

.status-active {
    background-color: rgba(76, 175, 80, 0.1);
    color: #4caf50;
}

.status-pending {
    background-color: rgba(255, 152, 0, 0.1);
    color: #ff9800;
}

.status-draft {
    background-color: rgba(158, 158, 158, 0.1);
    color: #9e9e9e;
}

.status-rejected {
    background-color: rgba(244, 67, 54, 0.1);
    color: #f44336;
}

.ad-metrics {
    display: flex;
    gap: 15px;
    margin-right: 15px;
}

.metric {
    text-align: center;
    min-width: 60px;
}

.metric-value {
    font-weight: 700;
    color: #1a237e;
    font-size: 14px;
    display: block;
}

.metric-label {
    font-size: 11px;
    color: #6c757d;
    display: block;
}

.ad-actions {
    opacity: 0;
    transition: all 0.3s ease;
}

.recent-ad-item:hover .ad-actions {
    opacity: 1;
}

/* Performance Metrics */
.performance-metrics {
    padding: 15px 25px;
}

.metric-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.metric-row:last-child {
    margin-bottom: 0;
}

.metric-card {
    flex: 1;
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 12px;
    padding: 15px;
    position: relative;
}

.metric-chart {
    margin-top: 10px;
    height: 30px;
}

.sparkline-placeholder {
    height: 100%;
    background: linear-gradient(90deg,
        rgba(26, 35, 126, 0.1) 0%,
        rgba(26, 35, 126, 0.2) 20%,
        rgba(26, 35, 126, 0.1) 40%,
        rgba(26, 35, 126, 0.3) 60%,
        rgba(26, 35, 126, 0.2) 80%,
        rgba(26, 35, 126, 0.4) 100%
    );
    border-radius: 4px;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 40px 20px;
}

.empty-state-icon {
    font-size: 48px;
    color: #c5cae9;
    margin-bottom: 20px;
}

.empty-state-title {
    font-size: 20px;
    font-weight: 600;
    color: #1a237e;
    margin-bottom: 10px;
}

.empty-state-description {
    color: #6c757d;
    margin-bottom: 20px;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .dashboard-sidebar {
        padding-right: 15px;
        margin-bottom: 30px;
    }

    .dashboard-main {
        padding-left: 15px;
    }

    .sidebar-container {
        height: auto;
        position: relative;
        top: 0;
    }
}

@media (max-width: 768px) {
    .dashboard-welcome {
        text-align: center;
        margin-bottom: 15px;
    }

    .dashboard-actions {
        justify-content: center;
    }

    .stat-card {
        margin-bottom: 20px;
    }

    .metric-row {
        flex-direction: column;
    }

    .metric-card {
        margin-bottom: 15px;
    }
}

@media (max-width: 576px) {
    .welcome-title {
        font-size: 20px;
    }

    .card-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .card-actions {
        margin-top: 10px;
    }

    .recent-ad-item {
        flex-wrap: wrap;
    }

    .ad-metrics {
        width: 100%;
        margin-top: 10px;
        margin-left: 60px;
    }

    .ad-actions {
        margin-top: 10px;
        margin-left: 60px;
        opacity: 1;
    }
}

/* Admin Dashboard Styles */
.admin-dashboard-content {
    padding: 20px;
}

.admin-section {
    margin-bottom: 30px;
}

.admin-section-title {
    font-size: 18px;
    font-weight: 700;
    color: #1a237e;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.admin-action-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.admin-stat-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    padding: 20px;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    height: 100%;
}

.admin-stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.admin-stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    background-color: rgba(26, 35, 126, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: #1a237e;
    font-size: 24px;
}

.admin-stat-info {
    flex: 1;
}

.admin-stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #1a237e;
    margin-bottom: 5px;
}

.admin-stat-label {
    font-size: 14px;
    color: #6c757d;
}

.admin-activity-list {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.admin-activity-list .table {
    margin-bottom: 0;
}

.admin-activity-list .table th {
    font-weight: 600;
    color: #1a237e;
    border-top: none;
    background-color: rgba(26, 35, 126, 0.05);
}

.admin-activity-list .table td {
    vertical-align: middle;
}

/* Sidebar Ad Types */
.sidebar-ad-types {
    padding: 0 20px;
    margin-top: 10px;
}

.sidebar-ad-type {
    display: flex;
    align-items: center;
    padding: 10px;
    margin-bottom: 8px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.sidebar-ad-type:hover {
    background-color: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.sidebar-ad-type i {
    font-size: 16px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    margin-right: 10px;
    color: white;
}

.ad-type-info {
    flex: 1;
}

.ad-type-name {
    display: block;
    font-size: 13px;
    font-weight: 600;
    color: white;
    margin-bottom: 2px;
}

.ad-type-price {
    display: block;
    font-size: 11px;
    color: rgba(255, 255, 255, 0.7);
}

/* Admin Tab Responsive Styles */
@media (max-width: 992px) {
    .admin-action-buttons {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }
}

@media (max-width: 768px) {
    .admin-stat-card {
        margin-bottom: 15px;
    }
}

@media (max-width: 576px) {
    .admin-action-buttons {
        grid-template-columns: 1fr;
    }

    .admin-stat-card {
        flex-direction: column;
        text-align: center;
    }

    .admin-stat-icon {
        margin-right: 0;
        margin-bottom: 15px;
    }
}
