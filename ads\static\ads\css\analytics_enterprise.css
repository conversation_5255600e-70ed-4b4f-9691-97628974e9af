/* Enterprise Analytics Dashboard Styles */

/* Analytics-specific styles */
.analytics-dashboard .dashboard-header {
    background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
}

/* Chart container */
.chart-container {
    position: relative;
    margin: auto;
    height: 300px;
    width: 100%;
}

/* Date Range Picker */
.date-range-picker-container {
    position: relative;
}

.date-range-picker-container .btn {
    min-width: 200px;
    text-align: left;
    background-color: rgba(255, 255, 255, 0.9);
    color: #1a237e;
    border: none;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.date-range-picker-container .btn:hover {
    background-color: white;
}

#export-data-btn {
    background-color: rgba(255, 255, 255, 0.9);
    color: #1a237e;
    border: none;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

#export-data-btn:hover {
    background-color: white;
}

/* Stat Cards */
.stat-card.impressions {
    border-top-color: #2196f3;
}

.stat-card.clicks {
    border-top-color: #9c27b0;
}

.stat-card.ctr {
    border-top-color: #ff9800;
}

.stat-card.impressions .stat-icon {
    background-color: rgba(33, 150, 243, 0.1);
    color: #2196f3;
}

.stat-card.clicks .stat-icon {
    background-color: rgba(156, 39, 176, 0.1);
    color: #9c27b0;
}

.stat-card.ctr .stat-icon {
    background-color: rgba(255, 152, 0, 0.1);
    color: #ff9800;
}

/* Table styles */
.table {
    margin-bottom: 0;
}

.table th {
    font-weight: 600;
    color: #1a237e;
    border-top: none;
    background-color: rgba(26, 35, 126, 0.05);
}

.table td {
    vertical-align: middle;
}

/* Badge colors */
.badge.bg-active {
    background-color: #4caf50;
}

.badge.bg-pending {
    background-color: #ff9800;
}

.badge.bg-draft {
    background-color: #9e9e9e;
}

.badge.bg-paused {
    background-color: #607d8b;
}

.badge.bg-rejected {
    background-color: #f44336;
}

.badge.bg-expired {
    background-color: #795548;
}

/* Metric toggle buttons */
.btn-group .btn-outline-primary {
    color: #1a237e;
    border-color: #1a237e;
}

.btn-group .btn-outline-primary:hover,
.btn-group .btn-outline-primary.active {
    background-color: #1a237e;
    border-color: #1a237e;
    color: white;
}

/* Dropdown menu */
.dropdown-menu {
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: none;
    padding: 8px;
}

.dropdown-item {
    border-radius: 4px;
    padding: 8px 16px;
}

.dropdown-item:hover,
.dropdown-item.active {
    background-color: rgba(26, 35, 126, 0.1);
    color: #1a237e;
}

/* Audience tab specific styles */
.audience-chart-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.audience-chart-card {
    flex: 1;
    min-width: 300px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    padding: 20px;
    margin-bottom: 20px;
}

.audience-chart-title {
    font-size: 16px;
    font-weight: 600;
    color: #1a237e;
    margin-bottom: 15px;
}

/* Geography tab specific styles */
.map-container {
    height: 400px;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 20px;
}

/* Comparison tab specific styles */
.comparison-controls {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.comparison-select {
    flex: 1;
    min-width: 200px;
}

.comparison-chart-container {
    height: 400px;
}

/* System analytics tab specific styles */
.system-metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.system-metric-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.system-metric-value {
    font-size: 32px;
    font-weight: 700;
    color: #1a237e;
    margin: 10px 0;
}

.system-metric-label {
    font-size: 14px;
    color: #6c757d;
}

.system-metric-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: rgba(26, 35, 126, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #1a237e;
    font-size: 20px;
}

/* Responsive styles */
@media (max-width: 992px) {
    .chart-container {
        height: 250px;
    }
    
    .map-container {
        height: 300px;
    }
}

@media (max-width: 768px) {
    .dashboard-actions {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .date-range-picker-container,
    #export-data-btn {
        width: 100%;
    }
    
    .date-range-picker-container .btn {
        width: 100%;
    }
    
    .chart-container {
        height: 200px;
    }
    
    .map-container {
        height: 250px;
    }
}

@media (max-width: 576px) {
    .btn-group {
        display: flex;
        flex-direction: column;
        width: 100%;
    }
    
    .btn-group .btn {
        border-radius: 4px !important;
        margin-bottom: 5px;
    }
    
    .comparison-controls {
        flex-direction: column;
    }
}
