"""
Management command to diagnose QR code issues
"""
from django.core.management.base import BaseCommand
from qrcode_app.models import QRCode
import qrcode
from io import BytesIO
import os
from PIL import Image


class Command(BaseCommand):
    help = 'Diagnose QR code generation and scanning issues'

    def add_arguments(self, parser):
        parser.add_argument(
            '--qr-id',
            type=int,
            help='Specific QR code ID to diagnose',
        )
        parser.add_argument(
            '--latest',
            action='store_true',
            help='Diagnose the most recent QR code',
        )
        parser.add_argument(
            '--test-generation',
            action='store_true',
            help='Test QR code generation with sample data',
        )

    def test_qr_generation(self):
        """Test QR code generation with sample data"""
        self.stdout.write('\n' + '='*60)
        self.stdout.write('🧪 TESTING QR CODE GENERATION')
        self.stdout.write('='*60)
        
        test_data = [
            ('Simple Text', 'Hello World', 'TEXT'),
            ('Simple URL', 'https://google.com', 'URL'),
            ('Long URL', 'https://github.com/Codegx-Technology/very-long-repository-name-for-testing', 'URL'),
        ]
        
        for name, data, qr_type in test_data:
            try:
                self.stdout.write(f'\n📝 Testing: {name}')
                self.stdout.write(f'Data: {data}')
                self.stdout.write(f'Type: {qr_type}')
                
                # Create QR code
                qr = qrcode.QRCode(
                    version=40,
                    error_correction=qrcode.constants.ERROR_CORRECT_L,
                    box_size=10,
                    border=4,
                )
                qr.add_data(data)
                qr.make(fit=True)
                
                # Create image
                img = qr.make_image(fill_color='black', back_color='white')
                
                # Test image creation
                buffer = BytesIO()
                img.save(buffer, format='PNG')
                buffer.seek(0)
                
                # Check image size
                test_img = Image.open(buffer)
                width, height = test_img.size
                
                self.stdout.write(f'✅ QR Code generated successfully')
                self.stdout.write(f'   Image size: {width}x{height}')
                self.stdout.write(f'   Data length: {len(data)} characters')
                
            except Exception as e:
                self.stdout.write(f'❌ Error generating QR code: {str(e)}')

    def diagnose_qr_code(self, qr_code):
        """Diagnose a specific QR code"""
        self.stdout.write('\n' + '='*60)
        self.stdout.write(f'🔍 DIAGNOSING QR CODE: {qr_code.name}')
        self.stdout.write('='*60)
        
        # Basic information
        self.stdout.write(f'📋 Basic Information:')
        self.stdout.write(f'   ID: {qr_code.id}')
        self.stdout.write(f'   Name: {qr_code.name}')
        self.stdout.write(f'   Type: {qr_code.qr_type}')
        self.stdout.write(f'   Created: {qr_code.created_at}')
        
        # Data analysis
        self.stdout.write(f'\n📊 Data Analysis:')
        self.stdout.write(f'   Data: {qr_code.data}')
        self.stdout.write(f'   Data length: {len(qr_code.data)} characters')
        self.stdout.write(f'   Original URL: {qr_code.original_url}')
        
        # Image analysis
        self.stdout.write(f'\n🖼️ Image Analysis:')
        if qr_code.image:
            self.stdout.write(f'   ✅ Image exists: {qr_code.image.name}')
            self.stdout.write(f'   Image URL: {qr_code.image.url}')
            
            # Check if file exists on disk
            if os.path.exists(qr_code.image.path):
                file_size = os.path.getsize(qr_code.image.path)
                self.stdout.write(f'   ✅ File exists on disk: {file_size} bytes')
                
                # Try to open and analyze the image
                try:
                    with Image.open(qr_code.image.path) as img:
                        width, height = img.size
                        mode = img.mode
                        self.stdout.write(f'   ✅ Image readable: {width}x{height}, mode: {mode}')
                except Exception as e:
                    self.stdout.write(f'   ❌ Image corrupted: {str(e)}')
            else:
                self.stdout.write(f'   ❌ File missing on disk: {qr_code.image.path}')
        else:
            self.stdout.write(f'   ❌ No image associated with QR code')
        
        # QR Type specific analysis
        self.stdout.write(f'\n🎯 Type-Specific Analysis:')
        if qr_code.qr_type == 'TEXT':
            self.stdout.write(f'   📝 TEXT QR Code')
            self.stdout.write(f'   Expected behavior: Display text directly when scanned')
            self.stdout.write(f'   Text content: "{qr_code.data}"')
            
        elif qr_code.qr_type == 'URL':
            self.stdout.write(f'   🌐 URL QR Code')
            if qr_code.original_url:
                self.stdout.write(f'   ✅ Has landing page functionality')
                self.stdout.write(f'   Original URL: {qr_code.original_url}')
                self.stdout.write(f'   Landing URL: {qr_code.data}')
                
                # Check if it's localhost
                if '127.0.0.1' in qr_code.data or 'localhost' in qr_code.data:
                    self.stdout.write(f'   ⚠️ Uses localhost - only works on same machine')
                    self.stdout.write(f'   💡 Use public URL for mobile testing')
                else:
                    self.stdout.write(f'   ✅ Uses public URL - should work from anywhere')
            else:
                self.stdout.write(f'   ⚠️ Direct URL (no landing page)')
                self.stdout.write(f'   URL: {qr_code.data}')
        
        # Scanning recommendations
        self.stdout.write(f'\n📱 Scanning Recommendations:')
        if qr_code.qr_type == 'TEXT':
            self.stdout.write(f'   • Scan with any QR scanner app')
            self.stdout.write(f'   • Should display: "{qr_code.data}"')
            self.stdout.write(f'   • No internet connection required')
            
        elif qr_code.qr_type == 'URL':
            if '127.0.0.1' in qr_code.data:
                self.stdout.write(f'   • ⚠️ Only works on same machine as server')
                self.stdout.write(f'   • For mobile testing, use: python manage.py test_qr_public')
                self.stdout.write(f'   • Test in browser: {qr_code.data}')
            else:
                self.stdout.write(f'   • ✅ Should work from any device with internet')
                self.stdout.write(f'   • Will show landing page then redirect')
                self.stdout.write(f'   • Test in browser: {qr_code.data}')
        
        # Troubleshooting
        self.stdout.write(f'\n🔧 Troubleshooting:')
        issues_found = []
        
        if not qr_code.image:
            issues_found.append('No image file')
        elif not os.path.exists(qr_code.image.path):
            issues_found.append('Image file missing from disk')
        
        if qr_code.qr_type == 'URL' and '127.0.0.1' in qr_code.data:
            issues_found.append('Uses localhost URL (not accessible from mobile)')
        
        if len(qr_code.data) > 2000:
            issues_found.append('Data too long (may cause scanning issues)')
        
        if issues_found:
            for issue in issues_found:
                self.stdout.write(f'   ❌ {issue}')
        else:
            self.stdout.write(f'   ✅ No obvious issues found')

    def handle(self, *args, **options):
        qr_id = options.get('qr_id')
        latest = options.get('latest')
        test_generation = options.get('test_generation')
        
        if test_generation:
            self.test_qr_generation()
            return
        
        if qr_id:
            try:
                qr_code = QRCode.objects.get(id=qr_id)
                self.diagnose_qr_code(qr_code)
            except QRCode.DoesNotExist:
                self.stdout.write(f'❌ QR code with ID {qr_id} not found')
                return
                
        elif latest:
            qr_code = QRCode.objects.order_by('-created_at').first()
            if qr_code:
                self.diagnose_qr_code(qr_code)
            else:
                self.stdout.write('❌ No QR codes found')
                return
        else:
            # Show recent QR codes and let user choose
            recent_qrs = QRCode.objects.order_by('-created_at')[:5]
            
            if not recent_qrs:
                self.stdout.write('❌ No QR codes found')
                return
            
            self.stdout.write('\n' + '='*60)
            self.stdout.write('📋 RECENT QR CODES')
            self.stdout.write('='*60)
            
            for qr in recent_qrs:
                self.stdout.write(f'ID: {qr.id} | {qr.qr_type} | {qr.name} | {qr.created_at.strftime("%Y-%m-%d %H:%M")}')
            
            self.stdout.write('\n💡 Usage:')
            self.stdout.write('python manage.py diagnose_qr --latest')
            self.stdout.write('python manage.py diagnose_qr --qr-id <ID>')
            self.stdout.write('python manage.py diagnose_qr --test-generation')
