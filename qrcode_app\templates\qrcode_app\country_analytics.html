{% extends 'base.html' %}
{% load static %}

{% block title %}Country Analytics Dashboard{% endblock %}

{% block extra_css %}
<style>
    .country-analytics {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .analytics-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .country-header {
        text-align: center;
        color: white;
        margin-bottom: 3rem;
    }

    .country-header h1 {
        font-size: 3rem;
        font-weight: bold;
        margin-bottom: 1rem;
    }

    .country-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem;
        margin-bottom: 1rem;
        background: rgba(102, 126, 234, 0.1);
        border-radius: 10px;
        border-left: 4px solid #667eea;
    }

    .country-name {
        font-weight: bold;
        font-size: 1.1rem;
        color: #495057;
    }

    .country-stats {
        display: flex;
        gap: 1rem;
        align-items: center;
    }

    .stat-badge {
        background: #667eea;
        color: white;
        padding: 0.3rem 0.8rem;
        border-radius: 15px;
        font-size: 0.9rem;
        font-weight: bold;
    }

    .threat-badge {
        background: #dc3545;
        color: white;
        padding: 0.2rem 0.6rem;
        border-radius: 10px;
        font-size: 0.8rem;
    }

    .org-list {
        max-height: 300px;
        overflow-y: auto;
        margin-top: 1rem;
    }

    .org-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }

    .summary-metrics {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .metric-box {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 15px;
        text-align: center;
    }

    .metric-value {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }

    .metric-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
</style>
{% endblock %}

{% block content %}
<div class="country-analytics">
    <div class="container">
        <!-- Header -->
        <div class="country-header">
            <h1><i class="fas fa-globe-americas me-3"></i>Country Analytics</h1>
            <p>Geographic distribution and security analysis by country</p>
        </div>

        <!-- Summary Metrics -->
        <div class="summary-metrics">
            <div class="metric-box">
                <div class="metric-value">{{ total_countries }}</div>
                <div class="metric-label">Countries</div>
            </div>
            <div class="metric-box">
                <div class="metric-value">{{ total_scans }}</div>
                <div class="metric-label">Total Scans</div>
            </div>
            <div class="metric-box">
                <div class="metric-value">{{ days }}</div>
                <div class="metric-label">Days Period</div>
            </div>
        </div>

        <!-- Country Statistics -->
        <div class="row">
            <div class="col-md-8">
                <div class="analytics-card">
                    <h4><i class="fas fa-flag me-2"></i>Top Countries by Scans</h4>
                    {% for country in country_stats %}
                    <div class="country-item">
                        <div>
                            <div class="country-name">
                                <i class="fas fa-map-marker-alt me-2"></i>{{ country.country }}
                            </div>
                            <small class="text-muted">
                                {{ country.unique_scanners }} unique scanner{{ country.unique_scanners|pluralize }}
                            </small>
                        </div>
                        <div class="country-stats">
                            <span class="stat-badge">{{ country.total_scans }} scan{{ country.total_scans|pluralize }}</span>
                            {% if country.vpn_scans > 0 %}
                            <span class="threat-badge">{{ country.vpn_scans }} VPN</span>
                            {% endif %}
                            {% if country.proxy_scans > 0 %}
                            <span class="threat-badge">{{ country.proxy_scans }} Proxy</span>
                            {% endif %}
                            {% if country.tor_scans > 0 %}
                            <span class="threat-badge">{{ country.tor_scans }} Tor</span>
                            {% endif %}
                        </div>
                    </div>
                    {% empty %}
                    <p class="text-muted text-center">No country data available for the selected period.</p>
                    {% endfor %}
                </div>
            </div>

            <div class="col-md-4">
                <div class="analytics-card">
                    <h4><i class="fas fa-building me-2"></i>Top Organizations</h4>
                    {% for country, orgs in org_by_country.items %}
                    <div class="mb-3">
                        <h6 class="text-primary">{{ country }}</h6>
                        <div class="org-list">
                            {% for org in orgs %}
                            <div class="org-item">
                                <span class="text-truncate" style="max-width: 150px;">{{ org.organization }}</span>
                                <span class="badge bg-info">{{ org.count }}</span>
                            </div>
                            {% empty %}
                            <p class="text-muted small">No organization data</p>
                            {% endfor %}
                        </div>
                    </div>
                    {% empty %}
                    <p class="text-muted">No organization data available.</p>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Time Range Filter -->
        <div class="analytics-card">
            <h4><i class="fas fa-calendar me-2"></i>Time Range</h4>
            <div class="row align-items-center">
                <div class="col-md-6">
                    <select id="dateRange" class="form-select">
                        <option value="7" {% if days == 7 %}selected{% endif %}>Last 7 days</option>
                        <option value="30" {% if days == 30 %}selected{% endif %}>Last 30 days</option>
                        <option value="90" {% if days == 90 %}selected{% endif %}>Last 90 days</option>
                        <option value="365" {% if days == 365 %}selected{% endif %}>Last year</option>
                    </select>
                </div>
                <div class="col-md-6 text-end">
                    <a href="{% url 'enterprise_dashboard' %}" class="btn btn-outline-primary">
                        <i class="fas fa-chart-line me-2"></i>Main Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Date range filter
document.getElementById('dateRange').addEventListener('change', function() {
    const days = this.value;
    window.location.href = `?days=${days}`;
});
</script>
{% endblock %}
