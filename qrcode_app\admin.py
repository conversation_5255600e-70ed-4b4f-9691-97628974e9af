from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from .models import (
    QRCode, UserProfile, APIKey, QRCodeBatch, QRCodeAnalytics,
    QRCodeScan, QRCodeBranding, QRLink, QRGeoTarget, QRScanLog, DynamicQRRedirect, AILandingPage,
    ScanEvent, ScanAlert, WebhookEndpoint, Plan, Subscription, StripeProduct
)

@admin.register(QRCode)
class QRCodeAdmin(admin.ModelAdmin):
    list_display = ('name', 'user', 'qr_type', 'created_at', 'is_encrypted')
    list_filter = ('qr_type', 'is_encrypted', 'created_at')
    search_fields = ('name', 'data', 'user__username')
    readonly_fields = ('created_at', 'updated_at')

@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'role', 'company', 'created_at')
    list_filter = ('role', 'created_at')
    search_fields = ('user__username', 'user__email', 'company')
    readonly_fields = ('api_key', 'created_at', 'updated_at')

@admin.register(APIKey)
class APIKeyAdmin(admin.ModelAdmin):
    list_display = ('name', 'user', 'is_active', 'created_at', 'last_used')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name', 'user__username')
    readonly_fields = ('key', 'created_at', 'last_used')

@admin.register(QRCodeBatch)
class QRCodeBatchAdmin(admin.ModelAdmin):
    list_display = ('name', 'user', 'count', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('name', 'description', 'user__username')


# Enterprise Admin Configurations

@admin.register(QRCodeScan)
class QRCodeScanAdmin(admin.ModelAdmin):
    list_display = (
        'qr_code_name', 'scanned_at', 'location_display', 'organization_display',
        'scanner_type', 'device_type', 'security_flags'
    )
    list_filter = (
        'scanner_type', 'device_type', 'country', 'scanned_at'
    )
    search_fields = (
        'qr_code__name', 'ip_address', 'city', 'country',
        'organization', 'user_agent'
    )
    readonly_fields = (
        'qr_code', 'scanned_at', 'ip_address', 'user_agent', 'referrer',
        'country', 'city', 'region', 'latitude', 'longitude', 'timezone',
        'postal_code', 'organization', 'asn', 'privacy_flags',
        'scanner_type', 'device_type', 'os', 'browser'
    )
    date_hierarchy = 'scanned_at'

    def qr_code_name(self, obj):
        return obj.qr_code.name
    qr_code_name.short_description = 'QR Code'
    qr_code_name.admin_order_field = 'qr_code__name'

    def location_display(self, obj):
        parts = []
        if obj.city:
            parts.append(obj.city)
        if obj.country:
            parts.append(obj.country)
        location = ', '.join(parts) if parts else 'Unknown'

        if obj.latitude and obj.longitude:
            return format_html(
                '{}<br><small style="color: #666;">📍 {:.4f}, {:.4f}</small>',
                location, float(obj.latitude), float(obj.longitude)
            )
        return location
    location_display.short_description = 'Location'

    def organization_display(self, obj):
        if obj.organization and obj.organization != 'Unknown':
            org_text = obj.organization[:30] + '...' if len(obj.organization) > 30 else obj.organization
            if obj.asn:
                return format_html(
                    '{}<br><small style="color: #666;">ASN: {}</small>',
                    org_text, obj.asn
                )
            return org_text
        return 'Unknown'
    organization_display.short_description = 'Organization/ISP'

    def security_flags(self, obj):
        flags = []
        privacy = obj.privacy_flags or {}

        if privacy.get('vpn'):
            flags.append('<span style="background: #dc3545; color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px;">VPN</span>')
        if privacy.get('proxy'):
            flags.append('<span style="background: #fd7e14; color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px;">PROXY</span>')
        if privacy.get('tor'):
            flags.append('<span style="background: #6f42c1; color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px;">TOR</span>')
        if privacy.get('hosting'):
            flags.append('<span style="background: #20c997; color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px;">HOSTING</span>')

        return format_html(' '.join(flags)) if flags else '-'
    security_flags.short_description = 'Security Flags'


@admin.register(QRGeoTarget)
class QRGeoTargetAdmin(admin.ModelAdmin):
    list_display = (
        'qr_code', 'country_code', 'region', 'city', 'redirect_url_display',
        'priority', 'is_active'
    )
    list_filter = ('country_code', 'is_active', 'created_at')
    search_fields = ('qr_code__name', 'country_code', 'region', 'city', 'redirect_url')
    list_editable = ('priority', 'is_active')

    def redirect_url_display(self, obj):
        url = obj.redirect_url
        if len(url) > 40:
            url = url[:37] + '...'
        return format_html('<a href="{}" target="_blank">{}</a>', obj.redirect_url, url)
    redirect_url_display.short_description = 'Redirect URL'


@admin.register(QRScanLog)
class QRScanLogAdmin(admin.ModelAdmin):
    """
    Simple QR scan log admin interface as requested in prompt
    """
    list_display = ("code", "timestamp", "ip_address", "country", "city", "org")
    search_fields = ("code", "ip_address", "city", "org")
    list_filter = ("country", "timestamp")
    readonly_fields = ("code", "timestamp", "ip_address", "user_agent", "country", "city", "org")
    date_hierarchy = 'timestamp'

    def has_add_permission(self, request):
        # Prevent manual addition - these should only be created automatically
        return False

    def has_change_permission(self, request, obj=None):
        # Make read-only
        return False


@admin.register(DynamicQRRedirect)
class DynamicQRRedirectAdmin(admin.ModelAdmin):
    """
    Admin interface for Dynamic QR Redirects - Premium monetization feature
    """
    list_display = ("qr_code", "current_url", "redirect_count", "max_redirects", "total_clicks", "is_active")
    search_fields = ("qr_code__name", "current_url")
    list_filter = ("is_active", "enable_analytics", "enable_geo_redirect", "created_at")
    readonly_fields = ("redirect_count", "total_clicks", "last_accessed", "created_at", "updated_at")

    fieldsets = (
        ('QR Code', {
            'fields': ('qr_code',)
        }),
        ('Redirect Settings', {
            'fields': ('current_url', 'backup_url', 'is_active')
        }),
        ('Limits & Analytics', {
            'fields': ('redirect_count', 'max_redirects', 'total_clicks', 'last_accessed')
        }),
        ('Premium Features', {
            'fields': ('enable_analytics', 'enable_geo_redirect'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )


@admin.register(AILandingPage)
class AILandingPageAdmin(admin.ModelAdmin):
    """
    Admin interface for AI Landing Pages - MODULE 3
    """
    list_display = ("title", "user", "qr_code", "page_type", "status", "view_count", "created_at")
    search_fields = ("title", "user__username", "qr_code__name", "prompt")
    list_filter = ("page_type", "status", "is_published", "created_at", "ai_model_used")
    readonly_fields = ("view_count", "generation_time", "ai_model_used", "created_at", "updated_at", "published_at")

    fieldsets = (
        ('Basic Information', {
            'fields': ('user', 'qr_code', 'title', 'page_type', 'status', 'is_published')
        }),
        ('Content', {
            'fields': ('prompt', 'content'),
            'classes': ('collapse',)
        }),
        ('Styling', {
            'fields': ('primary_color', 'secondary_color', 'font_family'),
            'classes': ('collapse',)
        }),
        ('AI Generation', {
            'fields': ('ai_model_used', 'generation_time'),
            'classes': ('collapse',)
        }),
        ('Analytics', {
            'fields': ('view_count', 'created_at', 'updated_at', 'published_at'),
            'classes': ('collapse',)
        })
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'qr_code')

    def save_model(self, request, obj, form, change):
        if not change:  # If creating new
            obj.user = request.user
        super().save_model(request, obj, form, change)


@admin.register(ScanEvent)
class ScanEventAdmin(admin.ModelAdmin):
    """
    MODULE 4: Smart Scan Alerts - Admin interface for Scan Events
    """
    list_display = ("qr_code", "timestamp", "location", "org", "device_type", "alerts_triggered", "processed_for_alerts")
    search_fields = ("qr_code__name", "ip", "location", "org", "user_agent")
    list_filter = ("device_type", "browser", "country_code", "processed_for_alerts", "timestamp")
    readonly_fields = ("qr_code", "timestamp", "ip", "location", "org", "user_agent", "device_type", "browser", "country_code", "alerts_triggered")
    date_hierarchy = 'timestamp'

    def has_add_permission(self, request):
        # Prevent manual addition - these should only be created automatically
        return False

    def has_change_permission(self, request, obj=None):
        # Allow changing only the processed_for_alerts field
        return True

    def get_readonly_fields(self, request, obj=None):
        # Make most fields readonly, allow editing processed_for_alerts
        readonly = list(self.readonly_fields)
        if obj:  # Editing existing object
            return readonly
        return readonly


@admin.register(ScanAlert)
class ScanAlertAdmin(admin.ModelAdmin):
    """
    MODULE 4: Smart Scan Alerts - Admin interface for Scan Alerts
    """
    list_display = ("user", "qr_code", "alert_type", "keyword", "email", "is_active", "triggered_count", "alerts_sent_today")
    search_fields = ("user__username", "qr_code__name", "keyword", "email")
    list_filter = ("alert_type", "is_active", "send_immediately", "created_at")
    readonly_fields = ("triggered_count", "last_triggered", "alerts_sent_today", "last_reset_date", "created_at", "updated_at")

    fieldsets = (
        ('Alert Configuration', {
            'fields': ('user', 'qr_code', 'alert_type', 'keyword', 'email')
        }),
        ('Settings', {
            'fields': ('is_active', 'send_immediately', 'max_alerts_per_day')
        }),
        ('Statistics', {
            'fields': ('triggered_count', 'last_triggered', 'alerts_sent_today', 'last_reset_date'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'qr_code')


@admin.register(WebhookEndpoint)
class WebhookEndpointAdmin(admin.ModelAdmin):
    """
    MODULE 5: Webhook Integration - Admin interface for Webhook Endpoints
    """
    list_display = ("user", "qr_code", "url_display", "active", "total_calls", "success_rate", "last_called")
    search_fields = ("user__username", "qr_code__name", "url")
    list_filter = ("active", "trigger_on_scan", "trigger_on_alert", "created_at")
    readonly_fields = ("total_calls", "successful_calls", "last_called", "last_error", "created_at", "updated_at")

    fieldsets = (
        ('Webhook Configuration', {
            'fields': ('user', 'qr_code', 'url', 'secret_key')
        }),
        ('Trigger Settings', {
            'fields': ('trigger_on_scan', 'trigger_on_alert', 'active')
        }),
        ('Statistics', {
            'fields': ('total_calls', 'successful_calls', 'last_called', 'last_error'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def url_display(self, obj):
        """Display truncated URL"""
        if len(obj.url) > 50:
            return f"{obj.url[:47]}..."
        return obj.url
    url_display.short_description = 'Webhook URL'

    def success_rate(self, obj):
        """Display success rate with color coding"""
        rate = obj.get_success_rate()
        if rate >= 90:
            color = 'green'
        elif rate >= 70:
            color = 'orange'
        else:
            color = 'red'
        return format_html(
            '<span style="color: {};">{:.1f}%</span>',
            color, rate
        )
    success_rate.short_description = 'Success Rate'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'qr_code')


@admin.register(Plan)
class PlanAdmin(admin.ModelAdmin):
    """
    MODULE 6: Usage Limits & Tiered Features - Admin interface for Subscription Plans
    """
    list_display = (
        "name", "plan_type", "price", "yearly_price", "max_qr_codes", "max_scans_per_month",
        "feature_summary", "is_active", "is_default", "sort_order"
    )
    list_filter = ("plan_type", "is_active", "is_default", "ai_enabled", "alerts_enabled", "webhooks_enabled")
    search_fields = ("name", "description")
    list_editable = ("is_active", "is_default", "sort_order")
    ordering = ("sort_order", "price")

    fieldsets = (
        ('Plan Information', {
            'fields': ('name', 'plan_type', 'description', 'is_active', 'is_default', 'sort_order')
        }),
        ('Pricing', {
            'fields': ('price', 'yearly_price')
        }),
        ('Usage Limits', {
            'fields': (
                'max_qr_codes', 'max_scans_per_month', 'max_dynamic_redirects',
                'max_ai_pages', 'max_webhooks', 'max_scan_alerts'
            )
        }),
        ('Feature Flags', {
            'fields': (
                'ai_enabled', 'alerts_enabled', 'webhooks_enabled', 'analytics_enabled',
                'advanced_analytics_enabled', 'branding_enabled', 'batch_processing_enabled',
                'api_access_enabled', 'priority_support_enabled'
            ),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    readonly_fields = ('created_at', 'updated_at')

    def feature_summary(self, obj):
        """Display summary of enabled features"""
        features = []
        if obj.ai_enabled:
            features.append('<span style="background: #28a745; color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px;">AI</span>')
        if obj.alerts_enabled:
            features.append('<span style="background: #ffc107; color: black; padding: 2px 6px; border-radius: 3px; font-size: 11px;">ALERTS</span>')
        if obj.webhooks_enabled:
            features.append('<span style="background: #17a2b8; color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px;">WEBHOOKS</span>')
        if obj.advanced_analytics_enabled:
            features.append('<span style="background: #6f42c1; color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px;">ANALYTICS</span>')
        if obj.branding_enabled:
            features.append('<span style="background: #fd7e14; color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px;">BRANDING</span>')
        if obj.batch_processing_enabled:
            features.append('<span style="background: #20c997; color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px;">BATCH</span>')
        if obj.api_access_enabled:
            features.append('<span style="background: #dc3545; color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px;">API</span>')

        return format_html(' '.join(features)) if features else '-'
    feature_summary.short_description = 'Features'

    def save_model(self, request, obj, form, change):
        # Ensure only one default plan
        if obj.is_default:
            Plan.objects.filter(is_default=True).exclude(pk=obj.pk).update(is_default=False)
        super().save_model(request, obj, form, change)


@admin.register(Subscription)
class SubscriptionAdmin(admin.ModelAdmin):
    """
    MODULE 6: Usage Limits & Tiered Features - Admin interface for User Subscriptions
    """
    list_display = (
        "user", "plan", "status", "usage_summary", "scans_this_month",
        "started_at", "expires_at", "billing_status"
    )
    list_filter = ("status", "plan__plan_type", "started_at", "expires_at")
    search_fields = ("user__username", "user__email", "plan__name", "stripe_customer_id")
    readonly_fields = (
        "total_qr_codes_created", "total_dynamic_redirects", "total_ai_pages",
        "total_webhooks", "total_scan_alerts", "last_scan_reset", "created_at", "updated_at"
    )
    date_hierarchy = 'started_at'

    fieldsets = (
        ('Subscription Details', {
            'fields': ('user', 'plan', 'status', 'started_at', 'expires_at', 'cancelled_at')
        }),
        ('Usage Tracking (Monthly)', {
            'fields': ('scans_this_month', 'last_scan_reset')
        }),
        ('Usage Tracking (Total)', {
            'fields': (
                'total_qr_codes_created', 'total_dynamic_redirects', 'total_ai_pages',
                'total_webhooks', 'total_scan_alerts'
            ),
            'classes': ('collapse',)
        }),
        ('Billing Integration', {
            'fields': ('stripe_subscription_id', 'stripe_customer_id'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def usage_summary(self, obj):
        """Display usage summary with progress bars"""
        if not obj.plan:
            return "No Plan"

        summary = obj.get_usage_summary()
        html_parts = []

        # QR Codes usage
        qr_usage = summary.get('qr_codes', {})
        qr_current = qr_usage.get('current', 0)
        qr_limit = qr_usage.get('limit', 0)
        qr_percentage = qr_usage.get('percentage', 0)

        if qr_limit > 0:
            color = 'green' if qr_percentage < 80 else 'orange' if qr_percentage < 95 else 'red'
            html_parts.append(f'<div style="margin-bottom: 4px;"><strong>QR Codes:</strong> {qr_current}/{qr_limit} <span style="color: {color};">({qr_percentage:.0f}%)</span></div>')

        # Scans usage
        scans_usage = summary.get('scans', {})
        scans_current = scans_usage.get('current', 0)
        scans_limit = scans_usage.get('limit', 0)
        scans_percentage = scans_usage.get('percentage', 0)

        if scans_limit > 0:
            color = 'green' if scans_percentage < 80 else 'orange' if scans_percentage < 95 else 'red'
            html_parts.append(f'<div><strong>Scans:</strong> {scans_current}/{scans_limit} <span style="color: {color};">({scans_percentage:.0f}%)</span></div>')

        return format_html(''.join(html_parts)) if html_parts else "No usage data"
    usage_summary.short_description = 'Usage Summary'

    def billing_status(self, obj):
        """Display billing status"""
        if obj.stripe_subscription_id:
            return format_html(
                '<span style="background: #28a745; color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px;">STRIPE</span>'
            )
        elif obj.status == 'ACTIVE':
            return format_html(
                '<span style="background: #6c757d; color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px;">MANUAL</span>'
            )
        else:
            return format_html(
                '<span style="background: #dc3545; color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px;">INACTIVE</span>'
            )
    billing_status.short_description = 'Billing'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'plan')

    actions = ['reset_monthly_scans', 'activate_subscription', 'cancel_subscription']

    def reset_monthly_scans(self, request, queryset):
        """Reset monthly scan counts for selected subscriptions"""
        count = 0
        for subscription in queryset:
            subscription.reset_monthly_scans()
            count += 1
        self.message_user(request, f'Reset monthly scans for {count} subscriptions.')
    reset_monthly_scans.short_description = 'Reset monthly scan counts'

    def activate_subscription(self, request, queryset):
        """Activate selected subscriptions"""
        count = queryset.update(status='ACTIVE')
        self.message_user(request, f'Activated {count} subscriptions.')
    activate_subscription.short_description = 'Activate subscriptions'

    def cancel_subscription(self, request, queryset):
        """Cancel selected subscriptions"""
        from django.utils import timezone
        count = queryset.update(status='CANCELLED', cancelled_at=timezone.now())
        self.message_user(request, f'Cancelled {count} subscriptions.')
    cancel_subscription.short_description = 'Cancel subscriptions'


@admin.register(StripeProduct)
class StripeProductAdmin(admin.ModelAdmin):
    """
    MODULE 6: Stripe Integration - Admin interface for Stripe Products
    """
    list_display = ("plan", "stripe_product_id", "stripe_price_id", "stripe_yearly_price_id", "is_active", "created_at")
    list_filter = ("is_active", "plan__plan_type")
    search_fields = ("plan__name", "stripe_product_id", "stripe_price_id")
    readonly_fields = ("created_at", "updated_at")

    fieldsets = (
        ('Plan Association', {
            'fields': ('plan',)
        }),
        ('Stripe Configuration', {
            'fields': ('stripe_product_id', 'stripe_price_id', 'stripe_yearly_price_id', 'is_active')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('plan')