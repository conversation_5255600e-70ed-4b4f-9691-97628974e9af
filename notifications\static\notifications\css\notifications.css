/**
 * Notifications CSS
 * Styles for the notification system
 */

/* Notification Container */
.notification-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.notification-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-dark);
    margin: 0;
}

.notification-actions {
    display: flex;
    gap: 0.5rem;
}

/* Notification Filters */
.notification-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background-color: var(--bg-light);
    border-radius: var(--radius-md);
}

/* Notification List */
.notification-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    padding: 1.25rem;
    background-color: var(--bg-white);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    border-left: 4px solid var(--primary-color);
}

.notification-item:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.notification-item.unread {
    background-color: rgba(var(--primary-rgb), 0.05);
}

.notification-icon {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(var(--primary-rgb), 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
}

.notification-icon i {
    font-size: 1.25rem;
    color: var(--primary-color);
}

/* Notification Types */
.notification-item.success .notification-icon {
    background-color: rgba(var(--success-rgb), 0.1);
}

.notification-item.success .notification-icon i {
    color: var(--success-color);
}

.notification-item.success {
    border-left-color: var(--success-color);
}

.notification-item.warning .notification-icon {
    background-color: rgba(var(--warning-rgb), 0.1);
}

.notification-item.warning .notification-icon i {
    color: var(--warning-color);
}

.notification-item.warning {
    border-left-color: var(--warning-color);
}

.notification-item.error .notification-icon {
    background-color: rgba(var(--danger-rgb), 0.1);
}

.notification-item.error .notification-icon i {
    color: var(--danger-color);
}

.notification-item.error {
    border-left-color: var(--danger-color);
}

/* Notification Content */
.notification-content {
    flex-grow: 1;
}

.notification-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
}

.notification-message {
    font-size: 0.95rem;
    color: var(--text-light);
    margin-bottom: 0.75rem;
}

.notification-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 0.85rem;
    color: var(--text-muted);
}

.notification-category {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    background-color: rgba(var(--primary-rgb), 0.1);
    border-radius: var(--radius-sm);
    color: var(--primary-color);
    font-weight: 500;
}

.notification-time {
    display: inline-flex;
    align-items: center;
}

.notification-time i {
    margin-right: 0.25rem;
}

/* Notification Actions */
.notification-actions-menu {
    flex-shrink: 0;
    margin-left: 1rem;
}

.notification-action-btn {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: var(--transition);
}

.notification-action-btn:hover {
    background-color: rgba(var(--primary-rgb), 0.1);
    color: var(--primary-color);
}

/* Notification Dropdown */
.notification-dropdown {
    position: relative;
}

.notification-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    z-index: 10;
    min-width: 180px;
    background-color: var(--bg-white);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    padding: 0.5rem 0;
    display: none;
}

.notification-dropdown-menu.show {
    display: block;
}

.notification-dropdown-item {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    color: var(--text-dark);
    text-decoration: none;
    transition: var(--transition);
}

.notification-dropdown-item:hover {
    background-color: rgba(var(--primary-rgb), 0.05);
}

.notification-dropdown-item i {
    margin-right: 0.5rem;
    width: 1rem;
    text-align: center;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
}

.empty-state-icon {
    font-size: 3rem;
    color: var(--text-muted);
    margin-bottom: 1rem;
}

.empty-state-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.empty-state-description {
    font-size: 1rem;
    color: var(--text-light);
    max-width: 500px;
    margin: 0 auto 1.5rem;
}

/* Notification Detail */
.notification-detail-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

.notification-detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.notification-detail-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-dark);
    margin: 0;
}

.notification-detail-actions {
    display: flex;
    gap: 0.5rem;
}

.notification-detail-card {
    background-color: var(--bg-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

.notification-detail-header-bar {
    height: 8px;
    background-color: var(--primary-color);
}

.notification-detail-header-bar.success {
    background-color: var(--success-color);
}

.notification-detail-header-bar.warning {
    background-color: var(--warning-color);
}

.notification-detail-header-bar.error {
    background-color: var(--danger-color);
}

.notification-detail-content {
    padding: 2rem;
}

.notification-detail-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.notification-detail-meta-item {
    display: flex;
    flex-direction: column;
}

.notification-detail-meta-label {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-bottom: 0.25rem;
}

.notification-detail-meta-value {
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-dark);
    display: flex;
    align-items: center;
}

.notification-detail-meta-value i {
    margin-right: 0.5rem;
}

.notification-detail-message {
    font-size: 1.1rem;
    line-height: 1.6;
    color: var(--text-dark);
    margin-bottom: 2rem;
}

.notification-detail-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.notification-detail-related {
    margin-top: 2rem;
}

.notification-detail-related-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.notification-detail-related-content {
    background-color: var(--bg-light);
    border-radius: var(--radius-md);
    padding: 1.5rem;
}

/* Notification Badge */
.notification-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.35rem 0.75rem;
    border-radius: 50px;
    font-size: 0.875rem;
    font-weight: 500;
}

.notification-badge.info {
    background-color: rgba(var(--primary-rgb), 0.1);
    color: var(--primary-color);
}

.notification-badge.success {
    background-color: rgba(var(--success-rgb), 0.1);
    color: var(--success-color);
}

.notification-badge.warning {
    background-color: rgba(var(--warning-rgb), 0.1);
    color: var(--warning-color);
}

.notification-badge.error {
    background-color: rgba(var(--danger-rgb), 0.1);
    color: var(--danger-color);
}

.notification-badge i {
    margin-right: 0.35rem;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .notification-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .notification-detail-meta {
        flex-direction: column;
        gap: 1rem;
    }
    
    .notification-detail-footer {
        flex-direction: column;
        gap: 1rem;
    }
}
