{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimal Dropdown Test</title>

    <!-- Only essential CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{% static 'css/new_nav.css' %}">
    <link rel="stylesheet" href="{% static 'css/new_nav_dropdowns.css' %}">

    <style>
        body {
            margin: 0;
            padding: 0;
            background: #f8f9fa;
            font-family: 'Inter', sans-serif;
        }

        .test-content {
            padding: 100px 20px 50px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .test-card {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        /* CSS DEBUG: Force dropdown to be visible when .show class is added */
        .new-navbar-dropdown.show {
            display: block !important;
            opacity: 1 !important;
            transform: translateY(0) !important;
            visibility: visible !important;
            background: white !important;
            border: 2px solid red !important; /* Debug border */
            z-index: 9999 !important;
        }

        /* CSS DEBUG: Make dropdown always visible for testing */
        .new-navbar-dropdown {
            background: white !important;
            border: 1px solid #ccc !important;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1) !important;
            min-width: 200px !important;
            padding: 10px 0 !important;
        }
    </style>
</head>
<body>
    <!-- Minimal navbar with just one dropdown -->
    <nav class="new-navbar">
        <div class="container d-flex justify-content-between align-items-center">
            <!-- Left Section with Brand/Logo -->
            <div class="navbar-left">
                <a href="/" class="new-navbar-brand">
                    <div class="new-navbar-logo">
                        <i class="fas fa-qrcode"></i>
                    </div>
                    <span class="new-navbar-brand-text">Enterprise <span>QR</span></span>
                </a>
            </div>

            <!-- Navigation Menu -->
            <div class="new-navbar-nav">
                <!-- Main Navigation -->
                <ul class="new-navbar-nav-main">
                    <!-- Test Dropdown -->
                    <li class="new-navbar-item has-dropdown">
                        <a href="#" class="new-navbar-link" aria-expanded="false">
                            <i class="fas fa-shield-alt new-navbar-icon"></i>
                            <span>Test Dropdown</span>
                        </a>
                        <div class="new-navbar-dropdown">
                            <div class="new-navbar-dropdown-header">Test Menu</div>
                            <ul class="new-navbar-dropdown-menu">
                                <li>
                                    <a href="#" class="new-navbar-dropdown-item">
                                        <i class="fas fa-users-cog new-navbar-dropdown-icon"></i>
                                        <span>Test Item 1</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="#" class="new-navbar-dropdown-item">
                                        <i class="fas fa-user-tag new-navbar-dropdown-icon"></i>
                                        <span>Test Item 2</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="#" class="new-navbar-dropdown-item">
                                        <i class="fas fa-key new-navbar-dropdown-icon"></i>
                                        <span>Test Item 3</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="test-content">
        <div class="test-card">
            <h1><i class="fas fa-bug"></i> Minimal Dropdown Test</h1>
            <p class="lead">This is a minimal test with only essential scripts to isolate the dropdown issue.</p>

            <div id="test-results">
                <p>Loading test results...</p>
            </div>
        </div>
    </div>

    <!-- Only essential JavaScript -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Minimal dropdown script -->
    <script>
        console.log('🧪 MINIMAL TEST: Starting...');

        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 MINIMAL TEST: DOM loaded');

            const resultsDiv = document.getElementById('test-results');
            const dropdownItems = document.querySelectorAll('.new-navbar-item.has-dropdown');

            resultsDiv.innerHTML = `
                <h3>Test Results:</h3>
                <p><strong>Dropdown items found:</strong> ${dropdownItems.length}</p>
            `;

            if (dropdownItems.length > 0) {
                const item = dropdownItems[0];
                const link = item.querySelector('.new-navbar-link');
                const dropdown = item.querySelector('.new-navbar-dropdown');

                resultsDiv.innerHTML += `
                    <p><strong>Link found:</strong> ${!!link}</p>
                    <p><strong>Dropdown found:</strong> ${!!dropdown}</p>
                `;

                if (link && dropdown) {
                    // Add simple click handler
                    link.addEventListener('click', function(e) {
                        e.preventDefault();
                        console.log('🎯 MINIMAL TEST: Link clicked!');

                        dropdown.classList.toggle('show');
                        const isShown = dropdown.classList.contains('show');

                        console.log('🎯 MINIMAL TEST: Dropdown show class:', isShown);

                        resultsDiv.innerHTML += `<p><strong>Click test:</strong> ${isShown ? 'SUCCESS - Dropdown shown!' : 'FAILED - Dropdown not shown'}</p>`;
                    });

                    // Add hover handler
                    item.addEventListener('mouseenter', function() {
                        console.log('🎯 MINIMAL TEST: Mouse enter');
                        dropdown.classList.add('show');
                    });

                    item.addEventListener('mouseleave', function() {
                        console.log('🎯 MINIMAL TEST: Mouse leave');
                        setTimeout(() => {
                            dropdown.classList.remove('show');
                        }, 200);
                    });

                    resultsDiv.innerHTML += `<p><strong>Event listeners:</strong> Added successfully</p>`;
                    resultsDiv.innerHTML += `<p><strong>Instructions:</strong> Try hovering over or clicking the "Test Dropdown" in the navbar above.</p>`;
                }
            }
        });
    </script>
</body>
</html>
