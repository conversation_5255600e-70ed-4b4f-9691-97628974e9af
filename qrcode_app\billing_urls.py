"""
MODULE 6: Billing URLs for Stripe integration
"""

from django.urls import path
from . import billing_views

urlpatterns = [
    # Pricing and subscription
    path('pricing/', billing_views.pricing_page, name='pricing'),
    path('checkout/<int:plan_id>/', billing_views.create_checkout_session, name='create_checkout_session'),
    
    # Billing flow
    path('success/', billing_views.billing_success, name='billing_success'),
    path('cancel/', billing_views.billing_cancel, name='billing_cancel'),
    path('portal/', billing_views.billing_portal, name='billing_portal'),
    
    # Stripe webhook
    path('webhook/', billing_views.stripe_webhook, name='stripe_webhook'),
]
