{% load static %}

<!-- Enterprise Header Ads Banner -->
{% if ads %}
    <div class="enterprise-header-ads">
        <div class="enterprise-header-ads-container">
            <div class="enterprise-header-ads-label">
                <i class="fas fa-bullhorn"></i>
                <span>Sponsored</span>
                <span class="ads-count">({{ ads|length }} ads)</span>
            </div>

            <!-- Use carousel for many ads (6+), grid for fewer ads -->
            {% if ads|length >= 6 %}
            <div class="enterprise-header-ads-carousel" id="header-ads-carousel">
                <div class="enterprise-header-ads-track" id="header-ads-track">
                    {% for ad in ads %}
                    <div class="enterprise-header-ad-slide" data-ad-id="{{ ad.id }}" data-ad-type="{{ ad.ad_type.name }}" data-ad-location="header">
                        {% if ad.media and ad.media.name %}
                        <div class="enterprise-header-ad-image">
                            <img src="/media/{{ ad.media }}" alt="{{ ad.title }}">
                        </div>
                        {% endif %}

                        <div class="enterprise-header-ad-content">
                            <h4 class="enterprise-header-ad-title">{{ ad.title }}</h4>
                            <p class="enterprise-header-ad-description">{{ ad.content|truncatechars:60 }}</p>

                            {% if ad.cta_link %}
                            <a href="{{ ad.cta_link }}" target="_blank" class="enterprise-header-ad-cta" rel="noopener" onclick="trackAdClick('{{ ad.id }}')">
                                <span>Learn More</span>
                                <i class="fas fa-external-link-alt"></i>
                            </a>
                            {% else %}
                            <div class="enterprise-header-ad-cta enterprise-header-ad-cta-disabled">
                                <span>No Link</span>
                                <i class="fas fa-link-slash"></i>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- Carousel Navigation -->
                <button class="carousel-nav carousel-prev" onclick="slideHeaderAds(-1)">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button class="carousel-nav carousel-next" onclick="slideHeaderAds(1)">
                    <i class="fas fa-chevron-right"></i>
                </button>

                <!-- Carousel Indicators -->
                <div class="carousel-indicators">
                    {% for ad in ads %}
                    <button class="carousel-indicator {% if forloop.first %}active{% endif %}" onclick="goToSlide({{ forloop.counter0 }})"></button>
                    {% endfor %}
                </div>
            </div>
            {% else %}
            <div class="enterprise-header-ads-grid">
                {% for ad in ads %}
                <div class="enterprise-header-ad-card" data-ad-id="{{ ad.id }}" data-ad-type="{{ ad.ad_type.name }}" data-ad-location="header">
                    {% if ad.media and ad.media.name %}
                    <div class="enterprise-header-ad-image">
                        <img src="/media/{{ ad.media }}" alt="{{ ad.title }}">
                    </div>
                    {% endif %}

                    <div class="enterprise-header-ad-content">
                        <h4 class="enterprise-header-ad-title">{{ ad.title }}</h4>
                        <p class="enterprise-header-ad-description">{{ ad.content|truncatechars:60 }}</p>

                        <a href="{{ ad.cta_link }}" target="_blank" class="enterprise-header-ad-cta" rel="noopener" onclick="trackAdClick('{{ ad.id }}')">
                            <span>Learn More</span>
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% endif %}

            <!-- Pagination Controls for Grid Mode -->
            <div class="enterprise-header-ads-pagination" id="header-ads-pagination" style="display: none;">
                <div class="pagination-controls">
                    <button class="pagination-btn pagination-prev" onclick="prevPage()" disabled>
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <span class="pagination-info">1 of 1</span>
                    <button class="pagination-btn pagination-next" onclick="nextPage()" disabled>
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>

            </div>

            <!-- Legacy Toggle for Expand/Collapse -->
            <div class="enterprise-header-ads-more" id="header-ads-toggle-section" style="display: none;">
                <button class="enterprise-header-ads-toggle" onclick="toggleMoreHeaderAds()">
                    <span class="toggle-text">Show More</span>
                    <i class="fas fa-chevron-down toggle-icon"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Enterprise Header Ads Script -->
    <script>
        let currentSlide = 0;
        const totalAds = {{ ads|length }};
        const isCarouselMode = totalAds >= 6;

        // Carousel functions for many ads
        function slideHeaderAds(direction) {
            if (!isCarouselMode) return;

            const track = document.getElementById('header-ads-track');
            const indicators = document.querySelectorAll('.carousel-indicator');

            // Remove active indicator
            indicators[currentSlide].classList.remove('active');

            // Calculate new slide
            currentSlide += direction;
            if (currentSlide >= totalAds) currentSlide = 0;
            if (currentSlide < 0) currentSlide = totalAds - 1;

            // Move track
            const slideWidth = 100;
            track.style.transform = `translateX(-${currentSlide * slideWidth}%)`;

            // Add active indicator
            indicators[currentSlide].classList.add('active');

            // Track impression for newly visible ad
            const slides = document.querySelectorAll('.enterprise-header-ad-slide');
            const adId = slides[currentSlide].getAttribute('data-ad-id');
            trackAdImpression(adId);
        }

        function goToSlide(index) {
            if (!isCarouselMode) return;

            const direction = index - currentSlide;
            currentSlide = index;
            slideHeaderAds(0); // Use 0 to just update position without changing currentSlide
        }

        // Enhanced pagination for header ads
        let currentPage = 0;
        const adsPerPage = 3;
        const totalPages = Math.ceil(totalAds / adsPerPage);

        function showAdsPage(pageIndex) {
            if (isCarouselMode) return; // Don't use pagination in carousel mode

            const allCards = document.querySelectorAll('.enterprise-header-ad-card');
            const startIndex = pageIndex * adsPerPage;
            const endIndex = startIndex + adsPerPage;

            // Hide all cards
            allCards.forEach((card, index) => {
                if (index >= startIndex && index < endIndex) {
                    card.style.display = 'flex';
                } else {
                    card.style.display = 'none';
                }
            });

            // Update pagination controls
            updatePaginationControls();
        }

        function nextPage() {
            if (currentPage < totalPages - 1) {
                currentPage++;
                showAdsPage(currentPage);
            }
        }

        function prevPage() {
            if (currentPage > 0) {
                currentPage--;
                showAdsPage(currentPage);
            }
        }

        function updatePaginationControls() {
            const prevBtn = document.querySelector('.pagination-prev');
            const nextBtn = document.querySelector('.pagination-next');
            const pageInfo = document.querySelector('.pagination-info');

            if (prevBtn) prevBtn.disabled = currentPage === 0;
            if (nextBtn) nextBtn.disabled = currentPage === totalPages - 1;
            if (pageInfo) pageInfo.textContent = `${currentPage + 1} of ${totalPages}`;
        }

        // Grid toggle functions for fewer ads (legacy support)
        function toggleMoreHeaderAds() {
            if (isCarouselMode) return; // Don't use toggle in carousel mode

            const grid = document.querySelector('.enterprise-header-ads-grid');
            const toggle = document.querySelector('.enterprise-header-ads-toggle');
            const toggleText = toggle.querySelector('.toggle-text');
            const toggleIcon = toggle.querySelector('.toggle-icon');

            if (grid.classList.contains('expanded')) {
                grid.classList.remove('expanded');
                toggleText.textContent = 'Show More';
                toggleIcon.classList.remove('fa-chevron-up');
                toggleIcon.classList.add('fa-chevron-down');
                // Reset to first page when collapsing
                currentPage = 0;
                showAdsPage(currentPage);
            } else {
                grid.classList.add('expanded');
                toggleText.textContent = 'Show Less';
                toggleIcon.classList.remove('fa-chevron-down');
                toggleIcon.classList.add('fa-chevron-up');
                // Show all ads when expanding
                const allCards = document.querySelectorAll('.enterprise-header-ad-card');
                allCards.forEach(card => card.style.display = 'flex');
            }
        }

        function checkHeaderAdsControls() {
            const paginationSection = document.getElementById('header-ads-pagination');
            const toggleSection = document.getElementById('header-ads-toggle-section');

            // Hide both controls in carousel mode (6+ ads)
            if (isCarouselMode) {
                paginationSection.style.display = 'none';
                toggleSection.style.display = 'none';
                return;
            }

            // For grid mode, use pagination if more than 3 ads
            if (totalAds > 3) {
                paginationSection.style.display = 'block';
                toggleSection.style.display = 'none';
                // Initialize pagination
                showAdsPage(0);
            } else {
                paginationSection.style.display = 'none';
                toggleSection.style.display = 'none';
                // Show all ads if 3 or fewer
                const allCards = document.querySelectorAll('.enterprise-header-ad-card');
                allCards.forEach(card => card.style.display = 'flex');
            }
        }

        // Auto-rotation for carousel mode
        function startAutoRotation() {
            if (isCarouselMode && totalAds > 1) {
                setInterval(() => {
                    slideHeaderAds(1);
                }, 4000); // Rotate every 4 seconds
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            checkHeaderAdsControls();

            if (isCarouselMode) {
                startAutoRotation();
                // Track impression for first slide
                const firstSlide = document.querySelector('.enterprise-header-ad-slide');
                if (firstSlide) {
                    const adId = firstSlide.getAttribute('data-ad-id');
                    trackAdImpression(adId);
                }
            } else {
                // Track impressions for visible ads in grid mode
                setTimeout(() => {
                    const visibleCards = document.querySelectorAll('.enterprise-header-ad-card[style*="flex"]');
                    visibleCards.forEach(card => {
                        const adId = card.getAttribute('data-ad-id');
                        if (adId) trackAdImpression(adId);
                    });
                }, 100);
            }
        });

        // Re-check controls visibility on window resize
        window.addEventListener('resize', function() {
            checkHeaderAdsControls();
        });
    </script>
{% endif %}

<style>
/* Enterprise Header Ads - Compact Banner Design */
.enterprise-header-ads {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin: 15px 0;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.enterprise-header-ads-container {
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
}

.enterprise-header-ads-label {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 8px 15px;
    font-size: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 6px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    justify-content: space-between;
}

.ads-count {
    font-size: 10px;
    opacity: 0.8;
    font-weight: 400;
    text-transform: none;
    letter-spacing: normal;
}

.enterprise-header-ads-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 0;
    transition: all 0.3s ease;
}

.enterprise-header-ads-grid:not(.expanded) {
    max-height: 120px;
    overflow: hidden;
}

.enterprise-header-ads-grid.expanded {
    max-height: none;
}

/* Show first 3 ads by default, hide the rest */
.enterprise-header-ads-grid:not(.expanded) .enterprise-header-ad-card:nth-child(n+4) {
    display: none;
}

/* Carousel styles for many ads (6+) */
.enterprise-header-ads-carousel {
    position: relative;
    height: 120px;
    overflow: hidden;
}

.enterprise-header-ads-track {
    display: flex;
    transition: transform 0.5s ease;
    height: 100%;
}

.enterprise-header-ad-slide {
    flex: 0 0 100%;
    display: flex;
    align-items: center;
    padding: 15px;
    min-height: 120px;
}

.carousel-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #e9ecef;
    color: #333;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
}

.carousel-nav:hover {
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.carousel-prev {
    left: 10px;
}

.carousel-next {
    right: 10px;
}

.carousel-indicators {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 6px;
    z-index: 2;
}

.carousel-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    border: none;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;
}

.carousel-indicator.active {
    background: white;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.3);
}

.enterprise-header-ad-card {
    display: flex;
    align-items: center;
    padding: 15px;
    border-right: 1px solid #e9ecef;
    transition: background-color 0.3s ease;
    min-height: 120px;
}

.enterprise-header-ad-card:last-child {
    border-right: none;
}

.enterprise-header-ad-card:hover {
    background-color: #f1f3f4;
}

.enterprise-header-ad-image {
    flex-shrink: 0;
    margin-right: 12px;
}

.enterprise-header-ad-image img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 6px;
    border: 2px solid #e9ecef;
}

.enterprise-header-ad-content {
    flex: 1;
    min-width: 0;
}

.enterprise-header-ad-title {
    font-size: 14px;
    font-weight: 600;
    margin: 0 0 6px 0;
    color: #333;
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.enterprise-header-ad-description {
    font-size: 12px;
    color: #666;
    margin: 0 0 8px 0;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.enterprise-header-ad-cta {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    background: #007bff;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 11px;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.enterprise-header-ad-cta:hover {
    background: #0056b3;
    color: white;
    text-decoration: none;
}

/* Pagination Controls */
.enterprise-header-ads-pagination {
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    padding: 10px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

.pagination-btn {
    background: white;
    border: 1px solid #e9ecef;
    color: #007bff;
    width: 32px;
    height: 32px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 12px;
}

.pagination-btn:hover:not(:disabled) {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.pagination-btn:disabled {
    background: #f8f9fa;
    color: #6c757d;
    border-color: #e9ecef;
    cursor: not-allowed;
    opacity: 0.6;
}

.pagination-info {
    font-size: 12px;
    font-weight: 500;
    color: #333;
    min-width: 60px;
    text-align: center;
}

.pagination-summary {
    font-size: 11px;
    color: #6c757d;
}

.ads-per-page {
    font-style: italic;
}

.enterprise-header-ads-more {
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    padding: 8px 15px;
    text-align: center;
}

.enterprise-header-ads-toggle {
    background: none;
    border: none;
    color: #007bff;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
    margin: 0 auto;
    transition: color 0.3s ease;
}

.enterprise-header-ads-toggle:hover {
    color: #0056b3;
}

.toggle-icon {
    transition: transform 0.3s ease;
}

/* Mobile responsive */
@media (max-width: 768px) {
    .enterprise-header-ads-grid {
        grid-template-columns: 1fr;
    }

    .enterprise-header-ads-grid:not(.expanded) {
        max-height: 240px; /* Show 2 ads on mobile by default */
    }

    /* Show first 2 ads on mobile by default, hide the rest */
    .enterprise-header-ads-grid:not(.expanded) .enterprise-header-ad-card:nth-child(n+3) {
        display: none;
    }

    /* Carousel responsive styles */
    .enterprise-header-ads-carousel {
        height: 100px;
    }

    .enterprise-header-ad-slide {
        padding: 12px;
        min-height: 100px;
    }

    .carousel-nav {
        width: 30px;
        height: 30px;
    }

    .carousel-prev {
        left: 8px;
    }

    .carousel-next {
        right: 8px;
    }

    .carousel-indicators {
        bottom: 8px;
        gap: 4px;
    }

    .carousel-indicator {
        width: 6px;
        height: 6px;
    }

    .enterprise-header-ad-card {
        border-right: none;
        border-bottom: 1px solid #e9ecef;
        min-height: 100px;
        padding: 12px;
    }

    .enterprise-header-ad-card:last-child {
        border-bottom: none;
    }

    .enterprise-header-ad-image img {
        width: 50px;
        height: 50px;
    }

    .enterprise-header-ad-title {
        font-size: 13px;
    }

    .enterprise-header-ad-description {
        font-size: 11px;
    }

    .enterprise-header-ads-label {
        padding: 6px 12px;
        font-size: 11px;
    }
}

@media (max-width: 480px) {
    .enterprise-header-ads {
        margin: 10px 0;
        border-radius: 6px;
    }

    .enterprise-header-ads-grid:not(.expanded) {
        max-height: 180px; /* Show 1.5 ads on very small screens */
    }

    /* Show only first ad on very small screens by default */
    .enterprise-header-ads-grid:not(.expanded) .enterprise-header-ad-card:nth-child(n+2) {
        display: none;
    }

    .enterprise-header-ad-card {
        padding: 10px;
        min-height: 90px;
    }

    .enterprise-header-ad-image {
        margin-right: 10px;
    }

    .enterprise-header-ad-image img {
        width: 45px;
        height: 45px;
    }

    .enterprise-header-ads-label {
        flex-direction: column;
        align-items: flex-start;
        gap: 2px;
    }

    .ads-count {
        font-size: 9px;
    }
}
</style>
