{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block title %}{% trans 'Site administration' %}{% endblock %}

{% block branding %}
<h1 id="site-name">
    <a href="{% url 'admin:index' %}">QR Code Platform Administration</a>
</h1>
{% endblock %}

{% block content %}
<div class="dashboard">
    <div class="module">
        <h2>🎯 Quick Access to Admin Sections</h2>
        <div class="admin-sections" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0;">

            <!-- QR Code Management -->
            <div class="admin-section-card" style="background: linear-gradient(145deg, #f8f9fa, #e9ecef); border: 1px solid #dee2e6; border-radius: 10px; padding: 20px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
                <h3 style="color: #495057; margin-top: 0; display: flex; align-items: center; gap: 10px;">
                    <span style="font-size: 1.5em;">📱</span>
                    QR Code Management
                </h3>
                <p style="color: #6c757d; margin: 10px 0;">Manage QR codes, scans, analytics, and user profiles</p>
                <div style="margin: 15px 0;">
                    <a href="/admin/qr/" class="button" style="background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px; display: inline-block; margin-right: 10px;">QR Admin Panel</a>
                </div>
                <small style="color: #868e96;">
                    • QR Codes & Batches<br>
                    • Scan Analytics<br>
                    • User Profiles & API Keys<br>
                    • Dynamic Redirects
                </small>
            </div>

            <!-- Advertising Management -->
            <div class="admin-section-card" style="background: linear-gradient(145deg, #fff3cd, #ffeaa7); border: 1px solid #ffeaa7; border-radius: 10px; padding: 20px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
                <h3 style="color: #856404; margin-top: 0; display: flex; align-items: center; gap: 10px;">
                    <span style="font-size: 1.5em;">📢</span>
                    Advertising Management
                </h3>
                <p style="color: #856404; margin: 10px 0;">Manage ads, campaigns, transactions, and analytics</p>
                <div style="margin: 15px 0;">
                    <a href="/admin/ads/" class="button" style="background: #ffc107; color: #212529; padding: 8px 16px; text-decoration: none; border-radius: 5px; display: inline-block; margin-right: 10px;">Ads Admin Panel</a>
                </div>
                <small style="color: #856404;">
                    • Ad Types & Locations<br>
                    • Campaign Management<br>
                    • Transaction Processing<br>
                    • Ad Analytics
                </small>
            </div>

            <!-- AI Services Management -->
            <div class="admin-section-card" style="background: linear-gradient(145deg, #e7f3ff, #cce7ff); border: 1px solid #b3d9ff; border-radius: 10px; padding: 20px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
                <h3 style="color: #004085; margin-top: 0; display: flex; align-items: center; gap: 10px;">
                    <span style="font-size: 1.5em;">🤖</span>
                    AI Services Management
                </h3>
                <p style="color: #004085; margin: 10px 0;">Manage AI landing pages, alerts, and webhooks</p>
                <div style="margin: 15px 0;">
                    <a href="/admin/ai/" class="button" style="background: #17a2b8; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px; display: inline-block; margin-right: 10px;">AI Admin Panel</a>
                    <a href="{% url 'ai_services:admin_dashboard' %}" class="button" style="background: #6f42c1; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px; display: inline-block;">AI Dashboard</a>
                </div>
                <small style="color: #004085;">
                    • AI Landing Pages<br>
                    • Scan Events & Alerts<br>
                    • Webhook Endpoints<br>
                    • AI Analytics
                </small>
            </div>

            <!-- Billing & Subscriptions -->
            <div class="admin-section-card" style="background: linear-gradient(145deg, #f8d7da, #f5c6cb); border: 1px solid #f5c6cb; border-radius: 10px; padding: 20px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
                <h3 style="color: #721c24; margin-top: 0; display: flex; align-items: center; gap: 10px;">
                    <span style="font-size: 1.5em;">💳</span>
                    Billing & Subscriptions
                </h3>
                <p style="color: #721c24; margin: 10px 0;">Manage plans, subscriptions, and billing</p>
                <div style="margin: 15px 0;">
                    <a href="/admin/billing/" class="button" style="background: #dc3545; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px; display: inline-block; margin-right: 10px;">Billing Admin Panel</a>
                </div>
                <small style="color: #721c24;">
                    • Subscription Plans<br>
                    • User Subscriptions<br>
                    • Stripe Integration<br>
                    • Usage Analytics
                </small>
            </div>

        </div>
    </div>

    <!-- Main Admin Content -->
    {% if app_list %}
        <div class="module">
            <h2>📊 All Models (Complete Admin)</h2>
            {% for app in app_list %}
                <div class="app-{{ app.app_label }} module">
                    <table>
                        <caption>
                            <a href="{{ app.app_url }}" class="section" title="{% blocktrans with name=app.name %}Models in the {{ name }} application{% endblocktrans %}">{{ app.name }}</a>
                        </caption>
                        {% for model in app.models %}
                            <tr class="model-{{ model.object_name|lower }}">
                                {% if model.admin_url %}
                                    <th scope="row"><a href="{{ model.admin_url }}">{{ model.name }}</a></th>
                                {% else %}
                                    <th scope="row">{{ model.name }}</th>
                                {% endif %}

                                {% if model.add_url %}
                                    <td><a href="{{ model.add_url }}" class="addlink">{% trans 'Add' %}</a></td>
                                {% else %}
                                    <td>&nbsp;</td>
                                {% endif %}

                                {% if model.admin_url %}
                                    {% if model.view_only %}
                                        <td><a href="{{ model.admin_url }}" class="viewlink">{% trans 'View' %}</a></td>
                                    {% else %}
                                        <td><a href="{{ model.admin_url }}" class="changelink">{% trans 'Change' %}</a></td>
                                    {% endif %}
                                {% else %}
                                    <td>&nbsp;</td>
                                {% endif %}
                            </tr>
                        {% endfor %}
                    </table>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <p>{% trans "You don't have permission to view or edit anything." %}</p>
    {% endif %}
</div>

<style>
.admin-section-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.15) !important;
    transition: all 0.3s ease;
}

.button:hover {
    opacity: 0.9;
    transform: translateY(-1px);
    transition: all 0.2s ease;
}

@media (max-width: 768px) {
    .admin-sections {
        grid-template-columns: 1fr !important;
    }
}
</style>
{% endblock %}
