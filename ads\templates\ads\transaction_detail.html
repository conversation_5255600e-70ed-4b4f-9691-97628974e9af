{% extends 'base.html' %}
{% load static %}

{% block title %}Transaction Details{% endblock %}

{% block extra_css %}
<style>
    .transaction-container {
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 8px 30px rgba(0,0,0,0.08);
        padding: 30px;
        margin: 30px auto;
        max-width: 900px;
        transition: all 0.3s ease;
    }

    .transaction-header {
        text-align: center;
        margin-bottom: 40px;
        position: relative;
        padding-bottom: 20px;
        border-bottom: 1px solid #f0f0f0;
    }

    .transaction-id {
        font-size: 28px;
        font-weight: 700;
        margin-bottom: 10px;
        color: #212529;
    }

    .transaction-date {
        color: #6c757d;
        font-size: 14px;
        margin-bottom: 15px;
    }

    .status-badge {
        display: inline-block;
        padding: 8px 16px;
        border-radius: 30px;
        font-size: 14px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-pending {
        background-color: #fff3cd;
        color: #856404;
        border: 1px solid #ffeeba;
    }

    .status-processing {
        background-color: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
    }

    .status-paid {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .status-failed {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .status-refunded {
        background-color: #e2e3e5;
        color: #383d41;
        border: 1px solid #d6d8db;
    }

    .transaction-details {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        margin-bottom: 40px;
    }

    .detail-group {
        margin-bottom: 20px;
        transition: all 0.3s ease;
    }

    .detail-group:hover {
        transform: translateY(-3px);
    }

    .detail-label {
        font-weight: 600;
        margin-bottom: 8px;
        color: #6c757d;
        font-size: 14px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .detail-value {
        font-size: 16px;
        color: #212529;
        padding: 8px 0;
        border-bottom: 1px dashed #e9ecef;
    }

    .detail-section {
        background-color: #fff;
        border-radius: 12px;
        padding: 25px;
        margin-bottom: 30px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
    }

    .detail-section:hover {
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }

    .section-title {
        font-size: 20px;
        font-weight: 700;
        margin-bottom: 20px;
        color: #343a40;
        padding-bottom: 15px;
        border-bottom: 2px solid #f8f9fa;
        position: relative;
    }

    .section-title:after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 60px;
        height: 2px;
        background-color: #007bff;
    }

    .ad-title {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 15px;
        color: #212529;
    }

    .action-buttons {
        display: flex;
        justify-content: center;
        gap: 15px;
        margin-top: 40px;
    }

    .action-button {
        padding: 12px 24px;
        border-radius: 6px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    .action-button i {
        margin-right: 8px;
    }

    .primary-button {
        background-color: #007bff;
        color: white;
        border: none;
    }

    .primary-button:hover {
        background-color: #0069d9;
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0, 105, 217, 0.3);
    }

    .secondary-button {
        background-color: #f8f9fa;
        color: #212529;
        border: 1px solid #dee2e6;
    }

    .secondary-button:hover {
        background-color: #e9ecef;
        color: #212529;
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .receipt-icon {
        font-size: 24px;
        color: #28a745;
        margin-right: 10px;
    }

    .transaction-amount {
        font-size: 32px;
        font-weight: 700;
        color: #28a745;
        text-align: center;
        margin: 20px 0;
    }

    .transaction-steps {
        display: flex;
        justify-content: space-between;
        margin: 40px 0;
        position: relative;
    }

    .transaction-steps:before {
        content: '';
        position: absolute;
        top: 25px;
        left: 0;
        right: 0;
        height: 2px;
        background-color: #e9ecef;
        z-index: 1;
    }

    .step {
        text-align: center;
        position: relative;
        z-index: 2;
    }

    .step-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background-color: #fff;
        border: 2px solid #e9ecef;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 10px;
        font-size: 20px;
        color: #adb5bd;
    }

    .step.active .step-icon {
        background-color: #007bff;
        border-color: #007bff;
        color: white;
    }

    .step.completed .step-icon {
        background-color: #28a745;
        border-color: #28a745;
        color: white;
    }

    .step-label {
        font-size: 14px;
        font-weight: 600;
        color: #6c757d;
    }

    .step.active .step-label {
        color: #007bff;
    }

    .step.completed .step-label {
        color: #28a745;
    }

    /* Mobile responsiveness */
    @media (max-width: 992px) {
        .transaction-container {
            padding: 20px;
        }

        .transaction-details {
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .transaction-steps {
            flex-direction: column;
            align-items: flex-start;
            gap: 20px;
        }

        .transaction-steps:before {
            display: none;
        }

        .step {
            display: flex;
            align-items: center;
            width: 100%;
        }

        .step-icon {
            margin: 0 15px 0 0;
        }
    }

    @media (max-width: 576px) {
        .action-buttons {
            flex-direction: column;
            gap: 10px;
        }

        .action-button {
            width: 100%;
        }

        .transaction-id {
            font-size: 24px;
        }

        .transaction-amount {
            font-size: 28px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'index' %}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'ads:dashboard' %}">Ad Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'ads:transaction_list' %}">Transactions</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Transaction #{{ transaction.id }}</li>
                </ol>
            </nav>

            <h1 class="display-4 text-center mb-2">Transaction Details</h1>
            <p class="lead text-center mb-4">View detailed information about this transaction</p>
        </div>
    </div>

    <div class="transaction-container animate__animated animate__fadeIn">
        <div class="transaction-header">
            <div class="transaction-id">Transaction #{{ transaction.id }}</div>
            <div class="transaction-date">{{ transaction.timestamp|date:"F d, Y H:i:s" }}</div>
            <div class="status-badge status-{{ transaction.status }}">
                {{ transaction.get_status_display }}
            </div>
        </div>

        <div class="transaction-amount">
            <i class="fas fa-receipt receipt-icon"></i>
            {{ transaction.amount }} KSH
        </div>

        <div class="transaction-steps">
            <div class="step completed">
                <div class="step-icon">
                    <i class="fas fa-file-invoice"></i>
                </div>
                <div class="step-label">Created</div>
            </div>
            <div class="step {% if transaction.status == 'processing' or transaction.status == 'paid' %}completed{% elif transaction.status == 'pending' %}active{% endif %}">
                <div class="step-icon">
                    <i class="fas fa-credit-card"></i>
                </div>
                <div class="step-label">Processing</div>
            </div>
            <div class="step {% if transaction.status == 'paid' %}completed{% elif transaction.status == 'processing' %}active{% endif %}">
                <div class="step-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="step-label">Completed</div>
            </div>
            <div class="step {% if transaction.status == 'failed' %}active{% endif %}">
                <div class="step-icon">
                    <i class="fas fa-times-circle"></i>
                </div>
                <div class="step-label">Failed</div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="detail-section animate__animated animate__fadeInLeft">
                    <h3 class="section-title">Transaction Information</h3>
                    <div class="detail-group">
                        <div class="detail-label">Transaction ID</div>
                        <div class="detail-value">{{ transaction.transaction_id|default:"Not assigned yet" }}</div>
                    </div>
                    <div class="detail-group">
                        <div class="detail-label">Payment Method</div>
                        <div class="detail-value">{{ transaction.get_payment_gateway_display }}</div>
                    </div>
                    <div class="detail-group">
                        <div class="detail-label">Status</div>
                        <div class="detail-value">{{ transaction.get_status_display }}</div>
                    </div>
                    <div class="detail-group">
                        <div class="detail-label">Date</div>
                        <div class="detail-value">{{ transaction.timestamp|date:"F d, Y" }}</div>
                    </div>
                    <div class="detail-group">
                        <div class="detail-label">Time</div>
                        <div class="detail-value">{{ transaction.timestamp|date:"H:i:s" }}</div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="detail-section animate__animated animate__fadeInRight">
                    <h3 class="section-title">Ad Information</h3>
                    <div class="ad-title">{{ transaction.ad.title }}</div>
                    <div class="detail-group">
                        <div class="detail-label">Ad Type</div>
                        <div class="detail-value">{{ transaction.ad.ad_type.name }}</div>
                    </div>
                    <div class="detail-group">
                        <div class="detail-label">Duration</div>
                        <div class="detail-value">{{ transaction.ad.start_date|date:"M d, Y" }} to {{ transaction.ad.end_date|date:"M d, Y" }}</div>
                    </div>
                    <div class="detail-group">
                        <div class="detail-label">Status</div>
                        <div class="detail-value">{{ transaction.ad.get_status_display }}</div>
                    </div>
                    <div class="detail-group">
                        <div class="detail-label">Created</div>
                        <div class="detail-value">{{ transaction.ad.created_at|date:"M d, Y" }}</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="detail-section animate__animated animate__fadeInUp">
            <h3 class="section-title">Payment Details</h3>
            {% if transaction.payment_details %}
                <div class="row">
                    {% for key, value in transaction.payment_details.items %}
                        <div class="col-md-6">
                            <div class="detail-group">
                                <div class="detail-label">{{ key|title }}</div>
                                <div class="detail-value">{{ value }}</div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <p class="text-center text-muted">No additional payment details available.</p>
            {% endif %}
        </div>

        <div class="action-buttons">
            <a href="{% url 'ads:transaction_list' %}" class="action-button secondary-button">
                <i class="fas fa-arrow-left"></i> Back to Transactions
            </a>
            <a href="{% url 'ads:ad_detail' slug=transaction.ad.slug %}" class="action-button primary-button">
                <i class="fas fa-eye"></i> View Ad
            </a>
            <a href="{% url 'ads:dashboard' %}" class="action-button secondary-button">
                <i class="fas fa-tachometer-alt"></i> Dashboard
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add animations with delays
        const detailGroups = document.querySelectorAll('.detail-group');
        detailGroups.forEach((group, index) => {
            group.style.animationDelay = `${index * 0.05}s`;
            group.classList.add('animate__animated', 'animate__fadeIn');
        });

        // Add hover effect to detail sections
        const detailSections = document.querySelectorAll('.detail-section');
        detailSections.forEach(section => {
            section.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
                this.style.boxShadow = '0 8px 25px rgba(0,0,0,0.1)';
            });

            section.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '0 4px 15px rgba(0,0,0,0.05)';
            });
        });
    });
</script>
{% endblock %}
