/**
 * Enterprise QR Code Generator - Batch Processor
 * 
 * This module provides batch processing capabilities for high-volume QR code generation
 * in enterprise environments, including CSV import, bulk generation, and export.
 */

// Batch processing state
let batchQueue = [];
let processingBatch = false;
let batchProgress = 0;
let batchTotal = 0;
let batchResults = [];
let batchCallbacks = {
    onProgress: null,
    onComplete: null,
    onError: null
};

// Batch processing configuration
let batchConfig = {
    concurrency: 5, // Number of concurrent QR codes to generate
    chunkSize: 20, // Number of QR codes to process in each chunk
    delayBetweenChunks: 100, // Delay between chunks in milliseconds
    maxRetries: 3, // Maximum number of retries for failed generations
    timeout: 30000 // Timeout for batch processing in milliseconds
};

/**
 * Initialize the batch processor
 * @param {Object} config - Batch processor configuration
 */
function initBatchProcessor(config = {}) {
    console.log('Initializing Batch Processor for Enterprise QR...');
    
    // Update configuration
    batchConfig = { ...batchConfig, ...config };
    
    console.log('Batch Processor initialized with:', batchConfig);
}

/**
 * Process a batch of QR codes
 * @param {Array} dataItems - Array of QR code data items
 * @param {Object} options - QR code options
 * @param {Object} callbacks - Callback functions
 * @returns {Promise} - Promise resolving to batch results
 */
function processBatch(dataItems, options = {}, callbacks = {}) {
    return new Promise((resolve, reject) => {
        // Reset batch state
        batchQueue = [...dataItems];
        batchProgress = 0;
        batchTotal = dataItems.length;
        batchResults = [];
        batchCallbacks = { ...callbacks };
        
        // Set up timeout
        const timeoutId = setTimeout(() => {
            if (processingBatch) {
                processingBatch = false;
                const error = new Error(`Batch processing timed out after ${batchConfig.timeout}ms`);
                
                if (batchCallbacks.onError) {
                    batchCallbacks.onError(error);
                }
                
                reject(error);
            }
        }, batchConfig.timeout);
        
        // Start processing
        processingBatch = true;
        
        // Process in chunks
        processNextChunk()
            .then(results => {
                clearTimeout(timeoutId);
                resolve(results);
            })
            .catch(error => {
                clearTimeout(timeoutId);
                reject(error);
            });
    });
}

/**
 * Process the next chunk of QR codes
 * @returns {Promise} - Promise resolving to batch results
 */
async function processNextChunk() {
    if (!processingBatch || batchQueue.length === 0) {
        // Batch processing complete or stopped
        processingBatch = false;
        
        if (batchCallbacks.onComplete) {
            batchCallbacks.onComplete(batchResults);
        }
        
        return batchResults;
    }
    
    // Get next chunk
    const chunk = batchQueue.splice(0, batchConfig.chunkSize);
    
    try {
        // Process chunk concurrently
        const chunkPromises = chunk.map(item => processQRCodeItem(item));
        const chunkResults = await Promise.allSettled(chunkPromises);
        
        // Process results
        chunkResults.forEach(result => {
            if (result.status === 'fulfilled') {
                batchResults.push(result.value);
            } else {
                // Add failed item to results with error
                batchResults.push({
                    success: false,
                    error: result.reason.message || 'Unknown error',
                    data: null
                });
                
                console.error('Error processing QR code item:', result.reason);
            }
        });
        
        // Update progress
        batchProgress += chunk.length;
        
        // Report progress
        if (batchCallbacks.onProgress) {
            const progressPercent = Math.round((batchProgress / batchTotal) * 100);
            batchCallbacks.onProgress(progressPercent, batchProgress, batchTotal);
        }
        
        // Delay before next chunk
        await new Promise(resolve => setTimeout(resolve, batchConfig.delayBetweenChunks));
        
        // Process next chunk
        return processNextChunk();
    } catch (error) {
        processingBatch = false;
        
        if (batchCallbacks.onError) {
            batchCallbacks.onError(error);
        }
        
        throw error;
    }
}

/**
 * Process a single QR code item
 * @param {Object} item - QR code data item
 * @returns {Promise} - Promise resolving to QR code result
 */
async function processQRCodeItem(item) {
    let retries = 0;
    
    while (retries <= batchConfig.maxRetries) {
        try {
            // Extract data and options
            const { data, options = {}, id, metadata = {} } = item;
            
            // Generate QR code
            const dataURL = await generateOptimizedQRCode(data, options);
            
            // Return result
            return {
                id: id || generateId(),
                data,
                dataURL,
                options,
                metadata,
                success: true,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            retries++;
            
            if (retries > batchConfig.maxRetries) {
                throw error;
            }
            
            // Wait before retrying
            await new Promise(resolve => setTimeout(resolve, 500 * retries));
        }
    }
}

/**
 * Generate a unique ID
 * @returns {string} - Unique ID
 */
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
}

/**
 * Parse CSV data for batch processing
 * @param {string} csvData - CSV data
 * @param {Object} options - Parsing options
 * @returns {Array} - Array of QR code data items
 */
function parseCSVForBatch(csvData, options = {}) {
    // Default options
    const defaultOptions = {
        delimiter: ',',
        dataColumn: 0,
        hasHeader: true,
        optionsColumns: {},
        metadataColumns: {}
    };
    
    const config = { ...defaultOptions, ...options };
    
    // Split into lines
    const lines = csvData.split(/\r?\n/).filter(line => line.trim());
    
    // Skip header if needed
    const startIndex = config.hasHeader ? 1 : 0;
    
    // Parse data
    const dataItems = [];
    
    for (let i = startIndex; i < lines.length; i++) {
        const line = lines[i];
        const columns = line.split(config.delimiter).map(col => col.trim());
        
        // Skip empty lines
        if (columns.length <= 1 && !columns[0]) continue;
        
        // Get QR data
        const data = columns[config.dataColumn];
        
        // Get options
        const itemOptions = {};
        
        Object.entries(config.optionsColumns).forEach(([option, columnIndex]) => {
            if (columnIndex < columns.length && columns[columnIndex]) {
                itemOptions[option] = columns[columnIndex];
            }
        });
        
        // Get metadata
        const metadata = {};
        
        Object.entries(config.metadataColumns).forEach(([field, columnIndex]) => {
            if (columnIndex < columns.length) {
                metadata[field] = columns[columnIndex];
            }
        });
        
        // Add to data items
        dataItems.push({
            id: generateId(),
            data,
            options: itemOptions,
            metadata
        });
    }
    
    return dataItems;
}

/**
 * Export batch results to CSV
 * @param {Array} results - Batch results
 * @param {Object} options - Export options
 * @returns {string} - CSV data
 */
function exportBatchResultsToCSV(results, options = {}) {
    // Default options
    const defaultOptions = {
        delimiter: ',',
        includeHeader: true,
        includeDataURL: false,
        includeTimestamp: true,
        includeSuccess: true,
        includeMetadata: true,
        metadataFields: null // If null, include all metadata fields
    };
    
    const config = { ...defaultOptions, ...options };
    
    // Collect all metadata fields
    let allMetadataFields = [];
    
    if (config.includeMetadata) {
        if (config.metadataFields) {
            allMetadataFields = config.metadataFields;
        } else {
            // Collect all unique metadata fields
            results.forEach(result => {
                if (result.metadata) {
                    Object.keys(result.metadata).forEach(field => {
                        if (!allMetadataFields.includes(field)) {
                            allMetadataFields.push(field);
                        }
                    });
                }
            });
        }
    }
    
    // Build CSV
    const lines = [];
    
    // Add header
    if (config.includeHeader) {
        const headerColumns = ['ID', 'Data'];
        
        if (config.includeDataURL) headerColumns.push('DataURL');
        if (config.includeTimestamp) headerColumns.push('Timestamp');
        if (config.includeSuccess) headerColumns.push('Success');
        
        // Add metadata fields
        if (config.includeMetadata) {
            allMetadataFields.forEach(field => {
                headerColumns.push(`Metadata_${field}`);
            });
        }
        
        lines.push(headerColumns.join(config.delimiter));
    }
    
    // Add data rows
    results.forEach(result => {
        const columns = [
            result.id || '',
            result.data || ''
        ];
        
        if (config.includeDataURL) columns.push(result.dataURL || '');
        if (config.includeTimestamp) columns.push(result.timestamp || '');
        if (config.includeSuccess) columns.push(result.success ? 'true' : 'false');
        
        // Add metadata fields
        if (config.includeMetadata) {
            allMetadataFields.forEach(field => {
                const value = result.metadata && result.metadata[field] !== undefined ? 
                    result.metadata[field] : '';
                columns.push(value);
            });
        }
        
        lines.push(columns.join(config.delimiter));
    });
    
    return lines.join('\n');
}

/**
 * Export batch results to ZIP file
 * @param {Array} results - Batch results
 * @param {Object} options - Export options
 * @returns {Promise} - Promise resolving to ZIP file blob
 */
async function exportBatchResultsToZIP(results, options = {}) {
    // Check if JSZip is available
    if (typeof JSZip === 'undefined') {
        throw new Error('JSZip library is required for ZIP export');
    }
    
    // Default options
    const defaultOptions = {
        includeCSV: true,
        fileNamePattern: 'qr_code_{id}',
        imageFormat: 'png',
        includeMetadataInFilename: false
    };
    
    const config = { ...defaultOptions, ...options };
    
    // Create ZIP file
    const zip = new JSZip();
    
    // Add QR code images
    results.forEach(result => {
        if (result.success && result.dataURL) {
            // Generate filename
            let filename = config.fileNamePattern.replace('{id}', result.id);
            
            // Add metadata to filename if requested
            if (config.includeMetadataInFilename && result.metadata) {
                Object.entries(result.metadata).forEach(([key, value]) => {
                    filename = filename.replace(`{${key}}`, value);
                });
            }
            
            // Add file extension
            filename += `.${config.imageFormat}`;
            
            // Add to ZIP
            const imageData = result.dataURL.split(',')[1];
            zip.file(filename, imageData, { base64: true });
        }
    });
    
    // Add CSV file if requested
    if (config.includeCSV) {
        const csvData = exportBatchResultsToCSV(results);
        zip.file('qr_codes.csv', csvData);
    }
    
    // Generate ZIP file
    return zip.generateAsync({ type: 'blob' });
}

/**
 * Cancel current batch processing
 */
function cancelBatchProcessing() {
    if (processingBatch) {
        processingBatch = false;
        console.log('Batch processing cancelled');
    }
}

/**
 * Get current batch processing status
 * @returns {Object} - Batch processing status
 */
function getBatchStatus() {
    return {
        processing: processingBatch,
        progress: batchProgress,
        total: batchTotal,
        percentComplete: batchTotal > 0 ? Math.round((batchProgress / batchTotal) * 100) : 0,
        remainingItems: batchQueue.length,
        completedItems: batchResults.length
    };
}

// Export functions for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        initBatchProcessor,
        processBatch,
        parseCSVForBatch,
        exportBatchResultsToCSV,
        exportBatchResultsToZIP,
        cancelBatchProcessing,
        getBatchStatus
    };
} else {
    // Add to window object
    window.initBatchProcessor = initBatchProcessor;
    window.processBatch = processBatch;
    window.parseCSVForBatch = parseCSVForBatch;
    window.exportBatchResultsToCSV = exportBatchResultsToCSV;
    window.exportBatchResultsToZIP = exportBatchResultsToZIP;
    window.cancelBatchProcessing = cancelBatchProcessing;
    window.getBatchStatus = getBatchStatus;
}
