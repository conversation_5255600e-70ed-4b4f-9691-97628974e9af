# Generated manually for QR landing functionality

import uuid
from django.db import migrations, models


def populate_unique_ids(apps, schema_editor):
    """Populate unique_id field for existing QR codes"""
    QRCode = apps.get_model('qrcode_app', 'QRCode')
    for qr_code in QRCode.objects.all():
        qr_code.unique_id = uuid.uuid4()
        qr_code.save()


def reverse_populate_unique_ids(apps, schema_editor):
    """Reverse operation - nothing to do"""
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('qrcode_app', '0005_merge_20250528_1323'),
    ]

    operations = [
        # Add original_url field
        migrations.AddField(
            model_name='qrcode',
            name='original_url',
            field=models.TextField(blank=True, help_text='Original destination URL before landing page', null=True),
        ),
        
        # Add unique_id field without unique constraint first
        migrations.AddField(
            model_name='qrcode',
            name='unique_id',
            field=models.UUIDField(default=uuid.uuid4, help_text='Unique identifier for QR landing page', null=True),
        ),
        
        # Populate unique_id for existing records
        migrations.RunPython(populate_unique_ids, reverse_populate_unique_ids),
        
        # Now make unique_id non-null and unique
        migrations.AlterField(
            model_name='qrcode',
            name='unique_id',
            field=models.UUIDField(default=uuid.uuid4, help_text='Unique identifier for QR landing page', unique=True),
        ),
        
        # Add geo-location fields to QRCodeScan
        migrations.AddField(
            model_name='qrcodescan',
            name='city',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='qrcodescan',
            name='country',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='qrcodescan',
            name='latitude',
            field=models.DecimalField(blank=True, decimal_places=7, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='qrcodescan',
            name='longitude',
            field=models.DecimalField(blank=True, decimal_places=7, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='qrcodescan',
            name='postal_code',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='qrcodescan',
            name='region',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='qrcodescan',
            name='scanner_type',
            field=models.CharField(
                choices=[('native_camera', 'Native Camera'), ('third_party_app', 'Third Party App'), ('unknown', 'Unknown')], 
                default='unknown', 
                max_length=20
            ),
        ),
        migrations.AddField(
            model_name='qrcodescan',
            name='timezone',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
    ]
