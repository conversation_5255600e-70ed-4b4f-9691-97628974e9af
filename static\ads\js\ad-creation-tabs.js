/**
 * Ad Creation Tabs JavaScript
 * Handles tab navigation and progress tracking for ad creation/edit forms
 * This is a standalone version for pages that don't use the consolidated script
 */

document.addEventListener('DOMContentLoaded', function() {
    try {
        console.log('Ad Creation Tabs: Initializing...');

        // Helper function to scroll to the top of the form
        function scrollToFormTop() {
            const adCreationForm = document.getElementById('adCreationForm');
            if (adCreationForm) {
                console.log('Ad Creation Tabs: Scrolling to form top');
                const rect = adCreationForm.getBoundingClientRect();
                const offset = window.pageYOffset + rect.top - 20;
                window.scrollTo({ top: offset, behavior: 'smooth' });
                return true;
            }

            const tabContent = document.getElementById('adCreationTabContent');
            if (tabContent) {
                console.log('Ad Creation Tabs: Scrolling to tab content');
                const rect = tabContent.getBoundingClientRect();
                const offset = window.pageYOffset + rect.top - 20;
                window.scrollTo({ top: offset, behavior: 'smooth' });
                return true;
            }

            return false;
        }

        // Update progress bar
        function updateProgressBar(tabId) {
            const progressBar = document.querySelector('.progress-bar');
            if (!progressBar) return;
            
            let progress = 25;
            switch(tabId) {
                case 'step1-tab': progress = 25; break;
                case 'step2-tab': progress = 50; break;
                case 'step3-tab': progress = 75; break;
                case 'step4-tab': progress = 100; break;
            }
            
            progressBar.style.width = progress + '%';
            progressBar.setAttribute('aria-valuenow', progress);
        }

        // Activate a tab
        function activateTab(tabId) {
            const tab = document.getElementById(tabId);
            if (tab && typeof bootstrap !== 'undefined') {
                const bsTab = new bootstrap.Tab(tab);
                bsTab.show();
            }
        }

        // Validate current step (basic validation)
        function validateCurrentStep() {
            // Basic validation - can be overridden by specific pages
            return true;
        }

        // Initialize tab navigation
        const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');
        const nextButtons = document.querySelectorAll('.next-step');
        const prevButtons = document.querySelectorAll('.prev-step');

        // Progress bar update
        if (tabButtons.length > 0) {
            tabButtons.forEach(button => {
                button.addEventListener('shown.bs.tab', function(e) {
                    updateProgressBar(e.target.getAttribute('id'));
                    scrollToFormTop();
                });
            });
        }

        // Next button handlers
        nextButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const nextTabId = this.getAttribute('data-next');
                if (nextTabId && validateCurrentStep()) {
                    activateTab(nextTabId);
                }
            });
        });

        // Previous button handlers
        prevButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const prevTabId = this.getAttribute('data-prev');
                if (prevTabId) {
                    activateTab(prevTabId);
                }
            });
        });

        console.log('Ad Creation Tabs: Initialized successfully');

    } catch (error) {
        console.error('Ad Creation Tabs: Initialization failed', error);
    }
});
