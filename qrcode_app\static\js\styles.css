@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&family=Georgia&family=Arial&family=Verdana&family=Courier+New&display=swap');

:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #3b82f6;
    --primary-gradient: linear-gradient(135deg, #2563eb, #1e40af);
    --secondary-color: #0f172a;
    --accent-color: #f59e0b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --text-dark: #1e293b;
    --text-light: #64748b;
    --text-lighter: #94a3b8;
    --bg-light: #f8fafc;
    --bg-white: #ffffff;
    --bg-dark: #0f172a;
    --bg-darker: #020617;
    --border-color: #e2e8f0;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --transition: all 0.3s ease;
    --max-width: 1400px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Inter', sans-serif;
}

body {
    background-color: var(--bg-light);
    color: var(--text-dark);
    min-height: 100vh;
    line-height: 1.5;
    overflow-x: hidden;
}

/* Preloader Styles */
.preloader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-white);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.preloader.fade-out {
    opacity: 0;
    visibility: hidden;
}

.preloader-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
    max-width: 300px;
    text-align: center;
}

.preloader-logo {
    font-size: 3.5rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: pulse-glow 2s infinite alternate;
}

@keyframes pulse-glow {
    0% {
        filter: drop-shadow(0 0 2px rgba(37, 99, 235, 0.3));
        transform: scale(1);
    }
    100% {
        filter: drop-shadow(0 0 8px rgba(37, 99, 235, 0.6));
        transform: scale(1.05);
    }
}

.preloader-text {
    font-family: 'Poppins', sans-serif;
    font-size: 2rem;
    font-weight: 700;
    color: var(--secondary-color);
    letter-spacing: -0.5px;
    position: relative;
}

.preloader-text span {
    color: var(--primary-color);
}

.preloader-spinner {
    position: relative;
    width: 60px;
    height: 60px;
    margin: 1rem 0;
}

.spinner-ring {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 3px solid rgba(37, 99, 235, 0.1);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1.5s linear infinite;
}

.spinner-dot {
    position: absolute;
    top: 0;
    left: 50%;
    width: 10px;
    height: 10px;
    background-color: var(--primary-color);
    border-radius: 50%;
    transform: translateX(-50%);
    animation: dot-move 1.5s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes dot-move {
    0% {
        transform: translateX(-50%) translateY(-5px) scale(1);
    }
    50% {
        transform: translateX(-50%) translateY(35px) scale(1.2);
    }
    100% {
        transform: translateX(-50%) translateY(-5px) scale(1);
    }
}

.preloader-progress {
    width: 100%;
    height: 4px;
    background-color: rgba(37, 99, 235, 0.1);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.progress-bar {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 0;
    background: var(--primary-gradient);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.preloader-status {
    font-size: 0.875rem;
    color: var(--text-light);
    font-weight: 500;
}

.app-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    opacity: 0;
    animation: fadeIn 0.5s ease forwards 0.5s;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Announcement Bar Styles */
.announcement-bar {
    background: var(--primary-gradient);
    color: white;
    padding: 0.5rem 0;
    font-size: 0.875rem;
    position: relative;
    overflow: hidden;
}

.announcement-container {
    max-width: var(--max-width);
    margin: 0 auto;
    padding: 0 2rem;
    position: relative;
}

.announcement-slider {
    display: flex;
    animation: slideAnnouncements 15s infinite;
    width: 300%;
}

.announcement-slide {
    width: 100%;
    text-align: center;
    padding: 0 1rem;
    white-space: nowrap;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.announcement-slide i {
    color: rgba(255, 255, 255, 0.9);
    margin-right: 0.25rem;
}

.announcement-slide a {
    color: white;
    text-decoration: underline;
    font-weight: 600;
    margin-left: 0.5rem;
    transition: var(--transition);
}

.announcement-slide a:hover {
    color: rgba(255, 255, 255, 0.9);
}

@keyframes slideAnnouncements {
    0%, 30% {
        transform: translateX(0);
    }
    33%, 63% {
        transform: translateX(-33.33%);
    }
    66%, 96% {
        transform: translateX(-66.66%);
    }
    100% {
        transform: translateX(0);
    }
}

/* Navigation Styles */
.top-nav {
    background-color: var(--bg-white);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 100;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    padding: 0 0.5rem;
}

.nav-container {
    max-width: var(--max-width);
    margin: 0 auto;
    padding: 1rem 2.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    flex-wrap: nowrap;
    gap: 1rem;
    position: relative;
    box-sizing: border-box;
}

.nav-left {
    display: flex;
    align-items: center;
    gap: 3rem;
    flex: 0 0 auto;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    position: relative;
}

.logo-icon-container {
    position: relative;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo i {
    font-size: 1.75rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: logoGlow 3s infinite alternate;
    position: relative;
    z-index: 2;
}

.logo-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.logo-particles span {
    position: absolute;
    display: block;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: var(--primary-color);
    opacity: 0;
    animation: particleFloat 3s infinite;
}

.logo-particles span:nth-child(1) {
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.logo-particles span:nth-child(2) {
    top: 20%;
    right: 10%;
    animation-delay: 0.4s;
}

.logo-particles span:nth-child(3) {
    bottom: 10%;
    left: 20%;
    animation-delay: 0.8s;
}

.logo-particles span:nth-child(4) {
    bottom: 20%;
    right: 20%;
    animation-delay: 1.2s;
}

.logo-particles span:nth-child(5) {
    top: 50%;
    left: 50%;
    animation-delay: 1.6s;
}

@keyframes particleFloat {
    0% {
        transform: translate(0, 0);
        opacity: 0;
    }
    20% {
        opacity: 0.8;
    }
    80% {
        opacity: 0.2;
    }
    100% {
        transform: translate(10px, -10px);
        opacity: 0;
    }
}

@keyframes logoGlow {
    0% {
        filter: drop-shadow(0 0 1px rgba(37, 99, 235, 0.3));
    }
    100% {
        filter: drop-shadow(0 0 5px rgba(37, 99, 235, 0.7));
    }
}

.logo-text {
    font-family: 'Poppins', sans-serif;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--secondary-color);
    letter-spacing: -0.5px;
    position: relative;
}

.logo-text::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--primary-gradient);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
}

.logo:hover .logo-text::after {
    transform: scaleX(1);
}

.logo-text .highlight {
    color: var(--primary-color);
    position: relative;
}

.nav-right {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    gap: 1rem;
    flex: 0 0 auto;
    padding-right: 0;
    max-width: 50%;
}

.desktop-links {
    display: flex;
    gap: 2rem;
    flex-wrap: nowrap;
    overflow-x: visible;
}

.nav-link {
    color: var(--text-light);
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    padding: 0.5rem 0;
    position: relative;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-link-text {
    position: relative;
    z-index: 1;
}

.nav-link-indicator {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--primary-gradient);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
    border-radius: 2px;
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-link:hover .nav-link-indicator {
    transform: scaleX(1);
}

.nav-link.active {
    color: var(--primary-color);
    font-weight: 600;
}

.nav-link.active .nav-link-indicator {
    transform: scaleX(1);
}

.nav-link-badge {
    font-size: 0.65rem;
    background: var(--primary-gradient);
    color: white;
    padding: 0.15rem 0.4rem;
    border-radius: 10px;
    margin-left: 0.25rem;
    font-weight: 600;
    letter-spacing: 0.5px;
    animation: badgePulse 2s infinite;
}

.dropdown-icon {
    font-size: 0.7rem;
    margin-left: 0.25rem;
    transition: transform 0.3s ease;
}

.nav-link.has-dropdown:hover .dropdown-icon {
    transform: rotate(180deg);
}

/* Dropdown Menu Styles */
.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: white;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-xl);
    min-width: 220px;
    padding: 0.75rem 0;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s ease;
    z-index: 100;
    border: 1px solid var(--border-color);
    margin-top: 0.5rem;
}

.nav-link.has-dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.25rem;
    color: var(--text-dark);
    text-decoration: none;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.dropdown-item i {
    color: var(--primary-color);
    font-size: 1rem;
    width: 20px;
    text-align: center;
}

.dropdown-item:hover {
    background-color: rgba(37, 99, 235, 0.05);
    color: var(--primary-color);
}

/* Features Dropdown - Special Styling */
.features-dropdown {
    width: 600px;
    max-width: 90vw;
    padding: 0;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    padding: 1.5rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: var(--radius-md);
    transition: var(--transition);
    text-decoration: none;
    border: 1px solid transparent;
}

.feature-item:hover {
    background-color: rgba(37, 99, 235, 0.05);
    border-color: rgba(37, 99, 235, 0.1);
}

.feature-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    flex-shrink: 0;
}

.feature-content h5 {
    font-size: 0.9375rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.25rem;
}

.feature-content p {
    font-size: 0.8125rem;
    color: var(--text-light);
    line-height: 1.4;
}

.dropdown-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
    text-align: center;
}

.view-all-link {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--primary-color);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition);
}

.view-all-link:hover {
    color: var(--secondary-color);
}

.view-all-link i {
    font-size: 0.75rem;
    transition: transform 0.3s ease;
}

.view-all-link:hover i {
    transform: translateX(3px);
}

/* Solutions Dropdown - Special Styling */
.solutions-dropdown {
    width: 650px;
    max-width: 90vw;
    padding: 0;
}

.dropdown-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.dropdown-header span {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-dark);
}

.dropdown-content {
    display: flex;
    padding: 1.5rem;
    flex-wrap: wrap;
    gap: 1.5rem;
}

.dropdown-column {
    flex: 1;
    min-width: 200px;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.dropdown-column h4 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.featured-column {
    background-color: rgba(37, 99, 235, 0.03);
    border-radius: 0 0 var(--radius-md) 0;
    padding: 1rem;
}

.featured-solution {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 1.5rem 1rem;
}

.featured-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    color: white;
    font-size: 1.25rem;
}

.featured-solution h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
    border: none;
}

.featured-solution p {
    font-size: 0.875rem;
    color: var(--text-light);
    margin-bottom: 1.25rem;
}

.featured-cta {
    display: inline-block;
    background: var(--primary-gradient);
    color: white;
    padding: 0.5rem 1.25rem;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition);
    box-shadow: 0 4px 6px -1px rgba(37, 99, 235, 0.2);
}

.featured-cta:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 10px -1px rgba(37, 99, 235, 0.3);
}

@keyframes badgePulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: nowrap;
    justify-content: flex-end;
    white-space: nowrap;
    min-width: 0;
    max-width: 100%;
    width: 100%;
}

.nav-action-item {
    position: relative;
    flex: 0 0 auto;
}

.vpn-status {
    position: relative;
}

.vpn-link {
    color: var(--success-color);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    padding: 0.5rem 0.75rem;
    border-radius: var(--radius-md);
    background-color: rgba(16, 185, 129, 0.1);
    transition: var(--transition);
}

.vpn-link:hover {
    background-color: rgba(16, 185, 129, 0.15);
}

.vpn-link i {
    font-size: 1rem;
    animation: shieldPulse 2s infinite;
}

@keyframes shieldPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

.online-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-md);
    background-color: rgba(16, 185, 129, 0.05);
}

.online-dot {
    width: 8px;
    height: 8px;
    background-color: var(--success-color);
    border-radius: 50%;
    position: relative;
}

.online-dot::after {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    width: 14px;
    height: 14px;
    background-color: rgba(16, 185, 129, 0.2);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.7;
    }
    70% {
        transform: scale(1.5);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 0;
    }
}

.online-text {
    font-size: 0.75rem;
    color: var(--text-light);
    font-weight: 500;
}

.login-btn {
    background: var(--primary-gradient);
    color: white;
    border: none;
    padding: 0.6rem 1.25rem;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(37, 99, 235, 0.2);
    white-space: nowrap;
    flex-shrink: 0;
}

.login-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: 0.5s;
}

.login-btn:hover::before {
    left: 100%;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 10px -1px rgba(37, 99, 235, 0.3);
}

/* User Dropdown Menu */
.user-dropdown {
    position: relative;
}

.user-menu-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.5rem;
    background: none;
    border: none;
    cursor: pointer;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
}

.user-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-dark);
}

.user-dropdown-menu {
    position: absolute;
    top: calc(100% + 0.5rem);
    right: 0;
    width: 220px;
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    padding: 0.5rem 0;
    z-index: 100;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
}

.user-dropdown.active .user-dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: var(--text-dark);
    text-decoration: none;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.dropdown-item i {
    font-size: 1rem;
    width: 1.25rem;
    text-align: center;
}

.dropdown-item:hover {
    background-color: var(--bg-light);
    color: var(--text-dark);
}

.dropdown-item.active {
    background-color: rgba(37, 99, 235, 0.1);
    color: var(--primary-color);
    font-weight: 500;
}

.dropdown-divider {
    height: 1px;
    background-color: var(--border-color);
    margin: 0.5rem 0;
}

.logout-item {
    color: var(--danger-color);
}

.logout-item:hover {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

/* Hidden elements based on permissions */
.hidden {
    display: none !important;
}

.ad-menu-toggle {
    position: relative;
}

.ad-menu-btn {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    border: none;
    padding: 0.5rem 0.75rem;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 4px 6px -1px rgba(37, 99, 235, 0.3);
    position: relative;
    overflow: hidden;
}

.ad-menu-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: 0.5s;
}

.ad-menu-btn:hover::before {
    left: 100%;
}

.ad-menu-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 10px -1px rgba(37, 99, 235, 0.4);
}

.ad-menu-btn i {
    font-size: 1rem;
    animation: featurePulse 3s infinite;
}

@keyframes featurePulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

/* Advertisement Menu Styles */
.ads-menu-toggle {
    position: relative;
    cursor: pointer;
}

.ads-menu-btn {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    border: none;
    padding: 0.5rem 0.75rem;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 4px 6px -1px rgba(37, 99, 235, 0.3);
    position: relative;
    overflow: hidden;
}

.ads-menu-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: 0.5s;
}

.ads-menu-btn:hover::before {
    left: 100%;
}

.ads-menu-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 10px -1px rgba(37, 99, 235, 0.4);
}

.ads-menu-btn i {
    font-size: 1rem;
    animation: adPulse 3s infinite;
}

@keyframes adPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
}

.ads-dropdown {
    position: absolute;
    top: calc(100% + 0.5rem);
    right: 0;
    background-color: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    width: 950px;
    max-width: 90vw;
    padding: 1.5rem;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s ease;
    z-index: 100;
    border: 1px solid var(--border-color);
    max-height: 85vh;
    overflow-y: auto;
}

.ads-menu-toggle:hover .ads-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.ads-dropdown-header {
    margin-bottom: 1.5rem;
    text-align: center;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.ads-dropdown-header h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.ads-dropdown-header p {
    font-size: 0.9375rem;
    color: var(--text-light);
}

.ads-sections {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
    flex-wrap: nowrap;
    justify-content: space-between;
}

.ads-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;
    max-width: 33.333%;
    overflow: hidden;
}

.ads-section-title {
    font-size: 0.9375rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.ads-options {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    max-height: 100%;
    overflow-y: auto;
}

.ads-option {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 0.875rem;
    border-radius: var(--radius-md);
    transition: var(--transition);
    text-decoration: none;
    border: 1px solid transparent;
    background-color: white;
    box-shadow: var(--shadow-sm);
    margin-bottom: 0.5rem;
}

.ads-option:hover {
    background-color: rgba(37, 99, 235, 0.05);
    border-color: rgba(37, 99, 235, 0.1);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.ads-option-icon {
    width: 42px;
    height: 42px;
    border-radius: 10px;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.125rem;
    flex-shrink: 0;
    box-shadow: 0 4px 8px rgba(37, 99, 235, 0.2);
    transition: all 0.3s ease;
}

.ads-option:hover .ads-option-icon {
    transform: scale(1.05);
    box-shadow: 0 6px 12px rgba(37, 99, 235, 0.3);
}

.ads-option-content h5 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.2rem;
}

.ads-option-content p {
    font-size: 0.75rem;
    color: var(--text-light);
    line-height: 1.3;
}

.ads-cta {
    text-align: center;
    padding: 1.25rem 0 0.75rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}

.ads-contact-btn {
    display: inline-block;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    padding: 0.85rem 2rem;
    border-radius: var(--radius-md);
    font-size: 0.9375rem;
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition);
    box-shadow: 0 4px 6px -1px rgba(37, 99, 235, 0.2);
    position: relative;
    overflow: hidden;
    margin: 0 auto;
    text-align: center;
}

.ads-contact-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: 0.5s;
}

.ads-contact-btn:hover::before {
    left: 100%;
}

.ads-contact-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px -2px rgba(37, 99, 235, 0.35);
    background: linear-gradient(135deg, #2563eb, #1e40af);
}

.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    width: 30px;
    height: 24px;
    position: relative;
    cursor: pointer;
    z-index: 101;
    margin-left: auto;
    flex: 0 0 auto;
}

.menu-bar {
    display: block;
    width: 100%;
    height: 2px;
    background-color: var(--text-dark);
    position: absolute;
    left: 0;
    transition: all 0.3s ease;
}

.menu-bar:nth-child(1) {
    top: 0;
}

.menu-bar:nth-child(2) {
    top: 50%;
    transform: translateY(-50%);
}

.menu-bar:nth-child(3) {
    bottom: 0;
}

.mobile-menu-btn.active .menu-bar:nth-child(1) {
    transform: rotate(45deg);
    top: 50%;
}

.mobile-menu-btn.active .menu-bar:nth-child(2) {
    opacity: 0;
}

.mobile-menu-btn.active .menu-bar:nth-child(3) {
    transform: rotate(-45deg);
    bottom: 40%;
}

/* Mobile Menu Styles */
.mobile-menu {
    position: fixed;
    top: 0;
    right: -100%;
    width: 90%;
    max-width: 420px;
    height: 100vh;
    background-color: white;
    z-index: 1000;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
    transition: right 0.3s ease;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    padding-bottom: 80px; /* Space for bottom premium bar */
    background-image: linear-gradient(to bottom, rgba(37, 99, 235, 0.03), rgba(37, 99, 235, 0.01));
}

.mobile-menu.active {
    right: 0;
}

.mobile-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(to right, rgba(37, 99, 235, 0.05), rgba(37, 99, 235, 0.02));
    position: sticky;
    top: 0;
    z-index: 10;
    backdrop-filter: blur(5px);
}

.mobile-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.mobile-logo i {
    font-size: 1.75rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    filter: drop-shadow(0 0 2px rgba(37, 99, 235, 0.3));
}

.mobile-logo span {
    font-family: 'Poppins', sans-serif;
    font-size: 1.375rem;
    font-weight: 700;
    color: var(--secondary-color);
    letter-spacing: -0.5px;
}

.mobile-menu-close {
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid var(--border-color);
    color: var(--text-dark);
    font-size: 1rem;
    cursor: pointer;
    padding: 0.5rem;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.mobile-menu-close:hover {
    background: var(--primary-color);
    color: white;
    transform: rotate(90deg);
}

.mobile-menu-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 1.5rem;
    gap: 2.25rem;
    overflow-y: auto;
}

.mobile-menu-section {
    display: flex;
    flex-direction: column;
    gap: 0.625rem;
    position: relative;
}

.mobile-menu-section::after {
    content: '';
    position: absolute;
    bottom: -1.125rem;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(to right, var(--border-color), transparent);
}

.mobile-menu-section:last-child::after {
    display: none;
}

.mobile-menu-section-title {
    font-size: 0.8125rem;
    font-weight: 600;
    color: var(--primary-color);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.mobile-menu-section-title::before {
    content: '';
    display: block;
    width: 4px;
    height: 16px;
    background: var(--primary-gradient);
    border-radius: 2px;
}

.mobile-menu-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.875rem 1rem;
    color: var(--text-dark);
    text-decoration: none;
    font-size: 1rem;
    border-radius: var(--radius-md);
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    border: 1px solid transparent;
}

.mobile-menu-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 0;
    background-color: rgba(37, 99, 235, 0.08);
    transition: width 0.2s ease;
    z-index: -1;
}

.mobile-menu-item:hover::before {
    width: 100%;
}

.mobile-menu-item i {
    font-size: 1.25rem;
    color: var(--text-light);
    width: 24px;
    text-align: center;
    transition: all 0.2s ease;
}

.mobile-menu-item.active {
    background-color: rgba(37, 99, 235, 0.1);
    color: var(--primary-color);
    border: 1px solid rgba(37, 99, 235, 0.2);
    font-weight: 500;
}

.mobile-menu-item.active i {
    color: var(--primary-color);
    transform: scale(1.1);
}

.mobile-menu-item:hover {
    transform: translateX(3px);
}

.mobile-menu-item:hover i {
    color: var(--primary-color);
}

.menu-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.menu-item-header div {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.submenu-toggle {
    font-size: 0.875rem;
    color: var(--text-light);
    transition: all 0.3s ease;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(37, 99, 235, 0.05);
}

.mobile-menu-item.has-submenu.active .submenu-toggle {
    transform: rotate(180deg);
    background-color: var(--primary-color);
    color: white;
}

.mobile-submenu {
    display: none;
    padding-left: 2.5rem;
    margin: 0.75rem 0 0.5rem;
    flex-direction: column;
    gap: 0.625rem;
    animation: fadeInDown 0.3s ease;
    border-left: 2px solid rgba(37, 99, 235, 0.2);
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.mobile-menu-item.has-submenu.active .mobile-submenu {
    display: flex;
}

.submenu-item {
    padding: 0.625rem 0.875rem;
    color: var(--text-dark);
    text-decoration: none;
    font-size: 0.9375rem;
    border-radius: var(--radius-md);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    position: relative;
}

.submenu-item::before {
    content: '';
    position: absolute;
    left: -2.5rem;
    top: 50%;
    width: 10px;
    height: 1px;
    background-color: rgba(37, 99, 235, 0.2);
    transform: translateY(-50%);
}

.submenu-item i {
    font-size: 0.9375rem;
    width: 20px;
    text-align: center;
    color: var(--primary-color);
}

.submenu-item:hover {
    background-color: rgba(37, 99, 235, 0.05);
    color: var(--primary-color);
    transform: translateX(3px);
}

.premium-item i {
    color: #ee5253;
}

.ads-item i {
    color: #4facfe;
}

.mobile-premium-toggle {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    border: none;
    padding: 0.875rem;
    border-radius: var(--radius-md);
    font-size: 0.9375rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    margin-top: 1.25rem;
    width: 100%;
    box-shadow: 0 4px 6px -1px rgba(37, 99, 235, 0.3);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.mobile-premium-toggle::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: 0.5s;
}

.mobile-premium-toggle:hover::before,
.mobile-premium-toggle:active::before {
    left: 100%;
}

.mobile-premium-toggle:active {
    transform: scale(0.98);
    box-shadow: 0 2px 3px -1px rgba(37, 99, 235, 0.3);
}

.mobile-premium-toggle i {
    animation: featurePulse 3s infinite;
}

.mobile-login-btn {
    background: var(--primary-gradient);
    color: white;
    border: none;
    padding: 0.875rem;
    border-radius: var(--radius-md);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    margin-bottom: 1.25rem;
    box-shadow: 0 4px 6px -1px rgba(37, 99, 235, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.mobile-login-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: 0.5s;
}

.mobile-login-btn:hover::before,
.mobile-login-btn:active::before {
    left: 100%;
}

.mobile-login-btn:active {
    transform: scale(0.98);
    box-shadow: 0 2px 3px -1px rgba(37, 99, 235, 0.3);
}

.mobile-vpn {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    padding: 0.875rem;
    border-radius: var(--radius-md);
    font-size: 0.9375rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    cursor: pointer;
    border: 1px solid rgba(16, 185, 129, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.mobile-vpn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(16, 185, 129, 0.1), transparent);
    transition: 0.5s;
}

.mobile-vpn:hover::before,
.mobile-vpn:active::before {
    left: 100%;
}

.mobile-vpn:hover {
    background-color: rgba(16, 185, 129, 0.15);
    border-color: rgba(16, 185, 129, 0.3);
}

.mobile-vpn:active {
    transform: scale(0.98);
}

/* Premium Ad Bar Styles */
.premium-ad-bar {
    background: linear-gradient(to bottom, #f8fafc, #f1f5f9);
    border-top: 1px solid var(--border-color);
    padding: 1rem 0;
    width: 100%;
    transition: opacity 0.4s ease, transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    opacity: 1;
    position: relative;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
    display: block;
    z-index: 10;
}

.premium-ad-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
    transform: translateY(-3px);
}

.premium-bar-header {
    display: none;
}

.ad-container {
    max-width: var(--max-width);
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    gap: 2rem;
    align-items: center;
    flex-wrap: nowrap;
}

.ad-item {
    display: flex;
    align-items: center;
    gap: 1.25rem;
    padding: 1rem 1.5rem;
    border-radius: var(--radius-md);
    transition: var(--transition);
    border: 1px solid var(--border-color);
    flex: 1;
    min-width: 0;
    max-width: 33.333%;
    background-color: white;
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
}

.ad-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--primary-gradient);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.ad-item:hover {
    border-color: rgba(37, 99, 235, 0.3);
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.ad-item:hover::before {
    opacity: 1;
}

.ad-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
    flex-shrink: 0;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.ad-item:hover .ad-icon {
    transform: scale(1.05);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.ad-item:nth-child(1) .ad-icon {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.ad-item:nth-child(2) .ad-icon {
    background: linear-gradient(135deg, #4f46e5, #3730a3);
}

.ad-item:nth-child(3) .ad-icon {
    background: linear-gradient(135deg, #2563eb, #1e40af);
}

.ad-content {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    min-width: 0;
    overflow: hidden;
}

.ad-label {
    font-size: 0.65rem;
    font-weight: 700;
    color: var(--text-light);
    letter-spacing: 0.5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.ad-text {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-dark);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.ad-cta {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--primary-color);
    text-decoration: none;
    padding: 0.35rem 0.85rem;
    border: 1px solid var(--primary-color);
    border-radius: var(--radius-md);
    transition: all 0.3s ease;
    white-space: nowrap;
    flex-shrink: 0;
    box-shadow: 0 2px 4px rgba(37, 99, 235, 0.1);
    position: relative;
    overflow: hidden;
}

.ad-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: 0.5s;
}

.ad-cta:hover::before {
    left: 100%;
}

.ad-cta:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(37, 99, 235, 0.2);
}

/* Header Styles */
header {
    background-color: var(--bg-white);
    padding: 3rem 2rem;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
}

.header-content {
    max-width: var(--max-width);
    margin: 0 auto;
}

header h1 {
    font-family: 'Poppins', sans-serif;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--secondary-color);
    margin-bottom: 1rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    display: inline-block;
}

.tagline {
    font-size: 1.125rem;
    color: var(--text-light);
    margin-bottom: 2rem;
}

.header-badges {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 2rem;
}

.badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background-color: rgba(37, 99, 235, 0.05);
    border-radius: var(--radius-md);
    border: 1px solid rgba(37, 99, 235, 0.1);
}

.badge i {
    color: var(--primary-color);
    font-size: 1rem;
}

.badge span {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-dark);
}

/* Main Content Styles */
main {
    flex: 1;
    padding: 2rem;
    max-width: var(--max-width);
    margin: 0 auto;
    width: 100%;
    background-color: var(--bg-light);
    position: relative;
}

main::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 200px;
    background: linear-gradient(180deg, rgba(37, 99, 235, 0.03) 0%, rgba(37, 99, 235, 0) 100%);
    z-index: -1;
}

.tabs {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 0.5rem;
    overflow-x: auto;
}

.tab-btn {
    background: none;
    border: none;
    padding: 0.75rem 1.25rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-light);
    cursor: pointer;
    border-radius: var(--radius-md);
    transition: var(--transition);
    white-space: nowrap;
}

.tab-btn:hover {
    background-color: var(--bg-light);
    color: var(--primary-color);
}

.tab-btn.active {
    background-color: var(--primary-color);
    color: white;
}

.content-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

/* Input Panel Styles */
.input-panel {
    background-color: var(--bg-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.input-group {
    margin-bottom: 1rem;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-dark);
}

input, textarea, select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    color: var(--text-dark);
    background-color: var(--bg-white);
    transition: var(--transition);
}

textarea {
    min-height: 120px;
    resize: vertical;
}

input:focus, textarea:focus, select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* File Upload Styles */
.file-upload-container {
    margin-bottom: 1rem;
}

.file-upload-area {
    border: 2px dashed var(--border-color);
    border-radius: var(--radius-md);
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
}

.file-upload-area:hover, .file-upload-area.highlight {
    border-color: var(--primary-color);
    background-color: rgba(37, 99, 235, 0.05);
}

.file-upload-area i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.file-upload-area p {
    margin-bottom: 1rem;
    color: var(--text-light);
}

.file-upload-btn {
    display: inline-block;
    background-color: var(--primary-light);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.file-upload-btn:hover {
    background-color: var(--primary-dark);
}

.file-info {
    font-size: 0.75rem;
    color: var(--text-lighter);
    margin-top: 0.5rem;
    text-align: center;
}

/* Customization Panel Styles */
.customization-panel {
    background-color: var(--bg-light);
    border-radius: var(--radius-md);
    padding: 1.5rem;
    margin-top: 1rem;
}

.customization-panel h3 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.customization-group {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.customization-group label {
    width: 40%;
    margin-bottom: 0;
}

.customization-group input,
.customization-group select {
    width: 60%;
}

/* Template options */
.template-option {
    display: none;
}

.template-option.active {
    display: flex;
}

/* Toggle switch for live preview */
.preview-toggle {
    margin: 1rem 0;
    display: flex;
    justify-content: flex-end;
}

.toggle-label {
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    gap: 0.5rem;
}

.toggle-label input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-text {
    font-size: 0.875rem;
    color: var(--text-dark);
}

.toggle-label input[type="checkbox"] + .toggle-text::before {
    content: '';
    display: inline-block;
    width: 36px;
    height: 20px;
    background-color: var(--border-color);
    border-radius: 10px;
    margin-right: 0.5rem;
    position: relative;
    transition: var(--transition);
}

.toggle-label input[type="checkbox"] + .toggle-text::after {
    content: '';
    position: absolute;
    left: 2px;
    width: 16px;
    height: 16px;
    background-color: white;
    border-radius: 50%;
    transition: var(--transition);
    transform: translateY(-50%);
    top: 50%;
}

.toggle-label input[type="checkbox"]:checked + .toggle-text::before {
    background-color: var(--primary-color);
}

.toggle-label input[type="checkbox"]:checked + .toggle-text::after {
    left: 18px;
}

/* Template preview styles */
.qr-template-preview {
    margin-bottom: 1rem;
    border: 1px dashed var(--border-color);
    border-radius: var(--radius-md);
    padding: 1rem;
    text-align: center;
    font-size: 0.875rem;
    color: var(--text-light);
}

.logo-upload-container {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo-upload-btn {
    background-color: var(--primary-light);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.logo-upload-btn:hover {
    background-color: var(--primary-dark);
}

#logo-preview {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-sm);
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

#logo-preview img {
    max-width: 100%;
    max-height: 100%;
}

/* Output Panel Styles */
.output-panel {
    background-color: var(--bg-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    padding: 2rem;
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.qr-preview {
    text-align: center;
    background: linear-gradient(to bottom, rgba(245, 247, 250, 0.5) 0%, rgba(255, 255, 255, 1) 100%);
    padding: 2.5rem;
    border-radius: var(--radius-lg);
    position: relative;
    overflow: hidden;
}

.qr-preview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(0, 0, 0, 0.05), transparent);
}

.qr-preview::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(0, 0, 0, 0.05), transparent);
}

.qr-preview h3 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

#qr-code-container {
    margin: 0 auto;
    padding: 20px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    min-height: 250px;
    min-width: 250px;
    position: relative;
    background-color: white;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
}

#qr-code-container img {
    max-width: 100%;
    height: auto;
    display: block;
    border-radius: var(--radius-sm);
}

.qr-info {
    font-size: 0.875rem;
    color: var(--text-light);
    margin-top: 0.75rem;
}

/* QR Code Template Styles */
/* Standard Corporate Template */
.qr-template-standard {
    padding: 20px;
    background-color: white;
    border-radius: 8px;
    box-shadow: var(--shadow-md);
}

/* Event Ticket Template */
.qr-template-event {
    padding: 20px;
    background-color: white;
    border-radius: 8px;
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.qr-template-event::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background-color: var(--accent-color, var(--primary-color));
}

.qr-template-event .template-title {
    position: relative;
    color: white;
    font-weight: 600;
    font-size: 1.2rem;
    margin-bottom: 10px;
    padding-top: 15px;
    z-index: 1;
}

.qr-template-event .template-subtitle {
    position: relative;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    margin-bottom: 30px;
    z-index: 1;
}

.qr-template-event .qr-code-wrapper {
    background-color: white;
    padding: 15px;
    border-radius: 8px;
    display: inline-block;
    margin-top: 10px;
}

/* Mobile Phone Template */
.qr-template-mobile {
    padding: 40px 20px;
    background-color: #f8f9fa;
    border-radius: 30px;
    box-shadow: var(--shadow-md);
    position: relative;
    border: 10px solid #333;
    max-width: 300px;
    margin: 0 auto;
}

.qr-template-mobile::before {
    content: '';
    position: absolute;
    top: 15px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 10px;
    background-color: #333;
    border-radius: 10px;
}

.qr-template-mobile .template-title {
    color: var(--text-dark);
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 5px;
}

.qr-template-mobile .template-subtitle {
    color: var(--text-light);
    font-size: 0.8rem;
    margin-bottom: 20px;
}

.qr-template-mobile .qr-code-wrapper {
    background-color: white;
    padding: 10px;
    border-radius: 8px;
    display: inline-block;
}

/* Business Card Template */
.qr-template-business {
    padding: 20px;
    background-color: white;
    border-radius: 8px;
    box-shadow: var(--shadow-md);
    display: flex;
    flex-direction: row;
    align-items: center;
    text-align: left;
    max-width: 400px;
    margin: 0 auto;
}

.qr-template-business .business-info {
    flex: 1;
    padding-right: 20px;
}

.qr-template-business .template-title {
    color: var(--text-dark);
    font-weight: 600;
    font-size: 1.2rem;
    margin-bottom: 5px;
}

.qr-template-business .template-subtitle {
    color: var(--text-light);
    font-size: 0.9rem;
    margin-bottom: 10px;
}

.qr-template-business .qr-code-wrapper {
    background-color: white;
    padding: 10px;
    border-radius: 8px;
    display: inline-block;
}

/* Social Media Template */
.qr-template-social {
    padding: 20px;
    background: linear-gradient(135deg, var(--accent-color, var(--primary-color)), darken(var(--accent-color, var(--primary-color)), 20%));
    border-radius: 15px;
    box-shadow: var(--shadow-md);
    color: white;
}

.qr-template-social .template-title {
    font-weight: 600;
    font-size: 1.2rem;
    margin-bottom: 5px;
}

.qr-template-social .template-subtitle {
    opacity: 0.9;
    font-size: 0.9rem;
    margin-bottom: 20px;
}

.qr-template-social .qr-code-wrapper {
    background-color: white;
    padding: 15px;
    border-radius: 15px;
    display: inline-block;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.action-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 1rem;
}

.action-btn {
    background-color: var(--bg-white);
    color: var(--text-dark);
    border: 1px solid var(--border-color);
    padding: 0.75rem 1.25rem;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.action-btn:hover {
    background-color: var(--bg-light);
    border-color: var(--primary-light);
    color: var(--primary-color);
}

.action-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.primary-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    width: 100%;
    margin-top: 1rem;
}

.primary-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
}

.primary-btn:active {
    transform: translateY(0);
}

/* Saved Codes Panel */
.saved-codes-panel {
    margin-top: 1rem;
}

.saved-codes-panel h3 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.saved-codes-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 1rem;
    max-height: 200px;
    overflow-y: auto;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
}

.saved-code-item {
    cursor: pointer;
    border-radius: var(--radius-sm);
    overflow: hidden;
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.saved-code-item:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-md);
}

.saved-code-item img {
    width: 100%;
    height: auto;
    display: block;
}

.empty-state {
    grid-column: 1 / -1;
    text-align: center;
    color: var(--text-lighter);
    font-size: 0.875rem;
    padding: 2rem 0;
}

/* Footer Styles */
footer {
    background: linear-gradient(to bottom, var(--bg-white), #f1f5f9);
    border-top: 1px solid var(--border-color);
    padding-top: 4rem;
    position: relative;
    overflow: hidden;
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

.footer-content {
    max-width: var(--max-width);
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1.5fr;
    gap: 3rem;
    position: relative;
}

.footer-section {
    display: flex;
    flex-direction: column;
}

.main-section {
    position: relative;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.25rem;
}

.footer-logo i {
    font-size: 2rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    filter: drop-shadow(0 0 2px rgba(37, 99, 235, 0.3));
}

.footer-logo span {
    font-family: 'Poppins', sans-serif;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--secondary-color);
    letter-spacing: -0.5px;
}

.footer-logo .highlight {
    color: var(--primary-color);
}

.footer-tagline {
    font-size: 1rem;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
    font-weight: 500;
    max-width: 80%;
}

.footer-cta {
    background: linear-gradient(to right, rgba(37, 99, 235, 0.05), rgba(37, 99, 235, 0.1));
    padding: 1.25rem;
    border-radius: var(--radius-md);
    margin-bottom: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    border-left: 3px solid var(--primary-color);
}

.footer-cta-text {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-dark);
}

.footer-cta-btn {
    display: inline-block;
    background: var(--primary-gradient);
    color: white;
    padding: 0.5rem 1.25rem;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition);
    box-shadow: 0 4px 6px -1px rgba(37, 99, 235, 0.2);
    align-self: flex-start;
}

.footer-cta-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 10px -1px rgba(37, 99, 235, 0.3);
}

.social-links {
    display: flex;
    gap: 0.75rem;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 38px;
    height: 38px;
    background: white;
    border-radius: 50%;
    color: var(--primary-color);
    transition: var(--transition);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(37, 99, 235, 0.1);
}

.social-link:hover {
    background: var(--primary-gradient);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 5px 10px rgba(37, 99, 235, 0.2);
}

.footer-section h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
    position: relative;
    padding-bottom: 0.75rem;
}

.footer-section h4::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 3px;
    background: var(--primary-gradient);
    border-radius: 3px;
}

.footer-links {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 0.875rem;
}

.footer-links li {
    position: relative;
    padding-left: 1.25rem;
}

.footer-links li::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    background: var(--primary-color);
    border-radius: 50%;
    opacity: 0.7;
}

.footer-links a {
    font-size: 0.9rem;
    color: var(--text-dark);
    text-decoration: none;
    transition: var(--transition);
    font-weight: 500;
}

.footer-links a:hover {
    color: var(--primary-color);
    padding-left: 0.25rem;
}

.contact-section {
    background-color: white;
    border-radius: var(--radius-md);
    padding: 1.5rem;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03);
    border: 1px solid var(--border-color);
}

.contact-section h4 {
    margin-top: 0;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.contact-item i {
    width: 32px;
    height: 32px;
    background: rgba(37, 99, 235, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 0.875rem;
}

.contact-item span {
    font-size: 0.875rem;
    color: var(--text-dark);
}

.footer-divider {
    max-width: var(--max-width);
    margin: 3rem auto 0;
    height: 1px;
    background: linear-gradient(to right, transparent, var(--border-color), transparent);
}

.footer-bottom {
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: var(--max-width);
    margin-left: auto;
    margin-right: auto;
}

.footer-bottom-left p {
    font-size: 0.875rem;
    color: var(--text-light);
}

.footer-bottom-left a {
    color: var(--text-dark);
    text-decoration: none;
    transition: var(--transition);
}

.footer-bottom-left a:hover {
    color: var(--primary-color);
}

.footer-badges {
    display: flex;
    gap: 1.5rem;
}

.footer-badge {
    font-size: 0.75rem;
    color: var(--text-dark);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background-color: white;
    padding: 0.5rem 0.75rem;
    border-radius: var(--radius-md);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
    border: 1px solid var(--border-color);
}

.footer-badge i {
    color: var(--primary-color);
}

/* Responsive Styles */
@media (max-width: 1024px) {
    .content-container {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 1024px) {
    .ad-container {
        flex-direction: column;
        gap: 0.75rem;
    }

    .ad-item {
        width: 100%;
    }
}

@media (min-width: 1201px) {
    .nav-container {
        justify-content: space-between;
    }

    .nav-left {
        flex: 0 1 auto;
    }

    .desktop-links {
        margin-left: 2rem;
    }
}

@media (max-width: 1200px) {
    .ads-dropdown {
        width: 700px;
    }

    .nav-container {
        justify-content: space-between;
    }

    .desktop-links {
        margin-left: 1rem;
    }
}

@media (max-width: 1024px) {
    .solutions-dropdown,
    .features-dropdown {
        width: 500px;
    }

    .ads-dropdown {
        width: 700px;
    }

    .ads-sections {
        flex-wrap: wrap;
        gap: 2rem;
    }

    .ads-section {
        flex: 1 1 calc(50% - 1rem);
        max-width: calc(50% - 1rem);
        min-width: 250px;
    }

    .ad-container {
        flex-wrap: wrap;
        gap: 1rem;
    }

    .ad-item {
        flex: 1 1 calc(50% - 1rem);
        max-width: calc(50% - 1rem);
        min-width: 250px;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    main {
        padding: 1rem;
    }

    .customization-group {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .customization-group label,
    .customization-group input,
    .customization-group select {
        width: 100%;
    }

    .announcement-slide {
        font-size: 0.75rem;
    }

    .nav-container {
        padding: 0.75rem 1rem;
        justify-content: space-between;
    }

    .desktop-links {
        display: none;
    }

    .nav-right {
        display: none;
    }

    .mobile-menu-btn {
        display: block;
    }

    .nav-left {
        gap: 1rem;
        flex: 0 1 auto;
        width: auto;
    }

    .dropdown-menu,
    .features-dropdown,
    .solutions-dropdown,
    .ads-dropdown {
        position: fixed;
        top: 0;
        left: 0;
        width: 100% !important;
        height: 100vh;
        max-width: 100% !important;
        border-radius: 0;
        margin-top: 0;
        z-index: 1001;
        overflow-y: auto;
    }

    .logo-text {
        font-size: 1.25rem;
    }

    .header-badges {
        flex-direction: column;
        gap: 0.75rem;
    }

    .footer-content {
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        padding: 0 1.5rem;
    }

    .main-section {
        grid-column: span 2;
    }

    .contact-section {
        grid-column: span 2;
    }

    .footer-badges {
        flex-wrap: wrap;
        justify-content: center;
        gap: 1rem;
    }

    .footer-bottom {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }

    .premium-ad-bar {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        z-index: 98;
        box-shadow: 0 -4px 15px rgba(0, 0, 0, 0.1);
        transform: translateY(100%);
        transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        background: linear-gradient(to bottom, #ffffff, #f8fafc);
        border-top: 3px solid var(--primary-color);
        border-radius: 15px 15px 0 0;
        max-height: 80vh;
        overflow-y: auto;
    }

    .ad-container {
        flex-direction: column;
        flex-wrap: wrap;
    }

    .ad-item {
        max-width: 100%;
        margin-bottom: 0.75rem;
    }

    .premium-ad-bar.mobile-visible {
        transform: translateY(0);
    }

    .premium-ad-bar::-webkit-scrollbar {
        width: 5px;
    }

    .premium-ad-bar::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.05);
    }

    .premium-ad-bar::-webkit-scrollbar-thumb {
        background: var(--primary-color);
        border-radius: 5px;
    }

    /* Mobile Menu Overlay */
    .mobile-menu-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(15, 23, 42, 0.7);
        z-index: 999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .mobile-menu-overlay.active {
        opacity: 1;
        visibility: visible;
    }
}

@media (max-width: 480px) {
    .announcement-bar {
        display: none;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        padding: 0 1rem;
    }

    .main-section {
        grid-column: span 1;
    }

    .contact-section {
        grid-column: span 1;
    }

    .footer-tagline {
        max-width: 100%;
    }

    .footer-cta {
        padding: 1rem;
    }

    .footer-bottom {
        flex-direction: column;
        gap: 1.5rem;
        padding: 1.5rem 1rem;
    }

    .footer-badges {
        flex-direction: column;
        gap: 0.75rem;
        width: 100%;
    }

    .footer-badge {
        width: 100%;
        justify-content: center;
    }

    .social-links {
        justify-content: center;
    }

    .header h1 {
        font-size: 1.75rem;
    }

    .tagline {
        font-size: 0.875rem;
    }

    /* Mobile Premium Ad Bar */
    .premium-ad-bar {
        padding: 1rem 0 1.5rem;
    }

    .premium-bar-header {
        display: block;
        text-align: center;
        padding: 1rem 1.5rem;
        margin-bottom: 0.5rem;
    }

    .premium-bar-header h3 {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--text-dark);
        margin-bottom: 0.5rem;
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .premium-bar-header p {
        font-size: 0.875rem;
        color: var(--text-light);
        max-width: 80%;
        margin: 0 auto;
    }

    .premium-ad-bar .ad-container {
        padding: 0 1rem;
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .premium-ad-bar .ad-item {
        padding: 1rem;
        border-radius: 12px;
        background: white;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(37, 99, 235, 0.1);
        transition: all 0.3s ease;
        transform: translateY(0);
    }

    .premium-ad-bar .ad-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.08);
        border-color: rgba(37, 99, 235, 0.2);
    }

    .premium-ad-bar .ad-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .premium-ad-bar .ad-label {
        font-size: 0.65rem;
        font-weight: 700;
        letter-spacing: 0.5px;
    }

    .premium-ad-bar .ad-text {
        font-size: 0.9rem;
        font-weight: 600;
    }

    .premium-ad-bar .ad-cta {
        font-size: 0.8rem;
        padding: 0.4rem 0.75rem;
        border-radius: 8px;
        font-weight: 600;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .premium-ad-bar .ad-cta:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    /* Close button for mobile premium bar */
    .mobile-premium-close {
        position: absolute;
        top: 0.75rem;
        right: 0.75rem;
        background: white;
        border: 1px solid var(--border-color);
        color: var(--text-dark);
        font-size: 1rem;
        cursor: pointer;
        z-index: 10;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .mobile-premium-close:hover {
        background: var(--primary-color);
        color: white;
        transform: rotate(90deg);
    }
}

@media (max-width: 480px) {
    header {
        padding: 1rem;
        flex-direction: column;
        align-items: flex-start;
    }

    .tabs {
        padding-bottom: 0.25rem;
    }

    .tab-btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.75rem;
    }

    .action-buttons {
        flex-direction: column;
    }

    .action-btn {
        width: 100%;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .footer-bottom {
        flex-direction: column;
        gap: 1rem;
    }
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.9);
    background-color: var(--bg-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    width: 90%;
    max-width: 500px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal.active {
    opacity: 1;
    visibility: visible;
    transform: translate(-50%, -50%) scale(1);
}

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(15, 23, 42, 0.7);
    backdrop-filter: blur(4px);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-dark);
}

.close-modal {
    background: none;
    border: none;
    color: var(--text-light);
    font-size: 1.25rem;
    cursor: pointer;
    transition: var(--transition);
}

.close-modal:hover {
    color: var(--danger-color);
}

.modal-body {
    padding: 1.5rem;
}

/* Login Modal Styles */
.login-options {
    margin-bottom: 1.5rem;
}

.login-option {
    width: 100%;
    padding: 0.75rem;
    margin-bottom: 0.75rem;
    background-color: var(--bg-white);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-dark);
    cursor: pointer;
    transition: var(--transition);
}

.login-option:hover {
    background-color: var(--bg-light);
    border-color: var(--primary-light);
}

.login-option i {
    font-size: 1.25rem;
}

.login-divider {
    position: relative;
    text-align: center;
    margin: 1.5rem 0;
}

.login-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background-color: var(--border-color);
}

.login-divider span {
    position: relative;
    background-color: var(--bg-white);
    padding: 0 1rem;
    font-size: 0.875rem;
    color: var(--text-light);
}

.form-group {
    margin-bottom: 1.25rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-dark);
}

.form-group input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    transition: var(--transition);
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.password-input {
    position: relative;
}

.toggle-password {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.checkbox-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--text-light);
    cursor: pointer;
}

.checkbox-container input {
    margin: 0;
}

.forgot-password {
    font-size: 0.875rem;
    color: var(--primary-color);
    text-decoration: none;
}

.forgot-password:hover {
    text-decoration: underline;
}

.login-submit-btn {
    width: 100%;
    padding: 0.75rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.login-submit-btn:hover {
    background-color: var(--primary-dark);
}

.signup-link {
    margin-top: 1.5rem;
    text-align: center;
    font-size: 0.875rem;
    color: var(--text-light);
}

.signup-link a {
    color: var(--primary-color);
    text-decoration: none;
}

.signup-link a:hover {
    text-decoration: underline;
}

/* VPN Modal Styles */
.vpn-status {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background-color: rgba(239, 68, 68, 0.1);
    border-radius: var(--radius-md);
}

.vpn-icon {
    width: 48px;
    height: 48px;
    background-color: rgba(239, 68, 68, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--danger-color);
    font-size: 1.5rem;
}

.vpn-info h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.25rem;
}

.vpn-info p {
    font-size: 0.875rem;
    color: var(--text-light);
}

.vpn-features {
    margin-bottom: 1.5rem;
}

.vpn-feature {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1rem;
}

.vpn-feature i {
    color: var(--primary-color);
    font-size: 1.25rem;
    margin-top: 0.25rem;
}

.vpn-feature h5 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.25rem;
}

.vpn-feature p {
    font-size: 0.75rem;
    color: var(--text-light);
}

.vpn-activate-btn {
    width: 100%;
    padding: 0.75rem;
    background-color: var(--success-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    margin-bottom: 1rem;
}

.vpn-activate-btn:hover {
    background-color: #0d9488;
}

.vpn-note {
    text-align: center;
    font-size: 0.75rem;
    color: var(--text-light);
}

.vpn-note a {
    color: var(--primary-color);
    text-decoration: none;
}

.vpn-note a:hover {
    text-decoration: underline;
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem;
    background-color: white;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    z-index: 1000;
    max-width: 350px;
    transform: translateX(120%);
    transition: transform 0.3s ease;
    border-left: 4px solid var(--primary-color);
}

.notification.show {
    transform: translateX(0);
}

.notification i {
    font-size: 1.25rem;
    flex-shrink: 0;
}

.notification span {
    font-size: 0.875rem;
    color: var(--text-dark);
    flex-grow: 1;
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    font-size: 0.875rem;
    padding: 0.25rem;
    transition: var(--transition);
}

.notification-close:hover {
    color: var(--text-dark);
}

.notification-success {
    border-left-color: var(--success-color);
}

.notification-success i {
    color: var(--success-color);
}

.notification-error {
    border-left-color: var(--danger-color);
}

.notification-error i {
    color: var(--danger-color);
}

.notification-info i {
    color: var(--primary-color);
}

/* Animation Styles */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

/* Loading Animation */
.loading-spinner {
    display: inline-block;
    width: 50px;
    height: 50px;
    border: 3px solid rgba(37, 99, 235, 0.2);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}
