/* Settings Page Styles */
:root {
    --primary-color: #4a6cf7;
    --primary-dark: #3a5ce5;
    --primary-light: #6a8cf9;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #6366f1;
    --text-dark: #1f2937;
    --text-medium: #4b5563;
    --text-light: #9ca3af;
    --bg-white: #ffffff;
    --bg-light: #f9fafb;
    --bg-medium: #f3f4f6;
    --border-color: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --transition: all 0.2s ease;
}

/* Settings Layout */
.settings-container {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: 1.5rem;
}

/* Settings Sidebar */
.settings-sidebar {
    position: sticky;
    top: 1.5rem;
}

/* Settings Navigation Pills */
.nav-pills .nav-link {
    color: var(--text-dark);
    font-weight: 500;
    padding: 0.75rem 1rem;
    border-radius: var(--radius-md);
    transition: var(--transition);
    margin-bottom: 0.5rem;
}

.nav-pills .nav-link:hover {
    background-color: var(--bg-medium);
    color: var(--primary-color);
}

.nav-pills .nav-link.active {
    background-color: var(--primary-color);
    color: white;
}

.nav-pills .nav-link i {
    width: 1.25rem;
    text-align: center;
    margin-right: 0.5rem;
}

/* Settings Content */
.settings-content {
    background-color: var(--bg-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

.settings-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--bg-light);
}

.settings-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0;
}

.settings-body {
    padding: 1.5rem;
}

/* Profile Image */
.profile-image-container {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto;
    background-color: var(--bg-medium);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-md);
}

.profile-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-image-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    color: var(--text-light);
}

/* Form Styling */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
}

.form-control {
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
    padding: 0.625rem 0.875rem;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-light);
    box-shadow: 0 0 0 0.2rem rgba(74, 108, 247, 0.25);
}

.form-text {
    font-size: 0.875rem;
    color: var(--text-medium);
}

/* Responsive */
@media (max-width: 991.98px) {
    .settings-container {
        grid-template-columns: 1fr;
    }
    
    .settings-sidebar {
        position: static;
        margin-bottom: 1.5rem;
    }
}

/* Fix for tab headers under Profile */
.nav-pills button.nav-link {
    color: var(--text-dark);
    font-weight: 500;
}

.nav-pills button.nav-link:hover {
    color: var(--primary-color);
}

.nav-pills button.nav-link.active {
    color: white;
}
