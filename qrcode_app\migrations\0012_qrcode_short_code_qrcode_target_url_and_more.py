# Generated by Django 5.1.7 on 2025-05-28 23:11

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('qrcode_app', '0011_qrscanlog_latitude_qrscanlog_longitude'),
    ]

    operations = [
        migrations.AddField(
            model_name='qrcode',
            name='short_code',
            field=models.CharField(blank=True, help_text="Short code for direct redirects (e.g., 'abc123')", max_length=20, null=True, unique=True),
        ),
        migrations.AddField(
            model_name='qrcode',
            name='target_url',
            field=models.URLField(blank=True, help_text='Current target URL for dynamic redirects', null=True),
        ),
        migrations.CreateModel(
            name='DynamicQRRedirect',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('current_url', models.URLField(help_text='Current destination URL')),
                ('backup_url', models.URLField(blank=True, help_text='Backup URL if primary fails', null=True)),
                ('is_active', models.BooleanField(default=True, help_text='Enable/disable dynamic redirects')),
                ('redirect_count', models.PositiveIntegerField(default=0, help_text='Number of times URL has been changed')),
                ('max_redirects', models.PositiveIntegerField(default=10, help_text='Maximum URL changes allowed')),
                ('total_clicks', models.PositiveIntegerField(default=0)),
                ('last_accessed', models.DateTimeField(blank=True, null=True)),
                ('enable_analytics', models.BooleanField(default=False, help_text='Track detailed click analytics')),
                ('enable_geo_redirect', models.BooleanField(default=False, help_text='Different URLs for different countries')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('qr_code', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='dynamic_redirect', to='qrcode_app.qrcode')),
            ],
            options={
                'verbose_name': 'Dynamic QR Redirect',
                'verbose_name_plural': 'Dynamic QR Redirects',
            },
        ),
    ]
