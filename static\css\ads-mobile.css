/* Ads Mobile Optimization Styles */

/* Ensure text is visible on dark backgrounds */
.bg-dark h1, .bg-dark h2, .bg-dark h3, .bg-dark h4, .bg-dark h5, .bg-dark h6,
.bg-dark .h1, .bg-dark .h2, .bg-dark .h3, .bg-dark .h4, .bg-dark .h5, .bg-dark .h6,
.bg-primary h1, .bg-primary h2, .bg-primary h3, .bg-primary h4, .bg-primary h5, .bg-primary h6,
.bg-primary .h1, .bg-primary .h2, .bg-primary .h3, .bg-primary .h4, .bg-primary .h5, .bg-primary .h6,
.bg-secondary h1, .bg-secondary h2, .bg-secondary h3, .bg-secondary h4, .bg-secondary h5, .bg-secondary h6,
.bg-secondary .h1, .bg-secondary .h2, .bg-secondary .h3, .bg-secondary .h4, .bg-secondary .h5, .bg-secondary .h6,
.bg-info h1, .bg-info h2, .bg-info h3, .bg-info h4, .bg-info h5, .bg-info h6,
.bg-info .h1, .bg-info .h2, .bg-info .h3, .bg-info .h4, .bg-info .h5, .bg-info .h6,
.bg-success h1, .bg-success h2, .bg-success h3, .bg-success h4, .bg-success h5, .bg-success h6,
.bg-success .h1, .bg-success .h2, .bg-success .h3, .bg-success .h4, .bg-success .h5, .bg-success .h6,
.bg-danger h1, .bg-danger h2, .bg-danger h3, .bg-danger h4, .bg-danger h5, .bg-danger h6,
.bg-danger .h1, .bg-danger .h2, .bg-danger .h3, .bg-danger .h4, .bg-danger .h5, .bg-danger .h6,
.bg-warning h1, .bg-warning h2, .bg-warning h3, .bg-warning h4, .bg-warning h5, .bg-warning h6,
.bg-warning .h1, .bg-warning .h2, .bg-warning .h3, .bg-warning .h4, .bg-warning .h5, .bg-warning .h6 {
    color: #ffffff !important;
}

/* Base Mobile Styles for Ads */
@media (max-width: 767.98px) {
    /* General Improvements */
    .container.ad-container {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    /* Typography adjustments */
    .ad-section h1, .ad-section .h1 {
        font-size: 1.75rem;
    }

    .ad-section h2, .ad-section .h2 {
        font-size: 1.5rem;
    }

    .ad-section h3, .ad-section .h3 {
        font-size: 1.25rem;
    }

    .ad-section .lead {
        font-size: 1rem;
    }

    /* Card improvements */
    .ad-card {
        margin-bottom: 1.25rem;
        border-radius: 0.5rem;
    }

    .ad-card .card-body {
        padding: 1.25rem;
    }

    /* Button improvements */
    .ad-button {
        width: 100%;
        padding: 0.75rem 1rem;
        margin-bottom: 0.5rem;
        font-size: 1rem;
    }

    /* Form improvements */
    .ad-form .form-control,
    .ad-form .form-select,
    .form-control,
    .form-select,
    input[type="text"],
    input[type="email"],
    input[type="password"],
    input[type="number"],
    input[type="tel"],
    input[type="url"],
    input[type="search"],
    textarea,
    select {
        font-size: 16px !important; /* Prevent iOS zoom */
        padding: 0.75rem 1rem;
        height: auto;
        min-height: 44px; /* Ensure touch-friendly size */
        border-radius: 8px;
        margin-bottom: 1rem;
    }

    .ad-form .form-label,
    .form-label,
    label {
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
        font-weight: 500;
    }

    /* Improve form buttons */
    .form-group {
        margin-bottom: 1.25rem;
    }

    /* Improve checkboxes and radio buttons */
    .form-check {
        padding-left: 2rem;
        margin-bottom: 1rem;
    }

    .form-check-input {
        width: 1.25rem;
        height: 1.25rem;
        margin-left: -2rem;
    }

    /* Table improvements */
    .ad-table {
        font-size: 0.9rem;
    }

    .ad-table th,
    .ad-table td {
        padding: 0.75rem 0.5rem;
    }

    /* Stat cards */
    .ad-stat-card {
        padding: 1rem;
        margin-bottom: 1rem;
        border-radius: 0.5rem;
    }

    .ad-stat-value {
        font-size: 1.5rem;
    }

    .ad-stat-label {
        font-size: 0.8rem;
    }

    /* Ad list improvements */
    .ad-list-item {
        padding: 1rem;
        margin-bottom: 1rem;
        border-radius: 0.5rem;
    }

    .ad-list-title {
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
    }

    .ad-list-meta {
        font-size: 0.8rem;
        margin-bottom: 0.75rem;
    }

    /* Ad detail improvements */
    .ad-detail-header {
        margin-bottom: 1.25rem;
    }

    .ad-detail-title {
        font-size: 1.5rem;
        margin-bottom: 0.75rem;
    }

    .ad-detail-meta {
        font-size: 0.8rem;
        margin-bottom: 1rem;
    }

    .ad-detail-content {
        font-size: 0.95rem;
        line-height: 1.5;
    }

    /* Ad creation improvements */
    .ad-creation-step {
        padding: 1.25rem;
        margin-bottom: 1.25rem;
    }

    .ad-creation-title {
        font-size: 1.25rem;
        margin-bottom: 1rem;
    }

    .ad-preview {
        padding: 1rem;
        margin-top: 1.25rem;
        margin-bottom: 1.25rem;
    }

    /* Ad dashboard improvements */
    .ad-dashboard-card {
        padding: 1.25rem;
        margin-bottom: 1.25rem;
    }

    .ad-dashboard-title {
        font-size: 1.25rem;
        margin-bottom: 1rem;
    }

    .ad-dashboard-value {
        font-size: 1.75rem;
        margin-bottom: 0.5rem;
    }

    /* Touch-friendly improvements */
    .ad-touch-target {
        min-height: 44px;
        min-width: 44px;
    }

    /* Spacing improvements */
    .ad-section {
        margin-bottom: 2rem;
    }

    .ad-section-title {
        margin-bottom: 1.25rem;
    }

    /* Fix for fixed position elements */
    .ad-fixed-bottom {
        bottom: 65px; /* Account for bottom navigation */
    }
}

/* Small mobile devices */
@media (max-width: 575.98px) {
    /* Further reduce sizes */
    .ad-section h1, .ad-section .h1 {
        font-size: 1.5rem;
    }

    .ad-section h2, .ad-section .h2 {
        font-size: 1.25rem;
    }

    .ad-section h3, .ad-section .h3 {
        font-size: 1.1rem;
    }

    /* Adjust card padding */
    .ad-card .card-body {
        padding: 1rem;
    }

    /* Simplify tables */
    .ad-table-simple th:nth-child(3),
    .ad-table-simple td:nth-child(3) {
        display: none;
    }

    /* Stack stat cards */
    .ad-stats-container {
        flex-direction: column;
    }

    .ad-stat-card {
        width: 100%;
        margin-right: 0;
    }
}

/* Landscape mode optimizations */
@media (max-width: 767.98px) and (orientation: landscape) {
    .ad-section {
        margin-bottom: 1.5rem;
    }

    .ad-card {
        margin-bottom: 1rem;
    }

    .ad-fixed-bottom {
        bottom: 55px; /* Account for smaller bottom navigation in landscape */
    }
}
