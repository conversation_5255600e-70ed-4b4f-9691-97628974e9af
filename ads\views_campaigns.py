from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Sum
from django.utils import timezone
from datetime import datetime
from .models import Campaign, Ad

@login_required
def campaign_list(request):
    """
    List all campaigns for the current user
    """
    # Get user's campaigns
    user_campaigns = Campaign.objects.filter(user=request.user)

    # Filter by status if provided
    status = request.GET.get('status')
    if status:
        user_campaigns = user_campaigns.filter(status=status)

    # Calculate total metrics
    total_campaigns = user_campaigns.count()
    active_campaigns = user_campaigns.filter(status='active').count()
    total_impressions = sum(campaign.total_impressions for campaign in user_campaigns)
    total_clicks = sum(campaign.total_clicks for campaign in user_campaigns)

    # Paginate the results
    paginator = Paginator(user_campaigns, 10)  # Show 10 campaigns per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'campaigns': page_obj,
        'page_obj': page_obj,
        'total_campaigns': total_campaigns,
        'active_campaigns': active_campaigns,
        'total_impressions': total_impressions,
        'total_clicks': total_clicks,
    }

    return render(request, 'ads/campaign_list.html', context)

@login_required
def campaign_detail(request, slug):
    """
    View details of a specific campaign
    """
    # Get the campaign
    campaign = get_object_or_404(Campaign, slug=slug, user=request.user)

    # Get campaign analytics data
    from .models import CampaignAnalytics
    from django.utils import timezone
    import json

    # Get the last 30 days of analytics data
    end_date = timezone.now().date()
    start_date = end_date - timezone.timedelta(days=30)

    campaign_analytics = CampaignAnalytics.objects.filter(
        campaign=campaign,
        date__gte=start_date,
        date__lte=end_date
    ).order_by('date')

    # Prepare data for charts
    analytics_data = {
        'labels': [],
        'impressions': [],
        'clicks': [],
        'ctr': []
    }

    # Fill in data from analytics records
    if campaign_analytics.exists():
        for record in campaign_analytics:
            analytics_data['labels'].append(record.date.strftime('%b %d'))
            analytics_data['impressions'].append(record.impressions)
            analytics_data['clicks'].append(record.clicks)

            # Calculate CTR
            if record.impressions > 0:
                ctr = (record.clicks / record.impressions) * 100
            else:
                ctr = 0

            analytics_data['ctr'].append(round(ctr, 2))
    else:
        # If no analytics data, provide empty datasets with dates
        current_date = start_date
        while current_date <= end_date:
            analytics_data['labels'].append(current_date.strftime('%b %d'))
            analytics_data['impressions'].append(0)
            analytics_data['clicks'].append(0)
            analytics_data['ctr'].append(0)
            current_date += timezone.timedelta(days=1)

    context = {
        'campaign': campaign,
        'campaign_analytics': campaign_analytics,
        'campaign_analytics_json': json.dumps(analytics_data)
    }

    return render(request, 'ads/campaign_detail.html', context)

@login_required
def campaign_create(request):
    """
    Create a new campaign
    """
    if request.method == 'POST':
        # Process form data
        name = request.POST.get('name')
        description = request.POST.get('description')
        status = request.POST.get('status')
        start_date = request.POST.get('start_date')
        end_date = request.POST.get('end_date')
        target_audience = request.POST.get('target_audience')
        target_location = request.POST.get('target_location')
        budget = request.POST.get('budget')
        auto_activate = request.POST.get('auto_activate') == 'on'

        # Convert date strings to datetime objects
        try:
            start_datetime = timezone.make_aware(datetime.strptime(start_date, "%Y-%m-%d %H:%M"))
            end_datetime = timezone.make_aware(datetime.strptime(end_date, "%Y-%m-%d %H:%M"))
        except ValueError:
            messages.error(request, "Invalid date format. Please use the date picker.")
            return redirect('ads:campaign_create')

        # Create campaign
        campaign = Campaign.objects.create(
            user=request.user,
            name=name,
            description=description,
            status=status,
            start_date=start_datetime,
            end_date=end_datetime,
            target_audience=target_audience,
            target_location=target_location,
            budget=budget
        )

        messages.success(request, 'Campaign created successfully!')
        return redirect('ads:campaign_detail', slug=campaign.slug)

    context = {
        'auto_activate': True,  # Default value
    }

    return render(request, 'ads/campaign_form.html', context)

@login_required
def campaign_edit(request, slug):
    """
    Edit an existing campaign
    """
    # Get the campaign
    campaign = get_object_or_404(Campaign, slug=slug, user=request.user)

    if request.method == 'POST':
        # Process form data
        name = request.POST.get('name')
        description = request.POST.get('description')
        status = request.POST.get('status')
        start_date = request.POST.get('start_date')
        end_date = request.POST.get('end_date')
        target_audience = request.POST.get('target_audience')
        target_location = request.POST.get('target_location')
        budget = request.POST.get('budget')
        auto_activate = request.POST.get('auto_activate') == 'on'

        # Convert date strings to datetime objects
        try:
            start_datetime = timezone.make_aware(datetime.strptime(start_date, "%Y-%m-%d %H:%M"))
            end_datetime = timezone.make_aware(datetime.strptime(end_date, "%Y-%m-%d %H:%M"))
        except ValueError:
            messages.error(request, "Invalid date format. Please use the date picker.")
            return redirect('ads:campaign_edit', slug=slug)

        # Update campaign
        campaign.name = name
        campaign.description = description
        campaign.status = status
        campaign.start_date = start_datetime
        campaign.end_date = end_datetime
        campaign.target_audience = target_audience
        campaign.target_location = target_location
        campaign.budget = budget
        campaign.save()

        messages.success(request, 'Campaign updated successfully!')
        return redirect('ads:campaign_detail', slug=campaign.slug)

    context = {
        'campaign': campaign,
        'auto_activate': False,  # Default value
    }

    return render(request, 'ads/campaign_form.html', context)

@login_required
def campaign_delete(request, slug):
    """
    Delete a campaign
    """
    # Get the campaign
    campaign = get_object_or_404(Campaign, slug=slug, user=request.user)

    if request.method == 'POST':
        campaign.delete()
        messages.success(request, "Campaign deleted successfully.")
        return redirect('ads:campaign_list')

    context = {
        'campaign': campaign,
    }

    return render(request, 'ads/campaign_delete.html', context)

@login_required
def campaign_activate(request, slug):
    """
    Activate a campaign
    """
    # Get the campaign
    campaign = get_object_or_404(Campaign, slug=slug, user=request.user)

    # Check if the campaign can be activated
    if campaign.status not in ['draft', 'paused']:
        messages.error(request, "This campaign cannot be activated in its current status.")
        return redirect('ads:campaign_detail', slug=slug)

    # Update the status
    campaign.status = 'active'
    campaign.save()

    messages.success(request, "Campaign activated successfully.")
    return redirect('ads:campaign_detail', slug=slug)

@login_required
def campaign_pause(request, slug):
    """
    Pause a campaign
    """
    # Get the campaign
    campaign = get_object_or_404(Campaign, slug=slug, user=request.user)

    # Check if the campaign can be paused
    if campaign.status != 'active':
        messages.error(request, "Only active campaigns can be paused.")
        return redirect('ads:campaign_detail', slug=slug)

    # Update the status
    campaign.status = 'paused'
    campaign.save()

    messages.success(request, "Campaign paused successfully.")
    return redirect('ads:campaign_detail', slug=slug)
