/* Home Page Animations - Corporate, Luxurious, Professional */

/* Enhanced CTA Buttons */
.btn {
    position: relative;
    overflow: hidden;
    transition: all 0.4s ease;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
    transition: all 0.6s ease;
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #4a6cf7, #3a5bd9);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #3a5bd9, #2a4bc9);
}

/* Hero Section Animations */
.hero-section {
    position: relative;
    overflow: hidden;
    padding: 3rem 0;
    opacity: 0;
    animation: fadeInUp 1.2s ease forwards;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(74, 108, 247, 0.03) 0%, rgba(0, 150, 136, 0.03) 100%);
    z-index: -1;
    transform: skewY(-2deg);
    transform-origin: top left;
}

.hero-section h1 {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.8s ease forwards 0.3s;
    position: relative;
    display: inline-block;
}

.hero-section h1::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 0;
    height: 3px;
    background: linear-gradient(90deg, #4a6cf7, #00d2ff);
    animation: lineGrow 1.2s ease-out forwards 1s;
}

.hero-section .lead {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.8s ease forwards 0.5s;
}

.hero-section .badge {
    opacity: 0;
    transform: translateY(15px);
    animation: fadeInUp 0.6s ease forwards;
}

.hero-section .badge:nth-child(1) {
    animation-delay: 0.7s;
}

.hero-section .badge:nth-child(2) {
    animation-delay: 0.9s;
}

.hero-section .badge:nth-child(3) {
    animation-delay: 1.1s;
}

.hero-section p.mb-4 {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.8s ease forwards 0.9s;
}

.hero-section .btn {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.8s ease forwards 1.1s;
}

.hero-section .btn-premium {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.8s ease forwards 1.3s;
}

/* Card Animation */
.hero-section .card {
    opacity: 0;
    transform: translateX(50px);
    animation: fadeInRight 1s ease forwards 0.7s;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border: none;
    border-radius: 1rem;
    overflow: hidden;
    transition: all 0.5s ease;
}

.hero-section .card:hover {
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    transform: translateY(-5px);
}

.hero-section .card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, #4a6cf7, #00d2ff);
    opacity: 0;
    animation: fadeIn 0.5s ease forwards 1.5s;
}

.hero-section .card-title {
    position: relative;
    display: inline-block;
    margin-bottom: 1.5rem;
}

.hero-section .card-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 50px;
    height: 2px;
    background: linear-gradient(90deg, #4a6cf7, #00d2ff);
}

/* Feature Section Animations */
.feature-section {
    opacity: 0;
    animation: fadeIn 1s ease forwards 1.5s;
}

.section-title {
    position: relative;
    display: inline-block;
    margin-bottom: 1rem;
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.8s ease forwards 1.7s;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, #4a6cf7, #00d2ff);
}

.section-subtitle {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.8s ease forwards 1.9s;
    margin-bottom: 2rem;
}

.feature-card {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.8s ease forwards;
    border: none;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    transition: all 0.5s ease;
    height: 100%;
    background: white;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
}

.feature-card:nth-child(1) {
    animation-delay: 2.0s;
}

.feature-card:nth-child(2) {
    animation-delay: 2.1s;
}

.feature-card:nth-child(3) {
    animation-delay: 2.2s;
}

.feature-card:nth-child(4) {
    animation-delay: 2.3s;
}

.feature-card:nth-child(5) {
    animation-delay: 2.4s;
}

.feature-card:nth-child(6) {
    animation-delay: 2.5s;
}

.feature-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, rgba(74, 108, 247, 0.1), rgba(0, 150, 136, 0.1));
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: #4a6cf7;
    transition: all 0.5s ease;
}

.feature-card:hover .feature-icon {
    background: linear-gradient(135deg, #4a6cf7, #00d2ff);
    color: white;
    transform: rotateY(180deg);
}

/* CTA Section Animation */
.cta-section {
    opacity: 1; /* Make visible by default */
    transition: all 0.8s ease-out;
    margin: 2.5rem 0 3rem; /* Reduced top and bottom margins */
}

/* Enhanced animation when visible */
.cta-section.visible {
    animation: gentleBounce 1s ease-out;
}

@keyframes gentleBounce {
    0% {
        transform: translateY(20px);
        opacity: 0.8;
    }
    60% {
        transform: translateY(-5px);
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

.cta-card {
    background: linear-gradient(135deg, #ffffff, #f8fafc);
    border-radius: 1rem;
    padding: 2.5rem 2rem; /* Reduced padding */
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(74, 108, 247, 0.1);
    transition: all 0.5s ease;
}

.cta-card:hover {
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    transform: translateY(-5px);
}

.cta-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%234a6cf7' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
    opacity: 0.5;
}

.cta-card::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, #4a6cf7, #00d2ff);
    opacity: 0.7;
}

.cta-card h2 {
    opacity: 1;
    margin-bottom: 1.25rem; /* Reduced bottom margin */
    color: #1e293b;
    font-weight: 700;
    font-size: 2.25rem; /* Slightly smaller font size */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    position: relative;
    display: inline-block;
    transition: all 0.6s ease;
}

.cta-section.visible .cta-card h2 {
    animation: fadeInDown 0.8s ease-out;
}

@keyframes fadeInDown {
    from {
        opacity: 0.7;
        transform: translateY(-15px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.cta-card h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, #4a6cf7, #00d2ff);
    transition: width 0.8s ease;
}

.cta-section.visible .cta-card h2::after {
    animation: lineExpand 1s ease-out;
}

@keyframes lineExpand {
    from {
        width: 0;
    }
    to {
        width: 80px;
    }
}

.cta-card p {
    opacity: 1;
    margin-bottom: 1.5rem; /* Reduced bottom margin */
    color: #475569;
    font-size: 1.1rem;
    line-height: 1.6;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    transition: all 0.6s ease;
}

.cta-section.visible .cta-card p {
    animation: fadeIn 1s ease-out 0.2s;
}

.cta-card .btn {
    opacity: 1;
    padding: 0.75rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    letter-spacing: 0.5px;
    transition: all 0.4s ease;
}

.cta-section.visible .cta-card .btn {
    animation: pulseButton 1s ease-out 0.4s;
}

@keyframes pulseButton {
    0% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(74, 108, 247, 0.4);
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(74, 108, 247, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(74, 108, 247, 0);
    }
}

.cta-card .btn-primary {
    background: linear-gradient(135deg, #4a6cf7, #3a5bd9);
    border: none;
    box-shadow: 0 5px 15px rgba(74, 108, 247, 0.3);
    transition: all 0.4s ease;
}

.cta-card .btn-primary:hover {
    background: linear-gradient(135deg, #3a5bd9, #2a4bc9);
    box-shadow: 0 8px 20px rgba(74, 108, 247, 0.4);
    transform: translateY(-3px);
}

/* Animation Keyframes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes lineGrow {
    from {
        width: 0;
    }
    to {
        width: 100px;
    }
}
