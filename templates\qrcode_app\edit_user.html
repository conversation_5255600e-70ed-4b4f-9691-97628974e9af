{% extends "base.html" %}
{% load static %}

{% block title %}Edit User - Enterprise QR{% endblock %}

{% block extra_css %}
<style>
    .form-container {
        max-width: 800px;
        margin: 0 auto;
    }
    
    .card {
        border: none;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        border-radius: 0.5rem;
    }
    
    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        padding: 1.25rem 1.5rem;
    }
    
    .card-title {
        margin-bottom: 0;
        font-weight: 600;
    }
    
    .card-body {
        padding: 1.5rem;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        font-weight: 500;
        margin-bottom: 0.5rem;
    }
    
    .form-text {
        color: #6c757d;
        font-size: 0.875rem;
    }
    
    .btn-toolbar {
        display: flex;
        justify-content: space-between;
        margin-top: 2rem;
    }
    
    .user-avatar {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        background-color: #e9ecef;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2.5rem;
        color: #6c757d;
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="h3">
                <i class="fas fa-user-edit me-2"></i>Edit User
            </h1>
            <p class="text-muted">Update user information and permissions</p>
        </div>
        <div class="col-md-4 text-md-end">
            <a href="{% url 'user_management' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to User Management
            </a>
        </div>
    </div>
    
    <div class="form-container">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">User Information</h5>
            </div>
            <div class="card-body">
                <form method="post" novalidate>
                    {% csrf_token %}
                    
                    <div class="text-center mb-4">
                        <div class="user-avatar mx-auto">
                            <i class="fas fa-user"></i>
                        </div>
                        <h4>{{ user_to_edit.get_full_name|default:user_to_edit.username }}</h4>
                        <p class="text-muted">@{{ user_to_edit.username }}</p>
                        {% if user_profile %}
                        <span class="badge {% if user_profile.role == 'superadmin' %}bg-danger{% elif user_profile.role == 'admin' %}bg-warning{% elif user_profile.role == 'premium' %}bg-success{% else %}bg-secondary{% endif %}">
                            {{ user_profile.get_role_display }}
                        </span>
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="username" class="form-label">Username</label>
                                <input type="text" class="form-control" id="username" name="username" value="{{ user_to_edit.username }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" name="email" value="{{ user_to_edit.email }}" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="first_name" class="form-label">First Name</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" value="{{ user_to_edit.first_name }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="last_name" class="form-label">Last Name</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" value="{{ user_to_edit.last_name }}">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="new_password" class="form-label">New Password</label>
                                <input type="password" class="form-control" id="new_password" name="new_password">
                                <div class="form-text">Leave blank to keep current password.</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="role" class="form-label">Role</label>
                                <select class="form-select" id="role" name="role">
                                    <option value="guest" {% if user_profile.role == 'guest' %}selected{% endif %}>Guest</option>
                                    <option value="user" {% if user_profile.role == 'user' %}selected{% endif %}>User</option>
                                    <option value="premium" {% if user_profile.role == 'premium' %}selected{% endif %}>Premium User</option>
                                    {% if is_superadmin %}
                                    <option value="admin" {% if user_profile.role == 'admin' %}selected{% endif %}>Administrator</option>
                                    <option value="superadmin" {% if user_profile.role == 'superadmin' %}selected{% endif %}>Super Administrator</option>
                                    {% endif %}
                                </select>
                                <div class="form-text">Determines the user's permissions and access level.</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="company" class="form-label">Company</label>
                                <input type="text" class="form-control" id="company" name="company" value="{{ user_profile.company|default:'' }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="phone" class="form-label">Phone</label>
                                <input type="text" class="form-control" id="phone" name="phone" value="{{ user_profile.phone|default:'' }}">
                            </div>
                        </div>
                    </div>
                    
                    <div class="btn-toolbar">
                        <a href="{% url 'user_management' %}" class="btn btn-outline-secondary">Cancel</a>
                        <div>
                            <a href="{% url 'delete_user' user_to_edit.id %}" class="btn btn-outline-danger me-2">
                                <i class="fas fa-trash me-2"></i>Delete User
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Save Changes
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
