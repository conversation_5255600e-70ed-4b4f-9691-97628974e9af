/**
 * Ad Creation Production JS
 * Enterprise-grade ad creation functionality with optimized performance
 */

// Use IIFE to avoid polluting global namespace
(function() {
    // Track initialization to prevent duplicate execution
    if (window.adCreationInitialized) return;
    window.adCreationInitialized = true;

    // Configuration
    const config = {
        aiEngineEndpoint: '/generate_ad',
        aiEngineTimeout: 30000, // 30 seconds
        debounceDelay: 300, // 300ms for debouncing input
        animationDuration: 300, // 300ms for animations
        maxSuggestions: 5, // Maximum number of AI suggestions to display
        minTitleLength: 3, // Minimum title length to enable AI suggestions
    };

    // State management
    const state = {
        smartEngineEnabled: false,
        isGeneratingSuggestions: false,
        selectedSuggestion: null,
        adTitle: '',
        adContent: '',
        adType: 'text',
        adLanguage: 'english',
        adTargetAudience: 'general',
        adStatus: 'draft',
        validationErrors: {},
    };

    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Ad Creation Production: Initializing');

        // Initialize ad creation components
        initSmartEngineToggle();
        initAdTitleInput();
        initAdContentInput();
        initAdTypeSelect();
        initAdLanguageSelect();
        initAdTargetAudienceSelect();
        initGenerateSuggestionsButton();
        initPreviewUpdates();
        initFormValidation();
        initSubmitButton();

        // Log successful initialization
        console.log('Ad Creation Production: Initialized successfully');
    });

    /**
     * Initialize Smart Engine toggle
     */
    function initSmartEngineToggle() {
        const smartEngineToggle = document.querySelector('#smart-engine-toggle');
        const aiSuggestionsContainer = document.querySelector('.ai-suggestions');
        const generateSuggestionsBtn = document.querySelector('#generate-suggestions-btn');
        const aiEngineBtn = document.querySelector('#ai-engine-btn');

        if (!smartEngineToggle) return;

        smartEngineToggle.addEventListener('change', function() {
            state.smartEngineEnabled = this.checked;

            if (state.smartEngineEnabled) {
                // Show generate suggestions button if title is long enough
                if (state.adTitle.length >= config.minTitleLength) {
                    if (generateSuggestionsBtn) {
                        generateSuggestionsBtn.style.display = 'block';
                    }
                }

                // Hide AI Engine button
                if (aiEngineBtn) {
                    aiEngineBtn.style.display = 'none';
                }
            } else {
                // Hide generate suggestions button
                if (generateSuggestionsBtn) {
                    generateSuggestionsBtn.style.display = 'none';
                }

                // Show AI Engine button
                if (aiEngineBtn) {
                    aiEngineBtn.style.display = 'block';
                }

                // Hide AI suggestions
                if (aiSuggestionsContainer) {
                    aiSuggestionsContainer.classList.remove('active');
                }
            }
        });
    }

    /**
     * Initialize Ad Title input
     */
    function initAdTitleInput() {
        const adTitleInput = document.querySelector('#ad-title');
        const generateSuggestionsBtn = document.querySelector('#generate-suggestions-btn');
        const adPreviewTitle = document.querySelector('.ad-preview-title');

        if (!adTitleInput) return;

        // Debounce function to limit how often the input event fires
        const debounce = (func, delay) => {
            let debounceTimer;
            return function() {
                const context = this;
                const args = arguments;
                clearTimeout(debounceTimer);
                debounceTimer = setTimeout(() => func.apply(context, args), delay);
            };
        };

        // Update state and UI when title changes
        adTitleInput.addEventListener('input', debounce(function() {
            state.adTitle = this.value.trim();

            // Update preview
            if (adPreviewTitle) {
                adPreviewTitle.textContent = state.adTitle || 'Ad Title Preview';
            }

            // Show/hide generate suggestions button based on title length
            if (generateSuggestionsBtn && state.smartEngineEnabled) {
                if (state.adTitle.length >= config.minTitleLength) {
                    generateSuggestionsBtn.style.display = 'block';
                } else {
                    generateSuggestionsBtn.style.display = 'none';
                }
            }

            // Validate title
            validateField('adTitle', state.adTitle);
        }, config.debounceDelay));
    }

    /**
     * Initialize Ad Content input
     */
    function initAdContentInput() {
        const adContentInput = document.querySelector('#ad-content');
        const adPreviewContent = document.querySelector('.ad-preview-content');

        if (!adContentInput) return;

        // Debounce function to limit how often the input event fires
        const debounce = (func, delay) => {
            let debounceTimer;
            return function() {
                const context = this;
                const args = arguments;
                clearTimeout(debounceTimer);
                debounceTimer = setTimeout(() => func.apply(context, args), delay);
            };
        };

        // Update state and UI when content changes
        adContentInput.addEventListener('input', debounce(function() {
            state.adContent = this.value.trim();

            // Update preview
            if (adPreviewContent) {
                if (state.adContent) {
                    adPreviewContent.innerHTML = state.adContent.replace(/\n/g, '<br>');
                } else {
                    adPreviewContent.innerHTML = '<span class="ad-preview-placeholder">Your ad content will appear here...</span>';
                }
            }

            // Validate content
            validateField('adContent', state.adContent);
        }, config.debounceDelay));
    }

    /**
     * Initialize Ad Type select
     */
    function initAdTypeSelect() {
        const adTypeSelect = document.querySelector('#ad-type');

        if (!adTypeSelect) return;

        adTypeSelect.addEventListener('change', function() {
            state.adType = this.value;

            // Update UI based on ad type
            updateUIForAdType(state.adType);

            // Validate ad type
            validateField('adType', state.adType);
        });
    }

    /**
     * Initialize Ad Language select
     */
    function initAdLanguageSelect() {
        const adLanguageSelect = document.querySelector('#ad-language');

        if (!adLanguageSelect) return;

        adLanguageSelect.addEventListener('change', function() {
            state.adLanguage = this.value;

            // Validate language
            validateField('adLanguage', state.adLanguage);
        });
    }

    /**
     * Initialize Ad Target Audience select
     */
    function initAdTargetAudienceSelect() {
        const adTargetAudienceSelect = document.querySelector('#ad-target-audience');

        if (!adTargetAudienceSelect) return;

        adTargetAudienceSelect.addEventListener('change', function() {
            state.adTargetAudience = this.value;

            // Validate target audience
            validateField('adTargetAudience', state.adTargetAudience);
        });
    }

    /**
     * Initialize Generate Suggestions button
     */
    function initGenerateSuggestionsButton() {
        const generateSuggestionsBtn = document.querySelector('#generate-suggestions-btn');

        if (!generateSuggestionsBtn) return;

        generateSuggestionsBtn.addEventListener('click', function() {
            if (state.isGeneratingSuggestions) return;

            // Validate title before generating suggestions
            if (state.adTitle.length < config.minTitleLength) {
                showError('Please enter a title with at least ' + config.minTitleLength + ' characters.');
                return;
            }

            // Generate suggestions
            generateSuggestions();
        });
    }

    /**
     * Initialize Preview Updates
     */
    function initPreviewUpdates() {
        // Initial preview update
        updateAdPreview();
    }

    /**
     * Initialize Form Validation
     */
    function initFormValidation() {
        const adForm = document.querySelector('#ad-creation-form');

        if (!adForm) return;

        adForm.addEventListener('submit', function(e) {
            // Prevent default form submission
            e.preventDefault();

            // Validate all fields
            const isValid = validateAllFields();

            if (isValid) {
                // Submit form
                submitAdForm();
            } else {
                // Show validation errors
                showValidationErrors();
            }
        });
    }

    /**
     * Initialize Submit Button
     */
    function initSubmitButton() {
        const submitBtn = document.querySelector('#submit-ad-btn');
        const reviewBtn = document.querySelector('#review-ad-btn');

        if (reviewBtn) {
            reviewBtn.addEventListener('click', function() {
                // Validate all fields
                const isValid = validateAllFields();

                if (isValid) {
                    // Show review modal
                    showReviewModal();
                } else {
                    // Show validation errors
                    showValidationErrors();
                }
            });
        }

        if (submitBtn) {
            submitBtn.addEventListener('click', function() {
                // Submit form
                submitAdForm();
            });
        }
    }

    /**
     * Generate AI Suggestions
     */
    function generateSuggestions() {
        const aiSuggestionsContainer = document.querySelector('.ai-suggestions');
        const generateSuggestionsBtn = document.querySelector('#generate-suggestions-btn');

        if (!aiSuggestionsContainer) return;

        // Update UI to show loading state
        state.isGeneratingSuggestions = true;

        if (generateSuggestionsBtn) {
            generateSuggestionsBtn.innerHTML = '<span class="spinner"></span> Generating...';
            generateSuggestionsBtn.disabled = true;
        }

        // Show suggestions container with loading state
        aiSuggestionsContainer.classList.add('active');
        aiSuggestionsContainer.innerHTML = '<div class="suggestion-loading">Generating AI suggestions...</div>';

        // Prepare request data
        const requestData = {
            title: state.adTitle,
            type: state.adType,
            language: state.adLanguage,
            target_audience: state.adTargetAudience
        };

        // Make API request to generate suggestions
        fetch(config.aiEngineEndpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCSRFToken()
            },
            body: JSON.stringify(requestData)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Failed to generate suggestions');
            }
            return response.json();
        })
        .then(data => {
            // Process suggestions
            processSuggestions(data.suggestions || []);
        })
        .catch(error => {
            console.error('Error generating suggestions:', error);
            aiSuggestionsContainer.innerHTML = '<div class="suggestion-error">Failed to generate suggestions. Please try again.</div>';
        })
        .finally(() => {
            // Reset UI state
            state.isGeneratingSuggestions = false;

            if (generateSuggestionsBtn) {
                generateSuggestionsBtn.innerHTML = 'Generate Suggestions';
                generateSuggestionsBtn.disabled = false;
            }
        });
    }

    /**
     * Process AI Suggestions
     */
    function processSuggestions(suggestions) {
        const aiSuggestionsContainer = document.querySelector('.ai-suggestions');

        if (!aiSuggestionsContainer) return;

        // Limit number of suggestions
        const limitedSuggestions = suggestions.slice(0, config.maxSuggestions);

        if (limitedSuggestions.length === 0) {
            aiSuggestionsContainer.innerHTML = '<div class="suggestion-empty">No suggestions available. Try a different title or ad type.</div>';
            return;
        }

        // Create HTML for suggestions
        let suggestionsHTML = '<div class="suggestions-title">AI Generated Suggestions</div>';

        limitedSuggestions.forEach((suggestion, index) => {
            suggestionsHTML += `
                <div class="suggestion-card" data-index="${index}">
                    <div class="suggestion-title">${suggestion.title || state.adTitle}</div>
                    <div class="suggestion-content">${suggestion.content}</div>
                </div>
            `;
        });

        // Update container with suggestions
        aiSuggestionsContainer.innerHTML = suggestionsHTML;

        // Add click event listeners to suggestion cards
        const suggestionCards = aiSuggestionsContainer.querySelectorAll('.suggestion-card');

        suggestionCards.forEach(card => {
            card.addEventListener('click', function() {
                const index = parseInt(this.dataset.index);
                selectSuggestion(limitedSuggestions[index], this);
            });
        });
    }

    /**
     * Select a suggestion
     */
    function selectSuggestion(suggestion, card) {
        const adTitleInput = document.querySelector('#ad-title');
        const adContentInput = document.querySelector('#ad-content');
        const suggestionCards = document.querySelectorAll('.suggestion-card');

        // Update selected suggestion state
        state.selectedSuggestion = suggestion;

        // Update UI to show selected suggestion
        suggestionCards.forEach(c => c.classList.remove('selected'));
        card.classList.add('selected');

        // Update form inputs with suggestion
        if (adTitleInput && suggestion.title) {
            adTitleInput.value = suggestion.title;
            state.adTitle = suggestion.title;
        }

        if (adContentInput && suggestion.content) {
            adContentInput.value = suggestion.content;
            state.adContent = suggestion.content;
        }

        // Update preview
        updateAdPreview();
    }

    /**
     * Update Ad Preview
     */
    function updateAdPreview() {
        const adPreviewTitle = document.querySelector('.ad-preview-title');
        const adPreviewContent = document.querySelector('.ad-preview-content');

        if (adPreviewTitle) {
            adPreviewTitle.textContent = state.adTitle || 'Ad Title Preview';
        }

        if (adPreviewContent) {
            if (state.adContent) {
                adPreviewContent.innerHTML = state.adContent.replace(/\n/g, '<br>');
            } else {
                adPreviewContent.innerHTML = '<span class="ad-preview-placeholder">Your ad content will appear here...</span>';
            }
        }
    }

    /**
     * Update UI for Ad Type
     */
    function updateUIForAdType(adType) {
        const imageUploadContainer = document.querySelector('.image-upload-container');
        const videoUploadContainer = document.querySelector('.video-upload-container');

        if (imageUploadContainer) {
            imageUploadContainer.style.display = (adType === 'image' || adType === 'banner') ? 'block' : 'none';
        }

        if (videoUploadContainer) {
            videoUploadContainer.style.display = adType === 'video' ? 'block' : 'none';
        }
    }

    /**
     * Validate Field
     */
    function validateField(fieldName, value) {
        switch (fieldName) {
            case 'adTitle':
                if (!value || value.length < 3) {
                    state.validationErrors.adTitle = 'Title must be at least 3 characters long';
                    return false;
                }
                break;

            case 'adContent':
                if (!value || value.length < 10) {
                    state.validationErrors.adContent = 'Content must be at least 10 characters long';
                    return false;
                }
                break;

            case 'adType':
                if (!value) {
                    state.validationErrors.adType = 'Please select an ad type';
                    return false;
                }
                break;

            case 'adLanguage':
                if (!value) {
                    state.validationErrors.adLanguage = 'Please select a language';
                    return false;
                }
                break;

            case 'adTargetAudience':
                if (!value) {
                    state.validationErrors.adTargetAudience = 'Please select a target audience';
                    return false;
                }
                break;
        }

        // Clear validation error if field is valid
        delete state.validationErrors[fieldName];
        return true;
    }

    /**
     * Validate All Fields
     */
    function validateAllFields() {
        // Clear all validation errors
        state.validationErrors = {};

        // Validate each field
        validateField('adTitle', state.adTitle);
        validateField('adContent', state.adContent);
        validateField('adType', state.adType);
        validateField('adLanguage', state.adLanguage);
        validateField('adTargetAudience', state.adTargetAudience);

        // Return true if no validation errors
        return Object.keys(state.validationErrors).length === 0;
    }

    /**
     * Show Validation Errors
     */
    function showValidationErrors() {
        // Clear existing error messages
        const errorMessages = document.querySelectorAll('.error-message');
        errorMessages.forEach(message => message.remove());

        // Show error messages for each field
        for (const [fieldName, errorMessage] of Object.entries(state.validationErrors)) {
            const fieldId = fieldName.replace(/([A-Z])/g, '-$1').toLowerCase();
            const field = document.querySelector(`#${fieldId}`);

            if (field) {
                const errorElement = document.createElement('div');
                errorElement.className = 'error-message';
                errorElement.textContent = errorMessage;

                field.parentNode.appendChild(errorElement);
                field.classList.add('error');
            }
        }
    }

    /**
     * Show Error
     */
    function showError(message) {
        const errorContainer = document.querySelector('.error-container');

        if (!errorContainer) {
            // Create error container if it doesn't exist
            const container = document.createElement('div');
            container.className = 'error-container';
            container.textContent = message;

            const adForm = document.querySelector('#ad-creation-form');
            if (adForm) {
                adForm.prepend(container);
            }
        } else {
            errorContainer.textContent = message;
            errorContainer.style.display = 'block';
        }

        // Hide error after 5 seconds
        setTimeout(() => {
            const errorContainer = document.querySelector('.error-container');
            if (errorContainer) {
                errorContainer.style.display = 'none';
            }
        }, 5000);
    }

    /**
     * Show Review Modal
     */
    function showReviewModal() {
        // Create modal if it doesn't exist
        let modal = document.querySelector('#ad-review-modal');

        if (!modal) {
            modal = document.createElement('div');
            modal.id = 'ad-review-modal';
            modal.className = 'modal';

            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>Review Your Ad</h2>
                        <span class="modal-close">&times;</span>
                    </div>
                    <div class="modal-body">
                        <div class="review-section">
                            <h3>Ad Title</h3>
                            <p class="review-title">${state.adTitle}</p>
                        </div>
                        <div class="review-section">
                            <h3>Ad Content</h3>
                            <p class="review-content">${state.adContent.replace(/\n/g, '<br>')}</p>
                        </div>
                        <div class="review-section">
                            <h3>Ad Details</h3>
                            <ul class="review-details">
                                <li><strong>Type:</strong> ${state.adType}</li>
                                <li><strong>Language:</strong> ${state.adLanguage}</li>
                                <li><strong>Target Audience:</strong> ${state.adTargetAudience}</li>
                            </ul>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary modal-close-btn">Edit</button>
                        <button class="btn btn-primary modal-submit-btn">Submit Ad</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Add event listeners to modal buttons
            const closeBtn = modal.querySelector('.modal-close');
            const closeBtnFooter = modal.querySelector('.modal-close-btn');
            const submitBtn = modal.querySelector('.modal-submit-btn');

            closeBtn.addEventListener('click', () => {
                modal.style.display = 'none';
            });

            closeBtnFooter.addEventListener('click', () => {
                modal.style.display = 'none';
            });

            submitBtn.addEventListener('click', () => {
                modal.style.display = 'none';
                submitAdForm();
            });

            // Close modal when clicking outside
            window.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.style.display = 'none';
                }
            });
        } else {
            // Update modal content
            modal.querySelector('.review-title').textContent = state.adTitle;
            modal.querySelector('.review-content').innerHTML = state.adContent.replace(/\n/g, '<br>');

            const reviewDetails = modal.querySelector('.review-details');
            reviewDetails.innerHTML = `
                <li><strong>Type:</strong> ${state.adType}</li>
                <li><strong>Language:</strong> ${state.adLanguage}</li>
                <li><strong>Target Audience:</strong> ${state.adTargetAudience}</li>
            `;
        }

        // Show modal
        modal.style.display = 'block';
    }

    /**
     * Submit Ad Form
     */
    function submitAdForm() {
        const adForm = document.querySelector('#ad-creation-form');
        const submitBtn = document.querySelector('#submit-ad-btn');

        if (!adForm) return;

        // Update UI to show loading state
        if (submitBtn) {
            submitBtn.innerHTML = '<span class="spinner"></span> Submitting...';
            submitBtn.disabled = true;
        }

        // Prepare form data
        const formData = new FormData(adForm);

        // Add additional data
        formData.append('status', 'pending');

        // Submit form
        fetch(adForm.action, {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCSRFToken()
            },
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Failed to submit ad');
            }
            return response.json();
        })
        .then(data => {
            // Handle successful submission
            if (data.success) {
                // Redirect to success page or show success message
                window.location.href = data.redirect_url || '/ads/';
            } else {
                // Show error message
                showError(data.message || 'Failed to submit ad. Please try again.');
            }
        })
        .catch(error => {
            console.error('Error submitting ad:', error);
            showError('Failed to submit ad. Please try again.');
        })
        .finally(() => {
            // Reset UI state
            if (submitBtn) {
                submitBtn.innerHTML = 'Submit Ad';
                submitBtn.disabled = false;
            }
        });
    }

    /**
     * Get CSRF Token
     */
    function getCSRFToken() {
        const cookieValue = document.cookie
            .split('; ')
            .find(row => row.startsWith('csrftoken='))
            ?.split('=')[1];

        return cookieValue || '';
    }
})();