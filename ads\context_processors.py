from django.utils import timezone
from .models import Ad, AdLocation

def active_ads(request):
    """
    Context processor to make active ads available to all templates.

    This allows templates to display ads in different locations without
    having to query the database in each view.
    """
    # Get current time
    now = timezone.now()

    # Get all active ads that are within their start and end dates
    active_ads = Ad.objects.filter(
        status='active',
        start_date__lte=now,
        end_date__gte=now
    )

    # Define location mapping from database names to template locations
    location_mapping = {
        'premium header': 'header',
        'header': 'header',
        'sidebar top': 'sidebar_left',
        'sidebar middle': 'sidebar_right',
        'homepage featured': 'content_top',
        'content inline': 'content_bottom',
        'footer banner': 'footer',
        'qr code results page': 'content_top',
        'profile dashboard': 'sidebar_right',
    }

    # Group ads by location
    ads_by_location = {
        'header': [],
        'sidebar_left': [],
        'sidebar_right': [],
        'content_top': [],
        'content_bottom': [],
        'footer': [],
        'popup': []
    }

    # Add ads to their respective locations based on mapping
    for ad in active_ads:
        if ad.ad_location:
            db_location = ad.ad_location.name.lower()
            template_location = location_mapping.get(db_location)

            if template_location and template_location in ads_by_location:
                ads_by_location[template_location].append(ad)

    # Create a function to get ads for a specific location
    def get_ads_for_location(location, max_ads=1):
        location = location.lower()
        if location in ads_by_location:
            return ads_by_location[location][:max_ads]
        return []

    return {
        'active_ads': active_ads,
        'ads_by_location': ads_by_location,
        'get_ads_for_location': get_ads_for_location,
    }
