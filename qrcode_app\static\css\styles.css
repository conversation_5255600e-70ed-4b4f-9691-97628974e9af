/* Enterprise QR Code Generator - Main Styles */

/* Global Styles */
:root {
    --primary-color: #4a6cf7;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --body-bg: #f5f7fa;
    --card-bg: #ffffff;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--body-bg);
    color: #333;
    line-height: 1.6;
}

/* Header & Navigation - Corporate Style */
.corporate-navbar {
    background: linear-gradient(135deg, #1a2a6c, #2a4065);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    padding: 0.75rem 0;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.navbar-brand {
    display: flex;
    align-items: center;
    padding: 0.5rem 0;
    transition: all 0.3s ease;
}

.navbar-brand:hover {
    transform: translateY(-2px);
}

.brand-icon {
    font-size: 1.75rem;
    margin-right: 0.75rem;
    color: #fff;
    background: var(--primary-color);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(74, 108, 247, 0.3);
}

.brand-text {
    font-weight: 700;
    font-size: 1.5rem;
    color: #fff;
    letter-spacing: 0.5px;
}

.nav-link {
    font-weight: 500;
    color: rgba(255, 255, 255, 0.85) !important;
    padding: 0.75rem 1rem;
    margin: 0 0.25rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.nav-link:hover {
    color: #fff !important;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.nav-icon {
    margin-right: 0.5rem;
    font-size: 1.1rem;
}

.dropdown-toggle::after {
    margin-left: 0.5rem;
    vertical-align: middle;
}

.corporate-dropdown {
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    padding: 1rem 0;
    margin-top: 0.5rem;
    min-width: 240px;
    animation: dropdownFadeIn 0.3s ease-out;
}

@keyframes dropdownFadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dropdown-header {
    font-weight: 600;
    color: var(--primary-color);
    padding: 0.5rem 1.5rem;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.dropdown-item {
    padding: 0.75rem 1.5rem;
    display: flex;
    align-items: center;
    color: #495057;
    transition: all 0.2s ease;
}

.dropdown-item i {
    margin-right: 0.75rem;
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
    color: var(--primary-color);
}

.dropdown-item:hover {
    background-color: rgba(74, 108, 247, 0.05);
    color: var(--primary-color);
    transform: translateX(5px);
}

.dropdown-divider {
    margin: 0.5rem 0;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

/* User Menu */
.user-menu-link {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50px;
    margin-left: 1rem;
}

.user-avatar {
    width: 32px;
    height: 32px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
}

.login-btn {
    background-color: var(--primary-color);
    color: white !important;
    border-radius: 50px;
    padding: 0.5rem 1.5rem !important;
    margin-left: 1rem;
    box-shadow: 0 4px 10px rgba(74, 108, 247, 0.3);
    transition: all 0.3s ease;
}

.login-btn:hover {
    background-color: #3a5bd9 !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(74, 108, 247, 0.4);
}

/* Cards */
.card {
    border-radius: 0.75rem;
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s, box-shadow 0.3s;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.card-title {
    font-weight: 600;
    margin-bottom: 1rem;
}

.card-title i {
    margin-right: 0.5rem;
}

/* Buttons */
.btn {
    border-radius: 0.5rem;
    font-weight: 500;
    padding: 0.5rem 1.5rem;
    transition: all 0.3s;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #3a5bd9;
    border-color: #3a5bd9;
}

/* Forms */
.form-control, .form-select {
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    border: 1px solid #e0e0e0;
}

.form-control:focus, .form-select:focus {
    box-shadow: 0 0 0 0.25rem rgba(74, 108, 247, 0.25);
    border-color: var(--primary-color);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

/* QR Code Display */
.qr-code-container {
    text-align: center;
    padding: 2rem;
    background-color: var(--card-bg);
    border-radius: 0.75rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.qr-code-image {
    max-width: 100%;
    height: auto;
}

/* Footer */
.footer {
    margin-top: 3rem;
    padding: 2rem 0;
    background-color: var(--dark-color);
    color: var(--light-color);
}

.footer a {
    color: var(--light-color);
    text-decoration: none;
}

.footer a:hover {
    text-decoration: underline;
}

/* Admin Panel */
.admin-panel {
    background-color: #f8f9fa;
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.admin-panel h2 {
    margin-bottom: 1.5rem;
    color: var(--primary-color);
}

/* Badges */
.badge {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-weight: 500;
}

.badge i {
    margin-right: 0.5rem;
}

/* Alerts */
.alert {
    border-radius: 0.5rem;
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* Tables */
.table {
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.table thead {
    background-color: var(--primary-color);
    color: white;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Pagination */
.pagination .page-link {
    border-radius: 0.5rem;
    margin: 0 0.25rem;
    color: var(--primary-color);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Corporate Animations */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Hover animations */
.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.hover-glow {
    transition: box-shadow 0.3s ease;
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(74, 108, 247, 0.4);
}

/* Button animations */
.btn-pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(74, 108, 247, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(74, 108, 247, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(74, 108, 247, 0);
    }
}

/* Page transitions */
.page-transition {
    animation: pageTransition 0.5s ease-out;
}

@keyframes pageTransition {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading animation */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(74, 108, 247, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Corporate reveal animation */
.reveal-right {
    animation: revealRight 0.5s ease-out forwards;
    opacity: 0;
}

@keyframes revealRight {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.reveal-left {
    animation: revealLeft 0.5s ease-out forwards;
    opacity: 0;
}

@keyframes revealLeft {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Staggered animations for lists */
.stagger-item {
    opacity: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.stagger-item:nth-child(1) { animation-delay: 0.1s; }
.stagger-item:nth-child(2) { animation-delay: 0.2s; }
.stagger-item:nth-child(3) { animation-delay: 0.3s; }
.stagger-item:nth-child(4) { animation-delay: 0.4s; }
.stagger-item:nth-child(5) { animation-delay: 0.5s; }
.stagger-item:nth-child(6) { animation-delay: 0.6s; }

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
