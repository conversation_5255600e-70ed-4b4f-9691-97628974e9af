/**
 * AI Suggestions Consolidated
 * This is a consolidated script that handles all AI suggestion functionality
 * It replaces multiple separate scripts to avoid conflicts and improve maintainability
 *
 * Version: 1.1.0 (Production Ready)
 * Last Updated: 2023-07-15
 *
 * The key principles:
 * 1. Complete abstraction of AI service status from users
 * 2. Immediate feedback with fallback suggestions
 * 3. Attempt to fetch real AI-generated suggestions in the background
 * 4. Consistent UI regardless of AI service status
 * 5. Robust error handling with graceful fallbacks
 */

// Execute immediately to ensure it runs before DOMContentLoaded
(function() {
    console.log('AI Suggestions Consolidated script loaded - immediate execution');

    // Global variables
    let isGenerating = false;
    let suggestions = [];

    // Main function to generate suggestions - defined globally first
    function generateSuggestions() {
        console.log('🚀 Generate Suggestions function called');
        console.log('🔍 Current isGenerating state:', isGenerating);

        // Check if already generating
        if (isGenerating) {
            console.log('⚠️ Already generating suggestions, ignoring request');
            return;
        }

        // Get required elements
        const adTitleInput = document.getElementById('adTitle');
        const businessTypeInput = document.getElementById('businessType');
        const aiLanguageSelect = document.getElementById('aiLanguage');
        const aiCreativityInput = document.getElementById('aiCreativity');
        const aiLengthSelect = document.getElementById('aiLength');
        const aiStyleSelect = document.getElementById('aiStyle');
        const targetAudienceInput = document.getElementById('targetAudience');

        console.log('📝 Checking form inputs...');
        console.log('📝 adTitleInput:', adTitleInput);
        console.log('📝 businessTypeInput:', businessTypeInput);

        // Check if title is provided
        if (!adTitleInput || !adTitleInput.value.trim()) {
            console.log('❌ No ad title provided');
            showError('Please enter an ad title before generating suggestions.');
            if (adTitleInput) adTitleInput.focus();
            return;
        }

        // Check if business type is provided
        if (!businessTypeInput || !businessTypeInput.value.trim()) {
            console.log('❌ No business type provided');
            showError('Please enter your business type before generating suggestions.');
            if (businessTypeInput) businessTypeInput.focus();
            return;
        }

        console.log('✅ Form validation passed');

        // Get the button
        const generateBtn = document.getElementById('generateSuggestions');

        // Show loading state
        isGenerating = true;
        if (generateBtn) {
            generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Generating...';
            generateBtn.disabled = true;
        }

        // Get parameters
        const title = adTitleInput.value.trim();
        const businessType = businessTypeInput.value.trim();
        const language = aiLanguageSelect ? aiLanguageSelect.value : 'english';
        const targetAudience = targetAudienceInput ? targetAudienceInput.value : '';

        // Get creativity value from the hidden input or radio buttons
        let creativity = 0.7; // Default value
        if (aiCreativityInput) {
            creativity = aiCreativityInput.value;
        } else {
            const conservativeRadio = document.getElementById('aiCreativityConservative');
            const balancedRadio = document.getElementById('aiCreativityBalanced');
            const creativeRadio = document.getElementById('aiCreativityCreative');

            if (conservativeRadio && conservativeRadio.checked) {
                creativity = conservativeRadio.value;
            } else if (balancedRadio && balancedRadio.checked) {
                creativity = balancedRadio.value;
            } else if (creativeRadio && creativeRadio.checked) {
                creativity = creativeRadio.value;
            }
        }

        const length = aiLengthSelect ? aiLengthSelect.value : 'medium';
        const style = aiStyleSelect ? aiStyleSelect.value : 'standard';

        console.log('🎯 Generating suggestions with parameters:', {
            title, businessType, language, creativity, length, style, targetAudience
        });

        // Start AI suggestion generation process
        console.log('🤖 Starting AI suggestion generation process...');

        // Show loading state
        showLoadingState();

        // Generate AI suggestions with full fallback chain
        generateAISuggestions(title, businessType, language, targetAudience, creativity, length, style, generateBtn, usedAiInput);
    }

    // Function to show loading state while AI generates suggestions
    function showLoadingState() {
        console.log('📺 Showing loading state...');

        // Create loading suggestions
        const loadingSuggestions = [
            {
                title: "🤖 Generating...",
                content: "AI is creating personalized suggestions for your business...",
                model: "loading",
                ai_generated: false,
                loading: true,
                language: "english"
            },
            {
                title: "🧠 Thinking...",
                content: "Analyzing your business type and target audience...",
                model: "loading",
                ai_generated: false,
                loading: true,
                language: "english"
            },
            {
                title: "✨ Almost ready...",
                content: "Crafting the perfect ad content just for you...",
                model: "loading",
                ai_generated: false,
                loading: true,
                language: "english"
            }
        ];

        displaySuggestions(loadingSuggestions);
    }

    // Main AI suggestion generation function with full fallback chain
    async function generateAISuggestions(title, businessType, language, targetAudience, creativity, length, style, generateBtn, usedAiInput) {
        console.log('🤖 Starting AI suggestion generation with fallback chain...');

        // Create cache key for this request
        const cacheKey = createCacheKey(title, businessType, language, targetAudience, creativity, length, style);

        // Step 1: Check cache first (6-hour TTL)
        console.log('💾 Checking cache for suggestions...');
        const cachedSuggestions = getCachedSuggestions(cacheKey);
        if (cachedSuggestions) {
            console.log('💾 Found cached suggestions, using them');
            displaySuggestions(cachedSuggestions);
            finalizeSuggestionGeneration(generateBtn, usedAiInput, true, 'Suggestions loaded from cache (Lightning fast!)');
            return;
        }

        // Step 2: Try AI providers in priority order
        const aiProviders = [
            { name: 'Groq (Lightning Engine)', endpoint: '/ads/test/generate-suggestions-no-auth/' },
            { name: 'Mistral', endpoint: '/ads/api/generate-suggestions/' },
            { name: 'GPT-4', endpoint: '/ads/test/smart-ai/' }
        ];

        for (const provider of aiProviders) {
            console.log(`🤖 Trying ${provider.name}...`);

            const suggestions = await tryAIProvider(provider, title, businessType, language, targetAudience, creativity, length, style);

            if (suggestions && suggestions.length > 0) {
                console.log(`✅ Success with ${provider.name}! Generated ${suggestions.length} suggestions`);

                // Cache the successful suggestions
                cacheSuggestions(cacheKey, suggestions);

                // Display the suggestions
                displaySuggestions(suggestions);

                // Finalize
                finalizeSuggestionGeneration(generateBtn, usedAiInput, true, `AI suggestions generated using ${provider.name}!`);
                return;
            }

            console.log(`❌ ${provider.name} failed, trying next provider...`);
        }

        // Step 3: If all AI providers fail, show error
        console.error('❌ All AI providers failed');
        showAIError();
        finalizeSuggestionGeneration(generateBtn, usedAiInput, false, 'AI services temporarily unavailable. Please try again later.');
    }

    // Function to create cache key
    function createCacheKey(title, businessType, language, targetAudience, creativity, length, style) {
        const keyData = {
            title: title.toLowerCase().trim(),
            businessType: businessType.toLowerCase().trim(),
            language: language.toLowerCase(),
            targetAudience: targetAudience.toLowerCase().trim(),
            creativity: creativity,
            length: length,
            style: style
        };

        // Create a simple hash of the key data
        const keyString = JSON.stringify(keyData);
        let hash = 0;
        for (let i = 0; i < keyString.length; i++) {
            const char = keyString.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }

        return `ai_suggestions_${Math.abs(hash)}`;
    }

    // Function to get cached suggestions
    function getCachedSuggestions(cacheKey) {
        try {
            const cached = localStorage.getItem(cacheKey);
            if (!cached) return null;

            const data = JSON.parse(cached);
            const now = Date.now();
            const sixHours = 6 * 60 * 60 * 1000; // 6 hours in milliseconds

            // Check if cache is still valid (6 hours TTL)
            if (now - data.timestamp > sixHours) {
                localStorage.removeItem(cacheKey);
                return null;
            }

            return data.suggestions;
        } catch (error) {
            console.warn('Error reading cache:', error);
            return null;
        }
    }

    // Function to cache suggestions
    function cacheSuggestions(cacheKey, suggestions) {
        try {
            const data = {
                suggestions: suggestions,
                timestamp: Date.now()
            };
            localStorage.setItem(cacheKey, JSON.stringify(data));
            console.log('💾 Cached suggestions for future use');
        } catch (error) {
            console.warn('Error caching suggestions:', error);
        }
    }

    // Function to try a specific AI provider
    async function tryAIProvider(provider, title, businessType, language, targetAudience, creativity, length, style) {
        try {
            console.log(`🤖 Attempting ${provider.name}...`);

            // Get the CSRF token
            const csrfToken = getCSRFToken();
            if (!csrfToken) {
                console.warn(`No CSRF token found for ${provider.name}`);
                return null;
            }

            // Prepare the request data
            const requestData = {
                language: language,
                business_type: businessType,
                target_audience: targetAudience,
                tone: style,
                title: title,
                creativity: creativity,
                length: length,
                style: style,
                num_suggestions: 3
            };

            console.log(`🤖 ${provider.name} request data:`, requestData);

            // Make the API call with timeout
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

            const response = await fetch(provider.endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                body: JSON.stringify(requestData),
                credentials: 'same-origin',
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                console.warn(`🤖 ${provider.name} returned ${response.status}: ${response.statusText}`);
                return null;
            }

            const data = await response.json();
            console.log(`🤖 ${provider.name} response:`, data);

            // Check if we have valid suggestions
            if (data.success && data.suggestions && Array.isArray(data.suggestions) && data.suggestions.length > 0) {
                // Add provider info to suggestions
                const enhancedSuggestions = data.suggestions.map(suggestion => ({
                    ...suggestion,
                    provider: provider.name,
                    ai_generated: true,
                    cached: false
                }));

                return enhancedSuggestions;
            }

            console.warn(`🤖 ${provider.name} returned invalid suggestions:`, data);
            return null;

        } catch (error) {
            if (error.name === 'AbortError') {
                console.warn(`🤖 ${provider.name} timed out`);
            } else {
                console.warn(`🤖 ${provider.name} error:`, error.message);
            }
            return null;
        }
    }

    // Function to show AI error state
    function showAIError() {
        console.log('❌ Showing AI error state...');

        const errorSuggestions = [
            {
                title: "⚠️ AI Temporarily Unavailable",
                content: "Our AI services are currently experiencing issues. Please try again in a few minutes.",
                model: "error",
                ai_generated: false,
                error: true,
                language: "english"
            },
            {
                title: "🔄 Try Again Later",
                content: "We're working to restore AI functionality. Your request is important to us.",
                model: "error",
                ai_generated: false,
                error: true,
                language: "english"
            },
            {
                title: "💡 Manual Creation",
                content: "In the meantime, you can create your ad content manually using the form fields.",
                model: "error",
                ai_generated: false,
                error: true,
                language: "english"
            }
        ];

        displaySuggestions(errorSuggestions);
    }

    // Helper function to finalize suggestion generation
    function finalizeSuggestionGeneration(generateBtn, usedAiInput, isAiGenerated, message = null) {
        // Mark as used AI
        if (usedAiInput) {
            usedAiInput.value = isAiGenerated ? 'true' : 'false';
            console.log('✅ Marked as used AI:', isAiGenerated);
        }

        // Show success message
        const successMessage = message || (isAiGenerated ? 'AI suggestions generated successfully!' : 'Smart suggestions generated successfully!');
        console.log('🎉 Showing success message:', successMessage);
        showSuccess(successMessage);

        // Reset button state
        isGenerating = false;
        if (generateBtn) {
            generateBtn.innerHTML = '<i class="fas fa-magic me-2"></i> Generate Suggestions';
            generateBtn.disabled = false;
            console.log('🔄 Reset button state');
        }
    }

    // Make the function globally available immediately
    window.generateSuggestions = generateSuggestions;

    // Initialize when DOM is ready
    function initSuggestionSystem() {
        console.log('Initializing AI Suggestion System');

        // Get elements
        const useSmartEngine = document.getElementById('useSmartEngine');
        const smartEngineOptions = document.getElementById('smartEngineOptions');
        const aiSuggestionsContainer = document.getElementById('aiSuggestionsContainer');
        const generateSuggestionsBtn = document.getElementById('generateSuggestions');
        const adTitleInput = document.getElementById('adTitle');

        // Debug: Log element existence
        console.log('Element check on initialization:');
        console.log('- Smart Engine toggle:', !!useSmartEngine);
        console.log('- Smart Engine options:', !!smartEngineOptions);
        console.log('- AI Suggestions container:', !!aiSuggestionsContainer);
        console.log('- Generate Suggestions button:', !!generateSuggestionsBtn);

        // Ensure Smart Engine toggle is properly initialized
        if (useSmartEngine) {
            console.log('Smart Engine toggle checked:', useSmartEngine.checked);

            // Don't force the toggle to be checked - let title validation control it
            // The checkAndDisableSmartEngine function will handle the proper state
        }

        // Setup UI state
        setupInitialUIState();

        // Setup event listeners
        setupEventListeners();

        // Initialize the Generate Suggestions button
        initGenerateButton();

        // Setup MutationObserver for dynamically created elements
        setupMutationObserver();

        // Check AI provider status and update UI
        checkAiProviderStatus();

        // Function to setup initial UI state
        function setupInitialUIState() {
            console.log('Setting up initial UI state');

            // Always show Smart Engine options if toggle exists and is checked
            if (smartEngineOptions && useSmartEngine) {
                const shouldShow = useSmartEngine.checked;
                console.log('Smart Engine toggle is checked:', shouldShow);
                // Force show the options if toggle is checked (default state)
                smartEngineOptions.style.display = shouldShow ? 'block' : 'none';
                console.log('Set Smart Engine options display to:', smartEngineOptions.style.display);

                // Also ensure the suggestions container is visible if toggle is checked
                if (aiSuggestionsContainer && shouldShow) {
                    aiSuggestionsContainer.style.display = 'block';
                    console.log('Made AI suggestions container visible');
                }
            }

            // Initialize suggestions container if it exists
            if (aiSuggestionsContainer) {
                // Always show the container if Smart Engine is enabled
                if (useSmartEngine && useSmartEngine.checked) {
                    aiSuggestionsContainer.style.display = 'block';
                    console.log('Set AI Suggestions container display to: block (Smart Engine enabled)');
                } else {
                    // Keep it visible if it has content, otherwise hide it
                    const hasContent = aiSuggestionsContainer.innerHTML.trim() !== '';
                    console.log('AI Suggestions container has content:', hasContent);
                    aiSuggestionsContainer.style.display = hasContent ? 'block' : 'none';
                    console.log('Set AI Suggestions container display to:', aiSuggestionsContainer.style.display);
                }
            } else {
                console.log('AI Suggestions container not found during initial setup');
            }

            // Always show Generate Suggestions button when Smart Engine is enabled
            if (generateSuggestionsBtn && useSmartEngine) {
                const shouldShow = useSmartEngine.checked;
                generateSuggestionsBtn.style.display = shouldShow ? 'block' : 'none';
                console.log('Generate Suggestions button display set to:', generateSuggestionsBtn.style.display);
            }

            // Check if title is provided and disable Smart Engine toggle if not
            checkAndDisableSmartEngine();
        }

        // Function to setup event listeners
        function setupEventListeners() {
            // Listen for changes in the title field
            if (adTitleInput) {
                adTitleInput.addEventListener('input', checkAndDisableSmartEngine);
            }

            // Setup "Go to Smart Engine" button functionality
            setupGoToSmartEngineButton();

            // Listen for changes in the Smart Engine toggle
            if (useSmartEngine && generateSuggestionsBtn) {
                useSmartEngine.addEventListener('change', function() {
                    // Always keep the Generate Suggestions button visible
                    generateSuggestionsBtn.style.display = 'block';

                    if (this.checked) {
                        // Show the Smart Engine options
                        if (smartEngineOptions) {
                            smartEngineOptions.style.display = 'block';
                        }

                        // Show the suggestions container
                        if (aiSuggestionsContainer) {
                            aiSuggestionsContainer.style.display = 'block';
                        }

                        // Set the used_ai field to true when Smart Engine is enabled
                        const usedAiInput = document.getElementById('usedAiInput');
                        if (usedAiInput) {
                            usedAiInput.value = 'true';
                        }
                    } else {
                        // Keep the Generate Suggestions button visible even when toggle is off
                        // Just hide the Smart Engine options and suggestions container
                        if (smartEngineOptions) {
                            smartEngineOptions.style.display = 'none';
                        }

                        if (aiSuggestionsContainer) {
                            aiSuggestionsContainer.style.display = 'none';
                        }

                        // Set the used_ai field to false when Smart Engine is disabled
                        const usedAiInput = document.getElementById('usedAiInput');
                        if (usedAiInput) {
                            usedAiInput.value = 'false';
                        }
                    }
                });
            }
        }

        // Function to check if title is provided and disable Smart Engine toggle if not
        function checkAndDisableSmartEngine() {
            if (useSmartEngine && adTitleInput) {
                const title = adTitleInput.value.trim();
                const toggleLabel = useSmartEngine.closest('.form-check');

                if (!title) {
                    // Disable the toggle when no title is provided
                    useSmartEngine.disabled = true;
                    useSmartEngine.checked = false;

                    // Add disabled styling to the toggle container
                    if (toggleLabel) {
                        toggleLabel.classList.add('smart-engine-disabled');
                    }

                    // Hide smart engine options if they're visible
                    const smartEngineOptions = document.getElementById('smartEngineOptions');
                    if (smartEngineOptions) {
                        smartEngineOptions.style.display = 'none';
                    }

                    // Hide generate suggestions button
                    if (generateSuggestionsBtn) {
                        generateSuggestionsBtn.style.display = 'none';
                    }

                    // Hide AI suggestions container
                    const aiSuggestionsContainer = document.getElementById('aiSuggestionsContainer');
                    if (aiSuggestionsContainer) {
                        aiSuggestionsContainer.style.display = 'none';
                    }

                    // Set the used_ai field to false
                    const usedAiInput = document.getElementById('usedAiInput');
                    if (usedAiInput) {
                        usedAiInput.value = 'false';
                    }
                } else {
                    // Enable the toggle when title is provided
                    useSmartEngine.disabled = false;

                    // Remove disabled styling from the toggle container
                    if (toggleLabel) {
                        toggleLabel.classList.remove('smart-engine-disabled');
                    }
                }
            }
        }

        // Function to setup "Go to Smart Engine" button functionality
        function setupGoToSmartEngineButton() {
            const goToSmartEngineBtn = document.getElementById('goToSmartEngineBtn');
            if (goToSmartEngineBtn) {
                goToSmartEngineBtn.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Navigate to Step 1 (Basic Info) tab
                    const step1Tab = document.getElementById('step1-tab');
                    if (step1Tab) {
                        // Use Bootstrap tab functionality to switch to Step 1
                        if (typeof bootstrap !== 'undefined' && bootstrap.Tab) {
                            const bsTab = new bootstrap.Tab(step1Tab);
                            bsTab.show();
                        } else {
                            // Fallback: manually trigger click
                            step1Tab.click();
                        }

                        // Scroll to the Smart Engine toggle after a short delay
                        setTimeout(() => {
                            const smartEngineToggle = document.getElementById('useSmartEngine');
                            if (smartEngineToggle) {
                                // Scroll the toggle into view
                                smartEngineToggle.scrollIntoView({
                                    behavior: 'smooth',
                                    block: 'center'
                                });

                                // Add a highlight effect to draw attention
                                const toggleContainer = smartEngineToggle.closest('.form-check');
                                if (toggleContainer) {
                                    toggleContainer.style.transition = 'all 0.3s ease';
                                    toggleContainer.style.backgroundColor = '#fff3cd';
                                    toggleContainer.style.border = '2px solid #ffc107';
                                    toggleContainer.style.borderRadius = '8px';
                                    toggleContainer.style.padding = '10px';

                                    // Remove highlight after 3 seconds
                                    setTimeout(() => {
                                        toggleContainer.style.backgroundColor = '';
                                        toggleContainer.style.border = '';
                                        toggleContainer.style.borderRadius = '';
                                        toggleContainer.style.padding = '';
                                    }, 3000);
                                }

                                // Enable the toggle if it's disabled due to no title
                                if (smartEngineToggle.disabled) {
                                    // Focus on the title field first to guide the user
                                    const titleInput = document.getElementById('adTitle');
                                    if (titleInput) {
                                        titleInput.focus();
                                        titleInput.style.border = '2px solid #ffc107';
                                        setTimeout(() => {
                                            titleInput.style.border = '';
                                        }, 3000);
                                    }
                                }
                            }
                        }, 300);
                    }
                });
            }
        }

        // Function to initialize the Generate Suggestions button
        function initGenerateButton() {
            console.log('Initializing Generate Button');

            // Get the button
            const generateBtn = document.getElementById('generateSuggestions');
            if (!generateBtn) {
                console.error('Generate Suggestions button not found!');
                return;
            }

            console.log('Generate Suggestions button found, setting up handler');

            // Remove any existing click handlers by setting onclick to null
            generateBtn.onclick = null;

            // Add our direct handler
            generateBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                generateSuggestions();
                return false;
            }, true);

            // Also set the onclick attribute directly as a backup
            generateBtn.setAttribute('onclick', 'if(window.generateSuggestions) { window.generateSuggestions(); } return false;');

            // Make the handler function globally available
            window.generateSuggestions = generateSuggestions;
        }

        // Function to setup MutationObserver for dynamically created elements
        function setupMutationObserver() {
            if (aiSuggestionsContainer) {
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                            // Check for newly added suggestion cards
                            mutation.addedNodes.forEach(function(node) {
                                if (node.nodeType === 1) { // Element node
                                    // Look for suggestion cards within the added node
                                    let cardsArray = [];
                                    if (node.querySelectorAll) {
                                        const cardsNodeList = node.querySelectorAll('.ai-suggestion-card');
                                        cardsArray = Array.from(cardsNodeList);
                                    }

                                    if (node.classList && node.classList.contains('ai-suggestion-card')) {
                                        cardsArray.push(node);
                                    }

                                    // Add apply buttons to new cards
                                    cardsArray.forEach(card => {
                                        setupSuggestionCard(card);
                                    });
                                }
                            });
                        }
                    });
                });

                // Start observing the suggestions container
                observer.observe(aiSuggestionsContainer, { childList: true, subtree: true });
            }
        }

        // Function to setup a suggestion card with apply button
        function setupSuggestionCard(card) {
            // Get the form-check element
            const formCheck = card.querySelector('.form-check');
            if (!formCheck) return;

            // Create button if it doesn't exist
            if (!formCheck.querySelector('.apply-suggestion-btn')) {
                const applyBtn = document.createElement('button');
                applyBtn.type = 'button';
                applyBtn.className = 'btn btn-sm btn-primary float-end apply-suggestion-btn';
                applyBtn.innerHTML = '<i class="fas fa-check me-1"></i> Apply';
                formCheck.appendChild(applyBtn);

                // Add click event
                applyBtn.addEventListener('click', function() {
                    const title = card.querySelector('.ai-suggestion-title').textContent;
                    const content = card.querySelector('.ai-suggestion-content').textContent;
                    applySuggestionToPreview(title, content);

                    // Also select the radio button
                    const radio = card.querySelector('.ai-suggestion-select');
                    if (radio) {
                        radio.checked = true;
                    }
                });
            }
        }

        // Function to check AI provider status and update UI
        function checkAiProviderStatus() {
            // Get AI provider status from the page
            const statusElement = document.getElementById('ai-provider-status-data');
            if (!statusElement) {
                console.warn('AI provider status element not found');
                return;
            }

            try {
                // Parse the status JSON
                const status = JSON.parse(statusElement.textContent);

                // Check if Turbo Engine is available
                const turboEngineAvailable = status.mistral && status.mistral.available;

                // Update Smart Engine toggle
                if (document.getElementById('useSmartEngine')) {
                    // Remove any tooltip
                    document.getElementById('useSmartEngine').title = '';

                    // Remove any info or warning messages
                    const infoElement = document.getElementById('smart-engine-info');
                    if (infoElement) {
                        infoElement.remove();
                    }

                    const warningElement = document.getElementById('smart-engine-warning');
                    if (warningElement) {
                        warningElement.remove();
                    }

                    // Check title validation before enabling the toggle
                    checkAndDisableSmartEngine();

                    // Only show status to admins
                    const isAdmin = document.body.hasAttribute('data-show-ai-status') &&
                                   document.body.getAttribute('data-show-ai-status') === 'true';

                    if (isAdmin && !turboEngineAvailable) {
                        // Add an admin-only info message
                        if (!document.getElementById('smart-engine-admin-info')) {
                            const infoElement = document.createElement('div');
                            infoElement.className = 'alert alert-info mt-2';
                            infoElement.id = 'smart-engine-admin-info';
                            infoElement.innerHTML = '<i class="fas fa-info-circle me-2"></i> <strong>Admin Notice:</strong> Smart Engine is in offline mode. Using fallback suggestions.';

                            // Add the info after the toggle
                            const toggleParent = document.getElementById('useSmartEngine').parentNode;
                            toggleParent.parentNode.insertBefore(infoElement, toggleParent.nextSibling);
                        }
                    }
                }

                // Update Generate Suggestions button
                if (generateSuggestionsBtn) {
                    // Only show status to admins
                    const isAdmin = document.body.hasAttribute('data-show-ai-status') &&
                                   document.body.getAttribute('data-show-ai-status') === 'true';

                    if (isAdmin && !turboEngineAvailable) {
                        // For admins, show a subtle indicator that we're in offline mode
                        generateSuggestionsBtn.classList.add('btn-primary');
                        generateSuggestionsBtn.classList.remove('btn-info');
                        generateSuggestionsBtn.classList.remove('btn-warning');

                        // Add a small indicator for admins only
                        generateSuggestionsBtn.innerHTML = '<i class="fas fa-magic me-2"></i> Generate Suggestions <small class="badge bg-secondary ms-1">Offline</small>';

                        // Add a tooltip explaining it's using fallback
                        generateSuggestionsBtn.title = 'Using offline mode with pre-generated suggestions (Admin Only)';
                    } else {
                        // For regular users or when online, always show the standard button
                        generateSuggestionsBtn.classList.add('btn-primary');
                        generateSuggestionsBtn.classList.remove('btn-info');
                        generateSuggestionsBtn.classList.remove('btn-warning');

                        // Update the text to the standard version
                        generateSuggestionsBtn.innerHTML = '<i class="fas fa-magic me-2"></i> Generate Suggestions';

                        // Clear any tooltip
                        generateSuggestionsBtn.title = '';
                    }
                }
            } catch (error) {
                console.error('Error parsing AI provider status:', error);
            }
        }
    }



    // Helper function to display suggestions
    function displaySuggestions(suggestions) {
        console.log('📺 displaySuggestions called with:', suggestions);
        console.log('📺 Number of suggestions:', suggestions ? suggestions.length : 0);

        // Make sure we have suggestions
        if (!suggestions || !Array.isArray(suggestions) || suggestions.length === 0) {
            console.error('❌ No valid suggestions to display');
            return;
        }

        // Get the container
        const aiSuggestionsContainer = document.getElementById('aiSuggestionsContainer');
        console.log('📺 aiSuggestionsContainer found:', !!aiSuggestionsContainer);
        if (!aiSuggestionsContainer) {
            console.error('❌ AI suggestions container not found');
            return;
        }

        // Make sure the container is visible
        console.log('📺 Making container visible...');
        aiSuggestionsContainer.style.display = 'block';
        console.log('📺 Container display style:', aiSuggestionsContainer.style.display);

        // Find existing suggestion cards
        const existingCards = aiSuggestionsContainer.querySelectorAll('.ai-suggestion-card');
        console.log('📺 Found existing cards:', existingCards.length);

        // Update existing cards with new suggestions
        suggestions.forEach((suggestion, index) => {
            console.log(`📺 Updating card ${index + 1} with:`, suggestion);
            if (index < existingCards.length) {
                const card = existingCards[index];

                // Update title
                const titleElement = card.querySelector('.ai-suggestion-title');
                if (titleElement) {
                    titleElement.textContent = suggestion.title || 'No title';
                    console.log(`📺 Updated title for card ${index + 1}:`, suggestion.title);
                }

                // Update content
                const contentElement = card.querySelector('.ai-suggestion-content');
                if (contentElement) {
                    contentElement.textContent = suggestion.content || 'No content';
                    console.log(`📺 Updated content for card ${index + 1}:`, suggestion.content);
                }

                // Make the card visible by overriding the CSS animation
                card.style.opacity = '1';
                card.style.visibility = 'visible';
                card.style.display = 'block';

                // Update apply button data-index
                const applyButton = card.querySelector('.apply-suggestion-btn');
                if (applyButton) {
                    applyButton.setAttribute('data-index', index);
                }

                // Update radio button value
                const radioButton = card.querySelector('.ai-suggestion-select');
                if (radioButton) {
                    radioButton.value = index + 1;
                    radioButton.id = `suggestion${index + 1}`;
                }

                // Update label
                const label = card.querySelector('.form-check-label');
                if (label) {
                    label.setAttribute('for', `suggestion${index + 1}`);
                    label.textContent = `Suggestion ${index + 1}`;
                }
            }
        });

        // Store suggestions in hidden input
        const aiSuggestionDataInput = document.getElementById('aiSuggestionData');
        if (aiSuggestionDataInput) {
            aiSuggestionDataInput.value = JSON.stringify(suggestions);
        }

        // Store suggestions globally
        window.aiSuggestions = suggestions;

        // Setup apply buttons
        setupApplySuggestionButtons();

        console.log('✅ Successfully updated suggestion cards with new content');
    }

    // Helper function to setup apply suggestion buttons
    function setupApplySuggestionButtons() {
        const applyButtons = document.querySelectorAll('.apply-suggestion-btn');
        applyButtons.forEach(button => {
            button.addEventListener('click', function() {
                const index = parseInt(this.dataset.index || '0');
                if (window.aiSuggestions && window.aiSuggestions[index]) {
                    applySuggestion(window.aiSuggestions[index]);

                    // Select the radio button
                    const radio = document.getElementById(`suggestion${index + 1}`);
                    if (radio) {
                        radio.checked = true;
                    }
                }
            });
        });

        // Also handle radio button changes
        const radioButtons = document.querySelectorAll('.ai-suggestion-select');
        radioButtons.forEach(radio => {
            radio.addEventListener('change', function() {
                if (this.checked) {
                    const index = parseInt(this.value) - 1;
                    if (window.aiSuggestions && window.aiSuggestions[index]) {
                        applySuggestion(window.aiSuggestions[index]);
                    }
                }
            });
        });
    }

    // Helper function to apply a suggestion
    function applySuggestion(suggestion) {
        // Apply title
        const adTitleInput = document.getElementById('adTitle');
        if (adTitleInput && suggestion.title) {
            adTitleInput.value = suggestion.title;
            // Trigger input event to update preview
            adTitleInput.dispatchEvent(new Event('input', { bubbles: true }));
        }

        // Apply content
        const adContentTextarea = document.getElementById('adContent');
        if (adContentTextarea && suggestion.content) {
            adContentTextarea.value = suggestion.content;
            // Trigger input event to update preview
            adContentTextarea.dispatchEvent(new Event('input', { bubbles: true }));
        }

        // Mark as used AI - try both possible input IDs
        const usedAiInput = document.getElementById('usedAiInput') || document.getElementById('usedAi');
        if (usedAiInput) {
            usedAiInput.value = 'true';
            console.log('Set usedAi to true');
        }

        // Update preview directly
        const previewTitle = document.getElementById('previewTitle');
        const previewContent = document.getElementById('previewContent');
        const reviewTitle = document.getElementById('reviewTitle');
        const reviewContent = document.getElementById('reviewContent');

        if (previewTitle) previewTitle.textContent = suggestion.title || '';
        if (previewContent) previewContent.textContent = suggestion.content || '';
        if (reviewTitle) reviewTitle.textContent = suggestion.title || '';
        if (reviewContent) reviewContent.textContent = suggestion.content || '';

        // Show preview container if it exists
        const previewWrapper = document.getElementById('previewWrapper');
        if (previewWrapper) {
            previewWrapper.style.display = 'block';
        }

        // Show success message
        showSuccess('Suggestion applied successfully!');
    }

    // Helper function to apply suggestion to preview
    function applySuggestionToPreview(title, content) {
        // Apply title
        const adTitleInput = document.getElementById('adTitle');
        if (adTitleInput) {
            adTitleInput.value = title;
            // Trigger input event to update preview
            adTitleInput.dispatchEvent(new Event('input', { bubbles: true }));
        }

        // Apply content
        const adContentTextarea = document.getElementById('adContent');
        if (adContentTextarea) {
            adContentTextarea.value = content;
            // Trigger input event to update preview
            adContentTextarea.dispatchEvent(new Event('input', { bubbles: true }));
        }

        // Mark as used AI
        const usedAiInput = document.getElementById('usedAiInput');
        if (usedAiInput) {
            usedAiInput.value = 'true';
            console.log('Set usedAi to true');
        }

        // Update preview directly
        const previewTitle = document.getElementById('previewTitle');
        const previewContent = document.getElementById('previewContent');
        const reviewTitle = document.getElementById('reviewTitle');
        const reviewContent = document.getElementById('reviewContent');

        if (previewTitle) previewTitle.textContent = title;
        if (previewContent) previewContent.textContent = content;
        if (reviewTitle) reviewTitle.textContent = title;
        if (reviewContent) reviewContent.textContent = content;

        // Show preview container if it exists
        const previewWrapper = document.getElementById('previewWrapper');
        if (previewWrapper) {
            previewWrapper.style.display = 'block';
            // Scroll to preview
            previewWrapper.scrollIntoView({ behavior: 'smooth' });
        }

        // Show success message
        showSuccess('Suggestion applied successfully!');
    }

    // Helper function to show success message
    function showSuccess(message) {
        // Create a success alert
        const successAlert = document.createElement('div');
        successAlert.className = 'alert alert-success alert-dismissible fade show';
        successAlert.innerHTML = `
            <i class="fas fa-check-circle me-2"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;

        // Add to the page
        const form = document.getElementById('adCreationForm');
        if (form) {
            form.prepend(successAlert);

            // Remove after 3 seconds
            setTimeout(() => {
                successAlert.remove();
            }, 3000);
        }
    }

    // Helper function to show error message
    function showError(message) {
        const formErrorContainer = document.getElementById('formErrorContainer');
        if (formErrorContainer) {
            formErrorContainer.textContent = message;
            formErrorContainer.style.display = 'block';

            // Hide after 5 seconds
            setTimeout(() => {
                formErrorContainer.style.display = 'none';
            }, 5000);
        } else {
            // Fallback to alert
            alert(message);
        }
    }

    // Function to display fallback suggestions when AI service is unavailable
    function displayFallbackSuggestions() {
        // Kenya-specific business ideas
        const kenyaIdeas = [
            {
                title: "Mobile Boda-Boda Booking",
                content: "Launch a mobile app for booking motorcycle taxis (boda-bodas) in urban areas. Connect riders with customers for safe, reliable transportation."
            },
            {
                title: "Local Tutoring Service",
                content: "Start an online platform connecting Kenyan students with qualified local tutors for personalized learning experiences and exam preparation."
            },
            {
                title: "SME Accounting Support",
                content: "Offer remote accounting and bookkeeping services tailored for small businesses in Kenya, helping them manage finances and tax compliance."
            },
            {
                title: "Office Food Delivery",
                content: "Create a service delivering fresh, local meals to offices in Nairobi and other business districts, featuring traditional and modern Kenyan cuisine."
            },
            {
                title: "Church Digital Tools",
                content: "Develop digital solutions for churches including donation platforms, event management, and community engagement tools tailored for Kenyan congregations."
            },
            {
                title: "Agri-Tech Solutions",
                content: "Provide technology solutions for Kenyan farmers, including weather alerts, market prices, and crop management advice through SMS and mobile apps."
            }
        ];

        // Convert to suggestion objects with proper formatting
        const suggestions = kenyaIdeas.map((idea) => ({
            title: idea.title,
            content: idea.content,
            model: "offline",
            ai_generated: false,
            fallback: true,
            language: "english"
        }));

        // Display the suggestions (limited to 3)
        displaySuggestions(suggestions.slice(0, 3));
    }

    // Helper function to get CSRF token
    function getCSRFToken() {
        // First try to get from cookies
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.startsWith('csrftoken=')) {
                return cookie.substring('csrftoken='.length, cookie.length);
            }
        }

        // If not found in cookies, try to get from the meta tag
        const csrfTokenMeta = document.querySelector('meta[name="csrf-token"]');
        if (csrfTokenMeta) {
            return csrfTokenMeta.getAttribute('content');
        }

        // If still not found, try to get from the hidden input
        const csrfTokenInput = document.querySelector('input[name="csrfmiddlewaretoken"]');
        if (csrfTokenInput) {
            return csrfTokenInput.value;
        }

        console.warn('CSRF token not found in cookies, meta tag, or hidden input');
        return '';
    }

    // Make key functions globally available
    window.generateSuggestions = generateSuggestions;
    window.displaySuggestions = displaySuggestions;
    window.displayFallbackSuggestions = displayFallbackSuggestions;
    window.applySuggestion = applySuggestion;
    window.applySuggestionToPreview = applySuggestionToPreview;
    window.showAiError = showError;

    // Initialize immediately
    initSuggestionSystem();

    // Also initialize on DOMContentLoaded for safety
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOMContentLoaded - initializing AI Suggestion System again');
        initSuggestionSystem();
    });

    // Also initialize after a short delay to ensure it runs after other scripts
    setTimeout(function() {
        console.log('Delayed initialization - initializing AI Suggestion System again');
        initSuggestionSystem();
    }, 500);
})();
