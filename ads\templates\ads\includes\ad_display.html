{% comment %}
    Enterprise-Grade Ad Display Template

    This template is used to display ads in different locations throughout the site with specialized
    display methods for each location type.

    Parameters:
    - location: The location identifier (e.g., 'header', 'sidebar', 'footer', 'popup', 'content')
    - max_ads: Maximum number of ads to display (default: 1)
    - rotation: Rotation strategy ('random', 'newest', 'oldest', 'highest_ctr') (default: 'random')
    - show_carousel: Whether to show a carousel for multiple ads (default: False)
    - display_style: Override the default display style for the location ('carousel', 'stack', 'grid', 'popup', 'sticky')

    Location-specific display methods:
    - header: Horizontal banner (full width)
    - sidebar: Vertical stack or sticky ad
    - content: Native ad integrated with content
    - footer: Horizontal banner or grid
    - popup: Modal popup with close button
    - interstitial: Full-screen takeover with countdown
    - sticky: Fixed position ad that stays in view while scrolling

    Usage:
    {% include 'ads/includes/ad_display.html' with location='sidebar' max_ads=2 rotation='random' display_style='sticky' %}
{% endcomment %}

{% load static %}

{% with display_style=display_style|default:location %}
{% if ads or show_placeholder|default:False %}
<div class="ad-container ad-location-{{ location|default:'default' }} ad-display-{{ display_style }}">
    {% if ads %}

        {% if display_style == 'popup' %}
            <!-- Popup Ad Display -->
            <div class="ad-popup" id="adPopup-{{ location }}">
                <div class="ad-popup-content">
                    <button class="ad-popup-close" onclick="closePopup('{{ location }}')">×</button>
                    {% with ad=ads.0 %}
                        <div class="ad-item" data-ad-id="{{ ad.id }}" data-ad-type="{{ ad.ad_type.name }}" data-ad-location="{{ location }}">
                            {% if ad.media %}
                                <a href="{{ ad.cta_link }}" target="_blank" class="ad-link" rel="noopener">
                                    <img src="{{ ad.media.url }}" alt="{{ ad.title }}" class="ad-image">
                                    <div class="ad-content-overlay">
                                        <h3 class="ad-title">{{ ad.title }}</h3>
                                        <p class="ad-description">{{ ad.content|truncatechars:100 }}</p>
                                    </div>
                                </a>
                            {% else %}
                                <a href="{{ ad.cta_link }}" target="_blank" class="ad-link ad-text-only" rel="noopener">
                                    <div class="ad-content">
                                        <h3 class="ad-title">{{ ad.title }}</h3>
                                        <p class="ad-description">{{ ad.content|truncatechars:100 }}</p>
                                    </div>
                                </a>
                            {% endif %}
                            {% if show_sponsor_label|default:True %}
                                <div class="ad-sponsor-label">Sponsored</div>
                            {% endif %}
                        </div>
                    {% endwith %}
                </div>
            </div>

        {% elif display_style == 'interstitial' %}
            <!-- Interstitial (Full-screen) Ad Display -->
            <div class="ad-interstitial" id="adInterstitial-{{ location }}">
                <div class="ad-interstitial-content">
                    <div class="ad-interstitial-header">
                        <div class="ad-interstitial-countdown">
                            <span id="adInterstitialCountdown-{{ location }}">5</span>
                        </div>
                        <button class="ad-interstitial-close" onclick="closeInterstitial('{{ location }}')">Skip Ad</button>
                    </div>
                    {% with ad=ads.0 %}
                        <div class="ad-item" data-ad-id="{{ ad.id }}" data-ad-type="{{ ad.ad_type.name }}" data-ad-location="{{ location }}">
                            {% if ad.media %}
                                <a href="{{ ad.cta_link }}" target="_blank" class="ad-link" rel="noopener">
                                    <img src="{{ ad.media.url }}" alt="{{ ad.title }}" class="ad-image">
                                    <div class="ad-content-overlay">
                                        <h3 class="ad-title">{{ ad.title }}</h3>
                                        <p class="ad-description">{{ ad.content|truncatechars:100 }}</p>
                                    </div>
                                </a>
                            {% else %}
                                <a href="{{ ad.cta_link }}" target="_blank" class="ad-link ad-text-only" rel="noopener">
                                    <div class="ad-content">
                                        <h3 class="ad-title">{{ ad.title }}</h3>
                                        <p class="ad-description">{{ ad.content|truncatechars:100 }}</p>
                                    </div>
                                </a>
                            {% endif %}
                            {% if show_sponsor_label|default:True %}
                                <div class="ad-sponsor-label">Sponsored</div>
                            {% endif %}
                        </div>
                    {% endwith %}
                </div>
            </div>

        {% elif display_style == 'sticky' %}
            <!-- Sticky Ad Display -->
            <div class="ad-sticky" id="adSticky-{{ location }}">
                <button class="ad-sticky-close" onclick="closeSticky('{{ location }}')">×</button>
                {% with ad=ads.0 %}
                    <div class="ad-item" data-ad-id="{{ ad.id }}" data-ad-type="{{ ad.ad_type.name }}" data-ad-location="{{ location }}">
                        {% if ad.media %}
                            <a href="{{ ad.cta_link }}" target="_blank" class="ad-link" rel="noopener">
                                <img src="{{ ad.media.url }}" alt="{{ ad.title }}" class="ad-image">
                                <div class="ad-content-overlay">
                                    <h3 class="ad-title">{{ ad.title }}</h3>
                                    <p class="ad-description">{{ ad.content|truncatechars:50 }}</p>
                                </div>
                            </a>
                        {% else %}
                            <a href="{{ ad.cta_link }}" target="_blank" class="ad-link ad-text-only" rel="noopener">
                                <div class="ad-content">
                                    <h3 class="ad-title">{{ ad.title }}</h3>
                                    <p class="ad-description">{{ ad.content|truncatechars:50 }}</p>
                                </div>
                            </a>
                        {% endif %}
                        {% if show_sponsor_label|default:True %}
                            <div class="ad-sponsor-label">Sponsored</div>
                        {% endif %}
                    </div>
                {% endwith %}
            </div>

        {% elif display_style == 'grid' and ads|length > 1 %}
            <!-- Grid Display for Multiple Ads -->
            <div class="ad-grid">
                {% for ad in ads|slice:":max_ads|default:4" %}
                    <div class="ad-grid-item" data-ad-id="{{ ad.id }}" data-ad-type="{{ ad.ad_type.name }}" data-ad-location="{{ location }}">
                        {% if ad.media %}
                            <a href="{{ ad.cta_link }}" target="_blank" class="ad-link" rel="noopener">
                                <img src="{{ ad.media.url }}" alt="{{ ad.title }}" class="ad-image">
                                <div class="ad-content-overlay">
                                    <h3 class="ad-title">{{ ad.title }}</h3>
                                </div>
                            </a>
                        {% else %}
                            <a href="{{ ad.cta_link }}" target="_blank" class="ad-link ad-text-only" rel="noopener">
                                <div class="ad-content">
                                    <h3 class="ad-title">{{ ad.title }}</h3>
                                </div>
                            </a>
                        {% endif %}
                        {% if show_sponsor_label|default:True %}
                            <div class="ad-sponsor-label">Sponsored</div>
                        {% endif %}
                    </div>
                {% endfor %}
            </div>

        {% elif display_style == 'stack' and ads|length > 1 %}
            <!-- Vertical Stack Display -->
            <div class="ad-stack">
                {% for ad in ads|slice:":max_ads|default:3" %}
                    <div class="ad-stack-item" data-ad-id="{{ ad.id }}" data-ad-type="{{ ad.ad_type.name }}" data-ad-location="{{ location }}">
                        {% if ad.media %}
                            <a href="{{ ad.cta_link }}" target="_blank" class="ad-link" rel="noopener">
                                <img src="{{ ad.media.url }}" alt="{{ ad.title }}" class="ad-image">
                                <div class="ad-content-overlay">
                                    <h3 class="ad-title">{{ ad.title }}</h3>
                                    <p class="ad-description">{{ ad.content|truncatechars:50 }}</p>
                                </div>
                            </a>
                        {% else %}
                            <a href="{{ ad.cta_link }}" target="_blank" class="ad-link ad-text-only" rel="noopener">
                                <div class="ad-content">
                                    <h3 class="ad-title">{{ ad.title }}</h3>
                                    <p class="ad-description">{{ ad.content|truncatechars:50 }}</p>
                                </div>
                            </a>
                        {% endif %}
                        {% if show_sponsor_label|default:True %}
                            <div class="ad-sponsor-label">Sponsored</div>
                        {% endif %}
                    </div>
                {% endfor %}
            </div>

        {% elif show_carousel|default:False and ads|length > 1 %}
            <!-- Carousel for multiple ads -->
            <div class="ad-carousel" id="adCarousel-{{ location }}">
                <div class="ad-carousel-inner">
                    {% for ad in ads %}
                        <div class="ad-carousel-item {% if forloop.first %}active{% endif %}" data-ad-id="{{ ad.id }}">
                            <div class="ad-item" data-ad-type="{{ ad.ad_type.name }}" data-ad-location="{{ location|default:'default' }}">
                                {% if ad.media %}
                                    <div class="ad-link-container">
                                        <a href="{{ ad.cta_link }}" target="_blank" class="ad-link" rel="noopener">
                                            <img src="{{ ad.media.url }}" alt="{{ ad.title }}" class="ad-image">
                                            <div class="ad-content-overlay">
                                                <h3 class="ad-title">{{ ad.title }}</h3>
                                                <p class="ad-description">{{ ad.content|truncatechars:100 }}</p>
                                                <button class="ad-cta-button" onclick="window.open('{{ ad.cta_link }}', '_blank')">Learn More</button>
                                            </div>
                                        </a>
                                    </div>
                                {% else %}
                                    <div class="ad-link-container">
                                        <a href="{{ ad.cta_link }}" target="_blank" class="ad-link ad-text-only" rel="noopener">
                                            <div class="ad-content">
                                                <h3 class="ad-title">{{ ad.title }}</h3>
                                                <p class="ad-description">{{ ad.content|truncatechars:100 }}</p>
                                                <button class="ad-cta-button" onclick="window.open('{{ ad.cta_link }}', '_blank')">Learn More</button>
                                            </div>
                                        </a>
                                    </div>
                                {% endif %}

                                {% if show_sponsor_label|default:True %}
                                    <div class="ad-sponsor-label">Sponsored</div>
                                {% endif %}
                            </div>
                        </div>
                    {% endfor %}
                </div>

                <!-- Carousel Controls -->
                {% if ads|length > 1 %}
                    <div class="ad-carousel-controls">
                        <button class="ad-carousel-prev" onclick="moveCarousel('{{ location }}', -1)">❮</button>
                        <div class="ad-carousel-indicators">
                            {% for ad in ads %}
                                <span class="ad-carousel-dot {% if forloop.first %}active{% endif %}" onclick="jumpToSlide('{{ location }}', {{ forloop.counter0 }})"></span>
                            {% endfor %}
                        </div>
                        <button class="ad-carousel-next" onclick="moveCarousel('{{ location }}', 1)">❯</button>
                    </div>
                {% endif %}
            </div>
            <hr class="golden-hr">

        {% else %}
            <!-- Standard Display (single ad or default) -->
            {% for ad in ads|slice:":max_ads|default:1" %}
                <div class="ad-item" data-ad-id="{{ ad.id }}" data-ad-type="{{ ad.ad_type.name }}" data-ad-location="{{ location|default:'default' }}">
                    {% if ad.media %}
                        <a href="{{ ad.cta_link }}" target="_blank" class="ad-link" rel="noopener">
                            <img src="{{ ad.media.url }}" alt="{{ ad.title }}" class="ad-image">
                            <div class="ad-content-overlay">
                                <h3 class="ad-title">{{ ad.title }}</h3>
                                <p class="ad-description">{{ ad.content|truncatechars:100 }}</p>
                            </div>
                        </a>
                    {% else %}
                        <a href="{{ ad.cta_link }}" target="_blank" class="ad-link ad-text-only" rel="noopener">
                            <div class="ad-content">
                                <h3 class="ad-title">{{ ad.title }}</h3>
                                <p class="ad-description">{{ ad.content|truncatechars:100 }}</p>
                            </div>
                        </a>
                    {% endif %}

                    {% if show_sponsor_label|default:True %}
                        <div class="ad-sponsor-label">Sponsored</div>
                    {% endif %}
                </div>
            {% endfor %}

            {% if ads|length > max_ads|default:1 and not show_carousel|default:False %}
                <div class="ad-more-indicator">
                    <span>{{ ads|length|add:"-max_ads|default:1" }} more ads available</span>
                </div>
            {% endif %}
        {% endif %}
    {% else %}
        {% if show_placeholder|default:False %}
            <div class="ad-placeholder">
                <div class="ad-placeholder-text">Your Ad Here</div>
                <div class="ad-placeholder-subtext">No ads available for this location</div>
            </div>
        {% endif %}
    {% endif %}
</div>
{% endif %}
{% endwith %}

<style>
    .ad-container {
        margin-bottom: 20px;
        position: relative;
    }

    .ad-item {
        position: relative;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
        margin-bottom: 15px;
    }

    .ad-item a {
        position: relative;
        display: block;
    }

    .ad-item:hover {
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }

    .ad-link {
        display: block;
        text-decoration: none;
        color: inherit;
    }

    .ad-image {
        width: 100%;
        height: auto;
        display: block;
        object-fit: cover;
    }

    .ad-text-only {
        padding: 15px;
        background-color: #f8f9fa;
        min-height: 100px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .ad-content {
        text-align: center;
    }

    .ad-content-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: rgba(0, 0, 0, 0.7);
        padding: 10px;
        color: white;
        text-align: center;
    }

    .ad-content-overlay .ad-title {
        color: white;
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 5px;
    }

    .ad-content-overlay .ad-description {
        color: rgba(255, 255, 255, 0.8);
        font-size: 14px;
        margin-bottom: 0;
    }

    .ad-title {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 5px;
        color: #1a237e;
    }

    .ad-description {
        font-size: 14px;
        color: #666;
        margin-bottom: 0;
    }

    .ad-sponsor-label {
        position: absolute;
        top: 5px;
        right: 5px;
        background-color: rgba(0,0,0,0.6);
        color: white;
        font-size: 10px;
        padding: 2px 6px;
        border-radius: 3px;
        opacity: 0.8;
        z-index: 10;
    }

    /* Add margin between image and content */
    .ad-carousel-item .ad-link {
        display: flex;
        align-items: center;
    }

    .ad-carousel-item .ad-image {
        margin-right: 15px;
    }

    /* Align button with sponsored text */
    .ad-carousel-item .ad-content {
        position: relative;
        padding-bottom: 30px; /* Space for the button */
    }

    .ad-carousel-item .ad-cta-button {
        position: absolute;
        bottom: 5px;
        right: 5px;
        padding: 5px 12px;
        background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
        color: white;
        font-size: 11px;
        border-radius: 4px;
        text-decoration: none;
        border: none;
        cursor: pointer;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        transition: all 0.3s ease;
    }

    .ad-carousel-item .ad-cta-button:hover {
        background: linear-gradient(135deg, #283593 0%, #5c6bc0 100%);
        box-shadow: 0 3px 8px rgba(0,0,0,0.3);
        transform: translateY(-1px);
    }

    .ad-placeholder {
        width: 100%;
        height: 200px;
        background-color: #f1f3f9;
        border: 2px dashed #d1d9e6;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .ad-placeholder-text {
        color: #8c9eff;
        font-weight: 600;
        font-size: 18px;
        margin-bottom: 5px;
    }

    .ad-placeholder-subtext {
        color: #a0a0a0;
        font-size: 12px;
    }

    /* Location-specific styles */
    /* Header Ads */
    .ad-location-header,
    .ad-location-premium_header {
        width: 100%;
    }

    .ad-location-header .ad-image,
    .ad-location-premium_header .ad-image {
        width: 100%;
        height: 90px;
        object-fit: cover;
    }

    /* Sidebar Ads */
    .ad-location-sidebar_left,
    .ad-location-sidebar_right,
    .ad-location-sidebar_top,
    .ad-location-sidebar_middle {
        width: 100%;
    }

    .ad-location-sidebar_left .ad-image,
    .ad-location-sidebar_right .ad-image,
    .ad-location-sidebar_top .ad-image,
    .ad-location-sidebar_middle .ad-image {
        width: 100%;
        height: 250px;
        object-fit: cover;
    }

    .ad-location-sidebar_left .ad-item,
    .ad-location-sidebar_right .ad-item,
    .ad-location-sidebar_top .ad-item,
    .ad-location-sidebar_middle .ad-item {
        margin-bottom: 15px;
    }

    /* Content Ads */
    .ad-location-content_top,
    .ad-location-content_bottom,
    .ad-location-content_inline,
    .ad-location-homepage_featured {
        width: 100%;
        margin: 20px 0;
    }

    .ad-location-content_top .ad-image,
    .ad-location-content_bottom .ad-image,
    .ad-location-content_inline .ad-image,
    .ad-location-homepage_featured .ad-image {
        width: 100%;
        height: 180px;
        object-fit: cover;
    }

    /* Footer Ads */
    .ad-location-footer,
    .ad-location-footer_banner {
        width: 100%;
    }

    .ad-location-footer .ad-image,
    .ad-location-footer_banner .ad-image {
        width: 100%;
        height: 90px;
        object-fit: cover;
    }

    /* Popup Ads */
    .ad-location-popup {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1000;
        max-width: 300px;
    }

    .ad-location-popup .ad-image {
        width: 100%;
        height: 250px;
        object-fit: cover;
    }

    .ad-location-popup .ad-item {
        box-shadow: 0 5px 25px rgba(0,0,0,0.15);
    }

    /* Special Locations */
    .ad-location-qr_code_results_page .ad-image,
    .ad-location-profile_dashboard .ad-image {
        width: 100%;
        height: 180px;
        object-fit: cover;
    }

    /* Popup Ad Styles */
    .ad-popup {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1000;
        max-width: 350px;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 5px 25px rgba(0,0,0,0.2);
        animation: adPopupFadeIn 0.5s ease;
    }

    .ad-popup-content {
        position: relative;
        padding: 0;
        border-radius: 8px;
        overflow: hidden;
    }

    .ad-popup-close {
        position: absolute;
        top: 5px;
        right: 5px;
        width: 24px;
        height: 24px;
        background-color: rgba(0,0,0,0.6);
        color: white;
        border: none;
        border-radius: 50%;
        font-size: 16px;
        line-height: 1;
        cursor: pointer;
        z-index: 10;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    @keyframes adPopupFadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    @keyframes adPopupFadeOut {
        from { opacity: 1; transform: translateY(0); }
        to { opacity: 0; transform: translateY(20px); }
    }

    /* Interstitial Ad Styles */
    .ad-interstitial {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0,0,0,0.8);
        z-index: 2000;
        display: flex;
        align-items: center;
        justify-content: center;
        animation: adInterstitialFadeIn 0.5s ease;
    }

    .ad-interstitial-content {
        position: relative;
        width: 80%;
        max-width: 800px;
        background-color: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 5px 25px rgba(0,0,0,0.3);
    }

    .ad-interstitial-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 15px;
        background-color: #f1f3f9;
    }

    .ad-interstitial-countdown {
        font-size: 14px;
        font-weight: 600;
        color: #1a237e;
    }

    .ad-interstitial-close {
        background-color: transparent;
        border: none;
        color: #666;
        cursor: pointer;
        font-size: 14px;
        padding: 5px 10px;
        border-radius: 4px;
        transition: background-color 0.3s;
    }

    .ad-interstitial-close:hover {
        background-color: rgba(0,0,0,0.1);
    }

    @keyframes adInterstitialFadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    @keyframes adInterstitialFadeOut {
        from { opacity: 1; }
        to { opacity: 0; }
    }

    /* Sticky Ad Styles */
    .ad-sticky {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: white;
        box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        z-index: 900;
        padding: 10px;
        animation: adStickySlideUp 0.5s ease;
    }

    .ad-sticky .ad-item {
        display: flex;
        align-items: center;
        max-height: 90px;
        overflow: hidden;
    }

    .ad-sticky .ad-image {
        max-width: 120px;
        height: 70px;
        object-fit: cover;
        margin-right: 15px;
    }

    .ad-sticky-close {
        position: absolute;
        top: 5px;
        right: 5px;
        width: 20px;
        height: 20px;
        background-color: rgba(0,0,0,0.6);
        color: white;
        border: none;
        border-radius: 50%;
        font-size: 14px;
        line-height: 1;
        cursor: pointer;
        z-index: 10;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    @keyframes adStickySlideUp {
        from { transform: translateY(100%); }
        to { transform: translateY(0); }
    }

    @keyframes adStickySlideDown {
        from { transform: translateY(0); }
        to { transform: translateY(100%); }
    }

    /* Grid Ad Styles */
    .ad-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 15px;
        width: 100%;
    }

    .ad-grid-item {
        position: relative;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
        height: 200px;
    }

    .ad-grid-item:hover {
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }

    .ad-grid-item .ad-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .ad-grid-item .ad-content-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: rgba(0,0,0,0.7);
        padding: 10px;
        color: white;
    }

    /* Stack Ad Styles */
    .ad-stack {
        display: flex;
        flex-direction: column;
        gap: 15px;
        width: 100%;
    }

    .ad-stack-item {
        position: relative;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
    }

    .ad-stack-item:hover {
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }

    .ad-stack-item .ad-image {
        width: 100%;
        height: 150px;
        object-fit: cover;
    }

    /* Carousel Styles */
    .ad-carousel {
        position: relative;
        width: 100%;
        overflow: hidden;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }

    .ad-carousel-inner {
        display: flex;
        transition: transform 0.5s ease;
        width: 100%;
    }

    .ad-carousel-item {
        flex: 0 0 100%;
        display: none;
        width: 100%;
    }

    .ad-carousel-item.active {
        display: block;
    }

    .ad-carousel-controls {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px;
        background-color: rgba(0,0,0,0.05);
    }

    .ad-carousel-prev,
    .ad-carousel-next {
        background-color: rgba(0,0,0,0.1);
        color: #333;
        border: none;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 16px;
        transition: background-color 0.3s;
    }

    .ad-carousel-prev:hover,
    .ad-carousel-next:hover {
        background-color: rgba(0,0,0,0.2);
    }

    .ad-carousel-indicators {
        display: flex;
        justify-content: center;
        gap: 5px;
    }

    .ad-carousel-dot {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color: rgba(0,0,0,0.2);
        cursor: pointer;
        transition: background-color 0.3s;
    }

    .ad-carousel-dot.active {
        background-color: #1a237e;
    }

    .ad-more-indicator {
        text-align: center;
        padding: 5px;
        background-color: #f1f3f9;
        border-radius: 0 0 8px 8px;
        font-size: 12px;
        color: #666;
        margin-top: -5px;
    }

    @media (max-width: 768px) {
        .ad-location-popup {
            bottom: 10px;
            right: 10px;
            max-width: 250px;
        }

        .ad-carousel-controls {
            padding: 5px;
        }

        .ad-carousel-prev,
        .ad-carousel-next {
            width: 24px;
            height: 24px;
            font-size: 14px;
        }

        .ad-carousel-dot {
            width: 8px;
            height: 8px;
        }
    }
</style>

{% block extra_js %}
<script src="{% static 'ads/js/ad-tracking.js' %}"></script>
<script>
    // Carousel functionality
    function moveCarousel(location, direction) {
        const carousel = document.getElementById(`adCarousel-${location}`);
        if (!carousel) return;

        const items = carousel.querySelectorAll('.ad-carousel-item');
        const dots = carousel.querySelectorAll('.ad-carousel-dot');

        // Find the currently active item
        let activeIndex = 0;
        items.forEach((item, index) => {
            if (item.classList.contains('active')) {
                activeIndex = index;
            }
        });

        // Calculate the new index
        let newIndex = activeIndex + direction;
        if (newIndex < 0) newIndex = items.length - 1;
        if (newIndex >= items.length) newIndex = 0;

        // Update active classes
        items.forEach(item => item.classList.remove('active'));
        dots.forEach(dot => dot.classList.remove('active'));

        items[newIndex].classList.add('active');
        dots[newIndex].classList.add('active');

        // Track impression for the newly visible ad
        const adId = items[newIndex].getAttribute('data-ad-id');
        if (adId && typeof trackImpression === 'function') {
            trackImpression(adId, location);
        }
    }

    function jumpToSlide(location, index) {
        const carousel = document.getElementById(`adCarousel-${location}`);
        if (!carousel) return;

        const items = carousel.querySelectorAll('.ad-carousel-item');
        const dots = carousel.querySelectorAll('.ad-carousel-dot');

        // Update active classes
        items.forEach(item => item.classList.remove('active'));
        dots.forEach(dot => dot.classList.remove('active'));

        items[index].classList.add('active');
        dots[index].classList.add('active');

        // Track impression for the newly visible ad
        const adId = items[index].getAttribute('data-ad-id');
        if (adId && typeof trackImpression === 'function') {
            trackImpression(adId, location);
        }
    }

    // Popup ad functionality
    function closePopup(location) {
        const popup = document.getElementById(`adPopup-${location}`);
        if (popup) {
            popup.style.animation = 'adPopupFadeOut 0.5s ease forwards';
            setTimeout(() => {
                popup.style.display = 'none';
            }, 500);

            // Store in localStorage that this popup was closed
            localStorage.setItem(`adPopup-${location}-closed`, 'true');
        }
    }

    // Interstitial ad functionality
    function closeInterstitial(location) {
        const interstitial = document.getElementById(`adInterstitial-${location}`);
        if (interstitial) {
            interstitial.style.animation = 'adInterstitialFadeOut 0.5s ease forwards';
            setTimeout(() => {
                interstitial.style.display = 'none';
            }, 500);

            // Store in localStorage that this interstitial was shown
            localStorage.setItem(`adInterstitial-${location}-shown`, 'true');
        }
    }

    // Sticky ad functionality
    function closeSticky(location) {
        const sticky = document.getElementById(`adSticky-${location}`);
        if (sticky) {
            sticky.style.animation = 'adStickySlideDown 0.5s ease forwards';
            setTimeout(() => {
                sticky.style.display = 'none';
            }, 500);

            // Store in localStorage that this sticky ad was closed
            localStorage.setItem(`adSticky-${location}-closed`, 'true');
        }
    }

    // Initialize all ad displays
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize carousels
        const carousels = document.querySelectorAll('.ad-carousel');
        carousels.forEach(carousel => {
            const location = carousel.id.replace('adCarousel-', '');

            // Set up auto-rotation
            setInterval(() => {
                moveCarousel(location, 1);
            }, 5000); // Rotate every 5 seconds
        });

        // Initialize interstitial countdown
        const interstitials = document.querySelectorAll('.ad-interstitial');
        interstitials.forEach(interstitial => {
            const location = interstitial.id.replace('adInterstitial-', '');
            const countdownElement = document.getElementById(`adInterstitialCountdown-${location}`);

            if (countdownElement) {
                let countdown = 5;
                const countdownInterval = setInterval(() => {
                    countdown--;
                    countdownElement.textContent = countdown;

                    if (countdown <= 0) {
                        clearInterval(countdownInterval);
                        document.querySelector(`#adInterstitial-${location} .ad-interstitial-close`).textContent = 'Close';
                    }
                }, 1000);
            }
        });

        // Check if popups should be shown (not shown in the last 24 hours)
        const popups = document.querySelectorAll('.ad-popup');
        popups.forEach(popup => {
            const location = popup.id.replace('adPopup-', '');
            const lastClosed = localStorage.getItem(`adPopup-${location}-closed`);

            if (lastClosed === 'true') {
                // Don't show the popup if it was closed recently
                popup.style.display = 'none';
            } else {
                // Show popup after a delay
                setTimeout(() => {
                    popup.style.display = 'block';

                    // Track impression
                    const adItem = popup.querySelector('.ad-item');
                    if (adItem && typeof trackImpression === 'function') {
                        const adId = adItem.getAttribute('data-ad-id');
                        if (adId) {
                            trackImpression(adId, location);
                        }
                    }
                }, 8000); // Show after 8 seconds - less intrusive
            }
        });

        // Check if interstitials should be shown (not shown in this session)
        const interstitialShown = sessionStorage.getItem('interstitialShown');
        if (!interstitialShown) {
            // Only show one interstitial per session
            const firstInterstitial = document.querySelector('.ad-interstitial');
            if (firstInterstitial) {
                firstInterstitial.style.display = 'flex';
                sessionStorage.setItem('interstitialShown', 'true');

                // Track impression
                const adItem = firstInterstitial.querySelector('.ad-item');
                if (adItem && typeof trackImpression === 'function') {
                    const adId = adItem.getAttribute('data-ad-id');
                    const location = firstInterstitial.id.replace('adInterstitial-', '');
                    if (adId) {
                        trackImpression(adId, location);
                    }
                }
            }
        } else {
            // Hide all interstitials if one was already shown
            document.querySelectorAll('.ad-interstitial').forEach(interstitial => {
                interstitial.style.display = 'none';
            });
        }
    });
</script>
{% endblock %}
