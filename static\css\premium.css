/* Premium Features Styling */

:root {
    --primary-color: #0078d4;
    --secondary-color: #00a2ed;
    --accent-color: #ffb900;
    --dark-color: #1a1a1a;
    --light-color: #f8f9fa;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;
    --border-radius: 8px;
    --border-radius-sm: 4px;
    --border-radius-lg: 12px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --box-shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* Hero Section */
.premium-hero {
    background: linear-gradient(135deg, var(--primary-color), #005a9e);
    color: white;
    padding: 6rem 0;
    position: relative;
    overflow: hidden;
}

.premium-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('../img/patterns/dots-pattern.svg');
    background-size: 30px;
    opacity: 0.1;
    z-index: 1;
}

.premium-hero-content {
    position: relative;
    z-index: 2;
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
    padding: 0 1rem;
}

.premium-badge {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #f5b014, #e67e22);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 10px rgba(230, 126, 34, 0.3);
}

.premium-badge i {
    margin-right: 0.5rem;
    font-size: 1rem;
}

.premium-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    background: linear-gradient(to right, #ffffff, #e0e0e0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.premium-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-cta {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
}

.hero-cta .btn {
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.hero-cta .btn-primary {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
    color: var(--dark-color);
}

.hero-cta .btn-primary:hover {
    background-color: #e6a800;
    border-color: #e6a800;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(255, 185, 0, 0.3);
}

.hero-cta .btn-outline-light:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(255, 255, 255, 0.1);
}

.premium-hero-image {
    position: relative;
    z-index: 2;
    max-width: 500px;
    margin: 3rem auto 0;
    text-align: center;
}

.premium-hero-image img {
    max-width: 100%;
    height: auto;
    filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.2));
    animation: float 6s ease-in-out infinite;
}

/* Overview Section */
.premium-overview {
    padding: 5rem 0;
    background-color: white;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: var(--dark-color);
}

.section-description {
    font-size: 1.1rem;
    color: var(--gray-700);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.feature-list {
    list-style: none;
    padding: 0;
    margin: 2rem 0;
}

.feature-list li {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.feature-list li i {
    color: var(--primary-color);
    margin-right: 1rem;
    font-size: 1.25rem;
}

.overview-image {
    text-align: center;
}

.overview-image img {
    max-width: 100%;
    height: auto;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-lg);
}

/* Features Section */
.premium-features {
    padding: 5rem 0;
    background-color: var(--gray-100);
}

.feature-card {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--box-shadow);
    height: 100%;
    transition: var(--transition);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    color: white;
    font-size: 1.75rem;
    box-shadow: 0 5px 15px rgba(0, 120, 212, 0.3);
}

.feature-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.feature-card p {
    color: var(--gray-700);
    margin-bottom: 0;
    line-height: 1.6;
}

/* Support Tiers Section */
.premium-support-tiers {
    padding: 5rem 0;
    background-color: white;
}

.tier-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 2rem;
    margin-bottom: 2rem;
    position: relative;
    transition: var(--transition);
    height: 100%;
    border: 1px solid var(--gray-300);
}

.tier-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.tier-card.featured {
    border: 2px solid var(--primary-color);
    transform: scale(1.05);
    z-index: 2;
}

.tier-card.featured:hover {
    transform: scale(1.05) translateY(-5px);
}

.tier-badge {
    position: absolute;
    top: -12px;
    right: 20px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 0.25rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    box-shadow: 0 4px 10px rgba(0, 120, 212, 0.3);
}

.tier-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--gray-300);
}

.tier-header h3 {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.tier-price {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.tier-price span {
    font-size: 1rem;
    font-weight: 400;
    color: var(--gray-600);
}

.tier-features {
    list-style: none;
    padding: 0;
    margin: 0 0 2rem 0;
}

.tier-features li {
    display: flex;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--gray-200);
}

.tier-features li:last-child {
    border-bottom: none;
}

.tier-features li i {
    width: 20px;
    margin-right: 1rem;
    text-align: center;
}

.tier-features li i.fa-check {
    color: var(--primary-color);
}

.tier-features li i.fa-times {
    color: var(--gray-500);
}

.tier-cta {
    text-align: center;
}

.tier-cta .btn {
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    width: 100%;
}

/* CTA Section */
.premium-cta {
    padding: 5rem 0;
    background: linear-gradient(135deg, var(--primary-color), #005a9e);
    color: white;
    position: relative;
    overflow: hidden;
}

.premium-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('../img/patterns/dots-pattern.svg');
    background-size: 30px;
    opacity: 0.1;
}

.cta-content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
    padding: 0 1rem;
}

.cta-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.cta-content p {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

/* Feature-specific styling */
.bulk-hero {
    background: linear-gradient(135deg, #3a7bd5, #00d2ff);
}

.analytics-hero {
    background: linear-gradient(135deg, #614385, #516395);
}

.encrypted-hero {
    background: linear-gradient(135deg, #1e3c72, #2a5298);
}

.hosting-hero {
    background: linear-gradient(135deg, #2c3e50, #4ca1af);
}

.support-hero {
    background: linear-gradient(135deg, #11998e, #38ef7d);
}

/* Animations */
@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0px);
    }
}

/* Responsive Styles */
@media (max-width: 991.98px) {
    .premium-title {
        font-size: 2.5rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .premium-hero {
        padding: 4rem 0;
    }
    
    .premium-overview,
    .premium-features,
    .premium-support-tiers,
    .premium-cta {
        padding: 4rem 0;
    }
    
    .tier-card.featured {
        transform: scale(1);
    }
    
    .tier-card.featured:hover {
        transform: translateY(-5px);
    }
}

@media (max-width: 767.98px) {
    .premium-title {
        font-size: 2rem;
    }
    
    .premium-subtitle {
        font-size: 1.1rem;
    }
    
    .section-title {
        font-size: 1.75rem;
    }
    
    .hero-cta {
        flex-direction: column;
    }
    
    .hero-cta .btn {
        width: 100%;
    }
    
    .cta-buttons {
        flex-direction: column;
    }
    
    .cta-buttons .btn {
        width: 100%;
    }
}
