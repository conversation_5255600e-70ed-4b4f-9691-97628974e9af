from django.contrib.auth.models import User

# Check if user already exists
if User.objects.filter(username='peter').exists():
    user = User.objects.get(username='peter')
    user.set_password('2587')
    user.is_staff = True
    user.is_superuser = True
    user.email = '<EMAIL>'
    user.save()
    print("Superuser 'peter' updated successfully!")
else:
    # Create superuser
    User.objects.create_superuser(
        username='peter',
        email='<EMAIL>',
        password='2587'
    )
    print("Superuser 'peter' created successfully!")
