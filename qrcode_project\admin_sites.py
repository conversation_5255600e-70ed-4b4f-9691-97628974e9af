"""
Custom Admin Sites for Different Modules
"""
from django.contrib import admin
from django.contrib.admin import AdminSite
from django.urls import path
from django.shortcuts import render
from django.contrib.admin.views.decorators import staff_member_required

# QR Code Admin Site
class QRCodeAdminSite(AdminSite):
    site_header = "QR Code Management"
    site_title = "QR Code Admin"
    index_title = "QR Code Administration"

    def get_app_list(self, request, app_label=None):
        """
        Return a sorted list of all the installed apps that have been
        registered in this site.
        """
        app_dict = self._build_app_dict(request, app_label)

        # Filter to only show QR-related models
        qr_models = [
            'QRCode', 'QRCodeScan', 'QRCodeAnalytics', 'QRCodeBatch',
            'QRCodeBranding', 'QRLink', 'QRGeoTarget', 'QRScanLog',
            'DynamicQRRedirect', 'UserProfile', 'APIKey'
        ]

        filtered_app_dict = {}
        for app_name, app_data in app_dict.items():
            if app_name == 'qrcode_app':
                filtered_models = []
                for model in app_data['models']:
                    if any(qr_model.lower() in model['object_name'].lower() for qr_model in qr_models):
                        filtered_models.append(model)
                if filtered_models:
                    app_data['models'] = filtered_models
                    filtered_app_dict[app_name] = app_data

        return [filtered_app_dict[app_name] for app_name in sorted(filtered_app_dict.keys())]

# Ads Admin Site
class AdsAdminSite(AdminSite):
    site_header = "Advertising Management"
    site_title = "Ads Admin"
    index_title = "Advertising Administration"

    def get_app_list(self, request, app_label=None):
        """
        Return a sorted list of all the installed apps that have been
        registered in this site.
        """
        app_dict = self._build_app_dict(request, app_label)

        # Filter to only show Ads-related models
        filtered_app_dict = {}
        for app_name, app_data in app_dict.items():
            if app_name == 'ads':
                filtered_app_dict[app_name] = app_data

        return [filtered_app_dict[app_name] for app_name in sorted(filtered_app_dict.keys())]

# AI Services Admin Site
class AIAdminSite(AdminSite):
    site_header = "AI Services Management"
    site_title = "AI Admin"
    index_title = "AI Services Administration"

# Billing & Subscriptions Admin Site
class BillingAdminSite(AdminSite):
    site_header = "Billing & Subscriptions"
    site_title = "Billing Admin"
    index_title = "Billing Administration"

    def get_app_list(self, request, app_label=None):
        """
        Return a sorted list of all the installed apps that have been
        registered in this site.
        """
        app_dict = self._build_app_dict(request, app_label)

        # Filter to only show Billing-related models
        billing_models = ['Plan', 'Subscription', 'StripeProduct']

        filtered_app_dict = {}
        for app_name, app_data in app_dict.items():
            if app_name == 'qrcode_app':
                filtered_models = []
                for model in app_data['models']:
                    if any(billing_model.lower() in model['object_name'].lower() for billing_model in billing_models):
                        filtered_models.append(model)
                if filtered_models:
                    app_data['models'] = filtered_models
                    filtered_app_dict[app_name] = app_data

        return [filtered_app_dict[app_name] for app_name in sorted(filtered_app_dict.keys())]

# Create instances
qr_admin_site = QRCodeAdminSite(name='qr_admin')
ads_admin_site = AdsAdminSite(name='ads_admin')
ai_admin_site = AIAdminSite(name='ai_admin')
billing_admin_site = BillingAdminSite(name='billing_admin')
