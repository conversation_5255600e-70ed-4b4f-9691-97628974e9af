/**
 * Enterprise QR Code Generator API
 * 
 * This is a RESTful API for the Enterprise QR Code Generator,
 * allowing programmatic generation and management of QR codes.
 */

const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const QRCode = require('qrcode');
const { v4: uuidv4 } = require('uuid');
const fs = require('fs');
const path = require('path');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const jwt = require('jsonwebtoken');

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 3000;

// Security middleware
app.use(helmet());
app.use(cors());

// Rate limiting
const apiLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: {
        status: 429,
        error: 'Too many requests, please try again later.'
    }
});

// Apply rate limiting to API routes
app.use('/api/', apiLimiter);

// Logging middleware
app.use(morgan('combined'));

// Parse JSON bodies
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));

// JWT Secret
const JWT_SECRET = process.env.JWT_SECRET || 'enterprise-qr-secret-key';

// In-memory storage (replace with database in production)
const users = [];
const apiKeys = [];
const qrCodes = [];

// Middleware to authenticate API key
const authenticateApiKey = (req, res, next) => {
    const apiKey = req.headers['x-api-key'];
    
    if (!apiKey) {
        return res.status(401).json({
            status: 401,
            error: 'API key is required'
        });
    }
    
    const validApiKey = apiKeys.find(key => key.value === apiKey);
    
    if (!validApiKey) {
        return res.status(401).json({
            status: 401,
            error: 'Invalid API key'
        });
    }
    
    // Add user info to request
    req.user = {
        id: validApiKey.userId,
        role: validApiKey.role
    };
    
    next();
};

// Middleware to authenticate JWT token
const authenticateToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    
    if (!token) {
        return res.status(401).json({
            status: 401,
            error: 'Authentication token is required'
        });
    }
    
    jwt.verify(token, JWT_SECRET, (err, user) => {
        if (err) {
            return res.status(403).json({
                status: 403,
                error: 'Invalid or expired token'
            });
        }
        
        req.user = user;
        next();
    });
};

// API Routes

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.status(200).json({
        status: 200,
        message: 'API is running',
        version: '1.0.0'
    });
});

// Authentication endpoints
app.post('/api/auth/register', (req, res) => {
    const { name, email, password } = req.body;
    
    if (!name || !email || !password) {
        return res.status(400).json({
            status: 400,
            error: 'Name, email, and password are required'
        });
    }
    
    // Check if user already exists
    if (users.find(user => user.email === email)) {
        return res.status(409).json({
            status: 409,
            error: 'User with this email already exists'
        });
    }
    
    // Create new user
    const newUser = {
        id: uuidv4(),
        name,
        email,
        password, // In production, hash the password
        role: 'user',
        createdAt: new Date().toISOString()
    };
    
    users.push(newUser);
    
    // Generate API key
    const apiKey = {
        id: uuidv4(),
        userId: newUser.id,
        value: uuidv4(),
        role: newUser.role,
        createdAt: new Date().toISOString()
    };
    
    apiKeys.push(apiKey);
    
    // Generate JWT token
    const token = jwt.sign(
        { id: newUser.id, email: newUser.email, role: newUser.role },
        JWT_SECRET,
        { expiresIn: '24h' }
    );
    
    res.status(201).json({
        status: 201,
        message: 'User registered successfully',
        data: {
            user: {
                id: newUser.id,
                name: newUser.name,
                email: newUser.email,
                role: newUser.role
            },
            apiKey: apiKey.value,
            token
        }
    });
});

app.post('/api/auth/login', (req, res) => {
    const { email, password } = req.body;
    
    if (!email || !password) {
        return res.status(400).json({
            status: 400,
            error: 'Email and password are required'
        });
    }
    
    // Find user
    const user = users.find(user => user.email === email && user.password === password);
    
    if (!user) {
        return res.status(401).json({
            status: 401,
            error: 'Invalid email or password'
        });
    }
    
    // Find or generate API key
    let apiKey = apiKeys.find(key => key.userId === user.id);
    
    if (!apiKey) {
        apiKey = {
            id: uuidv4(),
            userId: user.id,
            value: uuidv4(),
            role: user.role,
            createdAt: new Date().toISOString()
        };
        
        apiKeys.push(apiKey);
    }
    
    // Generate JWT token
    const token = jwt.sign(
        { id: user.id, email: user.email, role: user.role },
        JWT_SECRET,
        { expiresIn: '24h' }
    );
    
    res.status(200).json({
        status: 200,
        message: 'Login successful',
        data: {
            user: {
                id: user.id,
                name: user.name,
                email: user.email,
                role: user.role
            },
            apiKey: apiKey.value,
            token
        }
    });
});

// QR Code endpoints
app.post('/api/qrcodes', authenticateApiKey, async (req, res) => {
    try {
        const { data, options } = req.body;
        
        if (!data) {
            return res.status(400).json({
                status: 400,
                error: 'QR code data is required'
            });
        }
        
        // Generate QR code
        const qrOptions = {
            errorCorrectionLevel: options?.errorCorrectionLevel || 'H',
            type: 'image/png',
            margin: options?.margin || 4,
            color: {
                dark: options?.darkColor || '#000000',
                light: options?.lightColor || '#FFFFFF'
            },
            width: options?.width || 300
        };
        
        // Generate QR code as data URL
        const qrDataUrl = await QRCode.toDataURL(data, qrOptions);
        
        // Save QR code to storage
        const qrId = uuidv4();
        const fileName = `${qrId}.png`;
        const filePath = path.join(__dirname, 'public', 'qrcodes', fileName);
        
        // Ensure directory exists
        if (!fs.existsSync(path.join(__dirname, 'public', 'qrcodes'))) {
            fs.mkdirSync(path.join(__dirname, 'public', 'qrcodes'), { recursive: true });
        }
        
        // Convert data URL to buffer and save
        const qrBuffer = Buffer.from(qrDataUrl.split(',')[1], 'base64');
        fs.writeFileSync(filePath, qrBuffer);
        
        // Save QR code metadata
        const qrCode = {
            id: qrId,
            userId: req.user.id,
            data,
            options: qrOptions,
            fileName,
            url: `/qrcodes/${fileName}`,
            createdAt: new Date().toISOString()
        };
        
        qrCodes.push(qrCode);
        
        res.status(201).json({
            status: 201,
            message: 'QR code generated successfully',
            data: {
                id: qrCode.id,
                url: qrCode.url,
                dataUrl: qrDataUrl
            }
        });
    } catch (error) {
        console.error('Error generating QR code:', error);
        res.status(500).json({
            status: 500,
            error: 'Failed to generate QR code'
        });
    }
});

app.get('/api/qrcodes', authenticateApiKey, (req, res) => {
    // Get user's QR codes
    const userQrCodes = qrCodes.filter(qr => qr.userId === req.user.id);
    
    res.status(200).json({
        status: 200,
        message: 'QR codes retrieved successfully',
        data: userQrCodes.map(qr => ({
            id: qr.id,
            data: qr.data,
            url: qr.url,
            createdAt: qr.createdAt
        }))
    });
});

app.get('/api/qrcodes/:id', authenticateApiKey, (req, res) => {
    const { id } = req.params;
    
    // Find QR code
    const qrCode = qrCodes.find(qr => qr.id === id);
    
    if (!qrCode) {
        return res.status(404).json({
            status: 404,
            error: 'QR code not found'
        });
    }
    
    // Check if user owns the QR code
    if (qrCode.userId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({
            status: 403,
            error: 'You do not have permission to access this QR code'
        });
    }
    
    res.status(200).json({
        status: 200,
        message: 'QR code retrieved successfully',
        data: {
            id: qrCode.id,
            data: qrCode.data,
            options: qrCode.options,
            url: qrCode.url,
            createdAt: qrCode.createdAt
        }
    });
});

app.delete('/api/qrcodes/:id', authenticateApiKey, (req, res) => {
    const { id } = req.params;
    
    // Find QR code
    const qrCodeIndex = qrCodes.findIndex(qr => qr.id === id);
    
    if (qrCodeIndex === -1) {
        return res.status(404).json({
            status: 404,
            error: 'QR code not found'
        });
    }
    
    const qrCode = qrCodes[qrCodeIndex];
    
    // Check if user owns the QR code
    if (qrCode.userId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({
            status: 403,
            error: 'You do not have permission to delete this QR code'
        });
    }
    
    // Delete QR code file
    const filePath = path.join(__dirname, 'public', qrCode.url);
    if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
    }
    
    // Remove QR code from storage
    qrCodes.splice(qrCodeIndex, 1);
    
    res.status(200).json({
        status: 200,
        message: 'QR code deleted successfully'
    });
});

// Start server
app.listen(PORT, () => {
    console.log(`Enterprise QR API server running on port ${PORT}`);
});
