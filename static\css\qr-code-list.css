/* Enterprise QR Code List Styling */

/* Main container styling */
.qr-list-container {
    padding: 3rem 0;
}

/* Page title styling */
.qr-list-title {
    position: relative;
    margin-bottom: 2rem;
    padding-bottom: 1.2rem;
    font-weight: 700;
    color: #2c3e50;
    font-size: 2rem;
    letter-spacing: -0.02em;
}

.qr-list-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, #4a6cf7, #6366f1);
    border-radius: 4px;
}

.qr-list-title i {
    background: linear-gradient(135deg, #4a6cf7, #6366f1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-right: 0.5rem;
}

/* Search bar styling */
.qr-search-container {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.04);
    padding: 2rem;
    margin-bottom: 3rem;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    border: 1px solid rgba(233, 236, 239, 0.8);
    position: relative;
    overflow: hidden;
}

.qr-search-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(74, 108, 247, 0.03), rgba(99, 102, 241, 0.02));
    z-index: 0;
}

.qr-search-container::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(74, 108, 247, 0.05) 0%, rgba(255, 255, 255, 0) 70%);
    border-radius: 50%;
    z-index: 0;
}

.qr-search-container:hover {
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.06);
    transform: translateY(-2px);
}

.qr-search-container form {
    position: relative;
    z-index: 1;
}

.qr-search-input {
    border: 1px solid rgba(233, 236, 239, 0.8);
    border-radius: 12px;
    padding: 1rem 1.2rem;
    transition: all 0.3s ease;
    font-size: 1rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.02);
    background-color: rgba(255, 255, 255, 0.8);
}

.qr-search-input:focus {
    border-color: #4a6cf7;
    box-shadow: 0 0 0 0.25rem rgba(74, 108, 247, 0.15);
    background-color: white;
}

.qr-search-btn {
    background: linear-gradient(135deg, #4a6cf7, #6366f1);
    border: none;
    border-radius: 12px;
    color: white;
    padding: 1rem 1.5rem;
    font-weight: 600;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    box-shadow: 0 4px 15px rgba(74, 108, 247, 0.2);
    letter-spacing: 0.01em;
}

.qr-search-btn:hover {
    background: linear-gradient(135deg, #3a5bd6, #5254d0);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(74, 108, 247, 0.3);
}

/* QR Code Card Styling */
.qr-card-container {
    margin-bottom: 2rem;
}

/* Card animations */
@keyframes cardFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.qr-card {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.04);
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    height: 100%;
    display: flex;
    flex-direction: column;
    animation: cardFadeIn 0.5s ease forwards;
    opacity: 0;
    border: 1px solid rgba(233, 236, 239, 0.8);
    position: relative;
}

/* Elegant card highlight effect */
.qr-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #4a6cf7, #6366f1);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.qr-card:hover::after {
    transform: scaleX(1);
}

/* Staggered animation for cards */
.col-lg-4:nth-child(1) .qr-card { animation-delay: 0.1s; }
.col-lg-4:nth-child(2) .qr-card { animation-delay: 0.2s; }
.col-lg-4:nth-child(3) .qr-card { animation-delay: 0.3s; }
.col-lg-4:nth-child(4) .qr-card { animation-delay: 0.4s; }
.col-lg-4:nth-child(5) .qr-card { animation-delay: 0.5s; }

.qr-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(74, 108, 247, 0.08);
}

.qr-card-header {
    padding: 1.8rem 2rem;
    border-bottom: 1px solid rgba(241, 243, 245, 0.5);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: linear-gradient(to right, rgba(250, 251, 255, 0.5), rgba(255, 255, 255, 0.8));
}

.qr-card-title {
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
    font-size: 1.15rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 70%;
    letter-spacing: -0.01em;
    position: relative;
}

.qr-card-title::after {
    content: '';
    position: absolute;
    bottom: -6px;
    left: 0;
    width: 30px;
    height: 2px;
    background: linear-gradient(90deg, #4a6cf7, transparent);
    border-radius: 2px;
}

.qr-card-type {
    font-size: 0.75rem;
    padding: 0.4rem 0.9rem;
    border-radius: 50px;
    background: linear-gradient(135deg, rgba(74, 108, 247, 0.08), rgba(99, 102, 241, 0.12));
    color: #4a6cf7;
    font-weight: 600;
    letter-spacing: 0.02em;
    box-shadow: 0 2px 5px rgba(74, 108, 247, 0.1);
    backdrop-filter: blur(5px);
    border: 1px solid rgba(74, 108, 247, 0.1);
}

.qr-card-body {
    padding: 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    flex-grow: 1;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 1), rgba(250, 251, 255, 0.5));
}

.qr-card-image {
    width: 180px;
    height: 180px;
    object-fit: contain;
    margin-bottom: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.06);
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    border: 1px solid rgba(241, 243, 245, 0.8);
    background: white;
    padding: 10px;
}

.qr-card:hover .qr-card-image {
    transform: scale(1.05) translateY(-5px);
    box-shadow: 0 15px 30px rgba(74, 108, 247, 0.1);
}

.qr-card-date {
    color: #6c757d;
    font-size: 0.85rem;
    margin-bottom: 1.5rem;
    padding: 0.5rem 1rem;
    background: rgba(248, 249, 250, 0.7);
    border-radius: 30px;
    display: inline-flex;
    align-items: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
    border: 1px solid rgba(233, 236, 239, 0.5);
}

.qr-card-date i {
    color: #4a6cf7;
    margin-right: 6px;
}

.qr-card-footer {
    padding: 1.5rem 2rem;
    background: linear-gradient(to bottom, rgba(248, 249, 250, 0.5), rgba(241, 243, 245, 0.3));
    border-top: 1px solid rgba(241, 243, 245, 0.5);
    border-bottom-left-radius: 16px;
    border-bottom-right-radius: 16px;
}

.qr-card-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px; /* Consistent spacing between buttons */
    width: 100%;
}

.qr-action-btn {
    padding: 0.6rem 0.5rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-align: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.qr-action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.qr-action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.qr-action-btn:hover::before {
    transform: translateY(0);
}

.qr-action-btn i {
    margin-right: 4px;
    font-size: 0.85rem;
}

.qr-view-btn {
    background-color: #4a6cf7;
    color: white;
}

.qr-view-btn:hover {
    background-color: #3a5bd6;
    box-shadow: 0 4px 8px rgba(74, 108, 247, 0.2);
}

.qr-download-btn {
    background-color: #6c757d;
    color: white;
}

.qr-download-btn:hover {
    background-color: #5a6268;
    box-shadow: 0 4px 8px rgba(108, 117, 125, 0.2);
}

.qr-delete-btn {
    background-color: #dc3545;
    color: white;
}

.qr-delete-btn:hover {
    background-color: #c82333;
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.2);
}

.qr-share-btn {
    background-color: #25D366;
    color: white;
}

.qr-share-btn:hover {
    background-color: #128C7E;
    box-shadow: 0 4px 8px rgba(37, 211, 102, 0.2);
}

/* Empty state styling */
.qr-empty-state {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.04);
    padding: 4rem 3rem;
    text-align: center;
    margin-bottom: 3rem;
    border: 1px solid rgba(233, 236, 239, 0.8);
    position: relative;
    overflow: hidden;
}

.qr-empty-state::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(74, 108, 247, 0.02), rgba(99, 102, 241, 0.01));
    z-index: 0;
}

.qr-empty-icon {
    font-size: 5rem;
    margin-bottom: 2rem;
    position: relative;
    z-index: 1;
    display: inline-block;
}

.qr-empty-icon i {
    background: linear-gradient(135deg, #e9ecef, #ced4da);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    filter: drop-shadow(0 5px 15px rgba(0, 0, 0, 0.05));
}

.qr-empty-title {
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1.2rem;
    font-size: 1.8rem;
    position: relative;
    z-index: 1;
}

.qr-empty-text {
    color: #6c757d;
    margin-bottom: 2rem;
    font-size: 1.1rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
    position: relative;
    z-index: 1;
    line-height: 1.6;
}

/* Pagination styling */
.qr-pagination {
    margin-top: 3rem;
    margin-bottom: 2rem;
    display: flex;
    justify-content: center;
    animation: fadeInUp 0.6s ease forwards;
    animation-delay: 0.6s;
    opacity: 0;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.qr-pagination .pagination {
    background: white;
    padding: 0.8rem;
    border-radius: 50px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(233, 236, 239, 0.8);
    display: inline-flex;
}

.qr-pagination .page-link {
    border: none;
    color: #495057;
    margin: 0 0.3rem;
    border-radius: 50px;
    padding: 0.6rem 1rem;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    position: relative;
    overflow: hidden;
    font-weight: 500;
    min-width: 40px;
    text-align: center;
}

.qr-pagination .page-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(74, 108, 247, 0.1), rgba(99, 102, 241, 0.1));
    transform: translateY(100%);
    transition: transform 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    z-index: -1;
}

.qr-pagination .page-link:hover {
    color: #4a6cf7;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(74, 108, 247, 0.1);
}

.qr-pagination .page-link:hover::before {
    transform: translateY(0);
}

.qr-pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #4a6cf7, #6366f1);
    color: white;
    box-shadow: 0 8px 20px rgba(74, 108, 247, 0.2);
    transform: translateY(-3px);
}

.qr-pagination .page-item.disabled .page-link {
    color: #ced4da;
    pointer-events: none;
    background-color: #f8f9fa;
}

/* Responsive adjustments */
@media (max-width: 1199.98px) {
    .qr-card-footer {
        padding: 1.2rem;
    }

    .qr-action-btn {
        font-size: 0.85rem;
        padding: 0.6rem 0.4rem;
    }

    .qr-action-btn i {
        margin-right: 3px;
        font-size: 0.8rem;
    }
}

@media (max-width: 991.98px) {
    .qr-list-container {
        padding: 2rem 0;
    }

    .qr-list-title {
        font-size: 1.8rem;
    }

    .qr-search-container {
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .qr-card {
        margin-bottom: 1rem;
    }

    .qr-card-header {
        padding: 1.5rem;
    }

    .qr-card-body {
        padding: 1.5rem;
    }

    .qr-card-footer {
        padding: 1rem;
    }

    /* Hide button text on medium screens, show only icons */
    .qr-action-btn span {
        display: none;
    }

    .qr-action-btn i {
        margin-right: 0;
        font-size: 1rem;
    }
}

@media (max-width: 767.98px) {
    .qr-card-actions {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
    }

    .qr-action-btn {
        padding: 0.7rem 0.5rem;
    }

    /* Restore button text for smaller screens */
    .qr-action-btn span {
        display: inline;
    }

    .qr-action-btn i {
        margin-right: 4px;
    }
}

@media (max-width: 575.98px) {
    .qr-card-actions {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
    }

    .qr-action-btn {
        padding: 0.7rem 0.5rem;
        font-size: 0.85rem;
    }
}

    .qr-pagination .pagination {
        padding: 0.6rem;
        border-radius: 30px;
    }

    .qr-pagination .page-link {
        margin: 0 0.2rem;
        padding: 0.5rem 0.8rem;
        min-width: 35px;
    }

    .qr-empty-state {
        padding: 3rem 1.5rem;
    }
}

@media (max-width: 575.98px) {
    .qr-list-title {
        font-size: 1.6rem;
    }

    .qr-card-image {
        width: 150px;
        height: 150px;
    }

    .qr-search-btn {
        padding: 0.8rem 1rem;
    }

    .qr-search-input {
        padding: 0.8rem 1rem;
    }

    .qr-empty-title {
        font-size: 1.5rem;
    }

    .qr-empty-text {
        font-size: 1rem;
    }
}
