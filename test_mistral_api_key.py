"""
Test script for the Mistral API with the new API key
"""
import requests
import json
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# Mistral API settings
MISTRAL_API_KEY = "37OphnjoLD2IJN2kCZaFoHzgfcmJjltK"
MISTRAL_MODEL = "mistral-tiny"

def test_mistral_api():
    """Test the Mistral API with the new API key"""
    print(f"Testing Mistral API with key: {MISTRAL_API_KEY[:4]}...{MISTRAL_API_KEY[-4:]}")
    print(f"Using model: {MISTRAL_MODEL}")
    
    # First, check if the model is available
    try:
        # Make a request to the models endpoint
        api_url = "https://api.mistral.ai/v1/models"
        headers = {
            "Authorization": f"Bearer {MISTRAL_API_KEY}",
            "Content-Type": "application/json"
        }
        
        print(f"Making API request to: {api_url}")
        response = requests.get(api_url, headers=headers, timeout=5)
        
        print(f"Response status code: {response.status_code}")
        
        if response.status_code == 200:
            models_data = response.json()
            
            # Check if our model is in the list of available models
            available_models = []
            if 'data' in models_data and isinstance(models_data['data'], list):
                available_models = [model_data.get('id') for model_data in models_data.get('data', []) if model_data.get('id')]
            
            is_available = MISTRAL_MODEL in available_models
            
            print(f"Model {MISTRAL_MODEL} {'is' if is_available else 'is not'} available")
            print(f"Available models: {', '.join(available_models[:5])}{'...' if len(available_models) > 5 else ''}")
            
            if is_available:
                print("\nMistral API is ONLINE and the model is available")
            else:
                print("\nMistral API is ONLINE but the model is not available")
        else:
            print(f"\nMistral API is OFFLINE or not responding correctly")
            print(f"Response content: {response.text}")
            return False
    except Exception as e:
        print(f"Error checking model availability: {str(e)}")
        return False
    
    # Now, test generating a chat completion
    try:
        # Make a request to the chat completions endpoint
        api_url = "https://api.mistral.ai/v1/chat/completions"
        headers = {
            "Authorization": f"Bearer {MISTRAL_API_KEY}",
            "Content-Type": "application/json"
        }
        
        # Prepare the request payload
        payload = {
            "model": MISTRAL_MODEL,
            "messages": [
                {
                    "role": "user",
                    "content": "Generate a short advertisement for a mobile app targeting young professionals."
                }
            ],
            "temperature": 0.7,
            "max_tokens": 150
        }
        
        print(f"\nMaking API request to: {api_url}")
        response = requests.post(api_url, headers=headers, json=payload, timeout=10)
        
        print(f"Response status code: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            
            # Extract the content from the response
            message_content = response_data.get('choices', [{}])[0].get('message', {}).get('content', '')
            
            if message_content:
                print(f"\nGenerated content:\n{message_content}")
                return True
            else:
                print("No content returned")
                return False
        else:
            print(f"\nAPI request failed with status code: {response.status_code}")
            print(f"Response content: {response.text}")
            return False
    except Exception as e:
        print(f"Error generating chat completion: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_mistral_api()
    if success:
        print("\nTest completed successfully!")
    else:
        print("\nTest failed!")
        import sys
        sys.exit(1)
