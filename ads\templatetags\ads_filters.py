from django import template
from django.template.defaultfilters import stringfilter
from django.utils import timezone
from django.db.models import Q
from ads.models import Ad, AdLocation

register = template.Library()

@register.filter
def map(value, arg):
    """
    Maps a list of dictionaries to a list of values for a specific key

    Usage: {{ my_list|map:"key_name" }}
    """
    return [item.get(arg, '') for item in value]

@register.filter
def date(value, arg):
    """
    Format a date according to the given format

    Usage: {{ my_list|map:"date"|date:"M d" }}
    """
    if hasattr(value, 'strftime'):
        return value.strftime(arg)
    return value

@register.filter
def calculate_ctr(clicks, impressions):
    """
    Calculate CTR (Click-Through Rate) from clicks and impressions

    Usage: {{ clicks|calculate_ctr:impressions }}
    """
    if impressions and impressions > 0:
        ctr = (clicks / impressions) * 100
        return f"{ctr:.2f}%"
    return "0.00%"

@register.inclusion_tag('ads/includes/ad_display.html')
def display_ads(location, max_ads=1, rotation='random', display_style=None, show_placeholder=False, show_carousel=False):
    """
    Template tag to display ads in a specific location.

    Usage:
    {% load ads_filters %}
    {% display_ads location='header' max_ads=1 rotation='random' display_style='carousel' %}
    """
    return _get_ads_context(location, max_ads, rotation, display_style, show_placeholder, show_carousel)

@register.inclusion_tag('ads/includes/premium_header_ad.html')
def display_premium_header_ad(max_ads=10, rotation='random', show_placeholder=False):
    """
    Template tag specifically for displaying ads in the Premium Header location
    with a sleek, corporate-grade carousel design.

    Usage:
    {% load ads_filters %}
    {% display_premium_header_ad %}

    Parameters:
    - max_ads: Maximum number of ads to display in the carousel (default: 10)
    - rotation: Rotation strategy ('random', 'newest', 'oldest', 'highest_ctr') (default: 'random')
    - show_placeholder: Whether to show a placeholder if no ads are available (default: False)
    """
    return _get_ads_context('Premium Header', max_ads, rotation, None, show_placeholder, True)

@register.inclusion_tag('ads/includes/content_inline_ad.html')
def display_content_inline_ad(max_ads=1, rotation='random', show_placeholder=False):
    """
    Template tag specifically for displaying ads in the Content Inline location
    with medium visibility.

    Usage:
    {% load ads_filters %}
    {% display_content_inline_ad %}

    Parameters:
    - max_ads: Maximum number of ads to display (default: 1)
    - rotation: Rotation strategy ('random', 'newest', 'oldest', 'highest_ctr') (default: 'random')
    - show_placeholder: Whether to show a placeholder if no ads are available (default: False)
    """
    return _get_ads_context('Content Inline', max_ads, rotation, None, show_placeholder, False)

@register.inclusion_tag('ads/includes/sidebar_ad.html')
def display_sidebar_ads(max_ads=5, rotation='random', show_placeholder=False):
    """
    Template tag for displaying sidebar ads (Sidebar Top and Sidebar Middle).

    Usage:
    {% load ads_filters %}
    {% display_sidebar_ads %}
    """
    # Get ads from both Sidebar Top and Sidebar Middle locations
    now = timezone.now()

    sidebar_locations = AdLocation.objects.filter(
        name__icontains='sidebar'
    )

    ads = Ad.objects.filter(
        ad_location__in=sidebar_locations,
        status__in=['active', 'approved'],
        start_date__lte=now,
        end_date__gte=now
    )

    if rotation == 'newest':
        ads = ads.order_by('-created_at')
    elif rotation == 'oldest':
        ads = ads.order_by('created_at')
    elif rotation == 'highest_ctr':
        ads = ads.order_by('-clicks', '-impressions')
    else:  # random
        ads = ads.order_by('?')

    ads = ads[:max_ads]

    return {
        'ads': ads,
        'location': 'sidebar',
        'max_ads': max_ads,
        'rotation': rotation,
        'show_placeholder': show_placeholder,
        'show_carousel': False,
    }

@register.inclusion_tag('ads/includes/header_ad.html')
def display_header_ads(max_ads=5, rotation='random', show_placeholder=False):
    """
    Template tag for displaying header ads (regular header location).

    Usage:
    {% load ads_filters %}
    {% display_header_ads %}
    """
    return _get_ads_context('header', max_ads, rotation, None, show_placeholder, True)

@register.inclusion_tag('ads/includes/footer_ad.html')
def display_footer_ads(max_ads=50, rotation='random', show_placeholder=False):
    """
    Template tag for displaying footer ads.
    Now supports up to 50 ads with enhanced scrolling.

    Usage:
    {% load ads_filters %}
    {% display_footer_ads %}
    {% display_footer_ads max_ads=30 %}
    """
    return _get_ads_context('footer', max_ads, rotation, None, show_placeholder, False)

@register.inclusion_tag('ads/includes/homepage_featured_ad.html')
def display_homepage_featured_ad(max_ads=1, rotation='random', show_placeholder=False):
    """
    Template tag for displaying Homepage Featured ads.

    Usage:
    {% load ads_filters %}
    {% display_homepage_featured_ad %}
    """
    return _get_ads_context('Homepage Featured', max_ads, rotation, None, show_placeholder, False)

@register.inclusion_tag('ads/includes/ad_display.html')
def display_popup_ads(max_ads=1, rotation='random', show_placeholder=False):
    """
    Template tag for displaying popup ads.

    Usage:
    {% load ads_filters %}
    {% display_popup_ads %}
    """
    # Get popup ads from any location that has popup ad type
    # Search for "Pop-up" (with hyphen and capital) or "popup" (lowercase)
    now = timezone.now()

    ads = Ad.objects.filter(
        Q(ad_type__name__icontains='pop-up') | Q(ad_type__name__icontains='popup'),
        status__in=['active', 'approved'],
        start_date__lte=now,
        end_date__gte=now
    )

    if rotation == 'newest':
        ads = ads.order_by('-created_at')
    elif rotation == 'oldest':
        ads = ads.order_by('created_at')
    elif rotation == 'highest_ctr':
        ads = ads.order_by('-clicks', '-impressions')
    else:  # random
        ads = ads.order_by('?')

    ads = ads[:max_ads]

    return {
        'ads': ads,
        'location': 'popup',
        'max_ads': max_ads,
        'rotation': rotation,
        'show_placeholder': show_placeholder,
        'show_carousel': False,
        'display_style': 'popup',
    }



def _get_ads_context(location, max_ads=1, rotation='random', display_style=None, show_placeholder=True, show_carousel=False):
    """
    Helper function to get ads for a specific location.

    Parameters:
    - location: The location identifier (e.g., 'header', 'sidebar', 'footer', 'popup', 'content')
    - max_ads: Maximum number of ads to display (default: 1)
    - rotation: Rotation strategy ('random', 'newest', 'oldest', 'highest_ctr') (default: 'random')
    - display_style: Override the default display style for the location
    - show_placeholder: Whether to show a placeholder if no ads are available (default: True)
    - show_carousel: Whether to show a carousel for multiple ads (default: False)
    """
    # Get active ads for the specified location
    # Only get ads that are active and within their display period
    now = timezone.now()

    # Try to find location objects that match the specified location
    # First, try an exact match (case-insensitive)
    location_objs = AdLocation.objects.filter(
        name__iexact=location
    )

    # If no exact match, try a partial match
    if not location_objs.exists():
        location_objs = AdLocation.objects.filter(
            name__icontains=location
        )

    # Get ads for any of these locations
    # Include both active and approved ads
    ads = Ad.objects.filter(
        ad_location__in=location_objs,
        status__in=['active', 'approved'],
        start_date__lte=now,
        end_date__gte=now
    )

    # If no ads found, try a more general search
    if not ads.exists():
        # Try to find ads with similar location names
        location_objs = AdLocation.objects.all()
        for loc_obj in location_objs:
            if location.lower() in loc_obj.name.lower() or loc_obj.name.lower() in location.lower():
                # Add ads from this location
                ads = ads | Ad.objects.filter(
                    ad_location=loc_obj,
                    status__in=['active', 'approved'],
                    start_date__lte=now,
                    end_date__gte=now
                )

    # Apply rotation strategy
    if rotation == 'newest':
        ads = ads.order_by('-created_at')
    elif rotation == 'oldest':
        ads = ads.order_by('created_at')
    elif rotation == 'highest_ctr':
        # Calculate CTR for each ad and order by it
        ads = ads.order_by('-clicks', '-impressions')
    else:  # random
        ads = ads.order_by('?')

    # Limit to max_ads
    ads = ads[:max_ads]

    # Debug output (disabled for production)
    # print(f"Location: {location}, Found {len(ads)} ads")
    # for ad in ads:
    #     print(f"  - {ad.title} (location: {ad.ad_location.name})")

    # Return context for the template
    return {
        'ads': ads,
        'location': location,
        'max_ads': max_ads,
        'rotation': rotation,
        'display_style': display_style,
        'show_placeholder': show_placeholder,
        'show_carousel': show_carousel,
    }
