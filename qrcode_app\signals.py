from django.db.models.signals import post_save
from django.dispatch import receiver
from django.contrib.auth.models import User
from .models import UserProfile, APIKey
import uuid


@receiver(post_save, sender=User)
def create_user_profile(sender, instance, created, **kwargs):
    """
    Create a UserProfile when a new User is created.
    """
    if created:
        UserProfile.objects.create(user=instance)


@receiver(post_save, sender=APIKey)
def generate_api_key(sender, instance, created, **kwargs):
    """
    Generate a unique API key when a new APIKey is created.
    """
    if created and not instance.key:
        # Generate a unique API key
        instance.key = f"qr_{uuid.uuid4().hex}"
        instance.save(update_fields=['key'])
