/**
 * Smart Engine CSS
 * Styles for the AI-assisted ad creation feature
 * Consolidated and optimized for production
 */

/* Smart Engine Container */
#smartEngineOptions {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

/* Smart Engine Toggle Disabled State */
.smart-engine-disabled {
    opacity: 0.5;
    pointer-events: none;
    cursor: not-allowed;
}

.smart-engine-disabled .form-check-input {
    opacity: 0.5;
    cursor: not-allowed;
}

.smart-engine-disabled .form-check-label {
    opacity: 0.5;
    cursor: not-allowed;
    color: #6c757d !important;
}

.smart-engine-disabled .badge {
    opacity: 0.5;
}

.smart-engine-disabled .form-text {
    opacity: 0.5;
    color: #6c757d !important;
}

#smartEngineOptions:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

/* AI Suggestions Container */
#aiSuggestionsContainer {
    display: none;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px dashed #dee2e6;
    transition: all 0.3s ease;
}

#aiSuggestionsContainer h5 {
    margin-bottom: 15px;
    color: #1a237e;
    font-weight: 600;
}

/* AI Suggestion Cards */
.ai-suggestions {
    max-height: 400px;
    overflow-y: auto;
    padding-right: 5px;
}

.ai-suggestion-card {
    background-color: white;
    border: 1px solid #e9ecef !important;
    border-radius: 6px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.ai-suggestion-card:hover {
    border-color: #adb5bd !important;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
}

.ai-suggestion-title {
    font-weight: 600;
    color: #212529;
}

.ai-suggestion-content {
    color: #6c757d;
    white-space: pre-line;
}

/* Apply Suggestion Button */
.apply-suggestion-btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    transition: all 0.2s ease;
}

.apply-suggestion-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Generate Suggestions Button */
#generateSuggestions {
    background-color: #1a237e;
    border-color: #1a237e;
    transition: all 0.3s ease;
}

#generateSuggestions:hover {
    background-color: #0d1642;
    border-color: #0d1642;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

#generateSuggestions:focus {
    box-shadow: 0 0 0 0.25rem rgba(26, 35, 126, 0.25);
}

#generateSuggestions:disabled {
    opacity: 0.7;
    transform: none;
    box-shadow: none;
}

/* AI Customization Controls */
.ai-customization-controls {
    margin-top: 15px;
    padding: 12px;
    background-color: #f1f3f5;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.ai-customization-controls:hover {
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
}

.ai-customization-controls .form-label {
    font-size: 0.85rem;
    font-weight: 600;
    color: #1a237e;
    margin-bottom: 0.5rem;
}

/* Creativity Slider */
.creativity-slider-container {
    padding: 0 10px;
    margin-bottom: 10px;
}

#aiCreativity {
    width: 100%;
    height: 6px;
    -webkit-appearance: none;
    appearance: none;
    background: linear-gradient(to right, #74b9ff, #1a237e, #e84393);
    outline: none;
    border-radius: 5px;
}

#aiCreativity::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #1a237e;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

#aiCreativity::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #1a237e;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.creativity-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 5px;
}

.creativity-labels span {
    font-size: 0.7rem;
    color: #6c757d;
    font-weight: 500;
}

/* Smart Engine Reminder */
#smartEngineReminder {
    display: none;
    padding: 12px 15px;
    background-color: #fff3cd;
    border: 1px solid #ffeeba;
    border-radius: 8px;
    margin-bottom: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

#smartEngineReminder p {
    margin-bottom: 5px;
    color: #856404;
}

#goToSmartEngineBtn {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #212529;
    transition: all 0.2s ease;
}

#goToSmartEngineBtn:hover {
    background-color: #e0a800;
    border-color: #e0a800;
    transform: translateY(-1px);
}

/* Error Container */
#formErrorContainer {
    display: none;
    padding: 12px 15px;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 8px;
    margin-bottom: 15px;
    color: #721c24;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

/* Loading Animation */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.fa-spinner {
    animation: spin 1s linear infinite;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    #smartEngineOptions {
        padding: 10px;
    }

    .ai-suggestions {
        max-height: 300px;
    }

    .ai-customization-controls {
        padding: 10px;
    }
}
