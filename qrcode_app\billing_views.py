"""
MODULE 6: Stripe Billing Integration Views
"""

import stripe
import json
import logging
from django.conf import settings
from django.contrib.auth.decorators import login_required
from django.contrib.auth.models import User
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render, redirect, get_object_or_404
from django.urls import reverse
from django.utils import timezone
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from .models import Plan, Subscription, StripeProduct

# Configure Stripe
stripe.api_key = settings.STRIPE_SECRET_KEY
logger = logging.getLogger(__name__)


@login_required
def pricing_page(request):
    """
    Display pricing plans with Stripe integration
    """
    plans = Plan.objects.filter(is_active=True).order_by('sort_order')
    
    # Get user's current subscription
    current_subscription = None
    try:
        current_subscription = Subscription.objects.get(user=request.user)
    except Subscription.DoesNotExist:
        pass
    
    context = {
        'plans': plans,
        'current_subscription': current_subscription,
        'stripe_public_key': settings.STRIPE_PUBLIC_KEY,
    }
    
    return render(request, 'qrcode_app/billing/pricing.html', context)


@login_required
def create_checkout_session(request, plan_id):
    """
    Create Stripe checkout session for plan subscription
    """
    try:
        plan = get_object_or_404(Plan, id=plan_id, is_active=True)
        
        # Check if plan has Stripe integration
        try:
            stripe_product = StripeProduct.objects.get(plan=plan, is_active=True)
        except StripeProduct.DoesNotExist:
            messages.error(request, 'This plan is not available for online purchase. Please contact support.')
            return redirect('pricing')
        
        # Get billing cycle from request
        billing_cycle = request.GET.get('billing', 'monthly')
        price_id = stripe_product.get_price_id(billing_cycle)
        
        if not price_id:
            messages.error(request, f'{billing_cycle.title()} billing is not available for this plan.')
            return redirect('pricing')
        
        # Create Stripe checkout session
        try:
            checkout_session = stripe.checkout.Session.create(
                payment_method_types=['card'],
                mode='subscription',
                customer_email=request.user.email,
                line_items=[{
                    'price': price_id,
                    'quantity': 1,
                }],
                success_url=settings.DOMAIN_URL + reverse('billing_success') + '?session_id={CHECKOUT_SESSION_ID}',
                cancel_url=settings.DOMAIN_URL + reverse('billing_cancel'),
                metadata={
                    'user_id': str(request.user.id),
                    'plan_id': str(plan.id),
                    'billing_cycle': billing_cycle,
                },
                allow_promotion_codes=True,
                billing_address_collection='required',
                tax_id_collection={'enabled': True},
            )
            
            return redirect(checkout_session.url)
            
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error creating checkout session: {e}")
            messages.error(request, 'Unable to process payment. Please try again later.')
            return redirect('pricing')
            
    except Exception as e:
        logger.error(f"Error creating checkout session: {e}")
        messages.error(request, 'An error occurred. Please try again.')
        return redirect('pricing')


@login_required
def billing_success(request):
    """
    Handle successful billing
    """
    session_id = request.GET.get('session_id')
    
    if session_id:
        try:
            # Retrieve the session from Stripe
            session = stripe.checkout.Session.retrieve(session_id)
            
            # Get subscription details
            subscription_id = session.subscription
            customer_id = session.customer
            
            context = {
                'session': session,
                'subscription_id': subscription_id,
                'customer_id': customer_id,
            }
            
            messages.success(request, '🎉 Payment successful! Your subscription has been activated.')
            
        except stripe.error.StripeError as e:
            logger.error(f"Error retrieving Stripe session: {e}")
            messages.warning(request, 'Payment completed, but we encountered an issue retrieving details. Your subscription should be active shortly.')
            context = {}
    else:
        context = {}
        messages.success(request, '🎉 Payment successful! Your subscription has been activated.')
    
    return render(request, 'qrcode_app/billing/success.html', context)


@login_required
def billing_cancel(request):
    """
    Handle cancelled billing
    """
    messages.info(request, 'Payment was cancelled. You can try again anytime.')
    return render(request, 'qrcode_app/billing/cancel.html')


@login_required
def billing_portal(request):
    """
    Redirect to Stripe customer portal for subscription management
    """
    try:
        subscription = Subscription.objects.get(user=request.user)
        
        if not subscription.stripe_customer_id:
            messages.error(request, 'No billing information found. Please subscribe to a plan first.')
            return redirect('pricing')
        
        # Create portal session
        portal_session = stripe.billing_portal.Session.create(
            customer=subscription.stripe_customer_id,
            return_url=settings.DOMAIN_URL + reverse('pricing'),
        )
        
        return redirect(portal_session.url)
        
    except Subscription.DoesNotExist:
        messages.error(request, 'No subscription found. Please subscribe to a plan first.')
        return redirect('pricing')
    except stripe.error.StripeError as e:
        logger.error(f"Error creating portal session: {e}")
        messages.error(request, 'Unable to access billing portal. Please try again later.')
        return redirect('pricing')


@csrf_exempt
@require_http_methods(["POST"])
def stripe_webhook(request):
    """
    Handle Stripe webhooks for subscription updates
    """
    payload = request.body
    sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')
    
    if not sig_header:
        logger.warning("Missing Stripe signature header")
        return HttpResponse(status=400)
    
    try:
        event = stripe.Webhook.construct_event(
            payload, sig_header, settings.STRIPE_WEBHOOK_SECRET
        )
    except ValueError:
        logger.error("Invalid payload in Stripe webhook")
        return HttpResponse(status=400)
    except stripe.error.SignatureVerificationError:
        logger.error("Invalid signature in Stripe webhook")
        return HttpResponse(status=400)
    
    # Handle the event
    try:
        if event['type'] == 'checkout.session.completed':
            handle_checkout_completed(event['data']['object'])
        elif event['type'] == 'customer.subscription.updated':
            handle_subscription_updated(event['data']['object'])
        elif event['type'] == 'customer.subscription.deleted':
            handle_subscription_deleted(event['data']['object'])
        elif event['type'] == 'invoice.payment_succeeded':
            handle_payment_succeeded(event['data']['object'])
        elif event['type'] == 'invoice.payment_failed':
            handle_payment_failed(event['data']['object'])
        else:
            logger.info(f"Unhandled Stripe event type: {event['type']}")
    
    except Exception as e:
        logger.error(f"Error handling Stripe webhook: {e}")
        return HttpResponse(status=500)
    
    return HttpResponse(status=200)


def handle_checkout_completed(session):
    """Handle successful checkout completion"""
    try:
        user_id = session['metadata']['user_id']
        plan_id = session['metadata']['plan_id']
        billing_cycle = session['metadata'].get('billing_cycle', 'monthly')
        
        user = User.objects.get(id=user_id)
        plan = Plan.objects.get(id=plan_id)
        
        # Get subscription details from Stripe
        stripe_subscription = stripe.Subscription.retrieve(session['subscription'])
        
        # Update or create subscription
        subscription, created = Subscription.objects.update_or_create(
            user=user,
            defaults={
                'plan': plan,
                'status': 'ACTIVE',
                'started_at': timezone.now(),
                'stripe_subscription_id': session['subscription'],
                'stripe_customer_id': session['customer'],
                'scans_this_month': 0,
            }
        )
        
        logger.info(f"Subscription {'created' if created else 'updated'} for user {user.username} with plan {plan.name}")
        
    except Exception as e:
        logger.error(f"Error handling checkout completion: {e}")


def handle_subscription_updated(subscription):
    """Handle subscription updates"""
    try:
        stripe_subscription_id = subscription['id']
        
        # Find the subscription in our database
        try:
            sub = Subscription.objects.get(stripe_subscription_id=stripe_subscription_id)
            
            # Update status based on Stripe subscription status
            stripe_status = subscription['status']
            if stripe_status == 'active':
                sub.status = 'ACTIVE'
            elif stripe_status in ['canceled', 'unpaid']:
                sub.status = 'CANCELLED'
                sub.cancelled_at = timezone.now()
            elif stripe_status in ['past_due', 'incomplete']:
                sub.status = 'SUSPENDED'
            
            sub.save()
            logger.info(f"Updated subscription status for user {sub.user.username}: {sub.status}")
            
        except Subscription.DoesNotExist:
            logger.warning(f"Subscription not found for Stripe ID: {stripe_subscription_id}")
            
    except Exception as e:
        logger.error(f"Error handling subscription update: {e}")


def handle_subscription_deleted(subscription):
    """Handle subscription cancellation"""
    try:
        stripe_subscription_id = subscription['id']
        
        try:
            sub = Subscription.objects.get(stripe_subscription_id=stripe_subscription_id)
            sub.status = 'CANCELLED'
            sub.cancelled_at = timezone.now()
            sub.save()
            
            logger.info(f"Cancelled subscription for user {sub.user.username}")
            
        except Subscription.DoesNotExist:
            logger.warning(f"Subscription not found for cancellation: {stripe_subscription_id}")
            
    except Exception as e:
        logger.error(f"Error handling subscription deletion: {e}")


def handle_payment_succeeded(invoice):
    """Handle successful payment"""
    try:
        subscription_id = invoice['subscription']
        
        if subscription_id:
            try:
                sub = Subscription.objects.get(stripe_subscription_id=subscription_id)
                # Reset monthly scans on successful payment
                sub.reset_monthly_scans()
                logger.info(f"Payment succeeded for user {sub.user.username}, reset monthly scans")
                
            except Subscription.DoesNotExist:
                logger.warning(f"Subscription not found for payment: {subscription_id}")
                
    except Exception as e:
        logger.error(f"Error handling payment success: {e}")


def handle_payment_failed(invoice):
    """Handle failed payment"""
    try:
        subscription_id = invoice['subscription']
        
        if subscription_id:
            try:
                sub = Subscription.objects.get(stripe_subscription_id=subscription_id)
                sub.status = 'SUSPENDED'
                sub.save()
                logger.warning(f"Payment failed for user {sub.user.username}, suspended subscription")
                
            except Subscription.DoesNotExist:
                logger.warning(f"Subscription not found for failed payment: {subscription_id}")
                
    except Exception as e:
        logger.error(f"Error handling payment failure: {e}")
