"""
Enhanced AI Services Caching Module
Provides Redis and SQLite-based caching functionality for AI responses
"""
import os
import json
import time
import hashlib
import logging
import sqlite3
from typing import Dict, Any, Optional, List, Union
from pathlib import Path
from datetime import datetime, timedelta

# Try to import Redis, but don't fail if it's not available
try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

logger = logging.getLogger(__name__)

# Cache settings
CACHE_EXPIRY = 60 * 60 * 6  # 6 hours in seconds (as per requirements)
CACHE_PREFIX = "ai_suggestion:"

# Get cache type from environment
CACHE_TYPE = os.environ.get('AI_CACHE_TYPE', 'file').lower()  # 'redis', 'sqlite', or 'file'

# Redis settings
REDIS_HOST = os.environ.get('REDIS_HOST', 'localhost')
REDIS_PORT = int(os.environ.get('REDIS_PORT', 6379))
REDIS_DB = int(os.environ.get('REDIS_DB', 0))
REDIS_PASSWORD = os.environ.get('REDIS_PASSWORD', None)

# SQLite settings
SQLITE_DB_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'cache', 'suggestions.db')
os.makedirs(os.path.dirname(SQLITE_DB_PATH), exist_ok=True)

# File cache settings
FILE_CACHE_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'cache', 'files')
os.makedirs(FILE_CACHE_DIR, exist_ok=True)

# Cache hit/miss counters
_cache_hits = 0
_cache_misses = 0

# Initialize Redis client if available
_redis_client = None
if CACHE_TYPE == 'redis' and REDIS_AVAILABLE:
    try:
        _redis_client = redis.Redis(
            host=REDIS_HOST,
            port=REDIS_PORT,
            db=REDIS_DB,
            password=REDIS_PASSWORD,
            decode_responses=True
        )
        # Test connection
        _redis_client.ping()
        logger.info("Redis cache initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize Redis cache: {str(e)}")
        _redis_client = None
        CACHE_TYPE = 'sqlite'  # Fallback to SQLite

# Initialize SQLite if needed
if CACHE_TYPE == 'sqlite':
    try:
        conn = sqlite3.connect(SQLITE_DB_PATH)
        cursor = conn.cursor()
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS suggestions (
            prompt_hash TEXT PRIMARY KEY,
            content TEXT NOT NULL,
            created_at TIMESTAMP NOT NULL,
            expires_at TIMESTAMP NOT NULL
        )
        ''')
        conn.commit()
        conn.close()
        logger.info("SQLite cache initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize SQLite cache: {str(e)}")
        CACHE_TYPE = 'file'  # Fallback to file cache

logger.info(f"Using cache type: {CACHE_TYPE}")

def generate_cache_key(prompt: str, model: str, language: str, **kwargs) -> str:
    """
    Generate a cache key based on the input parameters
    
    Args:
        prompt: The prompt text
        model: The model name
        language: The language
        **kwargs: Additional parameters to include in the cache key
        
    Returns:
        A unique cache key string
    """
    # Create a dictionary of all parameters
    params = {
        'prompt': prompt,
        'model': model,
        'language': language,
        **kwargs
    }
    
    # Convert to a sorted JSON string to ensure consistent ordering
    params_str = json.dumps(params, sort_keys=True)
    
    # Generate a hash of the parameters
    return hashlib.md5(params_str.encode()).hexdigest()

def get_from_cache(cache_key: str) -> Optional[Any]:
    """
    Get data from the cache
    
    Args:
        cache_key: The cache key
        
    Returns:
        The cached data, or None if not found or expired
    """
    global _cache_hits, _cache_misses
    
    # Prefix the cache key
    prefixed_key = f"{CACHE_PREFIX}{cache_key}"
    
    try:
        # Redis cache
        if CACHE_TYPE == 'redis' and _redis_client:
            data = _redis_client.get(prefixed_key)
            if data:
                _cache_hits += 1
                logger.debug(f"Redis cache hit: {cache_key}")
                return json.loads(data)
            _cache_misses += 1
            return None
            
        # SQLite cache
        elif CACHE_TYPE == 'sqlite':
            conn = sqlite3.connect(SQLITE_DB_PATH)
            cursor = conn.cursor()
            cursor.execute(
                "SELECT content FROM suggestions WHERE prompt_hash = ? AND expires_at > ?", 
                (cache_key, datetime.now().isoformat())
            )
            result = cursor.fetchone()
            conn.close()
            
            if result:
                _cache_hits += 1
                logger.debug(f"SQLite cache hit: {cache_key}")
                return json.loads(result[0])
            _cache_misses += 1
            return None
            
        # File cache
        else:
            file_path = os.path.join(FILE_CACHE_DIR, f"{cache_key}.json")
            if not os.path.exists(file_path):
                _cache_misses += 1
                return None
                
            with open(file_path, 'r') as f:
                cache_data = json.load(f)
            
            # Check if the cache is expired
            timestamp = cache_data.get('timestamp', 0)
            if time.time() - timestamp > CACHE_EXPIRY:
                logger.debug(f"File cache expired: {cache_key}")
                _cache_misses += 1
                return None
            
            _cache_hits += 1
            logger.debug(f"File cache hit: {cache_key}")
            return cache_data.get('data')
            
    except Exception as e:
        logger.error(f"Error retrieving from cache: {str(e)}")
        _cache_misses += 1
        return None

def save_to_cache(cache_key: str, data: Any) -> bool:
    """
    Save data to the cache
    
    Args:
        cache_key: The cache key
        data: The data to cache
        
    Returns:
        True if successful, False otherwise
    """
    # Prefix the cache key
    prefixed_key = f"{CACHE_PREFIX}{cache_key}"
    
    try:
        # Redis cache
        if CACHE_TYPE == 'redis' and _redis_client:
            _redis_client.setex(
                prefixed_key,
                CACHE_EXPIRY,
                json.dumps(data)
            )
            logger.debug(f"Saved data to Redis cache: {cache_key}")
            return True
            
        # SQLite cache
        elif CACHE_TYPE == 'sqlite':
            conn = sqlite3.connect(SQLITE_DB_PATH)
            cursor = conn.cursor()
            now = datetime.now()
            expires_at = now + timedelta(seconds=CACHE_EXPIRY)
            
            cursor.execute(
                "INSERT OR REPLACE INTO suggestions (prompt_hash, content, created_at, expires_at) VALUES (?, ?, ?, ?)",
                (cache_key, json.dumps(data), now.isoformat(), expires_at.isoformat())
            )
            conn.commit()
            conn.close()
            
            logger.debug(f"Saved data to SQLite cache: {cache_key}")
            return True
            
        # File cache
        else:
            file_path = os.path.join(FILE_CACHE_DIR, f"{cache_key}.json")
            
            # Add timestamp to the cached data
            cache_data = {
                'timestamp': time.time(),
                'data': data
            }
            
            with open(file_path, 'w') as f:
                json.dump(cache_data, f)
            
            logger.debug(f"Saved data to file cache: {cache_key}")
            return True
            
    except Exception as e:
        logger.error(f"Error saving to cache: {str(e)}")
        return False

def clear_expired_cache() -> bool:
    """
    Clear expired cache entries
    
    Returns:
        True if successful, False otherwise
    """
    try:
        # SQLite cache
        if CACHE_TYPE == 'sqlite':
            conn = sqlite3.connect(SQLITE_DB_PATH)
            cursor = conn.cursor()
            cursor.execute("DELETE FROM suggestions WHERE expires_at < ?", (datetime.now().isoformat(),))
            deleted_count = cursor.rowcount
            conn.commit()
            conn.close()
            logger.info(f"Cleared {deleted_count} expired entries from SQLite cache")
            
        # File cache
        elif CACHE_TYPE == 'file':
            cache_files = list(Path(FILE_CACHE_DIR).glob("*.json"))
            deleted_count = 0
            
            for file_path in cache_files:
                try:
                    with open(file_path, 'r') as f:
                        cache_data = json.load(f)
                    
                    timestamp = cache_data.get('timestamp', 0)
                    if time.time() - timestamp > CACHE_EXPIRY:
                        os.remove(file_path)
                        deleted_count += 1
                except:
                    pass
            
            logger.info(f"Cleared {deleted_count} expired entries from file cache")
            
        # Redis automatically expires keys
        
        return True
    except Exception as e:
        logger.error(f"Error clearing expired cache: {str(e)}")
        return False

def get_cache_stats() -> Dict[str, Any]:
    """
    Get statistics about the cache
    
    Returns:
        A dictionary with cache statistics
    """
    stats = {
        'type': CACHE_TYPE,
        'hits': _cache_hits,
        'misses': _cache_misses,
        'hit_ratio': _cache_hits / (_cache_hits + _cache_misses) if (_cache_hits + _cache_misses) > 0 else 0,
        'entries': 0,
        'size': 0
    }
    
    try:
        # Redis cache
        if CACHE_TYPE == 'redis' and _redis_client:
            # Get all keys with the prefix
            keys = _redis_client.keys(f"{CACHE_PREFIX}*")
            stats['entries'] = len(keys)
            
            # Get memory usage if available
            try:
                memory_info = _redis_client.info('memory')
                stats['size'] = memory_info.get('used_memory', 0)
            except:
                pass
                
        # SQLite cache
        elif CACHE_TYPE == 'sqlite':
            conn = sqlite3.connect(SQLITE_DB_PATH)
            cursor = conn.cursor()
            
            # Count entries
            cursor.execute("SELECT COUNT(*) FROM suggestions")
            stats['entries'] = cursor.fetchone()[0]
            
            # Get database size
            cursor.execute("PRAGMA page_count")
            page_count = cursor.fetchone()[0]
            cursor.execute("PRAGMA page_size")
            page_size = cursor.fetchone()[0]
            stats['size'] = page_count * page_size
            
            conn.close()
            
        # File cache
        else:
            cache_files = list(Path(FILE_CACHE_DIR).glob("*.json"))
            stats['entries'] = len(cache_files)
            stats['size'] = sum(os.path.getsize(f) for f in cache_files)
    except Exception as e:
        logger.error(f"Error getting cache stats: {str(e)}")
        
    return stats
