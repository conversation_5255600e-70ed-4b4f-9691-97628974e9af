# 🧪 USER PRIVILEGE TESTING GUIDE

## 👥 Test Accounts Created

### 🆓 Regular User (Free Plan)
- **Username:** `apollo`
- **Password:** `2587`
- **Role:** `user`
- **Plan:** `FREE`
- **Limits:** 5 QR codes, 100 scans/month, basic features only

### 👑 Admin/Superuser
- **Username:** `peter`
- **Password:** `2587`
- **Role:** `admin`
- **Type:** `Superuser`
- **Access:** All features, no limits

## 🧪 Testing Scenarios

### 1. Free Plan Limitations (Login as `apollo`)
Test these scenarios to verify plan limits work:

**QR Code Creation Limits:**
- ✅ Create 5 QR codes (should work)
- ❌ Try to create 6th QR code (should be blocked)
- 📍 URL: `/generate/`

**Premium Feature Access:**
- ❌ Try to access AI Landing Pages (should redirect to pricing)
- ❌ Try to access Webhook Dashboard (should redirect to pricing)
- ❌ Try to access Dynamic Redirects (should redirect to pricing)
- ❌ Try to access Advanced Analytics (should redirect to pricing)

**Navigation Testing:**
- 🔍 Check QR Pro dropdown - should show upgrade prompts
- 🔍 Check Premium features - should show "Premium Feature" labels
- 🔍 Verify pricing page access works

### 2. Admin Access (Login as `peter`)
Test these scenarios to verify admin access:

**Full Feature Access:**
- ✅ Access all QR Pro features
- ✅ Access all Premium features
- ✅ Access admin dashboard
- ✅ Create unlimited QR codes

**Admin-Only Features:**
- ✅ Access Django admin panel: `/admin/`
- ✅ Access performance dashboard
- ✅ Access user management
- ✅ Access all analytics

### 3. Plan Upgrade Testing (Login as `apollo`)
Test the upgrade flow:

**Pricing Page:**
- 📍 URL: `/billing/pricing/`
- 🔍 Verify current plan is highlighted
- 🔍 Test billing toggle (monthly/yearly)
- 🔍 Check feature comparison

**Upgrade Flow:**
- 🔍 Click "Choose Pro" or "Choose Enterprise"
- 🔍 Should redirect to Stripe checkout (if configured)
- 🔍 Test upgrade to premium via: `/monetization/upgrade/`

### 4. Middleware Testing
Test the plan limits middleware:

**Protected URLs (should redirect apollo to pricing):**
- `/monetization/create-ai-page/1/`
- `/monetization/webhooks/`
- `/alerts/`
- `/analytics/advanced/`
- `/qr-map/`
- `/batch-processing/`
- `/api/`
- `/branding/`

**Allowed URLs (should work for apollo):**
- `/`
- `/generate/`
- `/qr-codes/`
- `/billing/pricing/`
- `/monetization/` (dashboard)

### 5. Feature Gating Testing
Test individual feature access:

**QR Pro Dashboard (`/monetization/`):**
- 🆓 Apollo: Should see upgrade prompts and "Premium Feature" labels
- 👑 Peter: Should see full feature access and usage stats

**Webhook Dashboard (`/monetization/webhooks/`):**
- 🆓 Apollo: Should redirect to pricing with warning message
- 👑 Peter: Should access full webhook management

**Advanced Analytics (`/premium/advanced-analytics/`):**
- 🆓 Apollo: Should show premium feature page
- 👑 Peter: Should access full analytics

## 🔍 What to Look For

### ✅ Expected Behavior (Working Correctly)
- Free users see upgrade prompts
- Premium features redirect to pricing for free users
- Admin users have full access
- Plan limits are enforced
- Navigation shows appropriate badges
- Error messages are user-friendly

### ❌ Issues to Report
- Free users can access premium features
- Plan limits not enforced
- Middleware not blocking protected URLs
- Missing upgrade prompts
- Broken navigation links
- Poor error messages

## 🚀 Quick Test Commands

```bash
# Reset monthly scans for testing
python manage.py reset_monthly_scans

# Create additional test data
python manage.py shell
>>> from qrcode_app.models import *
>>> # Create test QR codes, webhooks, etc.

# Check user subscriptions
python manage.py shell
>>> from qrcode_app.models import Subscription
>>> Subscription.objects.all()
```

## 📊 Testing Checklist

### Free User (apollo) Testing:
- [ ] Can create up to 5 QR codes
- [ ] Cannot create 6th QR code
- [ ] Cannot access webhook dashboard
- [ ] Cannot access AI landing pages
- [ ] Cannot access advanced analytics
- [ ] Can access pricing page
- [ ] Sees upgrade prompts in navigation
- [ ] Gets redirected to pricing for premium features

### Admin User (peter) Testing:
- [ ] Can access all features
- [ ] Can create unlimited QR codes
- [ ] Can access admin panel
- [ ] Can access all premium features
- [ ] No plan limit restrictions
- [ ] Full navigation access

### Billing Integration Testing:
- [ ] Pricing page loads correctly
- [ ] Billing toggle works
- [ ] Stripe checkout integration works
- [ ] Upgrade flow functions properly
- [ ] Plan comparison is accurate

## 🎯 Success Criteria

The system passes testing if:
1. **Plan limits are strictly enforced**
2. **Premium features are properly gated**
3. **Navigation reflects user privileges**
4. **Upgrade flow is seamless**
5. **Error messages are helpful**
6. **Admin access is unrestricted**

## 🔧 Troubleshooting

If something doesn't work:
1. Check if middleware is enabled in settings
2. Verify user has correct subscription
3. Check plan limits in database
4. Verify URL patterns are correct
5. Check for JavaScript errors in browser console

---

**Ready to test! Start by logging in as `apollo` to test free plan limitations.** 🚀
