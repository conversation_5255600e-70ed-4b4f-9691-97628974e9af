/**
 * Notifications JavaScript
 * NOTE: Notification dropdown functionality is now handled by notification-bell-hover.js
 * This file still handles notification actions on the notification list page
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Notification dropdown functionality is now handled by notification-bell-hover.js');

    // Only initialize notification actions for the notification list page
    // Skip the dropdown functionality which is now handled by notification-bell-hover.js
    initNotificationActions();
});

/**
 * Initialize notification system
 * NOTE: This functionality is now handled by notification-bell-hover.js
 */
function initNotifications() {
    console.log('Notification system initialization is now handled by notification-bell-hover.js');
    return;
}

/**
 * Add notification counter to the navbar
 * NOTE: This functionality is now handled by notification-bell-hover.js
 */
function addNotificationCounter() {
    console.log('Notification counter functionality is now handled by notification-bell-hover.js');
    return;
}

/**
 * Initialize notification dropdown
 * NOTE: This functionality is now handled by notification-bell-hover.js
 */
function initNotificationDropdown() {
    console.log('Notification dropdown functionality is now handled by notification-bell-hover.js');
    return;
}

/**
 * Load notifications into dropdown
 * NOTE: This functionality is now handled by notification-bell-hover.js
 */
function loadNotifications() {
    console.log('Notification loading functionality is now handled by notification-bell-hover.js');
    return;
}

/**
 * Create notification item for dropdown
 * NOTE: This functionality is now handled by notification-bell-hover.js
 */
function createNotificationItem(notification) {
    console.log('Notification item creation is now handled by notification-bell-hover.js');
    return document.createElement('div');
}

/**
 * Update notification count
 * NOTE: This functionality is now handled by notification-bell-hover.js
 */
function updateNotificationCount() {
    console.log('Notification count updating is now handled by notification-bell-hover.js');
    return;
}

/**
 * Set up polling for new notifications
 * NOTE: This functionality is now handled by notification-bell-hover.js
 */
function setupNotificationPolling() {
    console.log('Notification polling is now handled by notification-bell-hover.js');
    return;
}

/**
 * Initialize notification actions
 */
function initNotificationActions() {
    // Handle mark as read/unread buttons
    document.querySelectorAll('.notification-read-btn, .notification-unread-btn').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            // Get notification ID
            const notificationId = this.dataset.id;
            if (!notificationId) {
                return;
            }

            // Get action URL
            const action = this.classList.contains('notification-read-btn') ? 'mark-as-read' : 'mark-as-unread';
            const url = `/notifications/${notificationId}/${action}/`;

            // Send AJAX request
            fetch(url, {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': getCSRFToken(),
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Reload page
                    window.location.reload();
                }
            })
            .catch(error => {
                console.error(`Error ${action} notification:`, error);
                showNotification(`Error ${action.replace('-', ' ')} notification`, 'error');
            });
        });
    });

    // Handle delete buttons
    document.querySelectorAll('.notification-delete-btn').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            // Get notification ID
            const notificationId = this.dataset.id;
            if (!notificationId) {
                return;
            }

            // Confirm deletion
            if (!confirm('Are you sure you want to delete this notification?')) {
                return;
            }

            // Get action URL
            const url = `/notifications/${notificationId}/delete/`;

            // Send AJAX request
            fetch(url, {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': getCSRFToken(),
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Reload page
                    window.location.reload();
                }
            })
            .catch(error => {
                console.error('Error deleting notification:', error);
                showNotification('Error deleting notification', 'error');
            });
        });
    });
}

/**
 * Set up notification events (hover and click)
 * NOTE: This functionality is now handled by notification-bell-hover.js
 */
function setupNotificationEvents(notificationBtn, dropdown, dropdownMenu) {
    console.log('Notification events setup is now handled by notification-bell-hover.js');
    return;
}

/**
 * Get CSRF token from cookies
 */
function getCSRFToken() {
    const cookies = document.cookie.split(';');
    for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i].trim();
        if (cookie.startsWith('csrftoken=')) {
            return cookie.substring('csrftoken='.length, cookie.length);
        }
    }
    return '';
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    // Check if showNotification function already exists in global scope
    if (window.showNotification && typeof window.showNotification === 'function') {
        // Use existing function
        window.showNotification(message, type);
    } else {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;

        // Add icon based on type
        let icon = 'info-circle';
        if (type === 'success') icon = 'check-circle';
        if (type === 'warning') icon = 'exclamation-triangle';
        if (type === 'error') icon = 'exclamation-circle';

        notification.innerHTML = `
            <i class="fas fa-${icon}"></i>
            <span>${message}</span>
            <button class="notification-close"><i class="fas fa-times"></i></button>
        `;

        // Add to the DOM
        document.body.appendChild(notification);

        // Show notification with animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        // Add close button functionality
        const closeBtn = notification.querySelector('.notification-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                notification.classList.remove('show');
                setTimeout(() => {
                    notification.remove();
                }, 300);
            });
        }

        // Auto-hide after 5 seconds
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 5000);
    }
}
