# Generated by Django 5.1.7 on 2025-05-29 07:37

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('qrcode_app', '0014_scanalert_scanevent'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='WebhookEndpoint',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('url', models.URLField(help_text='Zapier/CRM webhook URL to send scan data to')),
                ('trigger_on_scan', models.BooleanField(default=True, help_text='Send webhook when QR is scanned')),
                ('trigger_on_alert', models.BooleanField(default=False, help_text='Send webhook when scan alert is triggered')),
                ('active', models.BooleanField(default=True, help_text='Enable/disable this webhook')),
                ('secret_key', models.Char<PERSON>ield(blank=True, help_text='Optional secret key for webhook verification', max_length=255)),
                ('total_calls', models.PositiveIntegerField(default=0, help_text='Total webhook calls made')),
                ('successful_calls', models.PositiveIntegerField(default=0, help_text='Successful webhook calls')),
                ('last_called', models.DateTimeField(blank=True, null=True)),
                ('last_error', models.TextField(blank=True, help_text='Last error message if webhook failed')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('qr_code', models.ForeignKey(blank=True, help_text='Specific QR code to monitor (leave blank for all QR codes)', null=True, on_delete=django.db.models.deletion.CASCADE, to='qrcode_app.qrcode')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='webhook_endpoints', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Webhook Endpoint',
                'verbose_name_plural': 'Webhook Endpoints',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', 'active'], name='qrcode_app__user_id_f29c50_idx'), models.Index(fields=['qr_code', 'active'], name='qrcode_app__qr_code_178ab9_idx')],
            },
        ),
    ]
