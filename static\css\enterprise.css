/* Enterprise QR Code Generator - Corporate UI Styles */

:root {
    /* Corporate Color Palette */
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #60a5fa;
    --secondary-color: #475569;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #6366f1;
    --light-color: #f8fafc;
    --dark-color: #0f172a;

    /* Neutral Colors */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;

    /* UI Elements */
    --body-bg: var(--gray-50);
    --card-bg: #ffffff;
    --border-radius: 0.5rem;
    --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --box-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --transition: all 0.3s ease;
    --header-height: 70px;
}

/* Base Styles */
body {
    font-family: 'Inter', sans-serif;
    background-color: var(--body-bg);
    color: var(--gray-700);
    line-height: 1.6;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    margin: 0; /* Ensure no margin */
    padding: 0; /* Ensure no padding */
    overflow-x: hidden; /* Prevent horizontal scrolling */
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    color: var(--gray-800);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--primary-dark);
}

/* Preloader */
.preloader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--card-bg);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.preloader.fade-out {
    opacity: 0;
    visibility: hidden;
}

.preloader-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
    max-width: 320px;
    text-align: center;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 1rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(37, 99, 235, 0.1);
    animation: preloaderAppear 0.5s ease-out;
}

@keyframes preloaderAppear {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.preloader-logo {
    font-size: 3.5rem;
    background: linear-gradient(135deg, #E03C31, #FF9500);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: kenyanGlow 3s infinite alternate;
    position: relative;
}

.preloader-logo::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: radial-gradient(circle, rgba(0, 150, 136, 0.2) 0%, rgba(0, 0, 0, 0) 70%);
    z-index: -1;
    opacity: 0.7;
    animation: pulseBackground 2s infinite;
}

@keyframes kenyanGlow {
    0% {
        filter: drop-shadow(0 0 3px rgba(224, 60, 49, 0.4));
        transform: scale(1);
    }
    50% {
        filter: drop-shadow(0 0 5px rgba(0, 150, 136, 0.5));
    }
    100% {
        filter: drop-shadow(0 0 8px rgba(224, 60, 49, 0.6));
        transform: scale(1.08);
    }
}

@keyframes pulseBackground {
    0%, 100% {
        opacity: 0.3;
        transform: scale(0.95);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.05);
    }
}

.preloader-text {
    font-family: 'Poppins', sans-serif;
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-800);
    letter-spacing: -0.5px;
    position: relative;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.preloader-text span {
    color: #E03C31;
    position: relative;
}

.preloader-text span::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, #E03C31, #009688);
    animation: lineGrow 2s infinite;
}

@keyframes lineGrow {
    0%, 100% {
        width: 0;
        left: 0;
    }
    50% {
        width: 100%;
        left: 0;
    }
}

.preloader-spinner {
    position: relative;
    width: 70px;
    height: 70px;
    margin: 1rem 0;
}

.spinner-ring {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 3px solid rgba(0, 150, 136, 0.1);
    border-top: 3px solid #009688;
    border-right: 3px solid #E03C31;
    border-bottom: 3px solid #000000;
    border-radius: 50%;
    animation: spinKenyan 1.5s linear infinite;
}

.spinner-dot {
    position: absolute;
    top: 0;
    left: 50%;
    width: 12px;
    height: 12px;
    background-color: #E03C31;
    border-radius: 50%;
    transform: translateX(-50%);
    animation: dotMoveKenyan 1.5s linear infinite;
    box-shadow: 0 0 10px rgba(224, 60, 49, 0.5);
}

@keyframes spinKenyan {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes dotMoveKenyan {
    0% {
        transform: translateX(-50%) translateY(-5px) scale(1);
        background-color: #E03C31;
    }
    33% {
        background-color: #009688;
    }
    66% {
        background-color: #000000;
    }
    100% {
        transform: translateX(-50%) translateY(-5px) scale(1);
        background-color: #E03C31;
    }
}

.preloader-progress {
    width: 100%;
    height: 6px;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 6px;
    overflow: hidden;
    position: relative;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.progress-bar {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 0;
    background: linear-gradient(90deg, #E03C31, #009688);
    border-radius: 6px;
    transition: width 0.3s ease;
    animation: progressPulse 2s infinite;
}

@keyframes progressPulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
}

.preloader-status {
    font-size: 0.95rem;
    color: var(--gray-700);
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    background-color: rgba(0, 0, 0, 0.03);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    animation: statusFade 0.5s ease-in-out;
    border-left: 3px solid #009688;
}

@keyframes statusFade {
    from {
        opacity: 0;
        transform: translateY(5px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Corporate Navbar */
.corporate-navbar {
    background-color: var(--card-bg);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
    padding: 0.75rem 1rem;
    height: var(--header-height);
    border-bottom: 1px solid var(--gray-200);
}

.navbar-brand {
    font-family: 'Poppins', sans-serif;
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--primary-color);
    display: flex;
    align-items: center;
}

.brand-icon {
    font-size: 1.75rem;
    margin-right: 0.5rem;
    color: var(--primary-color);
}

.brand-text {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: 700;
}

.nav-link {
    font-weight: 500;
    color: var(--gray-600);
    padding: 0.5rem 1rem;
    transition: var(--transition);
    border-radius: 0.375rem;
    margin: 0 0.25rem;
    display: flex;
    align-items: center;
}

.nav-icon {
    margin-right: 0.5rem;
    font-size: 1rem;
    width: 1.25rem;
    text-align: center;
}

.nav-link:hover {
    color: var(--primary-color);
    background-color: var(--gray-100);
}

.nav-link.active {
    color: var(--primary-color);
    background-color: rgba(37, 99, 235, 0.1);
}

/* Corporate Dropdown */
.corporate-dropdown {
    border: none;
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius);
    padding: 0.5rem;
    min-width: 220px;
}

.dropdown-header {
    font-weight: 600;
    color: var(--gray-500);
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 0.5rem 1rem;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    font-weight: 500;
    color: var(--gray-700);
    border-radius: 0.375rem;
    transition: var(--transition);
    display: flex;
    align-items: center;
}

.dropdown-item i {
    margin-right: 0.75rem;
    font-size: 1rem;
    width: 1.25rem;
    text-align: center;
}

.dropdown-item:hover {
    background-color: var(--gray-100);
    color: var(--primary-color);
}

.dropdown-item.active {
    background-color: rgba(37, 99, 235, 0.1);
    color: var(--primary-color);
}

.dropdown-divider {
    margin: 0.5rem 0;
    border-top: 1px solid var(--gray-200);
}

/* User Menu */
.user-menu-link {
    display: flex;
    align-items: center;
}

.user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.5rem;
    font-size: 1rem;
}

/* Login Button */
.login-btn {
    background-color: var(--primary-color);
    color: white !important;
    border-radius: 0.375rem;
    padding: 0.5rem 1.25rem;
    transition: var(--transition);
}

.login-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(37, 99, 235, 0.2);
}

/* Corporate Cards */
.corporate-card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
    height: 100%;
}

.corporate-card:hover {
    box-shadow: var(--box-shadow-lg);
    transform: translateY(-5px);
}

.card-header {
    background-color: var(--card-bg);
    border-bottom: 1px solid var(--gray-200);
    padding: 1.25rem 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(37, 99, 235, 0.1);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    margin-right: 1rem;
}

.card-body {
    padding: 1.5rem;
}

/* Corporate Buttons */
.btn {
    font-weight: 500;
    padding: 0.5rem 1.25rem;
    border-radius: 0.375rem;
    transition: var(--transition);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(37, 99, 235, 0.2);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(37, 99, 235, 0.2);
}

/* Corporate Footer */
.corporate-footer {
    background-color: var(--card-bg);
    border-top: 1px solid var(--gray-200);
    padding: 3rem 0 1.5rem;
    margin-top: auto;
}

.footer-heading {
    font-weight: 600;
    margin-bottom: 1.25rem;
    color: var(--gray-800);
}

.footer-link {
    color: var(--gray-600);
    transition: var(--transition);
    display: block;
    margin-bottom: 0.75rem;
}

.footer-link:hover {
    color: var(--primary-color);
}

.footer-social {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

.social-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: var(--gray-100);
    color: var(--gray-600);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.social-icon:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-3px);
}

.footer-bottom {
    border-top: 1px solid var(--gray-200);
    padding-top: 1.5rem;
    margin-top: 3rem;
}

/* Animations */
.fade-in {
    animation: fadeIn 0.5s ease forwards;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Premium Feature Modal Styles */
.bg-gradient-premium {
    background: linear-gradient(135deg, #FF9500, #FF2D55);
}

.premium-icon-container {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #FF9500, #FF2D55);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 10px 20px rgba(255, 45, 85, 0.2);
}

.premium-icon {
    font-size: 2.5rem;
    color: white;
}

.premium-feature-title {
    font-weight: 700;
    color: #333;
}

.premium-feature-description {
    color: #666;
    font-size: 1.1rem;
}

.premium-benefits {
    background-color: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
}

.benefit-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #FF9500, #FF2D55);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    box-shadow: 0 4px 8px rgba(255, 45, 85, 0.15);
}

.btn-premium {
    background: linear-gradient(135deg, #FF9500, #FF2D55);
    border: none;
    color: white;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(255, 45, 85, 0.2);
}

.btn-premium:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(255, 45, 85, 0.25);
    color: white;
}

/* VPN Modal Styles */
.modal-content {
    overflow: hidden;
}

#vpn-modal .modal-content {
    border: none;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    transform: translateY(20px);
    opacity: 0;
    animation: modalSlideIn 0.5s ease forwards;
    background: linear-gradient(135deg, #ffffff, #f8fafc);
}

@keyframes modalSlideIn {
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.modal-header.bg-gradient-primary {
    background: linear-gradient(135deg, #E03C31, #009688);
    padding: 1.25rem 1.5rem;
    position: relative;
    overflow: hidden;
}

.modal-header.bg-gradient-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E");
    opacity: 0.3;
}

.modal-title {
    display: flex;
    align-items: center;
    font-weight: 700;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.modal-title i {
    margin-right: 0.75rem;
    font-size: 1.25rem;
    animation: shieldPulse 2s infinite;
}

@keyframes shieldPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

.vpn-status {
    background-color: rgba(239, 68, 68, 0.1);
    padding: 1.5rem;
    border-radius: 1rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border-left: 4px solid #ef4444;
    position: relative;
    overflow: hidden;
    transition: all 0.5s ease;
    animation: statusAppear 0.5s ease-out;
}

@keyframes statusAppear {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.vpn-status::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100%;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.1));
    transform: skewX(-15deg) translateX(100%);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% {
        transform: skewX(-15deg) translateX(-150%);
    }
    100% {
        transform: skewX(-15deg) translateX(150%);
    }
}

.vpn-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: rgba(239, 68, 68, 0.15);
    color: #ef4444;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.75rem;
    box-shadow: 0 4px 8px rgba(239, 68, 68, 0.2);
    position: relative;
    transition: all 0.5s ease;
}

.vpn-icon::before {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border-radius: 50%;
    border: 2px solid rgba(239, 68, 68, 0.2);
    animation: pulseRing 2s infinite;
}

@keyframes pulseRing {
    0% {
        transform: scale(0.95);
        opacity: 0.7;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.3;
    }
    100% {
        transform: scale(0.95);
        opacity: 0.7;
    }
}

.vpn-info h4 {
    font-size: 1.35rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #1e293b;
    transition: all 0.5s ease;
}

.vpn-info p {
    margin-bottom: 0;
    color: #64748b;
    line-height: 1.5;
    transition: all 0.5s ease;
}

.vpn-features-container {
    margin-top: 1.5rem;
}

.vpn-features-title {
    font-size: 1.2rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 1rem;
    position: relative;
    padding-left: 1rem;
    border-left: 3px solid #E03C31;
}

.vpn-features {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
}

.vpn-feature {
    background-color: #f8fafc;
    padding: 1.5rem;
    border-radius: 1rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.03);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    animation: featureAppear 0.5s ease-out both;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.vpn-feature:nth-child(1) { animation-delay: 0.1s; }
.vpn-feature:nth-child(2) { animation-delay: 0.2s; }
.vpn-feature:nth-child(3) { animation-delay: 0.3s; }
.vpn-feature:nth-child(4) { animation-delay: 0.4s; }

@keyframes featureAppear {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.vpn-feature:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.05);
    border-color: rgba(0, 150, 136, 0.2);
}

.feature-icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, rgba(0, 150, 136, 0.1), rgba(224, 60, 49, 0.1));
    margin-bottom: 1rem;
}

.vpn-feature i {
    font-size: 1.5rem;
    color: #009688;
    transition: all 0.3s ease;
}

.vpn-feature:hover i {
    transform: scale(1.1);
    color: #E03C31;
}

.feature-content {
    flex: 1;
}

.feature-content h5 {
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
    color: #1e293b;
}

.feature-content p {
    font-size: 0.9rem;
    color: #64748b;
    margin-bottom: 0;
    line-height: 1.5;
}

.vpn-modal-footer {
    background-color: rgba(248, 250, 252, 0.5);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
}

.vpn-activate-btn {
    background: linear-gradient(135deg, #E03C31, #009688);
    border: none;
    padding: 0.85rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.vpn-activate-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
    transition: all 0.5s ease;
    z-index: -1;
}

.vpn-activate-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

.vpn-activate-btn:hover::before {
    left: 100%;
}

.vpn-note {
    font-size: 0.9rem;
    color: #64748b;
    margin-top: 1rem;
    text-align: center;
}

.vpn-note a {
    color: #009688;
    text-decoration: none;
    font-weight: 500;
    position: relative;
}

.vpn-note a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg, #E03C31, #009688);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
}

.vpn-note a:hover::after {
    transform: scaleX(1);
}

.coming-soon-note {
    color: #6b7280;
    font-size: 0.9rem;
    animation: fadeIn 0.5s ease-in-out;
    margin-top: 0.75rem;
}

.thank-you-message {
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.5s ease;
    margin-top: 1.5rem;
    margin-bottom: 0.5rem;
}

.thank-you-message.thank-you-visible {
    opacity: 1;
    transform: translateY(0);
}

.thank-you-content {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.2));
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(16, 185, 129, 0.2);
    position: relative;
    overflow: hidden;
}

.thank-you-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%2310b981' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
    opacity: 0.5;
}

.thank-you-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #10B981, #059669);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 4px 10px rgba(16, 185, 129, 0.3);
    animation: pulseThankYou 2s infinite;
}

@keyframes pulseThankYou {
    0% {
        box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.5);
    }
    70% {
        box-shadow: 0 0 0 15px rgba(16, 185, 129, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
    }
}

.thank-you-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #10B981;
    margin-bottom: 0.75rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.thank-you-text {
    font-size: 1rem;
    color: #374151;
    line-height: 1.5;
    margin-bottom: 0;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #E03C31, #009688);
}

/* Premium Badge Styles */
.badge.bg-warning.text-dark {
    background: linear-gradient(135deg, #FF9500, #FF2D55) !important;
    color: white !important;
    font-weight: 600;
    padding: 0.35em 0.65em;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(255, 45, 85, 0.2);
}

/* Responsive Adjustments */
@media (max-width: 992px) {
    .navbar-collapse {
        background-color: var(--card-bg);
        padding: 1rem;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        position: absolute;
        top: 70px;
        left: 0;
        right: 0;
        z-index: 1000;
    }

    .nav-link {
        padding: 0.75rem 1rem;
    }

    .dropdown-menu {
        box-shadow: none;
        border: 1px solid var(--gray-200);
    }
}

@media (max-width: 768px) {
    .corporate-footer {
        text-align: center;
    }

    .footer-social {
        justify-content: center;
    }
}
