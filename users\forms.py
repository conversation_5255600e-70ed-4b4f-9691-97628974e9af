from django import forms
from django.contrib.auth.models import User, Group
from django.contrib.auth.forms import UserCreationForm
from qrcode_app.models import UserProfile

class UserForm(forms.ModelForm):
    """Form for creating/editing users"""
    password = forms.CharField(widget=forms.PasswordInput(), required=False,
                              help_text="Leave blank to keep current password")
    confirm_password = forms.CharField(widget=forms.PasswordInput(), required=False)

    class Meta:
        model = User
        fields = ['username', 'email', 'first_name', 'last_name', 'is_active', 'is_staff', 'is_superuser', 'groups']
        widgets = {
            'groups': forms.CheckboxSelectMultiple(),
        }

    def __init__(self, *args, **kwargs):
        super(UserForm, self).__init__(*args, **kwargs)
        # Make some fields not required
        self.fields['email'].required = True
        self.fields['first_name'].required = True
        self.fields['last_name'].required = True

        # Add Bootstrap classes
        for field_name, field in self.fields.items():
            if field_name != 'groups':
                field.widget.attrs['class'] = 'form-control'

        # If this is an edit form (instance exists)
        if self.instance and self.instance.pk:
            self.fields['username'].widget.attrs['readonly'] = True
            self.fields['password'].help_text = "Leave blank to keep current password"
        else:
            self.fields['password'].required = True
            self.fields['confirm_password'].required = True

    def clean(self):
        cleaned_data = super().clean()
        password = cleaned_data.get("password")
        confirm_password = cleaned_data.get("confirm_password")

        if password and password != confirm_password:
            self.add_error('confirm_password', "Passwords don't match")

        return cleaned_data

    def save(self, commit=True):
        user = super().save(commit=False)

        # Set password if provided
        password = self.cleaned_data.get('password')
        if password:
            user.set_password(password)

        if commit:
            user.save()
            self.save_m2m()

        return user

class UserProfileForm(forms.ModelForm):
    """Form for creating/editing user profiles"""

    class Meta:
        model = UserProfile
        fields = ['role', 'company', 'phone', 'address', 'profile_image']
        widgets = {
            'address': forms.Textarea(attrs={'rows': 2}),
        }

    def __init__(self, *args, **kwargs):
        super(UserProfileForm, self).__init__(*args, **kwargs)

        # Add Bootstrap classes
        for field_name, field in self.fields.items():
            field.widget.attrs['class'] = 'form-control'
