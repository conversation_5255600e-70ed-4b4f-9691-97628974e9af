/**
 * Notification Actions CSS
 * Styles for notification action buttons and dropdowns
 */

/* Notification action toggle button */
.notification-action-toggle {
    background: none;
    border: none;
    color: #666;
    padding: 5px;
    cursor: pointer;
    border-radius: 4px;
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
}

.notification-action-toggle:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

/* Ellipsis and arrow icons */
.notification-action-toggle i {
    font-size: 14px;
}

.notification-action-toggle i.fa-ellipsis-v {
    margin-right: 2px;
}

.notification-action-toggle i.fa-chevron-down {
    font-size: 10px;
    transition: transform 0.2s ease;
}

/* Rotate arrow when dropdown is open */
.notification-action-dropdown.show + .notification-action-toggle i.fa-chevron-down,
.notification-action-toggle.active i.fa-chevron-down {
    transform: rotate(180deg);
}

/* Notification action dropdown */
.notification-action-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 5px 0;
    min-width: 150px;
    z-index: 1000;
    display: none;
    opacity: 0;
    transform: translateY(10px);
    transition: opacity 0.2s ease, transform 0.2s ease;
}

.notification-action-dropdown.show {
    display: block;
    opacity: 1;
    transform: translateY(0);
}

/* Notification action buttons */
.notification-action-btn {
    display: flex;
    align-items: center;
    width: 100%;
    text-align: left;
    background: none;
    border: none;
    padding: 8px 12px;
    font-size: 13px;
    color: #333;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.notification-action-btn:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.notification-action-btn i {
    margin-right: 8px;
    width: 16px;
    text-align: center;
}

/* Specific action button styles */
.notification-action-btn.mark-read i {
    color: #4caf50;
}

.notification-action-btn.archive i {
    color: #ff9800;
}

.notification-action-btn.delete i {
    color: #f44336;
}

/* Notification counter with pulsating effect */
.notification-badge.has-notifications {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(244, 67, 54, 0.4);
    }
    70% {
        box-shadow: 0 0 0 6px rgba(244, 67, 54, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(244, 67, 54, 0);
    }
}

/* Confirmation dialog */
.notification-confirm-dialog {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.notification-confirm-dialog.show {
    opacity: 1;
    visibility: visible;
}

.notification-confirm-content {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    width: 90%;
    max-width: 400px;
    padding: 20px;
    transform: translateY(20px);
    transition: transform 0.3s ease;
}

.notification-confirm-dialog.show .notification-confirm-content {
    transform: translateY(0);
}

.notification-confirm-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #f44336;
}

.notification-confirm-message {
    font-size: 14px;
    margin-bottom: 20px;
    color: #666;
}

.notification-confirm-actions {
    display: flex;
    justify-content: flex-end;
}

.notification-confirm-btn {
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.notification-confirm-btn.cancel {
    background-color: #f5f5f5;
    color: #333;
    border: none;
    margin-right: 10px;
}

.notification-confirm-btn.confirm {
    background-color: #f44336;
    color: white;
    border: none;
}

.notification-confirm-btn:hover {
    opacity: 0.9;
}

/* Pagination for notifications */
.notification-pagination {
    display: flex;
    justify-content: center;
    margin-top: 10px;
    padding: 5px 0;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.notification-pagination-btn {
    background: none;
    border: none;
    color: #1a237e;
    padding: 5px 10px;
    margin: 0 2px;
    cursor: pointer;
    border-radius: 4px;
    font-size: 12px;
    transition: all 0.2s ease;
}

.notification-pagination-btn:hover {
    background-color: rgba(26, 35, 126, 0.1);
}

.notification-pagination-btn.active {
    background-color: #1a237e;
    color: white;
}

.notification-pagination-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}
