/**
 * Analytics Charts JavaScript
 * Handles chart initialization and rendering for the analytics dashboard
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all charts
    initializeCharts();
    
    // Add event listeners for chart filters
    setupChartFilters();
    
    // Calculate and display average metrics
    calculateAverageMetrics();
});

/**
 * Initialize all charts on the page
 */
function initializeCharts() {
    // Performance Chart
    initializePerformanceChart();
    
    // Device Chart
    initializeDeviceChart();
    
    // Location Chart
    initializeLocationChart();
    
    // Campaign Comparison Chart
    initializeCampaignChart();
}

/**
 * Initialize the performance trend chart
 */
function initializePerformanceChart() {
    const performanceChartElement = document.getElementById('performanceChart');
    if (!performanceChartElement) return;
    
    try {
        // Get chart data from the element's data attributes
        const chartLabels = JSON.parse(performanceChartElement.getAttribute('data-labels') || '[]');
        const impressionsData = JSON.parse(performanceChartElement.getAttribute('data-impressions') || '[]');
        const clicksData = JSON.parse(performanceChartElement.getAttribute('data-clicks') || '[]');
        
        // Get selected metric
        const metricFilter = document.getElementById('metric-filter');
        const selectedMetric = metricFilter ? metricFilter.value : 'clicks';
        
        // Determine which dataset to display based on selected metric
        let displayData, dataLabel, borderColor, backgroundColor;
        
        if (selectedMetric === 'impressions') {
            displayData = impressionsData;
            dataLabel = 'Impressions';
            borderColor = 'rgba(75, 192, 192, 1)';
            backgroundColor = 'rgba(75, 192, 192, 0.2)';
        } else if (selectedMetric === 'ctr') {
            // Calculate CTR if impressions and clicks are available
            const ctrData = impressionsData.map((imp, index) => {
                return imp > 0 ? ((clicksData[index] / imp) * 100).toFixed(2) : 0;
            });
            displayData = ctrData;
            dataLabel = 'CTR (%)';
            borderColor = 'rgba(153, 102, 255, 1)';
            backgroundColor = 'rgba(153, 102, 255, 0.2)';
        } else {
            // Default to clicks
            displayData = clicksData;
            dataLabel = 'Clicks';
            borderColor = 'rgba(255, 159, 64, 1)';
            backgroundColor = 'rgba(255, 159, 64, 0.2)';
        }
        
        // Create the chart
        const ctx = performanceChartElement.getContext('2d');
        window.performanceChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: chartLabels,
                datasets: [{
                    label: dataLabel,
                    data: displayData,
                    borderColor: borderColor,
                    backgroundColor: backgroundColor,
                    borderWidth: 2,
                    tension: 0.3,
                    fill: true,
                    pointBackgroundColor: borderColor,
                    pointRadius: 3,
                    pointHoverRadius: 5
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (selectedMetric === 'ctr') {
                                    label += context.parsed.y + '%';
                                } else {
                                    label += context.parsed.y;
                                }
                                return label;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                if (selectedMetric === 'ctr') {
                                    return value + '%';
                                }
                                return value;
                            }
                        }
                    }
                }
            }
        });
        
        console.log('Performance chart initialized successfully');
    } catch (error) {
        console.error('Error initializing performance chart:', error);
    }
}

/**
 * Initialize the device breakdown chart
 */
function initializeDeviceChart() {
    const deviceChartElement = document.getElementById('deviceChart');
    if (!deviceChartElement) return;
    
    try {
        // Get chart data from the element's data attributes
        const deviceLabels = JSON.parse(deviceChartElement.getAttribute('data-devices') || '[]');
        const deviceValues = JSON.parse(deviceChartElement.getAttribute('data-counts') || '[]');
        
        // Define colors for the chart
        const deviceColors = [
            'rgba(255, 99, 132, 0.7)',
            'rgba(54, 162, 235, 0.7)',
            'rgba(255, 206, 86, 0.7)',
            'rgba(75, 192, 192, 0.7)',
            'rgba(153, 102, 255, 0.7)'
        ];
        
        // Create the chart
        const ctx = deviceChartElement.getContext('2d');
        window.deviceChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: deviceLabels.map(label => label.charAt(0).toUpperCase() + label.slice(1)),
                datasets: [{
                    data: deviceValues,
                    backgroundColor: deviceColors,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    }
                }
            }
        });
        
        console.log('Device chart initialized successfully');
    } catch (error) {
        console.error('Error initializing device chart:', error);
    }
}

/**
 * Initialize the location chart
 */
function initializeLocationChart() {
    const locationChartElement = document.getElementById('locationChart');
    if (!locationChartElement) return;
    
    try {
        // Get chart data from the element's data attributes
        const locationLabels = JSON.parse(locationChartElement.getAttribute('data-locations') || '[]');
        const locationValues = JSON.parse(locationChartElement.getAttribute('data-counts') || '[]');
        
        // Define colors for the chart
        const locationColors = [
            'rgba(255, 99, 132, 0.7)',
            'rgba(54, 162, 235, 0.7)',
            'rgba(255, 206, 86, 0.7)',
            'rgba(75, 192, 192, 0.7)',
            'rgba(153, 102, 255, 0.7)'
        ];
        
        // Create the chart
        const ctx = locationChartElement.getContext('2d');
        window.locationChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: locationLabels,
                datasets: [{
                    label: 'Impressions by Location',
                    data: locationValues,
                    backgroundColor: locationColors,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        
        console.log('Location chart initialized successfully');
    } catch (error) {
        console.error('Error initializing location chart:', error);
    }
}

/**
 * Initialize the campaign comparison chart
 */
function initializeCampaignChart() {
    const campaignChartElement = document.getElementById('campaignComparisonChart');
    if (!campaignChartElement) return;
    
    try {
        // Get chart data from the element's data attributes
        const campaignLabels = JSON.parse(campaignChartElement.getAttribute('data-campaigns') || '[]');
        const impressionsData = JSON.parse(campaignChartElement.getAttribute('data-impressions') || '[]');
        const clicksData = JSON.parse(campaignChartElement.getAttribute('data-clicks') || '[]');
        const ctrData = JSON.parse(campaignChartElement.getAttribute('data-ctr') || '[]');
        
        // Get selected metric
        const metricSelector = document.getElementById('campaign-metric-selector');
        const selectedMetric = metricSelector ? metricSelector.value : 'clicks';
        
        // Create the chart
        initCampaignChartWithMetric(campaignChartElement, campaignLabels, impressionsData, clicksData, ctrData, selectedMetric);
        
        // Add event listener for metric selector
        if (metricSelector) {
            metricSelector.addEventListener('change', function() {
                initCampaignChartWithMetric(campaignChartElement, campaignLabels, impressionsData, clicksData, ctrData, this.value);
            });
        }
        
        console.log('Campaign chart initialized successfully');
    } catch (error) {
        console.error('Error initializing campaign chart:', error);
    }
}

/**
 * Initialize campaign chart with the selected metric
 */
function initCampaignChartWithMetric(chartElement, labels, impressionsData, clicksData, ctrData, metric) {
    let chartData, chartLabel, chartColor;
    
    switch(metric) {
        case 'impressions':
            chartData = impressionsData;
            chartLabel = 'Impressions';
            chartColor = 'rgba(75, 192, 192, 0.7)';
            break;
        case 'ctr':
            chartData = ctrData;
            chartLabel = 'CTR (%)';
            chartColor = 'rgba(153, 102, 255, 0.7)';
            break;
        default: // clicks
            chartData = clicksData;
            chartLabel = 'Clicks';
            chartColor = 'rgba(255, 159, 64, 0.7)';
    }
    
    // Create or update chart
    const ctx = chartElement.getContext('2d');
    
    if (window.campaignChart) {
        window.campaignChart.data.datasets[0].data = chartData;
        window.campaignChart.data.datasets[0].label = chartLabel;
        window.campaignChart.data.datasets[0].backgroundColor = chartColor;
        window.campaignChart.update();
    } else {
        window.campaignChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: chartLabel,
                    data: chartData,
                    backgroundColor: chartColor,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                if (metric === 'ctr') {
                                    return value + '%';
                                }
                                return value;
                            }
                        }
                    }
                }
            }
        });
    }
}

/**
 * Set up event listeners for chart filters
 */
function setupChartFilters() {
    // Period filter
    const periodFilter = document.getElementById('period-filter');
    if (periodFilter) {
        periodFilter.addEventListener('change', function() {
            document.getElementById('chart-filter-form').submit();
        });
    }
    
    // Metric filter
    const metricFilter = document.getElementById('metric-filter');
    if (metricFilter) {
        metricFilter.addEventListener('change', function() {
            document.getElementById('chart-filter-form').submit();
        });
    }
}

/**
 * Calculate and display average metrics
 */
function calculateAverageMetrics() {
    const performanceChartElement = document.getElementById('performanceChart');
    if (!performanceChartElement) return;
    
    try {
        // Get chart data from the element's data attributes
        const impressionsData = JSON.parse(performanceChartElement.getAttribute('data-impressions') || '[]');
        const clicksData = JSON.parse(performanceChartElement.getAttribute('data-clicks') || '[]');
        
        // Calculate averages
        const calculateAverage = (data) => {
            if (!data || data.length === 0) return 0;
            const sum = data.reduce((a, b) => Number(a) + Number(b), 0);
            return Math.round(sum / data.length);
        };
        
        // Update average metrics display
        const avgImpressionsElement = document.getElementById('avgImpressions');
        const avgClicksElement = document.getElementById('avgClicks');
        
        if (avgImpressionsElement) {
            avgImpressionsElement.textContent = calculateAverage(impressionsData);
        }
        
        if (avgClicksElement) {
            avgClicksElement.textContent = calculateAverage(clicksData);
        }
        
        console.log('Average metrics calculated successfully');
    } catch (error) {
        console.error('Error calculating average metrics:', error);
    }
}
