/**
 * Notification Bell Fix
 * This script ensures the notification bell in the ad creation page works correctly
 */

document.addEventListener('DOMContentLoaded', function() {
    // Find the notification bell in the dashboard header
    const notificationBell = document.querySelector('.dashboard-actions .btn-icon');
    
    if (notificationBell) {
        console.log('Notification bell found in dashboard header');
        
        // Add click event listener
        notificationBell.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Show notification panel
            showNotificationPanel();
        });
    }
    
    // Function to show notification panel
    function showNotificationPanel() {
        // Check if we already have a notification panel
        let notificationPanel = document.querySelector('.dashboard-notification-panel');
        
        if (!notificationPanel) {
            // Create notification panel
            notificationPanel = document.createElement('div');
            notificationPanel.className = 'dashboard-notification-panel';
            
            // Add header
            const panelHeader = document.createElement('div');
            panelHeader.className = 'panel-header';
            panelHeader.innerHTML = `
                <h4>Notifications</h4>
                <button class="close-panel"><i class="fas fa-times"></i></button>
            `;
            notificationPanel.appendChild(panelHeader);
            
            // Add content
            const panelContent = document.createElement('div');
            panelContent.className = 'panel-content';
            
            // Fetch notifications
            fetchNotifications(panelContent);
            
            notificationPanel.appendChild(panelContent);
            
            // Add to document
            document.body.appendChild(notificationPanel);
            
            // Add event listener to close button
            const closeButton = notificationPanel.querySelector('.close-panel');
            if (closeButton) {
                closeButton.addEventListener('click', function() {
                    notificationPanel.classList.remove('show');
                    setTimeout(() => {
                        notificationPanel.remove();
                    }, 300);
                });
            }
            
            // Add event listener to close when clicking outside
            document.addEventListener('click', function(e) {
                if (notificationPanel && !notificationPanel.contains(e.target) && !e.target.closest('.btn-icon')) {
                    notificationPanel.classList.remove('show');
                    setTimeout(() => {
                        notificationPanel.remove();
                    }, 300);
                }
            });
            
            // Show panel with animation
            setTimeout(() => {
                notificationPanel.classList.add('show');
            }, 10);
        } else {
            // Toggle panel visibility
            if (notificationPanel.classList.contains('show')) {
                notificationPanel.classList.remove('show');
                setTimeout(() => {
                    notificationPanel.remove();
                }, 300);
            } else {
                notificationPanel.classList.add('show');
            }
        }
    }
    
    // Function to fetch notifications
    function fetchNotifications(container) {
        // Show loading spinner
        container.innerHTML = `
            <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
                <p>Loading notifications...</p>
            </div>
        `;
        
        // Fetch notifications from API
        fetch('/notifications/api/list/')
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to fetch notifications');
                }
                return response.json();
            })
            .then(data => {
                // Clear loading spinner
                container.innerHTML = '';
                
                if (data.notifications && data.notifications.length > 0) {
                    // Create notification items
                    data.notifications.forEach(notification => {
                        const notificationItem = createNotificationItem(notification);
                        container.appendChild(notificationItem);
                    });
                    
                    // Add view all link
                    const viewAllLink = document.createElement('a');
                    viewAllLink.href = '/notifications/';
                    viewAllLink.className = 'view-all-link';
                    viewAllLink.innerHTML = 'View all notifications';
                    container.appendChild(viewAllLink);
                } else {
                    // Show empty state
                    container.innerHTML = `
                        <div class="empty-state">
                            <i class="fas fa-bell-slash"></i>
                            <p>No notifications</p>
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Error fetching notifications:', error);
                
                // Show error state
                container.innerHTML = `
                    <div class="error-state">
                        <i class="fas fa-exclamation-circle"></i>
                        <p>Failed to load notifications</p>
                        <button class="btn btn-sm btn-primary retry-btn">Retry</button>
                    </div>
                `;
                
                // Add retry button functionality
                const retryButton = container.querySelector('.retry-btn');
                if (retryButton) {
                    retryButton.addEventListener('click', function() {
                        fetchNotifications(container);
                    });
                }
            });
    }
    
    // Function to create notification item
    function createNotificationItem(notification) {
        const item = document.createElement('div');
        item.className = `notification-item ${notification.is_read ? 'read' : 'unread'} ${notification.notification_type}`;
        
        item.innerHTML = `
            <div class="notification-icon">
                <i class="fas ${getIconForType(notification.notification_type)}"></i>
            </div>
            <div class="notification-content">
                <div class="notification-title">${notification.title}</div>
                <div class="notification-message">${notification.message}</div>
                <div class="notification-time">${notification.time_since}</div>
            </div>
        `;
        
        // Add click event to mark as read and navigate
        item.addEventListener('click', function() {
            // Mark as read
            if (!notification.is_read) {
                fetch(`/notifications/${notification.id}/mark-as-read/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': getCsrfToken(),
                        'Content-Type': 'application/x-www-form-urlencoded'
                    }
                });
            }
            
            // Navigate to action URL if available
            if (notification.action_url) {
                window.location.href = notification.action_url;
            }
        });
        
        return item;
    }
    
    // Function to get icon for notification type
    function getIconForType(type) {
        switch (type) {
            case 'success':
                return 'fa-check-circle';
            case 'warning':
                return 'fa-exclamation-triangle';
            case 'error':
                return 'fa-exclamation-circle';
            case 'info':
            default:
                return 'fa-info-circle';
        }
    }
    
    // Function to get CSRF token
    function getCsrfToken() {
        const cookieValue = document.cookie
            .split('; ')
            .find(row => row.startsWith('csrftoken='))
            ?.split('=')[1];
        return cookieValue || '';
    }
});
