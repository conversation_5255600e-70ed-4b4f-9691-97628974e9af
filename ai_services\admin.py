"""
AI Services Admin
"""
from django.contrib import admin
from django.urls import path
from django.shortcuts import render
from django.contrib.admin.views.decorators import staff_member_required
from django.conf import settings

# We don't need to register any models for this app
# This file is just for adding a custom admin view

# The dashboard view is already defined in urls.py, so we don't need to duplicate it here.
# The admin site integration is handled through the custom template in templates/admin/index.html
# which adds a link to the AI Services Dashboard in the admin sidebar.

# No need to register anything with the admin site as we're using a custom URL pattern
# defined in ai_services/urls.py
