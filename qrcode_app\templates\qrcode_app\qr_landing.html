<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Code Landing - {{ qr_code.name }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            {% if branding %}
            background: {{ branding.secondary_color }};
            color: {{ branding.primary_color }};
            {% else %}
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            {% endif %}
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .landing-container {
            {% if branding %}
            background: {{ branding.secondary_color }};
            border: 2px solid {{ branding.primary_color }};
            {% else %}
            background: white;
            {% endif %}
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 2rem;
            max-width: 500px;
            width: 90%;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .landing-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            {% if branding %}
            background: {{ branding.accent_color }};
            {% else %}
            background: linear-gradient(90deg, #667eea, #764ba2);
            {% endif %}
        }

        .scanner-warning {
            background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
            border: none;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 8px 16px rgba(253, 203, 110, 0.3);
        }

        .scanner-warning .warning-icon {
            font-size: 2rem;
            color: #e17055;
            margin-bottom: 1rem;
        }

        .scanner-warning h4 {
            color: #2d3436;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .scanner-warning p {
            color: #636e72;
            margin-bottom: 0;
            line-height: 1.6;
        }

        .qr-info {
            margin-bottom: 2rem;
        }

        .qr-info h2 {
            color: #2d3436;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .qr-info .qr-type {
            background: #74b9ff;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
            display: inline-block;
            margin-bottom: 1rem;
        }

        .redirect-info {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .redirect-info .redirect-icon {
            font-size: 2rem;
            color: #00b894;
            margin-bottom: 1rem;
        }

        .countdown {
            font-size: 1.2rem;
            font-weight: 600;
            color: #667eea;
            margin-bottom: 1rem;
        }

        .destination-url {
            background: white;
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 1rem;
            word-break: break-all;
            color: #636e72;
            font-family: monospace;
            margin-bottom: 1rem;
        }

        .action-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #636e72;
            border: none;
            border-radius: 25px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: #2d3436;
            transform: translateY(-2px);
        }

        .geo-info {
            background: #e8f4fd;
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
            font-size: 0.9rem;
            color: #636e72;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="landing-container fade-in">
        {% if show_scanner_warning %}
        <div id="scanner-warning" class="scanner-warning">
            <div class="warning-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <h4>Scanner App Detected</h4>
            <p>
                <i class="fas fa-camera me-2"></i>
                For the best experience, use your phone's native camera app to scan QR codes.
                Third-party scanner apps may show ads or have security concerns.
            </p>
        </div>
        {% endif %}

        <!-- Branded Header Section -->
        {% if branding %}
        <div class="brand-header" style="margin-bottom: 2rem;">
            {% if branding.get_logo_url %}
            <img src="{{ branding.get_logo_url }}" alt="{{ branding.company_name }}" style="max-height: 60px; margin-bottom: 1rem;">
            {% endif %}
            {% if branding.company_name %}
            <h3 style="color: {{ branding.primary_color }}; margin-bottom: 0.5rem;">{{ branding.company_name }}</h3>
            {% endif %}
            {% if branding.tagline %}
            <p style="color: {{ branding.primary_color }}; opacity: 0.8; margin-bottom: 1rem;">{{ branding.tagline }}</p>
            {% endif %}
        </div>
        {% endif %}

        <div class="qr-info">
            <h2 style="{% if branding %}color: {{ branding.primary_color }};{% endif %}">{{ qr_code.name }}</h2>
            <span class="qr-type" style="{% if branding %}color: {{ branding.primary_color }}; opacity: 0.7;{% endif %}">{{ qr_code.get_qr_type_display }}</span>
        </div>

        {% if is_url_type %}
        <div class="redirect-info">
            <div class="redirect-icon">
                <i class="fas fa-external-link-alt"></i>
            </div>
            <div class="countdown" id="countdown">
                Redirecting in <span id="timer">3</span> seconds...
            </div>
            <div class="destination-url">
                {{ destination_url }}
            </div>
            <div class="action-buttons">
                <button class="btn btn-primary" onclick="redirectNow()"
                        style="{% if branding %}background-color: {{ branding.accent_color }}; border-color: {{ branding.accent_color }};{% endif %}">
                    <i class="fas fa-arrow-right me-2"></i>Go Now
                </button>
                <button class="btn btn-secondary" onclick="cancelRedirect()"
                        style="{% if branding %}background-color: {{ branding.primary_color }}; border-color: {{ branding.primary_color }};{% endif %}">
                    <i class="fas fa-times me-2"></i>Cancel
                </button>
            </div>
        </div>
        {% else %}
        <div class="redirect-info">
            <div class="redirect-icon">
                <i class="fas fa-info-circle"></i>
            </div>
            <h4>QR Code Content</h4>
            <div class="destination-url">
                {{ qr_code.data }}
            </div>
        </div>
        {% endif %}

        {% if geo_data.city != 'Unknown' %}
        <div class="geo-info">
            <i class="fas fa-map-marker-alt me-2"></i>
            Scanned from: {{ geo_data.city }}{% if geo_data.region %}, {{ geo_data.region }}{% endif %}{% if geo_data.country %}, {{ geo_data.country }}{% endif %}
        </div>
        {% endif %}

        <!-- Monetization: Powered By Footer -->
        {% if show_powered_by %}
        <div class="powered-by" style="margin-top: 2rem; padding-top: 1rem; border-top: 1px solid #eee; opacity: 0.7;">
            <small>
                <i class="fas fa-qrcode me-1"></i>
                Powered by <a href="/" style="color: #667eea; text-decoration: none;">QR Generator Pro</a>
                <br>
                <a href="{% url 'pricing' %}" style="color: #667eea; text-decoration: none; font-size: 0.8em;">
                    Get your own branded QR codes →
                </a>
            </small>
        </div>
        {% endif %}

        <!-- Premium Footer -->
        {% if branding and branding.footer_text %}
        <div class="brand-footer" style="margin-top: 2rem; padding-top: 1rem; border-top: 1px solid {{ branding.primary_color }}; opacity: 0.8;">
            <small style="color: {{ branding.primary_color }};">{{ branding.footer_text }}</small>
        </div>
        {% endif %}
    </div>

    <script>
        // Scanner warning management
        const warning = document.getElementById('scanner-warning');
        if (warning && localStorage.getItem('scannerWarned')) {
            warning.style.display = 'none';
        } else if (warning) {
            localStorage.setItem('scannerWarned', 'true');
        }

        {% if is_url_type %}
        // Redirect functionality
        let countdown = 3;
        let redirectTimer;
        let cancelled = false;

        function updateCountdown() {
            const timerElement = document.getElementById('timer');
            const countdownElement = document.getElementById('countdown');

            if (cancelled) return;

            if (countdown > 0) {
                timerElement.textContent = countdown;
                countdown--;
                redirectTimer = setTimeout(updateCountdown, 1000);
            } else {
                countdownElement.innerHTML = '<span class="loading-spinner"></span> Redirecting...';
                setTimeout(() => {
                    if (!cancelled) {
                        window.location.href = '{{ destination_url|escapejs }}';
                    }
                }, 500);
            }
        }

        function redirectNow() {
            cancelled = false;
            clearTimeout(redirectTimer);
            document.getElementById('countdown').innerHTML = '<span class="loading-spinner"></span> Redirecting...';
            setTimeout(() => {
                window.location.href = '{{ destination_url|escapejs }}';
            }, 500);
        }

        function cancelRedirect() {
            cancelled = true;
            clearTimeout(redirectTimer);
            document.getElementById('countdown').innerHTML = '<i class="fas fa-hand-paper me-2"></i>Redirect cancelled';
        }

        // Start countdown
        updateCountdown();
        {% endif %}
    </script>
</body>
</html>
