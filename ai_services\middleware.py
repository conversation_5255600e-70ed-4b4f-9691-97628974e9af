"""
AI Services Middleware
Provides middleware to expose AI provider status to templates
"""
import json
import logging
from django.utils.deprecation import MiddlewareMixin

logger = logging.getLogger(__name__)

class AIProviderStatusMiddleware(MiddlewareMixin):
    """
    Middleware to expose AI provider status to templates
    
    This middleware adds AI provider status to the request context,
    making it available to templates and JavaScript.
    """
    
    def process_request(self, request):
        """
        Process the request and add AI provider status
        
        Args:
            request: The HTTP request
        """
        # Import here to avoid circular imports
        from ai_services.health import get_all_provider_status, get_best_available_provider
        
        # Get provider status
        provider_status = get_all_provider_status()
        best_provider = get_best_available_provider()
        
        # Add to request
        request.ai_provider_status = provider_status
        request.best_ai_provider = best_provider
        
        # Return None to continue processing
        return None
    
    def process_template_response(self, request, response):
        """
        Process the template response and add AI provider status
        
        Args:
            request: The HTTP request
            response: The HTTP response
            
        Returns:
            The modified response
        """
        # Check if the response has a context
        if hasattr(response, 'context_data'):
            # Add provider status to context
            if hasattr(request, 'ai_provider_status'):
                response.context_data['ai_provider_status'] = request.ai_provider_status
            
            # Add best provider to context
            if hasattr(request, 'best_ai_provider'):
                response.context_data['best_ai_provider'] = request.best_ai_provider
            
            # Add JSON-encoded provider status for JavaScript
            if hasattr(request, 'ai_provider_status'):
                # Create a simplified version for JavaScript
                js_status = {}
                for provider, status in request.ai_provider_status.items():
                    js_status[provider] = {
                        'available': status['available'],
                        'responseTime': status['response_time']
                    }
                
                response.context_data['ai_provider_status_json'] = json.dumps(js_status)
        
        return response
