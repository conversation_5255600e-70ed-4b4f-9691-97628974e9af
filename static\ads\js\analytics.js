/**
 * Ad Analytics JavaScript
 * Handles chart initialization and rendering for individual ad analytics
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all charts
    initializeCharts();
    
    // Set up period controls
    setupPeriodControls();
});

/**
 * Initialize all charts on the page
 */
function initializeCharts() {
    // Performance Chart
    initializePerformanceChart();
    
    // Device Chart
    initializeDeviceChart();
    
    // Location Chart
    initializeLocationChart();
}

/**
 * Initialize the performance chart
 */
function initializePerformanceChart() {
    const performanceChartElement = document.getElementById('performanceChart');
    if (!performanceChartElement) return;
    
    try {
        // Get chart data from the element's data attributes
        const dates = JSON.parse(performanceChartElement.getAttribute('data-dates') || '[]');
        const impressions = JSON.parse(performanceChartElement.getAttribute('data-impressions') || '[]');
        const clicks = JSON.parse(performanceChartElement.getAttribute('data-clicks') || '[]');
        
        // If no data, provide default empty arrays
        if (dates.length === 0) {
            const today = new Date();
            dates.push(today.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }));
            impressions.push(0);
            clicks.push(0);
        }
        
        // Create the chart
        const ctx = performanceChartElement.getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: dates,
                datasets: [
                    {
                        label: 'Impressions',
                        data: impressions,
                        borderColor: 'rgba(75, 192, 192, 1)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        borderWidth: 2,
                        tension: 0.3,
                        fill: true,
                        pointBackgroundColor: 'rgba(75, 192, 192, 1)',
                        pointRadius: 3,
                        pointHoverRadius: 5,
                        yAxisID: 'y'
                    },
                    {
                        label: 'Clicks',
                        data: clicks,
                        borderColor: 'rgba(255, 159, 64, 1)',
                        backgroundColor: 'rgba(255, 159, 64, 0.2)',
                        borderWidth: 2,
                        tension: 0.3,
                        fill: true,
                        pointBackgroundColor: 'rgba(255, 159, 64, 1)',
                        pointRadius: 3,
                        pointHoverRadius: 5,
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    }
                },
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Impressions'
                        },
                        beginAtZero: true
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Clicks'
                        },
                        beginAtZero: true,
                        grid: {
                            drawOnChartArea: false
                        }
                    }
                }
            }
        });
        
        console.log('Performance chart initialized successfully');
    } catch (error) {
        console.error('Error initializing performance chart:', error);
    }
}

/**
 * Initialize the device breakdown chart
 */
function initializeDeviceChart() {
    const deviceChartElement = document.getElementById('deviceChart');
    if (!deviceChartElement) return;
    
    try {
        // Get chart data from the element's data attributes
        let devices = JSON.parse(deviceChartElement.getAttribute('data-devices') || '[]');
        let counts = JSON.parse(deviceChartElement.getAttribute('data-counts') || '[]');
        
        // If no data, provide default values
        if (devices.length === 0 || counts.length === 0) {
            devices = ['desktop', 'mobile', 'tablet'];
            counts = [0, 0, 0];
        }
        
        // Format device labels to be capitalized
        const formattedDevices = devices.map(device => 
            device.charAt(0).toUpperCase() + device.slice(1)
        );
        
        // Define colors for the chart
        const backgroundColors = [
            'rgba(255, 99, 132, 0.7)',
            'rgba(54, 162, 235, 0.7)',
            'rgba(255, 206, 86, 0.7)',
            'rgba(75, 192, 192, 0.7)',
            'rgba(153, 102, 255, 0.7)'
        ];
        
        // Create the chart
        const ctx = deviceChartElement.getContext('2d');
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: formattedDevices,
                datasets: [{
                    data: counts,
                    backgroundColor: backgroundColors,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    }
                }
            }
        });
        
        console.log('Device chart initialized successfully');
    } catch (error) {
        console.error('Error initializing device chart:', error);
    }
}

/**
 * Initialize the location chart
 */
function initializeLocationChart() {
    const locationChartElement = document.getElementById('locationChart');
    if (!locationChartElement) return;
    
    try {
        // Get chart data from the element's data attributes
        let locations = JSON.parse(locationChartElement.getAttribute('data-locations') || '[]');
        let counts = JSON.parse(locationChartElement.getAttribute('data-counts') || '[]');
        
        // If no data, provide default values
        if (locations.length === 0 || counts.length === 0) {
            locations = ['Nairobi', 'Mombasa', 'Kisumu'];
            counts = [0, 0, 0];
        }
        
        // Define colors for the chart
        const backgroundColors = [
            'rgba(255, 99, 132, 0.7)',
            'rgba(54, 162, 235, 0.7)',
            'rgba(255, 206, 86, 0.7)',
            'rgba(75, 192, 192, 0.7)',
            'rgba(153, 102, 255, 0.7)'
        ];
        
        // Create the chart
        const ctx = locationChartElement.getContext('2d');
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: locations,
                datasets: [{
                    label: 'Impressions by Location',
                    data: counts,
                    backgroundColor: backgroundColors,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        
        console.log('Location chart initialized successfully');
    } catch (error) {
        console.error('Error initializing location chart:', error);
    }
}

/**
 * Set up period controls for filtering chart data
 */
function setupPeriodControls() {
    const periodButtons = document.querySelectorAll('.chart-control');
    if (periodButtons.length === 0) return;
    
    periodButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            periodButtons.forEach(btn => btn.classList.remove('active'));
            
            // Add active class to clicked button
            this.classList.add('active');
            
            // Get selected period
            const period = this.getAttribute('data-period');
            
            // In a real implementation, this would filter the chart data
            // For now, just log the selected period
            console.log('Selected period:', period);
        });
    });
}
