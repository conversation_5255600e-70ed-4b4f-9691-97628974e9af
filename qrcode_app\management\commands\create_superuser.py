from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.db.utils import IntegrityError

class Command(BaseCommand):
    help = 'Create a superuser'

    def handle(self, *args, **options):
        try:
            # Check if user already exists
            if User.objects.filter(username='peter').exists():
                user = User.objects.get(username='peter')
                user.set_password('2587')
                user.is_staff = True
                user.is_superuser = True
                user.email = '<EMAIL>'
                user.save()
                self.stdout.write(self.style.SUCCESS("Superuser 'peter' updated successfully!"))
            else:
                # Create superuser
                User.objects.create_superuser(
                    username='peter',
                    email='<EMAIL>',
                    password='2587'
                )
                self.stdout.write(self.style.SUCCESS("Superuser 'peter' created successfully!"))
        except IntegrityError as e:
            self.stdout.write(self.style.ERROR(f"Error creating superuser: {e}"))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Unexpected error: {e}"))
