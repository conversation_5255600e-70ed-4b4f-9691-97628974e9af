{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dropdown Test - Enterprise QR</title>

    <!-- CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{% static 'css/new_nav.css' %}">
    <link rel="stylesheet" href="{% static 'css/new_nav_dropdowns.css' %}">

    <style>
        body {
            margin: 0;
            padding: 0;
            background: #f8f9fa;
            font-family: 'Inter', sans-serif;
        }

        .test-content {
            padding: 100px 20px 50px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .test-card {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-working { background: #28a745; }
        .status-broken { background: #dc3545; }
        .status-unknown { background: #ffc107; }
    </style>
</head>
<body>
    <!-- Include the navbar -->
    {% include 'includes/new_navbar.html' %}

    <div class="test-content">
        <div class="test-card">
            <h1><i class="fas fa-bug"></i> Dropdown Functionality Test</h1>
            <p class="lead">This page is designed to test and debug the navigation dropdown functionality.</p>

            <h3>Test Instructions:</h3>
            <ol>
                <li><strong>Hover Test:</strong> Hover over "Admin Panel", "Solutions", "QR Pro", "Premium", or "Advertise" in the navigation</li>
                <li><strong>Click Test:</strong> Click on any dropdown menu item to test click functionality</li>
                <li><strong>Mobile Test:</strong> Resize your browser to mobile size and test mobile dropdowns</li>
                <li><strong>Console Test:</strong> Open browser developer tools (F12) and check the Console tab for debug messages</li>
            </ol>

            <h3>Expected Behavior:</h3>
            <ul>
                <li>Dropdowns should appear on hover (desktop) or click (mobile)</li>
                <li>Only one dropdown should be open at a time</li>
                <li>Dropdowns should close when clicking outside</li>
                <li>Console should show debug messages for each interaction</li>
            </ul>

            <div class="debug-info">
                <h4>Debug Information:</h4>
                <div id="debug-output">
                    <p>Open browser console (F12) to see detailed debug information...</p>
                </div>
            </div>
        </div>

        <div class="test-card">
            <h3>Dropdown Status Check</h3>
            <div id="dropdown-status">
                <p><span class="status-indicator status-unknown"></span> Checking dropdown elements...</p>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Load navigation scripts -->
    <script src="{% static 'js/dom-ready-init.js' %}"></script>
    <script src="{% static 'js/shared-utils.js' %}"></script>
    <script src="{% static 'js/new_nav.js' %}"></script>

    <script>
        // Test script to check dropdown functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('=== DROPDOWN TEST PAGE LOADED ===');

            // Check if dropdown elements exist
            const dropdownItems = document.querySelectorAll('.new-navbar-item.has-dropdown');
            const statusDiv = document.getElementById('dropdown-status');

            console.log('Found dropdown items:', dropdownItems.length);

            if (dropdownItems.length > 0) {
                statusDiv.innerHTML = `
                    <p><span class="status-indicator status-working"></span> Found ${dropdownItems.length} dropdown items</p>
                `;

                dropdownItems.forEach((item, index) => {
                    const menu = item.querySelector('.new-navbar-dropdown');
                    const toggle = item.querySelector('.new-navbar-link');
                    const text = toggle ? toggle.textContent.trim() : 'Unknown';

                    console.log(`Dropdown ${index + 1}: "${text}"`, {
                        item: item,
                        menu: menu,
                        toggle: toggle,
                        hasMenu: !!menu,
                        hasToggle: !!toggle
                    });

                    statusDiv.innerHTML += `
                        <p><span class="status-indicator ${menu && toggle ? 'status-working' : 'status-broken'}"></span>
                        Dropdown ${index + 1}: "${text}" - ${menu && toggle ? 'OK' : 'MISSING ELEMENTS'}</p>
                    `;
                });
            } else {
                statusDiv.innerHTML = `
                    <p><span class="status-indicator status-broken"></span> No dropdown items found!</p>
                `;
            }

            // Test click functionality
            setTimeout(() => {
                console.log('=== TESTING CLICK FUNCTIONALITY ===');
                dropdownItems.forEach((item, index) => {
                    const toggle = item.querySelector('.new-navbar-link');
                    if (toggle) {
                        console.log(`Setting up test click listener for dropdown ${index + 1}`);
                        toggle.addEventListener('click', function(e) {
                            console.log(`TEST: Click detected on dropdown ${index + 1}`, e);
                        });
                    }
                });
            }, 1000);
        });
    </script>
</body>
</html>
