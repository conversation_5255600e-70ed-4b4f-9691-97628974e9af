from django.core.management.base import BaseCommand
import os
import re

class Command(BaseCommand):
    help = 'Updates the settings.py file to include the ads context processor'

    def handle(self, *args, **options):
        # Find the settings.py file
        settings_file = None
        for root, dirs, files in os.walk('.'):
            for file in files:
                if file == 'settings.py':
                    settings_file = os.path.join(root, file)
                    break
            if settings_file:
                break
        
        if not settings_file:
            self.stdout.write(self.style.ERROR('Could not find settings.py file'))
            return
        
        self.stdout.write(f'Found settings.py at {settings_file}')
        
        # Read the settings file
        with open(settings_file, 'r') as f:
            content = f.read()
        
        # Check if the context processor is already added
        if 'ads.context_processors.active_ads' in content:
            self.stdout.write(self.style.SUCCESS('Context processor already added'))
            return
        
        # Find the TEMPLATES setting
        templates_pattern = r"TEMPLATES\s*=\s*\[\s*{\s*'BACKEND'.*?'OPTIONS':\s*{\s*'context_processors':\s*\[(.*?)\]\s*}"
        match = re.search(templates_pattern, content, re.DOTALL)
        
        if not match:
            self.stdout.write(self.style.ERROR('Could not find TEMPLATES setting'))
            return
        
        # Get the current context processors
        context_processors = match.group(1)
        
        # Add our context processor
        if context_processors.strip().endswith(','):
            new_context_processors = context_processors + "\n        'ads.context_processors.active_ads',"
        else:
            new_context_processors = context_processors + ",\n        'ads.context_processors.active_ads',"
        
        # Replace the context processors
        new_content = content.replace(context_processors, new_context_processors)
        
        # Write the updated content back to the file
        with open(settings_file, 'w') as f:
            f.write(new_content)
        
        self.stdout.write(self.style.SUCCESS('Successfully added context processor to settings.py'))
