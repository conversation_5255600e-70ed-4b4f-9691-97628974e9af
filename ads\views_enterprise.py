from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.db.models import Sum
from django.contrib.auth.models import User
from django.utils import timezone
from django.contrib import messages
from django.urls import reverse
from datetime import timed<PERSON><PERSON>, datetime
from decimal import Decimal
import requests
import logging
from .models import Ad, AdType, AdLocation, Transaction, AdAnalytics, Campaign

logger = logging.getLogger(__name__)

from .utils.ai_utils import with_retry, get_user_friendly_error_message, cache_ai_response, generate_cache_key

def call_local_ai(prompt: str, model: str, max_retries: int = 3, use_cache: bool = True) -> dict:
    """
    Call the AI Engine to generate ad content with robust fallback logic.

    This function now uses the FallbackAIClient to try multiple AI providers in sequence:
    1. MistralAIClient (mistralai lib)
    2. MistralDirectClient (raw requests fallback)
    3. OpenAIClient
    4. Hardcoded fallbacks

    Args:
        prompt: The prompt for ad generation
        model: The model to use for generation
        max_retries: Maximum number of retry attempts
        use_cache: Whether to use caching for identical requests

    Returns:
        A dictionary with the generated text and metadata, or an error message
    """
    # Import the FallbackAIClient
    from ai_services.clients import FallbackAIClient
    from ai_services.cache import generate_cache_key, get_from_cache, save_to_cache

    # Extract parameters from the prompt
    language = "english"  # Default language
    business_type = "general"  # Default business type
    target_audience = "general"  # Default target audience
    tone = "professional"  # Default tone

    # Extract language from the prompt if possible
    if "language" in prompt.lower():
        for lang in ["english", "swahili", "sheng"]:
            if lang in prompt.lower():
                language = lang
                break

    # Extract business type from the prompt if possible
    if "business type" in prompt.lower() or "product is" in prompt.lower():
        lines = prompt.split('\n')
        for line in lines:
            if "business type" in line.lower() or "product is" in line.lower():
                parts = line.split(':')
                if len(parts) > 1:
                    business_type = parts[1].strip()
                    break

    # Extract target audience from the prompt if possible
    if "target audience" in prompt.lower():
        lines = prompt.split('\n')
        for line in lines:
            if "target audience" in line.lower():
                parts = line.split(':')
                if len(parts) > 1:
                    target_audience = parts[1].strip()
                    break

    # Extract tone from the prompt if possible
    if "tone" in prompt.lower():
        lines = prompt.split('\n')
        for line in lines:
            if "tone" in line.lower():
                parts = line.split(':')
                if len(parts) > 1:
                    tone = parts[1].strip()
                    break

    # Apply caching if enabled
    if use_cache:
        # Generate a cache key for this request
        cache_key = generate_cache_key(
            prompt=prompt,
            model=model,
            language=language,
            business_type=business_type,
            target_audience=target_audience,
            tone=tone,
            service="fallback_ai_client"
        )

        # Try to get from cache first
        cached_result = get_from_cache(cache_key)
        if cached_result:
            logger.info(f"Retrieved AI response from cache for model {model}")
            return cached_result

    # Create a FallbackAIClient instance
    try:
        logger.info(f"Creating FallbackAIClient for generating ad suggestions")
        client = FallbackAIClient()

        # Extract title from the prompt if available
        ad_title = ""
        if "ad title is:" in prompt.lower():
            lines = prompt.split('\n')
            for line in lines:
                if "ad title is:" in line.lower():
                    parts = line.split(':')
                    if len(parts) > 1:
                        ad_title = parts[1].strip()
                        break

        # Generate suggestions using the FallbackAIClient
        logger.info(f"Generating ad suggestions with FallbackAIClient: language={language}, business_type={business_type}, target_audience={target_audience}, tone={tone}")
        suggestions = client.generate_ad_suggestions(
            language=language,
            business_type=business_type,
            target_audience=target_audience,
            tone=tone,
            num_suggestions=1,  # We only need one suggestion for this use case
            ad_title=ad_title
        )

        # Check if we got any suggestions
        if suggestions and len(suggestions) > 0:
            # Get the first suggestion
            suggestion = suggestions[0]

            # Create a result dictionary
            result = {
                "result": f"Title: {suggestion['title']}\nContent: {suggestion['content']}",
                "model": suggestion.get('provider', model),
                "processing_time": suggestion.get('processing_time', 0),
                "success": True,
                "error": None,
                "fallback": suggestion.get('fallback', False)
            }

            # Cache the result if caching is enabled
            if use_cache:
                save_to_cache(cache_key, result)
                logger.info(f"Cached AI response for model {result['model']}")

            return result
        else:
            # If no suggestions were generated, return an error
            logger.error("No suggestions generated by FallbackAIClient")
            return {
                "result": "AI error: No suggestions could be generated",
                "model": model,
                "processing_time": 0,
                "success": False,
                "error": "No suggestions generated",
                "fallback": True
            }

    except Exception as e:
        # Log the error
        logger.error(f"Error generating ad suggestions with FallbackAIClient: {str(e)}", exc_info=True)

        # Get user-friendly error message
        error_message = get_user_friendly_error_message(e)

        # Return error information
        return {
            "result": f"AI error: {error_message}",
            "model": model,
            "processing_time": 0,
            "success": False,
            "error": str(e),
            "fallback": True
        }

def get_user_balance(user):
    """
    Get the user's current balance
    This is a placeholder function - implement your actual balance logic here
    """
    # For demonstration purposes, we'll return a fixed balance
    # In a real application, you would query the user's balance from your payment system
    return Decimal('100.00')  # Default balance of 100 KSH

@login_required
def dashboard_enterprise(request):
    """
    Enterprise version of the dashboard view
    """
    # Get all active ad types
    ad_types = AdType.objects.filter(is_active=True)

    # Get user's ads
    user_ads = Ad.objects.filter(user=request.user)

    # Get counts for different statuses
    active_ads_count = user_ads.filter(status='active').count()
    pending_ads_count = user_ads.filter(status='pending').count()
    draft_ads_count = user_ads.filter(status='draft').count()
    total_ads_count = user_ads.count()

    # Get recent ads
    recent_ads = user_ads.order_by('-created_at')[:5]

    # Get top performing ads
    top_ads = []
    ads_with_analytics = user_ads.order_by('-impressions')[:5]

    for ad in ads_with_analytics:
        ctr = 0
        if ad.impressions > 0:
            ctr = (ad.clicks / ad.impressions) * 100
        top_ads.append({
            'title': ad.title,
            'slug': ad.slug,
            'impressions': ad.impressions,
            'clicks': ad.clicks,
            'ctr': ctr
        })

    # Get overall performance metrics
    total_impressions = user_ads.aggregate(Sum('impressions'))['impressions__sum'] or 0
    total_clicks = user_ads.aggregate(Sum('clicks'))['clicks__sum'] or 0
    total_conversions = user_ads.aggregate(Sum('conversion_count'))['conversion_count__sum'] or 0
    ctr = 0
    if total_impressions > 0:
        ctr = (total_clicks / total_impressions) * 100

    # Calculate trends
    today = timezone.now().date()
    last_30_days = today - timedelta(days=30)
    previous_30_days = last_30_days - timedelta(days=30)

    # Get analytics for current period
    current_analytics = AdAnalytics.objects.filter(
        ad__in=user_ads,
        date__gte=last_30_days,
        date__lte=today
    )

    # Get analytics for previous period
    previous_analytics = AdAnalytics.objects.filter(
        ad__in=user_ads,
        date__gte=previous_30_days,
        date__lte=last_30_days - timedelta(days=1)
    )

    # Calculate metrics for current period
    current_impressions = current_analytics.aggregate(Sum('impressions'))['impressions__sum'] or 0
    current_clicks = current_analytics.aggregate(Sum('clicks'))['clicks__sum'] or 0
    current_conversions = current_analytics.aggregate(Sum('conversion_count'))['conversion_count__sum'] or 0
    current_ctr = (current_clicks / current_impressions * 100) if current_impressions > 0 else 0

    # Calculate metrics for previous period
    previous_impressions = previous_analytics.aggregate(Sum('impressions'))['impressions__sum'] or 0
    previous_clicks = previous_analytics.aggregate(Sum('clicks'))['clicks__sum'] or 0
    previous_conversions = previous_analytics.aggregate(Sum('conversion_count'))['conversion_count__sum'] or 0
    previous_ctr = (previous_clicks / previous_impressions * 100) if previous_impressions > 0 else 0

    # Calculate trends
    impression_trend = calculate_trend(current_impressions, previous_impressions)
    click_trend = calculate_trend(current_clicks, previous_clicks)
    ctr_trend = calculate_trend(current_ctr, previous_ctr)
    conversion_trend = calculate_trend(current_conversions, previous_conversions)

    # Get unread notification count for the user
    try:
        from notifications.services import NotificationService
        notifications_count = NotificationService.get_user_notifications(
            user=request.user,
            include_deleted=False,
            include_read=False
        ).count()
    except ImportError:
        notifications_count = 0

    context = {
        'active_ads_count': active_ads_count,
        'pending_ads_count': pending_ads_count,
        'draft_ads_count': draft_ads_count,
        'total_ads_count': total_ads_count,
        'recent_ads': recent_ads,
        'top_ads': top_ads,
        'total_impressions': total_impressions,
        'total_clicks': total_clicks,
        'total_conversions': total_conversions,
        'ctr': ctr,
        'impression_trend': impression_trend,
        'click_trend': click_trend,
        'ctr_trend': ctr_trend,
        'conversion_trend': conversion_trend,
        'notifications_count': notifications_count,
        'ad_types': ad_types,
    }

    # Add admin-specific data for superusers
    if request.user.is_superuser:
        # Get total user count
        user_count = User.objects.count()

        # Get total ads count (all users)
        all_ads_count = Ad.objects.count()

        # Get pending approval count
        pending_approval_count = Ad.objects.filter(status='pending').count()

        # Get total revenue
        total_revenue = Transaction.objects.filter(status='approved').aggregate(Sum('amount'))['amount__sum'] or 0

        # Add admin data to context
        context.update({
            'user_count': user_count,
            'all_ads_count': all_ads_count,
            'pending_approval_count': pending_approval_count,
            'total_revenue': total_revenue,
            'recent_admin_activities': [],  # Placeholder for now
        })

    return render(request, 'ads/dashboard_enterprise.html', context)

# Helper function to calculate trend percentage
def calculate_trend(current, previous):
    """
    Calculate percentage change between current and previous values
    """
    if previous == 0:
        return 100 if current > 0 else 0

    change = ((current - previous) / previous) * 100
    return round(change, 1)

@login_required
def ad_create_enterprise(request):
    """
    Create a new advertisement (Enterprise version)
    This is the main ad creation view used throughout the application.
    """
    # Get all active ad types and locations
    ad_types = AdType.objects.filter(is_active=True)
    ad_locations = AdLocation.objects.filter(is_active=True)

    # Get user's campaigns
    campaigns = Campaign.objects.filter(user=request.user)

    if request.method == 'POST':
        # Process form data
        title = request.POST.get('title')
        ad_type_id = request.POST.get('ad_type')
        campaign_id = request.POST.get('campaign')
        ad_location_id = request.POST.get('ad_location')
        duration_option = request.POST.get('duration_option')
        start_date = request.POST.get('start_date')
        start_time = request.POST.get('start_time')
        end_date = request.POST.get('end_date')
        end_time = request.POST.get('end_time')
        content = request.POST.get('content')
        cta_link = request.POST.get('cta_link')
        target_location = request.POST.get('target_location')
        target_audience = request.POST.get('target_audience')
        requires_ai = request.POST.get('requires_ai') == 'true'
        wants_social = request.POST.get('wants_social') == 'true'
        base_pricing = request.POST.get('base_pricing')
        final_pricing = request.POST.get('final_pricing')

        # Process AI-related data
        use_smart_engine = request.POST.get('use_smart_engine') == 'on'
        ai_language = request.POST.get('ai_language')
        ai_suggestion_id = request.POST.get('ai_suggestion')

        # Get the used_ai value directly from the form
        used_ai_str = request.POST.get('used_ai')
        if used_ai_str == 'true':
            used_ai = True
        else:
            # Fallback to the original logic
            used_ai = use_smart_engine and ai_suggestion_id

        # Ensure used_ai is always a boolean
        used_ai = bool(used_ai)

        # Check if user has sufficient balance for AI usage
        if used_ai:
            # Get user's balance (implement your actual balance check logic here)
            user_balance = get_user_balance(request.user)
            ai_cost = Decimal('20.00')  # 20 KSH for AI content

            if user_balance < ai_cost:
                messages.error(request, f'Insufficient balance for AI content generation. You need {ai_cost} KSH but your balance is {user_balance} KSH.')
                return redirect('ads:ad_create')

        # Get AI suggestion data if available
        ai_suggestion_data = None
        if used_ai and ai_suggestion_id:
            try:
                # Try to get the selected suggestion from the hidden field
                ai_suggestion_json = request.POST.get('ai_suggestion_data')
                if ai_suggestion_json:
                    import json
                    ai_suggestions = json.loads(ai_suggestion_json)
                    # Get the selected suggestion by index (1-based in the form)
                    selected_index = int(ai_suggestion_id) - 1
                    if 0 <= selected_index < len(ai_suggestions):
                        ai_suggestion_data = ai_suggestions[selected_index]

                        # Example of calling the local AI Engine for additional processing
                        # This could be used to refine or enhance the selected suggestion
                        if ai_language and target_audience:
                            # Get business type from the form or use a default
                            business_type = request.POST.get('business_type', 'general')

                            # Create a prompt for the local AI Engine
                            prompt = f"Refine this advertisement in {ai_language} for a {business_type} targeting {target_audience}: {ai_suggestion_data.get('content', '')}"

                            # Call the local AI Engine
                            model = "mistral-7b-instruct"  # Use mistral-7b-instruct for all languages

                            refined_content = call_local_ai(prompt, model)

                            # Log the refined content
                            logger.info(f"Original content: {ai_suggestion_data.get('content', '')}")
                            logger.info(f"Refined content: {refined_content}")

                            # Only update if we got a valid response (not an error)
                            # Check if refined_content is a dict and has valid content
                            if isinstance(refined_content, dict):
                                # Check if it's an error response
                                if refined_content.get('error') or refined_content.get('result', '').startswith("AI error"):
                                    logger.warning(f"AI refinement failed: {refined_content}")
                                else:
                                    # Update the content with the refined version
                                    refined_text = refined_content.get('result', refined_content.get('content', ''))
                                    if refined_text and refined_text.strip():
                                        ai_suggestion_data['content'] = refined_text
                            elif isinstance(refined_content, str) and not refined_content.startswith("AI error"):
                                # Handle case where it returns a string directly
                                ai_suggestion_data['content'] = refined_content
            except (ValueError, json.JSONDecodeError, IndexError) as e:
                # Log the error but continue with the ad creation
                logger.error(f"Error processing AI suggestion data: {e}")

        # Get media file if uploaded
        media = request.FILES.get('media')

        # Get ad type and location
        ad_type = get_object_or_404(AdType, id=ad_type_id)
        ad_location = None
        if ad_location_id:
            ad_location = get_object_or_404(AdLocation, id=ad_location_id)

        # Get campaign if selected
        campaign = None
        if campaign_id:
            campaign = get_object_or_404(Campaign, id=campaign_id, user=request.user)

        # Combine date and time for start_date
        start_datetime_str = f"{start_date}T{start_time}:00"
        start_datetime = timezone.make_aware(datetime.strptime(start_datetime_str, "%Y-%m-%dT%H:%M:%S"))

        # Calculate end_datetime based on duration option
        if duration_option == 'custom' and end_date and end_time:
            # Use custom end date/time
            end_datetime_str = f"{end_date}T{end_time}:00"
            end_datetime = timezone.make_aware(datetime.strptime(end_datetime_str, "%Y-%m-%dT%H:%M:%S"))
        else:
            # Calculate based on preset duration
            end_datetime = start_datetime
            if duration_option == '7days':
                end_datetime = start_datetime + timezone.timedelta(days=7)
            elif duration_option == '2weeks':
                end_datetime = start_datetime + timezone.timedelta(days=14)
            elif duration_option == 'monthly':
                end_datetime = start_datetime + timezone.timedelta(days=30)
            else:
                # Default to 7 days
                end_datetime = start_datetime + timezone.timedelta(days=7)

        # Add 2-hour bonus to end time
        end_datetime = end_datetime + timezone.timedelta(hours=2)

        # Create ad
        ad = Ad.objects.create(
            user=request.user,
            ad_type=ad_type,
            ad_location=ad_location,
            campaign=campaign,
            title=title,
            content=content,
            media=media,
            cta_link=cta_link,
            target_location=target_location,
            target_audience=target_audience,
            start_date=start_datetime,
            end_date=end_datetime,
            requires_ai=requires_ai,
            used_ai=used_ai,
            ai_language=ai_language,
            ai_suggestion=ai_suggestion_data,
            wants_social=wants_social,
            base_pricing=base_pricing,
            final_pricing=final_pricing,
            status='draft'
        )

        messages.success(request, 'Advertisement created successfully!')
        return redirect('ads:ad_submitted', slug=ad.slug)

    # Get unread notification count for the user
    try:
        from notifications.services import NotificationService
        notifications_count = NotificationService.get_user_notifications(
            user=request.user,
            include_deleted=False,
            include_read=False
        ).count()
    except ImportError:
        notifications_count = 0

    # Check if a campaign ID was provided in the URL
    campaign_id = request.GET.get('campaign')
    selected_campaign = None
    if campaign_id:
        try:
            selected_campaign = Campaign.objects.get(id=campaign_id, user=request.user)
        except Campaign.DoesNotExist:
            pass

    # Get user's balance for AI usage
    user_balance = get_user_balance(request.user)

    # Get AI provider status
    from ai_services.network import get_all_provider_status, get_best_available_provider
    ai_provider_status = get_all_provider_status()
    best_ai_provider = get_best_available_provider()

    # Apply abstraction for regular users
    from ai_services.abstraction import get_user_facing_engine_name
    if not request.user.is_staff and not request.user.is_superuser:
        best_ai_provider = get_user_facing_engine_name(best_ai_provider)

    context = {
        'ad_types': ad_types,
        'ad_locations': ad_locations,
        'campaigns': campaigns,
        'selected_campaign': selected_campaign,
        'notifications_count': notifications_count,
        'api_search_url': reverse('ads:api_search_campaigns'),
        'user_balance': user_balance,
        'ai_provider_status': ai_provider_status,
        'best_ai_provider': best_ai_provider,
    }

    return render(request, 'ads/ad_create_consolidated.html', context)
