# Generated by Django 5.1.7 on 2025-05-12 09:42

import django.db.models.deletion
import qrcode_app.models
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='APIKey',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('key', models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('last_used', models.DateTimeField(blank=True, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='api_keys', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='QRCode',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('qr_type', models.CharField(choices=[('URL', 'URL'), ('TEXT', 'Text'), ('VCARD', 'vCard'), ('WIFI', 'WiFi'), ('EMAIL', 'Email'), ('PHONE', 'Phone'), ('LOCATION', 'Location'), ('PDF', 'PDF'), ('IMAGE', 'Image')], default='URL', max_length=20)),
                ('data', models.TextField()),
                ('image', models.ImageField(blank=True, null=True, upload_to=qrcode_app.models.qr_code_upload_path)),
                ('foreground_color', models.CharField(default='#000000', max_length=7)),
                ('background_color', models.CharField(default='#FFFFFF', max_length=7)),
                ('logo', models.ImageField(blank=True, null=True, upload_to='qr_logos')),
                ('is_encrypted', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='qr_codes', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'QR Code',
                'verbose_name_plural': 'QR Codes',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='QRCodeBatch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('zip_file', models.FileField(blank=True, null=True, upload_to='batch_zips')),
                ('count', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='qr_batches', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.CharField(choices=[('guest', 'Guest'), ('user', 'User'), ('premium', 'Premium User'), ('admin', 'Administrator'), ('superadmin', 'Super Administrator')], default='user', max_length=20)),
                ('company', models.CharField(blank=True, max_length=100, null=True)),
                ('phone', models.CharField(blank=True, max_length=20, null=True)),
                ('address', models.TextField(blank=True, null=True)),
                ('profile_image', models.ImageField(blank=True, null=True, upload_to='profile_images')),
                ('api_key', models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
