{% extends 'base.html' %}
{% load static %}

{% block title %}Admin Approval Dashboard{% endblock %}

{% block extra_css %}
<style>
    .approval-dashboard {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .approval-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .approval-header {
        text-align: center;
        color: white;
        margin-bottom: 3rem;
    }

    .approval-header h1 {
        font-size: 3rem;
        font-weight: bold;
        margin-bottom: 1rem;
    }

    .pending-item {
        border: 1px solid #dee2e6;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .pending-item:hover {
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }

    .pending-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .pending-title {
        font-weight: bold;
        font-size: 1.1rem;
        color: #495057;
    }

    .pending-type {
        background: #667eea;
        color: white;
        padding: 0.3rem 0.8rem;
        border-radius: 15px;
        font-size: 0.8rem;
    }

    .pending-details {
        color: #6c757d;
        margin-bottom: 1rem;
    }

    .pending-actions {
        display: flex;
        gap: 1rem;
    }

    .approve-btn {
        background: #28a745;
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .approve-btn:hover {
        background: #218838;
        transform: translateY(-1px);
    }

    .reject-btn {
        background: #dc3545;
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .reject-btn:hover {
        background: #c82333;
        transform: translateY(-1px);
    }

    .empty-state {
        text-align: center;
        padding: 3rem;
        color: #6c757d;
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 1rem;
        color: #667eea;
    }

    .stats-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .stat-box {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 15px;
        text-align: center;
    }

    .stat-value {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
</style>
{% endblock %}

{% block content %}
<div class="approval-dashboard">
    <div class="container">
        <!-- Header -->
        <div class="approval-header">
            <h1><i class="fas fa-clipboard-check me-3"></i>Admin Approval Dashboard</h1>
            <p>Review and approve pending QR codes and short links</p>
        </div>

        <!-- Statistics -->
        <div class="stats-row">
            <div class="stat-box">
                <div class="stat-value">{{ pending_qr_codes.count }}</div>
                <div class="stat-label">Pending QR Codes</div>
            </div>
            <div class="stat-box">
                <div class="stat-value">{{ pending_links.count }}</div>
                <div class="stat-label">Pending Short Links</div>
            </div>
        </div>

        <!-- Pending QR Codes -->
        <div class="approval-card">
            <h4><i class="fas fa-qrcode me-2"></i>Pending QR Codes</h4>
            {% if pending_qr_codes %}
                {% for qr in pending_qr_codes %}
                <div class="pending-item">
                    <div class="pending-header">
                        <div class="pending-title">{{ qr.name }}</div>
                        <div class="pending-type">{{ qr.get_qr_type_display }}</div>
                    </div>
                    <div class="pending-details">
                        <p><strong>User:</strong> {{ qr.user.username }}</p>
                        <p><strong>Data:</strong> {{ qr.data|truncatechars:100 }}</p>
                        <p><strong>Created:</strong> {{ qr.created_at|date:"M d, Y H:i" }}</p>
                    </div>
                    <div class="pending-actions">
                        <form method="post" action="{% url 'approve_qr_code' qr.id %}" style="display: inline;">
                            {% csrf_token %}
                            <button type="submit" class="approve-btn">
                                <i class="fas fa-check me-1"></i>Approve
                            </button>
                        </form>
                        <form method="post" action="{% url 'reject_qr_code' qr.id %}" style="display: inline;">
                            {% csrf_token %}
                            <input type="text" name="reason" placeholder="Rejection reason" required style="margin-right: 0.5rem; padding: 0.5rem; border: 1px solid #ddd; border-radius: 3px;">
                            <button type="submit" class="reject-btn">
                                <i class="fas fa-times me-1"></i>Reject
                            </button>
                        </form>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="empty-state">
                    <i class="fas fa-check-circle"></i>
                    <h4>No Pending QR Codes</h4>
                    <p>All QR codes have been reviewed!</p>
                </div>
            {% endif %}
        </div>

        <!-- Pending Short Links -->
        <div class="approval-card">
            <h4><i class="fas fa-link me-2"></i>Pending Short Links</h4>
            {% if pending_links %}
                {% for link in pending_links %}
                <div class="pending-item">
                    <div class="pending-header">
                        <div class="pending-title">{{ link.title|default:"Untitled Link" }}</div>
                        <div class="pending-type">Short Link</div>
                    </div>
                    <div class="pending-details">
                        <p><strong>User:</strong> {{ link.user.username }}</p>
                        <p><strong>Code:</strong> {{ link.code }}</p>
                        <p><strong>Target:</strong> {{ link.target_url|truncatechars:100 }}</p>
                        <p><strong>Created:</strong> {{ link.created_at|date:"M d, Y H:i" }}</p>
                    </div>
                    <div class="pending-actions">
                        <button class="approve-btn">
                            <i class="fas fa-check me-1"></i>Approve
                        </button>
                        <button class="reject-btn">
                            <i class="fas fa-times me-1"></i>Reject
                        </button>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="empty-state">
                    <i class="fas fa-check-circle"></i>
                    <h4>No Pending Short Links</h4>
                    <p>All short links have been reviewed!</p>
                </div>
            {% endif %}
        </div>

        <!-- Back to Dashboard -->
        <div class="text-center">
            <a href="{% url 'enterprise_dashboard' %}" class="btn btn-outline-light btn-lg">
                <i class="fas fa-arrow-left me-2"></i>Back to Enterprise Dashboard
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Placeholder for approval dashboard functionality
console.log('Admin approval dashboard loaded');
</script>
{% endblock %}
