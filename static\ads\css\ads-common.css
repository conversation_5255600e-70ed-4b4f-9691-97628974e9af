/* Common Styles for All Ads Pages - Glossy Corporate Modern Look */

/* Common Font Styles */
body {
    font-family: 'Poppins', 'Montserrat', sans-serif !important;
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Poppins', 'Montserrat', sans-serif !important;
    font-weight: 600;
}

p, span, div, a, button, input, select, textarea {
    font-family: 'Poppins', 'Montserrat', sans-serif !important;
}

/* Page Header Styles */
.ads-page-header {
    background: linear-gradient(135deg, #1a237e 0%, #283593 100%);
    padding: 30px 0 20px;
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
}

.ads-page-header::before {
    content: '';
    position: absolute;
    top: -50px;
    right: -50px;
    width: 200px;
    height: 200px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 50%;
    z-index: 0;
}

.ads-page-header::after {
    content: '';
    position: absolute;
    bottom: -80px;
    left: -80px;
    width: 250px;
    height: 250px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 50%;
    z-index: 0;
}

.ads-page-title {
    color: #ffffff;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    font-weight: 700;
    position: relative;
    z-index: 1;
    margin-bottom: 10px;
}

.ads-page-subtitle {
    color: rgba(255, 255, 255, 0.9);
    position: relative;
    z-index: 1;
    text-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
    font-size: 1rem;
}

/* Breadcrumb Styles */
.ads-breadcrumb {
    position: relative;
    z-index: 1;
    background: transparent !important;
    margin-bottom: 10px;
    padding: 0;
}

.ads-breadcrumb .breadcrumb-item {
    color: rgba(255, 255, 255, 0.7);
}

.ads-breadcrumb .breadcrumb-item.active {
    color: rgba(255, 255, 255, 0.9);
}

.ads-breadcrumb .breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.ads-breadcrumb .breadcrumb-item a:hover {
    color: #ffffff;
    text-decoration: underline;
}

.ads-breadcrumb .breadcrumb-item + .breadcrumb-item::before {
    color: rgba(255, 255, 255, 0.9) !important;
}

/* Card Styles */
.ads-card {
    background-color: #fff;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.05);
    padding: 25px;
    margin-bottom: 25px;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    border: 1px solid rgba(0,0,0,0.03);
    position: relative;
    z-index: 1;
    overflow: hidden;
}

.ads-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, rgba(57, 73, 171, 0.03) 0%, rgba(57, 73, 171, 0) 70%);
    border-radius: 0 0 0 100%;
    z-index: -1;
    transition: all 0.4s ease;
}

.ads-card:hover {
    box-shadow: 0 15px 40px rgba(0,0,0,0.08);
    transform: translateY(-5px);
}

.ads-card:hover::before {
    width: 150px;
    height: 150px;
    background: linear-gradient(135deg, rgba(57, 73, 171, 0.05) 0%, rgba(57, 73, 171, 0) 70%);
}

.ads-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 18px;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    position: relative;
}

.ads-card-header::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #3949ab, #5c6bc0);
    border-radius: 3px;
}

.ads-card-title {
    font-size: 20px;
    font-weight: 700;
    color: #1a237e;
    margin: 0;
    position: relative;
    padding-left: 15px;
}

.ads-card-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 5px;
    height: 20px;
    background: linear-gradient(to bottom, #3949ab, #5c6bc0);
    border-radius: 3px;
}

/* Button Styles */
.ads-btn {
    border-radius: 30px;
    padding: 10px 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    z-index: 1;
    border: none;
}

.ads-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255,255,255,0.1), rgba(255,255,255,0));
    transform: translateX(-100%);
    transition: all 0.3s ease;
    z-index: -1;
}

.ads-btn:hover::before {
    transform: translateX(0);
}

.ads-btn-primary {
    background: linear-gradient(90deg, #3949ab, #5c6bc0);
    color: white;
    box-shadow: 0 4px 15px rgba(57, 73, 171, 0.2);
}

.ads-btn-primary:hover {
    box-shadow: 0 6px 20px rgba(57, 73, 171, 0.3);
    transform: translateY(-2px);
}

.ads-btn-outline {
    background: transparent;
    color: #3949ab;
    border: 1px solid #3949ab;
}

.ads-btn-outline:hover {
    background: rgba(57, 73, 171, 0.05);
    transform: translateY(-2px);
}

/* Form Styles */
.ads-form-control {
    border-radius: 8px;
    padding: 12px 15px;
    border: 1px solid rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.ads-form-control:focus {
    border-color: #3949ab;
    box-shadow: 0 0 0 3px rgba(57, 73, 171, 0.1);
}

.ads-form-label {
    font-weight: 600;
    color: #1a237e;
    margin-bottom: 8px;
}

/* Animation Classes */
.ads-fade-in {
    animation: adsFadeIn 0.5s ease forwards;
}

.ads-fade-up {
    animation: adsFadeUp 0.5s ease forwards;
}

.ads-fade-right {
    animation: adsFadeRight 0.5s ease forwards;
}

/* Keyframes */
@keyframes adsFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes adsFadeUp {
    from { 
        opacity: 0;
        transform: translateY(20px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes adsFadeRight {
    from { 
        opacity: 0;
        transform: translateX(-20px);
    }
    to { 
        opacity: 1;
        transform: translateX(0);
    }
}

/* Table Styles */
.ads-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-bottom: 20px;
}

.ads-table th {
    background-color: rgba(57, 73, 171, 0.03);
    color: #1a237e;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 12px;
    letter-spacing: 1px;
    padding: 15px 18px;
    border-bottom: 2px solid rgba(57, 73, 171, 0.1);
}

.ads-table td {
    padding: 15px 18px;
    border-bottom: 1px solid rgba(57, 73, 171, 0.05);
    vertical-align: middle;
    transition: all 0.3s ease;
}

.ads-table tr:hover td {
    background-color: rgba(57, 73, 171, 0.02);
}

/* Responsive Styles */
@media (max-width: 768px) {
    .ads-page-header {
        padding: 20px 0 15px;
    }
    
    .ads-page-title {
        font-size: 24px;
    }
    
    .ads-card {
        padding: 20px;
    }
    
    .ads-card-title {
        font-size: 18px;
    }
}
