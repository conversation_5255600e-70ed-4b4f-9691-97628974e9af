"""
AI Services Views
"""
import json
import logging
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST
from django.contrib.auth.decorators import login_required
from django.contrib.admin.views.decorators import staff_member_required
from django.shortcuts import render
import requests

from ai_services.clients import get_ai_client
from ai_services.network import get_all_provider_status, get_best_available_provider, check_provider_health, check_mistral_connectivity
from ai_services.settings import (
    SUPPORTED_LANGUAGES,
    AD_GENERATION_NUM_SUGGESTIONS,
    AD_GENERATION_PROMPT_TEMPLATE,
    AD_GENERATION_TEMPERATURE,
    AD_GENERATION_MAX_TOKENS,
    AI_PROVIDER
)

logger = logging.getLogger(__name__)

@csrf_exempt
@require_POST
@login_required
def generate_ad_suggestions(request):
    """
    Redirect to the consolidated generate_ad_suggestions endpoint in ads app
    to avoid duplicate code and ensure consistent behavior
    """
    from django.http import HttpResponseRedirect
    from django.urls import reverse
    import logging

    logger = logging.getLogger(__name__)
    logger.info("Redirecting from ai_services.generate_ad_suggestions to ads.generate_ad_suggestions")

    # Redirect to the consolidated endpoint in the ads app
    return HttpResponseRedirect(reverse('ads:generate_suggestions'))

def test_ai_integration(request):
    """Test the AI integration"""
    result = None
    error = None

    if request.method == 'POST':
        try:
            # Get form data
            language = request.POST.get('language', 'english')
            business_type = request.POST.get('business_type', '')
            target_audience = request.POST.get('target_audience', '')
            tone = request.POST.get('tone', 'professional')
            ad_title = request.POST.get('title', '')

            # Validate language
            if language not in SUPPORTED_LANGUAGES:
                error = f'Unsupported language. Supported languages are: {", ".join(SUPPORTED_LANGUAGES.keys())}'
            else:
                # Get AI client
                client = get_ai_client()

                # Generate suggestions
                suggestions = client.generate_ad_suggestions(
                    language=language,
                    business_type=business_type,
                    target_audience=target_audience,
                    tone=tone,
                    num_suggestions=AD_GENERATION_NUM_SUGGESTIONS,
                    ad_title=ad_title
                )

                result = suggestions

        except Exception as e:
            logger.error(f"Error testing AI integration: {str(e)}")
            error = f'An error occurred: {str(e)}'

    # Render the test page
    return render(request, 'ai_services/test.html', {
        'result': result,
        'error': error,
        'languages': SUPPORTED_LANGUAGES
    })

@staff_member_required
def debug_api_response(request):
    """Debug view to test the Hugging Face API directly"""
    result = None
    error = None
    raw_response = None
    headers = None
    suggestions = None

    # Get the current AI provider and model
    ai_provider = AI_PROVIDER

    # Log the current configuration
    logger.info(f"Debug view using AI provider: {ai_provider}")

    if request.method == 'POST':
        try:
            # Get form data
            language = request.POST.get('language', 'english')
            business_type = request.POST.get('business_type', '')
            target_audience = request.POST.get('target_audience', '')
            tone = request.POST.get('tone', 'professional')
            ad_title = request.POST.get('title', '')

            # Use the get_ai_client function to get the appropriate client
            client = get_ai_client()

            # Log the client configuration
            logger.info(f"Debug view using model: {client.model}")
            logger.info(f"Debug view has API key: {bool(client.api_key)}")

            # Generate suggestions using the client
            suggestions = client.generate_ad_suggestions(
                language=language,
                business_type=business_type,
                target_audience=target_audience,
                tone=tone,
                num_suggestions=AD_GENERATION_NUM_SUGGESTIONS,
                ad_title=ad_title
            )

            # For the debug view, we'll also make a direct API call to see the raw response
            is_t5_model = 't5' in client.model.lower()

            # Prepare the prompt based on model type
            if is_t5_model:
                # T5 models work better with simpler prompts
                if ad_title:
                    prompt = f"Generate {language} advertisement for {business_type} targeting {target_audience} with title: {ad_title}"
                else:
                    prompt = f"Generate {language} advertisement for {business_type} targeting {target_audience}"
            else:
                # For other models, use the standard prompt template
                title_instruction = ""
                title_format = "[catchy title]"

                if ad_title:
                    title_instruction = f"The ad title is: {ad_title}\nGenerate content that aligns with this title."
                    title_format = ad_title

                prompt = AD_GENERATION_PROMPT_TEMPLATE.format(
                    language=language,
                    business_type=business_type,
                    target_audience=target_audience,
                    tone=tone,
                    num_suggestions=AD_GENERATION_NUM_SUGGESTIONS,
                    title_instruction=title_instruction,
                    title_format=title_format
                )

            # Prepare the API request
            api_url = client.api_url
            headers_dict = {"Authorization": f"Bearer {client.api_key}"} if client.api_key else {}

            payload = {
                "inputs": prompt,
                "parameters": {
                    "temperature": AD_GENERATION_TEMPERATURE,
                    "max_new_tokens": AD_GENERATION_MAX_TOKENS,
                    "return_full_text": False
                }
            }

            # Make the API request
            logger.info(f"Making API request to: {api_url}")
            response = requests.post(api_url, headers=headers_dict, json=payload)

            # Store the response headers
            headers = dict(response.headers)

            # Parse the response
            raw_response = response.text
            result = response.json()

        except Exception as e:
            logger.error(f"Error in debug API response: {str(e)}")
            error = f'An error occurred: {str(e)}'

    # Render the debug page
    return render(request, 'ai_services/debug.html', {
        'result': result,
        'raw_response': raw_response,
        'headers': headers,
        'error': error,
        'languages': SUPPORTED_LANGUAGES,
        'api_key_present': True if 'client' in locals() and hasattr(client, 'api_key') and client.api_key else False,
        'model': client.model if 'client' in locals() else "unknown",  # Display the actual model being used
        'provider': ai_provider,
        'is_t5_model': False,
        'suggestions': suggestions
    })

def get_ai_provider_status(request):
    """
    API endpoint to get the status of all AI providers

    Returns:
        JSON response with the status of all AI providers
    """
    # Check if we should force a refresh
    refresh = request.GET.get('refresh', 'false').lower() == 'true'

    # Get provider status
    if refresh:
        # Check Mistral provider health
        check_provider_health('mistral')
        # Check OpenAI provider health
        check_provider_health('openai')
        # Check local provider health
        check_provider_health('local')

    # Get all provider status
    provider_status = get_all_provider_status()

    # Get best available provider
    best_provider = get_best_available_provider()

    # Sanitize the status for the user
    from ai_services.abstraction import get_engine_status_for_user, get_user_facing_engine_name

    # Check if the user is an admin or superuser
    is_admin = request.user and (request.user.is_staff or request.user.is_superuser)

    if is_admin:
        # Admins see all details
        sanitized_status = provider_status
        sanitized_best_provider = best_provider
    else:
        # Regular users see abstracted status
        sanitized_status = get_engine_status_for_user(provider_status, request.user)
        sanitized_best_provider = get_user_facing_engine_name(best_provider)

    # Return JSON response
    return JsonResponse({
        'status': sanitized_status,
        'best_provider': sanitized_best_provider
    })

@staff_member_required
def ai_provider_status_admin(request):
    """
    Admin view to check the status of all AI providers

    Args:
        request: The HTTP request

    Returns:
        Rendered template with AI provider status
    """
    # Check if we should force a refresh
    refresh = request.GET.get('refresh', 'false').lower() == 'true'

    # Get provider status
    if refresh:
        # Check Mistral provider health
        check_provider_health('mistral')
        # Check OpenAI provider health
        check_provider_health('openai')
        # Check local provider health
        check_provider_health('local')

    # Get all provider status
    provider_status = get_all_provider_status()

    # Get best available provider
    best_provider = get_best_available_provider()

    # Render the template
    return render(request, 'ai_services/status.html', {
        'title': 'AI Provider Status',
        'ai_provider_status': provider_status,
        'best_ai_provider': best_provider,
        'refresh': refresh
    })

@staff_member_required
def check_mistral_api_admin(request):
    """
    Admin view to check Mistral API connectivity

    Args:
        request: The HTTP request

    Returns:
        JSON response with Mistral API connectivity status
    """
    # Force refresh if requested
    force_refresh = request.GET.get('refresh', 'false').lower() == 'true'

    # Check Mistral API connectivity
    result = check_mistral_connectivity(force_refresh=force_refresh)

    # Add timestamp in human-readable format
    from datetime import datetime
    result['timestamp_formatted'] = datetime.fromtimestamp(result['timestamp']).strftime('%Y-%m-%d %H:%M:%S')

    # Return JSON response
    return JsonResponse(result)

@staff_member_required
def ai_diagnostics_admin(request):
    """
    Admin view for AI engine diagnostics

    Args:
        request: The HTTP request

    Returns:
        Rendered template with AI engine diagnostics
    """
    from ai_services.clients import get_ai_client
    from ai_services.abstraction import get_user_facing_engine_name

    # Define a local get_cache_stats function as a fallback
    def get_cache_stats():
        """Fallback cache stats function"""
        return {
            'entries': 0,
            'size': 0,
            'hits': 0,
            'misses': 0,
            'hit_ratio': 0,
            'most_common': []
        }
    from django.db.models import Count
    from django.utils import timezone
    import os

    # Handle form submission
    if request.method == 'POST':
        # Update USE_LOCAL_MODE setting
        use_local_mode = request.POST.get('use_local_mode') == 'on'
        os.environ['USE_LOCAL_MODE'] = 'true' if use_local_mode else 'false'

        # Update default provider setting
        default_provider = request.POST.get('default_provider')
        if default_provider:
            os.environ['AI_PROVIDER'] = default_provider

    # Get current settings
    use_local_mode = os.environ.get('USE_LOCAL_MODE', 'false').lower() in ('true', '1', 'yes')

    # Get provider status
    provider_status = get_all_provider_status()

    # Get best available provider
    best_provider = get_best_available_provider()

    # Get current AI client
    ai_client = get_ai_client()
    current_provider = ai_client.__class__.__name__.replace('AIClient', '').lower()
    current_model = getattr(ai_client, 'model', 'unknown')
    current_engine = get_user_facing_engine_name(current_provider)

    # Get available providers
    available_providers = ['mistral', 'openai', 'local', 'offline']

    # Get usage statistics
    try:
        from ads.models import AiFeedback

        # Get usage stats for the last 24 hours
        last_24h = timezone.now() - timezone.timedelta(hours=24)
        usage_stats = {}

        # Get counts by model
        model_counts = AiFeedback.objects.filter(created_at__gte=last_24h).values('model_used').annotate(count=Count('id'))

        # Calculate total
        total_count = sum(item['count'] for item in model_counts)

        # Calculate percentages
        for item in model_counts:
            model = item['model_used'] or 'unknown'
            count = item['count']
            percentage = (count / total_count * 100) if total_count > 0 else 0

            # Group by provider
            if 'mistral' in model.lower():
                provider = 'mistral'
            elif 'gpt' in model.lower():
                provider = 'openai'
            elif 'offline' in model.lower():
                provider = 'offline'
            else:
                provider = 'other'

            # Add to stats
            if provider not in usage_stats:
                usage_stats[provider] = {'count': 0, 'percentage': 0}

            usage_stats[provider]['count'] += count
            usage_stats[provider]['percentage'] = round((usage_stats[provider]['count'] / total_count * 100) if total_count > 0 else 0, 1)

        # Get fallback count
        fallback_count = AiFeedback.objects.filter(created_at__gte=last_24h, suggestion_data__contains='"fallback": true').count()
    except Exception as e:
        logger.error(f"Error getting usage statistics: {str(e)}")
        usage_stats = {}
        fallback_count = 0

    # Get cache statistics
    cache_stats = get_cache_stats()

    # Render the template
    return render(request, 'ai_services/admin_diagnostics.html', {
        'title': 'AI Engine Diagnostics',
        'provider_status': provider_status,
        'best_ai_provider': best_provider,
        'current_provider': current_provider,
        'current_model': current_model,
        'current_engine': current_engine,
        'use_local_mode': use_local_mode,
        'available_providers': available_providers,
        'usage_stats': usage_stats,
        'fallback_count': fallback_count,
        'cache_stats': cache_stats
    })
