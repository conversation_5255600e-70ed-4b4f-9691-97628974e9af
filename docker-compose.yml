version: '3.8'

services:
  # Nginx service for HTTPS and reverse proxy
  nginx:
    build:
      context: ./nginx
      dockerfile: Dockerfile
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - static_volume:/var/www/static
      - media_volume:/var/www/media
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - certbot_data:/etc/letsencrypt
      - certbot_www:/var/www/certbot
    depends_on:
      - web
      # AI Engine dependency removed
      # - ai_engine
    restart: unless-stopped
    networks:
      - app_network

  # Certbot service for Let's Encrypt certificates
  certbot:
    image: certbot/certbot
    volumes:
      - certbot_data:/etc/letsencrypt
      - certbot_www:/var/www/certbot
    entrypoint: "/bin/sh -c 'trap exit TERM; while :; do certbot renew; sleep 12h & wait $${!}; done;'"
    networks:
      - app_network

  # AI Engine service - DISABLED
  # Uncomment the following section to re-enable the AI Engine
  # ai_engine:
  #   build:
  #     context: ./ai_engine
  #     dockerfile: Dockerfile
  #   volumes:
  #     - ai_models:/app/models
  #     - ./logs:/app/logs
  #   env_file:
  #     - .env
  #   environment:
  #     - PYTHONUNBUFFERED=1
  #     - AI_ENGINE_ENABLE_AUTH=true
  #     - AI_ENGINE_HOST=0.0.0.0
  #     - AI_ENGINE_PORT=8001
  #     - AI_ENGINE_MODEL_DIR=/app/models
  #     - LOG_DIR=/app/logs
  #     - ENVIRONMENT=production
  #     - AI_ENGINE_MIN_MEMORY_MB=2048
  #     - AI_ENGINE_ENABLE_CUDA=false
  #   restart: unless-stopped
  #   healthcheck:
  #     test: ["CMD", "python", "health_check.py", "--url", "http://localhost:8001"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3
  #     start_period: 60s
  #   networks:
  #     - app_network

  # Django web service
  web:
    build:
      context: .
      dockerfile: Dockerfile
    # No need to expose ports directly in production, Nginx will proxy
    # ports:
    #   - "8000:8000"
    volumes:
      - .:/app
      - static_volume:/app/static
      - media_volume:/app/media
      - ./logs:/app/logs
    env_file:
      - .env
    environment:
      - PYTHONUNBUFFERED=1
      - DEBUG=false
      # Changed from local to mistral
      - AI_PROVIDER=mistral
      # AI Engine URL disabled
      # - AI_ENGINE_URL=http://ai_engine:8001
    # Dependency on AI Engine removed
    # depends_on:
    #   ai_engine:
    #     condition: service_healthy
    restart: unless-stopped
    networks:
      - app_network

volumes:
  ai_models:
    # Persistent volume for AI models
  static_volume:
    # Volume for Django static files
  media_volume:
    # Volume for Django media files
  certbot_data:
    # Volume for Let's Encrypt certificates
  certbot_www:
    # Volume for Let's Encrypt challenges

networks:
  app_network:
    driver: bridge
