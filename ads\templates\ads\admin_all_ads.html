{% extends 'base.html' %}
{% load static %}

{% block title %}Admin - All Advertisements{% endblock %}

{% block extra_css %}
<!-- Google Fonts - Poppins and Montserrat -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

<!-- Include common ads CSS -->
{% include 'ads/includes/ads_common_css.html' %}

<style>
    /* Ultra-Premium Sleek Corporate Admin Styling */
    body {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 15%, #cbd5e1 30%, #94a3b8 50%, #64748b 70%, #475569 85%, #334155 100%);
        min-height: 100vh;
        font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        position: relative;
        overflow-x: hidden;
    }

    /* Elegant animated background with subtle corporate patterns */
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 25% 75%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),
            radial-gradient(circle at 75% 25%, rgba(255, 255, 255, 0.6) 0%, transparent 40%),
            radial-gradient(circle at 15% 85%, rgba(139, 92, 246, 0.06) 0%, transparent 45%),
            radial-gradient(circle at 85% 15%, rgba(16, 185, 129, 0.05) 0%, transparent 35%),
            radial-gradient(circle at 50% 50%, rgba(59, 130, 246, 0.04) 0%, transparent 60%);
        z-index: -1;
        animation: elegantCorporateFloat 120s ease-in-out infinite;
    }

    @keyframes elegantCorporateFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        25% { transform: translateY(-30px) rotate(1deg); }
        50% { transform: translateY(-20px) rotate(-1deg); }
        75% { transform: translateY(-35px) rotate(0.5deg); }
    }

    /* Enterprise Container */
    .enterprise-container {
        position: relative;
        z-index: 1;
        padding: 2rem 0;
        min-height: 100vh;
    }

    /* Premium Elegant Header Section */
    .enterprise-header {
        background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(51, 65, 85, 0.95) 50%, rgba(71, 85, 105, 0.95) 100%);
        backdrop-filter: blur(25px);
        border-radius: 24px;
        padding: 3rem;
        margin-bottom: 2rem;
        box-shadow:
            0 20px 40px rgba(0, 0, 0, 0.1),
            0 0 0 1px rgba(255, 255, 255, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        position: relative;
        overflow: hidden;
        animation: slideInDown 0.8s ease-out;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .enterprise-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
        animation: shimmer 3s ease-in-out infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    .enterprise-title {
        font-family: 'Montserrat', sans-serif !important;
        font-size: 2.5rem;
        font-weight: 700;
        color: #ffffff;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 2;
    }

    .enterprise-subtitle {
        font-size: 1.1rem;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 1.5rem;
        font-weight: 400;
        position: relative;
        z-index: 2;
    }

    /* Premium Elegant Enterprise Cards */
    .enterprise-card {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
        backdrop-filter: blur(25px);
        border-radius: 24px;
        overflow: hidden;
        box-shadow:
            0 15px 35px rgba(0, 0, 0, 0.08),
            0 0 0 1px rgba(255, 255, 255, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.4);
        margin-bottom: 2rem;
        transition: all 0.3s ease;
        animation: slideInUp 0.8s ease-out 0.2s both;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .enterprise-card:hover {
        transform: translateY(-8px);
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.12),
            0 0 0 1px rgba(255, 255, 255, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.5);
    }

    .enterprise-card-header {
        background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(51, 65, 85, 0.95) 100%);
        color: white;
        padding: 1.8rem 2.5rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .enterprise-card-header::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #3b82f6 0%, #8b5cf6 50%, #10b981 100%);
    }

    .enterprise-card-title {
        font-family: 'Montserrat', sans-serif !important;
        font-size: 1.3rem;
        font-weight: 700;
        margin: 0;
        position: relative;
        z-index: 2;
    }

    .admin-badge {
        background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
        color: white;
        padding: 0.6rem 1.2rem;
        border-radius: 16px;
        font-size: 0.9rem;
        font-weight: 600;
        box-shadow: 0 6px 20px rgba(59, 130, 246, 0.25);
        position: relative;
        z-index: 2;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .ad-user {
        font-weight: 600;
        color: #1a237e;
    }

    .action-buttons {
        display: flex;
        gap: 8px;
        justify-content: flex-start;
        align-items: center;
    }

    .action-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        color: white;
        transition: all 0.3s ease;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        border: none;
        cursor: pointer;
    }

    .action-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .view-btn {
        background: linear-gradient(135deg, #3949ab, #1e88e5);
    }

    .view-btn:hover {
        background: linear-gradient(135deg, #283593, #1565c0);
    }

    .status-btn {
        background: linear-gradient(135deg, #5e35b1, #7b1fa2);
    }

    .status-btn:hover {
        background: linear-gradient(135deg, #4527a0, #6a1b9a);
    }

    .approve-btn {
        background: linear-gradient(135deg, #43a047, #2e7d32);
    }

    .approve-btn:hover {
        background: linear-gradient(135deg, #388e3c, #1b5e20);
    }

    .reject-btn {
        background: linear-gradient(135deg, #e53935, #c62828);
    }

    .reject-btn:hover {
        background: linear-gradient(135deg, #d32f2f, #b71c1c);
    }

    .analytics-btn {
        background: linear-gradient(135deg, #fb8c00, #ef6c00);
    }

    .analytics-btn:hover {
        background: linear-gradient(135deg, #f57c00, #e65100);
    }

    /* Premium Elegant Filter Section */
    .filter-section {
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.06) 0%, rgba(139, 92, 246, 0.06) 100%);
        backdrop-filter: blur(15px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 1.5rem;
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    }

    .filter-section .form-label {
        font-weight: 600;
        color: #1e293b;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 0.5rem;
    }

    .filter-section .form-select,
    .filter-section .form-control {
        border-radius: 14px;
        border: 2px solid rgba(59, 130, 246, 0.15);
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
        padding: 0.75rem 1rem;
        font-weight: 500;
    }

    .filter-section .form-select:focus,
    .filter-section .form-control:focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        background: rgba(255, 255, 255, 1);
        outline: none;
    }

    /* Enhanced Mobile Responsiveness */
    @media (max-width: 768px) {
        .enterprise-header {
            padding: 2rem 1.5rem;
            margin: 1rem auto 1.5rem;
            border-radius: 16px;
        }

        .enterprise-title {
            font-size: 2rem;
        }

        .enterprise-subtitle {
            font-size: 1rem;
        }

        .enterprise-card-header {
            padding: 1.2rem 1.5rem;
        }

        .enterprise-card-title {
            font-size: 1.1rem;
        }

        .filter-section {
            padding: 1.5rem;
        }
    }

    @media (max-width: 576px) {
        .enterprise-header {
            padding: 1.5rem 1rem;
            margin: 0.5rem auto 1rem;
        }

        .enterprise-title {
            font-size: 1.6rem;
        }

        .enterprise-subtitle {
            font-size: 0.9rem;
        }

        .enterprise-card-header {
            padding: 1rem;
            flex-direction: column;
            gap: 0.5rem;
        }

        .enterprise-card-title {
            font-size: 1rem;
        }

        .admin-badge {
            padding: 0.4rem 0.8rem;
            font-size: 0.8rem;
        }

        .filter-section {
            padding: 1rem;
        }
    }

    .status-badge {
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 600;
    }

    .status-draft {
        background-color: #e0e0e0;
        color: #616161;
    }

    .status-pending {
        background-color: #fff8e1;
        color: #ff8f00;
    }

    .status-approved {
        background-color: #e8f5e9;
        color: #2e7d32;
    }

    .status-active {
        background-color: #e3f2fd;
        color: #1565c0;
    }

    .status-paused {
        background-color: #f3e5f5;
        color: #6a1b9a;
    }

    .status-rejected {
        background-color: #ffebee;
        color: #c62828;
    }

    .status-expired {
        background-color: #efebe9;
        color: #4e342e;
    }

    .user-filter {
        max-width: 200px;
    }
</style>
{% endblock %}

{% block content %}
<div class="enterprise-container">
    <div class="container">
        <!-- Premium Enterprise Header -->
        <div class="enterprise-header">
            <nav aria-label="breadcrumb" class="mb-3">
                <ol class="breadcrumb" style="background: rgba(255, 255, 255, 0.1); border-radius: 12px; padding: 0.75rem 1.5rem;">
                    <li class="breadcrumb-item"><a href="{% url 'index' %}" style="color: rgba(255, 255, 255, 0.8);">Home</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'ads:dashboard' %}" style="color: rgba(255, 255, 255, 0.8);">Ads Dashboard</a></li>
                    <li class="breadcrumb-item active" aria-current="page" style="color: white; font-weight: 600;">Admin - All Ads</li>
                </ol>
            </nav>

            <div class="text-center">
                <h1 class="enterprise-title">
                    <i class="fas fa-cogs me-3 text-warning"></i>
                    Advertisement Management Center
                </h1>
                <p class="enterprise-subtitle">
                    Comprehensive administration and management of all advertisements in the system
                </p>
            </div>
        </div>
        <div class="row mb-4">
            <div class="col-12">
                <div class="enterprise-card">
                    <div class="enterprise-card-header">
                        <h3 class="enterprise-card-title">
                            <i class="fas fa-list-alt me-2"></i>
                            All Advertisements
                        </h3>
                        <span class="admin-badge">{{ total_count }} Ads</span>
                    </div>

                <div class="card-body">
                    <div class="filter-section mb-4">
                        <form method="get" class="row g-3">
                            <div class="col-md-4">
                                <label for="status" class="form-label">Filter by Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">All Statuses</option>
                                    {% for status_code, status_name in status_choices %}
                                        <option value="{{ status_code }}" {% if current_status == status_code %}selected{% endif %}>{{ status_name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="username" class="form-label">Filter by User</label>
                                <select class="form-select user-filter" id="username" name="username">
                                    <option value="">All Users</option>
                                    {% for username in usernames %}
                                        <option value="{{ username }}" {% if current_username == username %}selected{% endif %}>{{ username }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <button type="submit" class="ads-btn ads-btn-primary">Apply Filters</button>
                                <a href="{% url 'ads:admin_all_ads' %}" class="ads-btn ads-btn-outline ms-2">Reset</a>
                            </div>
                        </form>
                    </div>

                    <div class="admin-actions mb-4">
                        <a href="{% url 'ads:admin_pending_ads' %}" class="ads-btn ads-btn-outline">
                            <i class="fas fa-clock"></i> Pending Ads
                        </a>
                        <a href="{% url 'ads:admin_active_ads' %}" class="ads-btn ads-btn-outline">
                            <i class="fas fa-play-circle"></i> Active Ads
                        </a>
                    </div>

                    {% if page_obj %}
                    <div class="table-responsive">
                        <table class="ads-table" id="allAdsTable">
                            <thead>
                                <tr>
                                    <th style="width: 25%;">Title</th>
                                    <th style="width: 15%;">User</th>
                                    <th style="width: 10%;">Type</th>
                                    <th style="width: 10%;">Status</th>
                                    <th style="width: 15%;">Created</th>
                                    <th style="width: 25%;">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for ad in page_obj %}
                                <tr>
                                    <td title="{{ ad.title }}">{{ ad.title }}</td>
                                    <td><span class="ad-user">{{ ad.user.username }}</span></td>
                                    <td>{{ ad.ad_type.name }}</td>
                                    <td>
                                        <span class="status-badge status-{{ ad.status }}">
                                            {{ ad.get_status_display }}
                                        </span>
                                    </td>
                                    <td>{{ ad.created_at|date:"M d, Y" }}</td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="{% url 'ads:admin_view_ad' ad.slug %}" class="action-btn view-btn" data-bs-toggle="tooltip" data-bs-placement="top" title="View Ad Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'ads:admin_update_ad_status' ad.slug %}" class="action-btn status-btn" data-bs-toggle="tooltip" data-bs-placement="top" title="Update Status">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% if ad.status == 'pending' %}
                                            <a href="{% url 'ads:admin_approve_ad' ad.slug %}" class="action-btn approve-btn" data-bs-toggle="tooltip" data-bs-placement="top" title="Approve Ad" onclick="return confirm('Are you sure you want to approve this ad?')">
                                                <i class="fas fa-check"></i>
                                            </a>
                                            <a href="{% url 'ads:admin_reject_ad' ad.slug %}" class="action-btn reject-btn" data-bs-toggle="tooltip" data-bs-placement="top" title="Reject Ad">
                                                <i class="fas fa-times"></i>
                                            </a>
                                            {% endif %}
                                            {% if ad.status == 'active' %}
                                            <a href="{% url 'ads:ad_analytics' ad.slug %}" class="action-btn analytics-btn" data-bs-toggle="tooltip" data-bs-placement="top" title="View Analytics">
                                                <i class="fas fa-chart-bar"></i>
                                            </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <h4>No advertisements found</h4>
                        <p>There are no advertisements matching your filter criteria.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="row mt-4">
        <div class="col-12">
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_username %}&username={{ current_username }}{% endif %}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link" aria-hidden="true">&laquo;</span>
                    </li>
                    {% endif %}

                    {% for i in page_obj.paginator.page_range %}
                        {% if page_obj.number == i %}
                        <li class="page-item active">
                            <span class="page-link">{{ i }}</span>
                        </li>
                        {% else %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ i }}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_username %}&username={{ current_username }}{% endif %}">{{ i }}</a>
                        </li>
                        {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_username %}&username={{ current_username }}{% endif %}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link" aria-hidden="true">&raquo;</span>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
</script>
{% endblock %}