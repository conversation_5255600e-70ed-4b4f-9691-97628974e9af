/**
 * UI Polish & Upgrade Flow
 * Handles usage limit checking and upgrade modal display
 */

class UpgradeFlowManager {
    constructor() {
        this.currentUser = null;
        this.currentSubscription = null;
        this.availablePlans = [];
        this.usageLimits = {};
        
        this.init();
    }

    init() {
        this.loadUserData();
        this.setupEventListeners();
        this.checkUsageLimits();
    }

    /**
     * Load current user subscription and usage data
     */
    async loadUserData() {
        try {
            const response = await fetch('/api/user/subscription-status/', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.currentUser = data.user;
                this.currentSubscription = data.subscription;
                this.usageLimits = data.usage_summary;
                this.availablePlans = data.available_plans;
                
                console.log('✅ User data loaded:', data);
            } else {
                console.warn('⚠️ Failed to load user data');
            }
        } catch (error) {
            console.error('❌ Error loading user data:', error);
        }
    }

    /**
     * Setup event listeners for usage limit checking
     */
    setupEventListeners() {
        // Check limits before QR code creation
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-action="create-qr"]') || 
                e.target.closest('[data-action="create-qr"]')) {
                e.preventDefault();
                this.checkQRCodeLimit(() => {
                    // Proceed with QR code creation
                    this.proceedWithAction('create-qr', e.target);
                });
            }
        });

        // Check limits before AI feature usage
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-action="use-ai"]') || 
                e.target.closest('[data-action="use-ai"]')) {
                e.preventDefault();
                this.checkAILimit(() => {
                    // Proceed with AI feature
                    this.proceedWithAction('use-ai', e.target);
                });
            }
        });

        // Check limits before advanced analytics
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-action="view-analytics"]') || 
                e.target.closest('[data-action="view-analytics"]')) {
                e.preventDefault();
                this.checkAnalyticsLimit(() => {
                    // Proceed with analytics
                    this.proceedWithAction('view-analytics', e.target);
                });
            }
        });

        // Check limits before API access
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-action="use-api"]') || 
                e.target.closest('[data-action="use-api"]')) {
                e.preventDefault();
                this.checkAPILimit(() => {
                    // Proceed with API access
                    this.proceedWithAction('use-api', e.target);
                });
            }
        });
    }

    /**
     * Check QR code creation limit
     */
    checkQRCodeLimit(onSuccess) {
        if (!this.usageLimits.qr_codes) {
            onSuccess();
            return;
        }

        const { current, limit, percentage } = this.usageLimits.qr_codes;
        
        if (current >= limit) {
            this.showUpgradeModal('qr_codes', {
                title: 'QR Code Limit Reached',
                message: `You've created ${current} out of ${limit} QR codes allowed in your current plan.`,
                feature: 'More QR Codes',
                icon: 'qrcode'
            });
        } else if (percentage >= 80) {
            this.showUsageWarning('qr_codes', percentage, onSuccess);
        } else {
            onSuccess();
        }
    }

    /**
     * Check monthly scan limit
     */
    checkScanLimit() {
        if (!this.usageLimits.scans) return true;

        const { current, limit, percentage } = this.usageLimits.scans;
        
        if (current >= limit) {
            this.showUpgradeModal('scans', {
                title: 'Monthly Scan Limit Reached',
                message: `You've used ${current} out of ${limit} scans this month.`,
                feature: 'More Monthly Scans',
                icon: 'chart-line'
            });
            return false;
        }
        
        return true;
    }

    /**
     * Check AI feature limit
     */
    checkAILimit(onSuccess) {
        if (!this.currentSubscription || !this.currentSubscription.plan.ai_enabled) {
            this.showUpgradeModal('ai', {
                title: 'AI Features Not Available',
                message: 'AI-powered landing pages are not included in your current plan.',
                feature: 'AI Landing Pages',
                icon: 'robot'
            });
            return;
        }

        if (this.usageLimits.ai_pages) {
            const { current, limit, percentage } = this.usageLimits.ai_pages;
            
            if (current >= limit) {
                this.showUpgradeModal('ai_pages', {
                    title: 'AI Pages Limit Reached',
                    message: `You've created ${current} out of ${limit} AI pages allowed in your plan.`,
                    feature: 'More AI Pages',
                    icon: 'robot'
                });
                return;
            }
        }

        onSuccess();
    }

    /**
     * Check advanced analytics limit
     */
    checkAnalyticsLimit(onSuccess) {
        if (!this.currentSubscription || !this.currentSubscription.plan.advanced_analytics_enabled) {
            this.showUpgradeModal('analytics', {
                title: 'Advanced Analytics Not Available',
                message: 'Advanced analytics features are not included in your current plan.',
                feature: 'Advanced Analytics',
                icon: 'chart-bar'
            });
            return;
        }

        onSuccess();
    }

    /**
     * Check API access limit
     */
    checkAPILimit(onSuccess) {
        if (!this.currentSubscription || !this.currentSubscription.plan.api_access_enabled) {
            this.showUpgradeModal('api', {
                title: 'API Access Not Available',
                message: 'API access is not included in your current plan.',
                feature: 'API Access',
                icon: 'code'
            });
            return;
        }

        onSuccess();
    }

    /**
     * Show usage warning when approaching limit
     */
    showUsageWarning(limitType, percentage, onSuccess) {
        const warningHtml = `
            <div class="usage-warning-toast">
                <div class="warning-content">
                    <i class="fas fa-exclamation-triangle"></i>
                    <div class="warning-text">
                        <strong>Usage Warning</strong>
                        <p>You're at ${percentage.toFixed(0)}% of your ${limitType.replace('_', ' ')} limit.</p>
                    </div>
                    <button class="btn btn-sm btn-primary" onclick="upgradeFlowManager.showUpgradeModal('${limitType}')">
                        Upgrade
                    </button>
                </div>
            </div>
        `;

        this.showToast(warningHtml, 'warning', 5000);
        onSuccess();
    }

    /**
     * Show upgrade modal with specific context
     */
    showUpgradeModal(limitType, context = {}) {
        // Update modal content based on context
        this.updateModalContent(context);
        
        // Load current plan data
        this.loadCurrentPlanData();
        
        // Load available upgrade plans
        this.loadUpgradePlans(limitType);
        
        // Show the modal
        const modal = new bootstrap.Modal(document.getElementById('upgradeModal'));
        modal.show();
        
        // Track upgrade modal display
        this.trackEvent('upgrade_modal_shown', {
            limit_type: limitType,
            current_plan: this.currentSubscription?.plan?.name || 'Free',
            ...context
        });
    }

    /**
     * Update modal content based on context
     */
    updateModalContent(context) {
        if (context.title) {
            document.getElementById('upgradeModalLabel').textContent = context.title;
        }
        
        if (context.message) {
            document.querySelector('.upgrade-subtitle').textContent = context.message;
        }
        
        if (context.icon) {
            document.querySelector('.upgrade-icon i').className = `fas fa-${context.icon}`;
        }
    }

    /**
     * Load current plan data into modal
     */
    loadCurrentPlanData() {
        if (!this.currentSubscription) return;

        const plan = this.currentSubscription.plan;
        const usage = this.usageLimits;

        // Update current plan info
        document.getElementById('currentPlanName').textContent = plan.name;
        document.getElementById('currentPlanPrice').textContent = `$${plan.price}/month`;

        // Update QR codes usage
        if (usage.qr_codes) {
            const { current, limit, percentage } = usage.qr_codes;
            document.getElementById('currentQRUsage').textContent = `${current}/${limit}`;
            document.getElementById('currentQRProgress').style.width = `${percentage}%`;
        }

        // Update scans usage
        if (usage.scans) {
            const { current, limit, percentage } = usage.scans;
            document.getElementById('currentScanUsage').textContent = `${current.toLocaleString()}/${limit.toLocaleString()}`;
            document.getElementById('currentScanProgress').style.width = `${percentage}%`;
        }
    }

    /**
     * Load available upgrade plans
     */
    loadUpgradePlans(limitType) {
        const plansGrid = document.getElementById('upgradePlansGrid');
        if (!plansGrid || !this.availablePlans.length) return;

        // Filter plans that solve the current limitation
        const relevantPlans = this.availablePlans.filter(plan => {
            return this.planSolvesLimitation(plan, limitType);
        });

        // Generate plan cards HTML
        const plansHTML = relevantPlans.map(plan => this.generatePlanCard(plan)).join('');
        plansGrid.innerHTML = plansHTML;
    }

    /**
     * Check if plan solves the current limitation
     */
    planSolvesLimitation(plan, limitType) {
        const currentPlan = this.currentSubscription?.plan;
        if (!currentPlan) return true;

        switch (limitType) {
            case 'qr_codes':
                return plan.max_qr_codes > currentPlan.max_qr_codes;
            case 'scans':
                return plan.max_scans_per_month > currentPlan.max_scans_per_month;
            case 'ai':
            case 'ai_pages':
                return plan.ai_enabled && plan.max_ai_pages > (currentPlan.max_ai_pages || 0);
            case 'analytics':
                return plan.advanced_analytics_enabled;
            case 'api':
                return plan.api_access_enabled;
            default:
                return plan.price > currentPlan.price;
        }
    }

    /**
     * Generate plan card HTML
     */
    generatePlanCard(plan) {
        const isPopular = plan.name.toLowerCase().includes('professional');
        const isEnterprise = plan.name.toLowerCase().includes('enterprise');
        
        return `
            <div class="upgrade-plan-card" data-plan-id="${plan.id}">
                ${isPopular ? '<div class="plan-badge">Most Popular</div>' : ''}
                ${isEnterprise ? '<div class="plan-badge enterprise">Enterprise</div>' : ''}
                <div class="plan-header">
                    <h6 class="plan-name">${plan.name}</h6>
                    <div class="plan-price">
                        <span class="price">$${plan.price}</span>
                        <span class="period">/month</span>
                    </div>
                </div>
                <div class="plan-features">
                    <ul>
                        ${plan.features_list.map(feature => `<li><i class="fas fa-check"></i> ${feature}</li>`).join('')}
                    </ul>
                </div>
                <button class="btn upgrade-btn ${isEnterprise ? 'enterprise' : ''}" onclick="upgradeFlowManager.upgradeToplan(${plan.id})">
                    <i class="fas fa-${isEnterprise ? 'crown' : 'arrow-up'}"></i> Upgrade Now
                </button>
            </div>
        `;
    }

    /**
     * Proceed with action after limit check
     */
    proceedWithAction(action, element) {
        switch (action) {
            case 'create-qr':
                // Redirect to QR creation page or trigger creation
                if (element.href) {
                    window.location.href = element.href;
                } else if (element.onclick) {
                    element.onclick();
                }
                break;
            case 'use-ai':
                // Enable AI features
                this.enableAIFeatures();
                break;
            case 'view-analytics':
                // Redirect to analytics page
                window.location.href = '/analytics/';
                break;
            case 'use-api':
                // Redirect to API documentation
                window.location.href = '/api/docs/';
                break;
        }
    }

    /**
     * Upgrade to specific plan
     */
    async upgradeToplan(planId) {
        try {
            // Track upgrade attempt
            this.trackEvent('upgrade_attempt', {
                plan_id: planId,
                current_plan: this.currentSubscription?.plan?.name || 'Free'
            });

            // Check if Stripe or Paystack should be used
            const paymentMethod = this.detectPaymentMethod();
            
            if (paymentMethod === 'stripe') {
                // Redirect to Stripe checkout
                window.location.href = `/billing/create-checkout-session/${planId}/`;
            } else if (paymentMethod === 'paystack') {
                // Initiate Paystack payment
                await this.initiatePaystackPayment(planId);
            } else {
                // Default to Stripe
                window.location.href = `/billing/create-checkout-session/${planId}/`;
            }
        } catch (error) {
            console.error('❌ Error upgrading plan:', error);
            this.showToast('Failed to initiate upgrade. Please try again.', 'error');
        }
    }

    /**
     * Detect preferred payment method based on user location
     */
    detectPaymentMethod() {
        // You can implement geo-detection or user preference here
        // For now, default to Stripe
        return 'stripe';
    }

    /**
     * Initiate Paystack payment
     */
    async initiatePaystackPayment(planId) {
        try {
            const response = await fetch('/billing/initiate-paystack-payment/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify({ plan_id: planId })
            });

            if (response.ok) {
                const data = await response.json();
                // Redirect to Paystack checkout
                window.location.href = data.checkout_url;
            } else {
                throw new Error('Failed to initiate Paystack payment');
            }
        } catch (error) {
            console.error('❌ Paystack payment error:', error);
            throw error;
        }
    }

    /**
     * View all plans
     */
    viewAllPlans() {
        window.location.href = '/pricing/';
    }

    /**
     * Check all usage limits periodically
     */
    checkUsageLimits() {
        // Check limits every 5 minutes
        setInterval(() => {
            this.loadUserData();
        }, 5 * 60 * 1000);
    }

    /**
     * Show toast notification
     */
    showToast(message, type = 'info', duration = 3000) {
        const toast = document.createElement('div');
        toast.className = `toast-notification toast-${type}`;
        toast.innerHTML = message;
        
        document.body.appendChild(toast);
        
        // Animate in
        setTimeout(() => toast.classList.add('show'), 100);
        
        // Remove after duration
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => document.body.removeChild(toast), 300);
        }, duration);
    }

    /**
     * Track events for analytics
     */
    trackEvent(eventName, properties = {}) {
        // Implement your analytics tracking here
        console.log('📊 Event tracked:', eventName, properties);
        
        // Example: Google Analytics
        if (typeof gtag !== 'undefined') {
            gtag('event', eventName, properties);
        }
    }

    /**
     * Get CSRF token for Django
     */
    getCSRFToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value || 
               document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
    }
}

// Initialize upgrade flow manager
let upgradeFlowManager;
document.addEventListener('DOMContentLoaded', () => {
    upgradeFlowManager = new UpgradeFlowManager();
});

// Global functions for modal interactions
function upgradeToplan(planId) {
    upgradeFlowManager.upgradeToplan(planId);
}

function viewAllPlans() {
    upgradeFlowManager.viewAllPlans();
}

// Toast notification styles
const toastStyles = `
<style>
.toast-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 10px;
    padding: 15px 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    z-index: 10000;
    transform: translateX(400px);
    transition: all 0.3s ease;
    max-width: 400px;
    border-left: 4px solid #007bff;
}

.toast-notification.show {
    transform: translateX(0);
}

.toast-notification.toast-warning {
    border-left-color: #ffc107;
}

.toast-notification.toast-error {
    border-left-color: #dc3545;
}

.toast-notification.toast-success {
    border-left-color: #28a745;
}

.usage-warning-toast .warning-content {
    display: flex;
    align-items: center;
    gap: 15px;
}

.usage-warning-toast i {
    color: #ffc107;
    font-size: 20px;
}

.usage-warning-toast .warning-text strong {
    display: block;
    margin-bottom: 5px;
    color: #495057;
}

.usage-warning-toast .warning-text p {
    margin: 0;
    font-size: 14px;
    color: #6c757d;
}
</style>
`;

// Inject toast styles
document.head.insertAdjacentHTML('beforeend', toastStyles);
