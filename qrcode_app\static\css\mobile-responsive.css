/* Enterprise QR Code Generator - Mobile Responsive Styles */

/* Base Mobile Styles */
@media (max-width: 767.98px) {
    body {
        font-size: 0.95rem;
    }

    .container {
        padding-left: 1.25rem;
        padding-right: 1.25rem;
    }

    h1 {
        font-size: 2rem;
    }

    h2 {
        font-size: 1.75rem;
    }

    h3 {
        font-size: 1.5rem;
    }

    .card {
        margin-bottom: 1.5rem;
    }

    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .d-md-flex .btn {
        width: auto;
    }
}

/* Corporate Bottom Navigation */
.bottom-nav {
    display: none;
}

@media (max-width: 767.98px) {
    .bottom-nav {
        display: flex;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(135deg, #1a2a6c, #2a4065);
        box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
        z-index: 1000;
        height: 65px;
        padding: 0 10px;
        border-top-left-radius: 20px;
        border-top-right-radius: 20px;
    }

    .bottom-nav-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: rgba(255, 255, 255, 0.7);
        text-decoration: none;
        font-size: 0.7rem;
        padding: 0.5rem;
        position: relative;
        transition: all 0.3s ease;
    }

    .bottom-nav-item i {
        font-size: 1.4rem;
        margin-bottom: 0.25rem;
        transition: all 0.3s ease;
    }

    .bottom-nav-item.active {
        color: #fff;
    }

    .bottom-nav-item.active i {
        transform: translateY(-5px);
    }

    .bottom-nav-item.active::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 5px;
        height: 5px;
        background-color: #fff;
        border-radius: 50%;
    }

    .bottom-nav-item:active {
        transform: scale(0.95);
    }

    /* Add padding to body to account for bottom nav */
    body {
        padding-bottom: 65px;
    }

    /* Hide footer on mobile */
    .footer {
        padding-bottom: 75px;
    }

    /* Center menu trigger */
    .bottom-nav-item.mobile-menu-trigger {
        position: relative;
    }

    .bottom-nav-item.mobile-menu-trigger i {
        background-color: var(--primary-color);
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        box-shadow: 0 4px 10px rgba(74, 108, 247, 0.3);
        margin-bottom: 0.1rem;
        margin-top: -10px;
    }
}

/* Floating Action Button */
.floating-action-btn {
    display: none;
}

@media (max-width: 767.98px) {
    .floating-action-btn {
        display: flex;
        position: fixed;
        bottom: 70px;
        right: 20px;
        width: 56px;
        height: 56px;
        border-radius: 50%;
        background-color: var(--primary-color);
        color: white;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        z-index: 1000;
        border: none;
        font-size: 1.5rem;
    }

    .floating-action-btn:active {
        transform: scale(0.95);
    }
}

/* Corporate Mobile Menu */
.mobile-menu {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #1a2a6c, #2a4065);
    z-index: 2000;
    overflow-y: auto;
    transform: translateX(100%);
    transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
}

.mobile-menu.active {
    transform: translateX(0);
}

.mobile-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.25rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-logo {
    display: flex;
    align-items: center;
    font-weight: 700;
    font-size: 1.5rem;
    color: #fff;
}

.mobile-logo i {
    margin-right: 0.75rem;
    color: #fff;
    background: var(--primary-color);
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    box-shadow: 0 4px 10px rgba(74, 108, 247, 0.3);
}

.mobile-menu-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #fff;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.mobile-menu-close:active {
    background-color: rgba(255, 255, 255, 0.1);
    transform: scale(0.95);
}

.mobile-menu-content {
    padding: 1.5rem;
}

.mobile-menu-section {
    margin-bottom: 2rem;
    animation: fadeInUp 0.5s ease forwards;
    opacity: 0;
}

.mobile-menu-section:nth-child(1) { animation-delay: 0.1s; }
.mobile-menu-section:nth-child(2) { animation-delay: 0.2s; }
.mobile-menu-section:nth-child(3) { animation-delay: 0.3s; }
.mobile-menu-section:nth-child(4) { animation-delay: 0.4s; }
.mobile-menu-section:nth-child(5) { animation-delay: 0.5s; }

.mobile-menu-section-title {
    font-weight: 600;
    margin-bottom: 1rem;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.mobile-menu-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    color: #fff;
    text-decoration: none;
    border-radius: 10px;
    margin-bottom: 0.75rem;
    transition: all 0.3s ease;
    background-color: rgba(255, 255, 255, 0.05);
}

.mobile-menu-item i {
    margin-right: 1rem;
    font-size: 1.25rem;
    width: 24px;
    text-align: center;
    color: rgba(255, 255, 255, 0.9);
}

.mobile-menu-item.active {
    background-color: var(--primary-color);
    color: #fff;
    box-shadow: 0 4px 15px rgba(74, 108, 247, 0.3);
}

.mobile-menu-item:active {
    transform: scale(0.98);
    background-color: rgba(255, 255, 255, 0.1);
}

.mobile-menu-divider {
    height: 1px;
    background-color: rgba(255, 255, 255, 0.1);
    margin: 1rem 0;
}

.mobile-menu-footer {
    text-align: center;
    color: rgba(255, 255, 255, 0.5);
    font-size: 0.875rem;
    padding: 1rem 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 2rem;
}

/* Mobile Swipe Gestures */
.swipe-container {
    touch-action: pan-y;
}

/* Pull to Refresh */
.pull-indicator {
    position: absolute;
    top: -50px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 1.5rem;
    color: var(--primary-color);
    transition: transform 0.3s;
}

.pull-to-refresh {
    position: relative;
    overflow-y: auto;
}
