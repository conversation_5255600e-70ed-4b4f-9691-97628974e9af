/**
 * Enterprise QR - Role-based Access Control System
 * 
 * This module implements a comprehensive role-based access control (RBAC) system
 * for the Enterprise QR application, allowing for fine-grained permission management
 * for different user roles within an organization.
 */

// Define permission constants
const PERMISSIONS = {
    // QR Code Permissions
    QR_CREATE: 'qr:create',
    QR_READ: 'qr:read',
    QR_UPDATE: 'qr:update',
    QR_DELETE: 'qr:delete',
    QR_BULK_CREATE: 'qr:bulk_create',
    QR_EXPORT: 'qr:export',
    QR_ANALYTICS_VIEW: 'qr:analytics_view',
    
    // User Management Permissions
    USER_CREATE: 'user:create',
    USER_READ: 'user:read',
    USER_UPDATE: 'user:update',
    USER_DELETE: 'user:delete',
    USER_ASSIGN_ROLE: 'user:assign_role',
    
    // Team Management Permissions
    TEAM_CREATE: 'team:create',
    TEAM_READ: 'team:read',
    TEAM_UPDATE: 'team:update',
    TEAM_DELETE: 'team:delete',
    TEAM_ASSIGN_USER: 'team:assign_user',
    
    // Organization Management Permissions
    ORG_UPDATE: 'org:update',
    ORG_BILLING: 'org:billing',
    ORG_SETTINGS: 'org:settings',
    
    // Ad Platform Permissions
    AD_CREATE: 'ad:create',
    AD_READ: 'ad:read',
    AD_UPDATE: 'ad:update',
    AD_DELETE: 'ad:delete',
    AD_ANALYTICS: 'ad:analytics',
    
    // API Access Permissions
    API_ACCESS: 'api:access',
    API_KEYS_MANAGE: 'api:keys_manage',
    
    // Audit Log Permissions
    AUDIT_VIEW: 'audit:view',
    
    // Security Permissions
    SECURITY_SETTINGS: 'security:settings',
    MFA_MANAGE: 'security:mfa_manage',
    
    // Integration Permissions
    INTEGRATION_MANAGE: 'integration:manage'
};

// Define role templates with associated permissions
const ROLE_TEMPLATES = {
    // System Roles
    SUPER_ADMIN: {
        name: 'Super Administrator',
        description: 'Full system access with all permissions',
        permissions: Object.values(PERMISSIONS)
    },
    
    ADMIN: {
        name: 'Administrator',
        description: 'Organization administrator with most permissions except critical system settings',
        permissions: [
            PERMISSIONS.QR_CREATE, PERMISSIONS.QR_READ, PERMISSIONS.QR_UPDATE, PERMISSIONS.QR_DELETE,
            PERMISSIONS.QR_BULK_CREATE, PERMISSIONS.QR_EXPORT, PERMISSIONS.QR_ANALYTICS_VIEW,
            PERMISSIONS.USER_CREATE, PERMISSIONS.USER_READ, PERMISSIONS.USER_UPDATE, PERMISSIONS.USER_DELETE,
            PERMISSIONS.USER_ASSIGN_ROLE,
            PERMISSIONS.TEAM_CREATE, PERMISSIONS.TEAM_READ, PERMISSIONS.TEAM_UPDATE, PERMISSIONS.TEAM_DELETE,
            PERMISSIONS.TEAM_ASSIGN_USER,
            PERMISSIONS.ORG_UPDATE, PERMISSIONS.ORG_BILLING, PERMISSIONS.ORG_SETTINGS,
            PERMISSIONS.AD_CREATE, PERMISSIONS.AD_READ, PERMISSIONS.AD_UPDATE, PERMISSIONS.AD_DELETE,
            PERMISSIONS.AD_ANALYTICS,
            PERMISSIONS.API_ACCESS, PERMISSIONS.API_KEYS_MANAGE,
            PERMISSIONS.AUDIT_VIEW,
            PERMISSIONS.SECURITY_SETTINGS, PERMISSIONS.MFA_MANAGE,
            PERMISSIONS.INTEGRATION_MANAGE
        ]
    },
    
    MANAGER: {
        name: 'Manager',
        description: 'Team manager with permissions to manage users and their QR codes',
        permissions: [
            PERMISSIONS.QR_CREATE, PERMISSIONS.QR_READ, PERMISSIONS.QR_UPDATE, PERMISSIONS.QR_DELETE,
            PERMISSIONS.QR_BULK_CREATE, PERMISSIONS.QR_EXPORT, PERMISSIONS.QR_ANALYTICS_VIEW,
            PERMISSIONS.USER_READ, PERMISSIONS.USER_UPDATE,
            PERMISSIONS.TEAM_READ, PERMISSIONS.TEAM_ASSIGN_USER,
            PERMISSIONS.AD_CREATE, PERMISSIONS.AD_READ, PERMISSIONS.AD_UPDATE, PERMISSIONS.AD_DELETE,
            PERMISSIONS.AD_ANALYTICS,
            PERMISSIONS.API_ACCESS,
            PERMISSIONS.AUDIT_VIEW
        ]
    },
    
    CREATOR: {
        name: 'Creator',
        description: 'Standard user with permissions to create and manage QR codes',
        permissions: [
            PERMISSIONS.QR_CREATE, PERMISSIONS.QR_READ, PERMISSIONS.QR_UPDATE, PERMISSIONS.QR_DELETE,
            PERMISSIONS.QR_EXPORT, PERMISSIONS.QR_ANALYTICS_VIEW,
            PERMISSIONS.AD_CREATE, PERMISSIONS.AD_READ, PERMISSIONS.AD_UPDATE, PERMISSIONS.AD_DELETE,
            PERMISSIONS.API_ACCESS
        ]
    },
    
    VIEWER: {
        name: 'Viewer',
        description: 'Read-only access to QR codes and analytics',
        permissions: [
            PERMISSIONS.QR_READ, PERMISSIONS.QR_ANALYTICS_VIEW,
            PERMISSIONS.AD_READ
        ]
    },
    
    // Specialized Roles
    MARKETING: {
        name: 'Marketing Specialist',
        description: 'Focused on QR creation and analytics for marketing purposes',
        permissions: [
            PERMISSIONS.QR_CREATE, PERMISSIONS.QR_READ, PERMISSIONS.QR_UPDATE, PERMISSIONS.QR_DELETE,
            PERMISSIONS.QR_BULK_CREATE, PERMISSIONS.QR_EXPORT, PERMISSIONS.QR_ANALYTICS_VIEW,
            PERMISSIONS.AD_CREATE, PERMISSIONS.AD_READ, PERMISSIONS.AD_UPDATE, PERMISSIONS.AD_DELETE,
            PERMISSIONS.AD_ANALYTICS
        ]
    },
    
    DEVELOPER: {
        name: 'Developer',
        description: 'Technical role with API access and integration capabilities',
        permissions: [
            PERMISSIONS.QR_CREATE, PERMISSIONS.QR_READ, PERMISSIONS.QR_UPDATE,
            PERMISSIONS.QR_BULK_CREATE, PERMISSIONS.QR_EXPORT,
            PERMISSIONS.API_ACCESS, PERMISSIONS.API_KEYS_MANAGE,
            PERMISSIONS.INTEGRATION_MANAGE
        ]
    },
    
    SECURITY: {
        name: 'Security Officer',
        description: 'Focused on security settings and audit logs',
        permissions: [
            PERMISSIONS.USER_READ,
            PERMISSIONS.TEAM_READ,
            PERMISSIONS.AUDIT_VIEW,
            PERMISSIONS.SECURITY_SETTINGS, PERMISSIONS.MFA_MANAGE
        ]
    },
    
    BILLING: {
        name: 'Billing Administrator',
        description: 'Manages organization billing and subscriptions',
        permissions: [
            PERMISSIONS.ORG_BILLING,
            PERMISSIONS.USER_READ,
            PERMISSIONS.TEAM_READ,
            PERMISSIONS.AUDIT_VIEW
        ]
    }
};

/**
 * Role-based Access Control (RBAC) class for managing permissions
 */
class RBAC {
    constructor() {
        this.currentUser = null;
        this.userRoles = [];
        this.userPermissions = new Set();
        this.customRoles = {};
        
        // Initialize from localStorage if available
        this.loadFromStorage();
    }
    
    /**
     * Load user roles and permissions from localStorage
     */
    loadFromStorage() {
        try {
            // Load current user
            const userInfo = localStorage.getItem('user_info');
            if (userInfo) {
                this.currentUser = JSON.parse(userInfo);
            }
            
            // Load user roles
            const userRoles = localStorage.getItem('user_roles');
            if (userRoles) {
                this.userRoles = JSON.parse(userRoles);
                this.computePermissions();
            }
            
            // Load custom roles
            const customRoles = localStorage.getItem('custom_roles');
            if (customRoles) {
                this.customRoles = JSON.parse(customRoles);
            }
        } catch (error) {
            console.error('Error loading RBAC data from storage:', error);
        }
    }
    
    /**
     * Save RBAC data to localStorage
     */
    saveToStorage() {
        try {
            localStorage.setItem('user_roles', JSON.stringify(this.userRoles));
            localStorage.setItem('custom_roles', JSON.stringify(this.customRoles));
        } catch (error) {
            console.error('Error saving RBAC data to storage:', error);
        }
    }
    
    /**
     * Compute user permissions based on assigned roles
     */
    computePermissions() {
        this.userPermissions = new Set();
        
        // Add permissions from each role
        this.userRoles.forEach(roleName => {
            const role = ROLE_TEMPLATES[roleName] || this.customRoles[roleName];
            if (role && role.permissions) {
                role.permissions.forEach(permission => {
                    this.userPermissions.add(permission);
                });
            }
        });
    }
    
    /**
     * Set the current user and their roles
     * @param {Object} user - User object
     * @param {Array} roles - Array of role names
     */
    setUser(user, roles = ['VIEWER']) {
        this.currentUser = user;
        this.userRoles = roles;
        this.computePermissions();
        this.saveToStorage();
        
        // Update UI based on permissions
        this.updateUI();
    }
    
    /**
     * Check if the current user has a specific permission
     * @param {string} permission - Permission to check
     * @returns {boolean} - Whether the user has the permission
     */
    hasPermission(permission) {
        return this.userPermissions.has(permission);
    }
    
    /**
     * Check if the current user has all of the specified permissions
     * @param {Array} permissions - Array of permissions to check
     * @returns {boolean} - Whether the user has all permissions
     */
    hasAllPermissions(permissions) {
        return permissions.every(permission => this.userPermissions.has(permission));
    }
    
    /**
     * Check if the current user has any of the specified permissions
     * @param {Array} permissions - Array of permissions to check
     * @returns {boolean} - Whether the user has any of the permissions
     */
    hasAnyPermission(permissions) {
        return permissions.some(permission => this.userPermissions.has(permission));
    }
    
    /**
     * Check if the current user has a specific role
     * @param {string} roleName - Role to check
     * @returns {boolean} - Whether the user has the role
     */
    hasRole(roleName) {
        return this.userRoles.includes(roleName);
    }
    
    /**
     * Add a role to the current user
     * @param {string} roleName - Role to add
     */
    addRole(roleName) {
        if (!this.hasRole(roleName)) {
            this.userRoles.push(roleName);
            this.computePermissions();
            this.saveToStorage();
            this.updateUI();
        }
    }
    
    /**
     * Remove a role from the current user
     * @param {string} roleName - Role to remove
     */
    removeRole(roleName) {
        const index = this.userRoles.indexOf(roleName);
        if (index !== -1) {
            this.userRoles.splice(index, 1);
            this.computePermissions();
            this.saveToStorage();
            this.updateUI();
        }
    }
    
    /**
     * Create a custom role
     * @param {string} roleName - Name of the custom role
     * @param {string} description - Description of the role
     * @param {Array} permissions - Array of permissions for the role
     */
    createCustomRole(roleName, description, permissions) {
        this.customRoles[roleName] = {
            name: roleName,
            description: description,
            permissions: permissions
        };
        this.saveToStorage();
    }
    
    /**
     * Update the UI based on user permissions
     */
    updateUI() {
        // Hide/show elements based on permissions
        document.querySelectorAll('[data-requires-permission]').forEach(element => {
            const requiredPermission = element.getAttribute('data-requires-permission');
            if (this.hasPermission(requiredPermission)) {
                element.classList.remove('hidden');
            } else {
                element.classList.add('hidden');
            }
        });
        
        // Hide/show elements based on roles
        document.querySelectorAll('[data-requires-role]').forEach(element => {
            const requiredRole = element.getAttribute('data-requires-role');
            if (this.hasRole(requiredRole)) {
                element.classList.remove('hidden');
            } else {
                element.classList.add('hidden');
            }
        });
    }
}

// Create global RBAC instance
const rbac = new RBAC();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        PERMISSIONS,
        ROLE_TEMPLATES,
        RBAC,
        rbac
    };
}
