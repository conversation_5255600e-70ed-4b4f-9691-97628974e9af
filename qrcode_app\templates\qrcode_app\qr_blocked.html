<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Access Blocked - QR Code</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .blocked-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 500px;
            margin: 2rem;
        }
        
        .blocked-icon {
            font-size: 4rem;
            color: #dc3545;
            margin-bottom: 1.5rem;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .blocked-title {
            color: #dc3545;
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }
        
        .blocked-reason {
            color: #6c757d;
            font-size: 1.1rem;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .qr-info {
            background: rgba(108, 117, 125, 0.1);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 2rem;
        }
        
        .qr-name {
            font-weight: bold;
            color: #495057;
            margin-bottom: 0.5rem;
        }
        
        .qr-type {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .security-info {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 2rem;
        }
        
        .security-info h6 {
            margin-bottom: 0.5rem;
            font-weight: bold;
        }
        
        .security-info p {
            margin-bottom: 0;
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .contact-info {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 10px;
            padding: 1rem;
            color: #495057;
        }
        
        .contact-info h6 {
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        
        .fade-in {
            animation: fadeIn 0.8s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="blocked-container fade-in">
        <div class="blocked-icon">
            <i class="fas fa-shield-alt"></i>
        </div>
        
        <h1 class="blocked-title">Access Blocked</h1>
        
        <div class="blocked-reason">
            {{ reason }}
        </div>
        
        {% if qr_code %}
        <div class="qr-info">
            <div class="qr-name">{{ qr_code.name }}</div>
            <div class="qr-type">{{ qr_code.get_qr_type_display }} QR Code</div>
        </div>
        {% endif %}
        
        <div class="security-info">
            <h6><i class="fas fa-exclamation-triangle me-2"></i>Security Notice</h6>
            <p>This QR code has enterprise security controls enabled. Access has been restricted based on your connection type or location.</p>
        </div>
        
        <div class="contact-info">
            <h6><i class="fas fa-envelope me-2"></i>Need Access?</h6>
            <p>If you believe this is an error, please contact the QR code owner or administrator for assistance.</p>
        </div>
        
        <div class="mt-4">
            <button onclick="history.back()" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Go Back
            </button>
        </div>
    </div>
    
    <script>
        // Log the blocked access attempt for analytics
        console.log('QR Code access blocked:', {
            reason: '{{ reason|escapejs }}',
            qr_code: '{{ qr_code.name|escapejs }}',
            timestamp: new Date().toISOString()
        });
    </script>
</body>
</html>
