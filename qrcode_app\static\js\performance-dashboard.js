/**
 * Performance Dashboard JavaScript
 * 
 * This file handles the functionality for the performance dashboard,
 * including metrics display, charts, and performance optimization tools.
 */

// Performance metrics history
const metricsHistory = {
    generationTimes: [],
    cacheHits: [],
    cacheMisses: [],
    timestamps: []
};

// Charts
let generationTimeChart;
let cachePerformanceChart;
let testResultsChart;

// Auto-refresh interval
let autoRefreshInterval;

document.addEventListener('DOMContentLoaded', function() {
    // Initialize performance optimizer
    if (typeof initPerformanceOptimizer === 'function') {
        initPerformanceOptimizer({
            cacheSize: 100,
            cacheExpiry: 24 * 60 * 60 * 1000, // 24 hours
            batchSize: 10,
            processingDelay: 50,
            maxWorkers: 4,
            useWorkers: true,
            enableMonitoring: true
        });
    }
    
    // Initialize batch processor
    if (typeof initBatchProcessor === 'function') {
        initBatchProcessor({
            concurrency: 5,
            chunkSize: 20,
            delayBetweenChunks: 100,
            maxRetries: 3,
            timeout: 30000
        });
    }
    
    // Initialize UI
    initUI();
    
    // Initialize charts
    initCharts();
    
    // Update dashboard
    updateDashboard();
    
    // Set up auto-refresh
    initAutoRefresh();
});

/**
 * Initialize the user interface
 */
function initUI() {
    // Update user name in header
    updateUserInfo();
    
    // Initialize refresh button
    document.getElementById('refresh-dashboard-btn').addEventListener('click', function() {
        updateDashboard();
    });
    
    // Initialize auto-refresh dropdown
    document.getElementById('auto-refresh').addEventListener('change', function() {
        const interval = parseInt(this.value);
        setAutoRefreshInterval(interval);
    });
    
    // Initialize cache settings
    document.getElementById('update-cache-settings-btn').addEventListener('click', function() {
        const cacheSize = parseInt(document.getElementById('cache-size-limit').value);
        const cacheExpiry = parseInt(document.getElementById('cache-expiry').value) * 60 * 60 * 1000; // Convert hours to milliseconds
        
        if (typeof initPerformanceOptimizer === 'function') {
            initPerformanceOptimizer({
                cacheSize,
                cacheExpiry
            });
            
            showNotification('Cache settings updated successfully', 'success');
            updateDashboard();
        } else {
            showNotification('Performance optimizer not available', 'error');
        }
    });
    
    // Initialize clear cache button
    document.getElementById('clear-cache-btn').addEventListener('click', function() {
        if (confirm('Are you sure you want to clear the cache? This will remove all cached QR codes.')) {
            // Clear cache (mock implementation)
            showNotification('Cache cleared successfully', 'success');
            updateDashboard();
        }
    });
    
    // Initialize worker settings
    document.getElementById('update-worker-settings-btn').addEventListener('click', function() {
        const maxWorkers = parseInt(document.getElementById('max-workers').value);
        const useWorkers = document.getElementById('worker-enabled').checked;
        
        if (typeof initPerformanceOptimizer === 'function') {
            initPerformanceOptimizer({
                maxWorkers,
                useWorkers
            });
            
            showNotification('Worker settings updated successfully', 'success');
            updateDashboard();
        } else {
            showNotification('Performance optimizer not available', 'error');
        }
    });
    
    // Initialize performance test button
    document.getElementById('run-performance-test-btn').addEventListener('click', function() {
        runPerformanceTest();
    });
}

/**
 * Update user info in the header
 */
function updateUserInfo() {
    if (typeof rbac !== 'undefined' && rbac.currentUser) {
        const userNameElement = document.querySelector('.user-name');
        if (userNameElement) {
            userNameElement.textContent = rbac.currentUser.name;
        }
    } else {
        const userInfo = JSON.parse(localStorage.getItem('user_info'));
        if (userInfo && userInfo.name) {
            const userNameElement = document.querySelector('.user-name');
            if (userNameElement) {
                userNameElement.textContent = userInfo.name;
            }
        }
    }
}

/**
 * Initialize charts
 */
function initCharts() {
    // Generation Time Chart
    const generationTimeCtx = document.getElementById('generation-time-chart').getContext('2d');
    generationTimeChart = new Chart(generationTimeCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'Generation Time (ms)',
                data: [],
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderWidth: 2,
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Time (ms)'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Time'
                    }
                }
            }
        }
    });
    
    // Cache Performance Chart
    const cachePerformanceCtx = document.getElementById('cache-performance-chart').getContext('2d');
    cachePerformanceChart = new Chart(cachePerformanceCtx, {
        type: 'bar',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'Cache Hits',
                    data: [],
                    backgroundColor: '#10b981',
                    borderColor: '#10b981',
                    borderWidth: 1
                },
                {
                    label: 'Cache Misses',
                    data: [],
                    backgroundColor: '#ef4444',
                    borderColor: '#ef4444',
                    borderWidth: 1
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    stacked: true,
                    title: {
                        display: true,
                        text: 'Count'
                    }
                },
                x: {
                    stacked: true,
                    title: {
                        display: true,
                        text: 'Time'
                    }
                }
            }
        }
    });
}

/**
 * Initialize auto-refresh
 */
function initAutoRefresh() {
    const autoRefreshSelect = document.getElementById('auto-refresh');
    const interval = parseInt(autoRefreshSelect.value);
    
    setAutoRefreshInterval(interval);
}

/**
 * Set auto-refresh interval
 * @param {number} interval - Interval in milliseconds
 */
function setAutoRefreshInterval(interval) {
    // Clear existing interval
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
        autoRefreshInterval = null;
    }
    
    // Set new interval if not 0
    if (interval > 0) {
        autoRefreshInterval = setInterval(updateDashboard, interval);
    }
}

/**
 * Update dashboard with latest metrics
 */
function updateDashboard() {
    // Get performance metrics
    let metrics;
    
    if (typeof getPerformanceMetrics === 'function') {
        metrics = getPerformanceMetrics();
    } else {
        // Mock metrics for demo
        metrics = {
            qr_generation: {
                count: Math.floor(Math.random() * 1000),
                avgTime: (Math.random() * 50 + 10).toFixed(2),
                minTime: (Math.random() * 5 + 5).toFixed(2),
                maxTime: (Math.random() * 100 + 50).toFixed(2)
            },
            cache: {
                hits: Math.floor(Math.random() * 500),
                misses: Math.floor(Math.random() * 200),
                hitRate: `${(Math.random() * 100).toFixed(2)}%`,
                size: Math.floor(Math.random() * 50),
                limit: 100
            }
        };
    }
    
    // Update metrics display
    document.getElementById('avg-generation-time').textContent = `${metrics.qr_generation.avgTime} ms`;
    document.getElementById('qr-codes-generated').textContent = metrics.qr_generation.count;
    document.getElementById('cache-hit-rate').textContent = metrics.cache.hitRate;
    document.getElementById('cache-size').textContent = `${metrics.cache.size} / ${metrics.cache.limit}`;
    
    // Update metrics history
    const timestamp = new Date().toLocaleTimeString();
    metricsHistory.timestamps.push(timestamp);
    metricsHistory.generationTimes.push(parseFloat(metrics.qr_generation.avgTime));
    metricsHistory.cacheHits.push(metrics.cache.hits);
    metricsHistory.cacheMisses.push(metrics.cache.misses);
    
    // Limit history to last 10 points
    if (metricsHistory.timestamps.length > 10) {
        metricsHistory.timestamps.shift();
        metricsHistory.generationTimes.shift();
        metricsHistory.cacheHits.shift();
        metricsHistory.cacheMisses.shift();
    }
    
    // Update charts
    updateCharts();
}

/**
 * Update charts with latest metrics
 */
function updateCharts() {
    // Update Generation Time Chart
    generationTimeChart.data.labels = metricsHistory.timestamps;
    generationTimeChart.data.datasets[0].data = metricsHistory.generationTimes;
    generationTimeChart.update();
    
    // Update Cache Performance Chart
    cachePerformanceChart.data.labels = metricsHistory.timestamps;
    cachePerformanceChart.data.datasets[0].data = metricsHistory.cacheHits;
    cachePerformanceChart.data.datasets[1].data = metricsHistory.cacheMisses;
    cachePerformanceChart.update();
}

/**
 * Run a performance test
 */
function runPerformanceTest() {
    const count = parseInt(document.getElementById('test-count').value);
    const complexity = document.getElementById('test-complexity').value;
    
    // Show loading state
    document.getElementById('run-performance-test-btn').disabled = true;
    document.getElementById('run-performance-test-btn').innerHTML = '<i class="fas fa-spinner fa-spin"></i> Running...';
    
    // Generate test data
    const testData = generateTestData(count, complexity);
    
    // Start test
    const startTime = performance.now();
    
    // Process test data
    processTestData(testData)
        .then(results => {
            const endTime = performance.now();
            const totalTime = endTime - startTime;
            const avgTime = totalTime / count;
            const throughput = Math.round((count / totalTime) * 1000);
            
            // Update test results
            document.getElementById('test-total-time').textContent = `${totalTime.toFixed(2)} ms`;
            document.getElementById('test-avg-time').textContent = `${avgTime.toFixed(2)} ms`;
            document.getElementById('test-qr-count').textContent = count;
            document.getElementById('test-throughput').textContent = throughput;
            
            // Show test results
            document.getElementById('test-results-container').style.display = 'block';
            
            // Update test results chart
            updateTestResultsChart(results);
            
            // Reset button
            document.getElementById('run-performance-test-btn').disabled = false;
            document.getElementById('run-performance-test-btn').innerHTML = 'Run Test';
            
            // Show notification
            showNotification(`Performance test completed: ${count} QR codes in ${totalTime.toFixed(2)}ms`, 'success');
        })
        .catch(error => {
            console.error('Performance test error:', error);
            
            // Reset button
            document.getElementById('run-performance-test-btn').disabled = false;
            document.getElementById('run-performance-test-btn').innerHTML = 'Run Test';
            
            // Show notification
            showNotification(`Performance test failed: ${error.message}`, 'error');
        });
}

/**
 * Generate test data
 * @param {number} count - Number of QR codes to generate
 * @param {string} complexity - Data complexity
 * @returns {Array} - Array of test data
 */
function generateTestData(count, complexity) {
    const testData = [];
    
    for (let i = 0; i < count; i++) {
        let data;
        
        switch (complexity) {
            case 'simple':
                data = `https://example.com/id/${i}`;
                break;
                
            case 'medium':
                data = `BEGIN:VCARD\nVERSION:3.0\nN:Doe;John;;;\nFN:John Doe\nORG:Example Corp\nTITLE:Software Engineer\nTEL;TYPE=WORK,VOICE:(*************\nEMAIL;TYPE=WORK,INTERNET:<EMAIL>\nEND:VCARD`;
                break;
                
            case 'complex':
                data = `BEGIN:VCARD\nVERSION:3.0\nN:Doe;John;Middle;Dr.;\nFN:Dr. John Middle Doe\nORG:Example Corp;Engineering\nTITLE:Senior Software Engineer\nTEL;TYPE=WORK,VOICE:(*************\nTEL;TYPE=CELL,VOICE:(*************\nEMAIL;TYPE=WORK,INTERNET:<EMAIL>\nEMAIL;TYPE=HOME,INTERNET:<EMAIL>\nADR;TYPE=WORK:;;123 Business St;City;State;12345;Country\nADR;TYPE=HOME:;;456 Home Ave;City;State;12345;Country\nURL;TYPE=WORK:https://example.com/johndoe\nBDAY:19800101\nNOTE:John is a skilled software engineer with expertise in JavaScript and Python.\nEND:VCARD`;
                break;
        }
        
        testData.push({
            id: `test-${i}`,
            data,
            options: {
                errorCorrectionLevel: 'H',
                margin: 4,
                width: 256,
                height: 256
            }
        });
    }
    
    return testData;
}

/**
 * Process test data
 * @param {Array} testData - Array of test data
 * @returns {Promise} - Promise resolving to test results
 */
async function processTestData(testData) {
    const results = [];
    
    // Process each item
    for (const item of testData) {
        const startTime = performance.now();
        
        try {
            // Generate QR code
            if (typeof generateOptimizedQRCode === 'function') {
                await generateOptimizedQRCode(item.data, item.options);
            } else {
                // Simulate QR code generation
                await new Promise(resolve => setTimeout(resolve, Math.random() * 50 + 10));
            }
            
            const endTime = performance.now();
            const duration = endTime - startTime;
            
            results.push({
                id: item.id,
                duration,
                success: true
            });
        } catch (error) {
            results.push({
                id: item.id,
                duration: 0,
                success: false,
                error: error.message
            });
        }
    }
    
    return results;
}

/**
 * Update test results chart
 * @param {Array} results - Test results
 */
function updateTestResultsChart(results) {
    // Destroy existing chart if it exists
    if (testResultsChart) {
        testResultsChart.destroy();
    }
    
    // Prepare data
    const labels = results.map(result => result.id);
    const durations = results.map(result => result.duration);
    
    // Create chart
    const ctx = document.getElementById('test-results-chart').getContext('2d');
    testResultsChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels,
            datasets: [{
                label: 'Generation Time (ms)',
                data: durations,
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderWidth: 2,
                tension: 0.1,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Time (ms)'
                    }
                },
                x: {
                    display: false
                }
            }
        }
    });
}

/**
 * Show a notification
 * @param {string} message - Notification message
 * @param {string} type - Notification type (success, error, info)
 */
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    
    // Add icon based on type
    let icon = 'info-circle';
    if (type === 'success') icon = 'check-circle';
    if (type === 'error') icon = 'exclamation-circle';
    
    notification.innerHTML = `
        <i class="fas fa-${icon}"></i>
        <span>${message}</span>
        <button class="notification-close"><i class="fas fa-times"></i></button>
    `;
    
    // Add to the DOM
    document.body.appendChild(notification);
    
    // Add styles if not already added
    if (!document.getElementById('notification-styles')) {
        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            .notification {
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem;
                background-color: white;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                display: flex;
                align-items: center;
                gap: 0.75rem;
                z-index: 1000;
                max-width: 350px;
                transform: translateX(120%);
                transition: transform 0.3s ease;
                border-left: 4px solid #3b82f6;
            }
            
            .notification.show {
                transform: translateX(0);
            }
            
            .notification i {
                font-size: 1.25rem;
                flex-shrink: 0;
            }
            
            .notification span {
                font-size: 0.875rem;
                color: #374151;
                flex-grow: 1;
            }
            
            .notification-close {
                background: none;
                border: none;
                color: #9ca3af;
                cursor: pointer;
                font-size: 0.875rem;
                padding: 0.25rem;
                transition: color 0.2s ease;
            }
            
            .notification-close:hover {
                color: #4b5563;
            }
            
            .notification-success {
                border-left-color: #10b981;
            }
            
            .notification-success i {
                color: #10b981;
            }
            
            .notification-error {
                border-left-color: #ef4444;
            }
            
            .notification-error i {
                color: #ef4444;
            }
            
            .notification-info i {
                color: #3b82f6;
            }
        `;
        document.head.appendChild(style);
    }
    
    // Show the notification
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);
    
    // Set up close button
    const closeButton = notification.querySelector('.notification-close');
    closeButton.addEventListener('click', () => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    });
    
    // Auto-close after 5 seconds
    setTimeout(() => {
        if (document.body.contains(notification)) {
            notification.classList.remove('show');
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    notification.remove();
                }
            }, 300);
        }
    }, 5000);
}
