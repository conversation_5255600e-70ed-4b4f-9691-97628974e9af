/**
 * Carousel Functionality
 * This file contains all the JavaScript needed for the ad carousels
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize standard carousels
    initStandardCarousels();
    
    // Initialize premium carousels
    initPremiumCarousels();
});

/**
 * Initialize all standard ad carousels
 */
function initStandardCarousels() {
    const carousels = document.querySelectorAll('.ad-carousel');
    
    carousels.forEach(carousel => {
        const location = carousel.id.replace('adCarousel-', '');
        
        // Set up auto-rotation
        setInterval(() => {
            moveCarousel(location, 1);
        }, 5000); // Rotate every 5 seconds
    });
}

/**
 * Move carousel in the specified direction
 * @param {string} location - Carousel location identifier
 * @param {number} direction - Direction to move (1 for next, -1 for previous)
 */
function moveCarousel(location, direction) {
    const carousel = document.getElementById(`adCarousel-${location}`);
    if (!carousel) return;
    
    const items = carousel.querySelectorAll('.ad-carousel-item');
    const dots = carousel.querySelectorAll('.ad-carousel-dot');
    
    if (items.length <= 1) return; // No need to move if only one item
    
    // Find the currently active item
    let activeIndex = 0;
    items.forEach((item, index) => {
        if (item.classList.contains('active')) {
            activeIndex = index;
        }
    });
    
    // Calculate the new index
    let newIndex = activeIndex + direction;
    
    // Handle wrapping
    if (newIndex >= items.length) {
        newIndex = 0;
    } else if (newIndex < 0) {
        newIndex = items.length - 1;
    }
    
    // Update active classes
    items.forEach(item => item.classList.remove('active'));
    dots.forEach(dot => dot.classList.remove('active'));
    
    items[newIndex].classList.add('active');
    dots[newIndex].classList.add('active');
    
    // Track impression for the newly visible ad
    const adId = items[newIndex].getAttribute('data-ad-id');
    if (adId && typeof trackImpression === 'function') {
        trackImpression(adId, location);
    }
}

/**
 * Jump to a specific slide
 * @param {string} location - Carousel location identifier
 * @param {number} index - Index of the slide to jump to
 */
function jumpToSlide(location, index) {
    const carousel = document.getElementById(`adCarousel-${location}`);
    if (!carousel) return;
    
    const items = carousel.querySelectorAll('.ad-carousel-item');
    const dots = carousel.querySelectorAll('.ad-carousel-dot');
    
    if (index < 0 || index >= items.length) return; // Invalid index
    
    // Update active classes
    items.forEach(item => item.classList.remove('active'));
    dots.forEach(dot => dot.classList.remove('active'));
    
    items[index].classList.add('active');
    dots[index].classList.add('active');
    
    // Track impression for the newly visible ad
    const adId = items[index].getAttribute('data-ad-id');
    if (adId && typeof trackImpression === 'function') {
        trackImpression(adId, location);
    }
}

/**
 * Initialize all premium ad carousels
 */
function initPremiumCarousels() {
    const carousels = document.querySelectorAll('.premium-ad-carousel');
    
    carousels.forEach(carousel => {
        const items = carousel.querySelectorAll('.premium-ad-item');
        
        if (items.length <= 1) return; // No need for carousel with only one item
        
        const indicators = carousel.querySelectorAll('.premium-ad-indicator');
        const prevBtn = carousel.querySelector('.premium-ad-carousel-prev');
        const nextBtn = carousel.querySelector('.premium-ad-carousel-next');
        
        let currentIndex = 0;
        let interval;
        
        // Make sure all items except the first one are hidden initially
        items.forEach((item, index) => {
            if (index === 0) {
                item.classList.add('active');
            } else {
                item.classList.remove('active');
            }
        });
        
        // Make sure all indicators except the first one are inactive initially
        indicators.forEach((indicator, index) => {
            if (index === 0) {
                indicator.classList.add('active');
            } else {
                indicator.classList.remove('active');
            }
        });
        
        // Start automatic rotation
        startPremiumAutoRotation(carousel, items, indicators);
        
        // Event listeners for controls
        if (prevBtn) {
            prevBtn.addEventListener('click', () => {
                goToPremiumSlide(carousel, items, indicators, currentIndex - 1);
                resetPremiumAutoRotation(carousel, items, indicators);
            });
        }
        
        if (nextBtn) {
            nextBtn.addEventListener('click', () => {
                goToPremiumSlide(carousel, items, indicators, currentIndex + 1);
                resetPremiumAutoRotation(carousel, items, indicators);
            });
        }
        
        // Event listeners for indicators
        indicators.forEach((indicator, index) => {
            indicator.addEventListener('click', () => {
                goToPremiumSlide(carousel, items, indicators, index);
                resetPremiumAutoRotation(carousel, items, indicators);
            });
        });
        
        // Pause rotation on hover
        carousel.addEventListener('mouseenter', () => {
            clearPremiumInterval(carousel);
        });
        
        // Resume rotation on mouse leave
        carousel.addEventListener('mouseleave', () => {
            startPremiumAutoRotation(carousel, items, indicators);
        });
    });
}

/**
 * Go to a specific slide in premium carousel
 * @param {HTMLElement} carousel - Carousel element
 * @param {NodeList} items - Carousel items
 * @param {NodeList} indicators - Carousel indicators
 * @param {number} index - Index of the slide to go to
 */
function goToPremiumSlide(carousel, items, indicators, index) {
    // Get current index
    let currentIndex = 0;
    items.forEach((item, i) => {
        if (item.classList.contains('active')) {
            currentIndex = i;
        }
    });
    
    // Calculate the new index with wrapping
    let newIndex = index;
    if (newIndex >= items.length) {
        newIndex = 0;
    } else if (newIndex < 0) {
        newIndex = items.length - 1;
    }
    
    // Skip if already on the target slide
    if (newIndex === currentIndex) return;
    
    // Update active classes
    items.forEach((item, i) => {
        if (i === newIndex) {
            item.classList.add('active');
        } else {
            item.classList.remove('active');
        }
    });
    
    indicators.forEach((indicator, i) => {
        if (i === newIndex) {
            indicator.classList.add('active');
        } else {
            indicator.classList.remove('active');
        }
    });
    
    // Track impression for the newly visible ad
    const adId = items[newIndex].getAttribute('data-ad-id');
    if (adId && typeof trackAdImpression === 'function') {
        trackAdImpression(adId);
    }
    
    // Store the current index on the carousel element
    carousel.setAttribute('data-current-index', newIndex);
}

/**
 * Start automatic rotation for premium carousel
 * @param {HTMLElement} carousel - Carousel element
 * @param {NodeList} items - Carousel items
 * @param {NodeList} indicators - Carousel indicators
 */
function startPremiumAutoRotation(carousel, items, indicators) {
    // Clear any existing interval
    clearPremiumInterval(carousel);
    
    // Set new interval
    const interval = setInterval(() => {
        // Get current index
        let currentIndex = parseInt(carousel.getAttribute('data-current-index') || 0);
        
        // Go to next slide
        goToPremiumSlide(carousel, items, indicators, currentIndex + 1);
    }, 5000); // Rotate every 5 seconds
    
    // Store the interval ID on the carousel element
    carousel.setAttribute('data-interval-id', interval);
}

/**
 * Reset automatic rotation for premium carousel
 * @param {HTMLElement} carousel - Carousel element
 * @param {NodeList} items - Carousel items
 * @param {NodeList} indicators - Carousel indicators
 */
function resetPremiumAutoRotation(carousel, items, indicators) {
    clearPremiumInterval(carousel);
    startPremiumAutoRotation(carousel, items, indicators);
}

/**
 * Clear interval for premium carousel
 * @param {HTMLElement} carousel - Carousel element
 */
function clearPremiumInterval(carousel) {
    const intervalId = carousel.getAttribute('data-interval-id');
    if (intervalId) {
        clearInterval(parseInt(intervalId));
        carousel.removeAttribute('data-interval-id');
    }
}

/**
 * Close sticky ad
 * @param {string} location - Sticky ad location identifier
 */
function closeSticky(location) {
    const sticky = document.getElementById(`adSticky-${location}`);
    if (sticky) {
        sticky.style.animation = 'adStickySlideDown 0.5s ease forwards';
        setTimeout(() => {
            sticky.style.display = 'none';
        }, 500);
        
        // Store in localStorage that this sticky ad was closed
        localStorage.setItem(`adSticky-${location}-closed`, 'true');
    }
}
