{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}📊 QR Scan Analytics{% endblock %}

{% block extrahead %}
<style>
    .scan-dashboard {
        padding: 20px;
    }

    .dashboard-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        border-radius: 12px;
        margin-bottom: 30px;
        box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
    }

    .dashboard-header h1 {
        margin: 0;
        font-size: 2.5rem;
        font-weight: 700;
    }

    .dashboard-header p {
        margin: 10px 0 0 0;
        opacity: 0.9;
        font-size: 1.1rem;
    }

    .metrics-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .metric-card {
        background: white;
        border-radius: 12px;
        padding: 25px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        border-left: 4px solid #667eea;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .metric-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    }

    .metric-value {
        font-size: 2.5rem;
        font-weight: 700;
        color: #667eea;
        margin-bottom: 5px;
    }

    .metric-label {
        color: #666;
        font-size: 1rem;
        font-weight: 500;
    }

    .analytics-section {
        background: white;
        border-radius: 12px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    .section-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .top-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .top-list li {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #eee;
        transition: background-color 0.3s ease;
    }

    .top-list li:hover {
        background-color: #f8f9ff;
        padding-left: 10px;
        margin-left: -10px;
        margin-right: -10px;
        padding-right: 10px;
    }

    .top-list li:last-child {
        border-bottom: none;
    }

    .list-item-name {
        font-weight: 500;
        color: #333;
    }

    .list-item-count {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 4px 12px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .recent-scans-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 15px;
    }

    .recent-scans-table th {
        background: #f8f9ff;
        color: #333;
        font-weight: 600;
        padding: 12px;
        text-align: left;
        border-bottom: 2px solid #667eea;
    }

    .recent-scans-table td {
        padding: 12px;
        border-bottom: 1px solid #eee;
        vertical-align: top;
    }

    .recent-scans-table tr:hover {
        background-color: #f8f9ff;
    }

    .code-cell {
        font-family: 'Courier New', monospace;
        background: #f5f5f5;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.9rem;
    }

    .ip-cell {
        font-family: 'Courier New', monospace;
        color: #666;
        font-size: 0.9rem;
    }

    .timestamp-cell {
        color: #666;
        font-size: 0.9rem;
    }

    .country-flag {
        margin-right: 8px;
    }

    .no-data {
        text-align: center;
        color: #666;
        font-style: italic;
        padding: 40px;
    }

    @media (max-width: 768px) {
        .metrics-grid {
            grid-template-columns: 1fr;
        }

        .dashboard-header h1 {
            font-size: 2rem;
        }

        .recent-scans-table {
            font-size: 0.8rem;
        }

        .recent-scans-table th,
        .recent-scans-table td {
            padding: 8px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="scan-dashboard">
    <!-- Dashboard Header -->
    <div class="dashboard-header">
        <h1>📊 QR Scan Analytics</h1>
        <p>Real-time analytics from QR scan logs with IPinfo geolocation data</p>
        <small style="opacity: 0.8;">Available at: /qr-admin-dashboard/ (Admin-only)</small>
    </div>

    <!-- Key Metrics -->
    <div class="metrics-grid">
        <div class="metric-card">
            <div class="metric-value">{{ total_scans }}</div>
            <div class="metric-label">Total Scans</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">{{ top_countries|length }}</div>
            <div class="metric-label">Countries</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">{{ top_orgs|length }}</div>
            <div class="metric-label">Organizations</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">{{ recent_scans|length }}</div>
            <div class="metric-label">Recent Activity</div>
        </div>
    </div>

    <!-- Top Countries Section -->
    <div class="analytics-section">
        <h3 class="section-title">🌍 Top Countries</h3>
        {% if top_countries %}
            <ul class="top-list">
                {% for item in top_countries %}
                    <li>
                        <span class="list-item-name">
                            <span class="country-flag">🌍</span>
                            {{ item.country|default:"Unknown" }}
                        </span>
                        <span class="list-item-count">{{ item.count }}</span>
                    </li>
                {% endfor %}
            </ul>
        {% else %}
            <div class="no-data">No country data available yet</div>
        {% endif %}
    </div>

    <!-- Top Organizations Section -->
    <div class="analytics-section">
        <h3 class="section-title">🏢 Top Organizations</h3>
        {% if top_orgs %}
            <ul class="top-list">
                {% for item in top_orgs %}
                    <li>
                        <span class="list-item-name">
                            <span class="country-flag">🏢</span>
                            {{ item.org|default:"Unknown"|truncatechars:50 }}
                        </span>
                        <span class="list-item-count">{{ item.count }}</span>
                    </li>
                {% endfor %}
            </ul>
        {% else %}
            <div class="no-data">No organization data available yet</div>
        {% endif %}
    </div>

    <!-- Recent Scans Section -->
    <div class="analytics-section">
        <h3 class="section-title">🕒 Recent Scans</h3>
        {% if recent_scans %}
            <table class="recent-scans-table">
                <thead>
                    <tr>
                        <th>Time</th>
                        <th>Code</th>
                        <th>IP</th>
                        <th>City</th>
                        <th>Country</th>
                        <th>Organization</th>
                    </tr>
                </thead>
                <tbody>
                    {% for scan in recent_scans %}
                        <tr>
                            <td class="timestamp-cell">{{ scan.timestamp|date:"M d, H:i" }}</td>
                            <td class="code-cell">{{ scan.code|truncatechars:15 }}</td>
                            <td class="ip-cell">{{ scan.ip_address }}</td>
                            <td>{{ scan.city|default:"Unknown" }}</td>
                            <td>{{ scan.country|default:"Unknown" }}</td>
                            <td>{{ scan.org|default:"Unknown"|truncatechars:30 }}</td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        {% else %}
            <div class="no-data">No recent scans available yet</div>
        {% endif %}
    </div>

    <!-- Admin Actions -->
    <div class="analytics-section">
        <h3 class="section-title">⚙️ Admin Actions</h3>
        <p>
            <a href="{% url 'admin:qrcode_app_qrscanlog_changelist' %}" class="button default">
                📋 View All Scan Logs
            </a>
            <a href="{% url 'admin:index' %}" class="button">
                🏠 Back to Admin
            </a>
        </p>
    </div>
</div>
{% endblock %}
