/* Enterprise-Grade Mobile Enhancements for Ads Platform
 * This file provides additional mobile optimizations specifically for the ads platform
 * to ensure a consistent, luxurious experience across all devices.
 */

/* Enhanced Mobile Styles for Ads Platform */
@media (max-width: 991.98px) {
    /* Enterprise Dashboard Mobile Enhancements */
    .enterprise-dashboard {
        padding: 0.75rem;
    }

    .dashboard-header {
        padding: 1.25rem;
        margin-bottom: 1.25rem;
        border-radius: 12px;
    }

    .dashboard-welcome {
        padding: 0.5rem 0;
    }

    .welcome-title {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .welcome-subtitle {
        font-size: 0.9rem;
    }

    .dashboard-actions {
        margin-top: 1rem;
        text-align: left;
    }

    .dashboard-actions .btn {
        margin-bottom: 0.5rem;
        width: 100%;
    }

    .dashboard-sidebar {
        position: relative;
        width: 100%;
        margin-bottom: 1.25rem;
    }

    .sidebar-container {
        position: relative;
        top: 0;
        height: auto;
        border-radius: 12px;
        margin-bottom: 1.25rem;
    }

    .sidebar-nav {
        display: flex;
        overflow-x: auto;
        padding-bottom: 0.5rem;
        -webkit-overflow-scrolling: touch;
    }

    .nav-list {
        display: flex;
        flex-wrap: nowrap;
        padding: 0.5rem;
    }

    .nav-item {
        flex: 0 0 auto;
        margin-right: 0.5rem;
        margin-bottom: 0;
        white-space: nowrap;
    }

    .nav-link {
        padding: 0.75rem 1.25rem;
        border-radius: 8px;
    }

    .dashboard-main {
        width: 100%;
    }

    .dashboard-card {
        margin-bottom: 1.25rem;
        border-radius: 12px;
    }

    .card-header {
        padding: 1rem;
        flex-direction: column;
        align-items: flex-start;
    }

    .card-title {
        margin-bottom: 0.75rem;
        font-size: 1.25rem;
    }

    .card-actions {
        width: 100%;
        margin-top: 0.5rem;
    }

    .card-body {
        padding: 1rem;
    }

    /* Stats Row Mobile Optimization */
    .stats-row {
        flex-direction: column;
        margin: 0;
    }

    .stat-card {
        width: 100%;
        margin: 0 0 1rem 0;
        padding: 1rem;
    }

    .stat-icon {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .stat-value {
        font-size: 1.75rem;
    }

    .stat-label {
        font-size: 0.85rem;
    }

    /* Chart Containers */
    .chart-container {
        height: auto;
        min-height: 250px;
        margin-bottom: 1.25rem;
    }

    /* Campaign Detail Mobile Optimization */
    .detail-group {
        margin-bottom: 1rem;
    }

    .detail-label {
        font-size: 0.85rem;
        margin-bottom: 0.25rem;
    }

    .detail-value {
        font-size: 1rem;
    }

    /* Ad Analytics Mobile Optimization */
    .analytics-container {
        padding: 1rem 0;
    }

    .analytics-header {
        padding: 1.25rem 1rem;
        margin-bottom: 1.25rem;
    }

    .analytics-title {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .analytics-subtitle {
        font-size: 0.9rem;
    }

    .ad-meta {
        flex-wrap: wrap;
        margin-top: 1rem;
    }

    .ad-meta-item {
        margin-right: 0.75rem;
        margin-bottom: 0.5rem;
        font-size: 0.85rem;
    }

    .insight-card {
        padding: 1rem;
        margin-bottom: 1.25rem;
    }

    .insight-title {
        font-size: 1.1rem;
        margin-bottom: 1rem;
    }

    /* Table Responsiveness */
    .data-table {
        font-size: 0.85rem;
    }

    .data-table th,
    .data-table td {
        padding: 0.75rem 0.5rem;
    }

    /* Form Improvements */
    .form-group {
        margin-bottom: 1.25rem;
    }

    .form-label {
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }

    .form-control,
    .form-select {
        font-size: 16px; /* Prevent iOS zoom */
        padding: 0.75rem 1rem;
        height: auto;
    }

    /* Action Buttons */
    .action-buttons {
        flex-direction: column;
        margin-top: 1.5rem;
    }

    .action-btn {
        width: 100%;
        margin-bottom: 0.75rem;
        padding: 0.75rem 1rem;
    }

    /* Modal Improvements */
    .modal-dialog {
        margin: 0.5rem;
    }

    .modal-content {
        border-radius: 12px;
    }

    .modal-header {
        padding: 1rem;
    }

    .modal-body {
        padding: 1rem;
    }

    .modal-footer {
        padding: 1rem;
    }
}

/* Small Mobile Devices */
@media (max-width: 575.98px) {
    .welcome-title {
        font-size: 1.25rem;
    }

    .card-title {
        font-size: 1.1rem;
    }

    .stat-value {
        font-size: 1.5rem;
    }

    .analytics-title {
        font-size: 1.25rem;
    }

    .insight-title {
        font-size: 1rem;
    }
}
