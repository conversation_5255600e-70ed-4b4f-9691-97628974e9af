{% extends "base.html" %}
{% load static %}

{% block title %}User Management - Enterprise QR{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'qrcode_app/css/user-management.css' %}">
{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="h3">
                <i class="fas fa-users-cog me-2"></i>User Management
            </h1>
        </div>
        <div class="col-md-4 text-md-end">
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                <i class="fas fa-user-plus me-2"></i>Add User
            </button>
        </div>
    </div>
    
    <div class="card shadow-sm">
        <div class="card-header bg-white">
            <div class="row g-2">
                <div class="col-md-4">
                    <select class="form-select" id="roleFilter">
                        <option value="">All Roles</option>
                        <option value="guest">Guest</option>
                        <option value="user">User</option>
                        <option value="premium">Premium User</option>
                        <option value="admin">Administrator</option>
                        <option value="superadmin">Super Administrator</option>
                    </select>
                </div>
                <div class="col-md-8">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" id="userSearch" placeholder="Search users...">
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>User</th>
                            <th>Email</th>
                            <th>Role</th>
                            <th>Company</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for profile in users %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    {% if profile.profile_image %}
                                    <img src="{{ profile.profile_image.url }}" alt="{{ profile.user.username }}" class="user-avatar me-2">
                                    {% else %}
                                    <div class="user-avatar-placeholder me-2">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    {% endif %}
                                    <div>
                                        <div class="fw-bold">{{ profile.user.get_full_name|default:profile.user.username }}</div>
                                        <div class="text-muted small">@{{ profile.user.username }}</div>
                                    </div>
                                </div>
                            </td>
                            <td>{{ profile.user.email }}</td>
                            <td>
                                <span class="badge {% if profile.role == 'superadmin' %}bg-danger{% elif profile.role == 'admin' %}bg-warning{% elif profile.role == 'premium' %}bg-success{% else %}bg-secondary{% endif %}">
                                    {{ profile.get_role_display }}
                                </span>
                            </td>
                            <td>{{ profile.company|default:"-" }}</td>
                            <td>{{ profile.created_at|date:"M d, Y" }}</td>
                            <td>
                                <div class="btn-group">
                                    <button class="btn btn-sm btn-outline-primary edit-user-btn" data-user-id="{{ profile.user.id }}">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger delete-user-btn" data-user-id="{{ profile.user.id }}">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center py-4">
                                <p class="text-muted mb-0">No users found.</p>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addUserModalLabel">Add New User</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addUserForm">
                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="firstName" class="form-label">First Name</label>
                        <input type="text" class="form-control" id="firstName">
                    </div>
                    <div class="mb-3">
                        <label for="lastName" class="form-label">Last Name</label>
                        <input type="text" class="form-control" id="lastName">
                    </div>
                    <div class="mb-3">
                        <label for="role" class="form-label">Role</label>
                        <select class="form-select" id="role" required>
                            <option value="guest">Guest</option>
                            <option value="user" selected>User</option>
                            <option value="premium">Premium User</option>
                            <option value="admin">Administrator</option>
                            <option value="superadmin">Super Administrator</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="company" class="form-label">Company</label>
                        <input type="text" class="form-control" id="company">
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" required>
                    </div>
                    <div class="mb-3">
                        <label for="confirmPassword" class="form-label">Confirm Password</label>
                        <input type="password" class="form-control" id="confirmPassword" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveUserBtn">Add User</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editUserModalLabel">Edit User</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editUserForm">
                    <input type="hidden" id="editUserId">
                    <div class="mb-3">
                        <label for="editEmail" class="form-label">Email</label>
                        <input type="email" class="form-control" id="editEmail" required>
                    </div>
                    <div class="mb-3">
                        <label for="editFirstName" class="form-label">First Name</label>
                        <input type="text" class="form-control" id="editFirstName">
                    </div>
                    <div class="mb-3">
                        <label for="editLastName" class="form-label">Last Name</label>
                        <input type="text" class="form-control" id="editLastName">
                    </div>
                    <div class="mb-3">
                        <label for="editRole" class="form-label">Role</label>
                        <select class="form-select" id="editRole" required>
                            <option value="guest">Guest</option>
                            <option value="user">User</option>
                            <option value="premium">Premium User</option>
                            <option value="admin">Administrator</option>
                            <option value="superadmin">Super Administrator</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="editCompany" class="form-label">Company</label>
                        <input type="text" class="form-control" id="editCompany">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="updateUserBtn">Save Changes</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete User Modal -->
<div class="modal fade" id="deleteUserModal" tabindex="-1" aria-labelledby="deleteUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteUserModalLabel">Confirm Deletion</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this user? This action cannot be undone.</p>
                <input type="hidden" id="deleteUserId">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'qrcode_app/js/user-management.js' %}"></script>
{% endblock %}
