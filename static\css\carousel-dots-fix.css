/*
 * Carousel Dots Fix
 * This CSS fixes the issue with oversized dots in the premium banner carousel
 */

/* Fix for premium carousel indicators */
.premium-ad-carousel-indicators {
    position: absolute;
    bottom: 15px !important; /* Force consistent positioning */
    left: 50% !important;
    transform: translateX(-50%) !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    gap: 6px !important; /* Consistent gap */
    z-index: 10;
    padding: 3px 8px !important; /* Smaller padding */
    background: rgba(255,255,255,0.35);
    border-radius: 30px;
    margin-bottom: 10px !important; /* Consistent margin */
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 1px solid rgba(255,255,255,0.5);
    height: auto !important; /* Prevent height issues */
    width: auto !important; /* Prevent width issues */
    max-width: 80% !important; /* Prevent overflow on small screens */
}

/* Fix for premium carousel indicator dots */
.premium-ad-indicator {
    width: 6px !important; /* Force consistent size */
    height: 6px !important; /* Force consistent size */
    min-width: 6px !important; /* Prevent expansion */
    min-height: 6px !important; /* Prevent expansion */
    max-width: 6px !important; /* Prevent expansion */
    max-height: 6px !important; /* Prevent expansion */
    border-radius: 50% !important;
    background: rgba(255,255,255,0.7) !important;
    border: none !important;
    cursor: pointer;
    padding: 0 !important;
    margin: 0 3px !important; /* Consistent margin */
    transition: all 0.3s ease !important; /* Smoother transition */
    box-shadow: 0 1px 3px rgba(0,0,0,0.15);
    display: inline-block !important;
    position: relative !important;
}

/* Active indicator - more subtle scaling */
.premium-ad-indicator.active {
    background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%) !important;
    transform: scale(1.2) !important; /* Reduced scale */
    box-shadow: 0 2px 5px rgba(26, 35, 126, 0.3);
}

/* Hover effect - more subtle */
.premium-ad-indicator:hover:not(.active) {
    background: rgba(255,255,255,0.9) !important;
    transform: scale(1.1) !important; /* Reduced scale */
}

/* Ensure consistent sizing on all screen sizes */
@media (max-width: 768px) {
    .premium-ad-carousel-indicators {
        bottom: 10px !important;
        padding: 2px 6px !important;
        gap: 4px !important;
        margin-bottom: 8px !important;
    }

    .premium-ad-indicator {
        width: 5px !important;
        height: 5px !important;
        min-width: 5px !important;
        min-height: 5px !important;
        max-width: 5px !important;
        max-height: 5px !important;
        margin: 0 2px !important;
    }
}

/* Fix for any JavaScript-induced size changes */
.premium-ad-carousel-indicators button,
.premium-ad-carousel-indicators .premium-ad-indicator {
    width: 6px !important;
    height: 6px !important;
    min-width: 6px !important;
    min-height: 6px !important;
    max-width: 6px !important;
    max-height: 6px !important;
}

/* Fix for mobile screens */
@media (max-width: 576px) {
    .premium-ad-carousel-indicators {
        bottom: 35px !important; /* Move indicators higher to avoid Learn More button */
        padding: 2px 5px !important;
        gap: 3px !important;
        background: rgba(255,255,255,0.6) !important; /* More opaque for better visibility */
        z-index: 20 !important; /* Ensure it's above other elements */
        right: 10px !important; /* Position to the right */
        left: auto !important; /* Override the left positioning */
        transform: none !important; /* Remove the transform */
        border-radius: 10px !important; /* Rounded corners */
        max-width: auto !important; /* Allow natural width */
    }

    .premium-ad-carousel-indicators button,
    .premium-ad-carousel-indicators .premium-ad-indicator {
        width: 4px !important;
        height: 4px !important;
        min-width: 4px !important;
        min-height: 4px !important;
        max-width: 4px !important;
        max-height: 4px !important;
        margin: 0 1px !important;
    }

    .premium-ad-indicator.active {
        transform: scale(1.1) !important; /* Even more subtle scale on mobile */
    }

    /* Ensure CTA button is not overlapped */
    .premium-ad-cta {
        margin-bottom: 5px !important; /* Reduce bottom margin on very small screens */
        position: relative !important; /* Ensure proper stacking */
        z-index: 15 !important; /* Lower than indicators but still high */
    }
}

/* Ensure indicators don't mask content */
.premium-ad-carousel-indicators {
    opacity: 0.8 !important; /* Slightly transparent */
    pointer-events: auto !important; /* Ensure clickability */
}

/* Fix for any transition issues */
.premium-ad-carousel-indicators,
.premium-ad-indicator {
    will-change: transform, opacity !important; /* Optimize animations */
    backface-visibility: hidden !important; /* Prevent flickering */
    -webkit-font-smoothing: antialiased !important; /* Smooth rendering */
}

/* Fix for any z-index issues */
.premium-ad-carousel {
    position: relative !important;
    z-index: 1 !important; /* Ensure proper stacking */
}

/* Fix for any overflow issues */
.premium-ad-carousel-inner {
    overflow: hidden !important;
    position: relative !important;
}

/* Ensure indicators are always visible but not intrusive */
.premium-ad-carousel:hover .premium-ad-carousel-indicators {
    opacity: 1 !important;
}
