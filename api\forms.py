from django import forms
from qrcode_app.models import APIKey

class APIKeyForm(forms.ModelForm):
    """Form for creating and updating API keys"""
    class Meta:
        model = APIKey
        fields = ['name', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'API Key Name'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
        labels = {
            'name': 'API Key Name',
            'is_active': 'Active',
        }
        help_texts = {
            'name': 'A descriptive name for this API key',
            'is_active': 'Uncheck to temporarily disable this API key',
        }
