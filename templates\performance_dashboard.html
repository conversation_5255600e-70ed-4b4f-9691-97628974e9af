{% extends "base.html" %}
{% load static %}

{% block title %}Performance Dashboard - Enterprise QR{% endblock %}

{% block extra_css %}
<!-- Google Fonts - Poppins and Montserrat -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

<style>
    /* Ultra-Premium Enterprise Performance Dashboard Styling */
    body {
        background: linear-gradient(135deg, #000000 0%, #0a0a0a 10%, #1a1a2e 25%, #16213e 40%, #0f3460 60%, #533483 80%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        position: relative;
        overflow-x: hidden;
    }

    /* Premium animated background with enterprise patterns */
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 25% 75%, rgba(102, 126, 234, 0.7) 0%, transparent 40%),
            radial-gradient(circle at 75% 25%, rgba(255, 255, 255, 0.3) 0%, transparent 35%),
            radial-gradient(circle at 45% 65%, rgba(118, 75, 162, 0.6) 0%, transparent 45%),
            radial-gradient(circle at 65% 35%, rgba(83, 52, 131, 0.5) 0%, transparent 30%),
            radial-gradient(circle at 55% 45%, rgba(102, 126, 234, 0.4) 0%, transparent 40%);
        z-index: -1;
        animation: enterprisePerformanceFloat 60s ease-in-out infinite;
    }

    @keyframes enterprisePerformanceFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        25% { transform: translateY(-35px) rotate(2.5deg); }
        50% { transform: translateY(-25px) rotate(-2.5deg); }
        75% { transform: translateY(-45px) rotate(1.2deg); }
    }

    /* Enterprise Container */
    .enterprise-container {
        position: relative;
        z-index: 1;
        padding: 2rem 0;
        min-height: 100vh;
    }

    /* Premium Header Section */
    .enterprise-header {
        background: linear-gradient(135deg, rgba(26, 35, 126, 0.95) 0%, rgba(40, 53, 147, 0.95) 50%, rgba(63, 81, 181, 0.95) 100%);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2.5rem;
        margin-bottom: 2rem;
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        position: relative;
        overflow: hidden;
        animation: slideInDown 0.8s ease-out;
    }

    .enterprise-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
        animation: shimmer 3s ease-in-out infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    .enterprise-title {
        font-family: 'Montserrat', sans-serif !important;
        font-size: 2.5rem;
        font-weight: 700;
        color: #ffffff;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 2;
    }

    .enterprise-subtitle {
        font-size: 1.1rem;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 0;
        font-weight: 400;
        position: relative;
        z-index: 2;
    }

    .enterprise-icon {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        width: 60px;
        height: 60px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.8rem;
        color: white;
        margin-right: 1.5rem;
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        position: relative;
        z-index: 2;
    }

    /* Premium Action Controls */
    .enterprise-controls {
        display: flex;
        gap: 1rem;
        align-items: center;
        position: relative;
        z-index: 2;
    }

    .enterprise-refresh-control {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        background: rgba(255, 255, 255, 0.1);
        padding: 0.75rem 1rem;
        border-radius: 12px;
        backdrop-filter: blur(10px);
    }

    .enterprise-refresh-control label {
        color: rgba(255, 255, 255, 0.9);
        font-weight: 500;
        font-size: 0.9rem;
    }

    .enterprise-refresh-control select {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        border-radius: 8px;
        padding: 0.5rem;
        font-size: 0.9rem;
    }

    .enterprise-action-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 12px;
        padding: 0.8rem 2rem;
        color: white;
        font-weight: 600;
        font-size: 1rem;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        position: relative;
        z-index: 2;
        overflow: hidden;
        cursor: pointer;
    }

    .enterprise-action-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .enterprise-action-btn:hover::before {
        left: 100%;
    }

    .enterprise-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    /* Premium Metric Cards */
    .enterprise-metrics-container {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        overflow: hidden;
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.15),
            0 0 0 1px rgba(255, 255, 255, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        animation: slideInUp 0.8s ease-out 0.2s both;
        margin-bottom: 2rem;
    }

    .enterprise-metrics-header {
        background: linear-gradient(135deg, #1a237e 0%, #283593 100%);
        color: white;
        padding: 1.5rem 2rem;
        font-weight: 600;
        font-size: 1.2rem;
        position: relative;
    }

    .enterprise-metrics-header::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    }

    .enterprise-metrics-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        padding: 2rem;
    }

    .enterprise-metric-card {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
        border-radius: 16px;
        padding: 1.5rem;
        transition: all 0.3s ease;
        border: 2px solid transparent;
        position: relative;
        overflow: hidden;
    }

    .enterprise-metric-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
        transition: left 0.5s;
    }

    .enterprise-metric-card:hover::before {
        left: 100%;
    }

    .enterprise-metric-card:hover {
        transform: translateY(-5px);
        border-color: rgba(102, 126, 234, 0.3);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    }

    .enterprise-metric-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        margin-bottom: 1rem;
        position: relative;
        z-index: 2;
    }

    .enterprise-metric-icon.success {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);
    }

    .enterprise-metric-icon.info {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
    }

    .enterprise-metric-icon.warning {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        box-shadow: 0 8px 20px rgba(245, 158, 11, 0.3);
    }

    .enterprise-metric-icon.primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
    }

    .enterprise-metric-content h3 {
        font-size: 1.1rem;
        font-weight: 600;
        color: #1a237e;
        margin-bottom: 0.5rem;
    }

    .enterprise-metric-value {
        font-size: 1.8rem;
        font-weight: 700;
        color: #667eea;
        margin-bottom: 0.75rem;
    }

    .enterprise-progress {
        height: 6px;
        background: rgba(102, 126, 234, 0.1);
        border-radius: 3px;
        overflow: hidden;
        position: relative;
    }

    .enterprise-progress-bar {
        height: 100%;
        border-radius: 3px;
        transition: width 0.6s ease;
        position: relative;
        overflow: hidden;
    }

    .enterprise-progress-bar.success {
        background: linear-gradient(90deg, #10b981 0%, #059669 100%);
    }

    .enterprise-progress-bar.info {
        background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
    }

    .enterprise-progress-bar.warning {
        background: linear-gradient(90deg, #f59e0b 0%, #d97706 100%);
    }

    .enterprise-progress-bar.primary {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    }

    .enterprise-progress-bar::after {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        animation: progressShimmer 2s ease-in-out infinite;
    }

    @keyframes progressShimmer {
        0% { left: -100%; }
        100% { left: 100%; }
    }

    /* Premium Chart Containers */
    .enterprise-chart-container {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        overflow: hidden;
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.15),
            0 0 0 1px rgba(255, 255, 255, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        animation: slideInUp 0.8s ease-out 0.4s both;
        margin-bottom: 2rem;
        height: 100%;
    }

    .enterprise-chart-header {
        background: linear-gradient(135deg, #1a237e 0%, #283593 100%);
        color: white;
        padding: 1.5rem 2rem;
        font-weight: 600;
        font-size: 1.1rem;
        position: relative;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .enterprise-chart-header::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    }

    .enterprise-chart-controls {
        display: flex;
        gap: 0.5rem;
    }

    .enterprise-chart-btn {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: rgba(255, 255, 255, 0.8);
        padding: 0.5rem 1rem;
        border-radius: 8px;
        font-size: 0.85rem;
        font-weight: 500;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .enterprise-chart-btn.active,
    .enterprise-chart-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        color: white;
        border-color: rgba(255, 255, 255, 0.5);
    }

    .enterprise-chart-body {
        padding: 2rem;
        height: 400px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Premium Tool Cards */
    .enterprise-tools-container {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        overflow: hidden;
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.15),
            0 0 0 1px rgba(255, 255, 255, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        animation: slideInUp 0.8s ease-out 0.6s both;
        margin-bottom: 2rem;
    }

    .enterprise-tools-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
        padding: 2rem;
    }

    .enterprise-tool-card {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
        border-radius: 16px;
        padding: 1.5rem;
        transition: all 0.3s ease;
        border: 2px solid transparent;
        position: relative;
        overflow: hidden;
    }

    .enterprise-tool-card:hover {
        transform: translateY(-5px);
        border-color: rgba(102, 126, 234, 0.3);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    }

    .enterprise-tool-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        margin-bottom: 1rem;
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
    }

    .enterprise-tool-content h3 {
        font-size: 1.1rem;
        font-weight: 600;
        color: #1a237e;
        margin-bottom: 0.5rem;
    }

    .enterprise-tool-content p {
        color: #667eea;
        font-weight: 500;
        margin-bottom: 1rem;
        font-size: 0.95rem;
    }

    .enterprise-tool-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 8px;
        padding: 0.6rem 1.2rem;
        color: white;
        font-weight: 600;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .enterprise-tool-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .enterprise-title {
            font-size: 2rem;
        }

        .enterprise-header {
            padding: 2rem 1.5rem;
        }

        .enterprise-controls {
            flex-direction: column;
            gap: 0.75rem;
        }

        .enterprise-metrics-grid,
        .enterprise-tools-grid {
            grid-template-columns: 1fr;
            padding: 1.5rem;
        }

        .enterprise-chart-body {
            height: 300px;
            padding: 1.5rem;
        }
    }

    @media (max-width: 576px) {
        .enterprise-title {
            font-size: 1.8rem;
        }

        .enterprise-header {
            padding: 1.5rem 1rem;
        }

        .enterprise-metrics-grid,
        .enterprise-tools-grid {
            padding: 1rem;
        }

        .enterprise-chart-header {
            padding: 1rem 1.5rem;
            font-size: 1rem;
        }

        .enterprise-chart-body {
            height: 250px;
            padding: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="enterprise-container">
    <div class="container-fluid">
        <!-- Premium Header Section -->
        <div class="enterprise-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <div class="d-flex align-items-center">
                        <div class="enterprise-icon">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <div>
                            <h1 class="enterprise-title mb-0">Performance Dashboard</h1>
                            <p class="enterprise-subtitle">Monitor and optimize your QR code generation performance with real-time analytics</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-md-end">
                    <div class="enterprise-controls">
                        <div class="enterprise-refresh-control">
                            <label for="refreshInterval">Auto-refresh:</label>
                            <select id="refreshInterval">
                                <option value="0">Off</option>
                                <option value="30">30 seconds</option>
                                <option value="60">1 minute</option>
                                <option value="300">5 minutes</option>
                            </select>
                        </div>
                        <button id="runOptimizationBtn" class="enterprise-action-btn">
                            <i class="fas fa-bolt"></i>Run Optimization
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Premium System Health Metrics -->
        <div class="enterprise-metrics-container">
            <div class="enterprise-metrics-header">
                <i class="fas fa-heartbeat me-2"></i>System Health Metrics
            </div>
            <div class="enterprise-metrics-grid">
                <div class="enterprise-metric-card">
                    <div class="enterprise-metric-icon success">
                        <i class="fas fa-server"></i>
                    </div>
                    <div class="enterprise-metric-content">
                        <h3>Server Status</h3>
                        <div class="enterprise-metric-value">{{ system_metrics.server_status|default:"Online" }}</div>
                        <div class="enterprise-progress">
                            <div class="enterprise-progress-bar success" style="width: 100%;"></div>
                        </div>
                    </div>
                </div>
                <div class="enterprise-metric-card">
                    <div class="enterprise-metric-icon info">
                        <i class="fas fa-memory"></i>
                    </div>
                    <div class="enterprise-metric-content">
                        <h3>Memory Usage</h3>
                        <div class="enterprise-metric-value">{{ system_metrics.memory_usage|default:"45" }}%</div>
                        <div class="enterprise-progress">
                            <div class="enterprise-progress-bar info" style="width: {{ system_metrics.memory_usage|default:"45" }}%;"></div>
                        </div>
                    </div>
                </div>
                <div class="enterprise-metric-card">
                    <div class="enterprise-metric-icon warning">
                        <i class="fas fa-microchip"></i>
                    </div>
                    <div class="enterprise-metric-content">
                        <h3>CPU Load</h3>
                        <div class="enterprise-metric-value">{{ system_metrics.cpu_load|default:"32" }}%</div>
                        <div class="enterprise-progress">
                            <div class="enterprise-progress-bar warning" style="width: {{ system_metrics.cpu_load|default:"32" }}%;"></div>
                        </div>
                    </div>
                </div>
                <div class="enterprise-metric-card">
                    <div class="enterprise-metric-icon primary">
                        <i class="fas fa-database"></i>
                    </div>
                    <div class="enterprise-metric-content">
                        <h3>Database</h3>
                        <div class="enterprise-metric-value">{{ system_metrics.database_status|default:"Optimal" }}</div>
                        <div class="enterprise-progress">
                            <div class="enterprise-progress-bar primary" style="width: 90%;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    <!-- Performance Charts -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">QR Code Generation Performance</h5>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-secondary active" data-period="day">Day</button>
                            <button class="btn btn-outline-secondary" data-period="week">Week</button>
                            <button class="btn btn-outline-secondary" data-period="month">Month</button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <canvas id="performanceChart" height="300"></canvas>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">Resource Allocation</h5>
                </div>
                <div class="card-body">
                    <canvas id="resourceChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Optimization Tools -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">Optimization Tools</h5>
                </div>
                <div class="card-body">
                    <div class="tools-grid">
                        <div class="tool-card">
                            <div class="tool-icon">
                                <i class="fas fa-database"></i>
                            </div>
                            <div class="tool-content">
                                <h3>Database Optimization</h3>
                                <p>Optimize database queries and indexes for faster QR code retrieval.</p>
                                <button class="btn btn-sm btn-outline-primary tool-btn" data-tool="database">
                                    <i class="fas fa-play me-1"></i> Run
                                </button>
                            </div>
                        </div>
                        <div class="tool-card">
                            <div class="tool-icon">
                                <i class="fas fa-hdd"></i>
                            </div>
                            <div class="tool-content">
                                <h3>Cache Management</h3>
                                <p>Manage QR code caching for improved generation speed.</p>
                                <button class="btn btn-sm btn-outline-primary tool-btn" data-tool="cache">
                                    <i class="fas fa-play me-1"></i> Run
                                </button>
                            </div>
                        </div>
                        <div class="tool-card">
                            <div class="tool-icon">
                                <i class="fas fa-tasks"></i>
                            </div>
                            <div class="tool-content">
                                <h3>Task Queue Optimization</h3>
                                <p>Optimize background task processing for batch operations.</p>
                                <button class="btn btn-sm btn-outline-primary tool-btn" data-tool="queue">
                                    <i class="fas fa-play me-1"></i> Run
                                </button>
                            </div>
                        </div>
                        <div class="tool-card">
                            <div class="tool-icon">
                                <i class="fas fa-image"></i>
                            </div>
                            <div class="tool-content">
                                <h3>Image Processing</h3>
                                <p>Optimize QR code image generation and processing.</p>
                                <button class="btn btn-sm btn-outline-primary tool-btn" data-tool="image">
                                    <i class="fas fa-play me-1"></i> Run
                                </button>
                            </div>
                        </div>
                        <div class="tool-card">
                            <div class="tool-icon">
                                <i class="fas fa-file-archive"></i>
                            </div>
                            <div class="tool-content">
                                <h3>Storage Cleanup</h3>
                                <p>Clean up unused QR codes and optimize storage usage.</p>
                                <button class="btn btn-sm btn-outline-primary tool-btn" data-tool="storage">
                                    <i class="fas fa-play me-1"></i> Run
                                </button>
                            </div>
                        </div>
                        <div class="tool-card">
                            <div class="tool-icon">
                                <i class="fas fa-tachometer-alt"></i>
                            </div>
                            <div class="tool-content">
                                <h3>Performance Test</h3>
                                <p>Run a performance test to identify bottlenecks.</p>
                                <button class="btn btn-sm btn-outline-primary tool-btn" data-tool="test">
                                    <i class="fas fa-play me-1"></i> Run
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">Recent QR Codes</h5>
                        <a href="{% url 'qr_code_list' %}" class="btn btn-sm btn-outline-primary">View All</a>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Name</th>
                                    <th>Type</th>
                                    <th>Created</th>
                                    <th>Size</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for qr_code in recent_qr_codes %}
                                <tr>
                                    <td>
                                        <a href="{% url 'qr_code_detail' qr_code.pk %}" class="text-decoration-none">
                                            {{ qr_code.name }}
                                        </a>
                                    </td>
                                    <td>{{ qr_code.qr_type }}</td>
                                    <td>{{ qr_code.created_at|date:"M d, Y" }}</td>
                                    <td>{% if qr_code.image %}{{ qr_code.image.size|filesizeformat }}{% else %}N/A{% endif %}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center py-3">No QR codes generated yet.</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">Optimization History</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Optimization</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                    <th>Improvement</th>
                                </tr>
                            </thead>
                            <tbody id="optimizationHistory">
                                {% for optimization in optimization_history %}
                                <tr>
                                    <td>{{ optimization.name }}</td>
                                    <td>{{ optimization.date|date:"M d, Y" }}</td>
                                    <td><span class="badge bg-success">{{ optimization.status }}</span></td>
                                    <td>{{ optimization.improvement }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center py-3">No optimization history available.</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Optimization Modal -->
<div class="modal fade" id="optimizationModal" tabindex="-1" aria-labelledby="optimizationModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="optimizationModalLabel">Running Optimization</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-3 mb-0" id="optimizationMessage">Optimizing system performance...</p>
                </div>
                <div class="progress">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="{% static 'js/performance-dashboard.js' %}"></script>
{% endblock %}
