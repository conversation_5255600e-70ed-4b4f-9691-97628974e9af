/* User Management Styles */
:root {
    --sidebar-width: 250px;
    --header-height: 70px;
    --primary-color: #3b82f6;
    --primary-dark: #1d4ed8;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #6366f1;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
}

/* Admin Layout */
.admin-container {
    display: flex;
    min-height: calc(100vh - var(--header-height));
    background-color: var(--gray-50);
}

/* Sidebar Styles */
.admin-sidebar {
    width: var(--sidebar-width);
    background-color: white;
    border-right: 1px solid var(--gray-200);
    display: flex;
    flex-direction: column;
    height: calc(100vh - var(--header-height));
    position: sticky;
    top: var(--header-height);
    overflow-y: auto;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--gray-200);
}

.sidebar-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
}

.sidebar-menu {
    list-style: none;
    padding: 1rem 0;
    margin: 0;
}

.sidebar-item {
    margin-bottom: 0.25rem;
}

.sidebar-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: var(--gray-600);
    text-decoration: none;
    transition: all 0.2s ease;
    font-size: 0.9375rem;
}

.sidebar-link i {
    margin-right: 0.75rem;
    font-size: 1.125rem;
    width: 1.25rem;
    text-align: center;
}

.sidebar-link:hover {
    background-color: var(--gray-100);
    color: var(--primary-color);
}

.sidebar-item.active .sidebar-link {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--primary-color);
    font-weight: 500;
    border-left: 3px solid var(--primary-color);
}

/* Main Content Styles */
.admin-content {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

.admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.admin-header h1 {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--gray-800);
    margin: 0;
}

.admin-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.search-box {
    position: relative;
}

.search-box i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-500);
}

.search-box input {
    padding: 0.5rem 1rem 0.5rem 2.5rem;
    border: 1px solid var(--gray-300);
    border-radius: 0.375rem;
    font-size: 0.875rem;
    width: 250px;
    transition: all 0.2s ease;
}

.search-box input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    outline: none;
}

.primary-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    border: none;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.primary-btn:hover {
    box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.3);
    transform: translateY(-1px);
}

.secondary-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background-color: white;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.secondary-btn:hover {
    background-color: rgba(59, 130, 246, 0.05);
    box-shadow: 0 2px 4px -1px rgba(59, 130, 246, 0.2);
}

/* Role Selector */
.role-selector {
    background-color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.role-selector h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0 0 0.5rem 0;
}

.role-selector p {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin: 0 0 1rem 0;
}

.role-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.role-btn {
    padding: 0.5rem 1rem;
    background-color: var(--gray-100);
    border: 1px solid var(--gray-300);
    border-radius: 0.375rem;
    font-size: 0.875rem;
    color: var(--gray-700);
    cursor: pointer;
    transition: all 0.2s ease;
}

.role-btn:hover {
    background-color: var(--gray-200);
}

.role-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Users Table */
.users-table-container {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.users-table {
    width: 100%;
    border-collapse: collapse;
}

.users-table th,
.users-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--gray-200);
}

.users-table th {
    background-color: var(--gray-50);
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--gray-500);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.th-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.th-content i {
    font-size: 0.75rem;
    color: var(--gray-400);
    cursor: pointer;
}

.th-content i:hover {
    color: var(--gray-600);
}

.users-table td {
    font-size: 0.875rem;
    color: var(--gray-700);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-600);
    font-size: 1rem;
}

.user-details {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-weight: 500;
    color: var(--gray-800);
}

.user-title {
    font-size: 0.75rem;
    color: var(--gray-500);
}

.user-role {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.role-admin {
    background-color: rgba(99, 102, 241, 0.1);
    color: var(--info-color);
}

.role-manager {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--primary-color);
}

.role-creator {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.role-viewer {
    background-color: rgba(107, 114, 128, 0.1);
    color: var(--gray-600);
}

.user-status {
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.status-active .status-dot {
    background-color: var(--success-color);
}

.status-inactive .status-dot {
    background-color: var(--gray-400);
}

.status-pending .status-dot {
    background-color: var(--warning-color);
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    width: 28px;
    height: 28px;
    border-radius: 6px;
    background-color: var(--gray-100);
    border: none;
    color: var(--gray-600);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background-color: var(--gray-200);
    color: var(--gray-800);
}

.action-btn.edit-btn:hover {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--primary-color);
}

.action-btn.delete-btn:hover {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.25rem;
}

.pagination-btn {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.375rem;
    background-color: white;
    border: 1px solid var(--gray-300);
    color: var(--gray-700);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
    background-color: var(--gray-100);
    border-color: var(--gray-400);
}

.pagination-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-ellipsis {
    color: var(--gray-500);
    font-size: 0.875rem;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.95);
    width: 500px;
    max-width: 90%;
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    z-index: 1001;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal.active {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
    visibility: visible;
}

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--gray-200);
}

.modal-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    color: var(--gray-500);
    font-size: 1.25rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.modal-close:hover {
    color: var(--gray-700);
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding: 1.5rem;
    border-top: 1px solid var(--gray-200);
}

.form-group {
    margin-bottom: 1.25rem;
}

.form-group label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--gray-300);
    border-radius: 0.375rem;
    font-size: 0.875rem;
    color: var(--gray-800);
    transition: all 0.2s ease;
}

.form-group input:focus,
.form-group select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    outline: none;
}

/* User Dropdown Menu */
.user-dropdown {
    position: relative;
}

.user-menu-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.5rem;
    background: none;
    border: none;
    cursor: pointer;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
}

.user-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--gray-800);
}

.user-dropdown-menu {
    position: absolute;
    top: calc(100% + 0.5rem);
    right: 0;
    width: 220px;
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    padding: 0.5rem 0;
    z-index: 100;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
}

.user-dropdown.active .user-dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: var(--gray-700);
    text-decoration: none;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.dropdown-item i {
    font-size: 1rem;
    width: 1.25rem;
    text-align: center;
}

.dropdown-item:hover {
    background-color: var(--gray-100);
    color: var(--gray-900);
}

.dropdown-item.active {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--primary-color);
    font-weight: 500;
}

.dropdown-divider {
    height: 1px;
    background-color: var(--gray-200);
    margin: 0.5rem 0;
}

.logout-item {
    color: var(--danger-color);
}

.logout-item:hover {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

/* Hidden elements based on permissions */
.hidden {
    display: none !important;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .admin-sidebar {
        width: 64px;
    }
    
    .sidebar-link span,
    .sidebar-header h3 {
        display: none;
    }
    
    .sidebar-link i {
        margin-right: 0;
    }
    
    .sidebar-header,
    .sidebar-link {
        display: flex;
        justify-content: center;
        padding: 1rem;
    }
    
    .sidebar-item.active .sidebar-link {
        border-left: none;
        border-left: 3px solid var(--primary-color);
    }
}

@media (max-width: 768px) {
    .admin-container {
        flex-direction: column;
    }
    
    .admin-sidebar {
        width: 100%;
        height: auto;
        position: static;
    }
    
    .sidebar-menu {
        display: flex;
        overflow-x: auto;
        padding: 0.5rem;
    }
    
    .sidebar-item {
        margin-bottom: 0;
        margin-right: 0.5rem;
    }
    
    .sidebar-link {
        padding: 0.5rem 1rem;
        border-radius: 0.375rem;
    }
    
    .sidebar-header {
        display: none;
    }
    
    .admin-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .admin-actions {
        width: 100%;
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-box {
        width: 100%;
    }
    
    .search-box input {
        width: 100%;
    }
    
    .role-buttons {
        flex-direction: column;
        align-items: stretch;
    }
    
    .users-table-container {
        overflow-x: auto;
    }
    
    .users-table {
        min-width: 800px;
    }
}
