"""
URLs for QR Generator Pro monetization features
"""
from django.urls import path
from . import monetization_views

urlpatterns = [
    # Main monetization dashboard
    path('', monetization_views.monetization_dashboard, name='monetization_dashboard'),

    # Dynamic QR Redirects (Premium feature)
    path('dynamic-redirects/', monetization_views.dynamic_redirect_dashboard, name='dynamic_redirect_dashboard'),
    path('dynamic-redirects/create/<int:qr_code_id>/', monetization_views.create_dynamic_redirect, name='create_dynamic_redirect'),
    path('dynamic-redirects/update/<int:redirect_id>/', monetization_views.update_dynamic_redirect, name='update_dynamic_redirect'),

    # MODULE 2: Simple QR Redirect Editing
    path('edit-redirect/<int:qr_code_id>/', monetization_views.edit_qr_redirect, name='edit_qr_redirect'),

    # MODULE 3: AI Landing Pages
    path('create-ai-page/<int:qr_code_id>/', monetization_views.create_ai_landing_page, name='create_ai_landing_page'),

    # MODULE 5: Webhook Integration
    path('webhooks/', monetization_views.webhook_dashboard, name='webhook_dashboard'),
    path('webhooks/create/', monetization_views.create_webhook, name='create_webhook'),
    path('webhooks/edit/<int:webhook_id>/', monetization_views.edit_webhook, name='edit_webhook'),
    path('webhooks/delete/<int:webhook_id>/', monetization_views.delete_webhook, name='delete_webhook'),
    path('webhooks/test/<int:webhook_id>/', monetization_views.test_webhook, name='test_webhook'),

    # Upgrade to premium
    path('upgrade/', monetization_views.upgrade_to_premium, name='upgrade_to_premium'),
]
