/* Enterprise QR Code Generator - Mobile Responsive Styles */

/* Base Mobile Styles */
@media (max-width: 767.98px) {
    body {
        font-size: 0.95rem;
    }

    .container {
        padding-left: 1.25rem;
        padding-right: 1.25rem;
    }

    h1 {
        font-size: 2rem;
    }

    h2 {
        font-size: 1.75rem;
    }

    h3 {
        font-size: 1.5rem;
    }

    .card {
        margin-bottom: 1.5rem;
    }

    .btn:not(.qr-action-btn) {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .d-md-flex .btn {
        width: auto;
    }

    /* QR Code specific mobile styles */
    .qr-card-actions {
        display: flex;
        justify-content: space-between;
    }

    .qr-action-btn {
        padding: 0.5rem;
        flex: 1;
        margin: 0 2px;
        font-size: 0.9rem;
    }

    .qr-card-image-container {
        max-width: 200px;
        margin-left: auto;
        margin-right: auto;
    }

    .qr-list-title {
        font-size: 1.5rem;
    }

    .qr-page-info {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }

    /* QR Code detail page mobile styles */
    .customization-panel .nav-tabs .nav-link {
        padding: 0.5rem 0.75rem;
        font-size: 0.85rem;
    }

    .qr-code-display {
        max-width: 250px;
    }
}

/* Bottom Navigation styles moved to enterprise-bottom-nav.css */

/* Floating Action Button */
.floating-action-btn {
    display: none;
}

@media (max-width: 767.98px) {
    .floating-action-btn {
        display: flex;
        position: fixed;
        bottom: 70px;
        right: 20px;
        width: 56px;
        height: 56px;
        border-radius: 50%;
        background-color: var(--primary-color);
        color: white;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        z-index: 1000;
        border: none;
        font-size: 1.5rem;
    }

    .floating-action-btn:active {
        transform: scale(0.95);
    }
}

/* Mobile Menu styles moved to enterprise-bottom-nav.css */

/* Mobile Swipe Gestures */
.swipe-container {
    touch-action: pan-y;
}

/* Pull to Refresh */
.pull-indicator {
    position: absolute;
    top: -50px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 1.5rem;
    color: var(--primary-color);
    transition: transform 0.3s;
}

.pull-to-refresh {
    position: relative;
    overflow-y: auto;
}
