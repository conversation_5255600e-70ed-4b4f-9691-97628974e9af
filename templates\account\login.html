{% extends "base.html" %}
{% load i18n %}
{% load account socialaccount %}
{% load static %}

{% block title %}{% trans "Enterprise Login" %} - Enterprise QR{% endblock %}

{% block extra_css %}
<style>
    /* Enterprise Authentication Styles */
    .auth-container {
        min-height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        position: relative;
        overflow: hidden;
        display: flex;
        align-items: center;
        padding: 40px 0;
    }

    .auth-background {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 1;
    }

    .auth-grid-pattern {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image:
            linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
            linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
        background-size: 40px 40px;
        animation: authGridMove 25s linear infinite;
    }

    @keyframes authGridMove {
        0% { transform: translate(0, 0); }
        100% { transform: translate(40px, 40px); }
    }

    .auth-floating-elements {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
    }

    .floating-qr {
        position: absolute;
        width: 60px;
        height: 60px;
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        justify-content: center;
        color: rgba(255, 255, 255, 0.7);
        font-size: 24px;
    }

    .floating-qr-1 {
        top: 15%;
        left: 10%;
        animation: floatQR1 8s ease-in-out infinite;
    }

    .floating-qr-2 {
        top: 70%;
        right: 15%;
        animation: floatQR2 10s ease-in-out infinite;
    }

    .floating-qr-3 {
        bottom: 20%;
        left: 20%;
        animation: floatQR3 12s ease-in-out infinite;
    }

    @keyframes floatQR1 {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(5deg); }
    }

    @keyframes floatQR2 {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-30px) rotate(-5deg); }
    }

    @keyframes floatQR3 {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-25px) rotate(3deg); }
    }

    .auth-card {
        position: relative;
        z-index: 2;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 20px;
        box-shadow:
            0 20px 60px rgba(0, 0, 0, 0.1),
            inset 0 2px 0 rgba(255, 255, 255, 0.5),
            inset 0 -2px 0 rgba(0, 0, 0, 0.05);
        overflow: hidden;
        max-width: 450px;
        width: 100%;
        margin: 0 auto;
    }

    .auth-header {
        text-align: center;
        padding: 40px 40px 20px;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        border-bottom: 1px solid rgba(102, 126, 234, 0.1);
    }

    .auth-logo {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        transform: rotate(45deg);
    }

    .auth-logo i {
        font-size: 32px;
        color: white;
        transform: rotate(-45deg);
    }

    .auth-title {
        font-size: 2rem;
        font-weight: 800;
        color: #2c3e50;
        margin-bottom: 10px;
    }

    .auth-subtitle {
        color: #6c757d;
        font-size: 1rem;
        margin-bottom: 0;
    }

    .auth-body {
        padding: 40px;
    }

    .enterprise-badge {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 165, 0, 0.1) 100%);
        border: 2px solid rgba(255, 215, 0, 0.3);
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 700;
        color: #f39c12;
        margin-bottom: 30px;
        box-shadow: 0 4px 15px rgba(255, 215, 0, 0.2);
    }

    .form-group {
        margin-bottom: 25px;
    }

    .form-label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 8px;
        display: block;
    }

    .form-control {
        border: 2px solid rgba(102, 126, 234, 0.1);
        border-radius: 12px;
        padding: 15px 20px;
        font-size: 16px;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
    }

    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        background: rgba(255, 255, 255, 0.95);
    }

    .password-input-container {
        position: relative;
    }

    .password-toggle {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #6c757d;
        cursor: pointer;
        padding: 5px;
        transition: color 0.3s ease;
    }

    .password-toggle:hover {
        color: #667eea;
    }

    .remember-me {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 30px;
    }

    .remember-me input[type="checkbox"] {
        width: 18px;
        height: 18px;
        accent-color: #667eea;
    }

    .btn-enterprise {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        padding: 15px 30px;
        border-radius: 12px;
        font-weight: 700;
        font-size: 16px;
        width: 100%;
        transition: all 0.3s ease;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        position: relative;
        overflow: hidden;
    }

    .btn-enterprise:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .btn-enterprise::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
    }

    .btn-enterprise:hover::before {
        left: 100%;
    }

    .auth-links {
        text-align: center;
        margin-top: 25px;
    }

    .auth-links a {
        color: #667eea;
        text-decoration: none;
        font-weight: 600;
        transition: color 0.3s ease;
    }

    .auth-links a:hover {
        color: #764ba2;
    }

    .divider {
        display: flex;
        align-items: center;
        margin: 30px 0;
        color: #6c757d;
        font-size: 14px;
    }

    .divider::before,
    .divider::after {
        content: '';
        flex: 1;
        height: 1px;
        background: rgba(102, 126, 234, 0.2);
    }

    .divider span {
        padding: 0 20px;
        background: rgba(255, 255, 255, 0.95);
    }

    .sso-buttons {
        display: flex;
        flex-direction: column;
        gap: 12px;
        margin-bottom: 25px;
    }

    .btn-sso {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12px;
        padding: 12px 20px;
        border: 2px solid rgba(102, 126, 234, 0.2);
        border-radius: 12px;
        background: rgba(255, 255, 255, 0.8);
        color: #2c3e50;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
    }

    .btn-sso:hover {
        border-color: #667eea;
        background: rgba(255, 255, 255, 0.95);
        transform: translateY(-1px);
        color: #2c3e50;
    }

    .alert-enterprise {
        background: rgba(220, 53, 69, 0.1);
        border: 2px solid rgba(220, 53, 69, 0.2);
        border-radius: 12px;
        padding: 15px;
        margin-bottom: 25px;
        color: #dc3545;
        font-weight: 500;
    }

    .security-features {
        background: rgba(102, 126, 234, 0.05);
        border-radius: 12px;
        padding: 20px;
        margin-top: 30px;
    }

    .security-features h6 {
        color: #2c3e50;
        font-weight: 700;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .security-feature {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 8px;
        font-size: 14px;
        color: #6c757d;
    }

    .security-feature i {
        color: #28a745;
        font-size: 12px;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .auth-container {
            padding: 20px;
        }

        .auth-card {
            margin: 20px;
        }

        .auth-header,
        .auth-body {
            padding: 30px 25px;
        }

        .auth-title {
            font-size: 1.5rem;
        }

        .floating-qr {
            display: none;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="auth-container">
    <!-- Animated Background -->
    <div class="auth-background">
        <div class="auth-grid-pattern"></div>
        <div class="auth-floating-elements">
            <div class="floating-qr floating-qr-1">
                <i class="fas fa-qrcode"></i>
            </div>
            <div class="floating-qr floating-qr-2">
                <i class="fas fa-qrcode"></i>
            </div>
            <div class="floating-qr floating-qr-3">
                <i class="fas fa-qrcode"></i>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="auth-card">
            <!-- Header -->
            <div class="auth-header">
                <div class="auth-logo">
                    <i class="fas fa-qrcode"></i>
                </div>
                <h1 class="auth-title">{% trans "Enterprise Login" %}</h1>
                <p class="auth-subtitle">{% trans "Access your professional QR code platform" %}</p>
            </div>

            <!-- Body -->
            <div class="auth-body">
                <!-- Enterprise Badge -->
                <div class="enterprise-badge">
                    <i class="fas fa-shield-alt"></i>
                    <span>{% trans "Enterprise Security" %}</span>
                </div>

                <!-- SSO Options -->
                {% if socialaccount_providers %}
                <div class="sso-buttons">
                    {% include "socialaccount/snippets/provider_list.html" with process="login" %}
                </div>

                <div class="divider">
                    <span>{% trans "or continue with email" %}</span>
                </div>
                {% endif %}

                <!-- Error Messages -->
                {% if form.non_field_errors %}
                <div class="alert-enterprise">
                    {% for error in form.non_field_errors %}
                        <i class="fas fa-exclamation-triangle"></i> {{ error }}
                    {% endfor %}
                </div>
                {% endif %}

                <!-- Login Form -->
                <form class="login" method="POST" action="{% url 'account_login' %}">
                    {% csrf_token %}

                    <!-- Username/Email Field -->
                    <div class="form-group">
                        <label for="id_login" class="form-label">
                            <i class="fas fa-user"></i> {% trans "Email or Username" %}
                        </label>
                        <input type="text"
                               name="login"
                               id="id_login"
                               class="form-control"
                               placeholder="{% trans 'Enter your email or username' %}"
                               required
                               autocomplete="username">
                        {% if form.login.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.login.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Password Field -->
                    <div class="form-group">
                        <label for="id_password" class="form-label">
                            <i class="fas fa-lock"></i> {% trans "Password" %}
                        </label>
                        <div class="password-input-container">
                            <input type="password"
                                   name="password"
                                   id="id_password"
                                   class="form-control"
                                   placeholder="{% trans 'Enter your password' %}"
                                   required
                                   autocomplete="current-password">
                            <button type="button" class="password-toggle" onclick="togglePassword('id_password')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        {% if form.password.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.password.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Remember Me -->
                    <div class="remember-me">
                        <input type="checkbox" name="remember" id="id_remember" class="form-check-input">
                        <label for="id_remember" class="form-check-label">
                            {% trans "Keep me signed in for 30 days" %}
                        </label>
                    </div>

                    <!-- Hidden Next Field -->
                    {% if redirect_field_value %}
                    <input type="hidden" name="{{ redirect_field_name }}" value="{{ redirect_field_value }}" />
                    {% endif %}

                    <!-- Submit Button -->
                    <button type="submit" class="btn-enterprise">
                        <i class="fas fa-sign-in-alt"></i>
                        {% trans "Sign In to Enterprise QR" %}
                    </button>
                </form>

                <!-- Auth Links -->
                <div class="auth-links">
                    <a href="{% url 'account_reset_password' %}">
                        <i class="fas fa-key"></i> {% trans "Forgot your password?" %}
                    </a>
                </div>

                {% if not socialaccount_providers %}
                <div class="auth-links" style="margin-top: 15px;">
                    <a href="{% url 'account_signup' %}">
                        <i class="fas fa-user-plus"></i> {% trans "Don't have an account? Sign up" %}
                    </a>
                </div>
                {% endif %}

                <!-- Security Features -->
                <div class="security-features">
                    <h6>
                        <i class="fas fa-shield-check"></i>
                        {% trans "Enterprise Security Features" %}
                    </h6>
                    <div class="security-feature">
                        <i class="fas fa-check"></i>
                        <span>{% trans "256-bit SSL encryption" %}</span>
                    </div>
                    <div class="security-feature">
                        <i class="fas fa-check"></i>
                        <span>{% trans "Multi-factor authentication ready" %}</span>
                    </div>
                    <div class="security-feature">
                        <i class="fas fa-check"></i>
                        <span>{% trans "SOC 2 Type II compliant" %}</span>
                    </div>
                    <div class="security-feature">
                        <i class="fas fa-check"></i>
                        <span>{% trans "Enterprise SSO integration" %}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const toggle = field.nextElementSibling;
    const icon = toggle.querySelector('i');

    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// Add loading state to form submission
document.querySelector('.login').addEventListener('submit', function(e) {
    const submitBtn = this.querySelector('.btn-enterprise');
    const originalText = submitBtn.innerHTML;

    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> {% trans "Signing in..." %}';
    submitBtn.disabled = true;

    // Re-enable button after 10 seconds as fallback
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 10000);
});
</script>
{% endblock %}