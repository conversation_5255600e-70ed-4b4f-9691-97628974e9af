{% extends 'base.html' %}
{% load static %}

{% block title %}Ad Size Guide{% endblock %}

{% block extra_css %}
<style>
    .size-guide-container {
        max-width: 900px;
        margin: 0 auto;
        padding: 30px;
    }

    .guide-header {
        text-align: center;
        margin-bottom: 40px;
    }

    .guide-title {
        font-size: 32px;
        font-weight: 700;
        color: #1a237e;
        margin-bottom: 10px;
    }

    .guide-subtitle {
        font-size: 18px;
        color: #666;
    }

    .size-cards {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 25px;
        margin-bottom: 40px;
    }

    .size-card {
        background: white;
        border-radius: 10px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .size-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .size-preview {
        background: #f5f5f5;
        padding: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .size-box {
        background: linear-gradient(135deg, #3949ab, #1e88e5);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    }

    .size-info {
        padding: 20px;
    }

    .size-name {
        font-size: 18px;
        font-weight: 600;
        color: #1a237e;
        margin-bottom: 5px;
    }

    .size-dimensions {
        font-size: 16px;
        color: #666;
        margin-bottom: 10px;
    }

    .size-description {
        font-size: 14px;
        color: #777;
        margin-bottom: 15px;
    }

    .size-locations {
        font-size: 14px;
        color: #555;
    }

    .size-locations strong {
        color: #1a237e;
    }

    .guide-tips {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 25px;
        margin-bottom: 30px;
    }

    .tips-title {
        font-size: 20px;
        font-weight: 600;
        color: #1a237e;
        margin-bottom: 15px;
    }

    .tips-list {
        list-style-type: none;
        padding: 0;
        margin: 0;
    }

    .tips-list li {
        position: relative;
        padding-left: 25px;
        margin-bottom: 12px;
        color: #555;
    }

    .tips-list li:before {
        content: '\f00c';
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        position: absolute;
        left: 0;
        color: #1e88e5;
    }

    .cta-section {
        text-align: center;
        margin-top: 40px;
    }

    .create-ad-btn {
        display: inline-block;
        background: linear-gradient(135deg, #3949ab, #1e88e5);
        color: white;
        padding: 12px 30px;
        border-radius: 5px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    }

    .create-ad-btn:hover {
        background: linear-gradient(135deg, #283593, #1565c0);
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="size-guide-container">
    <div class="guide-header">
        <h1 class="guide-title">Ad Size Guide</h1>
        <p class="guide-subtitle">Standard dimensions for different ad placements on our platform</p>
    </div>

    <div class="size-cards">
        <!-- Leaderboard -->
        <div class="size-card">
            <div class="size-preview">
                <div class="size-box" style="width: 200px; height: 25px;">
                    728 x 90 px
                </div>
            </div>
            <div class="size-info">
                <h3 class="size-name">Leaderboard</h3>
                <div class="size-dimensions">728 x 90 pixels</div>
                <p class="size-description">Large horizontal banner that spans across the top or bottom of a page.</p>
                <div class="size-locations">
                    <strong>Used in:</strong> Header, Footer
                </div>
            </div>
        </div>

        <!-- Medium Rectangle -->
        <div class="size-card">
            <div class="size-preview">
                <div class="size-box" style="width: 150px; height: 125px;">
                    300 x 250 px
                </div>
            </div>
            <div class="size-info">
                <h3 class="size-name">Medium Rectangle</h3>
                <div class="size-dimensions">300 x 250 pixels</div>
                <p class="size-description">Versatile ad format that works well in sidebars and content areas.</p>
                <div class="size-locations">
                    <strong>Used in:</strong> Sidebar, Popup
                </div>
            </div>
        </div>

        <!-- Large Rectangle -->
        <div class="size-card">
            <div class="size-preview">
                <div class="size-box" style="width: 168px; height: 140px;">
                    336 x 280 px
                </div>
            </div>
            <div class="size-info">
                <h3 class="size-name">Large Rectangle</h3>
                <div class="size-dimensions">336 x 280 pixels</div>
                <p class="size-description">Larger version of the medium rectangle, ideal for content areas.</p>
                <div class="size-locations">
                    <strong>Used in:</strong> Content Top, Content Bottom
                </div>
            </div>
        </div>
    </div>

    <div class="guide-tips">
        <h3 class="tips-title">Tips for Creating Effective Ads</h3>
        <ul class="tips-list">
            <li>Use high-quality images with the correct dimensions for best results</li>
            <li>Keep your design clean and focused with a clear call-to-action</li>
            <li>Ensure text is readable and not too small</li>
            <li>Use contrasting colors to make your ad stand out</li>
            <li>Test your ad in different locations to see which performs best</li>
            <li>Consider creating multiple versions of your ad for different placements</li>
        </ul>
    </div>

    <div class="cta-section">
        <a href="{% url 'ads:ad_create_consolidated' %}" class="create-ad-btn">
            <i class="fas fa-plus-circle me-2"></i>Create Your Ad Now
        </a>
    </div>
</div>
{% endblock %}
