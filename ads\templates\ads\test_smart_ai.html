<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart AI Engine Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .results {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 5px;
            display: none;
        }
        .suggestion {
            background: white;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .suggestion h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .suggestion p {
            margin: 0;
            color: #666;
            line-height: 1.5;
        }
        .metadata {
            font-size: 12px;
            color: #888;
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid #eee;
        }
        .loading {
            text-align: center;
            color: #666;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .success-info {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧠 Smart AI Engine Test</h1>
        
        <div class="success-info">
            <strong>✅ Test Endpoint Active!</strong><br>
            This endpoint tests the Smart AI Engine without authentication.
            It uses cache-first strategy, Groq model priority, and intelligent suggestion selection.
        </div>

        <form id="testForm">
            <div class="form-group">
                <label for="business_type">Business Type:</label>
                <input type="text" id="business_type" name="business_type" value="Premium Coffee Shop" required>
            </div>

            <div class="form-group">
                <label for="target_audience">Target Audience:</label>
                <input type="text" id="target_audience" name="target_audience" value="Young Professionals" required>
            </div>

            <div class="form-group">
                <label for="tone">Tone:</label>
                <select id="tone" name="tone" required>
                    <option value="professional" selected>Professional</option>
                    <option value="casual">Casual</option>
                    <option value="friendly">Friendly</option>
                    <option value="formal">Formal</option>
                    <option value="creative">Creative</option>
                </select>
            </div>

            <div class="form-group">
                <label for="title">Ad Title (optional):</label>
                <input type="text" id="title" name="title" value="Premium Coffee Experience">
            </div>

            <div class="form-group">
                <label for="language">Language:</label>
                <select id="language" name="language" required>
                    <option value="english" selected>English</option>
                    <option value="spanish">Spanish</option>
                    <option value="french">French</option>
                </select>
            </div>

            <div class="form-group">
                <label for="num_suggestions">Number of Suggestions:</label>
                <select id="num_suggestions" name="num_suggestions" required>
                    <option value="3" selected>3</option>
                    <option value="5">5</option>
                    <option value="10">10</option>
                </select>
            </div>

            <button type="submit" id="generateBtn">🚀 Generate Smart Suggestions</button>
        </form>

        <div id="results" class="results"></div>
    </div>

    <script>
        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const generateBtn = document.getElementById('generateBtn');
            const resultsDiv = document.getElementById('results');
            
            // Disable button and show loading
            generateBtn.disabled = true;
            generateBtn.textContent = '⏳ Generating...';
            resultsDiv.style.display = 'block';
            resultsDiv.innerHTML = '<div class="loading">🧠 Smart AI Engine is generating suggestions...</div>';
            
            // Collect form data
            const formData = new FormData(this);
            const data = Object.fromEntries(formData.entries());
            data.num_suggestions = parseInt(data.num_suggestions);
            
            const startTime = Date.now();
            
            try {
                const response = await fetch('/ads/test/smart-ai/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const endTime = Date.now();
                const responseTime = (endTime - startTime) / 1000;
                
                const result = await response.json();
                
                if (result.success) {
                    displayResults(result, responseTime);
                } else {
                    displayError(result.error || 'Unknown error occurred');
                }
                
            } catch (error) {
                displayError('Network error: ' + error.message);
            } finally {
                // Re-enable button
                generateBtn.disabled = false;
                generateBtn.textContent = '🚀 Generate Smart Suggestions';
            }
        });
        
        function displayResults(result, responseTime) {
            const resultsDiv = document.getElementById('results');
            
            let html = `
                <h2>✅ Smart AI Engine Results</h2>
                <div class="metadata">
                    <strong>Engine:</strong> ${result.engine || 'Unknown'} | 
                    <strong>Source:</strong> ${result.source || 'Unknown'} | 
                    <strong>Response Time:</strong> ${responseTime.toFixed(2)}s |
                    <strong>Suggestions:</strong> ${result.suggestions.length}
                </div>
            `;
            
            if (result.suggestions && result.suggestions.length > 0) {
                result.suggestions.forEach((suggestion, index) => {
                    html += `
                        <div class="suggestion">
                            <h3>💡 Suggestion ${index + 1}</h3>
                            <p><strong>Title:</strong> ${suggestion.title || 'N/A'}</p>
                            <p><strong>Content:</strong> ${suggestion.content || 'N/A'}</p>
                            <div class="metadata">
                                <strong>Provider:</strong> ${suggestion.provider || 'N/A'} | 
                                <strong>Model:</strong> ${suggestion.model || 'N/A'} | 
                                <strong>Cached:</strong> ${suggestion.cached ? 'Yes' : 'No'} | 
                                <strong>Smart Engine:</strong> ${suggestion.smart_engine ? 'Yes' : 'No'} |
                                <strong>Rank:</strong> ${suggestion.rank || 'N/A'} |
                                <strong>Score:</strong> ${suggestion.selection_score || 'N/A'}
                            </div>
                        </div>
                    `;
                });
            } else {
                html += '<div class="error">No suggestions were generated.</div>';
            }
            
            resultsDiv.innerHTML = html;
        }
        
        function displayError(error) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = `<div class="error"><strong>❌ Error:</strong> ${error}</div>`;
        }
    </script>
</body>
</html>
