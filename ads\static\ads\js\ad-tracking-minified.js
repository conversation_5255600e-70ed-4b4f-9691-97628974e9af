(function(){'use strict';if(window.adTrackingLoaded)return;window.adTrackingLoaded=true;window.userLocationData=window.userLocationData||{city:'unknown',region:'unknown',country:'unknown',loc:'unknown'};document.addEventListener('DOMContentLoaded',function(){getUserLocation();setupAdTracking()});function getUserLocation(){if(navigator.geolocation){navigator.geolocation.getCurrentPosition(function(p){const lat=p.coords.latitude,lon=p.coords.longitude;reverseGeocode(lat,lon)},function(e){getIPBasedLocation()},{timeout:5000,maximumAge:600000})}else{getIPBasedLocation()}}function reverseGeocode(lat,lon){fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lon}&zoom=10`).then(r=>r.json()).then(d=>{if(d&&d.address){window.userLocationData={city:d.address.city||d.address.town||d.address.village||'unknown',region:d.address.state||d.address.county||'unknown',country:d.address.country||'unknown',loc:`${lat},${lon}`}}}).catch(e=>{getIPBasedLocation()})}function getIPBasedLocation(){fetch('https://ipinfo.io/json?token=082682c8d0e7a8').then(r=>{if(!r.ok)throw new Error('Network response was not ok');return r.json()}).then(d=>{window.userLocationData={city:d.city||'unknown',region:d.region||'unknown',country:d.country||'unknown',loc:d.loc||'unknown',timezone:d.timezone||'unknown',postal:d.postal||'unknown'};if(!d.city)fallbackGeolocation()}).catch(e=>{fallbackGeolocation()})}function fallbackGeolocation(){fetch('https://ipapi.co/json/').then(r=>{if(!r.ok)throw new Error('Network response was not ok');return r.json()}).then(d=>{window.userLocationData={city:d.city||window.userLocationData.city||'unknown',region:d.region||window.userLocationData.region||'unknown',country:d.country_name||window.userLocationData.country||'unknown',loc:`${d.latitude},${d.longitude}`||window.userLocationData.loc||'unknown',timezone:d.timezone||window.userLocationData.timezone||'unknown',postal:d.postal||window.userLocationData.postal||'unknown'}}).catch(e=>{})}function setupAdTracking(){const ads=document.querySelectorAll('[data-ad-id]');ads.forEach(ad=>{const id=ad.getAttribute('data-ad-id');if(isElementVisible(ad))trackImpression(id);const obs=new IntersectionObserver(entries=>{entries.forEach(entry=>{if(entry.isIntersecting&&!ad.getAttribute('data-impression-tracked')){trackImpression(id);ad.setAttribute('data-impression-tracked','true')}})},{threshold:0.5});obs.observe(ad);ad.addEventListener('click',function(){trackClick(id)})})}function isElementVisible(el){const r=el.getBoundingClientRect();return r.top>=0&&r.left>=0&&r.bottom<=(window.innerHeight||document.documentElement.clientHeight)&&r.right<=(window.innerWidth||document.documentElement.clientWidth)}function getDeviceInfo(){let type='desktop';if(/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)){type=/iPad|tablet|Tablet/i.test(navigator.userAgent)?'tablet':'mobile'}return{type:type,browser:navigator.userAgent,screenWidth:window.innerWidth,screenHeight:window.innerHeight}}function trackImpression(id){const dev=getDeviceInfo();fetch('/ads/api/track-impression/',{method:'POST',headers:{'Content-Type':'application/json','X-CSRFToken':getCsrfToken()},body:JSON.stringify({ad_id:id,device_info:dev,location_info:window.userLocationData})}).then(r=>{if(!r.ok)throw new Error('Network response was not ok');return r.json()}).then(d=>{}).catch(e=>console.error('Error tracking impression:',e))}function trackClick(id){const dev=getDeviceInfo();fetch('/ads/api/track-click/',{method:'POST',headers:{'Content-Type':'application/json','X-CSRFToken':getCsrfToken()},body:JSON.stringify({ad_id:id,device_info:dev,location_info:window.userLocationData})}).then(r=>{if(!r.ok)throw new Error('Network response was not ok');return r.json()}).then(d=>{}).catch(e=>console.error('Error tracking click:',e))}function getCsrfToken(){const n='csrftoken',c=document.cookie.split(';');for(let cookie of c){const t=cookie.trim();if(t.startsWith(n+'='))return decodeURIComponent(t.substring(n.length+1))}return null}window.AdTracking={trackImpression,trackClick,getUserLocation,getDeviceInfo}})();
