/**
 * Notification Handler - Production Version
 * Handles notification display, actions, and user interactions
 */
(function() {
    'use strict';
    
    if (window.notificationHandlerLoaded) return;
    window.notificationHandlerLoaded = true;

    const utils = window.SharedUtils || {};
    
    function getCsrfToken() {
        const name = 'csrftoken';
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const trimmed = cookie.trim();
            if (trimmed.startsWith(name + '=')) {
                return decodeURIComponent(trimmed.substring(name.length + 1));
            }
        }
        return null;
    }

    function showNotificationError(content) {
        if (!content) return;
        content.innerHTML = `
            <div class="notification-dropdown-error p-3 text-center">
                <i class="fas fa-exclamation-circle text-danger mb-2"></i>
                <p class="mb-0">Unable to load notifications. Please try again later.</p>
            </div>`;
        content.style.opacity = '1';
        if (utils.updateNotificationCounter) utils.updateNotificationCounter(0);
    }

    function updateNotificationContent(data) {
        const content = document.getElementById('notification-dropdown-content');
        if (!content) return;

        content.style.opacity = '0';
        
        setTimeout(() => {
            if (!data.notifications || data.notifications.length === 0) {
                content.innerHTML = `
                    <div class="notification-dropdown-empty p-3 text-center">
                        <i class="fas fa-bell-slash"></i>
                        <p>0 notifications</p>
                    </div>`;
                content.style.opacity = '1';
                return;
            }

            const itemsPerPage = 4;
            const totalPages = Math.ceil(data.notifications.length / itemsPerPage);
            let currentPage = 1;

            function renderPage(page) {
                const startIndex = (page - 1) * itemsPerPage;
                const endIndex = Math.min(startIndex + itemsPerPage, data.notifications.length);
                const pageNotifications = data.notifications.slice(startIndex, endIndex);

                let html = '';
                pageNotifications.forEach(notification => {
                    let iconClass = 'fas fa-bell';
                    let iconColorClass = '';

                    switch(notification.notification_type) {
                        case 'success': iconClass = 'fas fa-check-circle'; iconColorClass = 'success'; break;
                        case 'warning': iconClass = 'fas fa-exclamation-triangle'; iconColorClass = 'warning'; break;
                        case 'error': iconClass = 'fas fa-exclamation-circle'; iconColorClass = 'danger'; break;
                        case 'info': iconClass = 'fas fa-info-circle'; iconColorClass = 'info'; break;
                    }

                    switch(notification.category) {
                        case 'ad': iconClass = 'fas fa-ad'; break;
                        case 'payment': iconClass = 'fas fa-credit-card'; break;
                        case 'qr_code': iconClass = 'fas fa-qrcode'; break;
                        case 'user': iconClass = 'fas fa-user'; break;
                        case 'campaign': iconClass = 'fas fa-bullhorn'; break;
                        case 'analytics': iconClass = 'fas fa-chart-bar'; break;
                    }

                    const formattedTime = notification.time_ago || 
                        (notification.created_at ? utils.formatTimestamp ? utils.formatTimestamp(new Date(notification.created_at)) : '' : '');

                    html += `
                        <div class="notification-item ${!notification.is_read ? 'unread' : ''}" data-notification-id="${notification.id}">
                            <div class="notification-item-icon ${iconColorClass}">
                                <i class="${iconClass}"></i>
                            </div>
                            <div class="notification-item-content">
                                <div class="notification-item-title">${notification.title}</div>
                                <div class="notification-item-message">${notification.message}</div>
                                <div class="notification-item-time">${formattedTime}</div>
                            </div>
                            <div class="notification-item-actions">
                                <button class="notification-action-toggle" aria-label="More actions">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <div class="notification-action-dropdown">
                                    <button class="notification-action-btn mark-read" data-action="mark-read">
                                        <i class="fas fa-check"></i> Mark as ${!notification.is_read ? 'read' : 'unread'}
                                    </button>
                                    <button class="notification-action-btn archive" data-action="archive">
                                        <i class="fas fa-archive"></i> Archive
                                    </button>
                                    <button class="notification-action-btn delete" data-action="delete">
                                        <i class="fas fa-trash-alt"></i> Delete
                                    </button>
                                </div>
                            </div>
                            ${notification.url ? `<a href="${notification.url}" class="notification-item-link" aria-label="View details"></a>` : ''}
                        </div>`;
                });

                if (totalPages > 1) {
                    html += '<div class="notification-pagination">';
                    html += `<button class="notification-pagination-btn prev ${page === 1 ? 'disabled' : ''}" ${page === 1 ? 'disabled' : ''}><i class="fas fa-chevron-left"></i></button>`;
                    
                    for (let i = 1; i <= totalPages; i++) {
                        html += `<button class="notification-pagination-btn page ${i === page ? 'active' : ''}" data-page="${i}">${i}</button>`;
                    }
                    
                    html += `<button class="notification-pagination-btn next ${page === totalPages ? 'disabled' : ''}" ${page === totalPages ? 'disabled' : ''}><i class="fas fa-chevron-right"></i></button>`;
                    html += '</div>';
                }

                content.innerHTML = html;

                if (totalPages > 1) {
                    document.querySelectorAll('.notification-pagination-btn.page').forEach(btn => {
                        btn.addEventListener('click', function() {
                            currentPage = parseInt(this.getAttribute('data-page'));
                            renderPage(currentPage);
                        });
                    });

                    const prevBtn = document.querySelector('.notification-pagination-btn.prev');
                    if (prevBtn && !prevBtn.disabled) {
                        prevBtn.addEventListener('click', () => {
                            if (currentPage > 1) {
                                currentPage--;
                                renderPage(currentPage);
                            }
                        });
                    }

                    const nextBtn = document.querySelector('.notification-pagination-btn.next');
                    if (nextBtn && !nextBtn.disabled) {
                        nextBtn.addEventListener('click', () => {
                            if (currentPage < totalPages) {
                                currentPage++;
                                renderPage(currentPage);
                            }
                        });
                    }
                }
            }

            renderPage(currentPage);
            setupNotificationActions();
            setTimeout(() => content.style.opacity = '1', 50);
        }, 200);
    }

    function setupNotificationActions() {
        document.querySelectorAll('.notification-action-toggle').forEach(toggle => {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                document.querySelectorAll('.notification-action-dropdown.show').forEach(dropdown => {
                    if (dropdown !== this.nextElementSibling) {
                        dropdown.classList.remove('show');
                    }
                });
                
                this.nextElementSibling.classList.toggle('show');
            });
        });

        document.querySelectorAll('.notification-action-btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                const item = this.closest('.notification-item');
                const id = item.getAttribute('data-notification-id');
                const action = this.getAttribute('data-action');
                
                switch(action) {
                    case 'mark-read': handleMarkReadUnread(id, item); break;
                    case 'archive': handleArchiveNotification(id, item); break;
                    case 'delete': showDeleteConfirmation(id, item); break;
                }
                
                this.closest('.notification-action-dropdown').classList.remove('show');
            });
        });

        document.addEventListener('click', function(e) {
            if (!e.target.closest('.notification-item-actions')) {
                document.querySelectorAll('.notification-action-dropdown.show').forEach(dropdown => {
                    dropdown.classList.remove('show');
                });
            }
        });
    }

    function handleMarkReadUnread(id, item) {
        const isUnread = item.classList.contains('unread');
        const url = `/notifications/${id}/mark-as-${isUnread ? 'read' : 'unread'}/`;
        
        fetch(url, {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCsrfToken(),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            item.classList.toggle('unread');
            const btn = item.querySelector('.notification-action-btn.mark-read');
            if (btn) {
                btn.innerHTML = `<i class="fas fa-check"></i> Mark as ${isUnread ? 'unread' : 'read'}`;
            }
            if (utils.updateNotificationCounter) utils.updateNotificationCounter(data.unread_count);
        })
        .catch(error => console.error('Error updating notification:', error));
    }

    function handleArchiveNotification(id, item) {
        fetch(`/notifications/${id}/archive/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCsrfToken(),
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            removeNotificationWithAnimation(item);
            showNotificationToast('Notification archived successfully.', 'success');
            if (utils.updateNotificationCounter) utils.updateNotificationCounter(data.unread_count);
        })
        .catch(error => {
            console.error('Error archiving notification:', error);
            showNotificationToast('Failed to archive notification.', 'error');
        });
    }

    function showDeleteConfirmation(id, item) {
        let dialog = document.getElementById('notification-confirm-dialog');
        
        if (!dialog) {
            dialog = document.createElement('div');
            dialog.id = 'notification-confirm-dialog';
            dialog.className = 'notification-confirm-dialog';
            dialog.innerHTML = `
                <div class="notification-confirm-content">
                    <div class="notification-confirm-title">
                        <i class="fas fa-exclamation-triangle"></i> Delete Notification
                    </div>
                    <div class="notification-confirm-message">
                        Are you sure you want to delete this notification? This action cannot be undone.
                    </div>
                    <div class="notification-confirm-actions">
                        <button class="notification-confirm-btn cancel">Cancel</button>
                        <button class="notification-confirm-btn confirm">Delete</button>
                    </div>
                </div>`;
            document.body.appendChild(dialog);
            
            dialog.querySelector('.cancel').addEventListener('click', () => dialog.classList.remove('show'));
            dialog.addEventListener('click', (e) => {
                if (e.target === dialog) dialog.classList.remove('show');
            });
        }

        const confirmBtn = dialog.querySelector('.confirm');
        const newConfirmBtn = confirmBtn.cloneNode(true);
        confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
        
        newConfirmBtn.addEventListener('click', () => {
            handleDeleteNotification(id, item);
            dialog.classList.remove('show');
        });
        
        dialog.classList.add('show');
    }

    function handleDeleteNotification(id, item) {
        fetch(`/notifications/${id}/delete/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCsrfToken(),
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            removeNotificationWithAnimation(item);
            showNotificationToast('Notification deleted successfully.', 'success');
            if (utils.updateNotificationCounter) utils.updateNotificationCounter(data.unread_count);
        })
        .catch(error => {
            console.error('Error deleting notification:', error);
            showNotificationToast('Failed to delete notification.', 'error');
        });
    }

    function removeNotificationWithAnimation(item) {
        item.style.opacity = '0';
        item.style.height = item.offsetHeight + 'px';
        item.style.overflow = 'hidden';
        
        setTimeout(() => {
            item.style.height = '0';
            item.style.margin = '0';
            item.style.padding = '0';
            setTimeout(() => {
                item.remove();
                const remaining = document.querySelectorAll('.notification-item');
                if (remaining.length === 0) {
                    const content = document.getElementById('notification-dropdown-content');
                    if (content) {
                        content.innerHTML = `
                            <div class="notification-dropdown-empty p-3 text-center">
                                <i class="fas fa-bell-slash"></i>
                                <p>0 notifications</p>
                            </div>`;
                    }
                }
            }, 300);
        }, 50);
    }

    function showNotificationToast(message, type = 'info') {
        let container = document.getElementById('notification-toast-container');
        
        if (!container) {
            container = document.createElement('div');
            container.id = 'notification-toast-container';
            container.style.cssText = 'position:fixed;top:20px;right:20px;z-index:9999';
            document.body.appendChild(container);
        }

        const toast = document.createElement('div');
        const colors = {success: '#4caf50', error: '#f44336', warning: '#ff9800', info: '#2196f3'};
        const icons = {success: 'check-circle', error: 'exclamation-circle', warning: 'exclamation-triangle', info: 'info-circle'};
        
        toast.style.cssText = `
            background:${colors[type]};color:white;padding:12px 20px;border-radius:4px;
            margin-bottom:10px;box-shadow:0 2px 10px rgba(0,0,0,0.1);display:flex;
            align-items:center;justify-content:space-between;opacity:0;
            transform:translateX(50px);transition:opacity 0.3s ease,transform 0.3s ease`;
        
        toast.innerHTML = `
            <div style="display:flex;align-items:center">
                <i class="fas fa-${icons[type]}" style="margin-right:10px"></i>
                <span>${message}</span>
            </div>
            <button style="background:none;border:none;color:white;cursor:pointer;margin-left:10px">
                <i class="fas fa-times"></i>
            </button>`;
        
        container.appendChild(toast);
        
        setTimeout(() => {
            toast.style.opacity = '1';
            toast.style.transform = 'translateX(0)';
        }, 10);
        
        const closeBtn = toast.querySelector('button');
        const hideToast = () => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(50px)';
            setTimeout(() => toast.remove(), 300);
        };
        
        closeBtn.addEventListener('click', hideToast);
        setTimeout(hideToast, 5000);
    }

    // Export functions for external use
    window.NotificationHandler = {
        showNotificationError,
        updateNotificationContent,
        setupNotificationActions,
        showNotificationToast
    };

})();
