/**
 * Modal Production CSS
 * Enterprise-grade modal styling with optimized performance
 */

/* Modal Container */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 9999;
    overflow: auto;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modal.show {
    opacity: 1;
}

/* Modal Content */
.modal-content {
    position: relative;
    background-color: #fff;
    margin: 50px auto;
    padding: 0;
    width: 90%;
    max-width: 600px;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
    transform: translateY(-20px);
    transition: transform 0.3s ease;
    overflow: hidden;
}

.modal.show .modal-content {
    transform: translateY(0);
}

/* Modal Header */
.modal-header {
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
    color: #333;
}

.modal-close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.modal-close:hover {
    color: #333;
}

/* Modal Body */
.modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

/* Modal Footer */
.modal-footer {
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Review Sections */
.review-section {
    margin-bottom: 20px;
}

.review-section h3 {
    font-size: 1.2rem;
    color: #333;
    margin-bottom: 10px;
    font-weight: 600;
}

.review-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.review-content {
    color: #555;
    line-height: 1.5;
    white-space: pre-line;
}

.review-details {
    list-style: none;
    padding: 0;
    margin: 0;
}

.review-details li {
    margin-bottom: 8px;
    color: #555;
}

.review-details li strong {
    color: #333;
    font-weight: 600;
    margin-right: 5px;
}

/* Error Container */
.error-container {
    background-color: #f8d7da;
    color: #721c24;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    border: 1px solid #f5c6cb;
    display: none;
}

.error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 5px;
}

.form-control.error {
    border-color: #dc3545;
}

/* Success Container */
.success-container {
    background-color: #d4edda;
    color: #155724;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    border: 1px solid #c3e6cb;
    display: none;
}

/* Modal Animation */
@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes modalFadeOut {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-20px);
    }
}

/* Responsive Styles */
@media (max-width: 767px) {
    .modal-content {
        margin: 20px auto;
        width: 95%;
    }
    
    .modal-body {
        max-height: 70vh;
    }
    
    .modal-footer {
        flex-direction: column;
    }
    
    .modal-footer .btn {
        width: 100%;
        margin-bottom: 10px;
    }
}
