{% extends 'base.html' %}
{% load static %}

{% block title %}Enterprise QR | Encrypted QR Codes{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/premium.css' %}">
<style>
    /* Ultra-Premium Encrypted QR Corporate Styling */
    body {
        background:
            linear-gradient(135deg, #000000 0%, #0a0a0a 10%, #1a1a2e 25%, #16213e 40%, #0f3460 60%, #533483 80%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        position: relative;
        overflow-x: hidden;
    }

    /* Premium animated background with security patterns */
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 3% 97%, rgba(102, 126, 234, 0.7) 0%, transparent 25%),
            radial-gradient(circle at 97% 3%, rgba(255, 255, 255, 0.3) 0%, transparent 20%),
            radial-gradient(circle at 20% 75%, rgba(118, 75, 162, 0.6) 0%, transparent 30%),
            radial-gradient(circle at 80% 90%, rgba(83, 52, 131, 0.5) 0%, transparent 15%),
            radial-gradient(circle at 65% 45%, rgba(102, 126, 234, 0.4) 0%, transparent 35%),
            radial-gradient(circle at 35% 25%, rgba(255, 255, 255, 0.2) 0%, transparent 25%),
            radial-gradient(circle at 50% 80%, rgba(118, 75, 162, 0.3) 0%, transparent 40%);
        z-index: -1;
        animation: securityFloat 40s ease-in-out infinite;
    }

    /* Corporate security pattern overlay */
    body::after {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image:
            radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.08) 2px, transparent 2px),
            radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.06) 1px, transparent 1px),
            linear-gradient(45deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px),
            linear-gradient(-45deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
        background-size: 100px 100px, 60px 60px, 40px 40px, 40px 40px;
        z-index: -1;
        opacity: 0.8;
        animation: securityPatternPulse 20s ease-in-out infinite;
    }

    @keyframes securityFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
        14% { transform: translateY(-30px) rotate(2deg) scale(1.05); }
        28% { transform: translateY(20px) rotate(-1deg) scale(0.95); }
        42% { transform: translateY(-25px) rotate(1.5deg) scale(1.04); }
        56% { transform: translateY(15px) rotate(-1.2deg) scale(0.97); }
        70% { transform: translateY(-20px) rotate(0.8deg) scale(1.02); }
        84% { transform: translateY(10px) rotate(-0.5deg) scale(0.99); }
    }

    @keyframes securityPatternPulse {
        0%, 100% { opacity: 0.6; transform: scale(1) rotate(0deg); }
        50% { opacity: 1; transform: scale(1.03) rotate(2deg); }
    }

    /* Ultra-Premium Hero Section */
    .premium-hero {
        padding: 6rem 0;
        position: relative;
        background: linear-gradient(135deg,
            rgba(0, 0, 0, 0.8) 0%,
            rgba(26, 26, 46, 0.7) 30%,
            rgba(22, 33, 62, 0.6) 60%,
            rgba(15, 52, 96, 0.5) 100%);
        backdrop-filter: blur(30px);
        border-bottom: 3px solid rgba(255, 255, 255, 0.2);
        box-shadow:
            0 30px 60px rgba(0, 0, 0, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }

    .premium-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: conic-gradient(from 0deg,
            rgba(102, 126, 234, 0.3) 0deg,
            rgba(255, 255, 255, 0.2) 90deg,
            rgba(118, 75, 162, 0.3) 180deg,
            rgba(255, 255, 255, 0.1) 270deg,
            rgba(102, 126, 234, 0.3) 360deg);
        z-index: 0;
        animation: heroSecurityGlow 15s linear infinite;
        opacity: 0.6;
    }

    @keyframes heroSecurityGlow {
        0% { transform: rotate(0deg); opacity: 0.4; }
        50% { opacity: 0.8; }
        100% { transform: rotate(360deg); opacity: 0.4; }
    }

    .premium-hero-content {
        position: relative;
        z-index: 2;
        text-align: center;
        max-width: 800px;
        margin: 0 auto;
        padding: 0 2rem;
    }

    /* Premium Badge with Security Theme */
    .premium-badge {
        display: inline-flex;
        align-items: center;
        background: linear-gradient(135deg,
            rgba(102, 126, 234, 0.9) 0%,
            rgba(118, 75, 162, 0.9) 100%);
        color: white;
        padding: 1rem 2rem;
        border-radius: 50px;
        font-weight: 800;
        font-size: 0.9rem;
        letter-spacing: 1.5px;
        text-transform: uppercase;
        margin-bottom: 2rem;
        box-shadow:
            0 15px 40px rgba(102, 126, 234, 0.4),
            0 8px 25px rgba(0, 0, 0, 0.3),
            inset 0 2px 0 rgba(255, 255, 255, 0.3);
        border: 2px solid rgba(255, 255, 255, 0.3);
        position: relative;
        overflow: hidden;
        animation: badgeSecurityPulse 3s ease-in-out infinite;
    }

    .premium-badge::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
            transparent 0%,
            rgba(255, 255, 255, 0.4) 50%,
            transparent 100%);
        animation: badgeShimmer 4s ease-in-out infinite;
    }

    @keyframes badgeSecurityPulse {
        0%, 100% {
            box-shadow:
                0 15px 40px rgba(102, 126, 234, 0.4),
                0 8px 25px rgba(0, 0, 0, 0.3),
                inset 0 2px 0 rgba(255, 255, 255, 0.3);
        }
        50% {
            box-shadow:
                0 20px 50px rgba(102, 126, 234, 0.6),
                0 12px 35px rgba(0, 0, 0, 0.4),
                inset 0 3px 0 rgba(255, 255, 255, 0.4);
        }
    }

    @keyframes badgeShimmer {
        0%, 100% { left: -100%; }
        50% { left: 100%; }
    }

    .premium-badge i {
        margin-right: 0.8rem;
        font-size: 1.1rem;
        filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.3));
        animation: crownRotate 6s ease-in-out infinite;
    }

    @keyframes crownRotate {
        0%, 100% { transform: rotate(0deg) scale(1); }
        25% { transform: rotate(5deg) scale(1.1); }
        75% { transform: rotate(-3deg) scale(1.05); }
    }

    /* Ultra-Premium Title */
    .premium-title {
        font-size: 4rem;
        font-weight: 900;
        color: white;
        margin-bottom: 1.5rem;
        background: linear-gradient(135deg, #ffffff 0%, #e0e0e0 30%, #ffffff 70%, #f0f0f0 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 15px 50px rgba(0, 0, 0, 0.7);
        position: relative;
        animation: titleSecurityGlow 6s ease-in-out infinite;
        line-height: 1.1;
        letter-spacing: -0.02em;
    }

    .premium-title::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.5) 0%, rgba(118, 75, 162, 0.5) 100%);
        -webkit-background-clip: text;
        background-clip: text;
        z-index: -1;
        animation: titleShimmer 10s ease-in-out infinite;
    }

    @keyframes titleSecurityGlow {
        0%, 100% { text-shadow: 0 15px 50px rgba(0, 0, 0, 0.7); }
        50% { text-shadow: 0 15px 50px rgba(102, 126, 234, 0.5), 0 0 100px rgba(255, 255, 255, 0.4); }
    }

    @keyframes titleShimmer {
        0%, 100% { opacity: 0.5; }
        50% { opacity: 0.9; }
    }

    /* Premium Subtitle */
    .premium-subtitle {
        font-size: 1.4rem;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 3rem;
        line-height: 1.8;
        font-weight: 500;
        text-shadow: 0 8px 30px rgba(0, 0, 0, 0.4);
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
        letter-spacing: 0.5px;
    }

    /* Premium CTA Buttons */
    .hero-cta {
        display: flex;
        gap: 1.5rem;
        justify-content: center;
        flex-wrap: wrap;
    }

    .btn {
        border-radius: 25px;
        padding: 1.2rem 3rem;
        font-weight: 800;
        font-size: 1rem;
        letter-spacing: 1.2px;
        transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        text-transform: uppercase;
        border: none;
        position: relative;
        overflow: hidden;
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
    }

    .btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
            transparent 0%,
            rgba(255, 255, 255, 0.4) 50%,
            transparent 100%);
        transition: left 1s ease;
    }

    .btn:hover::before {
        left: 100%;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
        color: white;
        box-shadow:
            0 15px 45px rgba(102, 126, 234, 0.5),
            0 8px 25px rgba(0, 0, 0, 0.2),
            inset 0 3px 0 rgba(255, 255, 255, 0.3);
        border: 3px solid rgba(255, 255, 255, 0.3);
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 50%, #5a67d8 100%);
        transform: translateY(-6px) scale(1.05);
        box-shadow:
            0 25px 60px rgba(102, 126, 234, 0.6),
            0 15px 40px rgba(0, 0, 0, 0.3),
            inset 0 4px 0 rgba(255, 255, 255, 0.4);
        color: white;
        border-color: rgba(255, 255, 255, 0.5);
    }

    .btn-outline-light {
        color: white;
        border: 3px solid rgba(255, 255, 255, 0.5);
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
        backdrop-filter: blur(20px);
    }

    .btn-outline-light:hover {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 249, 250, 0.8) 100%);
        color: #2c3e50;
        transform: translateY(-6px) scale(1.05);
        box-shadow:
            0 20px 50px rgba(255, 255, 255, 0.3),
            0 12px 30px rgba(0, 0, 0, 0.2);
        border-color: rgba(255, 255, 255, 0.8);
    }

    /* Premium Overview Section */
    .premium-overview {
        padding: 8rem 0;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.98) 0%,
            rgba(248, 249, 250, 0.95) 50%,
            rgba(255, 255, 255, 0.98) 100%);
        position: relative;
        backdrop-filter: blur(30px);
        border-top: 3px solid rgba(102, 126, 234, 0.3);
        border-bottom: 3px solid rgba(102, 126, 234, 0.3);
    }

    .premium-overview::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg,
            rgba(102, 126, 234, 0.05) 0%,
            rgba(255, 255, 255, 0.1) 50%,
            rgba(118, 75, 162, 0.05) 100%);
        z-index: 0;
    }

    .premium-overview .container {
        position: relative;
        z-index: 1;
    }

    .section-title {
        font-size: 3rem;
        font-weight: 900;
        color: #2c3e50;
        margin-bottom: 2rem;
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
        animation: sectionTitlePulse 4s ease-in-out infinite;
    }

    @keyframes sectionTitlePulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.02); }
    }

    .section-description {
        font-size: 1.3rem;
        color: #5a6c7d;
        line-height: 1.8;
        margin-bottom: 2rem;
        font-weight: 500;
    }

    /* Premium Feature List */
    .feature-list {
        list-style: none;
        padding: 0;
        margin: 2rem 0;
    }

    .feature-list li {
        display: flex;
        align-items: center;
        padding: 1rem 0;
        font-size: 1.1rem;
        color: #2c3e50;
        font-weight: 600;
        border-bottom: 1px solid rgba(102, 126, 234, 0.1);
        transition: all 0.3s ease;
    }

    .feature-list li:hover {
        background: rgba(102, 126, 234, 0.05);
        transform: translateX(10px);
        padding-left: 1rem;
        border-radius: 8px;
    }

    .feature-list li i {
        color: #10b981;
        margin-right: 1rem;
        font-size: 1.2rem;
        filter: drop-shadow(0 2px 8px rgba(16, 185, 129, 0.3));
    }

    /* Premium Features Section */
    .premium-features {
        padding: 8rem 0;
        background: linear-gradient(135deg,
            rgba(0, 0, 0, 0.95) 0%,
            rgba(26, 26, 46, 0.9) 30%,
            rgba(22, 33, 62, 0.85) 60%,
            rgba(15, 52, 96, 0.8) 100%);
        position: relative;
    }

    .premium-features::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: conic-gradient(from 0deg,
            rgba(102, 126, 234, 0.2) 0deg,
            rgba(255, 255, 255, 0.1) 90deg,
            rgba(118, 75, 162, 0.2) 180deg,
            rgba(255, 255, 255, 0.05) 270deg,
            rgba(102, 126, 234, 0.2) 360deg);
        z-index: 0;
        animation: featuresGlow 20s linear infinite;
        opacity: 0.7;
    }

    @keyframes featuresGlow {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .premium-features .container {
        position: relative;
        z-index: 1;
    }

    .premium-features .section-title {
        color: white;
        background: linear-gradient(135deg, #ffffff 0%, #e0e0e0 50%, #ffffff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 8px 30px rgba(0, 0, 0, 0.5);
    }

    /* Ultra-Premium Feature Cards */
    .feature-card {
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.98) 0%,
            rgba(248, 249, 250, 0.95) 50%,
            rgba(255, 255, 255, 0.98) 100%);
        border-radius: 32px;
        padding: 3rem 2.5rem;
        text-align: center;
        transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        box-shadow:
            0 30px 60px rgba(0, 0, 0, 0.3),
            0 20px 40px rgba(0, 0, 0, 0.2),
            inset 0 2px 0 rgba(255, 255, 255, 0.5);
        border: 3px solid rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(30px);
        position: relative;
        overflow: hidden;
        margin-bottom: 2rem;
    }

    .feature-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg,
            rgba(102, 126, 234, 0.08) 0%,
            rgba(255, 255, 255, 0.1) 50%,
            rgba(118, 75, 162, 0.08) 100%);
        z-index: 0;
        border-radius: 32px;
    }

    .feature-card::after {
        content: '';
        position: absolute;
        top: -3px;
        left: -3px;
        width: calc(100% + 6px);
        height: calc(100% + 6px);
        background: conic-gradient(from 0deg,
            rgba(102, 126, 234, 0.4) 0deg,
            rgba(255, 255, 255, 0.3) 90deg,
            rgba(118, 75, 162, 0.4) 180deg,
            rgba(255, 255, 255, 0.2) 270deg,
            rgba(102, 126, 234, 0.4) 360deg);
        border-radius: 35px;
        z-index: -1;
        opacity: 0;
        transition: opacity 0.8s ease;
        animation: featureCardGlow 12s linear infinite;
    }

    .feature-card:hover {
        transform: translateY(-15px) scale(1.03) rotateX(5deg);
        box-shadow:
            0 50px 100px rgba(0, 0, 0, 0.4),
            0 30px 60px rgba(0, 0, 0, 0.3),
            0 0 80px rgba(102, 126, 234, 0.3),
            inset 0 3px 0 rgba(255, 255, 255, 0.6);
        border-color: rgba(255, 255, 255, 0.5);
    }

    .feature-card:hover::after {
        opacity: 1;
    }

    @keyframes featureCardGlow {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .feature-card > * {
        position: relative;
        z-index: 1;
    }

    /* Premium Feature Icons */
    .feature-icon {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 2rem;
        font-size: 2.5rem;
        color: white;
        box-shadow:
            0 20px 40px rgba(102, 126, 234, 0.4),
            0 10px 20px rgba(0, 0, 0, 0.2),
            inset 0 3px 0 rgba(255, 255, 255, 0.3);
        transition: all 0.6s ease;
        position: relative;
        overflow: hidden;
    }

    .feature-icon::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: conic-gradient(from 0deg,
            rgba(255, 255, 255, 0.4) 0deg,
            transparent 90deg,
            rgba(255, 255, 255, 0.4) 180deg,
            transparent 270deg,
            rgba(255, 255, 255, 0.4) 360deg);
        animation: iconGlow 4s linear infinite;
    }

    .feature-icon:hover {
        transform: scale(1.15) rotate(10deg);
        box-shadow:
            0 30px 60px rgba(102, 126, 234, 0.5),
            0 15px 30px rgba(0, 0, 0, 0.3),
            inset 0 4px 0 rgba(255, 255, 255, 0.4);
    }

    @keyframes iconGlow {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .feature-card h3 {
        font-size: 1.8rem;
        font-weight: 800;
        color: #2c3e50;
        margin-bottom: 1.5rem;
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .feature-card p {
        font-size: 1.1rem;
        color: #5a6c7d;
        line-height: 1.7;
        font-weight: 500;
    }
</style>
{% endblock %}

{% block content %}
<div class="premium-hero encrypted-hero">
    <div class="premium-hero-content">
        <div class="premium-badge">
            <i class="fas fa-crown"></i>
            <span>Premium Feature</span>
        </div>
        <h1 class="premium-title">Encrypted QR Codes</h1>
        <p class="premium-subtitle">Secure your data with enterprise-grade encryption for sensitive information</p>
        <div class="hero-cta">
            <a href="{% url 'generate_qr_code' %}" class="btn btn-primary btn-lg">Create Secure QR</a>
            <a href="#features" class="btn btn-outline-light btn-lg">Learn More</a>
        </div>
    </div>
    <div class="premium-hero-image">
        <img src="{% static 'img/premium/encrypted-hero.svg' %}" alt="Encrypted QR Codes">
    </div>
</div>

<div class="premium-overview">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h2 class="section-title">Enhanced Security for Sensitive Data</h2>
                <p class="section-description">Our encrypted QR codes provide an additional layer of security for sensitive information, ensuring that only authorized users can access the data contained within.</p>
                <p>With Enterprise QR's encrypted QR codes, you can:</p>
                <ul class="feature-list">
                    <li><i class="fas fa-check-circle"></i> Protect confidential information with AES-256 encryption</li>
                    <li><i class="fas fa-check-circle"></i> Implement password protection for QR code access</li>
                    <li><i class="fas fa-check-circle"></i> Set expiration dates for temporary access</li>
                    <li><i class="fas fa-check-circle"></i> Control access with user authentication</li>
                    <li><i class="fas fa-check-circle"></i> Track access attempts for security monitoring</li>
                </ul>
            </div>
            <div class="col-lg-6">
                <div class="overview-image">
                    <img src="{% static 'img/premium/encrypted-overview.svg' %}" alt="Encrypted QR Overview">
                </div>
            </div>
        </div>
    </div>
</div>

<div id="features" class="premium-features">
    <div class="container">
        <h2 class="section-title text-center">Key Features</h2>
        <div class="row">
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>AES-256 Encryption</h3>
                    <p>Protect your data with military-grade AES-256 encryption, ensuring that sensitive information remains secure even if the QR code is intercepted.</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-key"></i>
                    </div>
                    <h3>Password Protection</h3>
                    <p>Add an additional layer of security with password-protected QR codes that require authentication before revealing the encrypted content.</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-hourglass-end"></i>
                    </div>
                    <h3>Time-Limited Access</h3>
                    <p>Set expiration dates and times for your QR codes, automatically revoking access after a specified period for temporary credentials.</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-user-lock"></i>
                    </div>
                    <h3>User Authentication</h3>
                    <p>Restrict access to specific users or groups by integrating with your existing authentication systems or our secure user management.</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-clipboard-check"></i>
                    </div>
                    <h3>Access Logging</h3>
                    <p>Monitor and log all access attempts to your encrypted QR codes, providing a complete audit trail for security compliance.</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-fingerprint"></i>
                    </div>
                    <h3>Biometric Verification</h3>
                    <p>Add an extra layer of security with optional biometric verification (fingerprint or facial recognition) on compatible devices.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="premium-use-cases">
    <div class="container">
        <h2 class="section-title text-center">Common Use Cases</h2>
        <div class="row">
            <div class="col-lg-6">
                <div class="use-case-card">
                    <div class="use-case-icon">
                        <i class="fas fa-file-contract"></i>
                    </div>
                    <div class="use-case-content">
                        <h3>Confidential Documents</h3>
                        <p>Secure access to sensitive documents, contracts, and legal materials with encrypted QR codes that verify user authorization.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="use-case-card">
                    <div class="use-case-icon">
                        <i class="fas fa-id-badge"></i>
                    </div>
                    <div class="use-case-content">
                        <h3>Secure Credentials</h3>
                        <p>Distribute secure access credentials, login information, and authentication tokens via encrypted QR codes for controlled access.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="use-case-card">
                    <div class="use-case-icon">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <div class="use-case-content">
                        <h3>Healthcare Information</h3>
                        <p>Protect patient records, medical information, and healthcare data with HIPAA-compliant encrypted QR codes.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="use-case-card">
                    <div class="use-case-icon">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <div class="use-case-content">
                        <h3>Financial Information</h3>
                        <p>Securely share payment details, account information, and financial data with encrypted QR codes that protect sensitive information.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="premium-cta">
    <div class="container">
        <div class="cta-content">
            <h2>Ready to Secure Your QR Codes?</h2>
            <p>Start creating encrypted QR codes today and ensure your sensitive information remains protected.</p>
            <div class="cta-buttons">
                <a href="{% url 'generate_qr_code' %}" class="btn btn-primary btn-lg">Create Secure QR Now</a>
                <a href="#" class="btn btn-outline-light btn-lg">Contact Sales</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
