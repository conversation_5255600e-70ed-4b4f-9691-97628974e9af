/**
 * dom-ready.js
 * 
 * This script ensures all DOM manipulation is deferred until the DOM is fully loaded.
 * It provides a centralized way to register initialization functions that should run
 * only after the DOM is ready.
 */

// Create a global namespace for DOM ready functionality
window.DOMReadyManager = (function() {
    // Store initialization functions
    const initFunctions = [];
    
    // Flag to track if DOM is ready
    let isDOMReady = false;
    
    // Flag to track if initialization has been executed
    let hasInitialized = false;
    
    // Register a function to be executed when DOM is ready
    function registerInitFunction(fn, name = 'unnamed') {
        if (typeof fn !== 'function') {
            console.error(`DOMReadyManager: Attempted to register non-function: ${fn}`);
            return;
        }
        
        // If DOM is already ready, execute immediately
        if (isDOMReady && !hasInitialized) {
            try {
                console.log(`DOMReadyManager: Executing function '${name}' immediately`);
                fn();
            } catch (error) {
                console.error(`DOMReadyManager: Error executing function '${name}':`, error);
            }
        } else {
            // Otherwise, store for later execution
            console.log(`DOMReadyManager: Registered function '${name}' for later execution`);
            initFunctions.push({ fn, name });
        }
    }
    
    // Execute all registered initialization functions
    function executeInitFunctions() {
        if (hasInitialized) {
            console.warn('DOMReadyManager: Init functions already executed, skipping');
            return;
        }
        
        console.log(`DOMReadyManager: Executing ${initFunctions.length} initialization functions`);
        
        // Execute each function in the order they were registered
        initFunctions.forEach(({ fn, name }) => {
            try {
                console.log(`DOMReadyManager: Executing '${name}'`);
                fn();
            } catch (error) {
                console.error(`DOMReadyManager: Error executing '${name}':`, error);
            }
        });
        
        // Mark as initialized
        hasInitialized = true;
        console.log('DOMReadyManager: All initialization functions executed');
    }
    
    // Set up the DOM ready event listener
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOMReadyManager: DOM is ready');
        isDOMReady = true;
        executeInitFunctions();
    });
    
    // Fallback for cases where DOMContentLoaded might have already fired
    if (document.readyState === 'interactive' || document.readyState === 'complete') {
        console.log(`DOMReadyManager: DOM already ready (${document.readyState})`);
        isDOMReady = true;
        
        // Execute on next tick to allow for registrations
        setTimeout(executeInitFunctions, 0);
    }
    
    // Public API
    return {
        register: registerInitFunction,
        isDOMReady: function() { return isDOMReady; },
        hasInitialized: function() { return hasInitialized; }
    };
})();

// Log that the DOM ready manager is loaded
console.log('DOM Ready Manager loaded');
