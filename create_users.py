#!/usr/bin/env python
"""
Script to create test users for privilege testing
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'qrcode_project.settings')
django.setup()

from django.contrib.auth.models import User
from qrcode_app.models import UserProfile, Plan, Subscription
from django.utils import timezone

def create_test_users():
    print("🚀 Creating test users for privilege testing...\n")
    
    # Test user accounts to create
    test_users = [
        {
            'username': 'apollo',
            'email': '<EMAIL>',
            'password': '2587',
            'role': 'user',
            'plan': 'FREE',
            'is_superuser': False,
            'description': 'Regular user with free plan'
        },
        {
            'username': 'peter',
            'email': '<EMAIL>',
            'password': '2587',
            'role': 'admin',
            'plan': None,
            'is_superuser': True,
            'description': 'Superuser/Admin account'
        },
        {
            'username': 'admin',
            'email': '<EMAIL>',
            'password': '2587',
            'role': 'admin',
            'plan': None,
            'is_superuser': True,
            'description': 'Alternative admin account'
        }
    ]
    
    for user_data in test_users:
        username = user_data['username']
        
        # Delete existing user if exists
        try:
            existing_user = User.objects.get(username=username)
            existing_user.delete()
            print(f"  🗑️  Deleted existing user: {username}")
        except User.DoesNotExist:
            pass
        
        # Create user
        if user_data['is_superuser']:
            user = User.objects.create_superuser(
                username=user_data['username'],
                email=user_data['email'],
                password=user_data['password']
            )
            print(f"  ✅ Created superuser: {username}")
        else:
            user = User.objects.create_user(
                username=user_data['username'],
                email=user_data['email'],
                password=user_data['password']
            )
            print(f"  ✅ Created user: {username}")
        
        # Create user profile
        profile = UserProfile.objects.create(
            user=user,
            role=user_data['role']
        )
        print(f"    📝 Created profile with role: {user_data['role']}")
        
        # Create subscription for non-admin users
        if user_data['plan'] and not user_data['is_superuser']:
            try:
                plan = Plan.objects.get(plan_type=user_data['plan'])
                
                subscription = Subscription.objects.create(
                    user=user,
                    plan=plan,
                    status='ACTIVE',
                    started_at=timezone.now(),
                    scans_this_month=0,
                )
                
                print(f"    💳 Created subscription: {plan.name}")
                
            except Plan.DoesNotExist:
                print(f"    ⚠️  Plan {user_data['plan']} not found. Run create_default_plans first.")
        
        print(f"    📋 Description: {user_data['description']}\n")
    
    # Display summary
    print("🎉 Test users created successfully!\n")
    
    print("Test Accounts:")
    print("=" * 50)
    
    for user_data in test_users:
        print(f"Username: {user_data['username']}")
        print(f"Password: {user_data['password']}")
        print(f"Role: {user_data['role']}")
        print(f"Type: {'Superuser' if user_data['is_superuser'] else 'Regular User'}")
        if user_data['plan']:
            print(f"Plan: {user_data['plan']}")
        print(f"Purpose: {user_data['description']}")
        print("-" * 30)
    
    print("\nTesting Scenarios:")
    print("=" * 50)
    print("1. Login as 'apollo' (password: 2587) - Test free plan limits")
    print("2. Login as 'peter' or 'admin' (password: 2587) - Test admin access")
    print("3. Test plan upgrade flow with apollo account")
    print("4. Test feature gating and middleware enforcement")
    print("5. Test Stripe billing integration")
    
    print("\nNext Steps:")
    print("=" * 50)
    print("1. Run: python manage.py create_default_plans")
    print("2. Test login with different accounts")
    print("3. Verify plan limits and feature access")
    print("4. Test billing and upgrade flows")
    
    print("\n✅ Ready for comprehensive privilege testing!")

if __name__ == '__main__':
    create_test_users()
