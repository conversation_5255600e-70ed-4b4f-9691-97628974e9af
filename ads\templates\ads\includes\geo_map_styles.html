<!-- jVectorMap Styles -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-jvectormap/2.0.5/jquery-jvectormap.min.css">

<style>
    /* Custom styles for the map */
    .jvectormap-container {
        width: 100%;
        height: 100%;
        border-radius: 8px;
        overflow: hidden;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    }
    
    .jvectormap-zoomin, .jvectormap-zoomout {
        background: linear-gradient(135deg, #1a237e, #3949ab);
        color: white;
        border-radius: 4px;
        width: 20px;
        height: 20px;
        line-height: 20px;
        text-align: center;
        font-weight: bold;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        cursor: pointer;
    }
    
    .jvectormap-zoomin:hover, .jvectormap-zoomout:hover {
        background: linear-gradient(135deg, #3949ab, #5c6bc0);
    }
    
    .jvectormap-zoomin {
        top: 10px;
    }
    
    .jvectormap-zoomout {
        top: 35px;
    }
    
    .jvectormap-label {
        background: linear-gradient(135deg, #1a237e, #3949ab);
        color: white;
        border: none;
        border-radius: 4px;
        padding: 5px 10px;
        font-size: 12px;
        font-weight: 500;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    }
    
    /* Map legend styles */
    .map-legend {
        position: absolute;
        bottom: 10px;
        right: 10px;
        background: white;
        border-radius: 4px;
        padding: 8px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        font-size: 12px;
        z-index: 1000;
    }
    
    .legend-item {
        display: flex;
        align-items: center;
        margin-bottom: 5px;
    }
    
    .legend-color {
        width: 15px;
        height: 15px;
        margin-right: 5px;
        border-radius: 2px;
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
        .jvectormap-container {
            height: 300px;
        }
        
        .map-legend {
            position: relative;
            bottom: auto;
            right: auto;
            margin-top: 10px;
            width: 100%;
        }
        
        .legend-items {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
        }
        
        .legend-item {
            width: 48%;
            margin-bottom: 5px;
        }
    }
</style>
