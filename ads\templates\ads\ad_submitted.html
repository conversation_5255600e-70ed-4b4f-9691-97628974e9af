{% extends 'base.html' %}
{% load static %}

{% block title %}Ad Created Successfully - Enterprise QR{% endblock %}

{% block extra_css %}
<!-- Include common ads CSS -->
{% include 'ads/includes/ads_common_css.html' %}
<style>
    .success-container {
        padding: 2rem 0;
        min-height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .success-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
        padding: 3rem;
        position: relative;
        overflow: hidden;
        max-width: 900px;
        width: 100%;
        margin: 0 auto;
        animation: successSlideIn 0.8s ease-out;
    }

    @keyframes successSlideIn {
        from {
            opacity: 0;
            transform: translateY(50px) scale(0.9);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    .success-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 8px;
        background: linear-gradient(90deg, #28a745, #20c997, #17a2b8);
        animation: shimmer 2s infinite;
    }

    @keyframes shimmer {
        0% { background-position: -200% 0; }
        100% { background-position: 200% 0; }
    }

    .success-icon {
        width: 120px;
        height: 120px;
        background: linear-gradient(135deg, #28a745, #20c997);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 2rem;
        color: white;
        font-size: 3.5rem;
        animation: successBounce 1s ease-out;
        box-shadow: 0 15px 40px rgba(40, 167, 69, 0.4);
    }

    @keyframes successBounce {
        0% {
            opacity: 0;
            transform: scale(0) rotate(-180deg);
        }
        50% {
            opacity: 1;
            transform: scale(1.2) rotate(0deg);
        }
        100% {
            opacity: 1;
            transform: scale(1) rotate(0deg);
        }
    }

    .success-title {
        font-size: 3rem;
        font-weight: 800;
        color: #2c3e50;
        margin-bottom: 1rem;
        text-align: center;
        animation: fadeInUp 0.8s ease-out 0.3s both;
    }

    .success-message {
        font-size: 1.4rem;
        color: #495057;
        margin-bottom: 3rem;
        text-align: center;
        font-weight: 500;
        line-height: 1.6;
        animation: fadeInUp 0.8s ease-out 0.5s both;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Startup Metrics Section */
    .startup-metrics {
        display: flex;
        justify-content: space-around;
        margin: 3rem 0;
        padding: 2.5rem;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 20px;
        animation: fadeInUp 0.8s ease-out 0.7s both;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .metric-item {
        text-align: center;
        flex: 1;
        padding: 0 1.5rem;
        position: relative;
    }

    .metric-item:not(:last-child)::after {
        content: '';
        position: absolute;
        right: 0;
        top: 20%;
        height: 60%;
        width: 2px;
        background: linear-gradient(to bottom, transparent, #dee2e6, transparent);
    }

    .metric-number {
        font-size: 3rem;
        font-weight: 900;
        background: linear-gradient(135deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        display: block;
        margin-bottom: 0.5rem;
        animation: countUp 2s ease-out 1s both;
    }

    .metric-label {
        font-size: 1.1rem;
        color: #6c757d;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    @keyframes countUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .ad-details {
        background-color: rgba(26, 35, 126, 0.05);
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        text-align: left;
    }

    .ad-detail-item {
        display: flex;
        margin-bottom: 1rem;
    }

    .ad-detail-label {
        font-weight: 600;
        color: #1a237e;
        width: 150px;
        flex-shrink: 0;
    }

    .ad-detail-value {
        color: #475569;
    }

    .status-badge {
        display: inline-block;
        padding: 0.35rem 0.75rem;
        border-radius: 50px;
        font-size: 0.875rem;
        font-weight: 600;
        background-color: rgba(255, 193, 7, 0.1);
        color: #ffc107;
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-top: 2rem;
    }

    .btn-primary {
        background: linear-gradient(135deg, #1a237e, #3949ab);
        border: none;
        box-shadow: 0 4px 10px rgba(26, 35, 126, 0.2);
        transition: all 0.3s ease;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #3949ab, #1a237e);
        box-shadow: 0 6px 15px rgba(26, 35, 126, 0.3);
        transform: translateY(-2px);
    }

    .btn-outline-primary {
        color: #1a237e;
        border: 2px solid #1a237e;
        background: transparent;
        transition: all 0.3s ease;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
    }

    .btn-outline-primary:hover {
        background-color: rgba(26, 35, 126, 0.05);
        transform: translateY(-2px);
    }

    .next-steps {
        margin-top: 3rem;
        text-align: left;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    .next-steps-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #1a237e;
        margin-bottom: 1rem;
        text-align: center;
    }

    .step-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 1.5rem;
    }

    .step-number {
        width: 30px;
        height: 30px;
        background: linear-gradient(135deg, #1a237e, #3949ab);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 700;
        margin-right: 1rem;
        flex-shrink: 0;
    }

    .step-content {
        flex: 1;
    }

    .step-title {
        font-weight: 700;
        color: #1a237e;
        margin-bottom: 0.5rem;
    }

    .step-description {
        color: #475569;
    }

    /* Enhanced Action Buttons */
    .action-buttons {
        display: flex;
        gap: 1.5rem;
        justify-content: center;
        margin-top: 3rem;
        animation: fadeInUp 0.8s ease-out 1.2s both;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        transition: all 0.3s ease;
        font-weight: 600;
        padding: 1rem 2rem;
        border-radius: 12px;
        font-size: 1.1rem;
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #764ba2, #667eea);
        box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        transform: translateY(-3px);
    }

    .btn-outline-primary {
        color: #667eea;
        border: 2px solid #667eea;
        background: transparent;
        transition: all 0.3s ease;
        font-weight: 600;
        padding: 1rem 2rem;
        border-radius: 12px;
        font-size: 1.1rem;
    }

    .btn-outline-primary:hover {
        background: rgba(102, 126, 234, 0.1);
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
    }

    /* Mobile Responsiveness */
    @media (max-width: 768px) {
        .success-container {
            padding: 1rem 0;
        }

        .success-card {
            padding: 2rem 1.5rem;
            margin: 1rem;
        }

        .success-title {
            font-size: 2.2rem;
        }

        .success-message {
            font-size: 1.2rem;
        }

        .startup-metrics {
            flex-direction: column;
            gap: 2rem;
            padding: 2rem 1rem;
        }

        .metric-item:not(:last-child)::after {
            display: none;
        }

        .metric-number {
            font-size: 2.5rem;
        }

        .action-buttons {
            flex-direction: column;
            gap: 1rem;
        }

        .btn-primary, .btn-outline-primary {
            width: 100%;
            padding: 1rem;
        }

        .next-steps {
            margin-top: 2rem;
        }
    }

    /* Prevent page from disappearing */
    .success-container {
        position: relative;
        z-index: 1;
    }
</style>
{% endblock %}

{% block content %}
<div class="success-container">
    <div class="container">
        <div class="success-card">
            <div class="success-icon">
                <i class="fas fa-rocket"></i>
            </div>

            <h1 class="success-title">🎉 Ad Created Successfully!</h1>
            <p class="success-message">
                Your advertisement is now live and ready to drive results!
                Join hundreds of growing businesses who trust our AI-powered platform.
            </p>

            <!-- Startup Metrics -->
            <div class="startup-metrics">
                <div class="metric-item">
                    <span class="metric-number">1.2K+</span>
                    <div class="metric-label">Ads Created</div>
                </div>
                <div class="metric-item">
                    <span class="metric-number">92%</span>
                    <div class="metric-label">Success Rate</div>
                </div>
                <div class="metric-item">
                    <span class="metric-number">2.8x</span>
                    <div class="metric-label">Average ROI</div>
                </div>
            </div>

            <div class="ad-details">
                <div class="ad-detail-item">
                    <div class="ad-detail-label">Ad Title:</div>
                    <div class="ad-detail-value">{{ ad.title }}</div>
                </div>
                <div class="ad-detail-item">
                    <div class="ad-detail-label">Ad Type:</div>
                    <div class="ad-detail-value">{{ ad.ad_type.name }}</div>
                </div>
                <div class="ad-detail-item">
                    <div class="ad-detail-label">Created On:</div>
                    <div class="ad-detail-value">{{ ad.updated_at|date:"F j, Y, g:i a" }}</div>
                </div>
                <div class="ad-detail-item">
                    <div class="ad-detail-label">Status:</div>
                    <div class="ad-detail-value">
                        <span class="status-badge">Ready to Launch</span>
                    </div>
                </div>
            </div>

            <div class="next-steps">
                <h2 class="next-steps-title">🚀 Your Ad is Now Working For You!</h2>

                <div class="step-item">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <div class="step-title">Instant Visibility</div>
                        <div class="step-description">
                            Your ad is now live and reaching your target audience across our premium network.
                            Expect to see initial results within the first few hours.
                        </div>
                    </div>
                </div>

                <div class="step-item">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <div class="step-title">Real-Time Analytics</div>
                        <div class="step-description">
                            Track your ad performance with detailed analytics including views, clicks, and conversions.
                            Monitor your ROI in real-time through your dashboard.
                        </div>
                    </div>
                </div>

                <div class="step-item">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <div class="step-title">AI Optimization</div>
                        <div class="step-description">
                            Our AI continuously optimizes your ad placement and targeting to maximize performance
                            and deliver the best possible results for your investment.
                        </div>
                    </div>
                </div>

                <div class="step-item">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <div class="step-title">Scale Your Success</div>
                        <div class="step-description">
                            Based on performance data, we'll recommend opportunities to scale your campaigns
                            and reach even more potential customers.
                        </div>
                    </div>
                </div>
            </div>

            <div class="action-buttons">
                <a href="{% url 'ads:dashboard' %}" class="btn btn-outline-primary">
                    <i class="fas fa-chart-line me-2"></i> View Analytics
                </a>
                <a href="{% url 'ads:ad_detail' ad.slug %}" class="btn btn-primary">
                    <i class="fas fa-rocket me-2"></i> Manage Campaign
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Success Page Stabilization Script -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('SUCCESS PAGE: Initializing stabilization script');

    // Prevent any automatic redirects
    window.onbeforeunload = null;

    // Override any conflicting navigation functions
    const originalLocationHref = window.location.href;

    // Prevent form submissions on this page
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            console.log('SUCCESS PAGE: Preventing form submission');
            e.preventDefault();
            e.stopPropagation();
            return false;
        });
    });

    // Prevent any automatic navigation
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;

    history.pushState = function() {
        console.log('SUCCESS PAGE: Preventing history.pushState');
        return false;
    };

    history.replaceState = function() {
        console.log('SUCCESS PAGE: Preventing history.replaceState');
        return false;
    };

    // Override window.location.href setter temporarily
    let locationOverridden = false;
    Object.defineProperty(window.location, 'href', {
        get: function() {
            return originalLocationHref;
        },
        set: function(value) {
            if (!locationOverridden && value !== originalLocationHref) {
                console.log('SUCCESS PAGE: Preventing automatic redirect to:', value);
                console.log('SUCCESS PAGE: Staying on success page');
                return false;
            }
            // Allow manual navigation from buttons
            window.location.assign(value);
        }
    });

    // Allow manual navigation from action buttons
    const actionButtons = document.querySelectorAll('.action-buttons a');
    actionButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            console.log('SUCCESS PAGE: Manual navigation allowed from action button');
            locationOverridden = true;
            // Allow this navigation
        });
    });

    // Stabilize the page content
    const successContainer = document.querySelector('.success-container');
    if (successContainer) {
        successContainer.style.position = 'relative';
        successContainer.style.zIndex = '9999';
        successContainer.style.display = 'block';
        successContainer.style.visibility = 'visible';
        successContainer.style.opacity = '1';

        console.log('SUCCESS PAGE: Page content stabilized');
    }

    // Prevent any scripts from hiding the success page
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                const target = mutation.target;
                if (target.classList.contains('success-container') ||
                    target.classList.contains('success-card')) {
                    // Ensure success elements remain visible
                    if (target.style.display === 'none' ||
                        target.style.visibility === 'hidden' ||
                        target.style.opacity === '0') {
                        console.log('SUCCESS PAGE: Preventing element from being hidden');
                        target.style.display = 'block';
                        target.style.visibility = 'visible';
                        target.style.opacity = '1';
                    }
                }
            }
        });
    });

    // Observe the success container for changes
    if (successContainer) {
        observer.observe(successContainer, {
            attributes: true,
            subtree: true,
            attributeFilter: ['style', 'class']
        });
    }

    console.log('SUCCESS PAGE: Stabilization complete - page should remain visible');
});
</script>
{% endblock %}
