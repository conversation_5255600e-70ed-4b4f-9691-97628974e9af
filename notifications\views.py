from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_POST
from django.core.paginator import Paginator
from django.contrib import messages
from django.urls import reverse
from django.db.models import Q

from .models import Notification
from .services import NotificationService


@login_required
def notification_list(request):
    """
    View for listing user notifications
    """
    # Get filter parameters
    category = request.GET.get('category', '')
    status = request.GET.get('status', '')
    show_archived = request.GET.get('show_archived', '').lower() == 'true'
    format_json = request.GET.get('format') == 'json' or request.headers.get('x-requested-with') == 'XMLHttpRequest'

    # Build query
    query = Q(user=request.user, is_deleted=False)

    # Handle status filter
    if status == 'unread':
        query &= Q(is_read=False)
    elif status == 'read':
        query &= Q(is_read=True)
    elif status == 'archived':
        query &= Q(is_archived=True)
    elif status == 'muted':
        query &= Q(is_muted=True)
    else:
        # Default behavior: exclude archived notifications unless explicitly requested
        if not show_archived:
            query &= Q(is_archived=False)

    if category:
        query &= Q(category=category)

    # Get notifications
    notifications = Notification.objects.filter(query).order_by('-created_at')

    # Handle JSON format for dropdown
    if format_json:
        # Limit to 5 most recent notifications for dropdown
        recent_notifications = notifications[:5]

        # Format notifications for JSON response
        notifications_data = []
        for notification in recent_notifications:
            notifications_data.append({
                'id': notification.id,
                'title': notification.title,
                'message': notification.message[:100] + ('...' if len(notification.message) > 100 else ''),
                'notification_type': notification.notification_type,
                'category': notification.category,
                'is_read': notification.is_read,
                'time_ago': notification.time_ago,
                'url': notification.get_action_url,
            })

        # Return JSON response
        return JsonResponse({
            'notifications': notifications_data,
            'unread_count': Notification.objects.filter(user=request.user, is_read=False, is_deleted=False).count(),
            'total_count': notifications.count(),
        })

    # Paginate results for HTML view
    paginator = Paginator(notifications, 10)
    page_number = request.GET.get('page', 1)
    page_obj = paginator.get_page(page_number)

    # Get notification categories for filter
    categories = Notification.NotificationCategory.choices

    context = {
        'page_obj': page_obj,
        'categories': categories,
        'selected_category': category,
        'selected_status': status,
        'show_archived': show_archived,
        'unread_count': Notification.objects.filter(user=request.user, is_read=False, is_deleted=False, is_archived=False).count(),
        'archived_count': Notification.objects.filter(user=request.user, is_archived=True, is_deleted=False).count(),
        'muted_count': Notification.objects.filter(user=request.user, is_muted=True, is_deleted=False).count(),
    }

    return render(request, 'notifications/notification_list.html', context)


@login_required
def notification_detail(request, pk):
    """
    View for displaying notification details
    """
    notification = get_object_or_404(Notification, pk=pk, user=request.user)

    # Mark as read if not already
    if not notification.is_read:
        notification.mark_as_read()

    # If notification has action URL, redirect to it
    if notification.action_url:
        return redirect(notification.action_url)

    context = {
        'notification': notification,
    }

    return render(request, 'notifications/notification_detail.html', context)


@login_required
@require_POST
def mark_as_read(request, pk):
    """
    Mark a notification as read
    """
    notification = get_object_or_404(Notification, pk=pk, user=request.user)
    notification.mark_as_read()

    if request.headers.get('x-requested-with') == 'XMLHttpRequest':
        return JsonResponse({'status': 'success'})

    messages.success(request, 'Notification marked as read.')
    return redirect('notifications:notification_list')


@login_required
@require_POST
def mark_as_unread(request, pk):
    """
    Mark a notification as unread
    """
    notification = get_object_or_404(Notification, pk=pk, user=request.user)
    notification.mark_as_unread()

    if request.headers.get('x-requested-with') == 'XMLHttpRequest':
        return JsonResponse({'status': 'success'})

    messages.success(request, 'Notification marked as unread.')
    return redirect('notifications:notification_list')


@login_required
@require_POST
def delete_notification(request, pk):
    """
    Soft delete a notification
    """
    notification = get_object_or_404(Notification, pk=pk, user=request.user)
    notification.soft_delete()

    if request.headers.get('x-requested-with') == 'XMLHttpRequest':
        unread_count = Notification.objects.filter(user=request.user, is_read=False, is_deleted=False, is_archived=False).count()
        return JsonResponse({'status': 'success', 'unread_count': unread_count})

    messages.success(request, 'Notification deleted.')
    return redirect('notifications:notification_list')


@login_required
@require_POST
def archive_notification(request, pk):
    """
    Archive a notification for 30 days before deletion
    """
    notification = get_object_or_404(Notification, pk=pk, user=request.user)
    notification.archive()

    if request.headers.get('x-requested-with') == 'XMLHttpRequest':
        unread_count = Notification.objects.filter(user=request.user, is_read=False, is_deleted=False, is_archived=False).count()
        return JsonResponse({
            'status': 'success',
            'unread_count': unread_count,
            'message': 'Notification archived. It will be automatically deleted after 30 days.'
        })

    messages.success(request, 'Notification archived. It will be automatically deleted after 30 days.')
    return redirect('notifications:notification_list')


@login_required
@require_POST
def mark_all_as_read(request):
    """
    Mark all notifications as read
    """
    count = NotificationService.mark_all_as_read(request.user)

    if request.headers.get('x-requested-with') == 'XMLHttpRequest':
        return JsonResponse({'status': 'success', 'count': count})

    messages.success(request, f'{count} notifications marked as read.')
    return redirect('notifications:notification_list')


@login_required
def notification_count(request):
    """
    Get unread notification count (for AJAX requests)
    """
    count = Notification.objects.filter(user=request.user, is_read=False, is_deleted=False, is_archived=False).count()
    return JsonResponse({'count': count})


@login_required
@require_POST
def mute_notification(request, pk):
    """
    Mute a notification
    """
    notification = get_object_or_404(Notification, pk=pk, user=request.user)
    notification.mute()

    if request.headers.get('x-requested-with') == 'XMLHttpRequest':
        unread_count = Notification.objects.filter(user=request.user, is_read=False, is_deleted=False, is_archived=False).count()
        return JsonResponse({
            'status': 'success',
            'unread_count': unread_count,
            'message': 'Notification muted. You will no longer receive notifications of this type.'
        })

    messages.success(request, 'Notification muted. You will no longer receive notifications of this type.')
    return redirect('notifications:notification_list')


@login_required
@require_POST
def mute_category(request):
    """
    Mute all notifications of a specific category
    """
    category = request.POST.get('category')

    if not category:
        if request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse({'status': 'error', 'message': 'Category is required'}, status=400)

        messages.error(request, 'Category is required')
        return redirect('notifications:notification_list')

    count = Notification.mute_category(request.user, category)

    if request.headers.get('x-requested-with') == 'XMLHttpRequest':
        unread_count = Notification.objects.filter(user=request.user, is_read=False, is_deleted=False, is_archived=False).count()
        return JsonResponse({
            'status': 'success',
            'unread_count': unread_count,
            'message': f'Successfully muted {count} notifications of category "{category}". You will no longer receive notifications of this type.',
            'count': count
        })

    messages.success(request, f'Successfully muted {count} notifications of category "{category}". You will no longer receive notifications of this type.')
    return redirect('notifications:notification_list')



