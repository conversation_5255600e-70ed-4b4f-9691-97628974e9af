"""
Celery configuration for the QR Code Generator project
"""
import os
from celery import Celery
from celery.schedules import crontab

# Set the default Django settings module for the 'celery' program.
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'qrcode_project.settings')

app = Celery('qrcode_project')

# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
app.config_from_object('django.conf:settings', namespace='CELERY')

# Load task modules from all registered Django app configs.
app.autodiscover_tasks()

# Define periodic tasks
app.conf.beat_schedule = {
    'clear-expired-cache-every-hour': {
        'task': 'ai_services.tasks.clear_expired_cache',
        'schedule': crontab(minute=0, hour='*/1'),  # Run every hour
    },
}

@app.task(bind=True, ignore_result=True)
def debug_task(self):
    """Debug task to verify Ce<PERSON>y is working"""
    print(f'Request: {self.request!r}')
