{% comment %}
    Enterprise-Grade Premium Header Ad Display Template

    This template is specifically designed for the Premium Header location with a sleek,
    corporate-grade design featuring:
    - Image on the right side
    - Ad title prominently displayed
    - Truncated content (10 characters visible)
    - Sleek, corporate CTA button
{% endcomment %}

{% load static %}

{% if ads %}
    <div class="premium-header-ad-container">
        <div class="premium-section-title">
            <span class="premium-icon"><i class="fas fa-crown"></i></span>
            <span class="premium-text" data-text="Premium Showcase">Premium Showcase</span>
        </div>
        <div class="premium-ad-carousel">
            <div class="premium-ad-carousel-inner">
                {% for ad in ads %}
                <div class="premium-ad-item {% if forloop.first %}active{% endif %}" data-ad-id="{{ ad.id }}" data-ad-type="{{ ad.ad_type.name }}" data-ad-location="Premium Header">
                    <div class="premium-ad-content">
                        <div class="premium-ad-text">
                            <div class="premium-ad-badge">
                                <span>Premium Partner</span>
                            </div>
                            <h3 class="premium-ad-title">{{ ad.title }}</h3>
                            <p class="premium-ad-description">{{ ad.content|truncatechars:60 }}</p>
                            {% if ad.cta_link %}
                            <a href="{{ ad.cta_link }}" target="_blank" class="premium-ad-cta" rel="noopener" onclick="trackAdClick('{{ ad.id }}')">
                                <div class="premium-ad-cta-content">
                                    <span>Learn More</span>
                                    <i class="fas fa-arrow-right"></i>
                                </div>
                                {% if ads|length > 1 %}
                                <div class="premium-ad-cta-indicators">
                                    {% for ad in ads %}
                                    <span class="premium-ad-cta-dot {% if forloop.counter0 == forloop.parentloop.counter0 %}active{% endif %}"></span>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </a>
                            {% else %}
                            <div class="premium-ad-cta premium-ad-cta-disabled">
                                <div class="premium-ad-cta-content">
                                    <span>No Link</span>
                                    <i class="fas fa-link-slash"></i>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                        <div class="premium-ad-media">
                            {% if ad.media and ad.media.name %}
                                <div class="premium-ad-image-wrapper">
                                    <img src="/media/{{ ad.media }}" alt="{{ ad.title }}" class="premium-ad-image">
                                </div>
                            {% else %}
                                <div class="premium-ad-image-placeholder">
                                    <i class="fas fa-ad"></i>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="premium-ad-sponsor-label">Sponsored</div>
                </div>
                {% endfor %}
            </div>

            {% if ads|length > 1 %}
            <!-- Original indicators hidden, now shown in CTA button -->
            <div class="premium-ad-carousel-indicators">
                {% for ad in ads %}
                <button class="premium-ad-indicator {% if forloop.first %}active{% endif %}" data-slide-to="{{ forloop.counter0 }}"></button>
                {% endfor %}
            </div>
            {% endif %}
        </div>
        <hr class="golden-hr">
    </div>
{% endif %}

<!-- Ad tracking and carousel script -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize carousel
        initPremiumAdCarousel();

        // Track impressions for visible ads
        {% if ads %}
            {% for ad in ads %}
                {% if forloop.first %}
                    trackAdImpression('{{ ad.id }}');
                {% endif %}
            {% endfor %}
        {% endif %}

        // Fix carousel dots size
        fixCarouselDots();

        // Fix carousel dots size after a short delay
        setTimeout(fixCarouselDots, 500);

        // Fix carousel dots size when window is resized
        window.addEventListener('resize', fixCarouselDots);
    });

    // Function to fix carousel dots size
    function fixCarouselDots() {
        const indicators = document.querySelectorAll('.premium-ad-indicator');
        if (indicators.length > 0) {
            // Apply fixed size to all indicators
            indicators.forEach(function(dot) {
                // Force consistent size
                dot.style.width = '6px';
                dot.style.height = '6px';
                dot.style.minWidth = '6px';
                dot.style.minHeight = '6px';
                dot.style.maxWidth = '6px';
                dot.style.maxHeight = '6px';

                // Apply proper transition
                dot.style.transition = 'all 0.3s ease';

                // Fix any transform issues
                if (dot.classList.contains('active')) {
                    dot.style.transform = 'scale(1.2)';
                } else {
                    dot.style.transform = 'scale(1)';
                }
            });

            // Apply responsive sizing based on screen width
            if (window.innerWidth <= 576) {
                indicators.forEach(function(dot) {
                    dot.style.width = '4px';
                    dot.style.height = '4px';
                    dot.style.minWidth = '4px';
                    dot.style.minHeight = '4px';
                    dot.style.maxWidth = '4px';
                    dot.style.maxHeight = '4px';

                    if (dot.classList.contains('active')) {
                        dot.style.transform = 'scale(1.1)';
                    }
                });

                // Fix indicator container position on small screens
                const indicatorContainer = document.querySelector('.premium-ad-carousel-indicators');
                if (indicatorContainer) {
                    indicatorContainer.style.bottom = '35px';
                    indicatorContainer.style.right = '10px';
                    indicatorContainer.style.left = 'auto';
                    indicatorContainer.style.transform = 'none';
                    indicatorContainer.style.zIndex = '20';
                    indicatorContainer.style.background = 'rgba(255,255,255,0.6)';
                    indicatorContainer.style.borderRadius = '10px';
                }

                // Ensure CTA button is not overlapped
                const ctaButtons = document.querySelectorAll('.premium-ad-cta');
                ctaButtons.forEach(function(btn) {
                    btn.style.marginBottom = '5px';
                    btn.style.position = 'relative';
                    btn.style.zIndex = '15';
                });
            } else if (window.innerWidth <= 768) {
                indicators.forEach(function(dot) {
                    dot.style.width = '5px';
                    dot.style.height = '5px';
                    dot.style.minWidth = '5px';
                    dot.style.minHeight = '5px';
                    dot.style.maxWidth = '5px';
                    dot.style.maxHeight = '5px';
                });

                // Adjust CTA button on medium screens
                const ctaButtons = document.querySelectorAll('.premium-ad-cta');
                ctaButtons.forEach(function(btn) {
                    btn.style.marginBottom = '25px';
                });
            }
        }
    }

    // Initialize the premium ad carousel
    function initPremiumAdCarousel() {
        console.log("Initializing premium ad carousel");
        const carousel = document.querySelector('.premium-ad-carousel');
        if (!carousel) {
            console.log("Carousel element not found");
            return;
        }

        const items = carousel.querySelectorAll('.premium-ad-item');
        console.log(`Found ${items.length} carousel items`);

        if (items.length <= 1) {
            console.log("Only one item found, no carousel needed");
            return; // No need for carousel with only one item
        }

        const indicators = carousel.querySelectorAll('.premium-ad-indicator');

        let currentIndex = 0;
        let interval;

        // Make sure all items except the first one are hidden initially
        items.forEach((item, index) => {
            if (index === 0) {
                item.classList.add('active');
            } else {
                item.classList.remove('active');
            }
        });

        // Make sure all indicators except the first one are inactive initially
        indicators.forEach((indicator, index) => {
            if (index === 0) {
                indicator.classList.add('active');
            } else {
                indicator.classList.remove('active');
            }
        });

        // Start automatic rotation with smooth transitions
        startAutoRotation();

        // Event listeners for indicators
        indicators.forEach((indicator, index) => {
            indicator.addEventListener('click', () => {
                console.log(`Indicator ${index} clicked`);
                goToSlide(index);
                resetAutoRotation();
            });
        });

        // Event listeners for CTA dots
        const ctaDots = document.querySelectorAll('.premium-ad-cta-dot');
        ctaDots.forEach((dot, index) => {
            dot.addEventListener('click', (e) => {
                e.preventDefault(); // Prevent the CTA link from being followed
                e.stopPropagation(); // Prevent event bubbling
                console.log(`CTA dot ${index} clicked`);
                goToSlide(index);
                resetAutoRotation();
            });
        });

        // Pause rotation on hover
        carousel.addEventListener('mouseenter', () => {
            console.log("Mouse entered carousel, pausing rotation");
            clearInterval(interval);
        });

        // Resume rotation on mouse leave
        carousel.addEventListener('mouseleave', () => {
            console.log("Mouse left carousel, resuming rotation");
            startAutoRotation();
        });

        // Function to go to a specific slide
        function goToSlide(index) {
            // Handle wrapping
            if (index < 0) {
                index = items.length - 1;
            } else if (index >= items.length) {
                index = 0;
            }

            console.log(`Going to slide ${index}`);

            // Add fade-out class to current item before removing active class
            items[currentIndex].classList.add('fade-out');

            // Remove active class from current indicators
            if (indicators.length) {
                indicators[currentIndex].classList.remove('active');
            }

            // Update CTA dots
            const ctaDots = document.querySelectorAll('.premium-ad-cta-dot');
            if (ctaDots.length) {
                ctaDots.forEach((dot, i) => {
                    if (i === index) {
                        dot.classList.add('active');
                    } else {
                        dot.classList.remove('active');
                    }
                });
            }

            // Wait for transition before changing slides
            setTimeout(() => {
                // Remove active and fade-out classes from current item
                items[currentIndex].classList.remove('active');
                items[currentIndex].classList.remove('fade-out');

                // Add active class to new item
                items[index].classList.add('active');

                // Add active class to new indicator
                if (indicators.length) {
                    indicators[index].classList.add('active');
                }
            }, 300); // Short delay for smoother transition

            // Track impression for newly visible ad
            trackAdImpression(items[index].getAttribute('data-ad-id'));

            // Update current index
            currentIndex = index;

            // Fix carousel dots size after slide change
            setTimeout(fixCarouselDots, 100);
        }

        // Function to start auto rotation with smooth transitions
        function startAutoRotation() {
            // Clear any existing interval first
            if (interval) {
                clearInterval(interval);
            }

            // Use a longer interval for a more elegant, unhurried rotation
            interval = setInterval(() => {
                console.log("Auto-rotating to next slide");

                // Add a smooth transition class before changing slides
                items.forEach(item => {
                    item.style.transition = "opacity 1.2s ease, visibility 1.2s ease, transform 1.2s ease";
                });

                goToSlide(currentIndex + 1);
            }, 7000); // Rotate every 7 seconds for a more elegant pace

            console.log("Auto rotation started with enhanced transitions");
        }

        // Function to reset auto rotation
        function resetAutoRotation() {
            clearInterval(interval);
            startAutoRotation();
        }

        // Force a rotation after a short delay to ensure everything is initialized
        setTimeout(() => {
            console.log("Forcing initial rotation");
            goToSlide(1); // Go to the second slide
            setTimeout(() => {
                goToSlide(0); // Then back to the first slide
            }, 100);
        }, 500);
    }

    // Function to track ad impressions
    function trackAdImpression(adId) {
        if (!adId) return;

        console.log('Tracking impression for ad ID:', adId);

        // Get device info
        const deviceInfo = {
            type: /Mobi|Android/i.test(navigator.userAgent) ? 'mobile' : 'desktop',
            userAgent: navigator.userAgent
        };

        // Get basic location info (more detailed would require geolocation API)
        const locationInfo = {
            city: 'Unknown', // Default value
            country: 'Unknown' // Default value
        };

        fetch('/ads/api/track-impression/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            },
            body: JSON.stringify({
                ad_id: adId,
                device_info: deviceInfo,
                location_info: locationInfo
            })
        })
        .then(response => response.json())
        .then(data => {
            console.log('Impression tracked:', data);
        })
        .catch(error => console.error('Error tracking impression:', error));
    }

    // Function to track ad clicks
    function trackAdClick(adId) {
        if (!adId) return;

        console.log('Tracking click for ad ID:', adId);

        // Get device info
        const deviceInfo = {
            type: /Mobi|Android/i.test(navigator.userAgent) ? 'mobile' : 'desktop',
            userAgent: navigator.userAgent
        };

        // Get basic location info (more detailed would require geolocation API)
        const locationInfo = {
            city: 'Unknown', // Default value
            country: 'Unknown' // Default value
        };

        fetch('/ads/api/track-click/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            },
            body: JSON.stringify({
                ad_id: adId,
                device_info: deviceInfo,
                location_info: locationInfo
            })
        })
        .then(response => response.json())
        .then(data => {
            console.log('Click tracked:', data);
        })
        .catch(error => console.error('Error tracking click:', error));

        // Return true to allow the link to be followed
        return true;
    }

    // Helper function to get CSRF token
    function getCsrfToken() {
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'csrftoken') {
                return value;
            }
        }
        return '';
    }
</script>

<style>
    /* Premium Header Ad Container */
    .premium-header-ad-container {
        max-width: 1254px; /* Increased by 10% from 1140px */
        margin: 0 auto;
        padding: 0 25px; /* Add padding to ensure arrows are visible */
        position: relative;
        width: 90%; /* Reduced from 110% to center properly */
        left: 0; /* Reset left position */
        right: 0; /* Reset right position */
    }

    /* Premium Section Title */
    .premium-section-title {
        text-align: center;
        margin-bottom: 20px;
        margin-top: 15px;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12px;
        padding: 5px 0;
    }

    .premium-section-title:before,
    .premium-section-title:after {
        content: '';
        height: 2px;
        background: linear-gradient(to right,
            rgba(184, 134, 11, 0),
            rgba(218, 165, 32, 0.4) 20%,
            rgba(255, 215, 0, 0.8) 50%,
            rgba(218, 165, 32, 0.4) 80%,
            rgba(184, 134, 11, 0) 100%);
        flex: 1;
        box-shadow: 0 1px 3px rgba(255, 215, 0, 0.2);
    }

    .premium-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        background: linear-gradient(135deg, #ffd700 0%, #b8860b 100%);
        border-radius: 50%;
        color: #5d4037;
        font-size: 12px;
        box-shadow: 0 4px 10px rgba(184, 134, 11, 0.5), 0 0 20px rgba(255, 215, 0, 0.3);
        position: relative;
        overflow: hidden;
        border: 1px solid rgba(255, 255, 255, 0.5);
        transform: translateY(-2px);
        z-index: 5;
    }

    .premium-icon:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 50%;
        background: linear-gradient(to bottom, rgba(255,255,255,0.8), rgba(255,255,255,0));
    }

    .premium-text {
        font-size: 16px;
        font-weight: 700;
        background: linear-gradient(135deg, #ffd700 0%, #b8860b 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
        text-transform: uppercase;
        letter-spacing: 2px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        position: relative;
        padding: 0 10px;
        transform: translateY(-2px);
    }

    /* Add glossy effect to the text */
    .premium-text::after {
        content: attr(data-text);
        position: absolute;
        left: 0;
        top: 0;
        background: linear-gradient(to bottom, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0) 50%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
        z-index: 2;
        pointer-events: none;
        padding: 0 10px;
    }

    /* Add 3D effect to the premium title */
    .premium-section-title::before {
        animation: shimmer 2s infinite linear;
    }

    .premium-section-title::after {
        animation: shimmer 2s infinite linear reverse;
    }

    @keyframes shimmer {
        0% { background-position: -100% 0; }
        100% { background-position: 100% 0; }
    }

    /* Premium Ad Carousel */
    .premium-ad-carousel {
        position: relative;
        overflow: hidden;
        border-radius: 16px;
        box-shadow: 0 15px 40px rgba(0,0,0,0.15), 0 5px 15px rgba(0,0,0,0.08), 0 0 30px rgba(255, 215, 0, 0.1);
        margin: 20px 0 25px;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border: 1px solid rgba(255, 215, 0, 0.3);
        transform: translateZ(0);
        transition: all 0.5s ease;
    }

    .premium-ad-carousel:hover {
        box-shadow: 0 20px 50px rgba(0,0,0,0.2), 0 8px 20px rgba(0,0,0,0.1), 0 0 40px rgba(255, 215, 0, 0.15);
        transform: translateY(-5px) translateZ(0);
    }

    /* Add a subtle golden glow around the carousel */
    .premium-ad-carousel::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(184, 134, 11, 0.2));
        border-radius: 18px;
        z-index: -1;
        filter: blur(8px);
        opacity: 0;
        transition: opacity 0.5s ease;
    }

    .premium-ad-carousel:hover::before {
        opacity: 1;
    }

    .premium-ad-carousel-inner {
        display: block; /* Changed from flex to block for better item positioning */
        position: relative;
    }

    /* Premium Ad Item */
    .premium-ad-item {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        transition: opacity 1.2s ease, visibility 1.2s ease, transform 1.2s ease;
        border: none;
        opacity: 0;
        visibility: hidden;
        z-index: 1;
        overflow: hidden;
        transform: translateX(30px); /* Start slightly off to the right */
    }

    /* Glossy overlay effect */
    .premium-ad-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 40%;
        background: linear-gradient(to bottom, rgba(255,255,255,0.4), rgba(255,255,255,0));
        z-index: 3;
        pointer-events: none;
    }

    /* Subtle bottom shadow */
    .premium-ad-item::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 20%;
        background: linear-gradient(to top, rgba(0,0,0,0.03), rgba(0,0,0,0));
        z-index: 3;
        pointer-events: none;
    }

    /* First item needs to establish the height */
    .premium-ad-item:first-child {
        position: relative;
    }

    .premium-ad-item.active {
        opacity: 1;
        visibility: visible;
        z-index: 2;
        transform: translateX(0) translateZ(0);
    }

    /* Add a fade-out effect for items that are becoming inactive */
    .premium-ad-item.fade-out {
        opacity: 0;
        transform: translateX(-30px) translateZ(0);
    }

    .premium-ad-item:hover {
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    }

    /* Premium Ad Content Layout */
    .premium-ad-content {
        display: flex;
        align-items: center;
        padding: 12px 20px;
    }

    /* Responsive adjustments for ad content */
    @media (max-width: 768px) {
        .premium-ad-content {
            padding: 10px 15px;
        }

        .premium-ad-title {
            font-size: 14px;
            margin-bottom: 3px;
        }

        .premium-ad-description {
            font-size: 12px;
            margin-bottom: 5px;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .premium-ad-badge span {
            font-size: 9px;
            padding: 2px 5px;
        }

        .premium-ad-cta {
            padding: 4px 8px;
            font-size: 11px;
        }

        .premium-ad-image {
            max-width: 80px;
            max-height: 60px;
        }
    }

    /* Carousel Controls */
    .premium-ad-carousel-control {
        position: absolute;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background: rgba(255,255,255,0.9);
        border: 1px solid rgba(255, 215, 0, 0.3);
        box-shadow: 0 4px 10px rgba(0,0,0,0.15), 0 2px 5px rgba(0,0,0,0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        z-index: 10;
        opacity: 0.9; /* Always visible */
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        color: #1a237e;
        font-size: 14px;
        /* Position in the middle of the sides */
        top: 50%;
        transform: translateY(-50%);
    }

    .premium-ad-carousel:hover .premium-ad-carousel-control {
        opacity: 1;
        box-shadow: 0 6px 15px rgba(0,0,0,0.2), 0 3px 8px rgba(0,0,0,0.1);
    }

    .premium-ad-carousel-control:hover {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        transform: translateY(-50%) scale(1.1);
        color: #3949ab;
        border-color: rgba(255, 215, 0, 0.5);
    }

    /* Add a subtle glow effect on hover */
    .premium-ad-carousel-control::after {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: radial-gradient(circle, rgba(255, 215, 0, 0.3) 0%, rgba(255, 215, 0, 0) 70%);
        border-radius: 50%;
        z-index: -1;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .premium-ad-carousel-control:hover::after {
        opacity: 1;
    }

    .premium-ad-carousel-prev {
        left: -18px; /* Position with a slight peek */
    }

    .premium-ad-carousel-next {
        right: -18px; /* Position with a slight peek */
    }

    /* Add a subtle arrow peek effect */
    .premium-ad-carousel-prev {
        padding-right: 3px; /* Shift icon slightly to create peek effect */
    }

    .premium-ad-carousel-next {
        padding-left: 3px; /* Shift icon slightly to create peek effect */
    }

    /* Responsive adjustments for controls */
    @media (max-width: 768px) {
        .premium-header-ad-container {
            padding: 0 20px; /* Ensure there's space for the arrows on mobile */
        }

        .premium-ad-carousel-control {
            width: 30px;
            height: 30px;
            font-size: 12px;
            background: rgba(255,255,255,0.95);
            box-shadow: 0 3px 8px rgba(0,0,0,0.15);
            opacity: 1; /* Always fully visible on mobile */
        }

        .premium-ad-carousel-prev {
            left: -15px;
        }

        .premium-ad-carousel-next {
            right: -15px;
        }

        /* Ensure arrows are always visible on mobile */
        .premium-ad-carousel-control::before {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            width: 10px;
            background: linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,0.5));
            z-index: -1;
        }

        .premium-ad-carousel-prev::before {
            right: 100%;
        }

        .premium-ad-carousel-next::before {
            left: 100%;
        }
    }

    /* Carousel Indicators */
    .premium-ad-carousel-indicators {
        position: absolute;
        bottom: 20px; /* Increased for better visibility */
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        justify-content: center; /* Center the dots horizontally */
        align-items: center; /* Center the dots vertically */
        gap: 8px;
        z-index: 10;
        padding: 4px 10px;
        background: rgba(255,255,255,0.35);
        border-radius: 30px;
        margin-bottom: 15px; /* Increased margin between dots and CTA */
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border: 1px solid rgba(255,255,255,0.5);
    }

    .premium-ad-indicator {
        width: 6px !important;
        height: 6px !important;
        min-width: 6px !important;
        min-height: 6px !important;
        max-width: 6px !important;
        max-height: 6px !important;
        border-radius: 50% !important;
        background: rgba(255,255,255,0.7) !important;
        border: none !important;
        cursor: pointer;
        padding: 0 !important;
        margin: 0 3px !important;
        transition: all 0.3s ease !important;
        box-shadow: 0 1px 3px rgba(0,0,0,0.15);
        display: inline-block !important;
        position: relative !important;
    }

    .premium-ad-indicator.active {
        background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%) !important;
        transform: scale(1.2) !important;
        box-shadow: 0 2px 5px rgba(26, 35, 126, 0.3);
    }

    /* Add subtle hover effect */
    .premium-ad-indicator:hover:not(.active) {
        background: rgba(255,255,255,0.9) !important;
        transform: scale(1.1) !important;
    }

    /* Responsive adjustments for indicators */
    @media (max-width: 768px) {
        .premium-ad-carousel-indicators {
            bottom: 10px !important;
            padding: 2px 6px !important;
            gap: 4px !important;
            margin-bottom: 8px !important;
            background: rgba(255,255,255,0.45) !important; /* Slightly more opaque on mobile */
            max-width: 80% !important;
        }

        .premium-ad-indicator {
            width: 5px !important;
            height: 5px !important;
            min-width: 5px !important;
            min-height: 5px !important;
            max-width: 5px !important;
            max-height: 5px !important;
            margin: 0 2px !important;
        }

        /* Ensure indicators are always visible on mobile */
        .premium-ad-carousel-indicators {
            opacity: 1 !important;
            visibility: visible !important;
        }

        /* Adjust CTA button to prevent overlap with indicators */
        .premium-ad-cta {
            margin-bottom: 25px !important; /* Add space below the button */
        }
    }

    /* Fix for very small screens */
    @media (max-width: 576px) {
        .premium-ad-carousel-indicators {
            bottom: 35px !important; /* Move indicators higher to avoid Learn More button */
            padding: 2px 5px !important;
            gap: 3px !important;
            background: rgba(255,255,255,0.6) !important; /* More opaque for better visibility */
            z-index: 20 !important; /* Ensure it's above other elements */
            right: 10px !important; /* Position to the right */
            left: auto !important; /* Override the left positioning */
            transform: none !important; /* Remove the transform */
            border-radius: 10px !important; /* Rounded corners */
            max-width: auto !important; /* Allow natural width */
        }

        .premium-ad-indicator {
            width: 4px !important;
            height: 4px !important;
            min-width: 4px !important;
            min-height: 4px !important;
            max-width: 4px !important;
            max-height: 4px !important;
            margin: 0 1px !important;
        }

        .premium-ad-indicator.active {
            transform: scale(1.1) !important; /* Even more subtle scale on mobile */
        }

        /* Ensure CTA button is not overlapped */
        .premium-ad-cta {
            margin-bottom: 5px !important; /* Reduce bottom margin on very small screens */
            position: relative !important; /* Ensure proper stacking */
            z-index: 15 !important; /* Lower than indicators but still high */
        }
    }

    /* Premium Ad Badge */
    .premium-ad-badge {
        display: inline-block;
        margin-bottom: 12px;
        background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
        border-radius: 30px;
        padding: 6px 16px;
        font-size: 11px;
        font-weight: 700;
        color: white;
        text-transform: uppercase;
        letter-spacing: 1px;
        box-shadow: 0 6px 15px rgba(26, 35, 126, 0.4), 0 0 20px rgba(26, 35, 126, 0.2);
        position: relative;
        overflow: hidden;
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    /* Glossy effect for badge */
    .premium-ad-badge::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 50%;
        background: linear-gradient(to bottom, rgba(255,255,255,0.5), rgba(255,255,255,0));
        z-index: 1;
    }

    /* Add shine animation */
    .premium-ad-badge::after {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(
            to right,
            rgba(255, 255, 255, 0) 0%,
            rgba(255, 255, 255, 0.3) 50%,
            rgba(255, 255, 255, 0) 100%
        );
        transform: rotate(30deg);
        animation: shineEffect 3s infinite;
        z-index: 1;
    }

    @keyframes shineEffect {
        0% { transform: rotate(30deg) translateX(-100%); }
        100% { transform: rotate(30deg) translateX(100%); }
    }

    .premium-ad-badge span {
        position: relative;
        z-index: 2;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    /* Premium Ad Text Section */
    .premium-ad-text {
        flex: 1;
        padding-right: 40px; /* Increased padding to create more space between text and image */
        margin-top: 10px; /* Add small margin on top of premium showcase text */
        position: relative;
        z-index: 5;
    }

    .premium-ad-title {
        font-size: 20px;
        font-weight: 800;
        color: #1a237e;
        margin-bottom: 10px;
        line-height: 1.3;
        letter-spacing: -0.2px;
        position: relative;
        display: inline-block;
        text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
    }

    /* Add a subtle underline to the title */
    .premium-ad-title::after {
        content: '';
        position: absolute;
        left: 0;
        bottom: -2px;
        width: 40px;
        height: 2px;
        background: linear-gradient(to right, #3949ab, rgba(57, 73, 171, 0.3));
        border-radius: 2px;
    }

    .premium-ad-description {
        font-size: 14px;
        color: #444;
        margin-bottom: 15px;
        line-height: 1.5;
        display: -webkit-box;
        -webkit-line-clamp: 2; /* Show 2 lines instead of 1 */
        -webkit-box-orient: vertical;
        overflow: hidden;
        opacity: 0.95;
        max-width: 90%;
        font-weight: 500;
    }

    /* Premium Ad CTA Button */
    .premium-ad-cta {
        display: inline-flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 10px 20px 14px; /* Increased padding for a larger button */
        background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
        color: white;
        font-weight: 700;
        font-size: 14px;
        border-radius: 8px;
        text-decoration: none;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        box-shadow: 0 8px 20px rgba(26, 35, 126, 0.3), 0 4px 10px rgba(26, 35, 126, 0.2);
        position: relative;
        overflow: hidden;
        border: 1px solid rgba(255, 255, 255, 0.3);
        margin-top: 8px; /* Add margin to the top of the button */
        float: right; /* Align button to the right to match with sponsored text */
        min-width: 140px; /* Ensure enough width for the dots */
    }

    /* CTA content container */
    .premium-ad-cta-content {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
    }

    .premium-ad-cta:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 50%;
        background: linear-gradient(to bottom, rgba(255,255,255,0.4), rgba(255,255,255,0));
        opacity: 1;
        transition: opacity 0.3s ease;
    }

    .premium-ad-cta:after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 5%;
        width: 90%;
        height: 12px;
        background: rgba(26, 35, 126, 0.4);
        filter: blur(6px);
        border-radius: 50%;
        z-index: -1;
        transition: all 0.3s ease;
    }

    /* Add shine effect */
    .premium-ad-cta::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(
            to right,
            rgba(255, 255, 255, 0) 0%,
            rgba(255, 255, 255, 0.3) 50%,
            rgba(255, 255, 255, 0) 100%
        );
        transform: rotate(30deg);
        animation: ctaShine 3s infinite;
        z-index: 1;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .premium-ad-cta:hover::before {
        opacity: 1;
    }

    @keyframes ctaShine {
        0% { transform: rotate(30deg) translateX(-100%); }
        100% { transform: rotate(30deg) translateX(100%); }
    }

    .premium-ad-cta:hover {
        background: linear-gradient(135deg, #283593 0%, #5c6bc0 100%);
        box-shadow: 0 12px 25px rgba(26, 35, 126, 0.4), 0 8px 15px rgba(26, 35, 126, 0.2);
        color: white;
        transform: translateY(-5px) scale(1.03);
    }

    .premium-ad-cta:hover:after {
        bottom: -6px;
        filter: blur(10px);
        width: 95%;
        left: 2.5%;
    }

    .premium-ad-cta span {
        position: relative;
        z-index: 2;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        letter-spacing: 0.5px;
    }

    .premium-ad-cta i {
        margin-left: 10px;
        transition: transform 0.4s ease;
        position: relative;
        z-index: 2;
        font-size: 12px;
    }

    .premium-ad-cta:hover i {
        transform: translateX(8px);
    }

    /* CTA indicators */
    .premium-ad-cta-indicators {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 4px;
        margin-top: 6px; /* Space between text and dots */
    }

    .premium-ad-cta-dot {
        width: 5px;
        height: 5px;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.5);
        display: inline-block;
        transition: all 0.3s ease;
    }

    .premium-ad-cta-dot.active {
        background-color: #ffffff;
        transform: scale(1.2);
        box-shadow: 0 0 4px rgba(255, 255, 255, 0.5);
    }

    /* Premium Ad Media Section */
    .premium-ad-media {
        width: 180px; /* Increased width */
        height: 120px; /* Increased height */
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 15px 30px rgba(0,0,0,0.2), 0 8px 15px rgba(0,0,0,0.1);
        position: relative;
        transform: perspective(800px) rotateY(-8deg) translateZ(20px);
        transition: all 0.6s cubic-bezier(0.19, 1, 0.22, 1);
        border: 3px solid rgba(255, 255, 255, 0.9);
        margin-left: 20px; /* Add margin between text content and image */
        z-index: 10;
    }

    .premium-ad-item:hover .premium-ad-media {
        transform: perspective(800px) rotateY(0deg) translateZ(30px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.25), 0 12px 20px rgba(0,0,0,0.15);
    }

    /* Add a subtle golden glow around the image */
    .premium-ad-media::after {
        content: '';
        position: absolute;
        top: -3px;
        left: -3px;
        right: -3px;
        bottom: -3px;
        background: linear-gradient(135deg, rgba(255, 215, 0, 0.3), rgba(184, 134, 11, 0.3));
        border-radius: 15px;
        z-index: -1;
        filter: blur(8px);
        opacity: 0;
        transition: opacity 0.5s ease;
    }

    .premium-ad-item:hover .premium-ad-media::after {
        opacity: 1;
    }

    .premium-ad-image-wrapper {
        width: 100%;
        height: 100%;
        overflow: hidden;
        position: relative;
    }

    .premium-ad-image-wrapper:after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, rgba(26, 35, 126, 0.15) 0%, rgba(0,0,0,0) 100%);
        z-index: 1;
    }

    /* Glossy effect for images */
    .premium-ad-image-wrapper:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 40%;
        background: linear-gradient(to bottom, rgba(255,255,255,0.4), rgba(255,255,255,0));
        z-index: 2;
        pointer-events: none;
    }

    .premium-ad-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 1s cubic-bezier(0.19, 1, 0.22, 1);
    }

    .premium-ad-item:hover .premium-ad-image {
        transform: scale(1.12);
    }

    /* Golden horizontal rule */
    .golden-hr {
        border: 0;
        height: 2px;
        background: linear-gradient(to right,
            rgba(184, 134, 11, 0),
            rgba(218, 165, 32, 0.5) 20%,
            rgba(255, 215, 0, 0.8) 50%,
            rgba(218, 165, 32, 0.5) 80%,
            rgba(184, 134, 11, 0) 100%);
        margin: 20px auto 10px;
        width: 80%;
        box-shadow: 0 1px 3px rgba(255, 215, 0, 0.2);
    }

    .premium-ad-image-placeholder {
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #e8eaf6 0%, #c5cae9 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: #3949ab;
        font-size: 30px;
    }

    /* Premium Ad Sponsor Label */
    .premium-ad-sponsor-label {
        position: absolute;
        top: 8px;
        right: 8px;
        background: linear-gradient(135deg, rgba(26, 35, 126, 0.8), rgba(57, 73, 171, 0.8));
        color: white;
        font-size: 8px;
        font-weight: 600;
        padding: 3px 8px;
        border-radius: 4px;
        opacity: 0.8;
        z-index: 10;
        transition: all 0.3s ease;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        border: 1px solid rgba(255,255,255,0.2);
        letter-spacing: 0.5px;
        text-transform: uppercase;
    }

    .premium-ad-sponsor-label:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 50%;
        background: linear-gradient(to bottom, rgba(255,255,255,0.3), rgba(255,255,255,0));
        z-index: -1;
    }

    .premium-ad-item:hover .premium-ad-sponsor-label {
        opacity: 1;
        box-shadow: 0 3px 8px rgba(0,0,0,0.15);
    }

    /* Responsive Adjustments */
    @media (max-width: 768px) {
        .premium-header-ad-container {
            width: 100%; /* Reset width for mobile */
            left: 0;
            right: 0;
            padding: 0 10px;
        }

        .premium-section-title {
            margin-bottom: 15px;
            margin-top: 10px;
        }

        .premium-icon {
            width: 20px;
            height: 20px;
            font-size: 10px;
        }

        .premium-text {
            font-size: 14px;
            letter-spacing: 1.5px;
        }

        .premium-ad-carousel {
            border-radius: 12px;
            margin: 15px 0 20px;
        }

        .premium-ad-content {
            flex-direction: row;
            padding: 12px 15px;
            align-items: center;
        }

        .premium-ad-text {
            padding-right: 12px;
        }

        .premium-ad-media {
            width: 100px;
            height: 90px;
            margin-left: 10px;
            transform: perspective(800px) rotateY(-5deg) translateZ(10px);
        }

        .premium-ad-item:hover .premium-ad-media {
            transform: perspective(800px) rotateY(0deg) translateZ(15px);
        }

        .premium-ad-badge {
            font-size: 9px;
            padding: 4px 10px;
            margin-bottom: 6px;
        }

        .premium-ad-title {
            font-size: 16px;
            margin-bottom: 6px;
        }

        .premium-ad-title::after {
            width: 30px;
        }

        .premium-ad-description {
            font-size: 12px;
            margin-bottom: 10px;
            -webkit-line-clamp: 2;
            max-width: 100%;
        }

        .premium-ad-cta {
            padding: 6px 14px 10px;
            font-size: 12px;
            min-width: 120px;
        }

        .premium-ad-cta-indicators {
            margin-top: 4px;
            gap: 3px;
        }

        .premium-ad-cta-dot {
            width: 4px;
            height: 4px;
        }

        .golden-hr {
            width: 90%;
            margin: 15px auto 5px;
        }
    }

    /* Animations for premium elements */
    @keyframes badgePulse {
        0% { box-shadow: 0 4px 10px rgba(26, 35, 126, 0.3); }
        50% { box-shadow: 0 4px 15px rgba(26, 35, 126, 0.5); }
        100% { box-shadow: 0 4px 10px rgba(26, 35, 126, 0.3); }
    }

    @keyframes iconGlow {
        0% { box-shadow: 0 3px 6px rgba(184, 134, 11, 0.4); }
        50% { box-shadow: 0 3px 10px rgba(184, 134, 11, 0.6); }
        100% { box-shadow: 0 3px 6px rgba(184, 134, 11, 0.4); }
    }

    @keyframes floatEffect {
        0% { transform: translateY(0); }
        50% { transform: translateY(-3px); }
        100% { transform: translateY(0); }
    }

    .premium-ad-badge {
        animation: badgePulse 3s infinite, floatEffect 5s ease-in-out infinite;
    }

    .premium-icon {
        animation: iconGlow 3s infinite;
    }
</style>
