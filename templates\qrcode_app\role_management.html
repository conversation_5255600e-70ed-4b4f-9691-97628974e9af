{% extends "base.html" %}
{% load static %}

{% block title %}Role Management - Enterprise QR{% endblock %}

{% block extra_css %}
<!-- Google Fonts - Poppins and Montserrat -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

<style>
    /* Ultra-Premium Enterprise Role Management Styling */
    body {
        background: linear-gradient(135deg, #000000 0%, #0a0a0a 10%, #1a1a2e 25%, #16213e 40%, #0f3460 60%, #533483 80%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        position: relative;
        overflow-x: hidden;
    }

    /* Premium animated background with enterprise patterns */
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 15% 85%, rgba(102, 126, 234, 0.7) 0%, transparent 40%),
            radial-gradient(circle at 85% 15%, rgba(255, 255, 255, 0.3) 0%, transparent 35%),
            radial-gradient(circle at 35% 75%, rgba(118, 75, 162, 0.6) 0%, transparent 45%),
            radial-gradient(circle at 75% 25%, rgba(83, 52, 131, 0.5) 0%, transparent 30%),
            radial-gradient(circle at 45% 55%, rgba(102, 126, 234, 0.4) 0%, transparent 40%);
        z-index: -1;
        animation: enterpriseRoleFloat 50s ease-in-out infinite;
    }

    @keyframes enterpriseRoleFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        25% { transform: translateY(-25px) rotate(1.5deg); }
        50% { transform: translateY(-15px) rotate(-1.5deg); }
        75% { transform: translateY(-35px) rotate(0.8deg); }
    }

    /* Enterprise Container */
    .enterprise-container {
        position: relative;
        z-index: 1;
        padding: 2rem 0;
        min-height: 100vh;
    }

    /* Premium Header Section */
    .enterprise-header {
        background: linear-gradient(135deg, rgba(26, 35, 126, 0.95) 0%, rgba(40, 53, 147, 0.95) 50%, rgba(63, 81, 181, 0.95) 100%);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2.5rem;
        margin-bottom: 2rem;
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        position: relative;
        overflow: hidden;
        animation: slideInDown 0.8s ease-out;
    }

    .enterprise-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
        animation: shimmer 3s ease-in-out infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    .enterprise-title {
        font-family: 'Montserrat', sans-serif !important;
        font-size: 2.5rem;
        font-weight: 700;
        color: #ffffff;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 2;
    }

    .enterprise-subtitle {
        font-size: 1.1rem;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 0;
        font-weight: 400;
        position: relative;
        z-index: 2;
    }

    .enterprise-icon {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        width: 60px;
        height: 60px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.8rem;
        color: white;
        margin-right: 1.5rem;
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        position: relative;
        z-index: 2;
    }

    /* Premium Action Button */
    .enterprise-action-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 12px;
        padding: 0.8rem 2rem;
        color: white;
        font-weight: 600;
        font-size: 1rem;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        position: relative;
        z-index: 2;
        overflow: hidden;
        cursor: pointer;
    }

    .enterprise-action-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .enterprise-action-btn:hover::before {
        left: 100%;
    }

    .enterprise-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    /* Premium Role Cards Container */
    .enterprise-roles-container {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        overflow: hidden;
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.15),
            0 0 0 1px rgba(255, 255, 255, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        animation: slideInLeft 0.8s ease-out 0.2s both;
        margin-bottom: 2rem;
    }

    .enterprise-roles-header {
        background: linear-gradient(135deg, #1a237e 0%, #283593 100%);
        color: white;
        padding: 1.5rem;
        font-weight: 600;
        font-size: 1.1rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        position: relative;
    }

    .enterprise-roles-header::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    }

    /* Premium Role Items */
    .enterprise-role-item {
        padding: 1.5rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .enterprise-role-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.05), transparent);
        transition: left 0.5s;
    }

    .enterprise-role-item:hover::before {
        left: 100%;
    }

    .enterprise-role-item:hover {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.08) 100%);
        transform: translateX(5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .enterprise-role-item.active {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.15) 100%);
        border-left: 4px solid #667eea;
        transform: translateX(5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .enterprise-role-name {
        font-weight: 700;
        font-size: 1.1rem;
        color: #1a237e;
        margin-bottom: 0.3rem;
    }

    .enterprise-role-description {
        color: #667eea;
        font-size: 0.9rem;
        font-weight: 500;
        margin-bottom: 0.5rem;
    }

    .enterprise-role-badge {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 0.3rem 0.8rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        display: inline-block;
    }

    /* Premium Permissions Panel */
    .enterprise-permissions-container {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        overflow: hidden;
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.15),
            0 0 0 1px rgba(255, 255, 255, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        animation: slideInRight 0.8s ease-out 0.4s both;
        min-height: 600px;
    }

    .enterprise-permissions-header {
        background: linear-gradient(135deg, #1a237e 0%, #283593 100%);
        color: white;
        padding: 1.5rem;
        font-weight: 600;
        font-size: 1.1rem;
        position: relative;
    }

    .enterprise-permissions-header::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    }

    .enterprise-permissions-content {
        padding: 2rem;
    }

    .enterprise-empty-state {
        text-align: center;
        padding: 4rem 2rem;
        color: #9ca3af;
    }

    .enterprise-empty-icon {
        font-size: 4rem;
        margin-bottom: 1.5rem;
        opacity: 0.5;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .enterprise-empty-text {
        font-size: 1.2rem;
        font-weight: 500;
        margin-bottom: 0.5rem;
    }

    .enterprise-empty-subtext {
        font-size: 1rem;
        opacity: 0.8;
    }

    /* Premium Modal Styling */
    .modal-content {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%);
        backdrop-filter: blur(20px);
        border: none;
        border-radius: 20px;
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }

    .modal-header {
        background: linear-gradient(135deg, #1a237e 0%, #283593 100%);
        color: white;
        border-bottom: none;
        border-radius: 20px 20px 0 0;
        padding: 1.5rem 2rem;
    }

    .modal-title {
        font-weight: 700;
        font-size: 1.3rem;
    }

    .btn-close {
        filter: invert(1);
        opacity: 0.8;
    }

    .modal-body {
        padding: 2rem;
    }

    .modal-footer {
        border-top: 1px solid rgba(0, 0, 0, 0.1);
        padding: 1.5rem 2rem;
        background: rgba(248, 250, 252, 0.5);
        border-radius: 0 0 20px 20px;
    }

    .modal .form-control,
    .modal .form-select {
        border: 2px solid rgba(102, 126, 234, 0.2);
        border-radius: 12px;
        padding: 0.75rem 1rem;
        font-weight: 500;
        background: rgba(255, 255, 255, 0.9);
        transition: all 0.3s ease;
    }

    .modal .form-control:focus,
    .modal .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        outline: none;
    }

    .modal .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .modal .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }

    .modal .btn-secondary {
        background: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%);
        border: none;
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .modal .btn-secondary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(107, 114, 128, 0.3);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .enterprise-title {
            font-size: 2rem;
        }

        .enterprise-header {
            padding: 2rem 1.5rem;
        }

        .enterprise-roles-container,
        .enterprise-permissions-container {
            margin-bottom: 1.5rem;
        }

        .enterprise-role-item {
            padding: 1rem;
        }

        .enterprise-permissions-content {
            padding: 1.5rem;
        }

        .enterprise-empty-state {
            padding: 3rem 1rem;
        }

        .enterprise-empty-icon {
            font-size: 3rem;
        }
    }

    @media (max-width: 576px) {
        .enterprise-title {
            font-size: 1.8rem;
        }

        .enterprise-header {
            padding: 1.5rem 1rem;
        }

        .enterprise-roles-container,
        .enterprise-permissions-container {
            border-radius: 15px;
        }

        .enterprise-roles-header,
        .enterprise-permissions-header {
            padding: 1rem;
            font-size: 1rem;
        }

        .enterprise-role-item {
            padding: 1rem;
        }

        .enterprise-permissions-content {
            padding: 1rem;
        }

        .modal-body {
            padding: 1.5rem;
        }

        .modal-footer {
            padding: 1rem 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="enterprise-container">
    <div class="container">
        <!-- Premium Header Section -->
        <div class="enterprise-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <div class="d-flex align-items-center">
                        <div class="enterprise-icon">
                            <i class="fas fa-user-tag"></i>
                        </div>
                        <div>
                            <h1 class="enterprise-title mb-0">Role Management</h1>
                            <p class="enterprise-subtitle">Configure roles and permissions with enterprise-grade security control</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-md-end">
                    <button class="enterprise-action-btn" data-bs-toggle="modal" data-bs-target="#addRoleModal">
                        <i class="fas fa-plus"></i>
                        Add Custom Role
                    </button>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Premium Roles Panel -->
            <div class="col-lg-4">
                <div class="enterprise-roles-container">
                    <div class="enterprise-roles-header">
                        <i class="fas fa-users-cog me-2"></i>System Roles
                    </div>
                    <div>
                        {% for group in groups %}
                        <div class="enterprise-role-item" data-role="{{ group.name|lower }}">
                            <div class="enterprise-role-name">{{ group.name }}</div>
                            <div class="enterprise-role-description">{{ group.permissions.count }} permissions assigned</div>
                            <span class="enterprise-role-badge">{{ group.user_set.count }} users</span>
                        </div>
                        {% empty %}
                        <div class="enterprise-empty-state">
                            <div class="enterprise-empty-icon">
                                <i class="fas fa-user-shield"></i>
                            </div>
                            <div class="enterprise-empty-text">No Roles Found</div>
                            <div class="enterprise-empty-subtext">Create your first custom role to get started</div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- Premium Permissions Panel -->
            <div class="col-lg-8">
                <div class="enterprise-permissions-container">
                    <div class="enterprise-permissions-header">
                        <i class="fas fa-shield-alt me-2"></i>Role Permissions: <span id="selectedRoleName">Select a Role</span>
                    </div>
                    <div class="enterprise-permissions-content">
                        <div id="permissionsContent">
                            <div class="enterprise-empty-state">
                                <div class="enterprise-empty-icon">
                                    <i class="fas fa-user-shield"></i>
                                </div>
                                <div class="enterprise-empty-text">Select a Role</div>
                                <div class="enterprise-empty-subtext">Choose a role from the left panel to view and manage its permissions</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Role Modal -->
<div class="modal fade" id="addRoleModal" tabindex="-1" aria-labelledby="addRoleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addRoleModalLabel">Add Custom Role</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addRoleForm">
                    <div class="mb-3">
                        <label for="roleName" class="form-label">Role Name</label>
                        <input type="text" class="form-control" id="roleName" required>
                    </div>
                    <div class="mb-3">
                        <label for="roleDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="roleDescription" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Base Permissions On</label>
                        <select class="form-select" id="baseRole">
                            <option value="guest">Guest</option>
                            <option value="user" selected>User</option>
                            <option value="premium">Premium User</option>
                            <option value="admin">Administrator</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="createRoleBtn">Create Role</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'qrcode_app/js/role-management.js' %}"></script>
{% endblock %}
