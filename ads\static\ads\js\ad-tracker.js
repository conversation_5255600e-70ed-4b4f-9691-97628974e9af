/**
 * Ad Tracker - Tracks impressions and clicks for advertisements
 * 
 * This script handles tracking of ad impressions and clicks by sending
 * data to the tracking API endpoints. It also collects basic device and
 * location information for analytics purposes.
 */

class AdTracker {
    constructor() {
        this.csrfToken = this.getCSRFToken();
        this.deviceInfo = this.getDeviceInfo();
        this.locationInfo = {};
        
        // Try to get location info if available
        this.getLocationInfo();
    }
    
    /**
     * Get CSRF token from cookie
     */
    getCSRFToken() {
        const name = 'csrftoken';
        let cookieValue = null;
        
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        
        return cookieValue;
    }
    
    /**
     * Get basic device information
     */
    getDeviceInfo() {
        const userAgent = navigator.userAgent;
        let deviceType = 'desktop';
        
        if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)) {
            deviceType = 'mobile';
            
            if (/iPad|tablet|Tablet/i.test(userAgent)) {
                deviceType = 'tablet';
            }
        }
        
        return {
            type: deviceType,
            userAgent: userAgent,
            screenWidth: window.innerWidth,
            screenHeight: window.innerHeight
        };
    }
    
    /**
     * Try to get location information
     */
    getLocationInfo() {
        // Default to unknown
        this.locationInfo = {
            country: 'unknown',
            city: 'unknown'
        };
        
        // Try to get location from IP using a free API
        fetch('https://ipapi.co/json/')
            .then(response => response.json())
            .then(data => {
                this.locationInfo = {
                    country: data.country_name || 'unknown',
                    city: data.city || 'unknown',
                    region: data.region || 'unknown',
                    ip: data.ip || 'unknown'
                };
            })
            .catch(error => {
                console.log('Error getting location info:', error);
            });
    }
    
    /**
     * Track an ad impression
     * @param {number} adId - The ID of the ad
     */
    trackImpression(adId) {
        // Don't track if no ad ID
        if (!adId) return;
        
        // Prepare data
        const data = {
            ad_id: adId,
            device_info: this.deviceInfo,
            location_info: this.locationInfo
        };
        
        // Send impression data to the server
        fetch('/ads/api/track-impression/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': this.csrfToken
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            console.log('Impression tracked:', data);
        })
        .catch(error => {
            console.error('Error tracking impression:', error);
        });
    }
    
    /**
     * Track an ad click
     * @param {number} adId - The ID of the ad
     */
    trackClick(adId) {
        // Don't track if no ad ID
        if (!adId) return;
        
        // Prepare data
        const data = {
            ad_id: adId,
            device_info: this.deviceInfo,
            location_info: this.locationInfo
        };
        
        // Send click data to the server
        fetch('/ads/api/track-click/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': this.csrfToken
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            console.log('Click tracked:', data);
        })
        .catch(error => {
            console.error('Error tracking click:', error);
        });
    }
    
    /**
     * Initialize tracking for ads on the page
     */
    init() {
        // Find all ad elements with data-ad-id attribute
        const adElements = document.querySelectorAll('[data-ad-id]');
        
        adElements.forEach(adElement => {
            const adId = adElement.getAttribute('data-ad-id');
            
            // Track impression when ad is visible
            if (this.isElementInViewport(adElement)) {
                this.trackImpression(adId);
            } else {
                // Use Intersection Observer to track when ad becomes visible
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            this.trackImpression(adId);
                            observer.unobserve(entry.target);
                        }
                    });
                }, { threshold: 0.5 });
                
                observer.observe(adElement);
            }
            
            // Track click when ad is clicked
            const clickableElements = adElement.querySelectorAll('a, button');
            clickableElements.forEach(element => {
                element.addEventListener('click', (event) => {
                    this.trackClick(adId);
                });
            });
        });
    }
    
    /**
     * Check if an element is in the viewport
     * @param {HTMLElement} element - The element to check
     * @returns {boolean} - Whether the element is in the viewport
     */
    isElementInViewport(element) {
        const rect = element.getBoundingClientRect();
        
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }
}

// Initialize the ad tracker when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const adTracker = new AdTracker();
    adTracker.init();
});
