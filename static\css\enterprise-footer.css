/* Enterprise QR Code Generator - Luxury Enterprise Design */

/* Main Content Area - Luxury Design */
.corporate-section {
    padding: 3rem 0;
    position: relative;
    margin-top: 2rem; /* Add margin below navigation */
}

/* Luxury Section Title */
.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(90deg, #1a1a1a, #333);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
    display: inline-block;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, #3a7bd5, #00d2ff);
    border-radius: 2px;
}

/* Luxury Section Subtitle */
.section-subtitle {
    font-size: 1.2rem;
    color: #505050;
    margin-bottom: 3rem;
    max-width: 800px;
    line-height: 1.6;
}

/* Luxury Card Design */
.luxury-card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    transition: all 0.4s ease;
    border: none;
    overflow: hidden;
    position: relative;
}

.luxury-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #3a7bd5, #00d2ff);
}

.luxury-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
}

/* Feature Icons - Luxury Design */
.feature-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
    background: linear-gradient(135deg, rgba(58, 123, 213, 0.1), rgba(0, 210, 255, 0.1));
    border-radius: 50%;
    position: relative;
    box-shadow: 0 5px 15px rgba(58, 123, 213, 0.15);
}

.feature-icon::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0));
    border-radius: 50% 50% 0 0;
    pointer-events: none;
}

@media (max-width: 767.98px) {
    .feature-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .section-subtitle {
        font-size: 1.1rem;
    }
}

/* Enterprise Footer Container - Glossy 3D Design */
.enterprise-footer {
    background: linear-gradient(135deg, #1a1f36, #121628);
    position: relative;
    overflow: hidden;
    color: #e6e6e6;
    padding: 0;
    margin-top: 4rem;
    margin-bottom: 0;
    z-index: 10;
    box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.2);
    /* 3D perspective effect */
    perspective: 1000px;
    /* Ensure no blue space below footer */
    border-bottom: 0;
}

/* Glossy overlay for 3D effect */
.enterprise-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 60%;
    background: linear-gradient(to bottom,
        rgba(255, 255, 255, 0.15),
        rgba(255, 255, 255, 0.05) 30%,
        rgba(255, 255, 255, 0) 60%);
    pointer-events: none;
    z-index: 1;
}

/* Footer Top Section with 3D Accent Line */
.footer-top-accent {
    height: 6px;
    background: linear-gradient(90deg, #3a7bd5, #00d2ff, #3a7bd5);
    position: relative;
    overflow: hidden;
    transform-style: preserve-3d;
    transform: translateZ(0);
    box-shadow: 0 2px 10px rgba(0, 210, 255, 0.3);
}

.footer-top-accent::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
    animation: shimmer 3s infinite;
    transform: translateZ(5px);
}

@keyframes shimmer {
    100% {
        left: 100%;
    }
}

/* Footer Main Content with 3D Effect */
.footer-main {
    padding: 4rem 0 3rem;
    position: relative;
    transform-style: preserve-3d;
    z-index: 2;
}

/* 3D Floating Elements Animation */
@keyframes float {
    0% {
        transform: translateZ(0px) translateY(0px);
    }
    50% {
        transform: translateZ(10px) translateY(-5px);
    }
    100% {
        transform: translateZ(0px) translateY(0px);
    }
}

/* Modern 3D Background Pattern */
.footer-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        linear-gradient(135deg, rgba(58, 123, 213, 0.05) 25%, transparent 25%),
        linear-gradient(225deg, rgba(58, 123, 213, 0.05) 25%, transparent 25%),
        linear-gradient(315deg, rgba(58, 123, 213, 0.05) 25%, transparent 25%),
        linear-gradient(45deg, rgba(58, 123, 213, 0.05) 25%, transparent 25%);
    background-size: 60px 60px;
    background-position: 0 0, 30px 0, 30px -30px, 0px 30px;
    opacity: 0.5;
    pointer-events: none;
    transform: translateZ(-5px);
}

/* Footer Logo Section - 3D Glossy Effect */
.footer-logo-section {
    margin-bottom: 2.5rem;
    position: relative;
    transform-style: preserve-3d;
    animation: float 6s ease-in-out infinite;
}

.footer-logo {
    font-size: 1.75rem;
    font-weight: 700;
    color: #fff;
    margin-bottom: 1.25rem;
    display: flex;
    align-items: center;
    text-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    position: relative;
}

.footer-logo-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #3a7bd5, #00d2ff);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    color: #fff;
    font-size: 1.25rem;
    box-shadow: 0 5px 15px rgba(0, 210, 255, 0.3),
                inset 0 1px 1px rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
    transform: translateZ(10px);
}

/* Glossy effect for logo icon */
.footer-logo-icon::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0));
    pointer-events: none;
}

.footer-tagline {
    color: #a0aec0;
    margin-bottom: 1.75rem;
    max-width: 320px;
    line-height: 1.6;
    position: relative;
    transform: translateZ(5px);
}

/* Footer Column Headings - 3D Effect */
.footer-heading {
    color: #fff;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1.75rem;
    position: relative;
    padding-bottom: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.08em;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    transform: translateZ(5px);
}

.footer-heading::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 3px;
    background: linear-gradient(90deg, #3a7bd5, #00d2ff);
    border-radius: 3px;
    box-shadow: 0 2px 5px rgba(0, 210, 255, 0.3);
    transform: translateZ(2px);
}

/* Footer Links - 3D Glossy Effect */
.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
    position: relative;
    transform-style: preserve-3d;
}

.footer-link-item {
    margin-bottom: 1rem;
    transform-style: preserve-3d;
    position: relative;
}

.footer-link {
    color: #a0aec0;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-block;
    position: relative;
    padding: 0.25rem 0;
    transform: translateZ(0);
}

.footer-link:hover {
    color: #fff;
    text-decoration: none;
    transform: translateX(8px) translateZ(5px);
    text-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
}

/* Modern hover effect with gradient line */
.footer-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #3a7bd5, #00d2ff);
    transition: width 0.3s ease;
    transform: translateZ(1px);
    opacity: 0;
    box-shadow: 0 0 5px rgba(0, 210, 255, 0.3);
}

.footer-link:hover::before {
    width: 100%;
    opacity: 1;
}

/* Contact Information - 3D Glossy Effect */
.contact-info {
    list-style: none;
    padding: 0;
    margin: 0;
    position: relative;
    transform-style: preserve-3d;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1.25rem;
    position: relative;
    transform-style: preserve-3d;
}

.contact-icon {
    color: #00d2ff;
    margin-right: 0.75rem;
    font-size: 1rem;
    margin-top: 0.25rem;
    position: relative;
    transform: translateZ(5px);
    text-shadow: 0 0 5px rgba(0, 210, 255, 0.5);
    /* Subtle pulse animation */
    animation: iconPulse 3s ease-in-out infinite;
}

@keyframes iconPulse {
    0%, 100% { text-shadow: 0 0 5px rgba(0, 210, 255, 0.5); }
    50% { text-shadow: 0 0 10px rgba(0, 210, 255, 0.8); }
}

.contact-text {
    color: #a0aec0;
    position: relative;
    transform: translateZ(2px);
}

.contact-text a {
    color: #a0aec0;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    display: inline-block;
}

.contact-text a:hover {
    color: #fff;
    text-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
    transform: translateZ(5px);
}

/* Social Media Icons - 3D Glossy Effect */
.social-icons {
    display: flex;
    margin-top: 1.75rem;
    position: relative;
    transform-style: preserve-3d;
}

.social-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(58, 123, 213, 0.2), rgba(0, 210, 255, 0.2));
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    transition: all 0.3s ease;
    text-decoration: none;
    position: relative;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    transform: translateZ(5px);
}

/* Glossy effect for social icons */
.social-icon::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0));
    pointer-events: none;
}

.social-icon:hover {
    background: linear-gradient(135deg, #3a7bd5, #00d2ff);
    transform: translateY(-5px) translateZ(15px);
    color: #fff;
    box-shadow: 0 8px 20px rgba(0, 210, 255, 0.3);
}

/* Newsletter Subscription - 3D Glossy Effect */
.newsletter-form {
    margin-top: 2rem;
    position: relative;
    transform-style: preserve-3d;
}

.newsletter-input-group {
    display: flex;
    margin-bottom: 1rem;
    position: relative;
    transform-style: preserve-3d;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    overflow: hidden;
}

.newsletter-input {
    flex: 1;
    background-color: rgba(255, 255, 255, 0.05);
    border: none;
    color: #fff;
    padding: 0.85rem 1.25rem;
    border-radius: 4px 0 0 4px;
    outline: none;
    position: relative;
    transform: translateZ(0);
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-right: none;
}

.newsletter-input::placeholder {
    color: rgba(160, 174, 192, 0.7);
}

.newsletter-button {
    background: linear-gradient(135deg, #3a7bd5, #00d2ff);
    color: #fff;
    border: none;
    padding: 0 1.5rem;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    transform: translateZ(2px);
}

.newsletter-text {
    color: #a0aec0;
    font-size: 0.875rem;
    margin-top: 0.75rem;
}

/* Footer Bottom - 3D Glossy Effect */
.footer-bottom {
    background-color: rgba(0, 0, 0, 0.2);
    padding: 1.5rem 0;
    position: relative;
    z-index: 5;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    transform-style: preserve-3d;
}

.footer-copyright {
    color: #a0aec0;
    font-size: 0.875rem;
    position: relative;
    transform: translateZ(2px);
}

.footer-bottom-links {
    display: flex;
    position: relative;
    transform-style: preserve-3d;
}

.footer-bottom-link {
    color: #a0aec0;
    text-decoration: none;
    font-size: 0.875rem;
    margin-left: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    transform: translateZ(0);
}

.footer-bottom-link:hover {
    color: #fff;
    transform: translateZ(5px);
    text-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
}

.footer-bottom-link::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 1px;
    background: linear-gradient(90deg, #3a7bd5, #00d2ff);
    transition: width 0.3s ease;
    transform: translateZ(1px);
    opacity: 0;
}

.footer-bottom-link:hover::before {
    width: 100%;
    opacity: 1;
}

/* Glossy effect for newsletter button */
.newsletter-button::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
    pointer-events: none;
}

.newsletter-button:hover {
    background: linear-gradient(135deg, #00d2ff, #3a7bd5);
    box-shadow: 0 0 15px rgba(0, 210, 255, 0.5);
    transform: translateZ(5px);
}

/* Footer Bottom - 3D Glossy Effect */
.footer-bottom {
    background: linear-gradient(to right, #0f1620, #121a29, #0f1620);
    padding: 1.5rem 0;
    position: relative;
    box-shadow: 0 -5px 15px rgba(0, 0, 0, 0.2);
    transform-style: preserve-3d;
    overflow: hidden;
}

/* Glossy effect for footer bottom */
.footer-bottom::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(58, 123, 213, 0.5), transparent);
    z-index: 1;
}

/* Animated light beam effect */
.footer-bottom::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 50%;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(0, 210, 255, 0.8), transparent);
    animation: lightBeam 8s infinite;
    z-index: 2;
}

@keyframes lightBeam {
    0% { left: -100%; }
    50% { left: 100%; }
    100% { left: 100%; }
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 3;
}

.copyright {
    color: #a0aec0;
    font-size: 0.875rem;
    position: relative;
    transform: translateZ(2px);
}

.footer-bottom-links {
    display: flex;
    position: relative;
    transform-style: preserve-3d;
}

.footer-bottom-link {
    color: #a0aec0;
    text-decoration: none;
    font-size: 0.875rem;
    margin-left: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    transform: translateZ(0);
}

.footer-bottom-link:hover {
    color: #fff;
    text-shadow: 0 0 5px rgba(0, 210, 255, 0.5);
    transform: translateZ(5px);
}

/* Responsive adjustments for 3D effects */
@media (max-width: 767.98px) {
    .footer-logo-section,
    .footer-heading,
    .footer-links,
    .contact-info,
    .social-icons,
    .newsletter-form {
        transform: none !important;
        animation: none !important;
    }

    .footer-logo-icon,
    .contact-icon,
    .social-icon,
    .footer-link:hover,
    .contact-text a:hover,
    .footer-bottom-link:hover,
    .newsletter-button:hover {
        transform: none !important;
    }

    .footer-heading::after {
        transform: none !important;
    }

    .enterprise-footer {
        perspective: none;
    }
}

/* Mobile Responsiveness */
@media (max-width: 991.98px) {
    .footer-main {
        padding: 3rem 0 5rem; /* Extra padding for mobile nav */
    }

    .footer-heading {
        margin-top: 2rem;
    }
}

@media (max-width: 767.98px) {
    .footer-main {
        padding: 2rem 0 5rem;
    }

    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
    }

    .footer-bottom-links {
        margin-top: 1rem;
        justify-content: center;
    }

    .footer-bottom-link {
        margin: 0 0.75rem;
    }

    .footer-heading {
        text-align: left;
    }

    .social-icons {
        justify-content: flex-start;
    }

    .newsletter-input-group {
        flex-direction: column;
    }

    .newsletter-input, .newsletter-button {
        width: 100%;
        border-radius: 4px;
    }

    .newsletter-button {
        margin-top: 0.5rem;
    }
}

@media (max-width: 575.98px) {
    .footer-logo {
        font-size: 1.5rem;
    }

    .footer-main {
        padding: 2rem 0 5rem;
    }
}
