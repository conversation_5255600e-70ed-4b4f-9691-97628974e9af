/* API Documentation Styles */
.api-docs-container {
    display: flex;
    background-color: #f8f9fa;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.api-docs-sidebar {
    width: 250px;
    background-color: #2c3e50;
    color: #fff;
    padding: 0;
    flex-shrink: 0;
}

.api-docs-sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.api-docs-sidebar-header h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.api-docs-sidebar-content {
    padding: 1rem 0;
}

.api-docs-nav {
    list-style: none;
    padding: 0;
    margin: 0;
}

.api-docs-nav li {
    margin: 0;
}

.api-docs-nav a {
    display: block;
    padding: 0.75rem 1.5rem;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: all 0.2s ease;
}

.api-docs-nav a:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
}

.api-docs-nav a.active {
    background-color: #3498db;
    color: #fff;
    font-weight: 500;
}

.api-docs-nav-header {
    padding: 1rem 1.5rem 0.5rem;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: rgba(255, 255, 255, 0.5);
    font-weight: 600;
}

.api-docs-content {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

.api-docs-section {
    margin-bottom: 3rem;
}

.api-docs-section:last-child {
    margin-bottom: 0;
}

.api-docs-section h1 {
    margin-top: 0;
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.api-docs-section h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 2rem 0 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

.api-docs-section h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 1.5rem 0 1rem;
}

.api-docs-version {
    display: inline-block;
    background-color: #3498db;
    color: #fff;
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    margin-bottom: 1rem;
}

.api-endpoint {
    background-color: #f1f3f5;
    padding: 1rem;
    border-radius: 4px;
    margin: 1rem 0;
}

.api-endpoint code {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: #e83e8c;
}

.api-features {
    list-style: none;
    padding: 0;
    margin: 1rem 0;
}

.api-features li {
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

.api-features li i {
    color: #2ecc71;
    margin-right: 0.5rem;
}

.code-block {
    background-color: #2c3e50;
    color: #fff;
    padding: 1rem;
    border-radius: 4px;
    margin: 1rem 0;
    overflow-x: auto;
}

.code-block pre {
    margin: 0;
}

.code-block code {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .api-docs-container {
        flex-direction: column;
    }
    
    .api-docs-sidebar {
        width: 100%;
    }
}
