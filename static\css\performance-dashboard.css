/* Performance Dashboard Styles */

:root {
    --primary-color: #0078d4;
    --secondary-color: #00a2ed;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;
    --border-color: #e9ecef;
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

/* Dashboard Actions */
.dashboard-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.refresh-control {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background-color: var(--gray-100);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
    border: 1px solid var(--gray-300);
}

.refresh-control label {
    margin-bottom: 0;
    font-weight: 500;
    color: var(--gray-700);
}

.refresh-control select {
    border: none;
    background-color: transparent;
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-sm);
    color: var(--gray-800);
    font-weight: 500;
}

/* Metrics Grid */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
}

.metric-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background-color: white;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.metric-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.metric-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.metric-content {
    flex: 1;
}

.metric-content h3 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-700);
    margin: 0 0 0.25rem 0;
}

.metric-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
    margin: 0 0 0.5rem 0;
}

/* Tools Grid */
.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.tool-card {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    background-color: white;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.tool-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.tool-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--primary-color);
    color: white;
    font-size: 1.25rem;
}

.tool-content {
    flex: 1;
}

.tool-content h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0 0 0.5rem 0;
}

.tool-content p {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin: 0 0 1rem 0;
}

.tool-btn {
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
}

/* Charts */
.card {
    border: none;
    border-radius: var(--radius-md);
    overflow: hidden;
}

.card-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--gray-200);
}

.card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-800);
}

.card-body {
    padding: 1.5rem;
}

/* Tables */
.table {
    margin-bottom: 0;
}

.table th {
    font-weight: 600;
    color: var(--gray-700);
    border-top: none;
    padding: 1rem 1.5rem;
}

.table td {
    padding: 1rem 1.5rem;
    vertical-align: middle;
}

.table-light {
    background-color: var(--gray-100);
}

/* Badges */
.badge {
    padding: 0.35em 0.65em;
    font-weight: 600;
    font-size: 0.75em;
}

/* Buttons */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #0069c0;
    border-color: #0069c0;
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Modal */
.modal-content {
    border: none;
    border-radius: var(--radius-md);
}

.modal-header {
    border-bottom: 1px solid var(--gray-200);
    padding: 1.25rem 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid var(--gray-200);
    padding: 1.25rem 1.5rem;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .metrics-grid,
    .tools-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .dashboard-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .refresh-control {
        width: 100%;
    }
    
    .metrics-grid,
    .tools-grid {
        grid-template-columns: 1fr;
    }
    
    .card-header {
        padding: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .table th,
    .table td {
        padding: 0.75rem 1rem;
    }
}
