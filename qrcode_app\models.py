from django.db import models
from django.contrib.auth.models import User
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
import uuid
import os

def qr_code_upload_path(instance, filename):
    """Generate a unique path for uploaded QR codes"""
    ext = filename.split('.')[-1]
    filename = f"{uuid.uuid4()}.{ext}"
    return os.path.join('qr_codes', filename)

def file_upload_path(instance, filename):
    """Generate a unique path for uploaded files"""
    ext = filename.split('.')[-1]
    filename = f"{uuid.uuid4()}.{ext}"
    return os.path.join('uploaded_files', instance.qr_type.lower(), filename)

class QRCode(models.Model):
    """Model for storing QR code data"""

    class QRCodeType(models.TextChoices):
        URL = 'URL', _('URL')
        TEXT = 'TEXT', _('Text')
        VCARD = 'VCARD', _('vCard')
        WIFI = 'WIFI', _('WiFi')
        EMAIL = 'EMAIL', _('Email')
        PHONE = 'PHONE', _('Phone')
        LOCATION = 'LOCATION', _('Location')
        PDF = 'PDF', _('PDF Document')
        IMAGE = 'IMAGE', _('Image')
        DOCUMENT = 'DOCUMENT', _('Document')
        AUDIO = 'AUDIO', _('Audio File')
        VIDEO = 'VIDEO', _('Video File')
        CALENDAR = 'CALENDAR', _('Calendar Event')
        PRODUCT = 'PRODUCT', _('Product Information')
        APP = 'APP', _('App Download')

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='qr_codes')
    name = models.CharField(max_length=100)
    qr_type = models.CharField(max_length=20, choices=QRCodeType.choices, default=QRCodeType.URL)
    data = models.TextField()
    original_url = models.TextField(blank=True, null=True, help_text="Original destination URL before landing page")
    unique_id = models.UUIDField(default=uuid.uuid4, unique=True, help_text="Unique identifier for QR landing page")

    # Dynamic redirect fields for MODULE 2
    short_code = models.CharField(max_length=20, unique=True, blank=True, null=True, help_text="Short code for direct redirects (e.g., 'abc123')")
    target_url = models.URLField(blank=True, null=True, help_text="Current target URL for dynamic redirects")
    image = models.ImageField(upload_to=qr_code_upload_path, blank=True, null=True)
    foreground_color = models.CharField(max_length=7, default='#000000')
    background_color = models.CharField(max_length=7, default='#FFFFFF')
    logo = models.ImageField(upload_to='qr_logos', blank=True, null=True)
    uploaded_file = models.FileField(upload_to=file_upload_path, blank=True, null=True,
                                    help_text="Upload a file for PDF, Image, Document, Audio, or Video QR codes")
    file_name = models.CharField(max_length=255, blank=True, null=True)
    file_size = models.PositiveIntegerField(blank=True, null=True, help_text="File size in bytes")
    file_type = models.CharField(max_length=100, blank=True, null=True, help_text="MIME type of the file")
    is_encrypted = models.BooleanField(default=False)

    # Advanced customization fields
    dot_style = models.CharField(max_length=50, default='square', blank=True, null=True,
                                help_text="QR code dot style (square, dots, rounded, classy, etc.)")
    corner_style = models.CharField(max_length=50, default='square', blank=True, null=True,
                                   help_text="QR code corner style (square, dot, rounded, extra-rounded, diamond)")
    frame_style = models.CharField(max_length=50, default='none', blank=True, null=True,
                                  help_text="QR code frame style (none, corporate, phone, business-card, ticket, elegant, modern)")
    title_text = models.CharField(max_length=200, blank=True, null=True,
                                 help_text="Title text to display above QR code")
    guiding_text = models.CharField(max_length=200, blank=True, null=True,
                                   help_text="Guiding text to display below QR code")
    guiding_text_position = models.CharField(max_length=20, default='below', blank=True, null=True,
                                            help_text="Position of guiding text (below, above, left, right)")

    # Enterprise features: Status and Expiry
    class QRStatus(models.TextChoices):
        ACTIVE = 'active', _('Active')
        DISABLED = 'disabled', _('Disabled')
        EXPIRED = 'expired', _('Expired')
        PENDING = 'pending', _('Pending Approval')
        REJECTED = 'rejected', _('Rejected')

    status = models.CharField(max_length=20, choices=QRStatus.choices, default=QRStatus.ACTIVE)
    expires_at = models.DateTimeField(blank=True, null=True, help_text="QR code expiry date/time")
    max_scans = models.PositiveIntegerField(blank=True, null=True, help_text="Maximum number of scans allowed")

    # Approval workflow
    requires_approval = models.BooleanField(default=False, help_text="Requires admin approval before activation")
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, blank=True, null=True, related_name='approved_qr_codes')
    approved_at = models.DateTimeField(blank=True, null=True)
    rejection_reason = models.TextField(blank=True, null=True, help_text="Reason for rejection")

    # Branding
    branding = models.ForeignKey('QRCodeBranding', on_delete=models.SET_NULL, blank=True, null=True, help_text="Custom branding for this QR code")

    # Geo-targeting (Enterprise feature)
    enable_geo_targeting = models.BooleanField(default=False, help_text="Enable different URLs for different countries")
    default_redirect_url = models.URLField(blank=True, null=True, help_text="Default URL when no geo-target matches")

    # Security controls (Enterprise feature)
    block_vpn_scans = models.BooleanField(default=False, help_text="Block scans from VPN/proxy connections")
    block_tor_scans = models.BooleanField(default=False, help_text="Block scans from Tor network")
    blocked_countries = models.JSONField(default=list, blank=True, help_text="List of blocked country codes")
    blocked_organizations = models.JSONField(default=list, blank=True, help_text="List of blocked organization patterns")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'QR Code'
        verbose_name_plural = 'QR Codes'
        permissions = [
            ('view_performance_dashboard', 'Can view performance dashboard'),
        ]

    def __str__(self):
        return self.name

    def is_active(self):
        """Check if QR code is active and accessible"""
        if self.status != self.QRStatus.ACTIVE:
            return False

        # Check expiry
        if self.expires_at and timezone.now() > self.expires_at:
            self.status = self.QRStatus.EXPIRED
            self.save()
            return False

        # Check max scans
        if self.max_scans:
            total_scans = getattr(self, 'analytics', None)
            if total_scans and total_scans.total_scans >= self.max_scans:
                self.status = self.QRStatus.DISABLED
                self.save()
                return False

        # Check approval requirement
        if self.requires_approval and not self.approved_at:
            return False

        return True

    def get_target_url(self):
        """Get the current target URL for dynamic redirects"""
        # Check for dynamic redirect first (premium feature)
        if hasattr(self, 'dynamic_redirect') and self.dynamic_redirect.is_active:
            return self.dynamic_redirect.current_url

        # Fall back to target_url field (simple redirect)
        if self.target_url:
            return self.target_url

        # Fall back to original URL or data
        return self.original_url or self.data

    def generate_short_code(self):
        """Generate a unique short code for this QR code"""
        if self.short_code:
            return self.short_code

        import random
        import string

        # Generate a random 6-character code
        while True:
            code = ''.join(random.choices(string.ascii_lowercase + string.digits, k=6))
            if not QRCode.objects.filter(short_code=code).exists():
                self.short_code = code
                self.save()
                return code

    def get_short_redirect_url(self, request=None):
        """Get the short redirect URL for this QR code"""
        if not self.short_code:
            self.generate_short_code()

        if request:
            return request.build_absolute_uri(f'/r/{self.short_code}/')
        return f'/r/{self.short_code}/'

    def approve(self, approved_by_user):
        """Approve the QR code"""
        self.status = self.QRStatus.ACTIVE
        self.approved_by = approved_by_user
        self.approved_at = timezone.now()
        self.rejection_reason = None
        self.save()

    def reject(self, rejected_by_user, reason):
        """Reject the QR code"""
        self.status = self.QRStatus.REJECTED
        self.approved_by = rejected_by_user
        self.approved_at = timezone.now()
        self.rejection_reason = reason
        self.save()


class QRGeoTarget(models.Model):
    """Model for geo-targeted redirects (Enterprise feature)"""

    qr_code = models.ForeignKey(QRCode, on_delete=models.CASCADE, related_name='geo_targets')
    country_code = models.CharField(max_length=2, help_text="ISO 2-letter country code (e.g., 'US', 'GB')")
    redirect_url = models.URLField(help_text="URL to redirect users from this country")
    priority = models.PositiveIntegerField(default=1, help_text="Priority order (lower number = higher priority)")
    is_active = models.BooleanField(default=True)

    # Optional region targeting
    region = models.CharField(max_length=100, blank=True, null=True, help_text="Specific region/state within country")
    city = models.CharField(max_length=100, blank=True, null=True, help_text="Specific city within country")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'QR Geo Target'
        verbose_name_plural = 'QR Geo Targets'
        ordering = ['priority', 'country_code']
        unique_together = ['qr_code', 'country_code', 'region', 'city']

    def __str__(self):
        location = self.country_code
        if self.region:
            location += f", {self.region}"
        if self.city:
            location += f", {self.city}"
        return f"{self.qr_code.name} → {location}"


class UserProfile(models.Model):
    """Extended user profile model"""

    class UserRole(models.TextChoices):
        GUEST = 'guest', _('Guest')
        USER = 'user', _('User')
        PREMIUM = 'premium', _('Premium User')
        ADMIN = 'admin', _('Administrator')
        SUPERADMIN = 'superadmin', _('Super Administrator')

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    role = models.CharField(max_length=20, choices=UserRole.choices, default=UserRole.USER)
    company = models.CharField(max_length=100, blank=True, null=True)
    phone = models.CharField(max_length=20, blank=True, null=True)
    address = models.TextField(blank=True, null=True)
    profile_image = models.ImageField(upload_to='profile_images', blank=True, null=True)
    api_key = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.user.username

    def is_admin(self):
        """Check if user has admin role"""
        return self.role in ['admin', 'superadmin']

    def is_superadmin(self):
        """Check if user has superadmin role"""
        return self.role == 'superadmin'

    def can_view_performance_dashboard(self):
        """Check if user can view performance dashboard"""
        return self.is_admin() or self.user.has_perm('qrcode_app.view_performance_dashboard')

    def can_manage_users(self):
        """Check if user can manage users"""
        return self.is_admin()

    def can_add_users(self):
        """Check if user can add users"""
        return self.is_admin()

    def can_edit_users(self):
        """Check if user can edit users"""
        return self.is_admin()

    def can_delete_users(self):
        """Check if user can delete users"""
        return self.is_superadmin()

    def is_premium(self):
        """Check if user has premium access"""
        return self.role in ['premium', 'admin', 'superadmin']

    def can_access_batch_processing(self):
        """Check if user can access batch processing"""
        return self.is_premium()

    def save(self, *args, **kwargs):
        """Override save to handle permissions"""
        is_new = self.pk is None

        # Check if a profile already exists for this user
        if is_new and UserProfile.objects.filter(user=self.user).exists():
            # Update the existing profile instead of creating a new one
            existing_profile = UserProfile.objects.get(user=self.user)
            existing_profile.role = self.role
            existing_profile.company = self.company
            existing_profile.phone = self.phone
            existing_profile.update_user_permissions()
            return existing_profile

        super().save(*args, **kwargs)

        # Ensure user has appropriate permissions based on role
        self.update_user_permissions()

    def update_user_permissions(self):
        """Update user permissions based on role"""
        from django.contrib.auth.models import Group, Permission
        from django.contrib.contenttypes.models import ContentType

        # Get or create role-based groups
        admin_group, _ = Group.objects.get_or_create(name='Administrators')
        superadmin_group, _ = Group.objects.get_or_create(name='SuperAdministrators')
        premium_group, _ = Group.objects.get_or_create(name='PremiumUsers')
        user_group, _ = Group.objects.get_or_create(name='RegularUsers')

        # Get the performance dashboard permission
        try:
            content_type = ContentType.objects.get_for_model(QRCode)
            performance_perm = Permission.objects.get(
                codename='view_performance_dashboard',
                content_type=content_type,
            )

            # Assign permission to admin and superadmin groups
            if performance_perm not in admin_group.permissions.all():
                admin_group.permissions.add(performance_perm)
            if performance_perm not in superadmin_group.permissions.all():
                superadmin_group.permissions.add(performance_perm)
        except (ContentType.DoesNotExist, Permission.DoesNotExist):
            pass

        # Clear existing groups
        for group in self.user.groups.all():
            self.user.groups.remove(group)

        # Assign user to appropriate group based on role
        if self.role == 'superadmin':
            self.user.groups.add(superadmin_group)
            self.user.groups.add(admin_group)  # Superadmins also have admin permissions
            self.user.is_staff = True
            self.user.is_superuser = True
        elif self.role == 'admin':
            self.user.groups.add(admin_group)
            self.user.is_staff = True
            self.user.is_superuser = False
        elif self.role == 'premium':
            self.user.groups.add(premium_group)
            self.user.is_staff = False
            self.user.is_superuser = False
        else:  # 'user' or 'guest'
            self.user.groups.add(user_group)
            self.user.is_staff = False
            self.user.is_superuser = False

        # Save the user to apply changes
        self.user.save()

class APIKey(models.Model):
    """Model for API keys"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='api_keys')
    name = models.CharField(max_length=100)
    key = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    last_used = models.DateTimeField(blank=True, null=True)

    def __str__(self):
        return f"{self.name} ({self.user.username})"

class QRCodeBatch(models.Model):
    """Model for batch QR code generation"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='qr_batches')
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    zip_file = models.FileField(upload_to='batch_zips', blank=True, null=True)
    count = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name

class QRCodeBranding(models.Model):
    """Model for QR code branding and theming (enterprise feature)"""

    class ThemeStyle(models.TextChoices):
        MINIMAL = 'minimal', _('Minimal')
        CORPORATE = 'corporate', _('Corporate')
        MODERN = 'modern', _('Modern')
        CUSTOM = 'custom', _('Custom')

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='qr_brandings')
    name = models.CharField(max_length=100, help_text="Brand name (e.g., 'Company Logo')")

    # Visual branding
    logo_url = models.URLField(blank=True, null=True, help_text="URL to brand logo")
    logo_image = models.ImageField(upload_to='brand_logos/', blank=True, null=True, help_text="Upload brand logo")
    primary_color = models.CharField(max_length=7, default="#000000", help_text="Primary brand color (hex)")
    secondary_color = models.CharField(max_length=7, default="#ffffff", help_text="Secondary brand color (hex)")
    accent_color = models.CharField(max_length=7, default="#007bff", help_text="Accent color for buttons/links")

    # Theme settings
    theme_style = models.CharField(max_length=20, choices=ThemeStyle.choices, default=ThemeStyle.MINIMAL)
    custom_css = models.TextField(blank=True, null=True, help_text="Custom CSS for advanced styling")

    # Text customization
    company_name = models.CharField(max_length=100, blank=True, null=True)
    tagline = models.CharField(max_length=200, blank=True, null=True)
    footer_text = models.CharField(max_length=200, blank=True, null=True)

    # Landing page settings
    show_powered_by = models.BooleanField(default=True, help_text="Show 'Powered by QR Generator'")
    redirect_delay = models.PositiveIntegerField(default=3, help_text="Redirect delay in seconds")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'QR Code Branding'
        verbose_name_plural = 'QR Code Brandings'
        unique_together = ['user', 'name']

    def __str__(self):
        return f"{self.name} - {self.user.username}"

    def get_logo_url(self):
        """Get the logo URL, preferring uploaded image over URL"""
        if self.logo_image:
            return self.logo_image.url
        return self.logo_url


class QRLink(models.Model):
    """Model for short URL engine (enterprise feature)"""

    class LinkStatus(models.TextChoices):
        ACTIVE = 'active', _('Active')
        DISABLED = 'disabled', _('Disabled')
        EXPIRED = 'expired', _('Expired')
        PENDING = 'pending', _('Pending Approval')

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='qr_links')
    qr_code = models.OneToOneField(QRCode, on_delete=models.CASCADE, related_name='short_link', blank=True, null=True)

    # Short URL components
    code = models.SlugField(unique=True, max_length=20, help_text="Short code (e.g., 'abc123')")
    target_url = models.URLField(help_text="Destination URL")
    custom_domain = models.CharField(max_length=100, blank=True, null=True, help_text="Custom domain (e.g., 'go.company.com')")

    # Enterprise features
    title = models.CharField(max_length=200, blank=True, null=True, help_text="Link title for analytics")
    description = models.TextField(blank=True, null=True, help_text="Link description")
    branding = models.ForeignKey(QRCodeBranding, on_delete=models.SET_NULL, blank=True, null=True, help_text="Custom branding for landing page")

    # Access control
    status = models.CharField(max_length=20, choices=LinkStatus.choices, default=LinkStatus.ACTIVE)
    password = models.CharField(max_length=100, blank=True, null=True, help_text="Password protection")
    expires_at = models.DateTimeField(blank=True, null=True, help_text="Expiry date/time")
    max_clicks = models.PositiveIntegerField(blank=True, null=True, help_text="Maximum number of clicks")

    # Analytics
    click_count = models.PositiveIntegerField(default=0)
    last_clicked = models.DateTimeField(blank=True, null=True)

    # Approval workflow
    requires_approval = models.BooleanField(default=False)
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, blank=True, null=True, related_name='approved_links')
    approved_at = models.DateTimeField(blank=True, null=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'QR Short Link'
        verbose_name_plural = 'QR Short Links'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.get_short_url()} → {self.target_url}"

    def get_short_url(self):
        """Get the complete short URL"""
        domain = self.custom_domain or 'qr.local'  # Default domain
        return f"https://{domain}/{self.code}"

    def is_active(self):
        """Check if the link is active and accessible"""
        if self.status != self.LinkStatus.ACTIVE:
            return False

        # Check expiry
        if self.expires_at and timezone.now() > self.expires_at:
            self.status = self.LinkStatus.EXPIRED
            self.save()
            return False

        # Check max clicks
        if self.max_clicks and self.click_count >= self.max_clicks:
            self.status = self.LinkStatus.DISABLED
            self.save()
            return False

        # Check approval requirement
        if self.requires_approval and not self.approved_at:
            return False

        return True

    def increment_clicks(self):
        """Increment click count and update last clicked"""
        self.click_count += 1
        self.last_clicked = timezone.now()
        self.save()


class QRCodeAnalytics(models.Model):
    """Model for QR code analytics"""
    qr_code = models.OneToOneField(QRCode, on_delete=models.CASCADE, related_name='analytics')
    total_scans = models.PositiveIntegerField(default=0)
    unique_scans = models.PositiveIntegerField(default=0)
    last_scanned = models.DateTimeField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'QR Code Analytics'
        verbose_name_plural = 'QR Code Analytics'

    def __str__(self):
        return f"Analytics for {self.qr_code.name}"

class QRCodeScan(models.Model):
    """Model for individual QR code scans"""

    class ScannerType(models.TextChoices):
        NATIVE_CAMERA = 'native_camera', _('Native Camera')
        THIRD_PARTY_APP = 'third_party_app', _('Third Party App')
        UNKNOWN = 'unknown', _('Unknown')

    qr_code = models.ForeignKey(QRCode, on_delete=models.CASCADE, related_name='scans')
    ip_address = models.GenericIPAddressField(blank=True, null=True)
    user_agent = models.TextField(blank=True, null=True)
    referrer = models.URLField(blank=True, null=True)

    # Legacy location field (kept for backward compatibility)
    location = models.CharField(max_length=255, blank=True, null=True)

    # Enhanced geo-location fields
    country = models.CharField(max_length=100, blank=True, null=True)
    city = models.CharField(max_length=100, blank=True, null=True)
    region = models.CharField(max_length=100, blank=True, null=True)
    latitude = models.DecimalField(max_digits=10, decimal_places=7, blank=True, null=True)
    longitude = models.DecimalField(max_digits=10, decimal_places=7, blank=True, null=True)
    timezone = models.CharField(max_length=50, blank=True, null=True)
    postal_code = models.CharField(max_length=20, blank=True, null=True)

    # Organization/ISP tracking (enterprise feature)
    organization = models.CharField(max_length=255, blank=True, null=True, help_text="ISP or organization name from IPinfo")
    asn = models.CharField(max_length=50, blank=True, null=True, help_text="Autonomous System Number")
    privacy_flags = models.JSONField(default=dict, blank=True, help_text="Privacy flags (VPN, proxy, etc.)")

    # Scanner detection
    scanner_type = models.CharField(max_length=20, choices=ScannerType.choices, default=ScannerType.UNKNOWN)

    # Device information
    device_type = models.CharField(max_length=50, blank=True, null=True)
    os = models.CharField(max_length=50, blank=True, null=True)
    browser = models.CharField(max_length=50, blank=True, null=True)

    scanned_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-scanned_at']

    def __str__(self):
        return f"Scan of {self.qr_code.name} at {self.scanned_at}"

    def get_location_display(self):
        """Return formatted location string"""
        parts = []
        if self.city:
            parts.append(self.city)
        if self.region:
            parts.append(self.region)
        if self.country:
            parts.append(self.country)
        return ', '.join(parts) if parts else self.location or 'Unknown'


class QRScanLog(models.Model):
    """
    Simple QR scan logging model with IPinfo fields
    This provides a lightweight logging layer in addition to the comprehensive QRCodeScan model
    """
    code = models.CharField(max_length=50, help_text="QR code identifier or unique_id")
    timestamp = models.DateTimeField(auto_now_add=True)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    country = models.CharField(max_length=2, blank=True, null=True, help_text="ISO country code")
    city = models.CharField(max_length=100, blank=True, null=True)
    org = models.CharField(max_length=255, blank=True, null=True, help_text="Organization/ISP from IPinfo")
    latitude = models.FloatField(blank=True, null=True, help_text="Latitude from IPinfo geolocation")
    longitude = models.FloatField(blank=True, null=True, help_text="Longitude from IPinfo geolocation")

    class Meta:
        ordering = ['-timestamp']
        verbose_name = 'QR Scan Log'
        verbose_name_plural = 'QR Scan Logs'

    def __str__(self):
        return f"{self.code} from {self.city}, {self.country} @ {self.timestamp}"


class DynamicQRRedirect(models.Model):
    """
    Dynamic QR redirect model for monetization - allows users to change QR destination without reprinting
    This is a premium feature that enables "edit after print" functionality
    """
    qr_code = models.OneToOneField(QRCode, on_delete=models.CASCADE, related_name='dynamic_redirect')
    current_url = models.URLField(help_text="Current destination URL")
    backup_url = models.URLField(blank=True, null=True, help_text="Backup URL if primary fails")

    # Monetization features
    is_active = models.BooleanField(default=True, help_text="Enable/disable dynamic redirects")
    redirect_count = models.PositiveIntegerField(default=0, help_text="Number of times URL has been changed")
    max_redirects = models.PositiveIntegerField(default=10, help_text="Maximum URL changes allowed")

    # Analytics
    total_clicks = models.PositiveIntegerField(default=0)
    last_accessed = models.DateTimeField(blank=True, null=True)

    # Premium features
    enable_analytics = models.BooleanField(default=False, help_text="Track detailed click analytics")
    enable_geo_redirect = models.BooleanField(default=False, help_text="Different URLs for different countries")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Dynamic QR Redirect'
        verbose_name_plural = 'Dynamic QR Redirects'

    def __str__(self):
        return f"Dynamic redirect for {self.qr_code.name}"

    def can_change_url(self):
        """Check if user can still change the URL"""
        return self.redirect_count < self.max_redirects

    def change_url(self, new_url):
        """Change the destination URL"""
        if not self.can_change_url():
            raise ValueError("Maximum redirect changes exceeded")

        self.current_url = new_url
        self.redirect_count += 1
        self.save()

    def get_redirect_url(self, request=None):
        """Get the appropriate redirect URL based on context"""
        if not self.is_active:
            return self.qr_code.data  # Fall back to original QR data

        # TODO: Add geo-targeting logic here
        return self.current_url


class AILandingPage(models.Model):
    """
    MODULE 3: AI-generated landing pages for QR codes
    Allows users to create beautiful, tailored landing pages using AI
    """

    class PageType(models.TextChoices):
        EVENT = 'EVENT', _('Event Invitation')
        PRODUCT = 'PRODUCT', _('Product Showcase')
        MENU = 'MENU', _('Restaurant Menu')
        BUSINESS = 'BUSINESS', _('Business Card')
        PORTFOLIO = 'PORTFOLIO', _('Portfolio/Resume')
        ANNOUNCEMENT = 'ANNOUNCEMENT', _('Announcement')
        CONTACT = 'CONTACT', _('Contact Information')
        CUSTOM = 'CUSTOM', _('Custom Page')

    class PageStatus(models.TextChoices):
        GENERATING = 'GENERATING', _('Generating')
        ACTIVE = 'ACTIVE', _('Active')
        DRAFT = 'DRAFT', _('Draft')
        ARCHIVED = 'ARCHIVED', _('Archived')

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='ai_landing_pages')
    qr_code = models.OneToOneField(QRCode, on_delete=models.CASCADE, related_name='ai_landing_page')

    # Page content
    title = models.CharField(max_length=255, help_text="Page title")
    page_type = models.CharField(max_length=20, choices=PageType.choices, default=PageType.CUSTOM)
    prompt = models.TextField(help_text="Original user prompt for AI generation")
    content = models.TextField(help_text="AI-generated HTML content")

    # Styling and customization
    primary_color = models.CharField(max_length=7, default='#667eea', help_text="Primary color for the page")
    secondary_color = models.CharField(max_length=7, default='#764ba2', help_text="Secondary color for the page")
    font_family = models.CharField(max_length=50, default='Inter', help_text="Font family for the page")

    # AI generation metadata
    ai_model_used = models.CharField(max_length=50, blank=True, null=True, help_text="AI model used for generation")
    generation_time = models.FloatField(blank=True, null=True, help_text="Time taken to generate (seconds)")

    # Status and management
    status = models.CharField(max_length=20, choices=PageStatus.choices, default=PageStatus.GENERATING)
    is_published = models.BooleanField(default=True, help_text="Whether the page is publicly accessible")
    view_count = models.PositiveIntegerField(default=0, help_text="Number of times the page has been viewed")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    published_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        verbose_name = 'AI Landing Page'
        verbose_name_plural = 'AI Landing Pages'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username}'s AI Page: {self.title}"

    def get_absolute_url(self):
        """Get the URL for viewing this AI landing page"""
        return f'/ai/{self.qr_code.short_code or self.qr_code.unique_id}/'

    def increment_view_count(self):
        """Increment the view count for analytics"""
        self.view_count += 1
        self.save(update_fields=['view_count'])

    def publish(self):
        """Publish the landing page"""
        self.status = self.PageStatus.ACTIVE
        self.is_published = True
        self.published_at = timezone.now()
        self.save()

    def get_page_type_display_icon(self):
        """Get an icon for the page type"""
        icons = {
            'EVENT': 'fas fa-calendar-alt',
            'PRODUCT': 'fas fa-box',
            'MENU': 'fas fa-utensils',
            'BUSINESS': 'fas fa-briefcase',
            'PORTFOLIO': 'fas fa-user',
            'ANNOUNCEMENT': 'fas fa-bullhorn',
            'CONTACT': 'fas fa-address-card',
            'CUSTOM': 'fas fa-magic',
        }
        return icons.get(self.page_type, 'fas fa-file')


class ScanEvent(models.Model):
    """
    MODULE 4: Smart Scan Alerts - Track individual QR scan events
    Enhanced version of QRScanLog with alert integration
    """
    qr_code = models.ForeignKey(QRCode, on_delete=models.CASCADE, related_name='scan_events')
    timestamp = models.DateTimeField(auto_now_add=True)
    ip = models.GenericIPAddressField()
    location = models.CharField(max_length=255, blank=True, help_text="City, Country from IPinfo")
    org = models.CharField(max_length=255, blank=True, help_text="Organization/ISP from IPinfo")
    user_agent = models.TextField(blank=True)

    # Additional tracking fields
    device_type = models.CharField(max_length=50, blank=True, help_text="Mobile, Desktop, Tablet")
    browser = models.CharField(max_length=50, blank=True)
    country_code = models.CharField(max_length=2, blank=True, help_text="ISO country code")

    # Alert processing
    alerts_triggered = models.PositiveIntegerField(default=0, help_text="Number of alerts triggered by this scan")
    processed_for_alerts = models.BooleanField(default=False, help_text="Whether this scan has been processed for alerts")

    class Meta:
        verbose_name = 'Scan Event'
        verbose_name_plural = 'Scan Events'
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['qr_code', '-timestamp']),
            models.Index(fields=['ip', '-timestamp']),
            models.Index(fields=['processed_for_alerts']),
        ]

    def __str__(self):
        return f"Scan {self.qr_code.name} @ {self.timestamp} from {self.location}"

    def get_searchable_content(self):
        """Get all content that can be searched for alert keywords"""
        return f"{self.location} {self.org} {self.user_agent} {self.device_type} {self.browser} {self.country_code}".lower()


class ScanAlert(models.Model):
    """
    MODULE 4: Smart Scan Alerts - Define alert triggers for QR scan events
    """

    class AlertType(models.TextChoices):
        KEYWORD = 'KEYWORD', _('Keyword Match')
        LOCATION = 'LOCATION', _('Location Based')
        FREQUENCY = 'FREQUENCY', _('Scan Frequency')
        DEVICE = 'DEVICE', _('Device Type')
        ORGANIZATION = 'ORGANIZATION', _('Organization/ISP')

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='scan_alerts')
    qr_code = models.ForeignKey(QRCode, on_delete=models.CASCADE, null=True, blank=True,
                               help_text="Specific QR code to monitor (leave blank for all QR codes)")

    # Alert configuration
    alert_type = models.CharField(max_length=20, choices=AlertType.choices, default=AlertType.KEYWORD)
    keyword = models.CharField(max_length=255, help_text="Keyword to match in location, org, user agent, etc.")
    email = models.EmailField(help_text="Email address to send alerts to")

    # Alert settings
    is_active = models.BooleanField(default=True, help_text="Enable/disable this alert")
    send_immediately = models.BooleanField(default=True, help_text="Send alert immediately or batch daily")
    max_alerts_per_day = models.PositiveIntegerField(default=10, help_text="Maximum alerts to send per day")

    # Statistics
    triggered_count = models.PositiveIntegerField(default=0, help_text="Total number of times this alert has been triggered")
    last_triggered = models.DateTimeField(blank=True, null=True)
    alerts_sent_today = models.PositiveIntegerField(default=0, help_text="Alerts sent today (resets daily)")
    last_reset_date = models.DateField(auto_now_add=True, help_text="Last date when daily counter was reset")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Scan Alert'
        verbose_name_plural = 'Scan Alerts'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['qr_code', 'is_active']),
            models.Index(fields=['keyword']),
        ]

    def __str__(self):
        qr_name = self.qr_code.name if self.qr_code else "All QR Codes"
        return f"Alert for '{self.keyword}' on {qr_name} → {self.email}"

    def can_send_alert(self):
        """Check if we can send an alert (not exceeding daily limit)"""
        from django.utils import timezone
        today = timezone.now().date()

        # Reset daily counter if it's a new day
        if self.last_reset_date < today:
            self.alerts_sent_today = 0
            self.last_reset_date = today
            self.save(update_fields=['alerts_sent_today', 'last_reset_date'])

        return self.is_active and self.alerts_sent_today < self.max_alerts_per_day

    def record_alert_sent(self):
        """Record that an alert was sent"""
        from django.utils import timezone
        self.triggered_count += 1
        self.alerts_sent_today += 1
        self.last_triggered = timezone.now()
        self.save(update_fields=['triggered_count', 'alerts_sent_today', 'last_triggered'])

    def matches_scan_event(self, scan_event):
        """Check if this alert should trigger for the given scan event"""
        if not self.is_active:
            return False

        # Check QR code filter
        if self.qr_code and self.qr_code != scan_event.qr_code:
            return False

        # Check keyword match
        searchable_content = scan_event.get_searchable_content()
        return self.keyword.lower() in searchable_content


class WebhookEndpoint(models.Model):
    """
    MODULE 5: Webhook Integration - Send scan data to external services (Zapier, CRM, etc.)
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='webhook_endpoints')
    qr_code = models.ForeignKey(QRCode, on_delete=models.CASCADE, null=True, blank=True,
                               help_text="Specific QR code to monitor (leave blank for all QR codes)")
    url = models.URLField(help_text="Zapier/CRM webhook URL to send scan data to")

    # Trigger settings
    trigger_on_scan = models.BooleanField(default=True, help_text="Send webhook when QR is scanned")
    trigger_on_alert = models.BooleanField(default=False, help_text="Send webhook when scan alert is triggered")

    # Configuration
    active = models.BooleanField(default=True, help_text="Enable/disable this webhook")
    secret_key = models.CharField(max_length=255, blank=True, help_text="Optional secret key for webhook verification")

    # Statistics
    total_calls = models.PositiveIntegerField(default=0, help_text="Total webhook calls made")
    successful_calls = models.PositiveIntegerField(default=0, help_text="Successful webhook calls")
    last_called = models.DateTimeField(blank=True, null=True)
    last_error = models.TextField(blank=True, help_text="Last error message if webhook failed")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Webhook Endpoint'
        verbose_name_plural = 'Webhook Endpoints'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'active']),
            models.Index(fields=['qr_code', 'active']),
        ]

    def __str__(self):
        qr_name = self.qr_code.name if self.qr_code else "All QR Codes"
        return f"{self.user.username} webhook to {self.url[:50]}... for {qr_name}"

    def get_success_rate(self):
        """Calculate webhook success rate percentage"""
        if self.total_calls == 0:
            return 0
        return round((self.successful_calls / self.total_calls) * 100, 1)

    def record_call_success(self):
        """Record a successful webhook call"""
        from django.utils import timezone
        self.total_calls += 1
        self.successful_calls += 1
        self.last_called = timezone.now()
        self.last_error = ''
        self.save(update_fields=['total_calls', 'successful_calls', 'last_called', 'last_error'])

    def record_call_failure(self, error_message):
        """Record a failed webhook call"""
        from django.utils import timezone
        self.total_calls += 1
        self.last_called = timezone.now()
        self.last_error = str(error_message)[:500]  # Truncate long error messages
        self.save(update_fields=['total_calls', 'last_called', 'last_error'])


class Plan(models.Model):
    """
    MODULE 6: Usage Limits & Tiered Features - Subscription plans with feature limits
    """

    class PlanType(models.TextChoices):
        FREE = 'FREE', _('Free')
        PRO = 'PRO', _('Pro')
        ENTERPRISE = 'ENTERPRISE', _('Enterprise')

    name = models.CharField(max_length=50, unique=True)
    plan_type = models.CharField(max_length=20, choices=PlanType.choices, default=PlanType.FREE)
    description = models.TextField(blank=True, help_text="Plan description for users")

    # QR Code Limits
    max_qr_codes = models.IntegerField(default=5, help_text="Maximum QR codes user can create")
    max_scans_per_month = models.IntegerField(default=100, help_text="Maximum scans per month")
    max_dynamic_redirects = models.IntegerField(default=0, help_text="Maximum dynamic redirects")
    max_ai_pages = models.IntegerField(default=0, help_text="Maximum AI landing pages")
    max_webhooks = models.IntegerField(default=0, help_text="Maximum webhook endpoints")
    max_scan_alerts = models.IntegerField(default=0, help_text="Maximum scan alerts")

    # Feature Flags
    ai_enabled = models.BooleanField(default=False, help_text="Enable AI landing pages")
    alerts_enabled = models.BooleanField(default=False, help_text="Enable scan alerts")
    webhooks_enabled = models.BooleanField(default=False, help_text="Enable webhook integration")
    analytics_enabled = models.BooleanField(default=True, help_text="Enable basic analytics")
    advanced_analytics_enabled = models.BooleanField(default=False, help_text="Enable advanced analytics")
    branding_enabled = models.BooleanField(default=False, help_text="Enable custom branding")
    batch_processing_enabled = models.BooleanField(default=False, help_text="Enable batch QR generation")
    api_access_enabled = models.BooleanField(default=False, help_text="Enable API access")
    priority_support_enabled = models.BooleanField(default=False, help_text="Enable priority support")

    # Pricing
    price = models.DecimalField(max_digits=8, decimal_places=2, default=0.00, help_text="Monthly price in USD")
    yearly_price = models.DecimalField(max_digits=8, decimal_places=2, default=0.00, help_text="Yearly price in USD")

    # Plan Settings
    is_active = models.BooleanField(default=True, help_text="Is this plan available for subscription")
    is_default = models.BooleanField(default=False, help_text="Default plan for new users")
    sort_order = models.PositiveIntegerField(default=0, help_text="Display order on pricing page")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Subscription Plan'
        verbose_name_plural = 'Subscription Plans'
        ordering = ['sort_order', 'price']

    def __str__(self):
        return f"{self.name} (${self.price}/month)"

    def get_features_list(self):
        """Get list of enabled features for this plan"""
        features = []

        if self.max_qr_codes > 0:
            if self.max_qr_codes >= 10000:
                features.append(f"Unlimited QR Codes")
            else:
                features.append(f"Up to {self.max_qr_codes} QR Codes")

        if self.max_scans_per_month > 0:
            if self.max_scans_per_month >= 1000000:
                features.append(f"Unlimited Scans")
            else:
                features.append(f"Up to {self.max_scans_per_month:,} Scans/Month")

        if self.ai_enabled:
            features.append("AI Landing Pages")

        if self.alerts_enabled:
            features.append("Scan Alerts")

        if self.webhooks_enabled:
            features.append("Webhook Integration")

        if self.advanced_analytics_enabled:
            features.append("Advanced Analytics")

        if self.branding_enabled:
            features.append("Custom Branding")

        if self.batch_processing_enabled:
            features.append("Batch Processing")

        if self.api_access_enabled:
            features.append("API Access")

        if self.priority_support_enabled:
            features.append("Priority Support")

        return features

    def is_feature_enabled(self, feature_name):
        """Check if a specific feature is enabled for this plan"""
        feature_map = {
            'ai': self.ai_enabled,
            'alerts': self.alerts_enabled,
            'webhooks': self.webhooks_enabled,
            'analytics': self.analytics_enabled,
            'advanced_analytics': self.advanced_analytics_enabled,
            'branding': self.branding_enabled,
            'batch_processing': self.batch_processing_enabled,
            'api_access': self.api_access_enabled,
            'priority_support': self.priority_support_enabled,
        }
        return feature_map.get(feature_name, False)


class Subscription(models.Model):
    """
    MODULE 6: User subscription with usage tracking and limits enforcement
    """

    class SubscriptionStatus(models.TextChoices):
        ACTIVE = 'ACTIVE', _('Active')
        CANCELLED = 'CANCELLED', _('Cancelled')
        EXPIRED = 'EXPIRED', _('Expired')
        SUSPENDED = 'SUSPENDED', _('Suspended')

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='subscription')
    plan = models.ForeignKey(Plan, on_delete=models.SET_NULL, null=True, blank=True)

    # Subscription Details
    status = models.CharField(max_length=20, choices=SubscriptionStatus.choices, default=SubscriptionStatus.ACTIVE)
    started_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(null=True, blank=True, help_text="When subscription expires")
    cancelled_at = models.DateTimeField(null=True, blank=True)

    # Usage Tracking (Monthly)
    scans_this_month = models.IntegerField(default=0, help_text="Number of scans this month")
    last_scan_reset = models.DateField(auto_now_add=True, help_text="Last time scan count was reset")

    # Usage Tracking (Total)
    total_qr_codes_created = models.IntegerField(default=0, help_text="Total QR codes created")
    total_dynamic_redirects = models.IntegerField(default=0, help_text="Total dynamic redirects created")
    total_ai_pages = models.IntegerField(default=0, help_text="Total AI pages created")
    total_webhooks = models.IntegerField(default=0, help_text="Total webhooks created")
    total_scan_alerts = models.IntegerField(default=0, help_text="Total scan alerts created")

    # Billing
    stripe_subscription_id = models.CharField(max_length=255, blank=True, help_text="Stripe subscription ID")
    stripe_customer_id = models.CharField(max_length=255, blank=True, help_text="Stripe customer ID")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'User Subscription'
        verbose_name_plural = 'User Subscriptions'
        ordering = ['-created_at']

    def __str__(self):
        plan_name = self.plan.name if self.plan else "No Plan"
        return f"{self.user.username} - {plan_name} ({self.status})"

    def reset_monthly_scans(self):
        """Reset monthly scan count"""
        from django.utils import timezone
        self.scans_this_month = 0
        self.last_scan_reset = timezone.now().date()
        self.save(update_fields=['scans_this_month', 'last_scan_reset'])

    def increment_scan_count(self):
        """Increment scan count and check if reset is needed"""
        from django.utils import timezone
        today = timezone.now().date()

        # Check if we need to reset monthly count
        if self.last_scan_reset.month != today.month or self.last_scan_reset.year != today.year:
            self.reset_monthly_scans()

        self.scans_this_month += 1
        self.save(update_fields=['scans_this_month'])

    def can_create_qr_code(self):
        """Check if user can create more QR codes"""
        if not self.plan:
            return False

        current_count = self.user.qrcode_set.count()
        return current_count < self.plan.max_qr_codes

    def can_scan_this_month(self):
        """Check if user has scans remaining this month"""
        if not self.plan:
            return False

        return self.scans_this_month < self.plan.max_scans_per_month

    def can_create_dynamic_redirect(self):
        """Check if user can create more dynamic redirects"""
        if not self.plan or not self.plan.webhooks_enabled:
            return False

        current_count = DynamicQRRedirect.objects.filter(qr_code__user=self.user).count()
        return current_count < self.plan.max_dynamic_redirects

    def can_create_ai_page(self):
        """Check if user can create more AI pages"""
        if not self.plan or not self.plan.ai_enabled:
            return False

        current_count = AILandingPage.objects.filter(qr_code__user=self.user).count()
        return current_count < self.plan.max_ai_pages

    def can_create_webhook(self):
        """Check if user can create more webhooks"""
        if not self.plan or not self.plan.webhooks_enabled:
            return False

        current_count = WebhookEndpoint.objects.filter(user=self.user).count()
        return current_count < self.plan.max_webhooks

    def can_create_scan_alert(self):
        """Check if user can create more scan alerts"""
        if not self.plan or not self.plan.alerts_enabled:
            return False

        current_count = ScanAlert.objects.filter(user=self.user).count()
        return current_count < self.plan.max_scan_alerts

    def get_usage_summary(self):
        """Get comprehensive usage summary"""
        if not self.plan:
            return {}

        return {
            'qr_codes': {
                'current': self.user.qrcode_set.count(),
                'limit': self.plan.max_qr_codes,
                'percentage': min(100, (self.user.qrcode_set.count() / self.plan.max_qr_codes) * 100) if self.plan.max_qr_codes > 0 else 0
            },
            'scans': {
                'current': self.scans_this_month,
                'limit': self.plan.max_scans_per_month,
                'percentage': min(100, (self.scans_this_month / self.plan.max_scans_per_month) * 100) if self.plan.max_scans_per_month > 0 else 0
            },
            'dynamic_redirects': {
                'current': DynamicQRRedirect.objects.filter(qr_code__user=self.user).count(),
                'limit': self.plan.max_dynamic_redirects,
                'percentage': min(100, (DynamicQRRedirect.objects.filter(qr_code__user=self.user).count() / self.plan.max_dynamic_redirects) * 100) if self.plan.max_dynamic_redirects > 0 else 0
            },
            'ai_pages': {
                'current': AILandingPage.objects.filter(qr_code__user=self.user).count(),
                'limit': self.plan.max_ai_pages,
                'percentage': min(100, (AILandingPage.objects.filter(qr_code__user=self.user).count() / self.plan.max_ai_pages) * 100) if self.plan.max_ai_pages > 0 else 0
            },
            'webhooks': {
                'current': WebhookEndpoint.objects.filter(user=self.user).count(),
                'limit': self.plan.max_webhooks,
                'percentage': min(100, (WebhookEndpoint.objects.filter(user=self.user).count() / self.plan.max_webhooks) * 100) if self.plan.max_webhooks > 0 else 0
            },
            'scan_alerts': {
                'current': ScanAlert.objects.filter(user=self.user).count(),
                'limit': self.plan.max_scan_alerts,
                'percentage': min(100, (ScanAlert.objects.filter(user=self.user).count() / self.plan.max_scan_alerts) * 100) if self.plan.max_scan_alerts > 0 else 0
            }
        }


class StripeProduct(models.Model):
    """
    MODULE 6: Stripe integration - Maps internal plans to Stripe products/prices
    """
    plan = models.OneToOneField(Plan, on_delete=models.CASCADE, related_name='stripe_product')
    stripe_product_id = models.CharField(max_length=100, help_text="Stripe Product ID")
    stripe_price_id = models.CharField(max_length=100, help_text="Stripe Price ID for monthly billing")
    stripe_yearly_price_id = models.CharField(max_length=100, blank=True, help_text="Stripe Price ID for yearly billing")

    # Metadata
    is_active = models.BooleanField(default=True, help_text="Is this Stripe product active")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Stripe Product'
        verbose_name_plural = 'Stripe Products'
        ordering = ['plan__sort_order']

    def __str__(self):
        return f"Stripe Product for {self.plan.name}"

    def get_price_id(self, billing_cycle='monthly'):
        """Get the appropriate Stripe price ID based on billing cycle"""
        if billing_cycle == 'yearly' and self.stripe_yearly_price_id:
            return self.stripe_yearly_price_id
        return self.stripe_price_id
