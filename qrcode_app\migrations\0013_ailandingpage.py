# Generated by Django 4.2.7 on 2025-05-28 23:37

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('qrcode_app', '0012_qrcode_short_code_qrcode_target_url_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='AILandingPage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='Page title', max_length=255)),
                ('page_type', models.CharField(choices=[('EVENT', 'Event Invitation'), ('PRODUCT', 'Product Showcase'), ('MENU', 'Restaurant Menu'), ('BUSINESS', 'Business Card'), ('PORTFOLIO', 'Portfolio/Resume'), ('ANNOUNCEMENT', 'Announcement'), ('CONTACT', 'Contact Information'), ('CUSTOM', 'Custom Page')], default='CUSTOM', max_length=20)),
                ('prompt', models.TextField(help_text='Original user prompt for AI generation')),
                ('content', models.TextField(help_text='AI-generated HTML content')),
                ('primary_color', models.CharField(default='#667eea', help_text='Primary color for the page', max_length=7)),
                ('secondary_color', models.CharField(default='#764ba2', help_text='Secondary color for the page', max_length=7)),
                ('font_family', models.CharField(default='Inter', help_text='Font family for the page', max_length=50)),
                ('ai_model_used', models.CharField(blank=True, help_text='AI model used for generation', max_length=50, null=True)),
                ('generation_time', models.FloatField(blank=True, help_text='Time taken to generate (seconds)', null=True)),
                ('status', models.CharField(choices=[('GENERATING', 'Generating'), ('ACTIVE', 'Active'), ('DRAFT', 'Draft'), ('ARCHIVED', 'Archived')], default='GENERATING', max_length=20)),
                ('is_published', models.BooleanField(default=True, help_text='Whether the page is publicly accessible')),
                ('view_count', models.PositiveIntegerField(default=0, help_text='Number of times the page has been viewed')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('published_at', models.DateTimeField(blank=True, null=True)),
                ('qr_code', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='ai_landing_page', to='qrcode_app.qrcode')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ai_landing_pages', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'AI Landing Page',
                'verbose_name_plural': 'AI Landing Pages',
                'ordering': ['-created_at'],
            },
        ),
    ]
