"""
MODULE 3: AI Landing Page Generation Utilities
Integrates with existing AI infrastructure to generate beautiful landing pages
"""
import logging
import time
import json
from typing import Dict, Optional, Tuple
from django.utils.html import mark_safe

logger = logging.getLogger(__name__)

# Landing page templates for different types
LANDING_PAGE_PROMPTS = {
    'EVENT': """
Create a beautiful, modern HTML landing page for an event invitation. The page should include:
- Event title and description
- Date, time, and location details
- RSVP or registration information
- Attractive styling with gradients and modern design
- Mobile-responsive layout
- Professional typography

User's request: {prompt}

Generate complete HTML with inline CSS. Use modern design principles, attractive colors, and ensure it's mobile-friendly.
""",

    'PRODUCT': """
Create a stunning product showcase landing page with:
- Product name and compelling description
- Key features and benefits
- High-quality visual layout (placeholder for images)
- Call-to-action buttons
- Modern, professional design
- Mobile-responsive layout

User's request: {prompt}

Generate complete HTML with inline CSS. Focus on conversion and visual appeal.
""",

    'MENU': """
Create an elegant restaurant menu landing page featuring:
- Restaurant name and description
- Menu categories and items
- Prices and descriptions
- Attractive food-focused design
- Easy-to-read typography
- Mobile-friendly layout

User's request: {prompt}

Generate complete HTML with inline CSS. Use warm, appetizing colors and clean layout.
""",

    'BUSINESS': """
Create a professional business card landing page with:
- Business/person name and title
- Contact information
- Services or expertise
- Professional bio or description
- Clean, corporate design
- Contact buttons and links

User's request: {prompt}

Generate complete HTML with inline CSS. Keep it professional and trustworthy.
""",

    'PORTFOLIO': """
Create an impressive portfolio/resume landing page featuring:
- Name and professional title
- Skills and experience
- Portfolio items or achievements
- Contact information
- Modern, creative design
- Professional presentation

User's request: {prompt}

Generate complete HTML with inline CSS. Balance creativity with professionalism.
""",

    'ANNOUNCEMENT': """
Create an eye-catching announcement page with:
- Clear, prominent headline
- Important details and information
- Call-to-action if needed
- Attention-grabbing design
- Easy-to-read layout
- Mobile-responsive

User's request: {prompt}

Generate complete HTML with inline CSS. Make it visually striking and informative.
""",

    'CONTACT': """
Create a comprehensive contact information page with:
- Contact details (phone, email, address)
- Business hours if applicable
- Map or location information
- Social media links
- Professional, accessible design
- Clear call-to-action

User's request: {prompt}

Generate complete HTML with inline CSS. Focus on accessibility and clarity.
""",

    'CUSTOM': """
Create a beautiful, custom landing page based on the user's specific requirements:

User's request: {prompt}

Generate complete HTML with inline CSS. Use modern design principles, ensure mobile responsiveness, and create an engaging user experience. Include appropriate sections, styling, and layout based on the user's needs.
"""
}

def get_ai_client():
    """Get the AI client using the existing infrastructure"""
    try:
        # Import the existing AI client infrastructure
        import sys
        import os

        # Add the project root to Python path if needed
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        if project_root not in sys.path:
            sys.path.append(project_root)

        from ai_services.clients import get_ai_client as get_smart_ai_client
        client = get_smart_ai_client()
        logger.info(f"Successfully got AI client: {type(client).__name__}")
        return client
    except ImportError as e:
        logger.error(f"Could not import AI client from ai_services: {e}")
        return None
    except Exception as e:
        logger.error(f"Error getting AI client: {e}")
        return None

def generate_ai_landing_page(prompt: str, page_type: str = 'CUSTOM', primary_color: str = '#667eea', secondary_color: str = '#764ba2') -> Tuple[str, Dict]:
    """
    Generate an AI landing page using the existing Smart AI Engine

    Args:
        prompt: User's description of what they want
        page_type: Type of landing page (EVENT, PRODUCT, etc.)
        primary_color: Primary color for the page
        secondary_color: Secondary color for the page

    Returns:
        Tuple of (generated_html, metadata)
    """
    start_time = time.time()

    try:
        # Get the AI client
        ai_client = get_ai_client()
        if not ai_client:
            return generate_fallback_landing_page(prompt, page_type, primary_color, secondary_color)

        # Get the appropriate prompt template
        template = LANDING_PAGE_PROMPTS.get(page_type, LANDING_PAGE_PROMPTS['CUSTOM'])

        # Create the full prompt with styling instructions
        full_prompt = template.format(prompt=prompt) + f"""

IMPORTANT STYLING REQUIREMENTS:
- Use primary color: {primary_color}
- Use secondary color: {secondary_color}
- Include modern CSS with gradients, shadows, and animations
- Ensure mobile responsiveness with proper viewport meta tag
- Use professional typography (Google Fonts recommended)
- Include hover effects and smooth transitions
- Make it visually stunning and professional

Return ONLY the complete HTML document with inline CSS. No explanations or markdown formatting.
"""

        logger.info(f"Generating AI landing page for type: {page_type}")

        # Use the existing AI infrastructure to generate content
        # First try the new HTML generation method if available
        if hasattr(ai_client, 'generate_html_content'):
            logger.info("Using dedicated HTML generation method")
            html_content = ai_client.generate_html_content(
                prompt=full_prompt,
                page_type=page_type,
                primary_color=primary_color,
                secondary_color=secondary_color
            )
        elif hasattr(ai_client, 'generate_ad_suggestions'):
            logger.info("Adapting ad generation infrastructure for HTML landing pages")
            # Fallback to adapting the ad generation method for landing pages
            html_content = generate_html_with_ai_client(ai_client, full_prompt)
        else:
            logger.warning("No suitable AI generation method found")
            html_content = generate_fallback_landing_page(prompt, page_type, primary_color, secondary_color)[0]

        generation_time = time.time() - start_time

        # Clean up the HTML content
        html_content = clean_html_content(html_content)

        metadata = {
            'ai_model_used': getattr(ai_client, 'model', 'smart_engine'),
            'generation_time': generation_time,
            'page_type': page_type,
            'primary_color': primary_color,
            'secondary_color': secondary_color,
            'success': True
        }

        logger.info(f"Successfully generated AI landing page in {generation_time:.2f}s")
        return html_content, metadata

    except Exception as e:
        logger.error(f"Error generating AI landing page: {str(e)}")
        return generate_fallback_landing_page(prompt, page_type, primary_color, secondary_color)

def generate_html_with_ai_client(ai_client, prompt: str) -> str:
    """Generate HTML content using the AI client - properly integrated with existing infrastructure"""
    try:
        # Check if we have a SmartAIEngine or similar client
        client_name = type(ai_client).__name__
        logger.info(f"Using AI client: {client_name}")

        # Method 1: Try direct content generation if available
        if hasattr(ai_client, 'generate_content'):
            logger.info("Using direct generate_content method")
            return ai_client.generate_content(prompt)

        # Method 2: Use the existing ad generation infrastructure but adapt for HTML
        # This leverages the full Smart AI Engine with caching, fallbacks, etc.
        if hasattr(ai_client, 'generate_ad_suggestions'):
            logger.info("Adapting ad generation infrastructure for HTML landing pages")

            # Use the ad generation system but with HTML-specific parameters
            suggestions = ai_client.generate_ad_suggestions(
                language="english",
                business_type="Landing Page HTML Generation",
                target_audience="Website Visitors",
                tone="professional",
                num_suggestions=1,
                ad_title="HTML Landing Page"
            )

            if suggestions and len(suggestions) > 0:
                first_suggestion = suggestions[0]
                logger.info(f"Got suggestion from AI: {type(first_suggestion)}")

                # Extract HTML content from the suggestion
                if isinstance(first_suggestion, dict):
                    # Try different fields that might contain the content
                    html_content = (
                        first_suggestion.get('content', '') or
                        first_suggestion.get('description', '') or
                        first_suggestion.get('text', '') or
                        first_suggestion.get('html', '') or
                        str(first_suggestion)
                    )
                else:
                    html_content = str(first_suggestion)

                # If we got content but it's not HTML, wrap it in basic HTML structure
                if html_content and not html_content.strip().startswith('<'):
                    html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Generated Landing Page</title>
    <style>
        body {{ font-family: 'Inter', sans-serif; margin: 0; padding: 2rem; }}
        .content {{ max-width: 800px; margin: 0 auto; }}
    </style>
</head>
<body>
    <div class="content">
        {html_content}
    </div>
</body>
</html>"""

                return html_content

        # Method 3: Try to use any available generation method
        for method_name in ['generate', 'create', 'generate_text', 'generate_html']:
            if hasattr(ai_client, method_name):
                logger.info(f"Trying method: {method_name}")
                method = getattr(ai_client, method_name)
                try:
                    result = method(prompt)
                    if result:
                        return str(result)
                except Exception as e:
                    logger.warning(f"Method {method_name} failed: {e}")
                    continue

        logger.warning("No suitable generation method found on AI client")
        return ""

    except Exception as e:
        logger.error(f"Error generating HTML with AI client: {str(e)}")
        return ""

def clean_html_content(html_content: str) -> str:
    """Clean and validate HTML content"""
    if not html_content:
        return ""

    # Remove any markdown formatting that might have been included
    html_content = html_content.replace('```html', '').replace('```', '')

    # Ensure we have a complete HTML document
    if not html_content.strip().startswith('<!DOCTYPE') and not html_content.strip().startswith('<html'):
        html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Generated Landing Page</title>
</head>
<body>
{html_content}
</body>
</html>"""

    return html_content.strip()

def generate_fallback_landing_page(prompt: str, page_type: str, primary_color: str, secondary_color: str) -> Tuple[str, Dict]:
    """Generate a fallback landing page when AI is unavailable"""

    fallback_html = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Landing Page</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        body {{
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, {primary_color} 0%, {secondary_color} 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            padding: 2rem;
        }}

        .container {{
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            max-width: 600px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }}

        h1 {{
            font-size: 2.5rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }}

        p {{
            font-size: 1.2rem;
            line-height: 1.6;
            margin-bottom: 2rem;
            opacity: 0.9;
        }}

        .cta-button {{
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }}

        .cta-button:hover {{
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }}

        @media (max-width: 768px) {{
            h1 {{ font-size: 2rem; }}
            p {{ font-size: 1rem; }}
            .container {{ padding: 2rem; }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>{page_type.title()} Page</h1>
        <p>{prompt}</p>
        <a href="#" class="cta-button">Learn More</a>
    </div>
</body>
</html>"""

    metadata = {
        'ai_model_used': 'fallback',
        'generation_time': 0.1,
        'page_type': page_type,
        'primary_color': primary_color,
        'secondary_color': secondary_color,
        'success': False,
        'fallback': True
    }

    return fallback_html, metadata
