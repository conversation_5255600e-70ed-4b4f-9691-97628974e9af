/**
 * Enterprise QR - New Navigation Bar
 * A premium, enterprise-grade navigation system with elegant animations,
 * responsive design, and professional aesthetics.
 */

/* Root variables for consistent theming */
:root {
    --nav-height: 75px;
    --nav-bg: #0c1e35;
    --nav-bg-scrolled: rgba(12, 30, 53, 0.97);
    --nav-border: rgba(59, 130, 246, 0.2);
    --nav-shadow: rgba(0, 0, 0, 0.15);

    /* Text colors */
    --text-primary: rgba(255, 255, 255, 0.95);
    --text-secondary: rgba(255, 255, 255, 0.75);
    --text-hover: #ffffff;
    --text-active: #4f9cf9;

    /* Accent colors */
    --accent-primary: #4f9cf9;
    --accent-secondary: #7a5af8;
    --accent-tertiary: #10b981;
    --accent-quaternary: #f59e0b;

    /* Premium elements */
    --premium-color: #f5b142;
    --premium-glow: rgba(245, 177, 66, 0.4);

    /* Dropdown colors */
    --dropdown-bg: #ffffff;
    --dropdown-text: #1f2937;
    --dropdown-hover: #f8fafc;
    --dropdown-border: #e5e7eb;
    --dropdown-shadow: rgba(0, 0, 0, 0.15);

    /* Notification colors */
    --notification-bg: #ef4444;
    --notification-text: #ffffff;

    /* Animation speeds */
    --transition-fast: 0.15s;
    --transition-medium: 0.25s;
    --transition-slow: 0.35s;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;

    /* Border radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-full: 9999px;
}

/* Base navbar container */
.new-navbar {
    position: sticky;
    top: 0;
    left: 0;
    right: 0;
    height: var(--nav-height);
    background-color: red !important; /* TESTING: Red background to identify navbar */
    background-image: none !important; /* TESTING: Remove gradient */
    border-bottom: 1px solid var(--nav-border);
    box-shadow: 0 4px 10px -1px var(--nav-shadow);
    z-index: 1000;
    transition: all var(--transition-medium) ease;
}

/* Scrolled state */
.new-navbar.scrolled {
    background-color: red !important; /* TESTING: Keep red when scrolled */
    background-image: none !important; /* TESTING: Remove gradient */
    box-shadow: 0 10px 20px -3px var(--nav-shadow);
    height: calc(var(--nav-height) - 5px);
}

/* Navbar container */
.new-navbar .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 var(--spacing-md);
    max-width: 1400px;
    margin: 0 auto;
}

/* Brand/logo section */
.new-navbar-brand {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 600;
    font-size: 1.3rem;
    transition: all var(--transition-fast) ease;
    padding: 0.5rem 0.75rem;
    border-radius: var(--radius-md);
}

.new-navbar-brand:hover {
    transform: translateY(-1px);
    color: var(--text-hover);
}

.new-navbar-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.75rem;
    height: 2.75rem;
    margin-right: var(--spacing-sm);
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: 1.35rem;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.25);
    position: relative;
    overflow: hidden;
}

.new-navbar-logo::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom right, rgba(255, 255, 255, 0.1), transparent);
    pointer-events: none;
}

.new-navbar-brand-text {
    font-family: 'Poppins', sans-serif;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.new-navbar-brand-text span {
    color: var(--accent-primary);
    font-weight: 700;
    position: relative;
}

.new-navbar-brand-text span::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(to right, var(--accent-primary), transparent);
    opacity: 0.7;
}

/* Navigation menu container */
.new-navbar-nav {
    display: flex;
    align-items: center;
    height: 100%;
}

/* Main navigation items */
.new-navbar-nav-main {
    display: flex;
    align-items: center;
    height: 100%;
    margin: 0;
    padding: 0;
    list-style: none;
}

/* Navigation item */
.new-navbar-item {
    position: relative;
    height: 100%;
    display: flex;
    align-items: center;
    margin: 0 var(--spacing-sm);
}

/* Navigation link */
.new-navbar-link {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 0 var(--spacing-md);
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.875rem;
    text-decoration: none;
    transition: all var(--transition-fast) ease;
    position: relative;
    letter-spacing: 0.3px;
}

.new-navbar-link:hover {
    color: var(--text-hover);
}

.new-navbar-link.active {
    color: var(--text-active);
    font-weight: 600;
}

/* Active indicator line */
.new-navbar-link.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: var(--spacing-md);
    right: var(--spacing-md);
    height: 3px;
    background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
    border-radius: var(--radius-full);
    box-shadow: 0 1px 3px rgba(79, 156, 249, 0.3);
}

/* Hover indicator line - subtle effect */
.new-navbar-link:not(.active):hover::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: var(--spacing-md);
    right: var(--spacing-md);
    height: 2px;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.2), transparent);
    border-radius: var(--radius-full);
    opacity: 0.5;
}

/* Navigation icon */
.new-navbar-icon {
    margin-right: var(--spacing-xs);
    font-size: 1rem;
    transition: transform var(--transition-fast) ease;
}

.new-navbar-link:hover .new-navbar-icon {
    transform: translateY(-1px);
}

/* Right side navigation */
.new-navbar-nav-right {
    display: flex;
    align-items: center;
    height: 100%;
    margin: 0;
    padding: 0;
    list-style: none;
}

/* Mobile toggle button - hidden by default on desktop */
.new-navbar-toggle {
    display: none; /* Hidden by default on all screen sizes */
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    background: transparent;
    border: none;
    color: var(--text-secondary);
    font-size: 1.25rem;
    cursor: pointer;
    transition: color var(--transition-fast) ease;
}

.new-navbar-toggle:hover {
    color: var(--text-hover);
}

/* Premium badge */
.premium-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    background: linear-gradient(135deg, var(--premium-color), #e08c0b);
    border-radius: var(--radius-full);
    color: #0c1e35;
    font-size: 0.7rem;
    font-weight: 700;
    margin-left: var(--spacing-xs);
    box-shadow: 0 2px 6px var(--premium-glow), inset 0 1px 1px rgba(255, 255, 255, 0.6);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 1px solid rgba(245, 177, 66, 0.7);
    position: relative; /* Ensure proper positioning */
    flex-shrink: 0; /* Prevent shrinking */
}

/* Admin badge */
.admin-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    background: linear-gradient(135deg, #ef4444, #dc2626);
    border-radius: var(--radius-full);
    color: #ffffff;
    font-size: 0.7rem;
    font-weight: 700;
    margin-left: var(--spacing-xs);
    box-shadow: 0 2px 6px rgba(239, 68, 68, 0.4), inset 0 1px 1px rgba(255, 255, 255, 0.2);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 1px solid rgba(239, 68, 68, 0.7);
    position: relative; /* Ensure proper positioning */
    flex-shrink: 0; /* Prevent shrinking */
}

/* Enhanced Submenu Styles - Fixed for nested dropdown structure */
.new-navbar-dropdown .has-submenu {
    position: relative;
}

.new-navbar-dropdown .submenu-trigger {
    position: relative;
    cursor: pointer;
    display: flex !important;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--dropdown-text);
    text-decoration: none;
    transition: all 0.2s ease;
    border-radius: 8px;
}

.new-navbar-dropdown .submenu-trigger:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    text-decoration: none;
}

.new-navbar-dropdown .submenu-arrow {
    margin-left: auto;
    font-size: 0.8rem;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    color: #718096;
    order: 3;
}

.new-navbar-dropdown .premium-badge {
    order: 2;
    margin-left: 8px;
    margin-right: 8px;
}

.new-navbar-dropdown .submenu {
    position: absolute;
    left: 100%;
    top: 0;
    min-width: 240px;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.08);
    opacity: 0;
    visibility: hidden;
    transform: translateX(-10px) scale(0.95);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1002;
    padding: 8px 0;
    backdrop-filter: blur(10px);
    list-style: none;
    margin: 0;
}

/* Desktop hover behavior - Fixed selectors with debugging */
@media (min-width: 769px) {
    .new-navbar-dropdown .has-submenu:hover > .submenu {
        opacity: 1 !important;
        visibility: visible !important;
        transform: translateX(0) scale(1) !important;
        display: block !important;
    }

    .new-navbar-dropdown .has-submenu:hover .submenu-arrow {
        transform: rotate(90deg);
        color: #667eea;
    }

    .new-navbar-dropdown .has-submenu:hover .submenu-trigger {
        background: rgba(102, 126, 234, 0.1);
        color: #667eea;
    }


}

.new-navbar-dropdown .submenu-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    color: #2d3748;
    text-decoration: none;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 0.9rem;
    font-weight: 500;
    border-radius: 8px;
    margin: 2px 8px;
}

.new-navbar-dropdown .submenu-item:hover {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: #ffffff;
    transform: translateX(4px);
    text-decoration: none;
}

.new-navbar-dropdown .submenu-item i {
    width: 18px;
    margin-right: 12px;
    color: #718096;
    transition: all 0.2s ease;
    font-size: 0.9rem;
}

.new-navbar-dropdown .submenu-item:hover i {
    color: #ffffff;
    transform: scale(1.1);
}

/* Mobile submenu styles - Fixed for nested structure */
@media (max-width: 768px) {
    .new-navbar-dropdown .submenu {
        position: static;
        opacity: 0;
        visibility: hidden;
        transform: none;
        box-shadow: none;
        border: none;
        background: rgba(102, 126, 234, 0.05);
        margin: 8px 0 8px 20px;
        border-radius: 8px;
        border-left: 3px solid #667eea;
        max-height: 0;
        overflow: hidden;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        backdrop-filter: none;
    }

    .new-navbar-dropdown .submenu.open {
        opacity: 1;
        visibility: visible;
        max-height: 300px;
        padding: 8px 0;
    }

    .new-navbar-dropdown .submenu-arrow {
        display: block;
        transition: transform 0.3s ease;
    }

    .new-navbar-dropdown .has-submenu.open .submenu-arrow {
        transform: rotate(90deg);
        color: #667eea;
    }

    .new-navbar-dropdown .submenu-item {
        margin: 2px 12px;
        padding: 10px 12px;
        font-size: 0.85rem;
    }

    .new-navbar-dropdown .submenu-item:hover {
        background: rgba(102, 126, 234, 0.1);
        color: #667eea;
        transform: translateX(2px);
    }

    .new-navbar-dropdown .submenu-item:hover i {
        color: #667eea;
    }
}

/* Notification indicator */
.new-navbar-notification {
    position: relative;
}

.new-navbar-notification-icon {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    transition: all var(--transition-fast) ease;
    cursor: pointer;
}

.new-navbar-notification-icon:hover {
    color: var(--text-hover);
}

.new-navbar-notification:hover .new-navbar-icon {
    transform: translateY(-1px);
}

.new-navbar-notification-badge {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 1.25rem;
    height: 1.25rem;
    padding: 0 0.25rem;
    background-color: var(--notification-bg);
    background-image: linear-gradient(135deg, var(--notification-bg), #d03333);
    border-radius: var(--radius-full);
    color: var(--notification-text);
    font-size: 0.75rem;
    font-weight: 600;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.new-navbar-notification-badge.has-notifications {
    animation: pulse 1.5s infinite;
}

.new-navbar-notification-link {
    color: var(--accent-primary);
    font-size: 0.75rem;
    text-decoration: none;
    transition: color var(--transition-fast) ease;
    font-weight: 600;
}

.new-navbar-notification-link:hover {
    color: var(--accent-secondary);
    text-decoration: underline;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
    }
    70% {
        transform: scale(1.1);
        box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
    }
}

/* User avatar and info */
.new-navbar-user-avatar {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    border-radius: var(--radius-full);
    color: var(--text-primary);
    font-size: 1rem;
    margin-right: var(--spacing-sm);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.new-navbar-user-avatar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom right, rgba(255, 255, 255, 0.15), transparent);
    pointer-events: none;
}

.new-navbar-user-info {
    display: flex;
    flex-direction: column;
    margin-right: var(--spacing-sm);
}

.new-navbar-user-name {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    letter-spacing: 0.2px;
}

.new-navbar-user-role {
    font-size: 0.7rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    opacity: 0.8;
}

/* Login/Signup button */
.new-navbar-auth {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1.25rem;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: 0.875rem;
    font-weight: 600;
    text-decoration: none;
    transition: all var(--transition-fast) ease;
    box-shadow: 0 3px 6px rgba(59, 130, 246, 0.3), inset 0 1px 1px rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.1);
    letter-spacing: 0.3px;
    position: relative;
    overflow: hidden;
}

.new-navbar-auth::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 40%;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.15), transparent);
    pointer-events: none;
}

.new-navbar-auth:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 10px rgba(59, 130, 246, 0.4), inset 0 1px 1px rgba(255, 255, 255, 0.2);
    color: var(--text-hover);
}

/* Upper Nav Account Wrapper */
.upper-nav-account-wrapper {
    display: none; /* Hidden by default on desktop */
    align-items: center;
    margin-right: var(--spacing-md);
    position: relative;
}

/* Mobile-only elements - hidden by default on desktop */
.mobile-only-element {
    display: none !important;
}

/* Mobile menu close button - removed */

/* Desktop styles - Ensure navigation is visible */
@media (min-width: 768px) {
    .new-navbar-nav {
        display: flex !important;
        position: static !important;
        transform: none !important;
        background: none !important;
        padding: 0 !important;
        flex-direction: row !important;
        align-items: center !important;
        height: 100% !important;
    }

    .new-navbar-toggle {
        display: none !important;
    }
}

/* Mobile responsive styles - Only show hamburger on small mobile devices */
@media (max-width: 767.98px) {
    .mobile-only-element,
    .new-navbar-toggle,
    .upper-nav-account-wrapper {
        display: flex !important;
    }

    .new-navbar-nav {
        position: fixed;
        top: var(--nav-height);
        left: 0;
        right: 0;
        bottom: 0;
        flex-direction: column;
        justify-content: flex-start;
        align-items: stretch;
        background-color: var(--nav-bg);
        background-image: linear-gradient(to bottom, var(--nav-bg), #132f4c);
        padding: var(--spacing-lg) var(--spacing-md);
        transform: translateX(-100%);
        transition: transform var(--transition-medium) ease, box-shadow var(--transition-medium) ease;
        overflow-y: auto;
        z-index: 999;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .new-navbar-nav.open {
        transform: translateX(0);
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
    }

    /* Close button removed */

    /* Add body class to prevent background scrolling when menu is open */
    body.menu-open {
        overflow: hidden;
    }

    .new-navbar-nav-main,
    .new-navbar-nav-right {
        flex-direction: column;
        height: auto;
    }

    .new-navbar-item {
        height: auto;
        margin: var(--spacing-xs) 0;
    }

    .new-navbar-link {
        height: auto;
        padding: var(--spacing-md);
        border-radius: var(--radius-md);
    }

    .new-navbar-link.active::after {
        display: none;
    }

    .new-navbar-link.active {
        background-color: rgba(59, 130, 246, 0.1);
    }

    .new-navbar-nav-right {
        margin-top: var(--spacing-md);
        padding-top: var(--spacing-md);
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }
}
