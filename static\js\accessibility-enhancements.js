/**
 * accessibility-enhancements.js
 * 
 * Enhances accessibility across the site for better user experience.
 * Implements WCAG 2.1 AA compliance improvements.
 */

(function() {
    // Configuration
    const config = {
        // Whether to log accessibility issues to console
        enableLogging: false,
        
        // Whether to fix accessibility issues automatically
        enableAutoFix: true,
        
        // Minimum contrast ratio for text
        minContrastRatio: 4.5,
        
        // Minimum font size for text (in pixels)
        minFontSize: 16,
        
        // Maximum number of elements to check at once
        maxElementsToCheck: 100
    };
    
    // Initialize accessibility enhancements
    function init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', onDOMContentLoaded);
        } else {
            onDOMContentLoaded();
        }
    }
    
    // Handle DOMContentLoaded event
    function onDOMContentLoaded() {
        // Enhance keyboard navigation
        enhanceKeyboardNavigation();
        
        // Enhance screen reader support
        enhanceScreenReaderSupport();
        
        // Enhance touch targets
        enhanceTouchTargets();
        
        // Enhance form accessibility
        enhanceFormAccessibility();
        
        // Enhance color contrast
        enhanceColorContrast();
        
        // Enhance focus states
        enhanceFocusStates();
        
        // Enhance semantic structure
        enhanceSemanticStructure();
    }
    
    // Enhance keyboard navigation
    function enhanceKeyboardNavigation() {
        // Add skip link if not present
        if (!document.querySelector('.skip-link')) {
            const skipLink = document.createElement('a');
            skipLink.href = '#main-content';
            skipLink.className = 'skip-link';
            skipLink.textContent = 'Skip to main content';
            document.body.insertBefore(skipLink, document.body.firstChild);
            
            // Add ID to main content if not present
            const main = document.querySelector('main');
            if (main && !main.id) {
                main.id = 'main-content';
            }
        }
        
        // Ensure all interactive elements are keyboard accessible
        const interactiveElements = document.querySelectorAll('a, button, input, select, textarea, [role="button"]');
        
        for (let i = 0; i < Math.min(interactiveElements.length, config.maxElementsToCheck); i++) {
            const el = interactiveElements[i];
            
            // Skip elements that are already accessible
            if (el.hasAttribute('data-a11y-enhanced')) continue;
            
            // Ensure tabindex is set correctly
            if (el.getAttribute('tabindex') === '-1' && !el.hasAttribute('aria-hidden')) {
                el.removeAttribute('tabindex');
            }
            
            // Ensure role is set correctly
            if (el.tagName === 'A' && !el.hasAttribute('href') && !el.hasAttribute('role')) {
                el.setAttribute('role', 'button');
                el.setAttribute('tabindex', '0');
            }
            
            // Mark as enhanced
            el.setAttribute('data-a11y-enhanced', 'true');
        }
    }
    
    // Enhance screen reader support
    function enhanceScreenReaderSupport() {
        // Add aria-label to elements that need it
        const elementsNeedingLabels = document.querySelectorAll('a:not([aria-label]):not([aria-labelledby]):not([title]):empty, button:not([aria-label]):not([aria-labelledby]):not([title]):empty, [role="button"]:not([aria-label]):not([aria-labelledby]):not([title]):empty');
        
        for (let i = 0; i < Math.min(elementsNeedingLabels.length, config.maxElementsToCheck); i++) {
            const el = elementsNeedingLabels[i];
            
            // Skip elements that are already labeled
            if (el.hasAttribute('data-a11y-enhanced')) continue;
            
            // Try to find an icon
            const icon = el.querySelector('i, svg');
            if (icon) {
                // Try to determine the purpose from the icon class
                let purpose = '';
                if (icon.className) {
                    const classList = icon.className.split(' ');
                    for (const cls of classList) {
                        if (cls.includes('search')) purpose = 'Search';
                        else if (cls.includes('close')) purpose = 'Close';
                        else if (cls.includes('menu')) purpose = 'Menu';
                        else if (cls.includes('user')) purpose = 'User';
                        else if (cls.includes('home')) purpose = 'Home';
                        else if (cls.includes('settings')) purpose = 'Settings';
                        else if (cls.includes('notification')) purpose = 'Notifications';
                        else if (cls.includes('bell')) purpose = 'Notifications';
                    }
                }
                
                if (purpose) {
                    el.setAttribute('aria-label', purpose);
                } else {
                    el.setAttribute('aria-label', 'Button');
                }
            } else {
                el.setAttribute('aria-label', 'Button');
            }
            
            // Mark as enhanced
            el.setAttribute('data-a11y-enhanced', 'true');
        }
        
        // Add alt text to images that need it
        const imagesNeedingAlt = document.querySelectorAll('img:not([alt])');
        
        for (let i = 0; i < Math.min(imagesNeedingAlt.length, config.maxElementsToCheck); i++) {
            const img = imagesNeedingAlt[i];
            
            // Skip elements that are already enhanced
            if (img.hasAttribute('data-a11y-enhanced')) continue;
            
            // Try to determine the purpose from the src or parent
            let alt = '';
            if (img.src) {
                const srcParts = img.src.split('/');
                const filename = srcParts[srcParts.length - 1].split('.')[0];
                alt = filename.replace(/[-_]/g, ' ');
            }
            
            if (!alt && img.parentElement.tagName === 'A') {
                alt = img.parentElement.textContent.trim();
            }
            
            if (!alt) {
                // Check if it's a decorative image
                if (img.width < 16 || img.height < 16 || img.className.includes('decoration')) {
                    alt = '';
                } else {
                    alt = 'Image';
                }
            }
            
            img.setAttribute('alt', alt);
            
            // Mark as enhanced
            img.setAttribute('data-a11y-enhanced', 'true');
        }
    }
    
    // Enhance touch targets
    function enhanceTouchTargets() {
        // Ensure touch targets are large enough
        const touchTargets = document.querySelectorAll('a, button, input[type="button"], input[type="submit"], input[type="reset"], input[type="checkbox"], input[type="radio"], [role="button"]');
        
        for (let i = 0; i < Math.min(touchTargets.length, config.maxElementsToCheck); i++) {
            const el = touchTargets[i];
            
            // Skip elements that are already enhanced
            if (el.hasAttribute('data-a11y-enhanced')) continue;
            
            // Get computed style
            const style = window.getComputedStyle(el);
            const width = parseFloat(style.width);
            const height = parseFloat(style.height);
            
            // Check if touch target is large enough
            if (width < 44 || height < 44) {
                // Add a class to enhance the touch target
                el.classList.add('a11y-touch-target');
            }
            
            // Mark as enhanced
            el.setAttribute('data-a11y-enhanced', 'true');
        }
    }
    
    // Enhance form accessibility
    function enhanceFormAccessibility() {
        // Ensure all form fields have labels
        const formFields = document.querySelectorAll('input:not([type="hidden"]):not([type="submit"]):not([type="reset"]):not([type="button"]), select, textarea');
        
        for (let i = 0; i < Math.min(formFields.length, config.maxElementsToCheck); i++) {
            const field = formFields[i];
            
            // Skip elements that are already enhanced
            if (field.hasAttribute('data-a11y-enhanced')) continue;
            
            // Check if field has a label
            const id = field.id;
            if (id) {
                const label = document.querySelector(`label[for="${id}"]`);
                if (!label) {
                    // Create a label if none exists
                    const newLabel = document.createElement('label');
                    newLabel.htmlFor = id;
                    
                    // Try to determine label text from placeholder or name
                    let labelText = '';
                    if (field.placeholder) {
                        labelText = field.placeholder;
                    } else if (field.name) {
                        labelText = field.name.replace(/[-_]/g, ' ');
                        // Capitalize first letter
                        labelText = labelText.charAt(0).toUpperCase() + labelText.slice(1);
                    } else {
                        labelText = 'Field';
                    }
                    
                    newLabel.textContent = labelText;
                    
                    // Insert label before field
                    field.parentNode.insertBefore(newLabel, field);
                }
            } else {
                // Generate an ID if none exists
                const newId = 'field-' + Math.random().toString(36).substr(2, 9);
                field.id = newId;
                
                // Create a label
                const newLabel = document.createElement('label');
                newLabel.htmlFor = newId;
                
                // Try to determine label text from placeholder or name
                let labelText = '';
                if (field.placeholder) {
                    labelText = field.placeholder;
                } else if (field.name) {
                    labelText = field.name.replace(/[-_]/g, ' ');
                    // Capitalize first letter
                    labelText = labelText.charAt(0).toUpperCase() + labelText.slice(1);
                } else {
                    labelText = 'Field';
                }
                
                newLabel.textContent = labelText;
                
                // Insert label before field
                field.parentNode.insertBefore(newLabel, field);
            }
            
            // Mark as enhanced
            field.setAttribute('data-a11y-enhanced', 'true');
        }
    }
    
    // Enhance color contrast
    function enhanceColorContrast() {
        // Add high contrast class to body for CSS targeting
        document.body.classList.add('a11y-high-contrast');
    }
    
    // Enhance focus states
    function enhanceFocusStates() {
        // Add focus visible class to body for CSS targeting
        document.body.classList.add('a11y-focus-visible');
    }
    
    // Enhance semantic structure
    function enhanceSemanticStructure() {
        // Ensure page has proper heading structure
        const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
        const headingLevels = {};
        
        for (let i = 1; i <= 6; i++) {
            headingLevels[i] = 0;
        }
        
        for (let i = 0; i < headings.length; i++) {
            const heading = headings[i];
            const level = parseInt(heading.tagName.charAt(1));
            headingLevels[level]++;
        }
        
        // Log heading structure
        if (config.enableLogging) {
            console.log('Heading structure:', headingLevels);
        }
        
        // Ensure page has proper landmark structure
        const landmarks = {
            header: document.querySelector('header'),
            nav: document.querySelector('nav'),
            main: document.querySelector('main'),
            footer: document.querySelector('footer')
        };
        
        // Log landmark structure
        if (config.enableLogging) {
            console.log('Landmark structure:', landmarks);
        }
    }
    
    // Initialize
    init();
    
    // Add CSS for accessibility enhancements
    const style = document.createElement('style');
    style.textContent = `
        /* Skip link */
        .skip-link {
            position: absolute;
            top: -40px;
            left: 0;
            background: #000;
            color: #fff;
            padding: 8px;
            z-index: 100;
            transition: top 0.3s;
        }
        
        .skip-link:focus {
            top: 0;
        }
        
        /* Touch target enhancement */
        .a11y-touch-target {
            min-width: 44px;
            min-height: 44px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
        
        /* High contrast mode */
        .a11y-high-contrast a:not(:hover):not(:focus) {
            text-decoration: underline;
        }
        
        /* Focus visible */
        .a11y-focus-visible :focus {
            outline: 3px solid #4d90fe;
            outline-offset: 2px;
        }
        
        .a11y-focus-visible :focus:not(:focus-visible) {
            outline: none;
        }
        
        .a11y-focus-visible :focus-visible {
            outline: 3px solid #4d90fe;
            outline-offset: 2px;
        }
    `;
    document.head.appendChild(style);
    
    // Expose public API
    window.AccessibilityEnhancements = {
        enableLogging: function(enable) {
            config.enableLogging = enable;
        },
        enableAutoFix: function(enable) {
            config.enableAutoFix = enable;
        }
    };
})();
