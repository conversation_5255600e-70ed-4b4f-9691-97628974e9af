/* ULTRA-COMPACT PREMIUM FOOTER - PRODUCTION OPTIMIZED */

/* Production Performance Optimizations */
.ultra-premium-footer * {
    box-sizing: border-box !important;
}

.ultra-premium-footer {
    will-change: transform !important;
    contain: layout style paint !important;
}

/* Hardware acceleration for smooth animations */
.btn-cta,
.newsletter-btn-premium,
.social-icon-premium,
.developer-link {
    transform: translateZ(0) !important;
    will-change: transform !important;
}

/* Touch-friendly interactions for mobile */
@media (hover: none) and (pointer: coarse) {
    .btn-cta,
    .newsletter-btn-premium,
    .developer-link,
    .legal-link {
        min-height: 44px !important;
        min-width: 44px !important;
        touch-action: manipulation !important;
    }

    .newsletter-input-premium {
        min-height: 44px !important;
        touch-action: manipulation !important;
    }
}

/* Override existing footer with compact design */
.ultra-premium-footer {
    background: linear-gradient(135deg, #0f1419 0%, #1a1f36 25%, #121628 50%, #0a0e1a 100%) !important;
    position: relative !important;
    overflow: visible !important;
    color: #e6e6e6 !important;
    padding: 0 !important;
    margin: 1rem 1.5rem 0 1.5rem !important;
    z-index: 10 !important;
    box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.3) !important;
    backdrop-filter: blur(20px) !important;
    border-top: 1px solid rgba(102, 126, 234, 0.2) !important;
    border-radius: 12px 12px 0 0 !important;
    min-height: auto !important;
}

/* Hide all existing sections and create new compact layout */
.footer-main-content {
    padding: 1.5rem 0 !important;
    position: relative !important;
    z-index: 2 !important;
}

.footer-brand-showcase {
    margin-bottom: 0 !important;
    padding: 0 !important;
}

.footer-navigation-grid {
    display: none !important; /* Hide the large navigation grid */
}

.footer-contact-newsletter {
    display: block !important;
    margin-bottom: 0 !important;
    padding: 1rem 0 !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.footer-social-trust {
    display: block !important; /* Show for clickable social icons */
    padding: 1rem 0 !important;
    margin-bottom: 0 !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Style social icons in middle layer */
.footer-social-trust .social-icons-premium {
    display: flex !important;
    gap: 1rem !important;
    justify-content: center !important;
}

.footer-social-trust .social-icon-premium {
    width: 35px !important;
    height: 35px !important;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%) !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
    backdrop-filter: blur(10px) !important;
}

.footer-social-trust .social-icon-premium i {
    font-size: 0.9rem !important;
    color: #667eea !important;
    z-index: 2 !important;
}

.footer-social-trust .social-icon-premium:hover {
    transform: translateY(-3px) scale(1.1) !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4) !important;
}

.footer-social-trust .social-icon-premium:hover i {
    color: white !important;
}

/* Hide social labels to prevent overlap */
.footer-social-trust .social-label {
    display: none !important;
}

/* Hide social icons from upper layer CTA section */
.footer-cta-section .social-icons-premium {
    display: none !important;
}

/* Style social and trust titles */
.footer-social-trust .social-title,
.footer-social-trust .trust-title {
    font-size: 0.9rem !important;
    font-weight: 700 !important;
    color: white !important;
    margin-bottom: 0.8rem !important;
    text-align: center !important;
}

/* Style trust badges */
.footer-social-trust .trust-badges {
    display: flex !important;
    gap: 1rem !important;
    justify-content: center !important;
    flex-wrap: wrap !important;
}

.footer-social-trust .trust-badge {
    display: flex !important;
    align-items: center !important;
    gap: 0.4rem !important;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-radius: 6px !important;
    padding: 0.4rem 0.8rem !important;
    font-size: 0.75rem !important;
    color: rgba(255, 255, 255, 0.8) !important;
    font-weight: 600 !important;
    backdrop-filter: blur(10px) !important;
}

.footer-social-trust .trust-badge i {
    color: #667eea !important;
    font-size: 0.7rem !important;
}

/* Style contact section in second layer - full width */
.footer-contact-newsletter .contact-info-premium {
    display: block !important;
    text-align: center !important;
}

/* Balanced contact and newsletter layout */
.footer-contact-newsletter .row {
    gap: 2rem !important;
    align-items: flex-start !important;
}

.footer-contact-newsletter .row .col-lg-6:first-child {
    flex: 0 0 45% !important;
    max-width: 45% !important;
}

.footer-contact-newsletter .row .col-lg-6:last-child {
    flex: 0 0 50% !important;
    max-width: 50% !important;
    display: block !important;
}

/* Hide individual contact items and create consolidated card */
.footer-contact-newsletter .contact-details,
.footer-contact-newsletter .contact-item-premium,
.footer-contact-newsletter .contact-icon-wrapper,
.footer-contact-newsletter .contact-text {
    display: none !important;
}

/* Create consolidated contact card with top margin */
.footer-contact-newsletter .contact-info-premium::after {
    content:
    '📞 Contact Us' '\A'
    '🏢 Enterprise QR' '\A'
    '📍 Progressive Village, Nairobi, Kenya' '\A'
    '☎️ Support: +254 -734 957 121' '\A'
    '✉️ Email: <EMAIL>' !important;

    display: block !important;
    font-size: 0.85rem !important;
    line-height: 1.6 !important;
    color: rgba(255, 255, 255, 0.9) !important;
    margin: 1rem auto 0 auto !important;
    padding: 1.2rem !important;
    max-width: 300px !important;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%) !important;
    border: 1px solid rgba(102, 126, 234, 0.2) !important;
    border-radius: 10px !important;
    backdrop-filter: blur(15px) !important;
    white-space: pre-line !important;
    box-shadow:
        0 6px 20px rgba(102, 126, 234, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
    text-align: center !important;
}

/* Hide contact title */
.footer-contact-newsletter .contact-title {
    display: none !important;
}

/* Create sophisticated newsletter section */
.footer-contact-newsletter .newsletter-premium {
    display: block !important;
}

.footer-contact-newsletter .newsletter-card {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%) !important;
    border: 1px solid rgba(102, 126, 234, 0.2) !important;
    border-radius: 10px !important;
    padding: 1.5rem !important;
    margin-top: 1rem !important;
    backdrop-filter: blur(15px) !important;
    box-shadow:
        0 6px 20px rgba(102, 126, 234, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
    text-align: center !important;
}

.footer-contact-newsletter .newsletter-header {
    margin-bottom: 1rem !important;
}

.footer-contact-newsletter .newsletter-icon {
    width: 40px !important;
    height: 40px !important;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%) !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin: 0 auto 0.8rem auto !important;
    backdrop-filter: blur(10px) !important;
}

.footer-contact-newsletter .newsletter-icon i {
    font-size: 1rem !important;
    color: #667eea !important;
}

.footer-contact-newsletter .newsletter-title {
    font-size: 1rem !important;
    font-weight: 700 !important;
    color: white !important;
    margin-bottom: 0.4rem !important;
}

.footer-contact-newsletter .newsletter-subtitle {
    font-size: 0.8rem !important;
    color: rgba(255, 255, 255, 0.7) !important;
    margin-bottom: 1rem !important;
    line-height: 1.4 !important;
}

.footer-contact-newsletter .newsletter-form-premium {
    display: block !important;
}

.footer-contact-newsletter .input-group-premium {
    display: flex !important;
    gap: 0.5rem !important;
    margin-bottom: 0 !important;
}

.footer-contact-newsletter .newsletter-input-premium {
    flex: 1 !important;
    padding: 0.7rem 1rem !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 6px !important;
    background: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
    font-size: 0.85rem !important;
    backdrop-filter: blur(10px) !important;
    transition: all 0.3s ease !important;
}

.footer-contact-newsletter .newsletter-input-premium::placeholder {
    color: rgba(255, 255, 255, 0.5) !important;
}

.footer-contact-newsletter .newsletter-input-premium:focus {
    outline: none !important;
    border-color: #667eea !important;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2) !important;
}

.footer-contact-newsletter .newsletter-btn-premium {
    padding: 0.7rem 1.2rem !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border: none !important;
    border-radius: 6px !important;
    color: white !important;
    font-size: 0.85rem !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.4rem !important;
}

.footer-contact-newsletter .newsletter-btn-premium:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4) !important;
}

.footer-contact-newsletter .newsletter-btn-premium i {
    font-size: 0.8rem !important;
}

/* Add social icons to contact section */
.footer-contact-newsletter::after {
    content: '' !important;
    display: block !important;
    margin-top: 1rem !important;
    text-align: center !important;
}

/* Create clickable social icons section */
.footer-contact-newsletter::before {
    content: '' !important;
    display: block !important;
    margin-top: 1rem !important;
    text-align: center !important;
}

/* Add actual social icons after contact section */
.footer-contact-newsletter::after {
    content: '' !important;
    display: block !important;
    margin-top: 1rem !important;
    padding: 1rem !important;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%) !important;
    border: 1px solid rgba(255, 255, 255, 0.08) !important;
    border-radius: 8px !important;
    backdrop-filter: blur(10px) !important;
    text-align: center !important;
}

/* Style for clickable social icons in the hidden section */
.footer-social-trust .social-icons-premium {
    display: flex !important;
    gap: 1rem !important;
    justify-content: center !important;
    margin-top: 1rem !important;
}

.footer-social-trust .social-icon-premium {
    position: relative !important;
    width: 40px !important;
    height: 40px !important;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%) !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    cursor: pointer !important;
}

.footer-social-trust .social-icon-premium i {
    font-size: 1rem !important;
    color: #667eea !important;
    z-index: 2 !important;
    transition: all 0.3s ease !important;
}

/* Hover effects for social icons */
.footer-social-trust .social-icon-premium:hover {
    transform: translateY(-3px) scale(1.1) !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4) !important;
    border-color: rgba(102, 126, 234, 0.3) !important;
}

.footer-social-trust .social-icon-premium:hover i {
    color: white !important;
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.5) !important;
}

/* Specific hover colors for each social platform */
.footer-social-trust .social-icon-premium[data-tooltip="LinkedIn"]:hover {
    background: linear-gradient(135deg, #0077b5 0%, #005885 100%) !important;
    box-shadow: 0 8px 25px rgba(0, 119, 181, 0.4) !important;
}

.footer-social-trust .social-icon-premium[data-tooltip="Twitter"]:hover {
    background: linear-gradient(135deg, #1da1f2 0%, #0d8bd9 100%) !important;
    box-shadow: 0 8px 25px rgba(29, 161, 242, 0.4) !important;
}

.footer-social-trust .social-icon-premium[data-tooltip="YouTube"]:hover {
    background: linear-gradient(135deg, #ff0000 0%, #cc0000 100%) !important;
    box-shadow: 0 8px 25px rgba(255, 0, 0, 0.4) !important;
}

.footer-social-trust .social-icon-premium[data-tooltip="GitHub"]:hover {
    background: linear-gradient(135deg, #333333 0%, #1a1a1a 100%) !important;
    box-shadow: 0 8px 25px rgba(51, 51, 51, 0.4) !important;
}

/* Tooltip styles */
.footer-social-trust .social-icon-premium::before {
    content: attr(data-tooltip) !important;
    position: absolute !important;
    bottom: 120% !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    background: rgba(0, 0, 0, 0.9) !important;
    color: white !important;
    padding: 0.4rem 0.6rem !important;
    border-radius: 4px !important;
    font-size: 0.75rem !important;
    font-weight: 600 !important;
    white-space: nowrap !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transition: all 0.3s ease !important;
    z-index: 1000 !important;
    pointer-events: none !important;
}

.footer-social-trust .social-icon-premium::after {
    content: '' !important;
    position: absolute !important;
    bottom: 110% !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    border: 4px solid transparent !important;
    border-top-color: rgba(0, 0, 0, 0.9) !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transition: all 0.3s ease !important;
    z-index: 1000 !important;
}

.footer-social-trust .social-icon-premium:hover::before,
.footer-social-trust .social-icon-premium:hover::after {
    opacity: 1 !important;
    visibility: visible !important;
}

/* Create balanced layout with proper spacing */
.footer-brand-showcase .row {
    align-items: flex-start !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    gap: 2rem !important;
}

.footer-brand-showcase .col-lg-6:first-child {
    flex: 0 0 45% !important;
    max-width: 45% !important;
    padding-right: 1rem !important;
}

.footer-brand-showcase .col-lg-6:last-child {
    flex: 0 0 50% !important;
    max-width: 50% !important;
    padding-left: 1rem !important;
}

/* Compact brand section */
.footer-premium-logo {
    display: flex !important;
    align-items: center !important;
    margin-bottom: 0.5rem !important;
}

.logo-icon-container {
    width: 35px !important;
    height: 35px !important;
    margin-right: 0.8rem !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%) !important;
    border-radius: 50% !important;
    backdrop-filter: blur(10px) !important;
}

.logo-icon {
    font-size: 1.2rem !important;
    color: #667eea !important;
}

.logo-main {
    font-size: 1.3rem !important;
    font-weight: 800 !important;
    color: white !important;
    display: block !important;
    line-height: 1.1 !important;
}

.logo-tagline {
    font-size: 0.75rem !important;
    color: rgba(255, 255, 255, 0.7) !important;
    font-weight: 500 !important;
}

.footer-brand-description {
    font-size: 0.8rem !important;
    line-height: 1.4 !important;
    color: rgba(255, 255, 255, 0.8) !important;
    margin-bottom: 0 !important;
}

/* Transform CTA section into compact horizontal layout */
.footer-cta-section {
    padding-left: 0 !important;
    padding-right: 0.5rem !important;
}

.cta-card {
    background: none !important;
    border: none !important;
    border-radius: 0 !important;
    padding: 0 !important;
    text-align: left !important;
    backdrop-filter: none !important;
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 0.8rem !important;
    align-items: flex-start !important;
    overflow: visible !important;
    width: 100% !important;
}

.cta-icon {
    display: none !important; /* Hide large icon */
}

.cta-title {
    display: none !important; /* Hide title */
}

.cta-description {
    display: none !important; /* Hide description */
}

/* Compact horizontal sections */
.cta-buttons {
    display: flex !important;
    flex-direction: row !important;
    gap: 0.6rem !important;
    margin-bottom: 0 !important;
    flex: 0 0 auto !important;
    flex-wrap: wrap !important;
}

/* Add compact sections to CTA area */
.cta-card::after {
    content: '' !important;
    display: block !important;
    width: 100% !important;
    margin-top: 1rem !important;
}

/* Compact info section with contact card */
.footer-cta-section::before {
    content:
    '🔗 Platform: QR Generator • Analytics • API Access    '
    '🌐 Solutions: Marketing • Events • Retail    '
    '📚 Resources: Documentation • API Reference    '
    '🛡️ Support: Help Center • Contact Support' !important;

    display: block !important;
    font-size: 0.75rem !important;
    line-height: 1.5 !important;
    color: rgba(255, 255, 255, 0.7) !important;
    margin-bottom: 0.8rem !important;
    padding: 0.8rem !important;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%) !important;
    border: 1px solid rgba(255, 255, 255, 0.08) !important;
    border-radius: 6px !important;
    backdrop-filter: blur(10px) !important;
    white-space: pre-line !important;
}

/* Remove flying contact card from upper layer */
.footer-cta-section .cta-card::before {
    display: none !important;
}

/* Remove newsletter section completely */
.cta-buttons::after {
    display: none !important;
}

/* Remove social links from upper layer - they'll be in middle layer */
.cta-buttons::before {
    display: none !important;
}

/* Compact CTA buttons */
.btn-cta {
    display: inline-flex !important;
    align-items: center !important;
    gap: 0.4rem !important;
    padding: 0.5rem 1rem !important;
    border-radius: 6px !important;
    text-decoration: none !important;
    font-size: 0.8rem !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    backdrop-filter: blur(10px) !important;
    white-space: nowrap !important;
    flex-shrink: 0 !important;
}

.btn-cta-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.btn-cta-primary:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4) !important;
    color: white !important;
}

.btn-cta-secondary {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%) !important;
    color: rgba(255, 255, 255, 0.9) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.btn-cta-secondary:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%) !important;
    color: white !important;
    transform: translateY(-2px) !important;
}

/* Compact footer bottom - Enhanced for better visibility */
.footer-bottom-premium {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.4) 0%, rgba(26, 31, 54, 0.6) 100%) !important;
    padding: 1.5rem 0 !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
    position: relative !important;
    z-index: 100 !important;
}

.footer-bottom-content-premium {
    position: relative !important;
    z-index: 101 !important;
}

.copyright-section {
    position: relative !important;
    z-index: 102 !important;
}

.copyright-text {
    font-size: 0.75rem !important;
    color: rgba(255, 255, 255, 0.6) !important;
    margin-bottom: 0.2rem !important;
    pointer-events: none !important;
}

.company-info {
    font-size: 0.7rem !important;
    color: rgba(255, 255, 255, 0.5) !important;
    margin: 0 !important;
    pointer-events: none !important;
}

.developer-credit {
    position: relative !important;
    z-index: 102 !important;
}

/* Developer credit styling - Enhanced for better interactivity */
.developer-link {
    color: white !important;
    text-decoration: none !important;
    font-size: 0.8rem !important;
    font-weight: 600 !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 0.4rem !important;
    padding: 0.5rem 1rem !important;
    border-radius: 8px !important;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%) !important;
    border: 1px solid rgba(255, 255, 255, 0.15) !important;
    transition: all 0.3s ease !important;
    backdrop-filter: blur(15px) !important;
    cursor: pointer !important;
    position: relative !important;
    overflow: hidden !important;
}

.developer-link::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: -100% !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent) !important;
    transition: left 0.5s ease !important;
}

.developer-link:hover::before {
    left: 100% !important;
}

.developer-link:hover {
    color: white !important;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.3) 0%, rgba(255, 255, 255, 0.15) 100%) !important;
    border-color: rgba(102, 126, 234, 0.4) !important;
    transform: translateY(-3px) scale(1.02) !important;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4) !important;
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.6) !important;
}

.developer-link i {
    color: #667eea !important;
    font-size: 0.9rem !important;
    transition: all 0.3s ease !important;
}

.developer-link:hover i {
    color: #8fa4f3 !important;
    text-shadow: 0 0 12px rgba(102, 126, 234, 0.8) !important;
    transform: rotate(360deg) !important;
}

.legal-links {
    display: flex !important;
    align-items: center !important;
    gap: 0.6rem !important;
    justify-content: flex-end !important;
    flex-wrap: wrap !important;
    position: relative !important;
    z-index: 102 !important;
}

.legal-link {
    color: rgba(255, 255, 255, 0.7) !important;
    text-decoration: none !important;
    font-size: 0.75rem !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    padding: 0.4rem 0.8rem !important;
    border-radius: 6px !important;
    position: relative !important;
    cursor: pointer !important;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%) !important;
    border: 1px solid rgba(255, 255, 255, 0.08) !important;
    backdrop-filter: blur(10px) !important;
    overflow: hidden !important;
}

.legal-link::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: -100% !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent) !important;
    transition: left 0.4s ease !important;
}

.legal-link:hover::before {
    left: 100% !important;
}

.legal-link:hover {
    color: white !important;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(255, 255, 255, 0.08) 100%) !important;
    border-color: rgba(102, 126, 234, 0.3) !important;
    transform: translateY(-2px) scale(1.05) !important;
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3) !important;
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.4) !important;
}

/* Pipe separator styling - Enhanced visibility */
.pipe-separator {
    color: rgba(255, 255, 255, 0.4) !important;
    font-size: 0.8rem !important;
    font-weight: 400 !important;
    margin: 0 0.4rem !important;
    transition: color 0.3s ease !important;
}

/* Enhance pipe visibility when adjacent links are hovered */
.legal-link:hover + .pipe-separator,
.pipe-separator + .legal-link:hover {
    color: rgba(102, 126, 234, 0.6) !important;
}

/* Ensure all footer bottom links are clickable and override any conflicting styles */
.footer-bottom-premium a {
    pointer-events: auto !important;
    cursor: pointer !important;
    position: relative !important;
    z-index: 1000 !important;
    display: inline-block !important;
}

.footer-bottom-premium .developer-link,
.footer-bottom-premium .legal-link {
    pointer-events: auto !important;
    cursor: pointer !important;
    position: relative !important;
    z-index: 1001 !important;
    display: inline-flex !important;
}

/* Force clickability for all interactive elements */
.developer-credit a,
.legal-links a {
    pointer-events: auto !important;
    cursor: pointer !important;
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
}

/* Mobile optimizations */
@media (max-width: 991.98px) {
    .footer-brand-showcase .row {
        gap: 1rem !important;
    }

    .footer-brand-showcase .col-lg-6:first-child {
        flex: 0 0 100% !important;
        max-width: 100% !important;
        margin-bottom: 1rem !important;
        padding-right: 0 !important;
    }

    .footer-brand-showcase .col-lg-6:last-child {
        flex: 0 0 100% !important;
        max-width: 100% !important;
        padding-left: 0 !important;
    }

    .footer-contact-newsletter .row {
        gap: 1rem !important;
    }

    .footer-contact-newsletter .row .col-lg-6:first-child {
        flex: 0 0 100% !important;
        max-width: 100% !important;
        margin-bottom: 1rem !important;
    }

    .footer-contact-newsletter .row .col-lg-6:last-child {
        flex: 0 0 100% !important;
        max-width: 100% !important;
    }

    .cta-card {
        gap: 0.8rem !important;
        overflow: visible !important;
    }

    .footer-cta-section {
        padding-right: 0 !important;
    }

    .footer-cta-section::before {
        font-size: 0.7rem !important;
        padding: 0.6rem !important;
        word-wrap: break-word !important;
    }

    .cta-buttons {
        flex-direction: column !important;
        gap: 0.5rem !important;
    }
}

@media (max-width: 767.98px) {
    .ultra-premium-footer {
        margin: 0.5rem 0.8rem 0 0.8rem !important;
        border-radius: 8px 8px 0 0 !important;
    }

    .footer-main-content {
        padding: 1.2rem 0 !important;
    }

    /* Mobile brand section optimization */
    .footer-premium-logo {
        justify-content: center !important;
        text-align: center !important;
        margin-bottom: 0.8rem !important;
    }

    .footer-brand-description {
        text-align: center !important;
        font-size: 0.8rem !important;
        line-height: 1.4 !important;
        margin-bottom: 1rem !important;
    }

    /* Mobile CTA section optimization */
    .cta-buttons {
        align-items: center !important;
        justify-content: center !important;
        gap: 0.8rem !important;
    }

    .btn-cta {
        font-size: 0.8rem !important;
        padding: 0.6rem 1rem !important;
        min-width: 120px !important;
    }

    /* Mobile contact card optimization */
    .footer-contact-newsletter .contact-info-premium::after {
        max-width: 100% !important;
        margin: 0.8rem auto 0 auto !important;
        padding: 1rem !important;
        font-size: 0.8rem !important;
    }

    /* Mobile newsletter optimization */
    .footer-contact-newsletter .newsletter-card {
        margin-top: 0.8rem !important;
        padding: 1.2rem !important;
    }

    .footer-contact-newsletter .newsletter-input-premium {
        font-size: 0.85rem !important;
        padding: 0.6rem 0.8rem !important;
    }

    .footer-contact-newsletter .newsletter-btn-premium {
        font-size: 0.8rem !important;
        padding: 0.6rem 1rem !important;
        min-width: 100px !important;
    }

    /* Mobile form optimization */
    .footer-contact-newsletter .input-group-premium {
        flex-direction: column !important;
        gap: 0.6rem !important;
    }

    /* Mobile social section optimization */
    .footer-contact-newsletter::before {
        font-size: 0.7rem !important;
        padding: 0.6rem !important;
        margin-top: 0.8rem !important;
    }

    /* Mobile footer bottom optimization */
    .footer-bottom-premium {
        padding: 0.8rem 0 !important;
    }

    .footer-bottom-premium .row > div {
        text-align: center !important;
        margin-bottom: 0.6rem !important;
    }

    .copyright-text {
        font-size: 0.7rem !important;
    }

    .company-info {
        font-size: 0.65rem !important;
    }

    .developer-link {
        font-size: 0.75rem !important;
        padding: 0.5rem 0.8rem !important;
        min-height: 44px !important;
        min-width: 120px !important;
    }

    .legal-links {
        justify-content: center !important;
        margin-top: 0.6rem !important;
        gap: 0.4rem !important;
        flex-wrap: wrap !important;
    }

    .legal-link {
        font-size: 0.7rem !important;
        padding: 0.5rem 0.6rem !important;
        min-height: 44px !important;
        min-width: 80px !important;
        text-align: center !important;
    }

    .pipe-separator {
        font-size: 0.7rem !important;
        margin: 0 0.2rem !important;
    }
}

/* Production Optimizations */

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    .ultra-premium-footer *,
    .ultra-premium-footer *::before,
    .ultra-premium-footer *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .ultra-premium-footer {
        border: 2px solid white !important;
    }

    .btn-cta,
    .newsletter-btn-premium {
        border: 2px solid white !important;
    }

    .newsletter-input-premium {
        border: 2px solid white !important;
    }
}

/* Dark mode optimization */
@media (prefers-color-scheme: dark) {
    .ultra-premium-footer {
        background: linear-gradient(135deg, #000000 0%, #1a1a1a 25%, #0d0d0d 50%, #000000 100%) !important;
    }
}

/* Print styles */
@media print {
    .ultra-premium-footer {
        background: white !important;
        color: black !important;
        box-shadow: none !important;
        backdrop-filter: none !important;
    }

    .ultra-premium-footer * {
        color: black !important;
        background: transparent !important;
        box-shadow: none !important;
    }

    .btn-cta,
    .newsletter-btn-premium {
        border: 1px solid black !important;
    }
}

/* Performance optimizations for large screens */
@media (min-width: 1920px) {
    .ultra-premium-footer {
        max-width: 1800px !important;
        margin-left: auto !important;
        margin-right: auto !important;
    }
}

/* Optimize for very small screens */
@media (max-width: 320px) {
    .ultra-premium-footer {
        margin: 0.3rem 0.5rem 0 0.5rem !important;
    }

    .footer-main-content {
        padding: 1rem 0 !important;
    }

    .footer-contact-newsletter .contact-info-premium::after {
        font-size: 0.75rem !important;
        padding: 0.8rem !important;
    }

    .footer-contact-newsletter .newsletter-card {
        padding: 1rem !important;
    }

    .btn-cta {
        font-size: 0.75rem !important;
        padding: 0.5rem 0.8rem !important;
        min-width: 100px !important;
    }
}

/* Focus management for keyboard navigation */
.ultra-premium-footer a:focus,
.ultra-premium-footer button:focus,
.ultra-premium-footer input:focus {
    outline: 2px solid #667eea !important;
    outline-offset: 2px !important;
    z-index: 1000 !important;
}

/* Ensure proper stacking context */
.ultra-premium-footer {
    isolation: isolate !important;
}

/* Optimize backdrop-filter performance */
@supports not (backdrop-filter: blur(10px)) {
    .ultra-premium-footer,
    .footer-contact-newsletter .contact-info-premium::after,
    .footer-contact-newsletter .newsletter-card {
        background: rgba(15, 20, 25, 0.95) !important;
    }
}
