"""
Test script for cache functionality
"""
import os
import sys
import django
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'qrcode_project.settings')
django.setup()

# Import the cache utilities
from ai_services.cache_utils import get_cached_suggestions, cache_suggestions

def test_cache():
    """Test the cache functionality"""
    print("Testing cache functionality...")
    
    try:
        # Create a test key
        test_key = "test_suggestions:test_input"
        
        # Create test data
        test_data = [
            {
                "title": "Test Title 1",
                "content": "Test Content 1"
            },
            {
                "title": "Test Title 2",
                "content": "Test Content 2"
            }
        ]
        
        # Cache the test data
        print(f"Caching test data with key: {test_key}")
        cache_suggestions(test_key, test_data)
        
        # Retrieve the test data
        print(f"Retrieving test data with key: {test_key}")
        cached_data = get_cached_suggestions(test_key)
        
        # Check if the data was retrieved successfully
        if cached_data:
            print(f"Successfully retrieved {len(cached_data)} suggestions from cache")
            for i, suggestion in enumerate(cached_data):
                print(f"\nSuggestion {i+1}:")
                print(f"Title: {suggestion.get('title', 'No title')}")
                print(f"Content: {suggestion.get('content', 'No content')}")
            
            return True
        else:
            print("Failed to retrieve data from cache")
            return False
    except Exception as e:
        print(f"\nError: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_cache()
    if success:
        print("\nTest completed successfully!")
    else:
        print("\nTest failed!")
        sys.exit(1)
