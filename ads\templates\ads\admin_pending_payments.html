{% extends 'base.html' %}
{% load static %}

{% block title %}Pending Payments - Admin{% endblock %}

{% block extra_css %}
<!-- Include common ads CSS -->
{% include 'ads/includes/ads_common_css.html' %}
<style>
    .payment-status {
        display: inline-block;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
    }

    .payment-status-paid {
        background-color: rgba(40, 167, 69, 0.1);
        color: #28a745;
    }

    .payment-status-pending {
        background-color: rgba(255, 193, 7, 0.1);
        color: #ffc107;
    }

    .payment-status-failed {
        background-color: rgba(220, 53, 69, 0.1);
        color: #dc3545;
    }

    .payment-method {
        display: inline-flex;
        align-items: center;
        gap: 5px;
    }

    .payment-method-icon {
        font-size: 16px;
    }

    .payment-amount {
        font-weight: 700;
        color: #1a237e;
    }

    .payment-date {
        color: #6c757d;
        font-size: 14px;
    }

    .payment-user {
        font-weight: 600;
    }

    .payment-ad-title {
        color: #3949ab;
        font-weight: 600;
    }

    .payment-ad-title:hover {
        text-decoration: underline;
    }

    .action-btn {
        padding: 5px 10px;
        font-size: 14px;
    }

    .empty-state {
        text-align: center;
        padding: 50px 20px;
    }

    .empty-state-icon {
        font-size: 48px;
        color: #adb5bd;
        margin-bottom: 15px;
    }

    .empty-state-text {
        font-size: 18px;
        color: #6c757d;
        margin-bottom: 10px;
    }

    .empty-state-subtext {
        font-size: 14px;
        color: #adb5bd;
    }
</style>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="ads-page-header">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <nav aria-label="breadcrumb" class="ads-breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'index' %}">Home</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'ads:dashboard' %}">Ads Dashboard</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Pending Payments</li>
                    </ol>
                </nav>

                <h1 class="display-6 text-center mb-1 ads-page-title">Pending Payments</h1>
                <p class="lead text-center mb-2 ads-page-subtitle">Review and approve payments for ads</p>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="ads-card animate__animated animate__fadeIn">
                <div class="ads-card-header d-flex justify-content-between align-items-center">
                    <h3 class="ads-card-title">Payments Awaiting Approval</h3>
                    <span class="badge bg-primary">{{ total_count }} Pending</span>
                </div>

                <div class="card-body">
                    {% if page_obj %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>User</th>
                                        <th>Ad</th>
                                        <th>Amount</th>
                                        <th>Method</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for transaction in page_obj %}
                                        {% with time_diff=transaction.timestamp|timesince %}
                                        <tr {% if "minute" in time_diff or "second" in time_diff %}class="table-warning animate__animated animate__pulse"{% endif %}>
                                            <td>{{ transaction.id }}</td>
                                            <td class="payment-user">{{ transaction.user.username }}</td>
                                            <td>
                                                <a href="{% url 'ads:admin_view_ad' transaction.ad.slug %}" class="payment-ad-title">
                                                    {{ transaction.ad.title }}
                                                </a>
                                                {% if "minute" in time_diff or "second" in time_diff %}
                                                    <span class="badge bg-danger animate__animated animate__fadeIn animate__infinite">New</span>
                                                {% endif %}
                                            </td>
                                            <td class="payment-amount">{{ transaction.amount }} KSH</td>
                                            <td>
                                                <div class="payment-method">
                                                    {% if transaction.payment_gateway == 'mpesa' %}
                                                        <i class="fas fa-mobile-alt payment-method-icon"></i> M-PESA
                                                    {% elif transaction.payment_gateway == 'card' %}
                                                        <i class="far fa-credit-card payment-method-icon"></i> Card
                                                    {% elif transaction.payment_gateway == 'bank' %}
                                                        <i class="fas fa-university payment-method-icon"></i> Bank
                                                    {% elif transaction.payment_gateway == 'paypal' %}
                                                        <i class="fab fa-paypal payment-method-icon"></i> PayPal
                                                    {% endif %}
                                                </div>
                                            </td>
                                            <td class="payment-date">
                                                {{ transaction.timestamp|date:"M d, Y H:i" }}
                                                {% if "minute" in time_diff or "second" in time_diff %}
                                                    <small class="text-danger">({{ time_diff }} ago)</small>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <a href="{% url 'ads:admin_approve_payment' transaction.id %}" class="btn btn-success btn-sm action-btn">
                                                    <i class="fas fa-check"></i> Approve
                                                </a>
                                            </td>
                                        </tr>
                                        {% endwith %}
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if page_obj.has_other_pages %}
                            <nav aria-label="Page navigation" class="mt-4">
                                <ul class="pagination justify-content-center">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page=1" aria-label="First">
                                                <span aria-hidden="true">&laquo;&laquo;</span>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}" aria-label="Previous">
                                                <span aria-hidden="true">&laquo;</span>
                                            </a>
                                        </li>
                                    {% endif %}

                                    {% for num in page_obj.paginator.page_range %}
                                        {% if page_obj.number == num %}
                                            <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                            <li class="page-item"><a class="page-link" href="?page={{ num }}">{{ num }}</a></li>
                                        {% endif %}
                                    {% endfor %}

                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.next_page_number }}" aria-label="Next">
                                                <span aria-hidden="true">&raquo;</span>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}" aria-label="Last">
                                                <span aria-hidden="true">&raquo;&raquo;</span>
                                            </a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="empty-state">
                            <div class="empty-state-icon">
                                <i class="fas fa-money-check-alt"></i>
                            </div>
                            <div class="empty-state-text">No pending payments to approve</div>
                            <div class="empty-state-subtext">All payments have been processed</div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
