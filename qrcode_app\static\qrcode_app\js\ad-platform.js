document.addEventListener('DOMContentLoaded', function() {
    // Initialize sidebar navigation
    initSidebarNavigation();
    
    // Initialize create ad buttons
    initCreateAdButtons();
    
    // Initialize placement selection
    initPlacementSelection();
});

// Initialize sidebar navigation
function initSidebarNavigation() {
    const sidebarLinks = document.querySelectorAll('.sidebar-link');
    
    sidebarLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Prevent default only if the link is pointing to a section on the same page
            const targetId = this.getAttribute('href');
            if (targetId.startsWith('#')) {
                e.preventDefault();
                
                // Remove active class from all links
                sidebarLinks.forEach(link => {
                    link.parentElement.classList.remove('active');
                });
                
                // Add active class to clicked link
                this.parentElement.classList.add('active');
                
                // Scroll to the target section
                const targetSection = document.querySelector(targetId);
                if (targetSection) {
                    targetSection.scrollIntoView({ behavior: 'smooth' });
                }
            }
        });
    });
}

// Initialize create ad buttons
function initCreateAdButtons() {
    const createAdButtons = document.querySelectorAll('.primary-btn');
    
    createAdButtons.forEach(button => {
        if (button.textContent.includes('Create New Ad') || button.textContent.includes('Create Your First Ad')) {
            button.addEventListener('click', function() {
                // Scroll to the ad placement section
                const adPlacementSection = document.querySelector('#ad-placement');
                if (adPlacementSection) {
                    adPlacementSection.scrollIntoView({ behavior: 'smooth' });
                    
                    // Highlight the section with a subtle animation
                    adPlacementSection.classList.add('highlight-section');
                    setTimeout(() => {
                        adPlacementSection.classList.remove('highlight-section');
                    }, 1500);
                }
                
                // Update sidebar active state
                const sidebarLinks = document.querySelectorAll('.sidebar-link');
                sidebarLinks.forEach(link => {
                    link.parentElement.classList.remove('active');
                    if (link.getAttribute('href') === '#create') {
                        link.parentElement.classList.add('active');
                    }
                });
            });
        }
    });
}

// Initialize placement selection
function initPlacementSelection() {
    const placementButtons = document.querySelectorAll('.placement-card .secondary-btn');
    
    placementButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Get the placement type from the card
            const placementCard = this.closest('.placement-card');
            const placementTitle = placementCard.querySelector('h3').textContent;
            
            // Show a confirmation dialog
            if (confirm(`Would you like to create an ad for the "${placementTitle}" placement?`)) {
                // In a real application, this would navigate to a form or open a modal
                // For now, we'll just show an alert
                alert(`You've selected the "${placementTitle}" placement. In a complete implementation, this would open a form to create your ad.`);
                
                // Highlight the selected card
                document.querySelectorAll('.placement-card').forEach(card => {
                    card.classList.remove('selected');
                });
                placementCard.classList.add('selected');
            }
        });
    });
}

// Add CSS class for highlighting sections
document.head.insertAdjacentHTML('beforeend', `
<style>
    @keyframes highlightSection {
        0% { background-color: transparent; }
        50% { background-color: rgba(59, 130, 246, 0.1); }
        100% { background-color: transparent; }
    }
    
    .highlight-section {
        animation: highlightSection 1.5s ease;
    }
    
    .placement-card.selected {
        border-color: var(--primary-blue);
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
    }
</style>
`);

// Function to create a new ad (placeholder for future implementation)
function createNewAd(placementType) {
    console.log(`Creating new ad for placement: ${placementType}`);
    
    // This would typically open a modal or navigate to a form
    // For now, we'll just log to the console
}

// Function to show a notification
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    
    // Add icon based on type
    let icon = 'info-circle';
    if (type === 'success') icon = 'check-circle';
    if (type === 'error') icon = 'exclamation-circle';
    
    notification.innerHTML = `
        <i class="fas fa-${icon}"></i>
        <span>${message}</span>
        <button class="notification-close"><i class="fas fa-times"></i></button>
    `;
    
    // Add to the DOM
    document.body.appendChild(notification);
    
    // Show the notification
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);
    
    // Set up close button
    const closeButton = notification.querySelector('.notification-close');
    closeButton.addEventListener('click', () => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    });
    
    // Auto-close after 5 seconds
    setTimeout(() => {
        if (document.body.contains(notification)) {
            notification.classList.remove('show');
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    notification.remove();
                }
            }, 300);
        }
    }, 5000);
}

// Add CSS for notifications
document.head.insertAdjacentHTML('beforeend', `
<style>
    .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 1rem;
        background-color: white;
        border-radius: 0.375rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        gap: 0.75rem;
        z-index: 1000;
        max-width: 350px;
        transform: translateX(120%);
        transition: transform 0.3s ease;
        border-left: 4px solid var(--primary-blue);
    }
    
    .notification.show {
        transform: translateX(0);
    }
    
    .notification i {
        font-size: 1.25rem;
        flex-shrink: 0;
    }
    
    .notification span {
        font-size: 0.875rem;
        color: var(--gray-700);
        flex-grow: 1;
    }
    
    .notification-close {
        background: none;
        border: none;
        color: var(--gray-400);
        cursor: pointer;
        font-size: 0.875rem;
        padding: 0.25rem;
        transition: color 0.2s ease;
    }
    
    .notification-close:hover {
        color: var(--gray-700);
    }
    
    .notification-success {
        border-left-color: var(--green);
    }
    
    .notification-success i {
        color: var(--green);
    }
    
    .notification-error {
        border-left-color: var(--red);
    }
    
    .notification-error i {
        color: var(--red);
    }
    
    .notification-info i {
        color: var(--primary-blue);
    }
</style>
`);
