{% extends 'base.html' %}
{% load static %}

{% block title %}{{ notification.title }} | Notifications{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'notifications/css/notifications.css' %}">
<style>
    /* Ultra-Premium Notification Detail Styling */
    body {
        background:
            linear-gradient(135deg, #000000 0%, #0a0a0a 10%, #1a1a2e 25%, #16213e 40%, #0f3460 60%, #533483 80%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        position: relative;
        overflow-x: hidden;
    }

    /* Premium animated background with notification detail patterns */
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 15% 85%, rgba(102, 126, 234, 0.5) 0%, transparent 35%),
            radial-gradient(circle at 85% 15%, rgba(255, 255, 255, 0.2) 0%, transparent 30%),
            radial-gradient(circle at 40% 60%, rgba(118, 75, 162, 0.4) 0%, transparent 40%),
            radial-gradient(circle at 70% 30%, rgba(83, 52, 131, 0.3) 0%, transparent 25%);
        z-index: -1;
        animation: detailFloat 30s ease-in-out infinite;
    }

    @keyframes detailFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
        25% { transform: translateY(-20px) rotate(1deg) scale(1.02); }
        50% { transform: translateY(15px) rotate(-0.5deg) scale(0.98); }
        75% { transform: translateY(-10px) rotate(0.8deg) scale(1.01); }
    }

    /* Premium Container */
    .notification-detail-container {
        max-width: 1000px;
        margin: 0 auto;
        padding: 4rem 2rem;
        position: relative;
        z-index: 1;
    }

    /* Ultra-Premium Header */
    .notification-detail-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 3rem;
        padding: 2rem 0;
        border-bottom: 2px solid rgba(255, 255, 255, 0.1);
    }

    .notification-detail-title {
        font-size: 2.5rem;
        font-weight: 900;
        color: white;
        margin: 0;
        background: linear-gradient(135deg, #ffffff 0%, #e0e0e0 50%, #ffffff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
        animation: titleDetailGlow 4s ease-in-out infinite;
    }

    @keyframes titleDetailGlow {
        0%, 100% { text-shadow: 0 8px 32px rgba(0, 0, 0, 0.5); }
        50% { text-shadow: 0 8px 32px rgba(102, 126, 234, 0.4), 0 0 60px rgba(255, 255, 255, 0.3); }
    }

    .notification-detail-actions {
        display: flex;
        gap: 1rem;
    }

    /* Ultra-Premium Detail Card */
    .notification-detail-card {
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.95) 0%,
            rgba(248, 249, 250, 0.9) 50%,
            rgba(255, 255, 255, 0.95) 100%);
        border-radius: 32px;
        box-shadow:
            0 30px 60px rgba(0, 0, 0, 0.25),
            0 20px 40px rgba(0, 0, 0, 0.15),
            inset 0 2px 0 rgba(255, 255, 255, 0.4);
        border: 2px solid rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(30px);
        overflow: hidden;
        position: relative;
        animation: cardDetailEntry 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }

    .notification-detail-card::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        width: calc(100% + 4px);
        height: calc(100% + 4px);
        background: conic-gradient(from 0deg,
            rgba(102, 126, 234, 0.3) 0deg,
            rgba(255, 255, 255, 0.2) 90deg,
            rgba(118, 75, 162, 0.3) 180deg,
            rgba(255, 255, 255, 0.1) 270deg,
            rgba(102, 126, 234, 0.3) 360deg);
        border-radius: 34px;
        z-index: -1;
        animation: cardDetailBorderGlow 10s linear infinite;
    }

    @keyframes cardDetailEntry {
        0% {
            opacity: 0;
            transform: translateY(50px) scale(0.95);
            filter: blur(15px);
        }
        100% {
            opacity: 1;
            transform: translateY(0) scale(1);
            filter: blur(0px);
        }
    }

    @keyframes cardDetailBorderGlow {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Premium Header Bar */
    .notification-detail-header-bar {
        height: 12px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        position: relative;
        overflow: hidden;
    }

    .notification-detail-header-bar::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
            transparent 0%,
            rgba(255, 255, 255, 0.4) 50%,
            transparent 100%);
        animation: headerBarShimmer 3s ease-in-out infinite;
    }

    @keyframes headerBarShimmer {
        0% { left: -100%; }
        100% { left: 100%; }
    }

    .notification-detail-header-bar.success {
        background: linear-gradient(90deg, #10b981 0%, #059669 100%);
    }

    .notification-detail-header-bar.warning {
        background: linear-gradient(90deg, #f59e0b 0%, #d97706 100%);
    }

    .notification-detail-header-bar.error {
        background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%);
    }

    /* Premium Content */
    .notification-detail-content {
        padding: 3rem;
        position: relative;
        z-index: 1;
    }

    /* Ultra-Premium Meta Section */
    .notification-detail-meta {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
        padding-bottom: 2rem;
        border-bottom: 2px solid rgba(102, 126, 234, 0.2);
    }

    .notification-detail-meta-item {
        display: flex;
        flex-direction: column;
        padding: 1.5rem;
        background: linear-gradient(135deg,
            rgba(102, 126, 234, 0.08) 0%,
            rgba(255, 255, 255, 0.15) 100%);
        border: 1px solid rgba(102, 126, 234, 0.2);
        border-radius: 20px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .notification-detail-meta-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
            transparent 0%,
            rgba(102, 126, 234, 0.1) 50%,
            transparent 100%);
        transition: left 0.6s ease;
    }

    .notification-detail-meta-item:hover {
        background: linear-gradient(135deg,
            rgba(102, 126, 234, 0.15) 0%,
            rgba(255, 255, 255, 0.25) 100%);
        transform: translateY(-5px);
        box-shadow:
            0 15px 30px rgba(102, 126, 234, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }

    .notification-detail-meta-item:hover::before {
        left: 100%;
    }

    .notification-detail-meta-label {
        font-size: 0.8rem;
        color: #6b7280;
        margin-bottom: 0.8rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 1px;
        position: relative;
        z-index: 1;
    }

    .notification-detail-meta-value {
        font-size: 1.1rem;
        font-weight: 700;
        color: #1a1a2e;
        display: flex;
        align-items: center;
        position: relative;
        z-index: 1;
    }

    .notification-detail-meta-value i {
        margin-right: 0.8rem;
        color: #667eea;
        font-size: 1.2rem;
        filter: drop-shadow(0 2px 4px rgba(102, 126, 234, 0.3));
    }

    /* Premium Message */
    .notification-detail-message {
        font-size: 1.3rem;
        line-height: 1.8;
        color: #2c3e50;
        margin-bottom: 3rem;
        padding: 2rem;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.8) 0%,
            rgba(248, 249, 250, 0.6) 100%);
        border: 1px solid rgba(102, 126, 234, 0.2);
        border-radius: 24px;
        box-shadow:
            0 8px 20px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.5);
        font-weight: 500;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    /* Premium Footer */
    .notification-detail-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 2rem;
        border-top: 2px solid rgba(102, 126, 234, 0.2);
        gap: 1rem;
        flex-wrap: wrap;
    }

    /* Premium Related Content */
    .notification-detail-related {
        margin-top: 3rem;
    }

    .notification-detail-related-title {
        font-size: 1.5rem;
        font-weight: 800;
        color: #1a1a2e;
        margin-bottom: 1.5rem;
        background: linear-gradient(135deg, #1a1a2e 0%, #2c3e50 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .notification-detail-related-content {
        background: linear-gradient(135deg,
            rgba(102, 126, 234, 0.08) 0%,
            rgba(255, 255, 255, 0.15) 100%);
        border: 1px solid rgba(102, 126, 234, 0.2);
        border-radius: 24px;
        padding: 2rem;
        box-shadow:
            0 8px 20px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.5);
    }

    /* Ultra-Premium Badges */
    .notification-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.8rem 1.5rem;
        border-radius: 25px;
        font-size: 0.9rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow:
            0 4px 12px rgba(0, 0, 0, 0.15),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .notification-badge::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
            transparent 0%,
            rgba(255, 255, 255, 0.3) 50%,
            transparent 100%);
        transition: left 0.6s ease;
    }

    .notification-badge:hover::before {
        left: 100%;
    }

    .notification-badge:hover {
        transform: translateY(-2px) scale(1.05);
    }

    .notification-badge.info {
        background: linear-gradient(135deg,
            rgba(102, 126, 234, 0.2) 0%,
            rgba(118, 75, 162, 0.2) 100%);
        color: #667eea;
        border: 1px solid rgba(102, 126, 234, 0.3);
    }

    .notification-badge.success {
        background: linear-gradient(135deg,
            rgba(16, 185, 129, 0.2) 0%,
            rgba(5, 150, 105, 0.2) 100%);
        color: #10b981;
        border: 1px solid rgba(16, 185, 129, 0.3);
    }

    .notification-badge.warning {
        background: linear-gradient(135deg,
            rgba(245, 158, 11, 0.2) 0%,
            rgba(217, 119, 6, 0.2) 100%);
        color: #f59e0b;
        border: 1px solid rgba(245, 158, 11, 0.3);
    }

    .notification-badge.error {
        background: linear-gradient(135deg,
            rgba(239, 68, 68, 0.2) 0%,
            rgba(220, 38, 38, 0.2) 100%);
        color: #ef4444;
        border: 1px solid rgba(239, 68, 68, 0.3);
    }

    .notification-badge i {
        margin-right: 0.8rem;
        font-size: 1rem;
        filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.2));
        position: relative;
        z-index: 1;
    }

    .notification-badge span {
        position: relative;
        z-index: 1;
    }

    /* Premium Buttons */
    .btn {
        padding: 1rem 2rem;
        border-radius: 20px;
        font-weight: 700;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 1px;
        transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        border: 2px solid transparent;
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(20px);
    }

    .btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
            transparent 0%,
            rgba(255, 255, 255, 0.3) 50%,
            transparent 100%);
        transition: left 0.6s ease;
    }

    .btn:hover::before {
        left: 100%;
    }

    .btn-outline-primary {
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.9) 0%,
            rgba(248, 249, 250, 0.8) 100%);
        color: #667eea;
        border-color: rgba(102, 126, 234, 0.3);
        box-shadow:
            0 8px 20px rgba(102, 126, 234, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.5);
    }

    .btn-outline-primary:hover {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-color: rgba(255, 255, 255, 0.4);
        transform: translateY(-3px) scale(1.05);
        box-shadow:
            0 15px 35px rgba(102, 126, 234, 0.4),
            inset 0 2px 0 rgba(255, 255, 255, 0.3);
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-color: rgba(255, 255, 255, 0.3);
        box-shadow:
            0 8px 20px rgba(102, 126, 234, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        transform: translateY(-3px) scale(1.05);
        box-shadow:
            0 15px 35px rgba(102, 126, 234, 0.5),
            inset 0 2px 0 rgba(255, 255, 255, 0.3);
        color: white;
    }

    .btn-outline-success {
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.9) 0%,
            rgba(248, 249, 250, 0.8) 100%);
        color: #10b981;
        border-color: rgba(16, 185, 129, 0.3);
        box-shadow:
            0 8px 20px rgba(16, 185, 129, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.5);
    }

    .btn-outline-success:hover {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
        border-color: rgba(255, 255, 255, 0.4);
        transform: translateY(-3px) scale(1.05);
        box-shadow:
            0 15px 35px rgba(16, 185, 129, 0.4),
            inset 0 2px 0 rgba(255, 255, 255, 0.3);
    }

    .btn-outline-danger {
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.9) 0%,
            rgba(248, 249, 250, 0.8) 100%);
        color: #ef4444;
        border-color: rgba(239, 68, 68, 0.3);
        box-shadow:
            0 8px 20px rgba(239, 68, 68, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.5);
    }

    .btn-outline-danger:hover {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        color: white;
        border-color: rgba(255, 255, 255, 0.4);
        transform: translateY(-3px) scale(1.05);
        box-shadow:
            0 15px 35px rgba(239, 68, 68, 0.4),
            inset 0 2px 0 rgba(255, 255, 255, 0.3);
    }

    /* Premium Responsive Design */
    @media (max-width: 991px) {
        .notification-detail-container {
            padding: 3rem 1.5rem;
        }

        .notification-detail-title {
            font-size: 2rem;
        }

        .notification-detail-content {
            padding: 2.5rem;
        }

        .notification-detail-meta {
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 1.5rem;
        }
    }

    @media (max-width: 768px) {
        .notification-detail-container {
            padding: 2rem 1rem;
        }

        .notification-detail-title {
            font-size: 1.8rem;
        }

        .notification-detail-header {
            flex-direction: column;
            gap: 1.5rem;
            text-align: center;
        }

        .notification-detail-content {
            padding: 2rem;
        }

        .notification-detail-meta {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .notification-detail-meta-item {
            padding: 1rem;
        }

        .notification-detail-message {
            font-size: 1.1rem;
            padding: 1.5rem;
        }

        .notification-detail-footer {
            flex-direction: column;
            gap: 1.5rem;
            align-items: stretch;
        }

        .btn {
            padding: 0.8rem 1.5rem;
            font-size: 0.8rem;
        }
    }

    @media (max-width: 480px) {
        .notification-detail-container {
            padding: 1.5rem 0.5rem;
        }

        .notification-detail-title {
            font-size: 1.5rem;
        }

        .notification-detail-content {
            padding: 1.5rem;
        }

        .notification-detail-meta-item {
            padding: 0.8rem;
        }

        .notification-detail-message {
            font-size: 1rem;
            padding: 1rem;
        }

        .notification-badge {
            padding: 0.6rem 1rem;
            font-size: 0.8rem;
        }

        .btn {
            padding: 0.7rem 1.2rem;
            font-size: 0.75rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="notification-detail-container">
    <div class="notification-detail-header">
        <h1 class="notification-detail-title">Notification Details</h1>
        <div class="notification-detail-actions">
            <a href="{% url 'notifications:notification_list' %}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-2"></i>Back to Notifications
            </a>
        </div>
    </div>

    <div class="notification-detail-card">
        <div class="notification-detail-header-bar {{ notification.notification_type }}"></div>
        <div class="notification-detail-content">
            <h2 class="notification-detail-title mb-4">{{ notification.title }}</h2>

            <div class="notification-detail-meta">
                <div class="notification-detail-meta-item">
                    <span class="notification-detail-meta-label">Type</span>
                    <span class="notification-detail-meta-value">
                        <span class="notification-badge {{ notification.notification_type }}">
                            {% if notification.notification_type == 'info' %}
                                <i class="fas fa-info-circle"></i> Information
                            {% elif notification.notification_type == 'success' %}
                                <i class="fas fa-check-circle"></i> Success
                            {% elif notification.notification_type == 'warning' %}
                                <i class="fas fa-exclamation-triangle"></i> Warning
                            {% elif notification.notification_type == 'error' %}
                                <i class="fas fa-times-circle"></i> Error
                            {% endif %}
                        </span>
                    </span>
                </div>

                <div class="notification-detail-meta-item">
                    <span class="notification-detail-meta-label">Category</span>
                    <span class="notification-detail-meta-value">
                        {% if notification.category == 'system' %}
                            <i class="fas fa-cog"></i>
                        {% elif notification.category == 'ad' %}
                            <i class="fas fa-ad"></i>
                        {% elif notification.category == 'payment' %}
                            <i class="fas fa-money-bill"></i>
                        {% elif notification.category == 'qr_code' %}
                            <i class="fas fa-qrcode"></i>
                        {% elif notification.category == 'user' %}
                            <i class="fas fa-user"></i>
                        {% elif notification.category == 'campaign' %}
                            <i class="fas fa-bullhorn"></i>
                        {% elif notification.category == 'analytics' %}
                            <i class="fas fa-chart-bar"></i>
                        {% else %}
                            <i class="fas fa-bell"></i>
                        {% endif %}
                        {{ notification.get_category_display }}
                    </span>
                </div>

                <div class="notification-detail-meta-item">
                    <span class="notification-detail-meta-label">Date</span>
                    <span class="notification-detail-meta-value">
                        <i class="far fa-calendar-alt"></i>
                        {{ notification.created_at|date:"F j, Y" }}
                    </span>
                </div>

                <div class="notification-detail-meta-item">
                    <span class="notification-detail-meta-label">Time</span>
                    <span class="notification-detail-meta-value">
                        <i class="far fa-clock"></i>
                        {{ notification.created_at|time:"g:i A" }}
                    </span>
                </div>

                <div class="notification-detail-meta-item">
                    <span class="notification-detail-meta-label">Status</span>
                    <span class="notification-detail-meta-value">
                        {% if notification.is_read %}
                            <i class="fas fa-envelope-open text-success"></i> Read
                        {% else %}
                            <i class="fas fa-envelope text-primary"></i> Unread
                        {% endif %}
                    </span>
                </div>
            </div>

            <div class="notification-detail-message">
                {{ notification.message }}
            </div>

            {% if notification.content_object %}
            <div class="notification-detail-related">
                <h3 class="notification-detail-related-title">Related Content</h3>
                <div class="notification-detail-related-content">
                    <p>
                        <strong>Type:</strong> {{ notification.content_type.name|title }}
                    </p>
                    <p>
                        <strong>ID:</strong> {{ notification.object_id }}
                    </p>
                    {% if notification.action_url %}
                    <div class="mt-3">
                        <a href="{{ notification.action_url }}" class="btn btn-primary">
                            <i class="fas fa-external-link-alt me-2"></i>View Related Content
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}

            <div class="notification-detail-footer">
                <div>
                    {% if notification.is_read %}
                    <form method="post" action="{% url 'notifications:mark_as_unread' pk=notification.pk %}" class="d-inline">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="fas fa-envelope me-2"></i>Mark as Unread
                        </button>
                    </form>
                    {% else %}
                    <form method="post" action="{% url 'notifications:mark_as_read' pk=notification.pk %}" class="d-inline">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-outline-success">
                            <i class="fas fa-envelope-open me-2"></i>Mark as Read
                        </button>
                    </form>
                    {% endif %}
                </div>

                <form method="post" action="{% url 'notifications:delete_notification' pk=notification.pk %}" class="d-inline">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-outline-danger">
                        <i class="fas fa-trash me-2"></i>Delete
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
