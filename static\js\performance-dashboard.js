/**
 * Performance Dashboard JavaScript
 * Enterprise QR Code Generator
 */

// Cache DOM elements
const refreshIntervalSelect = document.getElementById('refreshInterval');
const runOptimizationBtn = document.getElementById('runOptimizationBtn');
const toolButtons = document.querySelectorAll('.tool-btn');
const optimizationModal = new bootstrap.Modal(document.getElementById('optimizationModal'));
const optimizationMessage = document.getElementById('optimizationMessage');
const optimizationProgress = document.querySelector('.progress-bar');
const optimizationHistory = document.getElementById('optimizationHistory');

// Charts
let performanceChart;
let resourceChart;

// Refresh timer
let refreshTimer;

// Initialize the dashboard
document.addEventListener('DOMContentLoaded', function() {
    // Initialize charts
    initCharts();
    
    // Set up event listeners
    setupEventListeners();
    
    // Load initial data
    loadDashboardData();
});

/**
 * Initialize charts
 */
function initCharts() {
    // Performance Chart
    const performanceCtx = document.getElementById('performanceChart').getContext('2d');
    performanceChart = new Chart(performanceCtx, {
        type: 'line',
        data: {
            labels: generateTimeLabels(24),
            datasets: [{
                label: 'Generation Time (ms)',
                data: generateRandomData(24, 50, 200),
                borderColor: '#0078d4',
                backgroundColor: 'rgba(0, 120, 212, 0.1)',
                borderWidth: 2,
                tension: 0.4,
                fill: true
            }, {
                label: 'API Response Time (ms)',
                data: generateRandomData(24, 20, 100),
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                borderWidth: 2,
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Time (ms)'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Time'
                    }
                }
            }
        }
    });
    
    // Resource Chart
    const resourceCtx = document.getElementById('resourceChart').getContext('2d');
    resourceChart = new Chart(resourceCtx, {
        type: 'doughnut',
        data: {
            labels: ['QR Generation', 'Image Processing', 'Database', 'API', 'Other'],
            datasets: [{
                data: [40, 20, 15, 15, 10],
                backgroundColor: [
                    '#0078d4',
                    '#28a745',
                    '#ffc107',
                    '#17a2b8',
                    '#6c757d'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            },
            cutout: '70%'
        }
    });
}

/**
 * Set up event listeners
 */
function setupEventListeners() {
    // Auto-refresh interval change
    refreshIntervalSelect.addEventListener('change', function() {
        const interval = parseInt(this.value);
        
        // Clear existing timer
        if (refreshTimer) {
            clearInterval(refreshTimer);
            refreshTimer = null;
        }
        
        // Set new timer if interval > 0
        if (interval > 0) {
            refreshTimer = setInterval(loadDashboardData, interval * 1000);
        }
    });
    
    // Run optimization button
    runOptimizationBtn.addEventListener('click', function() {
        runFullOptimization();
    });
    
    // Individual tool buttons
    toolButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tool = this.dataset.tool;
            runToolOptimization(tool);
        });
    });
    
    // Period buttons for performance chart
    document.querySelectorAll('[data-period]').forEach(button => {
        button.addEventListener('click', function() {
            const period = this.dataset.period;
            updatePerformanceChart(period);
            
            // Update active state
            document.querySelectorAll('[data-period]').forEach(btn => {
                btn.classList.remove('active');
            });
            this.classList.add('active');
        });
    });
}

/**
 * Load dashboard data
 */
function loadDashboardData() {
    // In a real application, this would fetch data from the server
    // For demo purposes, we'll just update with random data
    
    // Update performance chart with new data
    updatePerformanceChart('day');
    
    // Update resource allocation chart
    updateResourceChart();
    
    // Update system health metrics
    updateSystemHealth();
}

/**
 * Update performance chart based on selected period
 * @param {string} period - day, week, or month
 */
function updatePerformanceChart(period) {
    let labels;
    let dataPoints;
    let apiDataPoints;
    
    switch(period) {
        case 'week':
            labels = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
            dataPoints = generateRandomData(7, 60, 180);
            apiDataPoints = generateRandomData(7, 30, 90);
            break;
        case 'month':
            labels = Array.from({length: 30}, (_, i) => i + 1);
            dataPoints = generateRandomData(30, 70, 160);
            apiDataPoints = generateRandomData(30, 35, 80);
            break;
        default: // day
            labels = generateTimeLabels(24);
            dataPoints = generateRandomData(24, 50, 200);
            apiDataPoints = generateRandomData(24, 20, 100);
    }
    
    performanceChart.data.labels = labels;
    performanceChart.data.datasets[0].data = dataPoints;
    performanceChart.data.datasets[1].data = apiDataPoints;
    performanceChart.update();
}

/**
 * Update resource allocation chart
 */
function updateResourceChart() {
    // Generate random data for resource allocation
    const data = [
        Math.floor(Math.random() * 20) + 30, // QR Generation (30-50%)
        Math.floor(Math.random() * 10) + 15, // Image Processing (15-25%)
        Math.floor(Math.random() * 10) + 10, // Database (10-20%)
        Math.floor(Math.random() * 10) + 10, // API (10-20%)
        Math.floor(Math.random() * 10) + 5   // Other (5-15%)
    ];
    
    resourceChart.data.datasets[0].data = data;
    resourceChart.update();
}

/**
 * Update system health metrics
 */
function updateSystemHealth() {
    // In a real application, these would be fetched from the server
    // For demo purposes, we'll just update with random data
    
    // Memory usage (30-60%)
    const memoryUsage = Math.floor(Math.random() * 30) + 30;
    document.querySelector('.metric-card:nth-child(2) .metric-value').textContent = `${memoryUsage}%`;
    document.querySelector('.metric-card:nth-child(2) .progress-bar').style.width = `${memoryUsage}%`;
    
    // CPU load (20-50%)
    const cpuLoad = Math.floor(Math.random() * 30) + 20;
    document.querySelector('.metric-card:nth-child(3) .metric-value').textContent = `${cpuLoad}%`;
    document.querySelector('.metric-card:nth-child(3) .progress-bar').style.width = `${cpuLoad}%`;
}

/**
 * Run full system optimization
 */
function runFullOptimization() {
    // Show optimization modal
    optimizationModal.show();
    optimizationMessage.textContent = 'Running full system optimization...';
    optimizationProgress.style.width = '0%';
    optimizationProgress.setAttribute('aria-valuenow', 0);
    
    // Simulate optimization process
    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.floor(Math.random() * 5) + 1;
        if (progress >= 100) {
            progress = 100;
            clearInterval(interval);
            
            // Complete optimization
            optimizationMessage.textContent = 'Optimization completed successfully!';
            
            // Add to optimization history
            addOptimizationToHistory('Full System Optimization', '+35%');
            
            // Reload dashboard data
            setTimeout(() => {
                optimizationModal.hide();
                loadDashboardData();
            }, 1500);
        }
        
        // Update progress bar
        optimizationProgress.style.width = `${progress}%`;
        optimizationProgress.setAttribute('aria-valuenow', progress);
    }, 200);
}

/**
 * Run specific tool optimization
 * @param {string} tool - Tool identifier
 */
function runToolOptimization(tool) {
    // Show optimization modal
    optimizationModal.show();
    
    // Set tool-specific message
    let toolName;
    switch(tool) {
        case 'database':
            toolName = 'Database Optimization';
            break;
        case 'cache':
            toolName = 'Cache Management';
            break;
        case 'queue':
            toolName = 'Task Queue Optimization';
            break;
        case 'image':
            toolName = 'Image Processing Optimization';
            break;
        case 'storage':
            toolName = 'Storage Cleanup';
            break;
        case 'test':
            toolName = 'Performance Test';
            break;
        default:
            toolName = 'Tool';
    }
    
    optimizationMessage.textContent = `Running ${toolName.toLowerCase()}...`;
    optimizationProgress.style.width = '0%';
    optimizationProgress.setAttribute('aria-valuenow', 0);
    
    // Simulate optimization process
    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.floor(Math.random() * 8) + 3;
        if (progress >= 100) {
            progress = 100;
            clearInterval(interval);
            
            // Complete optimization
            optimizationMessage.textContent = `${toolName} completed successfully!`;
            
            // Add to optimization history
            const improvement = `+${Math.floor(Math.random() * 20) + 10}%`;
            addOptimizationToHistory(toolName, improvement);
            
            // Reload dashboard data
            setTimeout(() => {
                optimizationModal.hide();
                loadDashboardData();
            }, 1500);
        }
        
        // Update progress bar
        optimizationProgress.style.width = `${progress}%`;
        optimizationProgress.setAttribute('aria-valuenow', progress);
    }, 150);
}

/**
 * Add optimization to history
 * @param {string} name - Optimization name
 * @param {string} improvement - Improvement percentage
 */
function addOptimizationToHistory(name, improvement) {
    const today = new Date();
    const dateStr = today.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
    
    const row = document.createElement('tr');
    row.innerHTML = `
        <td>${name}</td>
        <td>${dateStr}</td>
        <td><span class="badge bg-success">Completed</span></td>
        <td>${improvement}</td>
    `;
    
    // Add to the top of the history
    if (optimizationHistory.firstChild) {
        optimizationHistory.insertBefore(row, optimizationHistory.firstChild);
    } else {
        optimizationHistory.appendChild(row);
    }
}

/**
 * Generate time labels for 24-hour period
 * @param {number} count - Number of labels to generate
 * @returns {Array} Array of time labels
 */
function generateTimeLabels(count) {
    const labels = [];
    for (let i = 0; i < count; i++) {
        const hour = i % 12 || 12;
        const ampm = i < 12 ? 'AM' : 'PM';
        labels.push(`${hour}${ampm}`);
    }
    return labels;
}

/**
 * Generate random data points
 * @param {number} count - Number of data points
 * @param {number} min - Minimum value
 * @param {number} max - Maximum value
 * @returns {Array} Array of random data points
 */
function generateRandomData(count, min, max) {
    return Array.from({length: count}, () => Math.floor(Math.random() * (max - min + 1)) + min);
}
