/* Enhanced <PERSON>er St<PERSON> - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Glossy Corporate Design */

/* Enhanced CTA Card with Glossy Effects */
.footer-cta-section .cta-card {
    position: relative;
    background: linear-gradient(145deg, 
        rgba(255, 255, 255, 0.15) 0%, 
        rgba(255, 255, 255, 0.05) 50%, 
        rgba(0, 0, 0, 0.05) 100%);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    overflow: hidden;
}

.footer-cta-section .cta-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent, 
        rg<PERSON>(255, 255, 255, 0.1), 
        transparent);
    transition: left 0.6s ease;
}

.footer-cta-section .cta-card:hover::before {
    left: 100%;
}

.footer-cta-section .cta-card:hover {
    transform: translateY(-5px);
    box-shadow: 
        0 30px 60px rgba(0, 0, 0, 0.15),
        inset 0 2px 0 rgba(255, 255, 255, 0.4),
        inset 0 -2px 0 rgba(0, 0, 0, 0.15);
}

/* Enhanced CTA Icon with Pulse Effect */
.footer-cta-section .cta-icon {
    position: relative;
    width: 60px;
    height: 60px;
    background: linear-gradient(145deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    box-shadow: 
        0 10px 20px rgba(102, 126, 234, 0.3),
        inset 0 2px 0 rgba(255, 255, 255, 0.3),
        inset 0 -2px 0 rgba(0, 0, 0, 0.2);
    animation: iconPulse 3s ease-in-out infinite;
}

@keyframes iconPulse {
    0%, 100% {
        box-shadow: 
            0 10px 20px rgba(102, 126, 234, 0.3),
            inset 0 2px 0 rgba(255, 255, 255, 0.3),
            inset 0 -2px 0 rgba(0, 0, 0, 0.2),
            0 0 0 0 rgba(102, 126, 234, 0.4);
    }
    50% {
        box-shadow: 
            0 15px 30px rgba(102, 126, 234, 0.4),
            inset 0 3px 0 rgba(255, 255, 255, 0.4),
            inset 0 -3px 0 rgba(0, 0, 0, 0.3),
            0 0 0 10px rgba(102, 126, 234, 0.2);
    }
}

/* Enhanced CTA Buttons with Glass Morphism */
.footer-cta-section .btn-cta {
    position: relative;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.footer-cta-section .btn-cta-primary {
    background: linear-gradient(145deg, 
        rgba(255, 255, 255, 0.9) 0%, 
        rgba(255, 255, 255, 0.7) 100%);
    color: #333;
    box-shadow: 
        0 8px 20px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.footer-cta-section .btn-cta-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 255, 255, 0.3), 
        transparent);
    transition: left 0.5s ease;
}

.footer-cta-section .btn-cta-primary:hover::before {
    left: 100%;
}

.footer-cta-section .btn-cta-primary:hover {
    transform: translateY(-2px);
    box-shadow: 
        0 12px 30px rgba(0, 0, 0, 0.15),
        inset 0 2px 0 rgba(255, 255, 255, 0.9);
    color: #333;
}

.footer-cta-section .btn-cta-secondary {
    background: linear-gradient(145deg, 
        rgba(102, 126, 234, 0.8) 0%, 
        rgba(118, 75, 162, 0.8) 100%);
    color: white;
    box-shadow: 
        0 8px 20px rgba(102, 126, 234, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.footer-cta-section .btn-cta-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 
        0 12px 30px rgba(102, 126, 234, 0.3),
        inset 0 2px 0 rgba(255, 255, 255, 0.4);
    color: white;
}

/* Enhanced Navigation Sections with Glass Effect */
.footer-nav-section {
    position: relative;
    padding: 1.5rem;
    background: linear-gradient(145deg, 
        rgba(255, 255, 255, 0.08) 0%, 
        rgba(255, 255, 255, 0.02) 100%);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    margin-bottom: 1rem;
}

.footer-nav-section:hover {
    background: linear-gradient(145deg, 
        rgba(255, 255, 255, 0.12) 0%, 
        rgba(255, 255, 255, 0.04) 100%);
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.footer-nav-section .nav-section-title {
    position: relative;
    color: #ffffff;
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.footer-nav-section .nav-section-title i {
    color: #FFD700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

/* Enhanced Social Icons with 3D Effect */
.social-icon-premium {
    position: relative;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(145deg, 
        rgba(255, 255, 255, 0.15) 0%, 
        rgba(255, 255, 255, 0.05) 100%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(15px);
    transition: all 0.3s ease;
    text-decoration: none;
    color: white;
    box-shadow: 
        0 8px 20px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.social-icon-premium:hover {
    transform: translateY(-3px) scale(1.05);
    background: linear-gradient(145deg, 
        rgba(255, 255, 255, 0.25) 0%, 
        rgba(255, 255, 255, 0.1) 100%);
    box-shadow: 
        0 15px 35px rgba(0, 0, 0, 0.15),
        inset 0 2px 0 rgba(255, 255, 255, 0.4);
    color: white;
}

/* Enhanced Trust Badges with Glow Effect */
.trust-badge {
    position: relative;
    background: linear-gradient(145deg, 
        rgba(255, 255, 255, 0.1) 0%, 
        rgba(255, 255, 255, 0.05) 100%);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 20px;
    padding: 0.75rem 1rem;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.trust-badge:hover {
    background: linear-gradient(145deg, 
        rgba(255, 255, 255, 0.15) 0%, 
        rgba(255, 255, 255, 0.08) 100%);
    border-color: rgba(255, 255, 255, 0.25);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.trust-badge i {
    color: #FFD700;
    text-shadow: 0 0 8px rgba(255, 215, 0, 0.6);
}

/* Enhanced Newsletter Card */
.newsletter-card {
    position: relative;
    background: linear-gradient(145deg, 
        rgba(255, 255, 255, 0.12) 0%, 
        rgba(255, 255, 255, 0.04) 100%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 1.5rem;
    backdrop-filter: blur(15px);
    box-shadow: 
        0 15px 35px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.newsletter-input-premium {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    padding: 12px 20px;
    color: white;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.newsletter-input-premium:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
    outline: none;
}

.newsletter-btn-premium {
    background: linear-gradient(145deg, #667eea, #764ba2);
    border: none;
    border-radius: 25px;
    padding: 12px 20px;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 
        0 8px 20px rgba(102, 126, 234, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.newsletter-btn-premium:hover {
    transform: translateY(-2px);
    box-shadow: 
        0 12px 30px rgba(102, 126, 234, 0.4),
        inset 0 2px 0 rgba(255, 255, 255, 0.4);
}
