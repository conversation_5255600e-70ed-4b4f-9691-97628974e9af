/* Enhanced <PERSON>er St<PERSON> - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Glossy Corporate Design */

/* Enhanced CTA Card with Glossy Effects */
.footer-cta-section .cta-card {
    position: relative;
    background: linear-gradient(145deg,
        rgba(255, 255, 255, 0.15) 0%,
        rgba(255, 255, 255, 0.05) 50%,
        rgba(0, 0, 0, 0.05) 100%);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 2rem;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    overflow: hidden;
}

.footer-cta-section .cta-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rg<PERSON>(255, 255, 255, 0.1),
        transparent);
    transition: left 0.6s ease;
}

.footer-cta-section .cta-card:hover::before {
    left: 100%;
}

.footer-cta-section .cta-card:hover {
    transform: translateY(-5px);
    box-shadow:
        0 30px 60px rgba(0, 0, 0, 0.15),
        inset 0 2px 0 rgba(255, 255, 255, 0.4),
        inset 0 -2px 0 rgba(0, 0, 0, 0.15);
}

/* Enhanced CTA Icon with Pulse Effect */
.footer-cta-section .cta-icon {
    position: relative;
    width: 60px;
    height: 60px;
    background: linear-gradient(145deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    box-shadow:
        0 10px 20px rgba(102, 126, 234, 0.3),
        inset 0 2px 0 rgba(255, 255, 255, 0.3),
        inset 0 -2px 0 rgba(0, 0, 0, 0.2);
    animation: iconPulse 3s ease-in-out infinite;
}

@keyframes iconPulse {
    0%, 100% {
        box-shadow:
            0 10px 20px rgba(102, 126, 234, 0.3),
            inset 0 2px 0 rgba(255, 255, 255, 0.3),
            inset 0 -2px 0 rgba(0, 0, 0, 0.2),
            0 0 0 0 rgba(102, 126, 234, 0.4);
    }
    50% {
        box-shadow:
            0 15px 30px rgba(102, 126, 234, 0.4),
            inset 0 3px 0 rgba(255, 255, 255, 0.4),
            inset 0 -3px 0 rgba(0, 0, 0, 0.3),
            0 0 0 10px rgba(102, 126, 234, 0.2);
    }
}

/* Enhanced CTA Buttons with Glass Morphism */
.footer-cta-section .btn-cta {
    position: relative;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.footer-cta-section .btn-cta-primary {
    background: linear-gradient(145deg,
        rgba(255, 255, 255, 0.9) 0%,
        rgba(255, 255, 255, 0.7) 100%);
    color: #333;
    box-shadow:
        0 8px 20px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.footer-cta-section .btn-cta-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent);
    transition: left 0.5s ease;
}

.footer-cta-section .btn-cta-primary:hover::before {
    left: 100%;
}

.footer-cta-section .btn-cta-primary:hover {
    transform: translateY(-2px);
    box-shadow:
        0 12px 30px rgba(0, 0, 0, 0.15),
        inset 0 2px 0 rgba(255, 255, 255, 0.9);
    color: #333;
}

.footer-cta-section .btn-cta-secondary {
    background: linear-gradient(145deg,
        rgba(102, 126, 234, 0.8) 0%,
        rgba(118, 75, 162, 0.8) 100%);
    color: white;
    box-shadow:
        0 8px 20px rgba(102, 126, 234, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.footer-cta-section .btn-cta-secondary:hover {
    transform: translateY(-2px);
    box-shadow:
        0 12px 30px rgba(102, 126, 234, 0.3),
        inset 0 2px 0 rgba(255, 255, 255, 0.4);
    color: white;
}

/* Enhanced Navigation Sections with Glass Effect */
.footer-nav-section {
    position: relative;
    padding: 1.5rem;
    background: linear-gradient(145deg,
        rgba(255, 255, 255, 0.08) 0%,
        rgba(255, 255, 255, 0.02) 100%);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    margin-bottom: 1rem;
}

.footer-nav-section:hover {
    background: linear-gradient(145deg,
        rgba(255, 255, 255, 0.12) 0%,
        rgba(255, 255, 255, 0.04) 100%);
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.footer-nav-section .nav-section-title {
    position: relative;
    color: #ffffff;
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.footer-nav-section .nav-section-title i {
    color: #FFD700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

/* Enhanced Social Icons with 3D Effect */
.social-icon-premium {
    position: relative;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(145deg,
        rgba(255, 255, 255, 0.15) 0%,
        rgba(255, 255, 255, 0.05) 100%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(15px);
    transition: all 0.3s ease;
    text-decoration: none;
    color: white;
    box-shadow:
        0 8px 20px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.social-icon-premium:hover {
    transform: translateY(-3px) scale(1.05);
    background: linear-gradient(145deg,
        rgba(255, 255, 255, 0.25) 0%,
        rgba(255, 255, 255, 0.1) 100%);
    box-shadow:
        0 15px 35px rgba(0, 0, 0, 0.15),
        inset 0 2px 0 rgba(255, 255, 255, 0.4);
    color: white;
}

/* Enhanced Trust Badges with Glow Effect */
.trust-badge {
    position: relative;
    background: linear-gradient(145deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 100%);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 20px;
    padding: 0.75rem 1rem;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.trust-badge:hover {
    background: linear-gradient(145deg,
        rgba(255, 255, 255, 0.15) 0%,
        rgba(255, 255, 255, 0.08) 100%);
    border-color: rgba(255, 255, 255, 0.25);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.trust-badge i {
    color: #FFD700;
    text-shadow: 0 0 8px rgba(255, 215, 0, 0.6);
}

/* Enhanced Newsletter Card */
.newsletter-card {
    position: relative;
    background: linear-gradient(145deg,
        rgba(255, 255, 255, 0.12) 0%,
        rgba(255, 255, 255, 0.04) 100%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 1.5rem;
    backdrop-filter: blur(15px);
    box-shadow:
        0 15px 35px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.newsletter-input-premium {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    padding: 12px 20px;
    color: white;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.newsletter-input-premium:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
    outline: none;
}

.newsletter-btn-premium {
    background: linear-gradient(145deg, #667eea, #764ba2);
    border: none;
    border-radius: 25px;
    padding: 12px 20px;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow:
        0 8px 20px rgba(102, 126, 234, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.newsletter-btn-premium:hover {
    transform: translateY(-2px);
    box-shadow:
        0 12px 30px rgba(102, 126, 234, 0.4),
        inset 0 2px 0 rgba(255, 255, 255, 0.4);
}

/* Additional Footer Enhancements - Ultra Premium */
.footer-premium {
    position: relative;
    overflow: hidden;
}

.footer-premium::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.08) 0%, transparent 50%),
        linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    z-index: 0;
}

.footer-premium > * {
    position: relative;
    z-index: 1;
}

/* Enhanced Logo with 3D Effect */
.logo-container-premium {
    position: relative;
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(15px);
    transition: all 0.3s ease;
}

.logo-container-premium:hover {
    transform: translateY(-3px);
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.logo-container-premium i {
    font-size: 2.5rem;
    background: linear-gradient(145deg, #FFD700, #FFA500);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
    animation: logoGlow 3s ease-in-out infinite;
}

@keyframes logoGlow {
    0%, 100% {
        text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
    }
    50% {
        text-shadow: 0 0 30px rgba(255, 215, 0, 0.8);
    }
}

.logo-text-premium {
    font-size: 1.8rem;
    font-weight: 900;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Enhanced Navigation Links with Hover Effects */
.footer-nav-section .nav-link {
    position: relative;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    padding: 0.5rem 0;
    transition: all 0.3s ease;
    display: inline-block;
    overflow: hidden;
}

.footer-nav-section .nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #FFD700, #FFA500);
    transition: width 0.3s ease;
}

.footer-nav-section .nav-link:hover {
    color: #FFD700;
    transform: translateX(5px);
}

.footer-nav-section .nav-link:hover::before {
    width: 100%;
}

/* Enhanced Newsletter Section */
.newsletter-section {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
    border-radius: 20px;
    padding: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
}

.newsletter-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, rgba(255, 215, 0, 0.1), transparent);
    animation: newsletterRotate 8s linear infinite;
    z-index: 0;
}

@keyframes newsletterRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.newsletter-section > * {
    position: relative;
    z-index: 1;
}

.newsletter-title {
    font-size: 1.5rem;
    font-weight: 800;
    color: white;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.newsletter-description {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 1.5rem;
    font-size: 1rem;
}

.newsletter-form {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

/* Enhanced Footer Bottom with Floating Effect */
.footer-bottom-premium {
    background: linear-gradient(145deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.1) 100%);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    padding: 2rem 0;
    margin-top: 3rem;
    position: relative;
}

.footer-bottom-premium::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.5), transparent);
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-copyright {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.footer-links {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    position: relative;
}

.footer-links a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 1px;
    background: #FFD700;
    transition: width 0.3s ease;
}

.footer-links a:hover {
    color: #FFD700;
}

.footer-links a:hover::after {
    width: 100%;
}

/* Ultra Premium Animations */
@keyframes floatUp {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.footer-premium .cta-card,
.footer-premium .newsletter-section,
.footer-premium .nav-section {
    animation: floatUp 6s ease-in-out infinite;
}

.footer-premium .cta-card {
    animation-delay: 0s;
}

.footer-premium .newsletter-section {
    animation-delay: 2s;
}

.footer-premium .nav-section {
    animation-delay: 4s;
}
