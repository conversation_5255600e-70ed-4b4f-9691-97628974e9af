{% extends 'base.html' %}
{% load static %}

{% block title %}Admin - Update Ad Status: {{ ad.title }}{% endblock %}

{% block extra_css %}
<!-- Include common ads CSS -->
{% include 'ads/includes/ads_common_css.html' %}
<style>
    .admin-badge {
        background-color: #1a237e;
        color: white;
        padding: 3px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 600;
        margin-left: 10px;
    }

    .ad-title-preview {
        font-weight: 600;
        color: #1a237e;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid rgba(0,0,0,0.1);
    }

    .status-form {
        margin-top: 20px;
    }

    .status-form label {
        font-weight: 600;
        color: #1a237e;
        margin-bottom: 10px;
    }

    .status-option {
        display: flex;
        align-items: center;
        padding: 15px;
        border: 1px solid rgba(0,0,0,0.1);
        border-radius: 8px;
        margin-bottom: 10px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .status-option:hover {
        background-color: rgba(57, 73, 171, 0.03);
    }

    .status-option.active {
        border-color: #3949ab;
        background-color: rgba(57, 73, 171, 0.05);
        box-shadow: 0 5px 15px rgba(57, 73, 171, 0.1);
    }

    .status-option.disabled {
        opacity: 0.7;
        cursor: not-allowed;
        background-color: #f8f9fa;
        border-color: #dee2e6;
    }

    .status-option.disabled:hover {
        background-color: #f8f9fa;
    }

    .status-note {
        font-size: 13px;
        font-style: italic;
    }

    .status-option input[type="radio"] {
        margin-right: 15px;
    }

    .status-name {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .status-description {
        font-size: 14px;
        color: #666;
    }

    .status-draft .status-name {
        color: #607d8b;
    }

    .status-pending .status-name {
        color: #ff9800;
    }

    .status-approved .status-name {
        color: #8bc34a;
    }

    .status-active .status-name {
        color: #4caf50;
    }

    .status-paused .status-name {
        color: #9e9e9e;
    }

    .status-rejected .status-name {
        color: #f44336;
    }

    .status-expired .status-name {
        color: #795548;
    }

    .action-buttons {
        display: flex;
        gap: 10px;
        margin-top: 30px;
    }

    @media (max-width: 768px) {
        .action-buttons {
            flex-direction: column;
        }

        .action-buttons .ads-btn {
            width: 100%;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="ads-page-header">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <nav aria-label="breadcrumb" class="ads-breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'index' %}">Home</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'ads:dashboard' %}">Ads Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'ads:admin_view_ad' ad.slug %}">{{ ad.title }}</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Update Status</li>
                    </ol>
                </nav>

                <h1 class="display-6 text-center mb-1 ads-page-title">Update Ad Status</h1>
                <p class="lead text-center mb-2 ads-page-subtitle">Change the status of this advertisement</p>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="container mt-4">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="ads-card animate__animated animate__fadeIn">
                <div class="ads-card-header">
                    <h3 class="ads-card-title">Update Advertisement Status</h3>
                </div>

                <div class="card-body">
                    <div class="ad-title-preview">
                        <p>You are updating the status for:</p>
                        <h4>{{ ad.title }}</h4>
                        <p>Current status: <span class="badge bg-{{ ad.status }}">{{ ad.get_status_display }}</span></p>
                    </div>

                    <form method="post" class="status-form">
                        {% csrf_token %}
                        <div class="mb-4">
                            <label>Select New Status</label>

                            <div class="status-option status-draft {% if ad.status == 'draft' %}active{% endif %}">
                                <input type="radio" name="status" id="status_draft" value="draft" {% if ad.status == 'draft' %}checked{% endif %}>
                                <div>
                                    <div class="status-name">Draft</div>
                                    <div class="status-description">Ad is being created and not yet submitted for approval.</div>
                                </div>
                            </div>

                            <div class="status-option status-pending {% if ad.status == 'pending' %}active{% endif %}">
                                <input type="radio" name="status" id="status_pending" value="pending" {% if ad.status == 'pending' %}checked{% endif %}>
                                <div>
                                    <div class="status-name">Pending Approval</div>
                                    <div class="status-description">Ad has been submitted and is waiting for admin approval.</div>
                                </div>
                            </div>

                            <div class="status-option status-approved {% if ad.status == 'approved' %}active{% endif %} {% if ad.status == 'approved' %}disabled{% endif %}">
                                <input type="radio" name="status" id="status_approved" value="approved" {% if ad.status == 'approved' %}checked{% endif %} {% if ad.status == 'approved' %}disabled{% endif %}>
                                <div>
                                    <div class="status-name">Approved</div>
                                    <div class="status-description">Ad has been approved but payment is pending.</div>
                                    {% if ad.status == 'approved' %}
                                    <div class="status-note text-danger mt-2">
                                        <i class="fas fa-info-circle"></i> This ad is already approved and cannot be re-approved.
                                    </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="status-option status-active {% if ad.status == 'active' %}active{% endif %}">
                                <input type="radio" name="status" id="status_active" value="active" {% if ad.status == 'active' %}checked{% endif %}>
                                <div>
                                    <div class="status-name">Active</div>
                                    <div class="status-description">Ad is currently running and visible to users.</div>
                                </div>
                            </div>

                            <div class="status-option status-paused {% if ad.status == 'paused' %}active{% endif %}">
                                <input type="radio" name="status" id="status_paused" value="paused" {% if ad.status == 'paused' %}checked{% endif %}>
                                <div>
                                    <div class="status-name">Paused</div>
                                    <div class="status-description">Ad has been temporarily paused by the user or admin.</div>
                                </div>
                            </div>

                            <div class="status-option status-rejected {% if ad.status == 'rejected' %}active{% endif %}">
                                <input type="radio" name="status" id="status_rejected" value="rejected" {% if ad.status == 'rejected' %}checked{% endif %}>
                                <div>
                                    <div class="status-name">Rejected</div>
                                    <div class="status-description">Ad has been rejected by admin and needs revision.</div>
                                </div>
                            </div>

                            <div class="status-option status-expired {% if ad.status == 'expired' %}active{% endif %}">
                                <input type="radio" name="status" id="status_expired" value="expired" {% if ad.status == 'expired' %}checked{% endif %}>
                                <div>
                                    <div class="status-name">Expired</div>
                                    <div class="status-description">Ad has reached its end date and is no longer active.</div>
                                </div>
                            </div>
                        </div>

                        <div class="action-buttons">
                            <button type="submit" class="ads-btn ads-btn-primary">
                                <i class="fas fa-check-circle"></i> Update Status
                            </button>
                            <a href="{% url 'ads:admin_view_ad' ad.slug %}" class="ads-btn ads-btn-outline">
                                <i class="fas fa-arrow-left"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Make the entire status option clickable
        const statusOptions = document.querySelectorAll('.status-option');

        statusOptions.forEach(option => {
            option.addEventListener('click', function() {
                // Skip if this option is disabled
                if (this.classList.contains('disabled')) {
                    return;
                }

                // Find the radio input within this option and check it
                const radio = this.querySelector('input[type="radio"]');
                radio.checked = true;

                // Remove active class from all options
                statusOptions.forEach(opt => {
                    if (!opt.classList.contains('disabled')) {
                        opt.classList.remove('active');
                    }
                });

                // Add active class to clicked option
                this.classList.add('active');
            });
        });
    });
</script>
{% endblock %}
