/**
 * Notification Bell Hover
 * This script ensures the notification bell in the main navbar shows a modal on hover
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Notification bell hover script loaded');

    // Function to initialize the notification bell hover functionality
    function initNotificationBellHover() {
        // Find the notification bell in the main navbar
        const navbarBell = document.querySelector('.new-navbar-notification');

        if (!navbarBell) {
            console.log('Notification bell not found in navbar');
            return;
        }

        console.log('Initializing notification bell hover functionality');

        // Find the notification dropdown
        const dropdown = navbarBell.querySelector('.new-navbar-notification-dropdown');
        if (!dropdown) {
            console.log('Notification dropdown not found');
            return;
        }

        // Immediately show sample notifications in the dropdown
        const dropdownContent = navbarBell.querySelector('.new-navbar-notification-body');
        if (dropdownContent) {
            // Clear the loading spinner
            dropdownContent.innerHTML = '';

            // Show sample notifications right away
            showSampleNotifications(dropdownContent);
        }

        // Variable to store timeout ID
        let hideTimeout;

        // Add hover event listeners
        navbarBell.addEventListener('mouseenter', function() {
            console.log('Notification bell mouseenter');
            // Clear any existing timeout
            if (hideTimeout) {
                clearTimeout(hideTimeout);
                hideTimeout = null;
            }
            dropdown.classList.add('show');
        });

        navbarBell.addEventListener('mouseleave', function() {
            console.log('Notification bell mouseleave');
            // Set a timeout to hide the dropdown after a delay
            hideTimeout = setTimeout(function() {
                // Only hide if not hovering over the dropdown itself
                if (!dropdown.matches(':hover')) {
                    dropdown.classList.remove('show');
                }
            }, 300); // 300ms delay
        });

        dropdown.addEventListener('mouseenter', function() {
            console.log('Dropdown mouseenter');
            // Clear any existing timeout
            if (hideTimeout) {
                clearTimeout(hideTimeout);
                hideTimeout = null;
            }
            dropdown.classList.add('show');
        });

        dropdown.addEventListener('mouseleave', function() {
            console.log('Dropdown mouseleave');
            // Set a timeout to hide the dropdown after a delay
            hideTimeout = setTimeout(function() {
                if (!navbarBell.matches(':hover')) {
                    dropdown.classList.remove('show');
                }
            }, 300); // 300ms delay
        });

        // Add click event to make dropdown stay visible on click
        dropdown.addEventListener('click', function(e) {
            // If clicking on a button or link inside the dropdown, don't prevent default
            if (e.target.tagName === 'BUTTON' || e.target.tagName === 'A' ||
                e.target.closest('button') || e.target.closest('a')) {
                return;
            }

            // For other elements, prevent the default action
            e.preventDefault();
            e.stopPropagation();

            // Keep dropdown visible
            dropdown.classList.add('show');
        });

        // Add click event to notification button to toggle dropdown
        const notificationBtn = navbarBell.querySelector('#notificationBtn');
        if (notificationBtn) {
            notificationBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Toggle dropdown visibility
                if (dropdown.classList.contains('show')) {
                    dropdown.classList.remove('show');
                } else {
                    dropdown.classList.add('show');

                    // Make sure dropdown stays visible for a while
                    if (hideTimeout) {
                        clearTimeout(hideTimeout);
                        hideTimeout = null;
                    }
                }
            });
        }
    }

    // Function to show empty notification state
    function showSampleNotifications(dropdownContent) {
        if (!dropdownContent) return;

        console.log('Showing empty notification state');

        // Check if there are any unread notifications
        const badge = document.querySelector('.new-navbar-notification-badge');
        const hasNotifications = badge && !badge.classList.contains('d-none') && badge.textContent !== '0';

        if (hasNotifications) {
            // Show a message that there are notifications
            dropdownContent.innerHTML = `
                <div class="new-navbar-notification-empty">
                    <i class="fas fa-bell"></i>
                    <p>You have unread notifications</p>
                </div>
            `;
        } else {
            // Show empty state with crossed bell icon
            dropdownContent.innerHTML = `
                <div class="new-navbar-notification-empty">
                    <i class="fas fa-bell-slash"></i>
                    <p>No new notifications</p>
                    <p class="new-navbar-notification-empty-message">You're all caught up!</p>
                </div>
            `;
        }
    }

    // Add document click event to close dropdown when clicking outside
    document.addEventListener('click', function(e) {
        const dropdowns = document.querySelectorAll('.new-navbar-notification-dropdown');
        dropdowns.forEach(dropdown => {
            const navbarBell = dropdown.closest('.new-navbar-notification');
            if (navbarBell && !navbarBell.contains(e.target) && !dropdown.contains(e.target)) {
                dropdown.classList.remove('show');
            }
        });
    });

    // Run immediately to ensure it works
    initNotificationBellHover();

    // Also run after a short delay as a backup
    setTimeout(initNotificationBellHover, 500);

    // Run one more time after a longer delay to ensure it works even if the page loads slowly
    setTimeout(initNotificationBellHover, 2000);
});
