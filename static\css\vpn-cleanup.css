/**
 * VPN Cleanup CSS
 * This file removes all VPN-related styles from the application
 */

/* Hide all VPN-related elements */
.vpn-link,
.vpn-status,
.vpn-icon,
.vpn-info,
.vpn-features-container,
.vpn-features,
.vpn-feature,
.feature-icon-wrapper,
.feature-content,
.vpn-modal-footer,
.vpn-activate-btn,
.vpn-note,
.mobile-vpn,
.vpn-features-title {
    display: none !important;
}

/* Hide VPN modal */
#vpn-modal {
    display: none !important;
}

/* Remove VPN-related animations */
@keyframes shieldPulse {
    0%, 100% {
        transform: none;
    }
    50% {
        transform: none;
    }
}

@keyframes statusAppear {
    from {
        opacity: 0;
        transform: none;
    }
    to {
        opacity: 0;
        transform: none;
    }
}

@keyframes featureAppear {
    from {
        opacity: 0;
        transform: none;
    }
    to {
        opacity: 0;
        transform: none;
    }
}

/* Override any VPN-specific styles */
.modal-header.bg-gradient-primary {
    background: none;
}

.modal-title i {
    animation: none;
}
