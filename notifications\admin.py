from django.contrib import admin
from .models import Notification


@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    list_display = ('title', 'user', 'notification_type', 'category', 'is_read', 'created_at')
    list_filter = ('notification_type', 'category', 'is_read', 'is_deleted', 'created_at')
    search_fields = ('title', 'message', 'user__username', 'user__email')
    readonly_fields = ('created_at', 'updated_at')
    date_hierarchy = 'created_at'
    actions = ['mark_as_read', 'mark_as_unread', 'soft_delete']

    fieldsets = (
        (None, {
            'fields': ('user', 'title', 'message')
        }),
        ('Classification', {
            'fields': ('notification_type', 'category')
        }),
        ('Related Content', {
            'fields': ('content_type', 'object_id', 'action_url')
        }),
        ('Status', {
            'fields': ('is_read', 'is_deleted')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def mark_as_read(self, request, queryset):
        queryset.update(is_read=True)
        self.message_user(request, f"{queryset.count()} notifications marked as read.")
    mark_as_read.short_description = "Mark selected notifications as read"

    def mark_as_unread(self, request, queryset):
        queryset.update(is_read=False)
        self.message_user(request, f"{queryset.count()} notifications marked as unread.")
    mark_as_unread.short_description = "Mark selected notifications as unread"

    def soft_delete(self, request, queryset):
        queryset.update(is_deleted=True)
        self.message_user(request, f"{queryset.count()} notifications soft deleted.")
    soft_delete.short_description = "Soft delete selected notifications"
