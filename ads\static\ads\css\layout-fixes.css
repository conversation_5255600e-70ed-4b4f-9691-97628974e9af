/* Layout fixes for ad display system */

/* Ensure main content area maintains proper spacing */
.corporate-section {
    min-height: auto !important;
    padding: 0 !important;
}

/* Fix container spacing issues */
.container {
    max-width: 100%;
    padding-left: 15px;
    padding-right: 15px;
}

/* Ensure ads don't break out of their containers */
.ad-container,
.premium-header-ad-container,
.header-ads-container,
.sidebar-ads-container,
.footer-ads-container,
.homepage-featured-ad-container {
    max-width: 100%;
    overflow: hidden;
    box-sizing: border-box;
}

/* Fix sidebar layout on mobile */
@media (max-width: 767.98px) {
    .col-md-3 {
        display: none !important;
    }
    
    .col-md-9 {
        flex: 0 0 100% !important;
        max-width: 100% !important;
    }
}

/* Ensure proper spacing between ad sections */
.premium-header-ad-container + .header-ads-container {
    margin-top: 10px;
}

.header-ads-container + .container {
    margin-top: 20px;
}

/* Fix any overflow issues with ad images */
.ad-container img,
.premium-header-ad-container img,
.header-ads-container img,
.sidebar-ads-container img,
.footer-ads-container img,
.homepage-featured-ad-container img {
    max-width: 100%;
    height: auto;
    object-fit: cover;
}

/* Ensure proper row spacing */
.row {
    margin-left: -15px;
    margin-right: -15px;
}

.row > [class*="col-"] {
    padding-left: 15px;
    padding-right: 15px;
}

/* Fix any z-index issues */
.premium-header-ad-container,
.header-ads-container {
    position: relative;
    z-index: 1;
}

.sidebar-ads-container {
    position: relative;
    z-index: 2;
}

/* Ensure footer ads don't interfere with main footer */
.footer-ads-container + .enterprise-footer {
    margin-top: 0;
}

/* Fix any text overflow in ads */
.ad-title,
.ad-description,
.premium-header-ad-title,
.header-ad-title,
.sidebar-ad-title,
.footer-ad-title {
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
}

/* Ensure proper button spacing */
.ad-cta,
.premium-header-ad-cta,
.header-ad-cta,
.sidebar-ad-cta,
.footer-ad-cta {
    display: inline-block;
    margin: 5px 0;
}

/* Fix carousel indicators positioning */
.premium-header-carousel-indicators,
.header-ad-indicators {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 3;
}

/* Ensure proper spacing for mobile */
@media (max-width: 575.98px) {
    .premium-header-ad-container,
    .header-ads-container,
    .homepage-featured-ad-container {
        margin: 15px 0;
        border-radius: 8px;
    }
    
    .footer-ads-container {
        padding: 20px 0;
        margin: 20px 0 15px 0;
    }
}

/* Fix any layout shifts caused by ad loading */
.ad-container,
.premium-header-ad-container,
.header-ads-container {
    min-height: 50px;
    transition: height 0.3s ease;
}

/* Ensure proper alignment */
.text-center {
    text-align: center !important;
}

.text-left {
    text-align: left !important;
}

.text-right {
    text-align: right !important;
}

/* Fix any margin collapse issues */
.premium-header-ad-container,
.header-ads-container,
.sidebar-ads-container,
.footer-ads-container,
.homepage-featured-ad-container {
    margin-top: 0;
    margin-bottom: 0;
}

.premium-header-ad-container {
    margin-bottom: 20px;
}

.header-ads-container {
    margin-bottom: 20px;
}

.sidebar-ads-container {
    margin-bottom: 20px;
}

/* Ensure proper grid behavior */
.footer-ads-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

@media (max-width: 767.98px) {
    .footer-ads-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
}

/* Fix any Bootstrap conflicts */
.container-fluid {
    padding-left: 15px;
    padding-right: 15px;
}

/* Ensure ads don't interfere with navigation */
.navbar,
.nav,
.navigation {
    z-index: 1030 !important;
}

/* Fix any positioning issues with fixed elements */
.fixed-top,
.fixed-bottom,
.sticky-top {
    z-index: 1030 !important;
}
