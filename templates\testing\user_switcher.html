{% extends 'base.html' %}
{% load static %}

{% block title %}User Switcher - Testing{% endblock %}

{% block extra_css %}
<style>
.user-switcher-container {
    padding: 3rem 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.switcher-card {
    background: white;
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    max-width: 600px;
    margin: 0 auto;
}

.switcher-header {
    text-align: center;
    margin-bottom: 3rem;
}

.switcher-header h1 {
    color: #333;
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
}

.switcher-header p {
    color: #6c757d;
    font-size: 1.1rem;
}

.user-option {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 1.5rem;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    cursor: pointer;
}

.user-option:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.2);
}

.user-info {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.user-avatar {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1.5rem;
    color: white;
    font-size: 1.5rem;
}

.user-details h3 {
    color: #333;
    font-size: 1.3rem;
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.user-role {
    color: #667eea;
    font-weight: 600;
    font-size: 0.9rem;
}

.user-description {
    color: #6c757d;
    font-size: 0.95rem;
    line-height: 1.5;
    margin-bottom: 1rem;
}

.user-features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.feature-badge {
    background: #e3f2fd;
    color: #1976d2;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.feature-badge.premium {
    background: #fff3e0;
    color: #f57c00;
}

.feature-badge.admin {
    background: #f3e5f5;
    color: #7b1fa2;
}

.login-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    width: 100%;
    text-align: center;
    margin-top: 1rem;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
    color: white;
}

.current-user {
    background: #e8f5e8;
    border-color: #28a745;
}

.current-user .login-btn {
    background: #28a745;
}

.logout-section {
    text-align: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #eee;
}

.logout-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
}

.logout-btn:hover {
    background: #c82333;
    color: white;
}
</style>
{% endblock %}

{% block content %}
<div class="user-switcher-container">
    <div class="container">
        <div class="switcher-card">
            <div class="switcher-header">
                <h1>🧪 User Testing Switcher</h1>
                <p>Switch between different user accounts to test plan limits and features</p>
                {% if user.is_authenticated %}
                <div class="alert alert-info">
                    Currently logged in as: <strong>{{ user.username }}</strong> ({{ user.profile.role|default:"User" }})
                </div>
                {% endif %}
            </div>

            <!-- Apollo - Free User -->
            <div class="user-option {% if user.username == 'apollo' %}current-user{% endif %}">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-details">
                        <h3>Apollo</h3>
                        <div class="user-role">Regular User</div>
                    </div>
                </div>
                <div class="user-description">
                    Test free plan limitations and feature restrictions. Perfect for testing plan upgrade flows and feature gating.
                </div>
                <div class="user-features">
                    <span class="feature-badge">5 QR Codes</span>
                    <span class="feature-badge">100 Scans/month</span>
                    <span class="feature-badge">Basic Analytics</span>
                    <span class="feature-badge">No Premium Features</span>
                </div>
                {% if user.username != 'apollo' %}
                <form method="post" action="{% url 'test_login_as' %}">
                    {% csrf_token %}
                    <input type="hidden" name="username" value="apollo">
                    <button type="submit" class="login-btn">
                        <i class="fas fa-sign-in-alt me-2"></i>Login as Apollo
                    </button>
                </form>
                {% else %}
                <div class="login-btn" style="background: #28a745;">
                    <i class="fas fa-check me-2"></i>Currently Active
                </div>
                {% endif %}
            </div>

            <!-- Peter - Admin User -->
            <div class="user-option {% if user.username == 'peter' %}current-user{% endif %}">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-crown"></i>
                    </div>
                    <div class="user-details">
                        <h3>Peter</h3>
                        <div class="user-role">Admin/Superuser</div>
                    </div>
                </div>
                <div class="user-description">
                    Test full admin access and unrestricted features. Perfect for testing admin functionality and premium features.
                </div>
                <div class="user-features">
                    <span class="feature-badge admin">Unlimited QR Codes</span>
                    <span class="feature-badge admin">Unlimited Scans</span>
                    <span class="feature-badge admin">All Premium Features</span>
                    <span class="feature-badge admin">Admin Panel Access</span>
                </div>
                {% if user.username != 'peter' %}
                <form method="post" action="{% url 'test_login_as' %}">
                    {% csrf_token %}
                    <input type="hidden" name="username" value="peter">
                    <button type="submit" class="login-btn">
                        <i class="fas fa-sign-in-alt me-2"></i>Login as Peter
                    </button>
                </form>
                {% else %}
                <div class="login-btn" style="background: #28a745;">
                    <i class="fas fa-check me-2"></i>Currently Active
                </div>
                {% endif %}
            </div>

            {% if user.is_authenticated %}
            <div class="logout-section">
                <h5>Need to logout completely?</h5>
                <p class="text-muted">Logout to test the login flow or switch to a different account manually.</p>
                <a href="{% url 'account_logout' %}" class="logout-btn">
                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                </a>
            </div>
            {% endif %}

            <div class="mt-4 text-center">
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    This switcher is for testing purposes only. Use it to verify plan limits and feature access.
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Add click handlers for user options
document.querySelectorAll('.user-option').forEach(option => {
    option.addEventListener('click', function() {
        const form = this.querySelector('form');
        if (form) {
            form.submit();
        }
    });
});
</script>
{% endblock %}
