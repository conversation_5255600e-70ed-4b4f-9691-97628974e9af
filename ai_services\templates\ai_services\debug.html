{% extends 'base.html' %}
{% load static %}

{% block title %}Hugging Face API Debug{% endblock %}

{% block extra_css %}
<style>
    .debug-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    .debug-card {
        margin-bottom: 20px;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .debug-header {
        background-color: #f8f9fa;
        padding: 15px 20px;
        border-bottom: 1px solid #dee2e6;
        border-radius: 8px 8px 0 0;
    }
    .debug-body {
        padding: 20px;
    }
    pre {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 4px;
        overflow-x: auto;
    }
    .api-status {
        display: inline-block;
        padding: 5px 10px;
        border-radius: 4px;
        font-weight: bold;
    }
    .api-status.success {
        background-color: #d4edda;
        color: #155724;
    }
    .api-status.error {
        background-color: #f8d7da;
        color: #721c24;
    }
    .api-status.warning {
        background-color: #fff3cd;
        color: #856404;
    }
    .headers-table {
        width: 100%;
        border-collapse: collapse;
    }
    .headers-table th, .headers-table td {
        padding: 8px;
        border: 1px solid #dee2e6;
        text-align: left;
    }
    .headers-table th {
        background-color: #f8f9fa;
    }
</style>
{% endblock %}

{% block content %}
<div class="debug-container">
    <h1 class="mb-4">Hugging Face API Debug</h1>

    <div class="debug-card">
        <div class="debug-header">
            <h2>API Configuration</h2>
        </div>
        <div class="debug-body">
            <div class="row">
                <div class="col-md-4">
                    <p><strong>AI Provider:</strong> <span class="badge bg-primary">{{ provider }}</span></p>
                </div>
                <div class="col-md-4">
                    <p><strong>Hugging Face API Key:</strong>
                        {% if api_key_present %}
                        <span class="api-status success">Yes</span>
                        {% else %}
                        <span class="api-status error">No</span>
                        {% endif %}
                    </p>
                </div>
                <div class="col-md-4">
                    <p><strong>Model Type:</strong>
                        {% if is_t5_model %}
                        <span class="badge bg-info">T5 Model</span>
                        {% else %}
                        <span class="badge bg-secondary">Standard Model</span>
                        {% endif %}
                    </p>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <p><strong>Model:</strong> <code>{{ model }}</code></p>
                </div>
            </div>
        </div>
    </div>

    <div class="debug-card">
        <div class="debug-header">
            <h2>Test API Request</h2>
        </div>
        <div class="debug-body">
            <form method="post">
                {% csrf_token %}
                <div class="row mb-3">
                    <div class="col-md-4">
                        <label for="language" class="form-label">Language</label>
                        <select class="form-select" id="language" name="language">
                            {% for lang_code, lang_name in languages.items %}
                            <option value="{{ lang_code }}">{{ lang_name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="business_type" class="form-label">Business Type</label>
                        <input type="text" class="form-control" id="business_type" name="business_type" placeholder="e.g., Restaurant, Tech Company">
                    </div>
                    <div class="col-md-4">
                        <label for="target_audience" class="form-label">Target Audience</label>
                        <input type="text" class="form-control" id="target_audience" name="target_audience" placeholder="e.g., Young professionals">
                    </div>
                </div>
                <div class="mb-3">
                    <label for="title" class="form-label">Ad Title (Optional)</label>
                    <input type="text" class="form-control" id="title" name="title" placeholder="Enter a title to align content with">
                </div>
                <button type="submit" class="btn btn-primary">Test API</button>
            </form>
        </div>
    </div>

    {% if error %}
    <div class="debug-card">
        <div class="debug-header">
            <h2>Error</h2>
        </div>
        <div class="debug-body">
            <div class="alert alert-danger">{{ error }}</div>
        </div>
    </div>
    {% endif %}

    {% if headers %}
    <div class="debug-card">
        <div class="debug-header">
            <h2>Response Headers</h2>
        </div>
        <div class="debug-body">
            <table class="headers-table">
                <thead>
                    <tr>
                        <th>Header</th>
                        <th>Value</th>
                    </tr>
                </thead>
                <tbody>
                    {% for key, value in headers.items %}
                    <tr>
                        <td>{{ key }}</td>
                        <td>{{ value }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% endif %}

    {% if raw_response %}
    <div class="debug-card">
        <div class="debug-header">
            <h2>Raw Response</h2>
        </div>
        <div class="debug-body">
            <pre>{{ raw_response }}</pre>
        </div>
    </div>
    {% endif %}

    {% if result %}
    <div class="debug-card">
        <div class="debug-header">
            <h2>Parsed Result</h2>
        </div>
        <div class="debug-body">
            <pre>{{ result|pprint }}</pre>
        </div>
    </div>
    {% endif %}

    {% if suggestions %}
    <div class="debug-card">
        <div class="debug-header">
            <h2>Generated Suggestions</h2>
        </div>
        <div class="debug-body">
            {% for suggestion in suggestions %}
            <div class="card mb-3">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">{{ suggestion.title }}</h5>
                </div>
                <div class="card-body">
                    <p>{{ suggestion.content|linebreaks }}</p>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
