{% extends 'base.html' %}
{% load static %}

{% block title %}User Management | Enterprise QR{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'users/css/user_management.css' %}">
{% endblock %}

{% block content %}
<div class="user-management-container">
    <div class="user-management-header">
        <h1 class="section-title">User Management</h1>
        <p class="section-subtitle">Manage users, roles, and permissions for your organization.</p>
    </div>

    <!-- Filters and Search -->
    <div class="user-filters-container">
        <form method="get" class="user-filters-form">
            <div class="row g-3">
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="role">Role</label>
                        <select name="role" id="role" class="form-select">
                            <option value="">All Roles</option>
                            {% for role_value, role_name in roles %}
                            <option value="{{ role_value }}" {% if current_role == role_value %}selected{% endif %}>{{ role_name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="status">Status</label>
                        <select name="status" id="status" class="form-select">
                            <option value="">All Statuses</option>
                            <option value="active" {% if current_status == 'active' %}selected{% endif %}>Active</option>
                            <option value="inactive" {% if current_status == 'inactive' %}selected{% endif %}>Inactive</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="search">Search</label>
                        <input type="text" name="search" id="search" class="form-control" placeholder="Search users..." value="{{ search_query|default:'' }}">
                    </div>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-filter me-2"></i>Apply Filters
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- User Stats -->
    <div class="user-stats-container">
        <div class="row">
            <div class="col-md-3">
                <div class="user-stat-card">
                    <div class="user-stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="user-stat-content">
                        <h3 class="user-stat-value">{{ total_users }}</h3>
                        <p class="user-stat-label">Total Users</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="user-stat-card">
                    <div class="user-stat-icon">
                        <i class="fas fa-user-check"></i>
                    </div>
                    <div class="user-stat-content">
                        <h3 class="user-stat-value">{{ users.filter.is_active.count }}</h3>
                        <p class="user-stat-label">Active Users</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="user-stat-card">
                    <div class="user-stat-icon">
                        <i class="fas fa-user-shield"></i>
                    </div>
                    <div class="user-stat-content">
                        <h3 class="user-stat-value">{{ users.filter.is_staff.count }}</h3>
                        <p class="user-stat-label">Staff Users</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="user-stat-card">
                    <div class="user-stat-icon">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <div class="user-stat-content">
                        <a href="{% url 'users:user_create' %}" class="btn btn-success w-100">
                            <i class="fas fa-plus-circle me-2"></i>Add New User
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Users Table -->
    <div class="users-table-container">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Username</th>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Role</th>
                        <th>Status</th>
                        <th>Date Joined</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                    <tr>
                        <td>{{ user.username }}</td>
                        <td>{{ user.get_full_name|default:"-" }}</td>
                        <td>{{ user.email|default:"-" }}</td>
                        <td>
                            <span class="badge bg-{{ user.profile.role|default:'secondary' }}">
                                {{ user.profile.get_role_display|default:"User" }}
                            </span>
                        </td>
                        <td>
                            {% if user.is_active %}
                            <span class="badge bg-success">Active</span>
                            {% else %}
                            <span class="badge bg-danger">Inactive</span>
                            {% endif %}
                        </td>
                        <td>{{ user.date_joined|date:"M d, Y" }}</td>
                        <td>
                            <div class="btn-group">
                                <a href="{% url 'users:user_detail' user_id=user.id %}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'users:user_edit' user_id=user.id %}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'users:user_delete' user_id=user.id %}" class="btn btn-sm btn-danger">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <div class="empty-state">
                                <div class="empty-state-icon">
                                    <i class="fas fa-users-slash"></i>
                                </div>
                                <h4>No users found</h4>
                                <p>Try adjusting your search or filter criteria</p>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="pagination-container">
        <nav aria-label="User pagination">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if current_role %}&role={{ current_role }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}" aria-label="First">
                        <span aria-hidden="true">&laquo;&laquo;</span>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_role %}&role={{ current_role }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <a class="page-link" href="#" aria-label="First">
                        <span aria-hidden="true">&laquo;&laquo;</span>
                    </a>
                </li>
                <li class="page-item disabled">
                    <a class="page-link" href="#" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
                {% endif %}

                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                    <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_role %}&role={{ current_role }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}">{{ num }}</a>
                    </li>
                    {% endif %}
                {% endfor %}

                {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_role %}&role={{ current_role }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_role %}&role={{ current_role }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}" aria-label="Last">
                        <span aria-hidden="true">&raquo;&raquo;</span>
                    </a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <a class="page-link" href="#" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
                <li class="page-item disabled">
                    <a class="page-link" href="#" aria-label="Last">
                        <span aria-hidden="true">&raquo;&raquo;</span>
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'users/js/user_management.js' %}"></script>
{% endblock %}
