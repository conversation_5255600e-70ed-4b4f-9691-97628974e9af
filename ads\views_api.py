from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from django.db.models import Q
from django.views.decorators.http import require_POST
from django.views.decorators.csrf import csrf_protect, csrf_exempt
import json
import logging
import datetime
from .models import Campaign, AiFeedback, Ad
from .views_enterprise import call_local_ai
from .utils.ai_utils import get_user_friendly_error_message

logger = logging.getLogger(__name__)

@login_required
def search_campaigns(request):
    """
    API endpoint for searching campaigns
    Returns campaigns in chronological order based on search term
    """
    search_term = request.GET.get('term', '')

    # Get user's campaigns
    campaigns = Campaign.objects.filter(user=request.user)
    # Filter by search term if provided
    if search_term:
        campaigns = campaigns.filter(
            Q(name__icontains=search_term) |
            Q(description__icontains=search_term)
        )

    # Order by created_at (chronological order)
    campaigns = campaigns.order_by('-created_at')

    # Limit to 10 results
    campaigns = campaigns[:10]
    # Format results for Select2
    results = [
        {
            'id': campaign.id,
            'text': campaign.name,
            'created_at': campaign.created_at.strftime('%Y-%m-%d %H:%M'),
            'status': campaign.status,
            'description': campaign.description[:50] + '...' if len(campaign.description) > 50 else campaign.description
        }
        for campaign in campaigns
    ]
    # Add "No Campaign" option as the first result if no search term
    if not search_term:
        results.insert(0, {
            'id': '',
            'text': 'No Campaign (Individual Ad)',
            'created_at': '',
            'status': '',
            'description': 'Create this ad without associating it with a campaign'
        })
    return JsonResponse({
        'results': results,
        'pagination': {
            'more': False  # No pagination for simplicity
        }
    })

from .utils.ai_utils import get_user_friendly_error_message

@login_required
@csrf_protect
@require_POST
def generate_ad_suggestions(request):
    """
    API endpoint for generating ad suggestions using Smart AI Engine

    Uses the new Smart AI Engine with:
    1. Cache-first strategy (6-hour TTL)
    2. Groq model priority (llama3-70b → llama3-8b → gemma2-9b)
    3. Provider fallback (Groq → Mistral → OpenAI)
    4. Intelligent suggestion selection
    """
    from ai_services.clients import get_ai_client
    import logging

    logger = logging.getLogger(__name__)

    logger.info("🧠 Smart AI Engine: Incoming request to generate_suggestions")

    try:
        # Parse request body
        data = json.loads(request.body)

        # Extract parameters
        language = data.get('language', 'english')
        business_type = data.get('business_type', '')
        target_audience = data.get('target_audience', '')
        tone = data.get('tone', 'professional')
        ad_title = data.get('title', '')
        num_suggestions = data.get('num_suggestions', 3)

        logger.info(f"🎯 Parameters: {business_type} | {target_audience} | {tone} | {ad_title}")

        # Get the Smart AI Engine
        ai_client = get_ai_client()
        client_type = type(ai_client).__name__
        logger.info(f"🤖 Using AI Client: {client_type}")

        # Generate suggestions using Smart AI Engine
        suggestions = ai_client.generate_ad_suggestions(
            language=language,
            business_type=business_type,
            target_audience=target_audience,
            tone=tone,
            num_suggestions=num_suggestions,
            ad_title=ad_title
        )

        if suggestions:
            # Determine source from suggestions metadata
            source = 'smart_engine'
            if suggestions and len(suggestions) > 0:
                first_suggestion = suggestions[0]
                if first_suggestion.get('cached'):
                    source = 'cache'
                elif first_suggestion.get('provider') == 'groq':
                    source = 'groq'
                elif first_suggestion.get('provider') == 'mistral':
                    source = 'mistral'
                elif first_suggestion.get('provider') == 'openai':
                    source = 'openai'
                elif first_suggestion.get('offline'):
                    source = 'offline'

            logger.info(f"✅ Smart AI Engine generated {len(suggestions)} suggestions from {source}")

            return JsonResponse({
                'success': True,
                'suggestions': suggestions,
                'source': source,
                'engine': client_type
            })
        else:
            logger.warning("Smart AI Engine returned no suggestions")
            return JsonResponse({
                'success': False,
                'error': "No suggestions could be generated",
                'suggestions': [],
                'source': 'none'
            })

    except json.JSONDecodeError:
        logger.error("Invalid JSON in request body")

        # Use offline client for fallback
        from ai_services.clients import OfflineAIClient
        offline_client = OfflineAIClient()
        fallback_suggestions = offline_client.generate_ad_suggestions(
            language="english",
            business_type="General Business",
            target_audience="General Audience",
            tone="professional",
            num_suggestions=3,
            ad_title=""
        )

        return JsonResponse({
            'success': True,  # Always return success=True to avoid frontend errors
            'error': "Please provide valid input in JSON format",
            'error_code': "INVALID_JSON",
            'suggestions': fallback_suggestions,
            'source': 'offline'
        })

    except Exception as e:
        logger.error(f"Error generating ad suggestions: {str(e)}", exc_info=True)
        logger.info("❌ Smart AI Engine failed")

        # Final fallback - use offline client
        from ai_services.clients import OfflineAIClient
        offline_client = OfflineAIClient()
        fallback_suggestions = offline_client.generate_ad_suggestions(
            language="english",
            business_type="General Business",
            target_audience="General Audience",
            tone="professional",
            num_suggestions=3,
            ad_title=""
        )

        return JsonResponse({
            'success': True,  # Always return success=True to avoid frontend errors
            'error': "An error occurred while generating suggestions.",
            'error_code': "SERVER_ERROR",
            'error_details': str(e),
            'suggestions': fallback_suggestions,
            'source': 'offline'
        })

def get_fallback_suggestions(language: str, title: str = "") -> list:
    """
    Get fallback suggestions when AI generation fails

    Args:
        language: The language for the suggestions
        title: Optional title to use

    Returns:
        List of fallback suggestions
    """
    # Import the cache utilities
    from .utils.cache_utils import get_cached_suggestions, get_fallback_suggestions as get_hardcoded_suggestions

    # Try to get suggestions from cache first
    business_type = "general"  # Default business type
    target_audience = "general"  # Default target audience

    # Try to get from cache with extended expiry (30 days)
    cached_suggestions = get_cached_suggestions(
        language=language,
        business_type=business_type,
        target_audience=target_audience,
        title=title,
        max_age=60*60*24*30  # 30 days
    )

    if cached_suggestions:
        logger.info(f"[CACHE_USED] Using cached suggestions as fallback")
        # Mark these as fallback suggestions
        for suggestion in cached_suggestions:
            suggestion['fallback'] = True
        return cached_suggestions

    # If no cached suggestions, use hardcoded fallbacks
    logger.info(f"[FALLBACK_TRIGGERED] No cached suggestions found, using hardcoded fallbacks")
    return get_hardcoded_suggestions(language, title)

@login_required
@csrf_protect
@require_POST
def submit_feedback(request):
    """
    API endpoint for submitting feedback on AI-generated content
    """
    try:
        # Parse request body
        data = json.loads(request.body)

        # Extract parameters
        value = data.get('value', 'neutral')
        suggestion = data.get('suggestion', {})
        suggestion_index = data.get('suggestion_index', 0)
        page = data.get('page', '')
        timestamp = data.get('timestamp', datetime.datetime.now().isoformat())

        # Validate feedback value
        if value not in [choice[0] for choice in AiFeedback.FEEDBACK_CHOICES]:
            value = 'neutral'

        # Extract model and processing time from suggestion if available
        model_used = suggestion.get('model', '')
        language = suggestion.get('language', '')
        processing_time = suggestion.get('processing_time', 0)

        # Find associated ad if available
        ad = None
        if 'ad_id' in data:
            try:
                ad = Ad.objects.get(id=data['ad_id'])
            except Ad.DoesNotExist:
                pass

        # Create feedback record
        feedback = AiFeedback.objects.create(
            user=request.user,
            ad=ad,
            suggestion_data=suggestion,
            feedback=value,
            comments=data.get('comments', ''),
            model_used=model_used,
            language=language,
            page=page,
            processing_time=processing_time,
            created_at=datetime.datetime.now()
        )

        # Log the feedback
        logger.info(f"AI Feedback received: {value} for model {model_used} from user {request.user.username}")

        return JsonResponse({
            'success': True,
            'message': 'Feedback submitted successfully',
            'feedback_id': feedback.id
        })

    except json.JSONDecodeError:
        logger.error("Invalid JSON in feedback submission")
        return JsonResponse({
            'success': False,
            'error': "Invalid JSON format"
        }, status=400)

    except Exception as e:
        logger.error(f"Error submitting feedback: {str(e)}", exc_info=True)
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)

@csrf_exempt
def generate_ad_suggestions_no_auth(request):
    """
    API endpoint for generating ad suggestions using Smart AI Engine (NO AUTH REQUIRED - FOR TESTING)

    This is a temporary endpoint for testing without authentication.
    """
    from ai_services.clients import get_ai_client
    import logging

    logger = logging.getLogger(__name__)

    logger.info("🧠 Smart AI Engine: Incoming request to generate_suggestions (NO AUTH)")

    try:
        # Parse request body
        data = json.loads(request.body)

        # Extract parameters
        language = data.get('language', 'english')
        business_type = data.get('business_type', '')
        target_audience = data.get('target_audience', '')
        tone = data.get('tone', 'professional')
        ad_title = data.get('title', '')
        num_suggestions = data.get('num_suggestions', 3)

        logger.info(f"🎯 Parameters: {business_type} | {target_audience} | {tone} | {ad_title}")

        # Get the Smart AI Engine
        ai_client = get_ai_client()
        client_type = type(ai_client).__name__
        logger.info(f"🤖 Using AI Client: {client_type}")

        # Generate suggestions using Smart AI Engine
        suggestions = ai_client.generate_ad_suggestions(
            language=language,
            business_type=business_type,
            target_audience=target_audience,
            tone=tone,
            num_suggestions=num_suggestions,
            ad_title=ad_title
        )

        if suggestions:
            # Determine source from suggestions metadata
            source = 'smart_engine'
            if suggestions and len(suggestions) > 0:
                first_suggestion = suggestions[0]
                if first_suggestion.get('cached'):
                    source = 'cache'
                elif first_suggestion.get('provider') == 'groq':
                    source = 'groq'
                elif first_suggestion.get('provider') == 'mistral':
                    source = 'mistral'
                elif first_suggestion.get('provider') == 'openai':
                    source = 'openai'
                elif first_suggestion.get('offline'):
                    source = 'offline'

            logger.info(f"✅ Smart AI Engine generated {len(suggestions)} suggestions from {source}")

            return JsonResponse({
                'success': True,
                'suggestions': suggestions,
                'source': source,
                'engine': client_type
            })
        else:
            logger.warning("Smart AI Engine returned no suggestions")
            return JsonResponse({
                'success': False,
                'error': "No suggestions could be generated",
                'suggestions': [],
                'source': 'none'
            })

    except json.JSONDecodeError:
        logger.error("Invalid JSON in request body")

        # Use offline client for fallback
        from ai_services.clients import OfflineAIClient
        offline_client = OfflineAIClient()
        fallback_suggestions = offline_client.generate_ad_suggestions(
            language="english",
            business_type="General Business",
            target_audience="General Audience",
            tone="professional",
            num_suggestions=3,
            ad_title=""
        )

        return JsonResponse({
            'success': True,  # Always return success=True to avoid frontend errors
            'error': "Please provide valid input in JSON format",
            'error_code': "INVALID_JSON",
            'suggestions': fallback_suggestions,
            'source': 'offline'
        })

    except Exception as e:
        logger.error(f"Error generating ad suggestions: {str(e)}", exc_info=True)
        logger.info("❌ Smart AI Engine failed")

        # Final fallback - use offline client
        from ai_services.clients import OfflineAIClient
        offline_client = OfflineAIClient()
        fallback_suggestions = offline_client.generate_ad_suggestions(
            language="english",
            business_type="General Business",
            target_audience="General Audience",
            tone="professional",
            num_suggestions=3,
            ad_title=""
        )

        return JsonResponse({
            'success': True,  # Always return success=True to avoid frontend errors
            'error': "An error occurred while generating suggestions.",
            'error_code': "SERVER_ERROR",
            'error_details': str(e),
            'suggestions': fallback_suggestions,
            'source': 'offline'
        })
