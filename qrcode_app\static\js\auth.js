document.addEventListener('DOMContentLoaded', function() {
    // Tab switching
    initTabs();

    // Password visibility toggle
    initPasswordToggles();

    // Password strength meter
    initPasswordStrengthMeter();

    // Enterprise SSO toggle
    initEnterpriseSSO();

    // Form submission
    initFormSubmission();

    // SSO buttons
    initSSOButtons();
});

// Initialize tab switching
function initTabs() {
    const tabs = document.querySelectorAll('.auth-tab');
    const forms = document.querySelectorAll('.auth-form');

    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const target = this.getAttribute('data-tab');

            // Update active tab
            tabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');

            // Show corresponding form
            forms.forEach(form => {
                form.classList.remove('active');
                if (form.classList.contains(target + '-form')) {
                    form.classList.add('active');
                }
            });
        });
    });
}

// Initialize password visibility toggles
function initPasswordToggles() {
    const toggleButtons = document.querySelectorAll('.toggle-password');

    toggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            const input = this.parentElement.querySelector('input');
            const icon = this.querySelector('i');

            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    });
}

// Initialize password strength meter
function initPasswordStrengthMeter() {
    const passwordInput = document.getElementById('signup-password');
    const strengthSegments = document.querySelectorAll('.strength-segment');
    const strengthValue = document.getElementById('strength-value');

    if (!passwordInput) return;

    passwordInput.addEventListener('input', function() {
        const password = this.value;
        const strength = calculatePasswordStrength(password);

        // Update strength meter
        strengthSegments.forEach((segment, index) => {
            segment.classList.remove('weak', 'medium', 'strong');

            if (index < strength) {
                if (strength === 1) {
                    segment.classList.add('weak');
                } else if (strength === 2 || strength === 3) {
                    segment.classList.add('medium');
                } else {
                    segment.classList.add('strong');
                }
            }
        });

        // Update strength text
        if (password.length === 0) {
            strengthValue.textContent = 'Weak';
        } else if (strength === 1) {
            strengthValue.textContent = 'Weak';
        } else if (strength === 2) {
            strengthValue.textContent = 'Fair';
        } else if (strength === 3) {
            strengthValue.textContent = 'Good';
        } else {
            strengthValue.textContent = 'Strong';
        }
    });
}

// Calculate password strength (0-4)
function calculatePasswordStrength(password) {
    if (password.length === 0) return 0;

    let strength = 0;

    // Length check
    if (password.length >= 8) strength += 1;

    // Contains lowercase
    if (/[a-z]/.test(password)) strength += 1;

    // Contains uppercase and numbers
    if (/[A-Z]/.test(password) && /[0-9]/.test(password)) strength += 1;

    // Contains special characters
    if (/[^A-Za-z0-9]/.test(password)) strength += 1;

    return strength;
}

// Initialize Enterprise SSO toggle
function initEnterpriseSSO() {
    const ssoButton = document.querySelector('.enterprise-sso-btn');
    const ssoForm = document.querySelector('.enterprise-sso-form');

    if (!ssoButton || !ssoForm) return;

    ssoButton.addEventListener('click', function() {
        ssoForm.classList.toggle('active');

        if (ssoForm.classList.contains('active')) {
            this.innerHTML = '<i class="fas fa-building"></i><span>Hide Enterprise SSO</span>';
        } else {
            this.innerHTML = '<i class="fas fa-building"></i><span>Enterprise SSO</span>';
        }
    });
}

// Initialize form submission
function initFormSubmission() {
    const loginForm = document.getElementById('login-form');
    const signupForm = document.getElementById('signup-form');

    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Get form data
            const email = document.getElementById('login-email').value;
            const password = document.getElementById('login-password').value;
            const remember = document.getElementById('remember-me').checked;

            // Validate form data
            if (!email || !password) {
                showNotification('Please fill in all required fields', 'error');
                return;
            }

            // Simulate login API call
            showNotification('Logging in...', 'info');

            // Simulate successful login after 1.5 seconds
            setTimeout(() => {
                // In a real application, this would be an API call
                // For demo purposes, we'll just redirect to the dashboard

                // Store auth token in localStorage or sessionStorage
                const token = generateDemoToken();
                if (remember) {
                    localStorage.setItem('auth_token', token);
                } else {
                    sessionStorage.setItem('auth_token', token);
                }

                // Store user info
                const userInfo = {
                    email: email,
                    name: 'Demo User'
                };
                localStorage.setItem('user_info', JSON.stringify(userInfo));

                // Set user roles (default to ADMIN for demo)
                if (typeof rbac !== 'undefined') {
                    rbac.setUser(userInfo, ['ADMIN']);
                } else {
                    // Store roles in localStorage for later use
                    localStorage.setItem('user_roles', JSON.stringify(['ADMIN']));
                }

                showNotification('Login successful! Redirecting...', 'success');

                // Redirect to dashboard
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1000);
            }, 1500);
        });
    }

    if (signupForm) {
        signupForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Get form data
            const firstName = document.getElementById('first-name').value;
            const lastName = document.getElementById('last-name').value;
            const email = document.getElementById('signup-email').value;
            const password = document.getElementById('signup-password').value;
            const confirmPassword = document.getElementById('confirm-password').value;
            const terms = document.getElementById('terms').checked;

            // Validate form data
            if (!firstName || !lastName || !email || !password || !confirmPassword) {
                showNotification('Please fill in all required fields', 'error');
                return;
            }

            if (password !== confirmPassword) {
                showNotification('Passwords do not match', 'error');
                return;
            }

            if (!terms) {
                showNotification('Please agree to the Terms of Service and Privacy Policy', 'error');
                return;
            }

            // Simulate signup API call
            showNotification('Creating your account...', 'info');

            // Simulate successful signup after 1.5 seconds
            setTimeout(() => {
                // In a real application, this would be an API call
                // For demo purposes, we'll just redirect to the dashboard

                // Store auth token in sessionStorage
                const token = generateDemoToken();
                sessionStorage.setItem('auth_token', token);

                // Store user info
                const userInfo = {
                    email: email,
                    name: firstName + ' ' + lastName
                };
                localStorage.setItem('user_info', JSON.stringify(userInfo));

                // Set user roles (default to CREATOR for new users)
                if (typeof rbac !== 'undefined') {
                    rbac.setUser(userInfo, ['CREATOR']);
                } else {
                    // Store roles in localStorage for later use
                    localStorage.setItem('user_roles', JSON.stringify(['CREATOR']));
                }

                showNotification('Account created successfully! Redirecting...', 'success');

                // Redirect to dashboard
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1000);
            }, 1500);
        });
    }
}

// Initialize SSO buttons
function initSSOButtons() {
    const ssoButtons = document.querySelectorAll('.sso-btn');

    ssoButtons.forEach(button => {
        button.addEventListener('click', function() {
            const provider = this.classList.contains('google') ? 'Google' :
                            this.classList.contains('microsoft') ? 'Microsoft' :
                            this.classList.contains('apple') ? 'Apple' : 'Unknown';

            showNotification(`Redirecting to ${provider} for authentication...`, 'info');

            // In a real application, this would redirect to the OAuth provider
            // For demo purposes, we'll just show a notification
            setTimeout(() => {
                showNotification('This is a demo. SSO authentication would happen here.', 'info');
            }, 1500);
        });
    });
}

// Generate a demo token
function generateDemoToken() {
    return 'demo_' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

// Show notification
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;

    // Add icon based on type
    let icon = 'info-circle';
    if (type === 'success') icon = 'check-circle';
    if (type === 'error') icon = 'exclamation-circle';

    notification.innerHTML = `
        <i class="fas fa-${icon}"></i>
        <span>${message}</span>
        <button class="notification-close"><i class="fas fa-times"></i></button>
    `;

    // Add to the DOM
    document.body.appendChild(notification);

    // Add styles if not already added
    if (!document.getElementById('notification-styles')) {
        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            .notification {
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem;
                background-color: white;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                display: flex;
                align-items: center;
                gap: 0.75rem;
                z-index: 1000;
                max-width: 350px;
                transform: translateX(120%);
                transition: transform 0.3s ease;
                border-left: 4px solid #3b82f6;
            }

            .notification.show {
                transform: translateX(0);
            }

            .notification i {
                font-size: 1.25rem;
                flex-shrink: 0;
            }

            .notification span {
                font-size: 0.875rem;
                color: #374151;
                flex-grow: 1;
            }

            .notification-close {
                background: none;
                border: none;
                color: #9ca3af;
                cursor: pointer;
                font-size: 0.875rem;
                padding: 0.25rem;
                transition: color 0.2s ease;
            }

            .notification-close:hover {
                color: #4b5563;
            }

            .notification-success {
                border-left-color: #10b981;
            }

            .notification-success i {
                color: #10b981;
            }

            .notification-error {
                border-left-color: #ef4444;
            }

            .notification-error i {
                color: #ef4444;
            }

            .notification-info i {
                color: #3b82f6;
            }
        `;
        document.head.appendChild(style);
    }

    // Show the notification
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);

    // Set up close button
    const closeButton = notification.querySelector('.notification-close');
    closeButton.addEventListener('click', () => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    });

    // Auto-close after 5 seconds
    setTimeout(() => {
        if (document.body.contains(notification)) {
            notification.classList.remove('show');
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    notification.remove();
                }
            }, 300);
        }
    }, 5000);
}
