/**
 * QR Code Detail Page JavaScript
 * Handles customization and interaction with QR codes
 */

document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const customizeBtn = document.getElementById('customize-qr');
    const customizationPanel = document.getElementById('customization-panel');
    const applyCustomizationBtn = document.getElementById('apply-customization');
    const qrDisplay = document.getElementById('qr-display');
    const printBtn = document.getElementById('print-qr');
    const downloadBtn = document.getElementById('download-preview');
    const shareBtn = document.getElementById('share-qr');
    const deleteBtn = document.getElementById('delete-qr-btn');
    const confirmDeleteBtn = document.getElementById('confirm-delete-qr');
    const deleteForm = document.getElementById('delete-qr-form');

    // Customization form elements - Style
    const dotStyleSelect = document.getElementById('detail-dot-style');
    const cornerStyleSelect = document.getElementById('detail-corner-style');
    const cornerDotStyleSelect = document.getElementById('detail-corner-dot-style');
    const shapeSelect = document.getElementById('detail-shape');

    // Customization form elements - Colors
    const foregroundColorInput = document.getElementById('detail-foreground-color');
    const backgroundColorInput = document.getElementById('detail-background-color');
    const gradientTypeSelect = document.getElementById('detail-gradient-type');
    const gradientColorInput = document.getElementById('detail-gradient-color');

    // Customization form elements - Frame
    const frameStyleSelect = document.getElementById('detail-frame-style');
    const frameColorInput = document.getElementById('detail-frame-color');
    const titleInput = document.getElementById('detail-title');
    const guidingTextInput = document.getElementById('detail-guiding-text');
    const textPositionSelect = document.getElementById('detail-text-position');

    // Customization form elements - Logo
    const logoUploadInput = document.getElementById('detail-logo-upload');
    const logoSizeSelect = document.getElementById('detail-logo-size');
    const logoShapeSelect = document.getElementById('detail-logo-shape');
    const logoRemoveBackgroundCheck = document.getElementById('detail-logo-remove-background');

    // Reset button
    const resetCustomizationBtn = document.getElementById('reset-customization');

    // Get QR code data from the page
    const qrData = getQRCodeData();

    // Log QR data for debugging
    console.log('QR Data:', qrData);

    // Initialize QR code library with proper error handling
    let qrCode;

    // Wait a moment to ensure QRCodeStyling is fully loaded
    setTimeout(() => {
        try {
            // Check if QRCodeStyling is available
            if (typeof QRCodeStyling === 'undefined') {
                console.error('QRCodeStyling library not loaded');
                if (qrDisplay) {
                    qrDisplay.innerHTML += `
                        <div class="alert alert-danger mt-3">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            QR code library not loaded. Please refresh the page.
                        </div>
                    `;
                }
                return;
            }

            // Get the actual QR code data
            const actualData = qrData.data || document.querySelector('.data-display')?.textContent.trim() || "https://example.com";
            console.log('Using QR data:', actualData);

            // Create the QR code
            qrCode = new QRCodeStyling({
                width: 300,
                height: 300,
                type: "svg",
                data: actualData,
                image: qrData.logo || "",
                dotsOptions: {
                    color: qrData.foregroundColor || "#000000",
                    type: "square"
                },
                backgroundOptions: {
                    color: qrData.backgroundColor || "#FFFFFF",
                },
                cornersSquareOptions: {
                    type: "square"
                },
                cornersDotOptions: {
                    type: "square"
                },
                imageOptions: {
                    crossOrigin: "anonymous",
                    margin: 10
                }
            });

            console.log('QR code object created successfully');

            // Don't replace the existing QR code image on initial load
            // Only store the QR code object for later customization

        } catch (error) {
            console.error('Error initializing QR code:', error);
            // Show error message to user
            if (qrDisplay) {
                qrDisplay.innerHTML += `
                    <div class="alert alert-danger mt-3">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        Error initializing QR code: ${error.message}. Please refresh the page.
                    </div>
                `;
            }
        }
    }, 500); // Wait 500ms to ensure library is loaded

    // Toggle customization panel
    if (customizeBtn) {
        customizeBtn.addEventListener('click', function() {
            if (customizationPanel) {
                // Toggle display instead of using classes
                if (customizationPanel.style.display === 'none') {
                    customizationPanel.style.display = 'block';
                    customizeBtn.innerHTML = '<i class="fas fa-times me-1"></i>Hide Customization';
                } else {
                    customizationPanel.style.display = 'none';
                    customizeBtn.innerHTML = '<i class="fas fa-paint-brush me-1"></i>Customize';
                }
            }
        });
    }

    // Apply customization
    if (applyCustomizationBtn) {
        applyCustomizationBtn.addEventListener('click', function() {
            applyCustomization();
        });
    }

    // Reset customization
    if (resetCustomizationBtn) {
        resetCustomizationBtn.addEventListener('click', function() {
            resetCustomization();
        });
    }

    // Logo upload preview
    if (logoUploadInput) {
        logoUploadInput.addEventListener('change', function() {
            previewLogo();
        });
    }

    // Print QR code
    if (printBtn) {
        printBtn.addEventListener('click', function() {
            printQRCode();
        });
    }

    // Download QR code
    if (downloadBtn) {
        downloadBtn.addEventListener('click', function() {
            downloadQRCode();
        });
    }

    // Delete functionality is now handled directly in the template
    // to avoid conflicts with Bootstrap modal initialization

    // Function to get CSRF token
    function getCsrfToken() {
        const cookieValue = document.cookie
            .split('; ')
            .find(row => row.startsWith('csrftoken='))
            ?.split('=')[1];

        return cookieValue || '';
    }

    // Copy share link
    const copyLinkBtn = document.getElementById('copyLink');
    if (copyLinkBtn) {
        copyLinkBtn.addEventListener('click', function(e) {
            e.preventDefault();

            // Get the URL from the data attribute
            const url = this.getAttribute('data-url');

            // Create a temporary input element
            const tempInput = document.createElement('input');
            tempInput.value = url;
            document.body.appendChild(tempInput);

            // Select and copy the text
            tempInput.select();
            document.execCommand('copy');

            // Remove the temporary input
            document.body.removeChild(tempInput);

            // Show copied notification
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fas fa-check me-2"></i>Copied!';

            // Add success styling
            this.classList.add('text-success');

            // Reset after a delay
            setTimeout(() => {
                this.innerHTML = originalText;
                this.classList.remove('text-success');
            }, 2000);
        });
    }

    // Function to get QR code data from the page
    function getQRCodeData() {
        // Get data from the table
        const rows = document.querySelectorAll('table tbody tr');
        const data = {
            data: "https://example.com", // Default fallback
            foregroundColor: "#000000",
            backgroundColor: "#FFFFFF",
            imageUrl: ""
        };

        try {
            // Extract data from table rows
            rows.forEach(row => {
                const header = row.querySelector('th')?.textContent.trim();
                if (!header) return;

                const cell = row.querySelector('td');
                if (!cell) return;

                switch(header) {
                    case 'Data':
                        // Try to get data from links first
                        const link = cell.querySelector('a');
                        if (link) {
                            data.data = link.getAttribute('href') || link.textContent.trim();
                        } else {
                            // Otherwise get text content
                            const dataText = cell.textContent.trim();
                            if (dataText) {
                                data.data = dataText;
                            }
                        }
                        break;
                    case 'Colors':
                        // Try to get colors from style attributes first
                        const colorSwatches = cell.querySelectorAll('.color-swatch');
                        if (colorSwatches.length >= 2) {
                            data.foregroundColor = colorSwatches[0].style.backgroundColor || "#000000";
                            data.backgroundColor = colorSwatches[1].style.backgroundColor || "#FFFFFF";
                        }

                        // If that fails, try to get from text content
                        const colorTexts = cell.querySelectorAll('span');
                        if (colorTexts.length >= 2) {
                            if (!data.foregroundColor || data.foregroundColor === "rgba(0, 0, 0, 0)") {
                                data.foregroundColor = colorTexts[0].textContent.trim() || "#000000";
                            }
                            if (!data.backgroundColor || data.backgroundColor === "rgba(0, 0, 0, 0)") {
                                data.backgroundColor = colorTexts[1].textContent.trim() || "#FFFFFF";
                            }
                        }
                        break;
                }
            });

            // Get image URL
            const qrImage = document.querySelector('.qr-code-display img');
            if (qrImage) {
                data.imageUrl = qrImage.src;

                // If we couldn't get data from the table, try to extract from URL
                if (data.data === "https://example.com") {
                    // Try to get data from URL parameters if it's a QR code service
                    const urlParams = new URLSearchParams(new URL(qrImage.src).search);
                    const qrData = urlParams.get('data');
                    if (qrData) {
                        data.data = qrData;
                    }
                }
            }

            console.log('Extracted QR data:', data);
        } catch (error) {
            console.error('Error extracting QR data:', error);
        }

        return data;
    }

    // Function to apply customization
    function applyCustomization() {
        try {
            // Check if QRCodeStyling is available
            if (typeof QRCodeStyling === 'undefined') {
                console.error('QRCodeStyling library not loaded');
                showCustomizationError('QR code library not loaded. Please refresh the page and try again.');
                return;
            }

            // Check if QR code was initialized successfully
            if (!qrCode) {
                console.error('QR code not initialized, creating a new one');

                // Get the actual QR code data from the page
                const actualData = document.querySelector('.data-display')?.textContent.trim() ||
                                  document.querySelector('table tbody tr:nth-child(3) td')?.textContent.trim() ||
                                  "https://example.com";

                // Create a new QR code object if it doesn't exist
                try {
                    qrCode = new QRCodeStyling({
                        width: 300,
                        height: 300,
                        type: "svg",
                        data: actualData,
                        dotsOptions: {
                            color: "#000000",
                            type: "square"
                        },
                        backgroundOptions: {
                            color: "#FFFFFF",
                        },
                        cornersSquareOptions: {
                            type: "square"
                        },
                        cornersDotOptions: {
                            type: "square"
                        }
                    });
                } catch (initError) {
                    console.error('Failed to create QR code:', initError);
                    showCustomizationError('Could not create QR code. Please refresh the page and try again.');
                    return;
                }
            }

            console.log('Applying customization...');

            // Get style values
            const dotStyle = dotStyleSelect ? dotStyleSelect.value : 'square';
            const cornerStyle = cornerStyleSelect ? cornerStyleSelect.value : 'square';
            const cornerDotStyle = cornerDotStyleSelect ? cornerDotStyleSelect.value : 'square';
            const qrShape = shapeSelect ? shapeSelect.value : 'square';

            // Get color values
            const fgColor = foregroundColorInput ? foregroundColorInput.value : '#000000';
            const bgColor = backgroundColorInput ? backgroundColorInput.value : '#FFFFFF';
            const gradientType = gradientTypeSelect ? gradientTypeSelect.value : 'none';
            const gradientColor = gradientColorInput ? gradientColorInput.value : '#4A6CF7';

            // Get frame values
            const frameStyle = frameStyleSelect ? frameStyleSelect.value : 'none';
            const frameColor = frameColorInput ? frameColorInput.value : '#4A6CF7';
            const title = titleInput ? titleInput.value : '';
            const guidingText = guidingTextInput ? guidingTextInput.value : '';
            const textPosition = textPositionSelect ? textPositionSelect.value : 'below';

            // Get logo values
            let logoImage = null;
            const logoSize = logoSizeSelect ? logoSizeSelect.value : 'medium';
            const logoShape = logoShapeSelect ? logoShapeSelect.value : 'circle';
            const removeLogoBg = logoRemoveBackgroundCheck ? logoRemoveBackgroundCheck.checked : false;

            // Create gradient if selected
            let gradient = null;
            if (gradientType !== 'none') {
                gradient = {
                    type: gradientType,
                    colorStops: [
                        { offset: 0, color: fgColor },
                        { offset: 1, color: gradientColor }
                    ]
                };
            }

            // Log customization options for debugging
            console.log('Customization options:', {
                dotStyle, cornerStyle, cornerDotStyle, qrShape,
                fgColor, bgColor, gradientType, gradientColor,
                frameStyle, frameColor, title, guidingText, textPosition,
                logoSize, logoShape, removeLogoBg
            });

            // Get the QR code data from the page if not already set
            const qrCodeData = qrData.data ||
                              document.querySelector('.data-display')?.textContent.trim() ||
                              document.querySelector('table tbody tr:nth-child(3) td')?.textContent.trim() ||
                              "https://example.com";

            // Update QR code options
            const qrOptions = {
                width: 300,
                height: 300,
                type: "svg",
                data: qrCodeData,
                dotsOptions: {
                    color: gradient || fgColor,
                    type: dotStyle
                },
                backgroundOptions: {
                    color: bgColor,
                },
                cornersSquareOptions: {
                    type: cornerStyle,
                    color: gradient || fgColor
                },
                cornersDotOptions: {
                    type: cornerDotStyle,
                    color: gradient || fgColor
                }
            };

            // Add logo if uploaded
            if (logoUploadInput && logoUploadInput.files && logoUploadInput.files[0]) {
                const logoFile = logoUploadInput.files[0];
                const reader = new FileReader();

                reader.onload = function(e) {
                    try {
                        // Update QR code with logo
                        qrCode.update({
                            ...qrOptions,
                            image: e.target.result,
                            imageOptions: {
                                crossOrigin: "anonymous",
                                margin: 5,
                                imageSize: getLogoSizeValue(logoSize),
                                hideBackgroundDots: removeLogoBg
                            }
                        });

                        // Clear the display and append updated QR code
                        if (qrDisplay) {
                            // Save the original image for backup
                            const originalImg = qrDisplay.querySelector('img');
                            const originalImgSrc = originalImg ? originalImg.src : null;

                            // Clear and append new QR code
                            qrDisplay.innerHTML = '';
                            qrCode.append(qrDisplay);
                            console.log('QR code with logo updated and appended to display');

                            // Add frame if selected
                            if (frameStyle !== 'none') {
                                addFrame(frameStyle, frameColor, title, guidingText, textPosition);
                            }

                            // Enable download button
                            if (downloadBtn) {
                                downloadBtn.disabled = false;
                            }

                            // Show success message
                            showCustomizationSuccess();
                        }
                    } catch (error) {
                        console.error('Error updating QR code with logo:', error);
                        showCustomizationError('Error applying logo to QR code. Please try a different logo or refresh the page.');
                    }
                };

                reader.onerror = function(error) {
                    console.error('Error reading logo file:', error);
                    showCustomizationError('Error reading logo file. Please try a different image.');
                };

                reader.readAsDataURL(logoFile);
            } else {
                try {
                    // Update QR code without logo
                    qrCode.update(qrOptions);
                    console.log('QR code updated without logo');

                    // Clear the display and append updated QR code
                    if (qrDisplay) {
                        // Save the original image for backup
                        const originalImg = qrDisplay.querySelector('img');
                        const originalImgSrc = originalImg ? originalImg.src : null;

                        // Clear and append new QR code
                        qrDisplay.innerHTML = '';
                        qrCode.append(qrDisplay);
                        console.log('Updated QR code appended to display');

                        // Add frame if selected
                        if (frameStyle !== 'none') {
                            addFrame(frameStyle, frameColor, title, guidingText, textPosition);
                        }

                        // Enable download button
                        if (downloadBtn) {
                            downloadBtn.disabled = false;
                        }

                        // Show success message
                        showCustomizationSuccess();
                    }
                } catch (error) {
                    console.error('Error updating QR code:', error);
                    showCustomizationError('Error updating QR code. Please try different settings or refresh the page.');
                }
            }
        } catch (error) {
            console.error('Error in applyCustomization:', error);
            showCustomizationError('An unexpected error occurred. Please try again or refresh the page.');
        }
    }

    // Function to show customization error message
    function showCustomizationError(message) {
        // Create error message
        const errorMsg = document.createElement('div');
        errorMsg.className = 'alert alert-danger mt-2 mb-0 py-2';
        errorMsg.innerHTML = `<i class="fas fa-exclamation-circle me-2"></i>${message}`;

        // Add to the customization panel
        const customizationPanel = document.getElementById('customization-panel');
        if (customizationPanel) {
            // Remove any existing messages
            const existingMsg = customizationPanel.querySelector('.alert');
            if (existingMsg) {
                existingMsg.remove();
            }

            // Add the new message
            customizationPanel.appendChild(errorMsg);

            // Remove after 5 seconds
            setTimeout(() => {
                errorMsg.remove();
            }, 5000);
        }
    }

    // Function to get logo size value
    function getLogoSizeValue(size) {
        switch(size) {
            case 'small': return 0.1;
            case 'large': return 0.3;
            case 'medium':
            default: return 0.2;
        }
    }

    // Function to show customization success message
    function showCustomizationSuccess() {
        // Create success message
        const successMsg = document.createElement('div');
        successMsg.className = 'alert alert-success mt-2 mb-0 py-2';
        successMsg.innerHTML = '<i class="fas fa-check-circle me-2"></i>QR code customized successfully!';

        // Add to the customization panel
        const customizationPanel = document.getElementById('customization-panel');
        if (customizationPanel) {
            // Remove any existing success message
            const existingMsg = customizationPanel.querySelector('.alert-success');
            if (existingMsg) {
                existingMsg.remove();
            }

            // Add the new message
            customizationPanel.appendChild(successMsg);

            // Remove after 3 seconds
            setTimeout(() => {
                successMsg.remove();
            }, 3000);
        }
    }

    // Function to add frame to QR code
    function addFrame(frameType, frameColor, title, guidingText, textPosition) {
        const qrElement = qrDisplay.querySelector('svg');
        if (!qrElement) return;

        // Create wrapper for framed QR code
        const wrapper = document.createElement('div');
        wrapper.className = `qr-frame qr-frame-${frameType}`;
        wrapper.style.borderColor = frameColor;

        // Add title if provided
        if (title) {
            const titleElement = document.createElement('div');
            titleElement.className = 'qr-title';
            titleElement.textContent = title;
            titleElement.style.color = frameColor;
            wrapper.appendChild(titleElement);
        }

        // Add guiding text based on position
        if (guidingText) {
            const textElement = document.createElement('div');
            textElement.className = `qr-guiding-text qr-text-${textPosition}`;
            textElement.textContent = guidingText;

            // Position the text based on the selected position
            switch(textPosition) {
                case 'above':
                    wrapper.appendChild(textElement);
                    break;
                case 'left':
                    wrapper.style.display = 'flex';
                    wrapper.style.alignItems = 'center';
                    textElement.style.marginRight = '15px';
                    textElement.style.writingMode = 'vertical-rl';
                    textElement.style.transform = 'rotate(180deg)';
                    wrapper.appendChild(textElement);
                    break;
                case 'right':
                    wrapper.style.display = 'flex';
                    wrapper.style.alignItems = 'center';
                    textElement.style.marginLeft = '15px';
                    textElement.style.writingMode = 'vertical-rl';
                    wrapper.appendChild(textElement);
                    break;
                case 'below':
                default:
                    // Will be added after the QR container
                    break;
            }
        }

        // Add the QR code
        const qrContainer = document.createElement('div');
        qrContainer.className = 'qr-container';

        // Apply special styling for specific frame types
        if (frameType === 'phone') {
            qrContainer.className += ' qr-phone-frame';
            const phoneFrame = document.createElement('div');
            phoneFrame.className = 'phone-frame';
            phoneFrame.style.borderColor = frameColor;

            const phoneScreen = document.createElement('div');
            phoneScreen.className = 'phone-screen';
            phoneScreen.appendChild(qrElement);

            const phoneButton = document.createElement('div');
            phoneButton.className = 'phone-button';
            phoneButton.style.backgroundColor = frameColor;

            phoneFrame.appendChild(phoneScreen);
            phoneFrame.appendChild(phoneButton);
            qrContainer.appendChild(phoneFrame);
        } else if (frameType === 'business-card') {
            qrContainer.className += ' qr-business-card';
            qrContainer.style.borderColor = frameColor;

            const cardContent = document.createElement('div');
            cardContent.className = 'card-content';

            const cardLogo = document.createElement('div');
            cardLogo.className = 'card-logo';
            cardLogo.style.backgroundColor = frameColor;
            cardLogo.innerHTML = '<i class="fas fa-building"></i>';

            const cardQR = document.createElement('div');
            cardQR.className = 'card-qr';
            cardQR.appendChild(qrElement);

            cardContent.appendChild(cardLogo);
            cardContent.appendChild(cardQR);
            qrContainer.appendChild(cardContent);
        } else {
            qrContainer.appendChild(qrElement);
        }

        wrapper.appendChild(qrContainer);

        // Add guiding text below if position is 'below'
        if (guidingText && textPosition === 'below') {
            const textElement = document.createElement('div');
            textElement.className = 'qr-guiding-text';
            textElement.textContent = guidingText;
            wrapper.appendChild(textElement);
        }

        // Replace the QR code with the framed version
        qrDisplay.innerHTML = '';
        qrDisplay.appendChild(wrapper);

        // Add CSS for the frames
        addFrameStyles(frameColor);
    }

    // Function to add frame styles
    function addFrameStyles(frameColor) {
        // Check if styles already exist
        let styleElement = document.getElementById('qr-frame-styles');
        if (!styleElement) {
            styleElement = document.createElement('style');
            styleElement.id = 'qr-frame-styles';
            document.head.appendChild(styleElement);
        }

        // Add styles for frames
        styleElement.textContent = `
            .qr-frame {
                padding: 20px;
                border-radius: 10px;
                border: 2px solid ${frameColor};
                background-color: white;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                text-align: center;
                max-width: 100%;
            }

            .qr-title {
                font-weight: bold;
                margin-bottom: 10px;
                color: ${frameColor};
                font-size: 16px;
            }

            .qr-guiding-text {
                margin-top: 10px;
                font-size: 14px;
                color: #555;
            }

            .qr-frame-corporate {
                background: linear-gradient(to bottom, #f8f9fa, #e9ecef);
                border-width: 3px;
            }

            .qr-frame-elegant {
                background: linear-gradient(135deg, #f5f5f5 0%, #ffffff 50%, #f5f5f5 100%);
                border-width: 1px;
                box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
            }

            .qr-frame-modern {
                background: white;
                border-radius: 20px;
                border-width: 0;
                box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
            }

            .qr-phone-frame .phone-frame {
                border: 10px solid ${frameColor};
                border-radius: 30px;
                padding: 10px;
                background-color: #000;
                position: relative;
                margin: 0 auto;
            }

            .qr-phone-frame .phone-screen {
                background-color: white;
                border-radius: 20px;
                overflow: hidden;
                padding: 10px;
            }

            .qr-phone-frame .phone-button {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                background-color: ${frameColor};
                margin: 10px auto 0;
            }

            .qr-business-card {
                display: flex;
                background: white;
                border-radius: 10px;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
                padding: 0;
                overflow: hidden;
                width: 350px;
                max-width: 100%;
                margin: 0 auto;
            }

            .qr-business-card .card-content {
                display: flex;
                width: 100%;
            }

            .qr-business-card .card-logo {
                width: 80px;
                background-color: ${frameColor};
                color: white;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 30px;
            }

            .qr-business-card .card-qr {
                flex: 1;
                padding: 15px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .qr-frame-ticket {
                background: white;
                border-radius: 10px;
                border: 2px dashed ${frameColor};
                position: relative;
            }

            .qr-frame-ticket:before,
            .qr-frame-ticket:after {
                content: '';
                position: absolute;
                width: 20px;
                height: 20px;
                background: #f8f9fa;
                border-radius: 50%;
            }

            .qr-frame-ticket:before {
                top: -10px;
                left: 50%;
                transform: translateX(-50%);
            }

            .qr-frame-ticket:after {
                bottom: -10px;
                left: 50%;
                transform: translateX(-50%);
            }
        `;
    }

    // Function to print QR code
    function printQRCode() {
        const printWindow = window.open('', '_blank');
        const qrImage = qrDisplay.innerHTML;
        const qrName = document.querySelector('h1').textContent.trim();

        printWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>Print QR Code - ${qrName}</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        text-align: center;
                        padding: 20px;
                    }
                    .print-container {
                        max-width: 500px;
                        margin: 0 auto;
                    }
                    .qr-title {
                        font-size: 18px;
                        font-weight: bold;
                        margin-bottom: 10px;
                    }
                    @media print {
                        .no-print {
                            display: none;
                        }
                    }
                </style>
            </head>
            <body>
                <div class="print-container">
                    <div class="qr-title">${qrName}</div>
                    ${qrImage}
                    <div class="no-print">
                        <p>Click the button below to print this QR code.</p>
                        <button onclick="window.print()">Print QR Code</button>
                    </div>
                </div>
                <script>
                    // Auto-print
                    setTimeout(function() {
                        window.print();
                    }, 500);
                </script>
            </body>
            </html>
        `);

        printWindow.document.close();
    }

    // Function to download QR code
    function downloadQRCode() {
        qrCode.download({
            extension: 'png'
        });
    }

    // Function to reset customization
    function resetCustomization() {
        // Reset style selects
        if (dotStyleSelect) dotStyleSelect.value = 'square';
        if (cornerStyleSelect) cornerStyleSelect.value = 'square';
        if (cornerDotStyleSelect) cornerDotStyleSelect.value = 'square';
        if (shapeSelect) shapeSelect.value = 'square';

        // Reset color inputs
        if (foregroundColorInput) foregroundColorInput.value = '#000000';
        if (backgroundColorInput) backgroundColorInput.value = '#FFFFFF';
        if (gradientTypeSelect) gradientTypeSelect.value = 'none';
        if (gradientColorInput) gradientColorInput.value = '#4A6CF7';

        // Reset frame inputs
        if (frameStyleSelect) frameStyleSelect.value = 'none';
        if (frameColorInput) frameColorInput.value = '#4A6CF7';
        if (titleInput) titleInput.value = '';
        if (guidingTextInput) guidingTextInput.value = '';
        if (textPositionSelect) textPositionSelect.value = 'below';

        // Reset logo inputs
        if (logoUploadInput) logoUploadInput.value = '';
        if (logoSizeSelect) logoSizeSelect.value = 'medium';
        if (logoShapeSelect) logoShapeSelect.value = 'circle';
        if (logoRemoveBackgroundCheck) logoRemoveBackgroundCheck.checked = false;

        // Reset QR code to original
        qrCode.update({
            width: 300,
            height: 300,
            type: "svg",
            data: qrData.data,
            image: qrData.logo || "",
            dotsOptions: {
                color: qrData.foregroundColor || '#000000',
                type: "square"
            },
            backgroundOptions: {
                color: qrData.backgroundColor || '#FFFFFF',
            },
            cornersSquareOptions: {
                type: "square"
            },
            cornersDotOptions: {
                type: "square"
            },
            imageOptions: {
                crossOrigin: "anonymous",
                margin: 10
            }
        });

        // Clear the display and append reset QR code
        if (qrDisplay) {
            qrDisplay.innerHTML = '';
            qrCode.append(qrDisplay);
        }

        // Show reset message
        const successMsg = document.createElement('div');
        successMsg.className = 'alert alert-info mt-2 mb-0 py-2';
        successMsg.innerHTML = '<i class="fas fa-undo me-2"></i>QR code reset to original!';

        // Add to the customization panel
        const customizationPanel = document.getElementById('customization-panel');
        if (customizationPanel) {
            // Remove any existing success message
            const existingMsg = customizationPanel.querySelector('.alert');
            if (existingMsg) {
                existingMsg.remove();
            }

            // Add the new message
            customizationPanel.appendChild(successMsg);

            // Remove after 3 seconds
            setTimeout(() => {
                successMsg.remove();
            }, 3000);
        }
    }

    // Function to preview logo
    function previewLogo() {
        if (logoUploadInput && logoUploadInput.files && logoUploadInput.files[0]) {
            const file = logoUploadInput.files[0];

            // Check if file is an image
            if (!file.type.match('image.*')) {
                alert('Please select an image file');
                logoUploadInput.value = '';
                return;
            }

            // Create preview
            const reader = new FileReader();
            reader.onload = function(e) {
                // Create preview element if it doesn't exist
                let previewElement = document.getElementById('logo-preview');
                if (!previewElement) {
                    previewElement = document.createElement('div');
                    previewElement.id = 'logo-preview';
                    previewElement.className = 'mt-2 text-center';

                    // Insert after the file input
                    logoUploadInput.parentNode.appendChild(previewElement);
                }

                // Update preview
                previewElement.innerHTML = `
                    <p class="mb-1 text-muted small">Logo Preview:</p>
                    <img src="${e.target.result}" alt="Logo Preview" class="img-thumbnail" style="max-height: 80px;">
                `;
            };

            reader.readAsDataURL(file);
        }
    }
});
