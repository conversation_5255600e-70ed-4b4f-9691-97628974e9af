/**
 * Ad Analytics Dashboard JavaScript
 * 
 * This file contains the code for the ad analytics dashboard, including:
 * - Chart initialization and rendering
 * - Data processing for analytics visualization
 * - Interactive UI elements
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize performance chart
    initPerformanceChart();
    
    // Initialize device distribution chart
    initDeviceChart();
    
    // Initialize location distribution chart
    initLocationChart();
    
    // Set up chart period controls
    setupChartControls();
});

/**
 * Initialize the main performance chart showing impressions and clicks over time
 */
function initPerformanceChart() {
    const ctx = document.getElementById('performanceChart');
    
    if (!ctx) return;
    
    // Get data from the data attributes
    const dates = JSON.parse(ctx.getAttribute('data-dates') || '[]');
    const impressions = JSON.parse(ctx.getAttribute('data-impressions') || '[]');
    const clicks = JSON.parse(ctx.getAttribute('data-clicks') || '[]');
    
    // Create the chart
    const performanceChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: dates,
            datasets: [
                {
                    label: 'Impressions',
                    data: impressions,
                    borderColor: '#1e88e5',
                    backgroundColor: 'rgba(30, 136, 229, 0.1)',
                    borderWidth: 2,
                    tension: 0.4,
                    fill: true
                },
                {
                    label: 'Clicks',
                    data: clicks,
                    borderColor: '#43a047',
                    backgroundColor: 'rgba(67, 160, 71, 0.1)',
                    borderWidth: 2,
                    tension: 0.4,
                    fill: true
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            label += context.parsed.y;
                            return label;
                        }
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    }
                },
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    }
                }
            },
            interaction: {
                mode: 'nearest',
                axis: 'x',
                intersect: false
            }
        }
    });
}

/**
 * Initialize the device distribution chart
 */
function initDeviceChart() {
    const ctx = document.getElementById('deviceChart');
    
    if (!ctx) return;
    
    // Get data from the data attributes
    const devices = JSON.parse(ctx.getAttribute('data-devices') || '[]');
    const counts = JSON.parse(ctx.getAttribute('data-counts') || '[]');
    
    // Create the chart
    const deviceChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: devices,
            datasets: [{
                data: counts,
                backgroundColor: [
                    '#3949ab', // Desktop
                    '#1e88e5', // Mobile
                    '#00acc1', // Tablet
                    '#5e35b1'  // Other
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right',
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.parsed || 0;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = Math.round((value / total) * 100);
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    }
                }
            },
            cutout: '70%'
        }
    });
}

/**
 * Initialize the location distribution chart
 */
function initLocationChart() {
    const ctx = document.getElementById('locationChart');
    
    if (!ctx) return;
    
    // Get data from the data attributes
    const locations = JSON.parse(ctx.getAttribute('data-locations') || '[]');
    const counts = JSON.parse(ctx.getAttribute('data-counts') || '[]');
    
    // Create the chart
    const locationChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: locations,
            datasets: [{
                label: 'Impressions by Location',
                data: counts,
                backgroundColor: '#5e35b1',
                borderRadius: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    }
                },
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    }
                }
            }
        }
    });
}

/**
 * Set up the chart period control buttons
 */
function setupChartControls() {
    const controls = document.querySelectorAll('.chart-control');
    
    if (!controls.length) return;
    
    controls.forEach(control => {
        control.addEventListener('click', function() {
            // Remove active class from all controls
            controls.forEach(c => c.classList.remove('active'));
            
            // Add active class to clicked control
            this.classList.add('active');
            
            // Get the period from the data attribute
            const period = this.getAttribute('data-period');
            
            // Update charts based on the selected period
            updateChartsForPeriod(period);
        });
    });
}

/**
 * Update all charts based on the selected time period
 */
function updateChartsForPeriod(period) {
    // This would typically involve an AJAX call to get new data
    // For now, we'll just simulate a loading state
    
    const charts = document.querySelectorAll('.chart-canvas');
    
    // Show loading state
    charts.forEach(chart => {
        chart.style.opacity = 0.5;
    });
    
    // Simulate loading delay
    setTimeout(() => {
        // Restore charts
        charts.forEach(chart => {
            chart.style.opacity = 1;
        });
        
        // In a real implementation, we would update the chart data here
        console.log(`Charts updated for period: ${period}`);
    }, 800);
}
