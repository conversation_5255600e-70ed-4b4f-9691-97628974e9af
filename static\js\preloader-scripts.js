// Preloader Scripts - EMERGENCY FIX

// EMERGENCY FAILSAFE: Force hide preloader IMMEDIATELY and after 1 second
// Immediate removal
document.addEventListener('DOMContentLoaded', function() {
    console.log("EMERGENCY: Removing preloader immediately");
    const preloader = document.querySelector('.preloader');
    if (preloader) {
        preloader.style.display = 'none';
        document.body.style.overflow = 'auto'; // Enable scrolling
    }
});

// Backup removal after 1 second
setTimeout(function() {
    console.log("EMERGENCY FAILSAFE: Forcing preloader to hide after 1 second");
    const preloader = document.querySelector('.preloader');
    if (preloader) {
        preloader.style.display = 'none';
        document.body.style.overflow = 'auto'; // Enable scrolling
    }
}, 1000);

// Additional backup removal after 2 seconds
setTimeout(function() {
    console.log("FINAL FAILSAFE: Forcing preloader to hide after 2 seconds");
    const preloader = document.querySelector('.preloader');
    if (preloader) {
        preloader.style.display = 'none';
        document.body.style.overflow = 'auto'; // Enable scrolling
    }
}, 2000);

// Ensure preloader is removed when page is fully loaded
window.addEventListener('load', function() {
    console.log("Window load event fired");
    const preloader = document.querySelector('.preloader');
    if (preloader) {
        preloader.style.display = 'none';
        document.body.style.overflow = 'auto'; // Enable scrolling
    }
});

// Function to initialize scroll animations (simplified)
function initScrollAnimations() {
    // Intersection Observer for CTA section
    const ctaSection = document.querySelector('.cta-section');
    if (ctaSection) {
        ctaSection.classList.add('visible');
    }
}
