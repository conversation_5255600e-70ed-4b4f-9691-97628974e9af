{% extends 'base.html' %}
{% load static %}

{% block title %}Campaign Management{% endblock %}

{% block extra_css %}
<!-- Include enterprise common CSS -->
{% include 'ads/includes/enterprise_common_css.html' %}
<link rel="stylesheet" href="{% static 'ads/css/dashboard_enterprise.css' %}">
<link rel="stylesheet" href="{% static 'ads/css/campaign_management.css' %}">
<style>
    /* Additional styles specific to this page */
    .dashboard-card {
        background: linear-gradient(to bottom, #ffffff, #f9f9f9);
    }

    .card-header {
        background: linear-gradient(to right, rgba(26, 35, 126, 0.05), rgba(26, 35, 126, 0.01));
    }

    .table th {
        background: linear-gradient(to right, rgba(26, 35, 126, 0.08), rgba(26, 35, 126, 0.03));
        color: #1a237e;
        font-weight: 600;
    }

    .table td {
        vertical-align: middle;
    }

    .campaign-name {
        color: #1a237e;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .campaign-name:hover {
        color: #3949ab;
        text-decoration: underline;
    }

    .badge {
        padding: 0.5em 0.8em;
        font-weight: 500;
        border-radius: 30px;
    }

    .badge.bg-active {
        background-color: #4caf50;
    }

    .badge.bg-draft {
        background-color: #9e9e9e;
    }

    .badge.bg-completed {
        background-color: #2196f3;
    }

    .progress {
        height: 6px;
        background-color: rgba(0, 0, 0, 0.05);
        border-radius: 3px;
        overflow: hidden;
    }

    .progress-bar {
        background: linear-gradient(to right, #1a237e, #3949ab);
    }
</style>
{% endblock %}

{% block content %}
<!-- Campaign Management Container -->
<div class="enterprise-dashboard campaign-dashboard">
    <!-- Context-aware Header -->
    <div class="dashboard-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="dashboard-welcome">
                        <h1 class="welcome-title">Campaign Management</h1>
                        <p class="welcome-subtitle">Organize and manage your advertising campaigns</p>
                        <a href="{% url 'ads:dashboard' %}" class="btn btn-sm btn-outline-light mt-2">
                            <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
                        </a>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="dashboard-actions">
                        <a href="{% url 'ads:campaign_create' %}" class="btn btn-light">
                            <i class="fas fa-plus me-2"></i> Create New Campaign
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Dashboard Content -->
    <div class="dashboard-content">
        <div class="container-fluid">
            <div class="row">
                <!-- Sidebar Navigation -->
                <div class="col-lg-2 dashboard-sidebar">
                    <div class="sidebar-container">
                        <div class="sidebar-header">
                            <h2 class="sidebar-title">Campaigns</h2>
                        </div>
                        <nav class="sidebar-nav">
                            <ul class="nav-list">
                                <li class="nav-item active">
                                    <a href="{% url 'ads:campaign_list' %}" class="nav-link">
                                        <i class="fas fa-list"></i>
                                        <span>All Campaigns</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{% url 'ads:campaign_list' %}?status=active" class="nav-link">
                                        <i class="fas fa-play-circle"></i>
                                        <span>Active Campaigns</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{% url 'ads:campaign_list' %}?status=draft" class="nav-link">
                                        <i class="fas fa-edit"></i>
                                        <span>Draft Campaigns</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{% url 'ads:campaign_list' %}?status=completed" class="nav-link">
                                        <i class="fas fa-check-circle"></i>
                                        <span>Completed Campaigns</span>
                                    </a>
                                </li>
                                <li class="nav-item nav-divider">
                                    <span class="divider-label">Tools</span>
                                </li>
                                <li class="nav-item">
                                    <a href="{% url 'ads:analytics_enterprise' %}" class="nav-link">
                                        <i class="fas fa-chart-line"></i>
                                        <span>Campaign Analytics</span>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>

                <!-- Main Content Area -->
                <div class="col-lg-10 dashboard-main">
                    <!-- Campaign Stats -->
                    <div class="dashboard-section">
                        <div class="row stats-row">
                            <div class="col-md-3 col-sm-6">
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-bullhorn"></i>
                                    </div>
                                    <div class="stat-value">{{ total_campaigns }}</div>
                                    <div class="stat-label">Total Campaigns</div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="stat-card active">
                                    <div class="stat-icon">
                                        <i class="fas fa-play-circle"></i>
                                    </div>
                                    <div class="stat-value">{{ active_campaigns }}</div>
                                    <div class="stat-label">Active Campaigns</div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="stat-card impressions">
                                    <div class="stat-icon">
                                        <i class="fas fa-eye"></i>
                                    </div>
                                    <div class="stat-value">{{ total_impressions }}</div>
                                    <div class="stat-label">Total Impressions</div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="stat-card clicks">
                                    <div class="stat-icon">
                                        <i class="fas fa-mouse-pointer"></i>
                                    </div>
                                    <div class="stat-value">{{ total_clicks }}</div>
                                    <div class="stat-label">Total Clicks</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Campaign List -->
                    <div class="dashboard-section">
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3 class="card-title">Your Campaigns</h3>
                                <div class="card-actions">
                                    <div class="input-group">
                                        <input type="text" class="form-control" placeholder="Search campaigns..." id="campaign-search">
                                        <button class="btn btn-outline-primary" type="button">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                {% if campaigns %}
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Campaign Name</th>
                                                <th>Status</th>
                                                <th>Ads</th>
                                                <th>Start Date</th>
                                                <th>End Date</th>
                                                <th>Budget</th>
                                                <th>Performance</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for campaign in campaigns %}
                                            <tr>
                                                <td>
                                                    <a href="{% url 'ads:campaign_detail' campaign.slug %}" class="campaign-name">
                                                        {{ campaign.name }}
                                                    </a>
                                                </td>
                                                <td>
                                                    <span class="badge bg-{{ campaign.status }}">
                                                        {{ campaign.get_status_display }}
                                                    </span>
                                                </td>
                                                <td>{{ campaign.ads_count }}</td>
                                                <td>{{ campaign.start_date|date:"M d, Y" }}</td>
                                                <td>{{ campaign.end_date|date:"M d, Y" }}</td>
                                                <td>${{ campaign.budget }}</td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="progress flex-grow-1 me-2" style="height: 6px;">
                                                            <div class="progress-bar" role="progressbar" style="width: {{ campaign.ctr|default:0 }}%;" aria-valuenow="{{ campaign.ctr|default:0 }}" aria-valuemin="0" aria-valuemax="100"></div>
                                                        </div>
                                                        <span>{{ campaign.ctr|default:0|floatformat:1 }}%</span>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="btn-group">
                                                        <a href="{% url 'ads:campaign_detail' campaign.slug %}" class="btn btn-sm btn-outline-primary">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="{% url 'ads:campaign_edit' campaign.slug %}" class="btn btn-sm btn-outline-secondary">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteCampaignModal{{ campaign.id }}">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>

                                                    <!-- Delete Modal -->
                                                    <div class="modal fade" id="deleteCampaignModal{{ campaign.id }}" tabindex="-1" aria-labelledby="deleteCampaignModalLabel{{ campaign.id }}" aria-hidden="true">
                                                        <div class="modal-dialog">
                                                            <div class="modal-content">
                                                                <div class="modal-header">
                                                                    <h5 class="modal-title" id="deleteCampaignModalLabel{{ campaign.id }}">Confirm Deletion</h5>
                                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                </div>
                                                                <div class="modal-body">
                                                                    Are you sure you want to delete the campaign "{{ campaign.name }}"? This action cannot be undone.
                                                                </div>
                                                                <div class="modal-footer">
                                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                                    <form action="{% url 'ads:campaign_delete' campaign.slug %}" method="post">
                                                                        {% csrf_token %}
                                                                        <button type="submit" class="btn btn-danger">Delete</button>
                                                                    </form>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Pagination -->
                                {% if page_obj.has_other_pages %}
                                <nav aria-label="Campaign pagination" class="mt-4">
                                    <ul class="pagination justify-content-center">
                                        {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page=1" aria-label="First">
                                                <span aria-hidden="true">&laquo;&laquo;</span>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}" aria-label="Previous">
                                                <span aria-hidden="true">&laquo;</span>
                                            </a>
                                        </li>
                                        {% endif %}

                                        {% for num in page_obj.paginator.page_range %}
                                            {% if page_obj.number == num %}
                                            <li class="page-item active"><a class="page-link" href="?page={{ num }}">{{ num }}</a></li>
                                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                            <li class="page-item"><a class="page-link" href="?page={{ num }}">{{ num }}</a></li>
                                            {% endif %}
                                        {% endfor %}

                                        {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.next_page_number }}" aria-label="Next">
                                                <span aria-hidden="true">&raquo;</span>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}" aria-label="Last">
                                                <span aria-hidden="true">&raquo;&raquo;</span>
                                            </a>
                                        </li>
                                        {% endif %}
                                    </ul>
                                </nav>
                                {% endif %}

                                {% else %}
                                <div class="empty-state">
                                    <div class="empty-state-icon">
                                        <i class="fas fa-bullhorn"></i>
                                    </div>
                                    <h3 class="empty-state-title">No campaigns yet</h3>
                                    <p class="empty-state-description">Create your first campaign to organize your ads and track their performance together.</p>
                                    <a href="{% url 'ads:campaign_create' %}" class="btn btn-primary">
                                        <i class="fas fa-plus me-2"></i> Create Campaign
                                    </a>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Enterprise Dashboard JS -->
<script src="{% static 'ads/js/dashboard_enterprise.js' %}"></script>
<!-- Campaign Management JS -->
<script src="{% static 'ads/js/campaign_management.js' %}"></script>
{% endblock %}
