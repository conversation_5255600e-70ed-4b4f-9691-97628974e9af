import os
import requests
import json

# Get the Mistral API key from the .env file
with open('.env', 'r') as f:
    for line in f:
        if line.startswith('MISTRAL_API_KEY='):
            api_key = line.strip().split('=')[1]
            break

# Print the API key (first 4 and last 4 characters)
print(f"Using API key: {api_key[:4]}...{api_key[-4:]}")

# Make a request to the Mistral API
url = "https://api.mistral.ai/v1/chat/completions"
headers = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {api_key}"
}
data = {
    "model": "mistral-tiny",
    "messages": [
        {"role": "user", "content": "Hello, how are you?"}
    ]
}

try:
    response = requests.post(url, headers=headers, json=data)
    
    # Print the response status code
    print(f"Response status code: {response.status_code}")
    
    # Print the response content
    if response.status_code == 200:
        print("Response content:")
        print(json.dumps(response.json(), indent=2))
    else:
        print("Error response:")
        print(response.text)
except Exception as e:
    print(f"Error: {str(e)}")
