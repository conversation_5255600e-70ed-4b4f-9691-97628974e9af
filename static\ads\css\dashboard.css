/* Enterprise Dashboard Styles - Elegant, Corporate, Luxury */

/* Global Dashboard Styles */
.dashboard-container {
    padding: 10px 0;
    background-color: #f8f9fa;
    position: relative;
}

.dashboard-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 150px;
    background: linear-gradient(135deg, #1a237e 0%, #283593 100%);
    z-index: 0;
}

/* Dashboard Title Styles */
.dashboard-title {
    color: #ffffff !important;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    font-weight: 700;
    position: relative;
    z-index: 1;
}

.dashboard-subtitle {
    color: rgba(255, 255, 255, 0.9) !important;
    position: relative;
    z-index: 1;
    text-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
}

/* Dashboard Breadcrumb Styles */
.dashboard-breadcrumb {
    position: relative;
    z-index: 1;
}

.dashboard-breadcrumb .breadcrumb-item {
    color: rgba(255, 255, 255, 0.7);
}

.dashboard-breadcrumb .breadcrumb-item.active {
    color: rgba(255, 255, 255, 0.9);
}

.dashboard-breadcrumb .breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.dashboard-breadcrumb .breadcrumb-item a:hover {
    color: #ffffff;
    text-decoration: underline;
}

/* Make breadcrumb separator white */
.dashboard-breadcrumb .breadcrumb-item + .breadcrumb-item::before {
    color: rgba(255, 255, 255, 0.9) !important;
}

.stats-row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10px 30px;
    position: relative;
    z-index: 1;
}

.stat-card {
    flex: 1;
    min-width: 220px;
    margin: 10px;
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.08);
    padding: 30px;
    text-align: center;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(0,0,0,0.05);
}

.stat-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.12);
}

.stat-card::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #1a237e, #3949ab);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.stat-card:hover::after {
    transform: scaleX(1);
}

.stat-card.active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, #1e88e5, #42a5f5);
}

.stat-card.pending::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, #fb8c00, #ffb74d);
}

.stat-card.draft::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, #757575, #bdbdbd);
}

.stat-card.total::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, #3949ab, #5c6bc0);
}

.stat-icon {
    font-size: 28px;
    margin-bottom: 20px;
    color: #3949ab;
    height: 60px;
    width: 60px;
    line-height: 60px;
    border-radius: 50%;
    background-color: rgba(57, 73, 171, 0.1);
    margin: 0 auto 20px;
    transition: all 0.3s ease;
}

.stat-card:hover .stat-icon {
    transform: scale(1.1);
    background-color: rgba(57, 73, 171, 0.15);
}

.stat-card.active .stat-icon {
    color: #1e88e5;
    background-color: rgba(30, 136, 229, 0.1);
}

.stat-card.pending .stat-icon {
    color: #fb8c00;
    background-color: rgba(251, 140, 0, 0.1);
}

.stat-card.draft .stat-icon {
    color: #757575;
    background-color: rgba(117, 117, 117, 0.1);
}

.stat-value {
    font-size: 42px;
    font-weight: 700;
    margin-bottom: 12px;
    color: #212529;
    font-family: 'Montserrat', sans-serif;
    background: linear-gradient(90deg, #1a237e, #3949ab);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    letter-spacing: -1px;
}

.stat-label {
    font-size: 14px;
    color: #5c6bc0;
    text-transform: uppercase;
    letter-spacing: 1.5px;
    font-weight: 600;
    margin-top: 5px;
}

.dashboard-card {
    background-color: #fff;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.05);
    padding: 25px;
    margin-bottom: 25px;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    height: auto !important;
    min-height: auto !important;
    border: 1px solid rgba(0,0,0,0.03);
    position: relative;
    z-index: 1;
    overflow: visible !important;
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, rgba(57, 73, 171, 0.03) 0%, rgba(57, 73, 171, 0) 70%);
    border-radius: 0 0 0 100%;
    z-index: -1;
    transition: all 0.4s ease;
}

.dashboard-card:hover {
    box-shadow: 0 15px 40px rgba(0,0,0,0.08);
    transform: translateY(-5px);
}

.dashboard-card:hover::before {
    width: 150px;
    height: 150px;
    background: linear-gradient(135deg, rgba(57, 73, 171, 0.05) 0%, rgba(57, 73, 171, 0) 70%);
}

.top-ads-card {
    min-height: auto !important;
    height: auto !important;
    display: block;
}

.top-ads-card .table-responsive {
    overflow: visible !important;
    max-height: none !important;
    height: auto !important;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 18px;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    position: relative;
}

.card-header::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #3949ab, #5c6bc0);
    border-radius: 3px;
}

.card-title {
    font-size: 20px;
    font-weight: 700;
    color: #1a237e;
    margin: 0;
    position: relative;
    padding-left: 15px;
}

.card-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 5px;
    height: 20px;
    background: linear-gradient(to bottom, #3949ab, #5c6bc0);
    border-radius: 3px;
}

.card-actions {
    display: flex;
    gap: 12px;
}

.recent-ads-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.recent-ad-item {
    display: flex;
    align-items: center;
    padding: 18px;
    border-bottom: 1px solid rgba(0,0,0,0.04);
    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
    border-radius: 12px;
    margin-bottom: 8px;
}

.recent-ad-item:last-child {
    border-bottom: none;
}

.recent-ad-item:hover {
    background-color: rgba(57, 73, 171, 0.03);
    transform: translateX(8px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.03);
}

.ad-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background-color: rgba(57, 73, 171, 0.08);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 18px;
    color: #3949ab;
    font-size: 20px;
    transition: all 0.3s ease;
}

.recent-ad-item:hover .ad-icon {
    transform: scale(1.1);
    background-color: rgba(57, 73, 171, 0.12);
}

.ad-info {
    flex: 1;
}

.ad-title {
    font-weight: 600;
    color: #1a237e;
    margin-bottom: 6px;
    font-size: 15px;
    transition: all 0.3s ease;
}

.recent-ad-item:hover .ad-title {
    color: #3949ab;
}

.ad-meta {
    display: flex;
    align-items: center;
    font-size: 13px;
    color: #5c6bc0;
}

.ad-date {
    margin-right: 15px;
    display: flex;
    align-items: center;
}

.ad-date i {
    margin-right: 5px;
    font-size: 12px;
    opacity: 0.7;
}

.ad-status {
    display: inline-flex;
    align-items: center;
    padding: 3px 10px;
    border-radius: 30px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-active {
    background-color: rgba(30, 136, 229, 0.1);
    color: #1e88e5;
}

.status-pending {
    background-color: rgba(251, 140, 0, 0.1);
    color: #fb8c00;
}

.status-draft {
    background-color: rgba(117, 117, 117, 0.1);
    color: #757575;
}

.status-rejected {
    background-color: rgba(229, 57, 53, 0.1);
    color: #e53935;
}

.ad-actions {
    margin-left: 15px;
    opacity: 0;
    transform: translateX(-10px);
    transition: all 0.3s ease;
}

.recent-ad-item:hover .ad-actions {
    opacity: 1;
    transform: translateX(0);
}

.performance-metrics {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10px;
}

.metric-card {
    flex: 1;
    min-width: 150px;
    margin: 10px;
    padding: 20px;
    background-color: rgba(57, 73, 171, 0.03);
    border-radius: 12px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(57, 73, 171, 0.05);
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, #3949ab, #5c6bc0);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
}

.metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.05);
}

.metric-card:hover::before {
    transform: scaleX(1);
}

.metric-value {
    font-size: 28px;
    font-weight: 700;
    color: #1a237e;
    margin-bottom: 8px;
    font-family: 'Montserrat', sans-serif;
}

.metric-label {
    font-size: 12px;
    color: #5c6bc0;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 600;
}

.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 25px;
}

.action-card {
    background-color: #fff;
    border-radius: 14px;
    padding: 25px 20px;
    text-align: center;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    text-decoration: none;
    color: #1a237e;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border: 1px solid rgba(57, 73, 171, 0.08);
    box-shadow: 0 8px 20px rgba(0,0,0,0.03);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #3949ab 0%, #5c6bc0 100%);
    opacity: 0;
    z-index: -1;
    transition: opacity 0.4s ease;
}

.action-card:hover {
    color: white;
    transform: translateY(-8px);
    box-shadow: 0 12px 25px rgba(57, 73, 171, 0.2);
    text-decoration: none;
    border-color: transparent;
}

.action-card:hover::before {
    opacity: 1;
}

.action-icon {
    font-size: 28px;
    margin-bottom: 15px;
    height: 60px;
    width: 60px;
    line-height: 60px;
    border-radius: 50%;
    background-color: rgba(57, 73, 171, 0.08);
    color: #3949ab;
    transition: all 0.3s ease;
}

.action-card:hover .action-icon {
    background-color: rgba(255, 255, 255, 0.15);
    color: white;
    transform: scale(1.1);
}

.action-label {
    font-weight: 600;
    font-size: 15px;
    transition: all 0.3s ease;
    letter-spacing: 0.5px;
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    background-color: rgba(57, 73, 171, 0.02);
    border-radius: 16px;
    border: 1px dashed rgba(57, 73, 171, 0.15);
}

.empty-state-icon {
    font-size: 60px;
    color: #5c6bc0;
    margin-bottom: 25px;
    opacity: 0.6;
    height: 80px;
    width: 80px;
    line-height: 80px;
    border-radius: 50%;
    background-color: rgba(57, 73, 171, 0.08);
    margin: 0 auto 25px;
}

.empty-state-title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #1a237e;
}

.empty-state-description {
    color: #5c6bc0;
    max-width: 400px;
    margin: 0 auto 25px;
    line-height: 1.6;
}

.top-ads-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    table-layout: fixed;
    margin-bottom: 20px;
    display: table !important;
    height: auto !important;
}

.top-ads-table th {
    background-color: rgba(57, 73, 171, 0.03);
    color: #1a237e;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 12px;
    letter-spacing: 1px;
    padding: 15px 18px;
    border-bottom: 2px solid rgba(57, 73, 171, 0.1);
    text-align: left;
    position: relative;
}

.top-ads-table th:first-child {
    border-top-left-radius: 12px;
}

.top-ads-table th:last-child {
    border-top-right-radius: 12px;
}

.top-ads-table td {
    padding: 15px 18px;
    border-bottom: 1px solid rgba(57, 73, 171, 0.05);
    vertical-align: middle;
    transition: all 0.3s ease;
    color: #3949ab;
}

.top-ads-table tr:hover td {
    background-color: rgba(57, 73, 171, 0.02);
}

.top-ads-table tr:last-child td {
    border-bottom: none;
}

.top-ads-table tr:last-child td:first-child {
    border-bottom-left-radius: 12px;
}

.top-ads-table tr:last-child td:last-child {
    border-bottom-right-radius: 12px;
}

.create-ad-cta {
    background: linear-gradient(135deg, #3949ab 0%, #5c6bc0 100%);
    color: white;
    border-radius: 16px;
    padding: 35px;
    text-align: center;
    margin-bottom: 30px;
    box-shadow: 0 15px 40px rgba(57, 73, 171, 0.2);
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.create-ad-cta::before {
    content: '';
    position: absolute;
    top: -50px;
    right: -50px;
    width: 150px;
    height: 150px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    z-index: -1;
    transition: all 0.4s ease;
}

.create-ad-cta::after {
    content: '';
    position: absolute;
    bottom: -80px;
    left: -80px;
    width: 200px;
    height: 200px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 50%;
    z-index: -1;
    transition: all 0.4s ease;
}

.create-ad-cta:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 50px rgba(57, 73, 171, 0.3);
}

.create-ad-cta:hover::before {
    transform: scale(1.2);
}

.create-ad-cta:hover::after {
    transform: scale(1.1);
}

.create-ad-title {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 15px;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.create-ad-description {
    margin-bottom: 25px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 16px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

.create-ad-button {
    background-color: white;
    color: #3949ab;
    border: none;
    border-radius: 30px;
    padding: 14px 35px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-block;
    text-decoration: none;
    font-size: 15px;
    letter-spacing: 0.5px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.create-ad-button:hover {
    background-color: #f8f9fa;
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    text-decoration: none;
    color: #3949ab;
}

/* Ad Types Grid */
.ad-types-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 15px;
    margin-top: 10px;
}

.ad-type-card {
    background-color: rgba(57, 73, 171, 0.03);
    border-radius: 12px;
    padding: 15px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(57, 73, 171, 0.08);
}

.ad-type-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.05);
    background-color: rgba(57, 73, 171, 0.06);
}

.ad-type-icon {
    height: 50px;
    width: 50px;
    line-height: 50px;
    border-radius: 50%;
    background-color: rgba(57, 73, 171, 0.1);
    color: #3949ab;
    font-size: 20px;
    margin: 0 auto 12px;
    transition: all 0.3s ease;
}

.ad-type-card:hover .ad-type-icon {
    transform: scale(1.1);
    background-color: rgba(57, 73, 171, 0.15);
}

.ad-type-title {
    font-size: 14px;
    font-weight: 600;
    color: #1a237e;
    margin-bottom: 8px;
}

.ad-type-price {
    font-size: 13px;
    font-weight: 700;
    color: #3949ab;
    background: linear-gradient(90deg, #3949ab, #5c6bc0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* Mobile responsiveness */
@media (max-width: 992px) {
    .dashboard-container::before {
        height: 150px;
    }

    .stat-card {
        min-width: calc(50% - 20px);
    }

    .card-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .card-actions {
        margin-top: 12px;
    }

    .create-ad-cta {
        padding: 25px;
    }

    .create-ad-title {
        font-size: 24px;
    }
}

@media (max-width: 768px) {
    .top-ads-table {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
    }

    .quick-actions {
        grid-template-columns: repeat(2, 1fr);
    }

    .ad-types-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 10px;
    }

    .recent-ad-item {
        padding: 15px;
    }

    .ad-icon {
        width: 40px;
        height: 40px;
        font-size: 18px;
        margin-right: 15px;
    }

    .ad-type-icon {
        height: 40px;
        width: 40px;
        line-height: 40px;
        font-size: 16px;
    }

    .create-ad-description {
        font-size: 14px;
    }
}

@media (max-width: 576px) {
    .dashboard-container::before {
        height: 120px;
    }

    .stat-card {
        min-width: 100%;
        padding: 20px;
    }

    .stat-value {
        font-size: 28px;
    }

    .stat-icon {
        height: 50px;
        width: 50px;
        line-height: 50px;
        font-size: 22px;
    }

    .quick-actions {
        grid-template-columns: 1fr;
    }

    .ad-types-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .ad-type-title {
        font-size: 13px;
    }

    .ad-type-price {
        font-size: 12px;
    }

    .action-icon {
        height: 50px;
        width: 50px;
        line-height: 50px;
    }

    .create-ad-button {
        padding: 12px 25px;
        font-size: 14px;
    }

    .card-title {
        font-size: 18px;
    }

    .empty-state-icon {
        height: 60px;
        width: 60px;
        line-height: 60px;
        font-size: 30px;
    }
}
