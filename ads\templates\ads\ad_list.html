{% extends 'base.html' %}
{% load static %}

{% block title %}My Advertisements{% endblock %}

{% block extra_css %}
<!-- Google Fonts - Poppins and Montserrat -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

<!-- Include common ads CSS -->
{% include 'ads/includes/ads_common_css.html' %}
<link rel="stylesheet" href="{% static 'css/ads-list.css' %}">

<style>
    /* Ultra-Premium Enterprise Ad Listings Styling */
    body {
        background: linear-gradient(135deg, #000000 0%, #0a0a0a 10%, #1a1a2e 25%, #16213e 40%, #0f3460 60%, #533483 80%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        position: relative;
        overflow-x: hidden;
    }

    /* Premium animated background with enterprise patterns */
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 40% 60%, rgba(102, 126, 234, 0.7) 0%, transparent 40%),
            radial-gradient(circle at 60% 40%, rgba(255, 255, 255, 0.3) 0%, transparent 35%),
            radial-gradient(circle at 50% 50%, rgba(118, 75, 162, 0.6) 0%, transparent 45%),
            radial-gradient(circle at 30% 70%, rgba(83, 52, 131, 0.5) 0%, transparent 30%),
            radial-gradient(circle at 70% 30%, rgba(102, 126, 234, 0.4) 0%, transparent 40%);
        z-index: -1;
        animation: enterpriseListFloat 75s ease-in-out infinite;
    }

    @keyframes enterpriseListFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        25% { transform: translateY(-50px) rotate(4deg); }
        50% { transform: translateY(-40px) rotate(-4deg); }
        75% { transform: translateY(-60px) rotate(2deg); }
    }

    /* Enterprise Container */
    .enterprise-container {
        position: relative;
        z-index: 1;
        padding: 2rem 0;
        min-height: 100vh;
    }

    /* Premium Header Section */
    .enterprise-header {
        background: linear-gradient(135deg, rgba(26, 35, 126, 0.95) 0%, rgba(40, 53, 147, 0.95) 50%, rgba(63, 81, 181, 0.95) 100%);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2.5rem;
        margin-bottom: 2rem;
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        position: relative;
        overflow: hidden;
        animation: slideInDown 0.8s ease-out;
    }

    .enterprise-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
        animation: shimmer 3s ease-in-out infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    .enterprise-title {
        font-family: 'Montserrat', sans-serif !important;
        font-size: 2.5rem;
        font-weight: 700;
        color: #ffffff;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 2;
    }

    .enterprise-subtitle {
        font-size: 1.1rem;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 0;
        font-weight: 400;
        position: relative;
        z-index: 2;
    }

    .enterprise-breadcrumb {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        margin-bottom: 1.5rem;
        position: relative;
        z-index: 2;
    }

    .enterprise-breadcrumb .breadcrumb {
        margin: 0;
        background: transparent;
        padding: 0;
    }

    .enterprise-breadcrumb .breadcrumb-item a {
        color: rgba(255, 255, 255, 0.8);
        text-decoration: none;
        font-weight: 500;
    }

    .enterprise-breadcrumb .breadcrumb-item a:hover {
        color: white;
    }

    .enterprise-breadcrumb .breadcrumb-item.active {
        color: white;
        font-weight: 600;
    }

    /* Premium Action Section */
    .enterprise-actions {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        overflow: hidden;
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.15),
            0 0 0 1px rgba(255, 255, 255, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        animation: slideInUp 0.8s ease-out 0.2s both;
        margin-bottom: 2rem;
        padding: 2rem;
    }

    .enterprise-action-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 12px;
        padding: 0.8rem 2rem;
        color: white;
        font-weight: 600;
        font-size: 1rem;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        position: relative;
        overflow: hidden;
        cursor: pointer;
    }

    .enterprise-action-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .enterprise-action-btn:hover::before {
        left: 100%;
    }

    .enterprise-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    .enterprise-search {
        position: relative;
    }

    .enterprise-search .form-control {
        border: 2px solid rgba(102, 126, 234, 0.2);
        border-radius: 12px;
        padding: 0.875rem 1.25rem;
        font-weight: 500;
        background: rgba(255, 255, 255, 0.9);
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    }

    .enterprise-search .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 8px 25px rgba(0, 0, 0, 0.1);
        outline: none;
        background: rgba(255, 255, 255, 1);
    }

    .enterprise-search .btn {
        border: 2px solid rgba(102, 126, 234, 0.2);
        border-radius: 0 12px 12px 0;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        color: #667eea;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .enterprise-search .btn:hover {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-color: #667eea;
    }

    /* Premium Enterprise Cards */
    .enterprise-card {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        overflow: hidden;
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.15),
            0 0 0 1px rgba(255, 255, 255, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        animation: slideInUp 0.8s ease-out 0.4s both;
        margin-bottom: 2rem;
        transition: all 0.3s ease;
    }

    .enterprise-card:hover {
        transform: translateY(-5px);
        box-shadow:
            0 35px 70px rgba(0, 0, 0, 0.2),
            0 0 0 1px rgba(255, 255, 255, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.4);
    }

    .enterprise-card-header {
        background: linear-gradient(135deg, #1a237e 0%, #283593 100%);
        color: white;
        padding: 1.5rem 2rem;
        font-weight: 600;
        font-size: 1.2rem;
        position: relative;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .enterprise-card-header::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    }

    .enterprise-card-title {
        font-family: 'Montserrat', sans-serif !important;
        font-weight: 700;
        margin: 0;
        position: relative;
        z-index: 2;
    }

    .enterprise-card-body {
        padding: 2rem;
    }

    /* Premium Form Elements */
    .form-select {
        border: 2px solid rgba(102, 126, 234, 0.2);
        border-radius: 12px;
        padding: 0.875rem 1.25rem;
        font-weight: 500;
        background: rgba(255, 255, 255, 0.9);
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    }

    .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 8px 25px rgba(0, 0, 0, 0.1);
        outline: none;
        background: rgba(255, 255, 255, 1);
    }

    /* Premium Table Styling */
    .enterprise-table {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border: none;
    }

    .enterprise-table thead th {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 0.85rem;
        color: #1a237e;
        border: none;
        padding: 1.2rem 1.5rem;
    }

    .enterprise-table tbody td {
        padding: 1.2rem 1.5rem;
        border: none;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        vertical-align: middle;
        font-weight: 500;
    }

    .enterprise-table tbody tr:hover {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    }

    /* Premium Badges */
    .badge {
        padding: 0.6rem 1.2rem;
        border-radius: 12px;
        font-weight: 600;
        letter-spacing: 0.5px;
        text-transform: uppercase;
        font-size: 0.75rem;
    }

    /* Premium Buttons */
    .btn-group .btn {
        border-radius: 8px;
        margin-right: 0.25rem;
        font-weight: 600;
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }

    .btn-group .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-color: #667eea;
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        border-color: #5a67d8;
    }

    /* Premium Empty State */
    .enterprise-empty-state {
        text-align: center;
        padding: 4rem 2rem;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
        border-radius: 16px;
        margin: 2rem 0;
    }

    .enterprise-empty-state h4 {
        color: #1a237e;
        font-weight: 700;
        margin-bottom: 1rem;
    }

    .enterprise-empty-state p {
        color: #667eea;
        font-weight: 500;
        margin-bottom: 2rem;
    }

    /* Premium Pagination */
    .pagination .page-link {
        border: 2px solid rgba(102, 126, 234, 0.2);
        border-radius: 8px;
        margin: 0 0.25rem;
        padding: 0.75rem 1rem;
        color: #667eea;
        font-weight: 600;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.9);
    }

    .pagination .page-link:hover {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        border-color: rgba(102, 126, 234, 0.4);
        color: #667eea;
        transform: translateY(-2px);
    }

    .pagination .page-item.active .page-link {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-color: #667eea;
        color: white;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .enterprise-title {
            font-size: 2rem;
        }

        .enterprise-header {
            padding: 2rem 1.5rem;
        }

        .enterprise-actions {
            padding: 1.5rem;
        }

        .enterprise-card-body {
            padding: 1.5rem;
        }
    }

    @media (max-width: 576px) {
        .enterprise-title {
            font-size: 1.8rem;
        }

        .enterprise-header {
            padding: 1.5rem 1rem;
        }

        .enterprise-actions {
            padding: 1rem;
        }

        .enterprise-card-body {
            padding: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="enterprise-container">
    <div class="container">
        <!-- Premium Header Section -->
        <div class="enterprise-header">
            <nav aria-label="breadcrumb" class="enterprise-breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'index' %}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'ads:dashboard' %}">Dashboard</a></li>
                    <li class="breadcrumb-item active" aria-current="page">My Advertisements</li>
                </ol>
            </nav>

            <div class="text-center">
                <h1 class="enterprise-title">My Advertisements</h1>
                <p class="enterprise-subtitle">Manage and monitor all your advertising campaigns with enterprise-grade tools</p>
            </div>
        </div>

        <!-- Premium Action Section -->
        <div class="enterprise-actions">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <a href="{% url 'ads:ad_create_consolidated' %}" class="enterprise-action-btn">
                        <i class="fas fa-plus-circle"></i> Create New Ad
                    </a>
                </div>
                <div class="col-md-6">
                    <div class="enterprise-search">
                        <div class="input-group">
                            <input type="text" class="form-control" id="adSearch" placeholder="Search advertisements...">
                            <button class="btn" type="button" id="searchButton">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Premium Filters -->
        <div class="enterprise-card">
            <div class="enterprise-card-header">
                <h3 class="enterprise-card-title">
                    <i class="fas fa-filter me-2"></i>Filters
                </h3>
            </div>
            <div class="enterprise-card-body">
                <form method="get">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <select class="form-select" id="statusFilter" name="status">
                                <option value="">All Statuses</option>
                                <option value="draft" {% if request.GET.status == 'draft' %}selected{% endif %}>Draft</option>
                                <option value="pending" {% if request.GET.status == 'pending' %}selected{% endif %}>Pending Approval</option>
                                <option value="approved" {% if request.GET.status == 'approved' %}selected{% endif %}>Approved</option>
                                <option value="active" {% if request.GET.status == 'active' %}selected{% endif %}>Active</option>
                                <option value="rejected" {% if request.GET.status == 'rejected' %}selected{% endif %}>Rejected</option>
                                <option value="expired" {% if request.GET.status == 'expired' %}selected{% endif %}>Expired</option>
                                <option value="paused" {% if request.GET.status == 'paused' %}selected{% endif %}>Paused</option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-2">
                            <select class="form-select" id="typeFilter" name="ad_type">
                                <option value="">All Types</option>
                                {% for ad_type in ad_types %}
                                <option value="{{ ad_type.id }}" {% if request.GET.ad_type == ad_type.id|stringformat:"i" %}selected{% endif %}>{{ ad_type.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3 mb-2">
                            <select class="form-select" id="dateFilter" name="date_range">
                                <option value="">All Dates</option>
                                <option value="today" {% if request.GET.date_range == 'today' %}selected{% endif %}>Today</option>
                                <option value="week" {% if request.GET.date_range == 'week' %}selected{% endif %}>This Week</option>
                                <option value="month" {% if request.GET.date_range == 'month' %}selected{% endif %}>This Month</option>
                                <option value="year" {% if request.GET.date_range == 'year' %}selected{% endif %}>This Year</option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button type="submit" class="enterprise-action-btn w-100">Apply Filters</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Premium Ad List -->
        <div class="enterprise-card">
            <div class="enterprise-card-header">
                <h3 class="enterprise-card-title">
                    {% if filter_context.status or filter_context.ad_type or filter_context.date_range %}
                        <i class="fas fa-filter me-2"></i>Filtered Advertisements
                    {% else %}
                        <i class="fas fa-bullhorn me-2"></i>Your Advertisements
                    {% endif %}
                </h3>
                <span class="badge bg-primary">{{ total_count }} Ads</span>
            </div>

                {% if filter_context.status or filter_context.ad_type or filter_context.date_range %}
                <div class="card-header bg-light">
                    <div class="d-flex flex-wrap align-items-center">
                        <span class="me-2">Active filters:</span>
                        {% if filter_context.status %}
                            <span class="badge bg-secondary me-2 mb-1">
                                Status: {{ filter_context.status|title }}
                                <a href="?{% for key, value in request.GET.items %}{% if key != 'status' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="text-white ms-1" title="Remove filter"><i class="fas fa-times"></i></a>
                            </span>
                        {% endif %}
                        {% if filter_context.ad_type %}
                            <span class="badge bg-secondary me-2 mb-1">
                                Type: {{ filter_context.ad_type_name }}
                                <a href="?{% for key, value in request.GET.items %}{% if key != 'ad_type' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="text-white ms-1" title="Remove filter"><i class="fas fa-times"></i></a>
                            </span>
                        {% endif %}
                        {% if filter_context.date_range %}
                            <span class="badge bg-secondary me-2 mb-1">
                                Date:
                                {% if filter_context.date_range == 'today' %}Today
                                {% elif filter_context.date_range == 'week' %}This Week
                                {% elif filter_context.date_range == 'month' %}This Month
                                {% elif filter_context.date_range == 'year' %}This Year
                                {% endif %}
                                <a href="?{% for key, value in request.GET.items %}{% if key != 'date_range' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="text-white ms-1" title="Remove filter"><i class="fas fa-times"></i></a>
                            </span>
                        {% endif %}
                        <a href="{% url 'ads:ad_list' %}" class="btn btn-sm btn-outline-secondary ms-auto">Clear All Filters</a>
                    </div>
                </div>
                {% endif %}

            <div class="enterprise-card-body">
                {% if page_obj %}
                <div class="table-responsive">
                    <table class="enterprise-table table" id="adsTable">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Type</th>
                                <th>Status</th>
                                <th>Duration</th>
                                <th>Impressions</th>
                                <th>Clicks</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for ad in page_obj %}
                            <tr>
                                <td>{{ ad.title }}</td>
                                <td>{{ ad.ad_type.name }}</td>
                                <td>
                                    <span class="badge
                                        {% if ad.status == 'active' %}bg-success
                                        {% elif ad.status == 'pending' %}bg-warning
                                        {% elif ad.status == 'rejected' %}bg-danger
                                        {% elif ad.status == 'expired' %}bg-secondary
                                        {% elif ad.status == 'paused' %}bg-info
                                        {% else %}bg-secondary{% endif %}">
                                        {{ ad.get_status_display }}
                                    </span>
                                </td>
                                <td>{{ ad.start_date|date:"M d" }} - {{ ad.end_date|date:"M d, Y" }}</td>
                                <td>{{ ad.impressions }}</td>
                                <td>{{ ad.clicks }}</td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{% url 'ads:ad_detail' ad.slug %}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if ad.status == 'draft' or ad.status == 'rejected' %}
                                        <a href="{% url 'ads:ad_edit' ad.slug %}" class="btn btn-sm btn-warning">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        {% endif %}
                                        {% if ad.status == 'draft' %}
                                        <a href="{% url 'ads:ad_submit' ad.slug %}" class="btn btn-sm btn-success">
                                            <i class="fas fa-paper-plane"></i>
                                        </a>
                                        {% endif %}
                                        {% if ad.status == 'active' %}
                                        <a href="{% url 'ads:ad_pause' ad.slug %}" class="btn btn-sm btn-info">
                                            <i class="fas fa-pause"></i>
                                        </a>
                                        {% endif %}
                                        {% if ad.status == 'paused' %}
                                        <a href="{% url 'ads:ad_resume' ad.slug %}" class="btn btn-sm btn-success">
                                            <i class="fas fa-play"></i>
                                        </a>
                                        {% endif %}
                                        <a href="{% url 'ads:ad_delete' ad.slug %}" class="btn btn-sm btn-danger">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="enterprise-empty-state">
                    {% if filter_context.status %}
                        <h4>You don't have any {{ filter_context.status }} advertisements</h4>
                        {% if filter_context.status == 'draft' %}
                            <p>Create a new ad to start drafting your campaign</p>
                        {% elif filter_context.status == 'active' %}
                            <p>Activate your ads to start promoting your business</p>
                        {% elif filter_context.status == 'paused' %}
                            <p>You haven't paused any advertisements yet</p>
                        {% elif filter_context.status == 'pending' %}
                            <p>You don't have any ads waiting for approval</p>
                        {% elif filter_context.status == 'rejected' %}
                            <p>None of your ads have been rejected</p>
                        {% elif filter_context.status == 'expired' %}
                            <p>You don't have any expired advertisements</p>
                        {% else %}
                            <p>No advertisements match your filter criteria</p>
                        {% endif %}
                    {% elif filter_context.ad_type %}
                        <h4>You don't have any {{ filter_context.ad_type_name }} advertisements</h4>
                        <p>Create a new ad of this type to start promoting your business</p>
                    {% elif filter_context.date_range %}
                        <h4>No advertisements found in the selected time period</h4>
                        <p>
                            {% if filter_context.date_range == 'today' %}
                                You haven't created any ads today
                            {% elif filter_context.date_range == 'week' %}
                                You haven't created any ads this week
                            {% elif filter_context.date_range == 'month' %}
                                You haven't created any ads this month
                            {% elif filter_context.date_range == 'year' %}
                                You haven't created any ads this year
                            {% endif %}
                        </p>
                    {% else %}
                        <h4>You don't have any advertisements yet</h4>
                        <p>Create your first ad to start promoting your business</p>
                    {% endif %}

                    <div class="mt-3">
                        {% if filter_context.status or filter_context.ad_type or filter_context.date_range %}
                            <a href="{% url 'ads:ad_list' %}" class="btn btn-secondary btn-lg me-2">
                                <i class="fas fa-filter"></i> Clear Filters
                            </a>
                        {% endif %}
                        <a href="{% url 'ads:ad_create_consolidated' %}" class="enterprise-action-btn btn-lg">
                            <i class="fas fa-plus-circle"></i> Create New Ad
                        </a>
                    </div>
                </div>
                {% endif %}
            </div>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="row mt-4">
        <div class="col-12">
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link" aria-hidden="true">&laquo;</span>
                    </li>
                    {% endif %}

                    {% for i in page_obj.paginator.page_range %}
                        {% if page_obj.number == i %}
                        <li class="page-item active">
                            <span class="page-link">{{ i }}</span>
                        </li>
                        {% else %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ i }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ i }}</a>
                        </li>
                        {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link" aria-hidden="true">&raquo;</span>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/ads-list.js' %}"></script>

{% endblock %}
