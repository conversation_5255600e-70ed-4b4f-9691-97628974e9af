/**
 * touch-interaction.js
 * 
 * Enhances touch interactions across the site for better mobile experience.
 * Implements touch-friendly improvements and fixes common mobile interaction issues.
 */

(function() {
    // Wait for DOM to be ready
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize touch interaction enhancements
        initTouchInteractions();
    });

    /**
     * Initialize touch interaction enhancements
     */
    function initTouchInteractions() {
        // Enhance touch targets
        enhanceTouchTargets();
        
        // Fix double-tap issues
        fixDoubleTapIssues();
        
        // Improve form interactions
        improveFormInteractions();
        
        // Enhance scrolling experience
        enhanceScrolling();
        
        // Fix tap delay
        fixTapDelay();
    }

    /**
     * Enhance touch targets to ensure they meet minimum size requirements
     */
    function enhanceTouchTargets() {
        // Find all interactive elements
        const interactiveElements = document.querySelectorAll('a, button, input, select, textarea, [role="button"]');
        
        interactiveElements.forEach(element => {
            // Skip elements that are already large enough
            const rect = element.getBoundingClientRect();
            if (rect.width >= 44 && rect.height >= 44) return;
            
            // Add touch-friendly class
            element.classList.add('touch-friendly');
            
            // For very small elements, add padding or increase size
            if (rect.width < 30 || rect.height < 30) {
                element.classList.add('touch-friendly-enhanced');
            }
        });
    }

    /**
     * Fix double-tap issues on iOS
     */
    function fixDoubleTapIssues() {
        // Find all links
        const links = document.querySelectorAll('a');
        
        links.forEach(link => {
            // Skip links with no href
            if (!link.hasAttribute('href')) return;
            
            // Add touch-action CSS property
            link.style.touchAction = 'manipulation';
            
            // For links with dropdown functionality, prevent default on first tap
            if (link.classList.contains('has-dropdown') || 
                link.parentElement.classList.contains('has-dropdown')) {
                
                link.addEventListener('touchend', function(e) {
                    // If this is a dropdown toggle, handle it specially
                    if (this.getAttribute('aria-expanded') === 'false') {
                        e.preventDefault();
                    }
                });
            }
        });
    }

    /**
     * Improve form interactions on mobile
     */
    function improveFormInteractions() {
        // Find all form elements
        const formElements = document.querySelectorAll('input, select, textarea');
        
        formElements.forEach(element => {
            // Ensure proper font size to prevent zoom
            if (getComputedStyle(element).fontSize.replace('px', '') < 16) {
                element.style.fontSize = '16px';
            }
            
            // Improve focus states
            element.addEventListener('focus', function() {
                this.classList.add('touch-focus');
            });
            
            element.addEventListener('blur', function() {
                this.classList.remove('touch-focus');
            });
            
            // For select elements, ensure they're easy to use
            if (element.tagName === 'SELECT') {
                element.classList.add('touch-select');
            }
        });
        
        // Improve label tap targets
        const labels = document.querySelectorAll('label');
        labels.forEach(label => {
            label.classList.add('touch-label');
        });
    }

    /**
     * Enhance scrolling experience on mobile
     */
    function enhanceScrolling() {
        // Find all scrollable elements
        const scrollableElements = document.querySelectorAll('.scrollable, [data-scrollable]');
        
        scrollableElements.forEach(element => {
            // Add momentum scrolling
            element.style.webkitOverflowScrolling = 'touch';
            element.style.overflowScrolling = 'touch';
            
            // Ensure proper overflow
            if (getComputedStyle(element).overflow === 'visible') {
                element.style.overflow = 'auto';
            }
        });
        
        // Improve table scrolling
        const tables = document.querySelectorAll('table');
        tables.forEach(table => {
            // Skip tables that are already in a scrollable container
            if (table.closest('.table-responsive')) return;
            
            // Wrap table in scrollable container if needed
            if (table.offsetWidth > table.parentElement.offsetWidth) {
                const wrapper = document.createElement('div');
                wrapper.className = 'table-responsive';
                wrapper.style.overflowX = 'auto';
                wrapper.style.webkitOverflowScrolling = 'touch';
                table.parentNode.insertBefore(wrapper, table);
                wrapper.appendChild(table);
            }
        });
    }

    /**
     * Fix tap delay on mobile browsers
     */
    function fixTapDelay() {
        // Add touch-action: manipulation to body
        document.body.style.touchAction = 'manipulation';
        
        // For older browsers, use FastClick if available
        if (typeof FastClick !== 'undefined') {
            FastClick.attach(document.body);
        }
    }

    /**
     * Add swipe gesture support for common actions
     * @param {HTMLElement} element Element to add swipe support to
     * @param {Object} options Swipe options
     */
    function addSwipeSupport(element, options = {}) {
        let touchStartX = 0;
        let touchStartY = 0;
        let touchEndX = 0;
        let touchEndY = 0;
        
        const defaults = {
            threshold: 50, // Minimum distance for a swipe
            restraint: 100, // Maximum perpendicular distance allowed
            allowedTime: 300, // Maximum time allowed for the swipe
            onSwipeLeft: null,
            onSwipeRight: null,
            onSwipeUp: null,
            onSwipeDown: null
        };
        
        const settings = Object.assign({}, defaults, options);
        
        element.addEventListener('touchstart', function(e) {
            const touchObj = e.changedTouches[0];
            touchStartX = touchObj.pageX;
            touchStartY = touchObj.pageY;
            startTime = new Date().getTime();
            e.preventDefault();
        }, false);
        
        element.addEventListener('touchend', function(e) {
            const touchObj = e.changedTouches[0];
            touchEndX = touchObj.pageX;
            touchEndY = touchObj.pageY;
            
            const distX = touchEndX - touchStartX;
            const distY = touchEndY - touchStartY;
            const elapsedTime = new Date().getTime() - startTime;
            
            if (elapsedTime <= settings.allowedTime) {
                if (Math.abs(distX) >= settings.threshold && Math.abs(distY) <= settings.restraint) {
                    // Horizontal swipe
                    if (distX > 0 && settings.onSwipeRight) {
                        settings.onSwipeRight(e);
                    } else if (distX < 0 && settings.onSwipeLeft) {
                        settings.onSwipeLeft(e);
                    }
                } else if (Math.abs(distY) >= settings.threshold && Math.abs(distX) <= settings.restraint) {
                    // Vertical swipe
                    if (distY > 0 && settings.onSwipeDown) {
                        settings.onSwipeDown(e);
                    } else if (distY < 0 && settings.onSwipeUp) {
                        settings.onSwipeUp(e);
                    }
                }
            }
            
            e.preventDefault();
        }, false);
    }

    // Expose public methods
    window.TouchInteraction = {
        addSwipeSupport: addSwipeSupport
    };
})();
