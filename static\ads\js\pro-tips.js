/**
 * Pro Tips JavaScript
 * Handles pro tips collapse functionality for ad creation/edit forms
 * This is a standalone version for pages that don't use the consolidated script
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Pro Tips: Initializing...');

    const proTipsContent = document.getElementById('proTipsContent') || document.getElementById('tipsCollapse');
    const proTipsHeader = document.getElementById('proTipsHeader');

    if (!proTipsContent) {
        console.warn('Pro Tips: Content element not found');
        return;
    }

    // Check if Bootstrap is available
    if (typeof bootstrap === 'undefined' || typeof bootstrap.Collapse === 'undefined') {
        console.warn('Pro Tips: Bootstrap Collapse not available, using fallback');
        initializeProTipsFallback();
        return;
    }

    try {
        // Initialize Bootstrap collapse
        const bsCollapse = new bootstrap.Collapse(proTipsContent, {
            toggle: false
        });

        // Set initial state based on screen size
        if (window.innerWidth >= 768) {
            // Desktop: show by default
            proTipsContent.classList.add('show');
            updateChevronIcon('up');
            console.log('Pro Tips: Shown on desktop');
        } else {
            // Mobile: hide by default
            proTipsContent.classList.remove('show');
            updateChevronIcon('down');
            console.log('Pro Tips: Collapsed on mobile');
        }

        // Add click event to header (not the button)
        if (proTipsHeader) {
            proTipsHeader.addEventListener('click', function(e) {
                // Only handle clicks on the header itself, not on the toggle button
                if (!e.target.closest('.btn-link') && !e.target.closest('button')) {
                    console.log('Pro Tips: Header clicked');

                    // Toggle the collapse
                    if (proTipsContent.classList.contains('show')) {
                        bsCollapse.hide();
                    } else {
                        bsCollapse.show();
                    }

                    // Toggle chevron icon
                    toggleChevronIcon();
                }
            });
        }

        // Listen for collapse events
        proTipsContent.addEventListener('shown.bs.collapse', function() {
            console.log('Pro Tips: Shown');
            updateChevronIcon('up');
        });

        proTipsContent.addEventListener('hidden.bs.collapse', function() {
            console.log('Pro Tips: Hidden');
            updateChevronIcon('down');
        });

    } catch (error) {
        console.error('Pro Tips: Error initializing:', error);
        initializeProTipsFallback();
    }

    /**
     * Fallback pro tips functionality without Bootstrap
     */
    function initializeProTipsFallback() {
        if (proTipsHeader) {
            proTipsHeader.addEventListener('click', function(e) {
                if (!e.target.closest('.btn-link') && !e.target.closest('button')) {
                    proTipsContent.classList.toggle('show');
                    toggleChevronIcon();
                }
            });
        }
    }

    /**
     * Toggle chevron icon
     */
    function toggleChevronIcon() {
        const chevron = proTipsHeader?.querySelector('.fa-chevron-down, .fa-chevron-up');

        if (chevron) {
            chevron.classList.toggle('fa-chevron-down');
            chevron.classList.toggle('fa-chevron-up');
        }
    }

    /**
     * Update chevron icon to specific direction
     */
    function updateChevronIcon(direction) {
        const chevron = proTipsHeader?.querySelector('.fa-chevron-down, .fa-chevron-up');

        if (chevron) {
            chevron.classList.remove('fa-chevron-down', 'fa-chevron-up');
            chevron.classList.add(`fa-chevron-${direction}`);
        }
    }

    console.log('Pro Tips: Initialized successfully');
});
