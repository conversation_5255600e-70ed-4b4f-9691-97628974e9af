"""
Signal handlers for QR code scans to generate real-time notifications
"""
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.urls import reverse
from django.utils import timezone
from django.db.models import F

from .models import QRCodeScan, QRCodeAnalytics
from notifications.services import NotificationService

@receiver(post_save, sender=QRCodeScan)
def qrcode_scanned(sender, instance, created, **kwargs):
    """
    Send notification when a QR code is scanned
    """
    if created:
        # Get the QR code
        qr_code = instance.qr_code
        
        # Get or create analytics for this QR code
        analytics, _ = QRCodeAnalytics.objects.get_or_create(qr_code=qr_code)
        
        # Update analytics
        QRCodeAnalytics.objects.filter(qr_code=qr_code).update(
            total_scans=F('total_scans') + 1,
            last_scanned=timezone.now()
        )
        
        # Create notification for the QR code owner
        NotificationService.create_notification(
            user=qr_code.user,
            title="QR Code Scanned",
            message=f"Your QR code '{qr_code.name}' was just scanned.",
            notification_type="info",
            category="qr_code",
            content_object=qr_code,
            action_url=reverse('qr_code_detail', kwargs={'pk': qr_code.pk})
        )
        
        # If this is a milestone scan (e.g., 10th, 50th, 100th), send a special notification
        if analytics.total_scans in [10, 50, 100, 500, 1000]:
            NotificationService.create_notification(
                user=qr_code.user,
                title=f"Milestone: {analytics.total_scans} Scans!",
                message=f"Congratulations! Your QR code '{qr_code.name}' has reached {analytics.total_scans} scans.",
                notification_type="success",
                category="qr_code",
                content_object=qr_code,
                action_url=reverse('qr_code_detail', kwargs={'pk': qr_code.pk})
            )

# Function to simulate a QR code scan for testing
def simulate_qr_code_scan(qr_code, ip_address="127.0.0.1", user_agent="Test Browser", location="Test Location"):
    """
    Simulate a QR code scan for testing purposes
    
    Args:
        qr_code: The QR code to simulate a scan for
        ip_address: IP address of the scanner
        user_agent: User agent of the scanner
        location: Location of the scanner
        
    Returns:
        QRCodeScan: The created scan object
    """
    # Create a new scan
    scan = QRCodeScan.objects.create(
        qr_code=qr_code,
        ip_address=ip_address,
        user_agent=user_agent,
        location=location,
        device_type="Test Device",
        os="Test OS",
        browser="Test Browser"
    )
    
    return scan
