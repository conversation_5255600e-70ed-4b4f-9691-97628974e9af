/**
 * Ad Preview Location JavaScript
 * Handles location-specific preview styling for ad creation/edit forms
 * This is a standalone version for pages that don't use the consolidated script
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Ad Preview Location: Initializing...');

    const previewLocationType = document.getElementById('previewLocationType');
    const previewWrapper = document.getElementById('previewWrapper');
    const previewLocationLabel = document.getElementById('previewLocationLabel');
    const previewSizeInfo = document.getElementById('previewSizeInfo');
    const adLocation = document.getElementById('ad_location');

    // Update preview location type based on selected location
    function updatePreviewLocationType(locationText) {
        if (previewLocationType) {
            const text = locationText.toLowerCase();
            if (text.includes('header')) previewLocationType.value = 'header';
            else if (text.includes('sidebar')) previewLocationType.value = 'sidebar';
            else if (text.includes('content')) previewLocationType.value = 'content';
            else if (text.includes('footer')) previewLocationType.value = 'footer';
            updateLocationPreview();
        }
    }

    // Update location preview styling
    function updateLocationPreview() {
        if (!previewLocationType || !previewWrapper) return;
        
        const locationType = previewLocationType.value;
        previewWrapper.className = 'ad-preview-wrapper';
        
        switch(locationType) {
            case 'header':
                previewWrapper.classList.add('ad-preview-header');
                if (previewLocationLabel) previewLocationLabel.textContent = 'Header Ad Preview';
                if (previewSizeInfo) previewSizeInfo.textContent = 'Header size: 728x90px';
                break;
            case 'sidebar':
                previewWrapper.classList.add('ad-preview-sidebar');
                if (previewLocationLabel) previewLocationLabel.textContent = 'Sidebar Ad Preview';
                if (previewSizeInfo) previewSizeInfo.textContent = 'Sidebar size: 300x250px';
                break;
            case 'content':
                previewWrapper.classList.add('ad-preview-content');
                if (previewLocationLabel) previewLocationLabel.textContent = 'In-Content Ad Preview';
                if (previewSizeInfo) previewSizeInfo.textContent = 'Content size: 468x60px';
                break;
            case 'footer':
                previewWrapper.classList.add('ad-preview-footer');
                if (previewLocationLabel) previewLocationLabel.textContent = 'Footer Ad Preview';
                if (previewSizeInfo) previewSizeInfo.textContent = 'Footer size: 970x90px';
                break;
            default:
                if (previewLocationLabel) previewLocationLabel.textContent = 'Default Ad Preview';
                if (previewSizeInfo) previewSizeInfo.textContent = 'Standard size: 300x250px';
        }
    }

    // Event listeners
    if (previewLocationType) {
        previewLocationType.addEventListener('change', updateLocationPreview);
    }

    if (adLocation) {
        adLocation.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            updatePreviewLocationType(selectedOption.text);
        });
    }

    // Initialize
    if (previewLocationType) {
        updateLocationPreview();
    }

    console.log('Ad Preview Location: Initialized successfully');
});
