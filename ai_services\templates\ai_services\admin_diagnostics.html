{% extends 'admin/base_site.html' %}
{% load static %}

{% block title %}AI Engine Diagnostics{% endblock %}

{% block extrastyle %}
<style>
    .diagnostics-card {
        margin-bottom: 20px;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .diagnostics-card .card-header {
        padding: 15px;
        font-weight: bold;
    }

    .diagnostics-card .card-body {
        padding: 20px;
    }

    .provider-details {
        margin-top: 15px;
    }

    .provider-details dt {
        font-weight: 500;
    }

    .provider-details dd {
        margin-bottom: 10px;
    }

    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
    }

    .status-indicator.online {
        background-color: #28a745;
    }

    .status-indicator.offline {
        background-color: #dc3545;
    }

    .status-indicator.unknown {
        background-color: #ffc107;
    }

    .usage-stats {
        margin-top: 20px;
    }

    .usage-stats .progress {
        height: 20px;
        margin-bottom: 10px;
    }

    .usage-stats .progress-bar {
        font-size: 12px;
        line-height: 20px;
    }

    .cache-stats {
        margin-top: 20px;
    }

    .cache-stats .list-group-item {
        display: flex;
        justify-content: space-between;
    }

    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 60px;
        height: 34px;
    }

    .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .toggle-slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 34px;
    }

    .toggle-slider:before {
        position: absolute;
        content: "";
        height: 26px;
        width: 26px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }

    input:checked + .toggle-slider {
        background-color: #2196F3;
    }

    input:checked + .toggle-slider:before {
        transform: translateX(26px);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1>AI Engine Diagnostics</h1>

    <div class="row">
        <div class="col-md-6">
            <div class="diagnostics-card">
                <div class="card-header bg-primary text-white">
                    <i class="fas fa-brain me-2"></i> AI Engine Status
                </div>
                <div class="card-body">
                    <h4>Current Engine: <span class="badge bg-primary">{{ current_engine }}</span></h4>
                    <p>Internal Provider: <code>{{ current_provider }}</code></p>
                    <p>Model: <code>{{ current_model }}</code></p>

                    <h5 class="mt-4">Provider Status</h5>
                    <div class="provider-details">
                        {% for provider, status in provider_status.items %}
                        <div class="mb-3">
                            <h6>
                                <span class="status-indicator {% if status.available %}online{% else %}offline{% endif %}"></span>
                                {{ provider|title }}
                            </h6>
                            <dl class="row">
                                <dt class="col-sm-4">Status</dt>
                                <dd class="col-sm-8">
                                    {% if status.available %}
                                    <span class="badge bg-success">Online</span>
                                    {% else %}
                                    <span class="badge bg-danger">Offline</span>
                                    {% endif %}
                                </dd>

                                <dt class="col-sm-4">Response Time</dt>
                                <dd class="col-sm-8">{{ status.response_time|floatformat:2 }} seconds</dd>

                                <dt class="col-sm-4">Last Check</dt>
                                <dd class="col-sm-8">{{ status.last_check|date:"Y-m-d H:i:s" }}</dd>

                                {% if status.error %}
                                <dt class="col-sm-4">Error</dt>
                                <dd class="col-sm-8 text-danger">{{ status.error }}</dd>
                                {% endif %}
                            </dl>
                        </div>
                        {% endfor %}
                    </div>

                    <div class="mt-4">
                        <a href="{% url 'ai_services:admin_status' %}" class="btn btn-outline-primary">
                            <i class="fas fa-sync-alt me-2"></i> Refresh Status
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="diagnostics-card">
                <div class="card-header bg-info text-white">
                    <i class="fas fa-chart-bar me-2"></i> Usage Statistics
                </div>
                <div class="card-body">
                    <h5>Provider Usage (Last 24 Hours)</h5>
                    <div class="usage-stats">
                        {% for provider, stats in usage_stats.items %}
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>{{ provider|title }}</span>
                                <span>{{ stats.count }} requests</span>
                            </div>
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" style="width: {{ stats.percentage }}%;" aria-valuenow="{{ stats.percentage }}" aria-valuemin="0" aria-valuemax="100">{{ stats.percentage }}%</div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <h5 class="mt-4">Cache Statistics</h5>
                    <div class="cache-stats">
                        <ul class="list-group">
                            <li class="list-group-item">
                                <span>Cache Hits</span>
                                <span class="badge bg-success">{{ cache_stats.hits }}</span>
                            </li>
                            <li class="list-group-item">
                                <span>Cache Misses</span>
                                <span class="badge bg-warning">{{ cache_stats.misses }}</span>
                            </li>
                            <li class="list-group-item">
                                <span>Cache Size</span>
                                <span>{{ cache_stats.size|filesizeformat }}</span>
                            </li>
                            <li class="list-group-item">
                                <span>Cache Entries</span>
                                <span>{{ cache_stats.entries }}</span>
                            </li>
                        </ul>
                    </div>

                    <h5 class="mt-4">Fallback Usage</h5>
                    <p>Fallback suggestions used in last 24 hours: <span class="badge bg-warning">{{ fallback_count }}</span></p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="diagnostics-card">
                <div class="card-header bg-warning text-white">
                    <i class="fas fa-network-wired me-2"></i> Mistral API Connectivity Check
                </div>
                <div class="card-body">
                    <div id="mistral-connectivity-results">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Checking Mistral API connectivity...</p>
                        </div>
                    </div>

                    <div class="mt-3">
                        <button id="check-mistral-api" class="btn btn-warning">
                            <i class="fas fa-sync-alt me-2"></i> Check Mistral API
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-12">
            <div class="diagnostics-card">
                <div class="card-header bg-secondary text-white">
                    <i class="fas fa-cogs me-2"></i> Engine Settings
                </div>
                <div class="card-body">
                    <form method="post" action="{% url 'ai_services:admin_diagnostics' %}">
                        {% csrf_token %}

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h5>Local Mode</h5>
                                <p class="text-muted">When enabled, all AI requests will use local models or fallback suggestions.</p>

                                <div class="form-check form-switch">
                                    <label class="toggle-switch">
                                        <input type="checkbox" name="use_local_mode" {% if use_local_mode %}checked{% endif %}>
                                        <span class="toggle-slider"></span>
                                    </label>
                                    <span class="ms-3">Use Local Mode</span>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <h5>Default Provider</h5>
                                <p class="text-muted">Select the default AI provider to use when multiple are available.</p>

                                <select class="form-select" name="default_provider">
                                    {% for provider in available_providers %}
                                    <option value="{{ provider }}" {% if provider == current_provider %}selected{% endif %}>{{ provider|title }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i> Save Settings
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extrajs %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initial check of Mistral API connectivity
        checkMistralConnectivity();

        // Add event listener for the check button
        document.getElementById('check-mistral-api').addEventListener('click', function() {
            checkMistralConnectivity(true);
        });
    });

    function checkMistralConnectivity(forceRefresh = false) {
        // Show loading state
        document.getElementById('mistral-connectivity-results').innerHTML = `
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Checking Mistral API connectivity...</p>
            </div>
        `;

        // Make the API request
        fetch(`/ai_services/api/check-mistral-api/?refresh=${forceRefresh}`)
            .then(response => response.json())
            .then(data => {
                // Format the results
                let statusClass = data.available ? 'success' : 'danger';
                let statusText = data.available ? 'Online' : 'Offline';
                let statusIcon = data.available ? 'check-circle' : 'times-circle';

                // Create the results HTML
                let resultsHtml = `
                    <div class="card">
                        <div class="card-header bg-${statusClass} text-white">
                            <i class="fas fa-${statusIcon} me-2"></i> Mistral API Status: ${statusText}
                        </div>
                        <div class="card-body">
                            <dl class="row">
                                <dt class="col-sm-4">Status</dt>
                                <dd class="col-sm-8">
                                    <span class="badge bg-${statusClass}">${statusText}</span>
                                </dd>

                                <dt class="col-sm-4">Response Time</dt>
                                <dd class="col-sm-8">${data.response_time.toFixed(2)} seconds</dd>

                                <dt class="col-sm-4">Last Check</dt>
                                <dd class="col-sm-8">${data.timestamp_formatted}</dd>

                                <dt class="col-sm-4">Status Code</dt>
                                <dd class="col-sm-8">${data.status_code || 'N/A'}</dd>
                `;

                // Add error message if present
                if (data.error) {
                    resultsHtml += `
                                <dt class="col-sm-4">Error</dt>
                                <dd class="col-sm-8 text-danger">${data.error}</dd>
                    `;
                }

                // Add available models if present
                if (data.available_models && data.available_models.length > 0) {
                    resultsHtml += `
                                <dt class="col-sm-4">Available Models</dt>
                                <dd class="col-sm-8">
                                    <ul class="list-group">
                    `;

                    data.available_models.forEach(model => {
                        resultsHtml += `
                                        <li class="list-group-item">${model}</li>
                        `;
                    });

                    resultsHtml += `
                                    </ul>
                                </dd>
                    `;
                }

                // Close the HTML
                resultsHtml += `
                            </dl>
                        </div>
                    </div>
                `;

                // Update the results container
                document.getElementById('mistral-connectivity-results').innerHTML = resultsHtml;
            })
            .catch(error => {
                // Show error message
                document.getElementById('mistral-connectivity-results').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i> Error checking Mistral API: ${error.message}
                    </div>
                `;
            });
    }
</script>
{% endblock %}