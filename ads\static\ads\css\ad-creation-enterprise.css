/**
 * Ad Creation Enterprise CSS
 * Styles for the enterprise ad creation form
 */

/* Multi-step Form Styles */
.nav-tabs {
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 1.5rem;
}

.nav-tabs .nav-link {
    border: none;
    color: #6c757d;
    font-weight: 500;
    padding: 0.75rem 1rem;
    position: relative;
    transition: all 0.2s ease;
}

.nav-tabs .nav-link:hover {
    color: #495057;
    background-color: transparent;
    border-color: transparent;
}

.nav-tabs .nav-link.active {
    color: #3f51b5;
    background-color: transparent;
    border-color: transparent;
}

.nav-tabs .nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #3f51b5;
}

/* Step Buttons */
.step-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 2rem;
}

.btn-prev, .btn-next, .btn-submit {
    padding: 0.5rem 1.25rem;
    border-radius: 4px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-prev {
    background-color: #f8f9fa;
    border: 1px solid #ced4da;
    color: #495057;
}

.btn-prev:hover {
    background-color: #e9ecef;
}

.btn-next {
    background-color: #3f51b5;
    border: 1px solid #3f51b5;
    color: white;
}

.btn-next:hover {
    background-color: #303f9f;
}

.btn-submit {
    background-color: #4caf50;
    border: 1px solid #4caf50;
    color: white;
}

.btn-submit:hover {
    background-color: #43a047;
}

/* Progress Bar */
.progress {
    height: 6px;
    margin-top: 0.5rem;
    background-color: #e9ecef;
}

.progress-bar {
    background-color: #3f51b5;
    transition: width 0.3s ease;
}

/* Form Controls */
.form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-control, .form-select {
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 0.5rem 0.75rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: #3f51b5;
    box-shadow: 0 0 0 0.25rem rgba(63, 81, 181, 0.25);
}

.form-text {
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Preview Section */
.preview-section {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    position: sticky;
    top: 20px;
}

.preview-header {
    margin-bottom: 1.5rem;
}

.preview-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #212529;
    margin-bottom: 0.25rem;
}

.preview-subtitle {
    color: #6c757d;
    font-size: 0.875rem;
    margin-bottom: 0;
}

.preview-content {
    background-color: white;
    border-radius: 6px;
    padding: 1rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.ad-preview-location-label {
    font-size: 0.75rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
}

.ad-preview-wrapper {
    border: 1px dashed #ced4da;
    padding: 1rem;
    border-radius: 4px;
    min-height: 250px;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.ad-preview-title {
    font-weight: 600;
    color: #212529;
    font-size: 1.1rem;
}

.ad-preview-content {
    color: #495057;
    font-size: 0.9rem;
}

.ad-preview-image {
    text-align: center;
    margin: 0.5rem 0;
}

.ad-preview-image img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
}

.ad-preview-cta {
    margin-top: auto;
}

.ad-preview-size-info {
    text-align: right;
    font-size: 0.75rem;
    color: #6c757d;
}

/* Pro Tips Section */
#proTipsHeader {
    background-color: #fff3cd;
    color: #856404;
}

#proTipsContent .list-group-item {
    padding: 0.5rem 0;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
    .preview-section {
        margin-top: 2rem;
        position: static;
    }
}

@media (max-width: 768px) {
    .nav-tabs .nav-link {
        padding: 0.5rem 0.75rem;
        font-size: 0.9rem;
    }
    
    .step-buttons {
        flex-direction: column;
        gap: 1rem;
    }
    
    .btn-prev, .btn-next, .btn-submit {
        width: 100%;
    }
}
