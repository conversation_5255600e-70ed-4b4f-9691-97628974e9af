from django.db import models
from django.contrib.auth.models import User
from django.contrib.contenttypes.fields import GenericForeign<PERSON>ey
from django.contrib.contenttypes.models import ContentType
from django.utils.translation import gettext_lazy as _
from django.urls import reverse


class Notification(models.Model):
    """
    Model for storing user notifications
    """

    class NotificationType(models.TextChoices):
        INFO = 'info', _('Information')
        SUCCESS = 'success', _('Success')
        WARNING = 'warning', _('Warning')
        ERROR = 'error', _('Error')

    class NotificationCategory(models.TextChoices):
        SYSTEM = 'system', _('System')
        AD = 'ad', _('Advertisement')
        PAYMENT = 'payment', _('Payment')
        QR_CODE = 'qr_code', _('QR Code')
        USER = 'user', _('User')
        CAMPAIGN = 'campaign', _('Campaign')
        ANALYTICS = 'analytics', _('Analytics')
        OTHER = 'other', _('Other')

    # User who receives the notification
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='notifications',
        verbose_name=_('User')
    )

    # Notification content
    title = models.CharField(_('Title'), max_length=255)
    message = models.TextField(_('Message'))

    # Notification type and category
    notification_type = models.CharField(
        _('Type'),
        max_length=10,
        choices=NotificationType.choices,
        default=NotificationType.INFO
    )
    category = models.CharField(
        _('Category'),
        max_length=20,
        choices=NotificationCategory.choices,
        default=NotificationCategory.SYSTEM
    )

    # Link to related object (optional)
    content_type = models.ForeignKey(
        ContentType,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_('Content Type')
    )
    object_id = models.PositiveIntegerField(null=True, blank=True, verbose_name=_('Object ID'))
    content_object = GenericForeignKey('content_type', 'object_id')

    # URL to redirect to when notification is clicked (optional)
    action_url = models.CharField(_('Action URL'), max_length=255, blank=True, null=True)

    # Status fields
    is_read = models.BooleanField(_('Read'), default=False)
    is_deleted = models.BooleanField(_('Deleted'), default=False)
    is_archived = models.BooleanField(_('Archived'), default=False)
    archived_at = models.DateTimeField(_('Archived At'), null=True, blank=True)
    is_muted = models.BooleanField(_('Muted'), default=False)
    muted_at = models.DateTimeField(_('Muted At'), null=True, blank=True)

    # Timestamps
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = _('Notification')
        verbose_name_plural = _('Notifications')
        indexes = [
            models.Index(fields=['user', '-created_at']),
            models.Index(fields=['user', 'is_read']),
            models.Index(fields=['user', 'category']),
        ]

    def __str__(self):
        return f"{self.title} - {self.user.username}"

    def mark_as_read(self):
        """Mark notification as read"""
        self.is_read = True
        self.save(update_fields=['is_read', 'updated_at'])

    def mark_as_unread(self):
        """Mark notification as unread"""
        self.is_read = False
        self.save(update_fields=['is_read', 'updated_at'])

    def soft_delete(self):
        """Soft delete notification"""
        self.is_deleted = True
        self.save(update_fields=['is_deleted', 'updated_at'])

    def archive(self):
        """Archive notification for 30 days before deletion"""
        from django.utils import timezone

        self.is_archived = True
        self.archived_at = timezone.now()
        self.save(update_fields=['is_archived', 'archived_at', 'updated_at'])

    def mute(self):
        """Mute notifications of this category"""
        from django.utils import timezone

        self.is_muted = True
        self.muted_at = timezone.now()
        self.save(update_fields=['is_muted', 'muted_at', 'updated_at'])

    @classmethod
    def mute_category(cls, user, category):
        """Mute all notifications of a specific category for a user"""
        from django.utils import timezone

        # Mark all existing notifications of this category as muted
        notifications = cls.objects.filter(user=user, category=category, is_deleted=False)
        notifications.update(is_muted=True, muted_at=timezone.now())

        return notifications.count()

    def get_absolute_url(self):
        """Get URL to view notification details"""
        return reverse('notifications:notification_detail', kwargs={'pk': self.pk})

    @property
    def get_action_url(self):
        """Get action URL or fallback to notification detail URL"""
        if self.action_url:
            return self.action_url
        return self.get_absolute_url()

    @property
    def time_ago(self):
        """Get human-readable time since notification was created"""
        from django.utils import timezone
        from django.utils.timesince import timesince

        return timesince(self.created_at, timezone.now())
