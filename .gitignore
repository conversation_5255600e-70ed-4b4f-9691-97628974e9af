# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Django
*.log
*.pot
*.pyc
local_settings.py
db.sqlite3
db.sqlite3-journal
media

# Virtual Environment
venv/
ENV/
env/

# IDE
.idea/
.vscode/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Project specific
staticfiles/
.env
celerybeat-schedule
celerybeat.pid

# Legacy AI engine - locally kept
ai_engine/

# Virtual environment
qrgenvenv/

# Logs
logs/
*.log.*

# Docker
.docker/

# IDE files
.vs/
*.vsidx

# Large files
*.safetensors
*.bin
*.pt
*.pth
*.onnx
*.model
