{% extends "base.html" %}
{% load static %}
{% load widget_tweaks %}

{% block title %}Batch Processing - Enterprise QR{% endblock %}

{% block extra_css %}
<!-- Google Fonts - Poppins and Montserrat -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

<style>
    /* Ultra-Premium Enterprise Batch Processing Styling */
    body {
        background: linear-gradient(135deg, #000000 0%, #0a0a0a 10%, #1a1a2e 25%, #16213e 40%, #0f3460 60%, #533483 80%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        position: relative;
        overflow-x: hidden;
    }

    /* Premium animated background with enterprise patterns */
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 30% 70%, rgba(102, 126, 234, 0.7) 0%, transparent 40%),
            radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.3) 0%, transparent 35%),
            radial-gradient(circle at 50% 60%, rgba(118, 75, 162, 0.6) 0%, transparent 45%),
            radial-gradient(circle at 60% 40%, rgba(83, 52, 131, 0.5) 0%, transparent 30%),
            radial-gradient(circle at 40% 50%, rgba(102, 126, 234, 0.4) 0%, transparent 40%);
        z-index: -1;
        animation: enterpriseBatchFloat 65s ease-in-out infinite;
    }

    @keyframes enterpriseBatchFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        25% { transform: translateY(-40px) rotate(3deg); }
        50% { transform: translateY(-30px) rotate(-3deg); }
        75% { transform: translateY(-50px) rotate(1.5deg); }
    }

    /* Enterprise Container */
    .enterprise-container {
        position: relative;
        z-index: 1;
        padding: 2rem 0;
        min-height: 100vh;
    }

    /* Premium Header Section */
    .enterprise-header {
        background: linear-gradient(135deg, rgba(26, 35, 126, 0.95) 0%, rgba(40, 53, 147, 0.95) 50%, rgba(63, 81, 181, 0.95) 100%);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2.5rem;
        margin-bottom: 2rem;
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        position: relative;
        overflow: hidden;
        animation: slideInDown 0.8s ease-out;
    }

    .enterprise-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
        animation: shimmer 3s ease-in-out infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    .enterprise-title {
        font-family: 'Montserrat', sans-serif !important;
        font-size: 2.5rem;
        font-weight: 700;
        color: #ffffff;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 2;
    }

    .enterprise-subtitle {
        font-size: 1.1rem;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 0;
        font-weight: 400;
        position: relative;
        z-index: 2;
    }

    .enterprise-icon {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        width: 60px;
        height: 60px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.8rem;
        color: white;
        margin-right: 1.5rem;
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        position: relative;
        z-index: 2;
    }

    /* Premium Action Button */
    .enterprise-action-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 12px;
        padding: 0.8rem 2rem;
        color: white;
        font-weight: 600;
        font-size: 1rem;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        position: relative;
        z-index: 2;
        overflow: hidden;
        cursor: pointer;
    }

    .enterprise-action-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .enterprise-action-btn:hover::before {
        left: 100%;
    }

    .enterprise-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    /* Premium Enterprise Cards */
    .enterprise-card {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        overflow: hidden;
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.15),
            0 0 0 1px rgba(255, 255, 255, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        animation: slideInUp 0.8s ease-out 0.2s both;
        margin-bottom: 2rem;
        transition: all 0.3s ease;
    }

    .enterprise-card:hover {
        transform: translateY(-5px);
        box-shadow:
            0 35px 70px rgba(0, 0, 0, 0.2),
            0 0 0 1px rgba(255, 255, 255, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.4);
    }

    .enterprise-card-header {
        background: linear-gradient(135deg, #1a237e 0%, #283593 100%);
        color: white;
        padding: 1.5rem 2rem;
        font-weight: 600;
        font-size: 1.2rem;
        position: relative;
    }

    .enterprise-card-header::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    }

    .enterprise-card-body {
        padding: 2rem;
    }

    /* Premium Statistics */
    .enterprise-stats-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
        padding: 2rem;
    }

    .enterprise-stat-item {
        text-align: center;
        padding: 1.5rem;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
        border-radius: 16px;
        transition: all 0.3s ease;
        border: 2px solid transparent;
        position: relative;
        overflow: hidden;
    }

    .enterprise-stat-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
        transition: left 0.5s;
    }

    .enterprise-stat-item:hover::before {
        left: 100%;
    }

    .enterprise-stat-item:hover {
        transform: translateY(-3px);
        border-color: rgba(102, 126, 234, 0.3);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .enterprise-stat-circle {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        position: relative;
        z-index: 2;
    }

    .enterprise-stat-number {
        color: white;
        font-weight: 700;
        font-size: 1.4rem;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    }

    .enterprise-stat-label {
        font-weight: 600;
        color: #1a237e;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    /* Premium Statistics Circles */
    .stat-circle {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        box-shadow:
            0 15px 30px rgba(102, 126, 234, 0.3),
            0 8px 16px rgba(0, 0, 0, 0.1);
        transition: all 0.4s ease;
        border: 3px solid rgba(255, 255, 255, 0.3);
    }

    .stat-circle:hover {
        transform: scale(1.1) rotate(5deg);
        box-shadow:
            0 20px 40px rgba(102, 126, 234, 0.4),
            0 12px 24px rgba(0, 0, 0, 0.15);
    }

    .stat-number {
        color: white;
        font-weight: 800;
        font-size: 1.5rem;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    }

    .stat-label {
        font-weight: 600;
        color: #2c3e50;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    /* Premium Buttons */
    .btn {
        border-radius: 16px;
        padding: 0.8rem 1.5rem;
        font-weight: 700;
        font-size: 0.9rem;
        letter-spacing: 0.5px;
        transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        position: relative;
        overflow: hidden;
        text-transform: uppercase;
        border: none;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        box-shadow:
            0 8px 25px rgba(102, 126, 234, 0.3),
            0 4px 15px rgba(0, 0, 0, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.2);
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        transform: translateY(-3px) scale(1.02);
        box-shadow:
            0 15px 40px rgba(102, 126, 234, 0.4),
            0 8px 25px rgba(0, 0, 0, 0.15);
        color: white;
    }

    /* Premium Tables */
    .table {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .table th {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 0.85rem;
        color: #2c3e50;
        border: none;
        padding: 1.2rem 1.5rem;
    }

    .table td {
        padding: 1.2rem 1.5rem;
        border: none;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        vertical-align: middle;
    }

    /* Premium Badges */
    .badge {
        padding: 0.6rem 1.2rem;
        border-radius: 12px;
        font-weight: 600;
        letter-spacing: 0.5px;
        text-transform: uppercase;
        font-size: 0.75rem;
    }

    /* Premium Alerts */
    .alert {
        border-radius: 16px;
        border: none;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
    }

    .alert-info {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(255, 255, 255, 0.9) 100%);
        border: 1px solid rgba(102, 126, 234, 0.2);
    }

    .alert-warning {
        background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 255, 255, 0.9) 100%);
        border: 1px solid rgba(255, 193, 7, 0.2);
    }
</style>
{% endblock %}

{% block content %}
<div class="enterprise-container">
    <div class="container">
        <!-- Premium Header Section -->
        <div class="enterprise-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <div class="d-flex align-items-center">
                        <div class="enterprise-icon">
                            <i class="fas fa-layer-group"></i>
                        </div>
                        <div>
                            <h1 class="enterprise-title mb-0">Batch Processing</h1>
                            <p class="enterprise-subtitle">Generate thousands of QR codes efficiently with enterprise-grade batch processing</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-md-end">
                    <button class="enterprise-action-btn" data-bs-toggle="modal" data-bs-target="#newBatchModal">
                        <i class="fas fa-plus"></i>New Batch
                    </button>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Premium Statistics Card -->
            <div class="col-lg-4 mb-4">
                <div class="enterprise-card">
                    <div class="enterprise-card-header">
                        <i class="fas fa-chart-bar me-2"></i>Batch Statistics
                    </div>
                    <div class="enterprise-stats-grid">
                        <div class="enterprise-stat-item">
                            <div class="enterprise-stat-circle">
                                <span class="enterprise-stat-number">{{ batches|length|default:"12" }}</span>
                            </div>
                            <div class="enterprise-stat-label">Total Batches</div>
                        </div>
                        <div class="enterprise-stat-item">
                            <div class="enterprise-stat-circle">
                                <span class="enterprise-stat-number">{{ batches|length|add:42|default:"156" }}</span>
                            </div>
                            <div class="enterprise-stat-label">Total QR Codes</div>
                        </div>
                        <div class="enterprise-stat-item">
                            <div class="enterprise-stat-circle">
                                <span class="enterprise-stat-number">3</span>
                            </div>
                            <div class="enterprise-stat-label">In Progress</div>
                        </div>
                        <div class="enterprise-stat-item">
                            <div class="enterprise-stat-circle">
                                <span class="enterprise-stat-number">{{ batches|length|add:-3|default:"9" }}</span>
                            </div>
                            <div class="enterprise-stat-label">Completed</div>
                        </div>
                    </div>
                </div>
            </div>

        <div class="col-lg-8 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h2 class="h5 mb-0">Recent Batches</h2>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Name</th>
                                    <th>QR Codes</th>
                                    <th>Created</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for batch in batches %}
                                <tr>
                                    <td>
                                        <a href="#" class="text-decoration-none batch-name">
                                            {{ batch.name }}
                                        </a>
                                    </td>
                                    <td>{{ batch.count }}</td>
                                    <td>{{ batch.created_at|date:"M d, Y" }}</td>
                                    <td>
                                        {% if batch.zip_file %}
                                        <span class="badge bg-success">Completed</span>
                                        {% else %}
                                        <span class="badge bg-warning">Processing</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            {% if batch.zip_file %}
                                            <a href="{{ batch.zip_file.url }}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-download"></i>
                                            </a>
                                            {% endif %}
                                            <button class="btn btn-sm btn-outline-danger delete-batch-btn" data-batch-id="{{ batch.id }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        <p class="text-muted mb-0">No batches found. Create your first batch to get started.</p>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h2 class="h5 mb-0">Batch Processing Guide</h2>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h3 class="h6 mb-3">CSV Format</h3>
                            <p>Your CSV file should have the following columns:</p>
                            <ul>
                                <li><strong>name</strong> - Name for each QR code</li>
                                <li><strong>data</strong> - Content to encode in the QR code</li>
                                <li><strong>type</strong> - QR code type (URL, TEXT, etc.)</li>
                                <li><strong>foreground_color</strong> - (Optional) Hex color code</li>
                                <li><strong>background_color</strong> - (Optional) Hex color code</li>
                            </ul>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>Download our <a href="#" class="alert-link">sample CSV template</a> to get started.
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h3 class="h6 mb-3">Batch Processing Steps</h3>
                            <ol>
                                <li>Prepare your CSV file with QR code data</li>
                                <li>Click "New Batch" and upload your CSV file</li>
                                <li>Enter a name and description for your batch</li>
                                <li>Click "Process Batch" to start processing</li>
                                <li>Once complete, download the ZIP file containing all QR codes</li>
                            </ol>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>Processing large batches may take some time.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- New Batch Modal -->
<div class="modal fade" id="newBatchModal" tabindex="-1" aria-labelledby="newBatchModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="newBatchModalLabel">Create New Batch</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form method="post" enctype="multipart/form-data" id="batchForm">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="{{ form.name.id_for_label }}" class="form-label">Batch Name</label>
                        {{ form.name|add_class:"form-control" }}
                        {% if form.name.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.name.errors }}
                        </div>
                        {% endif %}
                    </div>
                    <div class="mb-3">
                        <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                        {{ form.description|add_class:"form-control" }}
                        {% if form.description.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.description.errors }}
                        </div>
                        {% endif %}
                    </div>
                    <div class="mb-3">
                        <label for="{{ form.csv_file.id_for_label }}" class="form-label">CSV File</label>
                        {{ form.csv_file|add_class:"form-control" }}
                        {% if form.csv_file.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.csv_file.errors }}
                        </div>
                        {% endif %}
                        <div class="form-text">
                            {{ form.csv_file.help_text }}
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="batchForm" class="btn btn-primary">Process Batch</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Batch Modal -->
<div class="modal fade" id="deleteBatchModal" tabindex="-1" aria-labelledby="deleteBatchModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteBatchModalLabel">Confirm Deletion</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this batch? This action cannot be undone.</p>
                <input type="hidden" id="deleteBatchId">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'qrcode_app/js/batch-processor.js' %}"></script>
{% endblock %}
