/**
 * shared-utils.js
 *
 * This file contains shared utility functions used across multiple JavaScript files.
 * It helps break circular dependencies between notification-handler.js, new_nav.js,
 * and mobile-navigation.js.
 */

// Prevent duplicate initialization
if (typeof window.sharedUtilsLoaded === 'undefined') {
    window.sharedUtilsLoaded = true;

    console.log('Shared utilities loaded');

    /**
     * DOM Utilities
     */

    /**
     * Safely get an element by ID with error handling
     * @param {string} id - The ID of the element to get
     * @returns {HTMLElement|null} - The element or null if not found
     */
    function getElementByIdSafe(id) {
        try {
            return document.getElementById(id);
        } catch (error) {
            console.error(`Error getting element with ID ${id}:`, error);
            return null;
        }
    }

    /**
     * Safely query for elements with error handling
     * @param {string} selector - The CSS selector to query
     * @param {HTMLElement} [parent=document] - The parent element to query within
     * @returns {NodeList|null} - The query results or null if error
     */
    function querySelectorAllSafe(selector, parent = document) {
        try {
            return parent.querySelectorAll(selector);
        } catch (error) {
            console.error(`Error querying for ${selector}:`, error);
            return null;
        }
    }

    /**
     * Safely add an event listener with error handling
     * @param {HTMLElement} element - The element to add the listener to
     * @param {string} eventType - The event type (e.g., 'click')
     * @param {Function} handler - The event handler function
     */
    function addEventListenerSafe(element, eventType, handler) {
        if (!element) return;

        try {
            element.addEventListener(eventType, handler);
        } catch (error) {
            console.error(`Error adding ${eventType} listener:`, error);
        }
    }

    /**
     * Remove event listeners by cloning and replacing an element
     * @param {HTMLElement} element - The element to clone and replace
     * @returns {HTMLElement|null} - The new element or null if error
     */
    function cloneAndReplaceElement(element) {
        if (!element || !element.parentNode) return null;

        try {
            const newElement = element.cloneNode(true);
            element.parentNode.replaceChild(newElement, element);
            return newElement;
        } catch (error) {
            console.error('Error cloning and replacing element:', error);
            return null;
        }
    }

    /**
     * Format a timestamp into a human-readable format
     * @param {Date} timestamp - The timestamp to format
     * @returns {string} - The formatted timestamp
     */
    function formatTimestamp(timestamp) {
        // Check if timestamp is valid
        if (!timestamp || isNaN(timestamp.getTime())) {
            return 'Just now';
        }

        const now = new Date();
        const diffMs = now - timestamp;
        const diffSec = Math.floor(diffMs / 1000);
        const diffMin = Math.floor(diffSec / 60);
        const diffHour = Math.floor(diffMin / 60);
        const diffDay = Math.floor(diffHour / 24);

        // Format based on how long ago
        if (diffSec < 60) {
            return 'Just now';
        } else if (diffMin < 60) {
            return `${diffMin} minute${diffMin > 1 ? 's' : ''} ago`;
        } else if (diffHour < 24) {
            return `${diffHour} hour${diffHour > 1 ? 's' : ''} ago`;
        } else if (diffDay < 7) {
            return `${diffDay} day${diffDay > 1 ? 's' : ''} ago`;
        } else {
            // Format as date
            return timestamp.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }
    }

    /**
     * Get CSRF token from cookies
     * @returns {string} - The CSRF token
     */
    function getCsrfToken() {
        const name = 'csrftoken';
        let cookieValue = null;

        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }

        return cookieValue;
    }

    /**
     * Alternative name for CSRF token function for compatibility
     * @returns {string} - The CSRF token
     */
    function getCSRFToken() {
        return getCsrfToken();
    }

    /**
     * Notification Utilities
     */

    /**
     * Update notification counter with improved handling and pulsating effect
     * @param {number} count - The notification count
     */
    function updateNotificationCounter(count) {
        // Update counter in navbar
        const notificationCounter = getElementByIdSafe('notification-counter');
        if (notificationCounter) {
            if (count > 0) {
                notificationCounter.textContent = count;
                notificationCounter.classList.remove('d-none');

                // Add pulsating effect if there are multiple notifications
                if (count >= 2) {
                    notificationCounter.classList.add('has-notifications');
                } else {
                    notificationCounter.classList.remove('has-notifications');
                }
            } else {
                notificationCounter.classList.add('d-none');
                notificationCounter.classList.remove('has-notifications');
            }
        }

        // Update all notification bells and badges in the navbar only
        const bells = querySelectorAllSafe('.notification-bell.desktop-nav, .notification-bell.mobile-bell');
        const badges = querySelectorAllSafe('.notification-badge');

        // Update bell appearance
        if (bells) {
            bells.forEach(bell => {
                if (count > 0) {
                    bell.classList.add('has-notifications');
                } else {
                    bell.classList.remove('has-notifications');
                }
            });
        }

        // Update badges
        if (badges) {
            badges.forEach(badge => {
                if (count > 0) {
                    badge.textContent = count;
                    badge.style.display = 'flex';

                    // Add pulsating effect if there are multiple notifications
                    if (count >= 2) {
                        badge.classList.add('has-notifications');
                    } else {
                        badge.classList.remove('has-notifications');
                    }
                } else {
                    badge.textContent = '0';
                    badge.style.display = 'none';
                    badge.classList.remove('has-notifications');
                }
            });
        }
    }

    /**
     * Navigation Utilities
     */

    /**
     * Handle navbar scroll effects
     */
    function handleNavbarScroll() {
        const navbar = document.querySelector('.new-navbar');
        if (navbar) {
            if (window.scrollY > 10) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        }
    }

    /**
     * Highlight active links based on current path
     */
    function highlightActiveLinks() {
        const currentPath = window.location.pathname;
        const navLinks = querySelectorAllSafe('.new-navbar-link');

        if (!navLinks) return;

        navLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href && currentPath === href) {
                link.classList.add('active');
            }

            // For dropdown items
            if (link.parentElement && link.parentElement.classList.contains('has-dropdown')) {
                const dropdownMenu = link.nextElementSibling;
                if (dropdownMenu) {
                    const dropdownItems = querySelectorAllSafe('.new-navbar-dropdown-item', dropdownMenu);
                    if (dropdownItems) {
                        dropdownItems.forEach(item => {
                            const itemHref = item.getAttribute('href');
                            if (itemHref && currentPath === itemHref) {
                                link.classList.add('active');
                                item.classList.add('active');
                            }
                        });
                    }
                }
            }
        });
    }

    // Export functions to global scope for use in other files
    window.SharedUtils = {
        // DOM Utilities
        getElementByIdSafe,
        querySelectorAllSafe,
        addEventListenerSafe,
        cloneAndReplaceElement,
        formatTimestamp,
        getCsrfToken,
        getCSRFToken,

        // Notification Utilities
        updateNotificationCounter,

        // Navigation Utilities
        handleNavbarScroll,
        highlightActiveLinks
    };
}
