{% extends "base.html" %}
{% load static %}

{% block title %}{{ qr_code.name }} - Enterprise QR{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/qr-code-styles.css' %}">
<style>
    /* Ultra-Premium Enterprise Corporate Styling */
    body {
        background:
            linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #0f3460 50%, #533483 75%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        position: relative;
        overflow-x: hidden;
    }

    /* Premium animated background with corporate patterns */
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 15% 85%, rgba(102, 126, 234, 0.4) 0%, transparent 40%),
            radial-gradient(circle at 85% 15%, rgba(255, 255, 255, 0.15) 0%, transparent 35%),
            radial-gradient(circle at 45% 55%, rgba(118, 75, 162, 0.3) 0%, transparent 45%),
            radial-gradient(circle at 75% 75%, rgba(83, 52, 131, 0.25) 0%, transparent 30%);
        z-index: -1;
        animation: premiumBackgroundFloat 25s ease-in-out infinite;
    }

    /* Corporate grid pattern overlay */
    body::after {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image:
            linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
            linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
        background-size: 50px 50px;
        z-index: -1;
        opacity: 0.5;
        animation: gridPulse 8s ease-in-out infinite;
    }

    @keyframes premiumBackgroundFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
        25% { transform: translateY(-15px) rotate(1deg) scale(1.02); }
        50% { transform: translateY(5px) rotate(-0.5deg) scale(0.98); }
        75% { transform: translateY(-8px) rotate(0.8deg) scale(1.01); }
    }

    @keyframes gridPulse {
        0%, 100% { opacity: 0.3; }
        50% { opacity: 0.6; }
    }

    /* Premium Container */
    .container {
        max-width: 1400px;
        padding: 3rem 2rem;
        position: relative;
        z-index: 1;
    }

    /* Ultra-Premium Breadcrumb */
    .breadcrumb {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
        border-radius: 20px;
        padding: 1rem 1.5rem;
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    }

    .breadcrumb-item a {
        color: rgba(255, 255, 255, 0.8);
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .breadcrumb-item a:hover {
        color: white;
        text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
    }

    .breadcrumb-item.active {
        color: white;
        font-weight: 600;
    }

    /* Premium Page Title */
    h1 {
        color: white;
        font-weight: 800;
        font-size: 2.5rem;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
        margin-bottom: 2rem;
        background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    h1 i {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #ffffff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
    }

    /* Ultra-Premium Cards */
    .card {
        border-radius: 32px;
        overflow: hidden;
        transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.2),
            0 15px 30px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        border: 2px solid rgba(255, 255, 255, 0.25);
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.95) 0%,
            rgba(248, 249, 250, 0.9) 50%,
            rgba(255, 255, 255, 0.95) 100%);
        backdrop-filter: blur(30px);
        position: relative;
        animation: cardEntry 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }

    .card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg,
            rgba(102, 126, 234, 0.08) 0%,
            rgba(255, 255, 255, 0.1) 50%,
            rgba(118, 75, 162, 0.08) 100%);
        z-index: 0;
        pointer-events: none;
        border-radius: 32px;
    }

    .card:hover {
        box-shadow:
            0 40px 80px rgba(0, 0, 0, 0.25),
            0 20px 40px rgba(0, 0, 0, 0.15),
            0 0 60px rgba(102, 126, 234, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.4);
        transform: translateY(-8px) scale(1.02);
        border-color: rgba(255, 255, 255, 0.4);
    }

    @keyframes cardEntry {
        0% {
            opacity: 0;
            transform: translateY(40px) scale(0.95);
            filter: blur(10px);
        }
        100% {
            opacity: 1;
            transform: translateY(0) scale(1);
            filter: blur(0px);
        }
    }

    /* Premium Card Headers */
    .card-header {
        padding: 2.5rem 3rem;
        border-bottom: none;
        position: relative;
        overflow: hidden;
        background: linear-gradient(135deg,
            #1a1a2e 0%,
            #16213e 25%,
            #0f3460 50%,
            #533483 75%,
            #764ba2 100%);
        border-radius: 32px 32px 0 0;
    }

    .card-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.15) 0%,
            rgba(255, 255, 255, 0.08) 50%,
            rgba(255, 255, 255, 0.15) 100%);
        z-index: 0;
    }

    .card-header h2 {
        position: relative;
        z-index: 2;
        margin: 0;
        color: white;
        font-weight: 800;
        font-size: 1.3rem;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
        letter-spacing: 1px;
        text-transform: uppercase;
        background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .card-body {
        padding: 3rem;
        position: relative;
        z-index: 1;
        background: rgba(255, 255, 255, 0.95);
    }

    /* Premium QR Code Display */
    .qr-code-display {
        margin: 0 auto;
        max-width: 350px;
        position: relative;
    }

    .qr-code-display img {
        max-width: 100%;
        height: auto;
        border-radius: 20px;
        box-shadow:
            0 20px 40px rgba(0, 0, 0, 0.2),
            0 8px 16px rgba(0, 0, 0, 0.1);
        transition: all 0.4s ease;
    }

    .qr-code-display img:hover {
        transform: scale(1.05);
        box-shadow:
            0 30px 60px rgba(0, 0, 0, 0.3),
            0 12px 24px rgba(0, 0, 0, 0.15);
    }

    /* Premium Buttons */
    .btn {
        border-radius: 16px;
        padding: 0.8rem 1.5rem;
        font-weight: 700;
        font-size: 0.9rem;
        letter-spacing: 0.5px;
        transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        position: relative;
        overflow: hidden;
        text-transform: uppercase;
        border: none;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        box-shadow:
            0 8px 25px rgba(102, 126, 234, 0.3),
            0 4px 15px rgba(0, 0, 0, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.2);
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        transform: translateY(-3px) scale(1.02);
        box-shadow:
            0 15px 40px rgba(102, 126, 234, 0.4),
            0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .btn-outline-primary {
        color: #667eea;
        border: 2px solid #667eea;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
    }

    .btn-outline-primary:hover {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        transform: translateY(-3px) scale(1.02);
        box-shadow:
            0 12px 30px rgba(102, 126, 234, 0.3),
            0 6px 20px rgba(0, 0, 0, 0.1);
        border-color: transparent;
    }

    /* Premium Analytics Cards */
    .analytics-card {
        border-radius: 20px;
        box-shadow:
            0 15px 30px rgba(0, 0, 0, 0.1),
            0 8px 16px rgba(0, 0, 0, 0.05);
        transition: all 0.4s ease;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 249, 250, 0.8) 100%);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .analytics-card:hover {
        transform: translateY(-8px) scale(1.03);
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.15),
            0 12px 24px rgba(0, 0, 0, 0.08);
    }

    /* Premium Tables */
    .table {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .table th {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 0.85rem;
        color: #2c3e50;
        border: none;
        padding: 1.2rem 1.5rem;
    }

    .table td {
        padding: 1.2rem 1.5rem;
        border: none;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    /* Premium Color Swatches */
    .color-swatch {
        width: 24px;
        height: 24px;
        border-radius: 8px;
        display: inline-block;
        border: 2px solid rgba(255, 255, 255, 0.8);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        transition: all 0.3s ease;
    }

    .color-swatch:hover {
        transform: scale(1.2);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
    }

    /* Premium Badges */
    .badge {
        padding: 0.6rem 1.2rem;
        border-radius: 12px;
        font-weight: 600;
        letter-spacing: 0.5px;
        text-transform: uppercase;
        font-size: 0.75rem;
    }

    /* Premium Chart Container */
    .chart-container {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 249, 250, 0.8) 100%);
        border-radius: 20px;
        padding: 2rem;
        box-shadow:
            0 15px 30px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.5);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'index' %}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'qr_code_list' %}">My QR Codes</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ qr_code.name }}</li>
                </ol>
            </nav>
            <h1 class="h3">
                <i class="fas fa-qrcode me-2"></i>{{ qr_code.name }}
            </h1>
        </div>
        <div class="col-md-4 text-md-end">
            <div class="btn-group">
                {% if qr_code.image %}<a href="{{ qr_code.image.url }}" class="btn btn-outline-primary" id="download-qr" download="qrcode-{{ qr_code.name|slugify }}.png">{% else %}<a href="#" class="btn btn-outline-primary disabled" title="No image available">{% endif %}
                    <i class="fas fa-download me-2"></i>Download
                </a>
                <div class="dropdown d-inline-block">
                    <button type="button" class="btn btn-outline-secondary dropdown-toggle" id="shareDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-share-alt me-2"></i>Share
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="shareDropdown">
                        <li><h6 class="dropdown-header">Share on Social Media</h6></li>
                        <li><a class="dropdown-item" href="https://www.facebook.com/sharer/sharer.php?u={{ request.build_absolute_uri }}" target="_blank">
                            <i class="fab fa-facebook-f me-2"></i>Facebook
                        </a></li>
                        <li><a class="dropdown-item" href="https://twitter.com/intent/tweet?url={{ request.build_absolute_uri }}&text=Check%20out%20this%20QR%20code" target="_blank">
                            <i class="fab fa-twitter me-2"></i>Twitter
                        </a></li>
                        <li><a class="dropdown-item" href="https://api.whatsapp.com/send?text={{ request.build_absolute_uri }}" target="_blank">
                            <i class="fab fa-whatsapp me-2"></i>WhatsApp
                        </a></li>
                        <li><a class="dropdown-item" href="mailto:?subject=QR%20Code%20-%20{{ qr_code.name }}&body=Check%20out%20this%20QR%20code:%20{{ request.build_absolute_uri }}" target="_blank">
                            <i class="fas fa-envelope me-2"></i>Email
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" id="copyLink" data-url="{{ request.build_absolute_uri }}">
                            <i class="fas fa-copy me-2"></i>Copy Link
                        </a></li>
                    </ul>
                </div>
                <button type="button" class="btn btn-outline-danger" id="delete-qr-btn">
                    <i class="fas fa-trash me-2"></i>Delete
                </button>
                <form method="post" action="{% url 'delete_qr_code' qr_code.id %}" style="display: none;" id="delete-qr-form">
                    {% csrf_token %}
                </form>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-6">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white">
                    <h2 class="h5 mb-0">QR Code</h2>
                </div>
                <div class="card-body text-center">
                    <div class="qr-code-display" id="qr-display">
                        {% if qr_code.image %}<img src="{{ qr_code.image.url }}" alt="{{ qr_code.name }}" class="img-fluid">{% else %}<div class="text-muted p-4"><i class="fas fa-image fa-3x mb-2"></i><br>No image available</div>{% endif %}
                    </div>
                    <div class="mt-3">
                        <div class="btn-group">
                            <button class="btn btn-sm btn-outline-primary" id="print-qr">
                                <i class="fas fa-print me-1"></i>Print
                            </button>
                            <button class="btn btn-sm btn-outline-secondary" id="customize-qr">
                                <i class="fas fa-paint-brush me-1"></i>Customize
                            </button>
                            <button class="btn btn-sm btn-outline-success" id="download-preview">
                                <i class="fas fa-download me-1"></i>Download
                            </button>
                        </div>
                    </div>

                    <!-- Customization Panel -->
                    <div class="customization-panel mt-4" id="customization-panel" style="display: none;">
                        <h4 class="h6 mb-3">Customize Your QR Code</h4>

                        <ul class="nav nav-tabs mb-3" id="customizationTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="style-tab" data-bs-toggle="tab" data-bs-target="#style-tab-pane" type="button" role="tab" aria-controls="style-tab-pane" aria-selected="true">Style</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="colors-tab" data-bs-toggle="tab" data-bs-target="#colors-tab-pane" type="button" role="tab" aria-controls="colors-tab-pane" aria-selected="false">Colors</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="frame-tab" data-bs-toggle="tab" data-bs-target="#frame-tab-pane" type="button" role="tab" aria-controls="frame-tab-pane" aria-selected="false">Frame</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="logo-tab" data-bs-toggle="tab" data-bs-target="#logo-tab-pane" type="button" role="tab" aria-controls="logo-tab-pane" aria-selected="false">Logo</button>
                            </li>
                        </ul>

                        <div class="tab-content" id="customizationTabContent">
                            <!-- Style Tab -->
                            <div class="tab-pane fade show active" id="style-tab-pane" role="tabpanel" aria-labelledby="style-tab" tabindex="0">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="detail-dot-style" class="form-label">Dot Style</label>
                                        <select class="form-select form-select-sm" id="detail-dot-style">
                                            <option value="square">Square (Default)</option>
                                            <option value="dots">Rounded Dots</option>
                                            <option value="rounded">Rounded</option>
                                            <option value="classy">Classy</option>
                                            <option value="classy-rounded">Classy Rounded</option>
                                            <option value="extra-rounded">Extra Rounded</option>
                                            <option value="diamond">Diamond</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="detail-corner-style" class="form-label">Corner Style</label>
                                        <select class="form-select form-select-sm" id="detail-corner-style">
                                            <option value="square">Square (Default)</option>
                                            <option value="dot">Dot</option>
                                            <option value="rounded">Rounded</option>
                                            <option value="extra-rounded">Extra Rounded</option>
                                            <option value="diamond">Diamond</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="detail-corner-dot-style" class="form-label">Corner Dot Style</label>
                                        <select class="form-select form-select-sm" id="detail-corner-dot-style">
                                            <option value="square">Square (Default)</option>
                                            <option value="dot">Dot</option>
                                            <option value="rounded">Rounded</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="detail-shape" class="form-label">QR Shape</label>
                                        <select class="form-select form-select-sm" id="detail-shape">
                                            <option value="square">Square (Default)</option>
                                            <option value="circle">Circle</option>
                                            <option value="rounded">Rounded</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Colors Tab -->
                            <div class="tab-pane fade" id="colors-tab-pane" role="tabpanel" aria-labelledby="colors-tab" tabindex="0">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="detail-foreground-color" class="form-label">Foreground Color</label>
                                        <input type="color" class="form-control form-control-color w-100" id="detail-foreground-color" value="{{ qr_code.foreground_color|default:'#000000' }}">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="detail-background-color" class="form-label">Background Color</label>
                                        <input type="color" class="form-control form-control-color w-100" id="detail-background-color" value="{{ qr_code.background_color|default:'#FFFFFF' }}">
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="detail-gradient-type" class="form-label">Gradient Type</label>
                                        <select class="form-select form-select-sm" id="detail-gradient-type">
                                            <option value="none">No Gradient</option>
                                            <option value="linear">Linear</option>
                                            <option value="radial">Radial</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="detail-gradient-color" class="form-label">Gradient Color</label>
                                        <input type="color" class="form-control form-control-color w-100" id="detail-gradient-color" value="#4A6CF7">
                                    </div>
                                </div>
                            </div>

                            <!-- Frame Tab -->
                            <div class="tab-pane fade" id="frame-tab-pane" role="tabpanel" aria-labelledby="frame-tab" tabindex="0">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="detail-frame-style" class="form-label">Frame Style</label>
                                        <select class="form-select form-select-sm" id="detail-frame-style">
                                            <option value="none">No Frame</option>
                                            <option value="corporate">Corporate</option>
                                            <option value="phone">Mobile Phone</option>
                                            <option value="business-card">Business Card</option>
                                            <option value="ticket">Event Ticket</option>
                                            <option value="elegant">Elegant</option>
                                            <option value="modern">Modern</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="detail-frame-color" class="form-label">Frame Color</label>
                                        <input type="color" class="form-control form-control-color w-100" id="detail-frame-color" value="#4A6CF7">
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-12">
                                        <label for="detail-title" class="form-label">Title</label>
                                        <input type="text" class="form-control form-control-sm" id="detail-title" placeholder="Add a title">
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-8">
                                        <label for="detail-guiding-text" class="form-label">Guiding Text</label>
                                        <input type="text" class="form-control form-control-sm" id="detail-guiding-text" placeholder="e.g., 'Scan me'">
                                    </div>
                                    <div class="col-md-4">
                                        <label for="detail-text-position" class="form-label">Position</label>
                                        <select class="form-select form-select-sm" id="detail-text-position">
                                            <option value="below">Below</option>
                                            <option value="above">Above</option>
                                            <option value="left">Left</option>
                                            <option value="right">Right</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Logo Tab -->
                            <div class="tab-pane fade" id="logo-tab-pane" role="tabpanel" aria-labelledby="logo-tab" tabindex="0">
                                <div class="mb-3">
                                    <label for="detail-logo-upload" class="form-label">Upload Logo</label>
                                    <input type="file" class="form-control form-control-sm" id="detail-logo-upload" accept="image/*">
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="detail-logo-size" class="form-label">Logo Size</label>
                                        <select class="form-select form-select-sm" id="detail-logo-size">
                                            <option value="small">Small</option>
                                            <option value="medium" selected>Medium</option>
                                            <option value="large">Large</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="detail-logo-shape" class="form-label">Logo Shape</label>
                                        <select class="form-select form-select-sm" id="detail-logo-shape">
                                            <option value="square">Square</option>
                                            <option value="circle" selected>Circle</option>
                                            <option value="rounded">Rounded</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="detail-logo-remove-background">
                                    <label class="form-check-label" for="detail-logo-remove-background">
                                        Remove Logo Background (for PNG logos)
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end mt-3">
                            <button class="btn btn-sm btn-outline-secondary me-2" id="reset-customization">
                                <i class="fas fa-undo me-1"></i>Reset
                            </button>
                            <button class="btn btn-sm btn-primary" id="apply-customization">
                                <i class="fas fa-check me-1"></i>Apply Changes
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white">
                    <h2 class="h5 mb-0">QR Code Details</h2>
                </div>
                <div class="card-body">
                    <table class="table">
                        <tbody>
                            <tr>
                                <th>Name</th>
                                <td>{{ qr_code.name }}</td>
                            </tr>
                            <tr>
                                <th>Type</th>
                                <td><span class="badge bg-info">{{ qr_code.get_qr_type_display }}</span></td>
                            </tr>
                            <tr>
                                <th>Data</th>
                                <td>
                                    <div class="data-display">
                                        {% if qr_code.qr_type == 'URL' %}
                                            <a href="{{ qr_code.data }}" target="_blank">{{ qr_code.data }}</a>
                                        {% elif qr_code.qr_type == 'PDF' or qr_code.qr_type == 'DOCUMENT' %}
                                            {% if qr_code.uploaded_file %}
                                                <div class="mb-2">
                                                    <a href="{{ qr_code.uploaded_file.url }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-file-pdf me-1"></i>View Document
                                                    </a>
                                                </div>
                                                <small class="text-muted">{{ qr_code.file_name }} ({{ qr_code.file_size|filesizeformat }})</small>
                                            {% endif %}
                                            <div>{{ qr_code.data }}</div>
                                        {% elif qr_code.qr_type == 'IMAGE' %}
                                            {% if qr_code.uploaded_file %}
                                                <div class="mb-2">
                                                    <img src="{{ qr_code.uploaded_file.url }}" alt="{{ qr_code.name }}" class="img-fluid img-thumbnail" style="max-height: 150px;">
                                                </div>
                                                <small class="text-muted">{{ qr_code.file_name }} ({{ qr_code.file_size|filesizeformat }})</small>
                                            {% endif %}
                                            <div>{{ qr_code.data }}</div>
                                        {% elif qr_code.qr_type == 'AUDIO' %}
                                            {% if qr_code.uploaded_file %}
                                                <div class="mb-2">
                                                    <audio controls class="w-100">
                                                        <source src="{{ qr_code.uploaded_file.url }}" type="{{ qr_code.file_type }}">
                                                        Your browser does not support the audio element.
                                                    </audio>
                                                </div>
                                                <small class="text-muted">{{ qr_code.file_name }} ({{ qr_code.file_size|filesizeformat }})</small>
                                            {% endif %}
                                            <div>{{ qr_code.data }}</div>
                                        {% elif qr_code.qr_type == 'VIDEO' %}
                                            {% if qr_code.uploaded_file %}
                                                <div class="mb-2">
                                                    <video controls class="w-100" style="max-height: 200px;">
                                                        <source src="{{ qr_code.uploaded_file.url }}" type="{{ qr_code.file_type }}">
                                                        Your browser does not support the video element.
                                                    </video>
                                                </div>
                                                <small class="text-muted">{{ qr_code.file_name }} ({{ qr_code.file_size|filesizeformat }})</small>
                                            {% endif %}
                                            <div>{{ qr_code.data }}</div>
                                        {% else %}
                                            {{ qr_code.data }}
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <th>Created</th>
                                <td>{{ qr_code.created_at }}</td>
                            </tr>
                            <tr>
                                <th>Last Updated</th>
                                <td>{{ qr_code.updated_at }}</td>
                            </tr>
                            <tr>
                                <th>Colors</th>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="color-swatch me-2" style="background-color: {{ qr_code.foreground_color }};"></div>
                                        <span>{{ qr_code.foreground_color }}</span>
                                        <div class="mx-2">/</div>
                                        <div class="color-swatch me-2" style="background-color: {{ qr_code.background_color }};"></div>
                                        <span>{{ qr_code.background_color }}</span>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <th>Encrypted</th>
                                <td>
                                    {% if qr_code.is_encrypted %}
                                        <span class="badge bg-success">Yes</span>
                                    {% else %}
                                        <span class="badge bg-secondary">No</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h2 class="h5 mb-0">Analytics</h2>
                    {% if is_premium %}
                    <span class="badge bg-primary">Premium Feature</span>
                    {% endif %}
                </div>
                <div class="card-body">
                    {% if is_premium %}
                        <div class="row mb-4">
                            <div class="col-md-4 text-center">
                                <div class="analytics-card p-3 rounded bg-light">
                                    <h3 class="h2 text-primary mb-0">{{ analytics.total_scans }}</h3>
                                    <p class="text-muted mb-0">Total Scans</p>
                                </div>
                            </div>
                            <div class="col-md-4 text-center">
                                <div class="analytics-card p-3 rounded bg-light">
                                    <h3 class="h2 text-primary mb-0">{{ analytics.unique_scans }}</h3>
                                    <p class="text-muted mb-0">Unique Visitors</p>
                                </div>
                            </div>
                            <div class="col-md-4 text-center">
                                <div class="analytics-card p-3 rounded bg-light">
                                    <h3 class="h2 text-primary mb-0">{{ analytics.last_scanned|date:"M d" }}</h3>
                                    <p class="text-muted mb-0">Last Scanned</p>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <h4 class="h6 mb-3">Scan Activity</h4>
                            <div class="chart-container" style="position: relative; height:200px;">
                                <canvas id="scanChart"></canvas>
                            </div>
                        </div>

                        <!-- Geographic Analytics -->
                        <div class="mb-4">
                            <h4 class="h6 mb-3">Geographic Distribution</h4>
                            {% if geo_analytics.countries %}
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="text-muted mb-2">Top Countries</h6>
                                        {% for country, count in geo_analytics.countries.items|slice:":5" %}
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span>{{ country }}</span>
                                            <span class="badge bg-primary">{{ count }}</span>
                                        </div>
                                        {% endfor %}
                                    </div>
                                    <div class="col-md-6">
                                        <h6 class="text-muted mb-2">Top Cities</h6>
                                        {% for city, count in geo_analytics.cities.items|slice:":5" %}
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span>{{ city }}</span>
                                            <span class="badge bg-info">{{ count }}</span>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            {% else %}
                                <p class="text-muted">No geographic data available yet.</p>
                            {% endif %}
                        </div>

                        <!-- Scanner Type Analytics -->
                        <div class="mb-4">
                            <h4 class="h6 mb-3">Scanner Types</h4>
                            {% if scanner_analytics %}
                                <div class="row">
                                    {% for scanner_type, count in scanner_analytics.items %}
                                    <div class="col-md-4 mb-2">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span>
                                                {% if scanner_type == 'native_camera' %}
                                                    <i class="fas fa-camera text-success me-1"></i>Native Camera
                                                {% elif scanner_type == 'third_party_app' %}
                                                    <i class="fas fa-mobile-alt text-warning me-1"></i>Scanner App
                                                {% else %}
                                                    <i class="fas fa-question text-muted me-1"></i>Unknown
                                                {% endif %}
                                            </span>
                                            <span class="badge bg-secondary">{{ count }}</span>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            {% else %}
                                <p class="text-muted">No scanner data available yet.</p>
                            {% endif %}
                        </div>

                        {% if recent_scans %}
                        <div>
                            <h4 class="h6 mb-3">Recent Scans</h4>
                            <div class="table-responsive">
                                <table class="table table-sm table-hover">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Location</th>
                                            <th>Device</th>
                                            <th>Scanner</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for scan in recent_scans %}
                                        <tr>
                                            <td>{{ scan.scanned_at|date:"M d, Y H:i" }}</td>
                                            <td>{{ scan.get_location_display }}</td>
                                            <td>{{ scan.device_type|default:"Unknown" }}</td>
                                            <td>
                                                {% if scan.scanner_type == 'native_camera' %}
                                                    <i class="fas fa-camera text-success" title="Native Camera"></i>
                                                {% elif scan.scanner_type == 'third_party_app' %}
                                                    <i class="fas fa-mobile-alt text-warning" title="Scanner App"></i>
                                                {% else %}
                                                    <i class="fas fa-question text-muted" title="Unknown"></i>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> No recent scan data available yet.
                        </div>
                        {% endif %}

                        <div class="text-end mt-3">
                            <a href="{% url 'advanced_analytics' %}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-chart-line me-1"></i>View Detailed Analytics
                            </a>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <div class="mb-3">
                                <i class="fas fa-chart-bar fa-3x text-muted"></i>
                            </div>
                            <h4 class="h5 mb-3">Analytics are available for premium users</h4>
                            <p class="text-muted mb-4">Upgrade to premium to access detailed analytics about your QR code scans, including:</p>

                            <div class="row mb-4">
                                <div class="col-md-4">
                                    <div class="feature-item">
                                        <i class="fas fa-chart-line text-primary mb-2"></i>
                                        <h5 class="h6">Scan Statistics</h5>
                                        <p class="small text-muted">Track total and unique scans</p>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="feature-item">
                                        <i class="fas fa-globe text-primary mb-2"></i>
                                        <h5 class="h6">Geographic Data</h5>
                                        <p class="small text-muted">See where your QR codes are scanned</p>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="feature-item">
                                        <i class="fas fa-mobile-alt text-primary mb-2"></i>
                                        <h5 class="h6">Device Insights</h5>
                                        <p class="small text-muted">Learn which devices are used</p>
                                    </div>
                                </div>
                            </div>

                            <a href="{% url 'pricing' %}" class="btn btn-primary">
                                <i class="fas fa-crown me-1"></i>Upgrade to Premium
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- QR Pro Features -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h2 class="h5 mb-0">
                        <i class="fas fa-crown text-warning me-2"></i>QR Pro Features
                    </h2>
                    <span class="badge bg-gradient-primary">New</span>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- AI Landing Pages -->
                        <div class="col-md-6 mb-3">
                            <div class="pro-feature-card h-100">
                                <div class="pro-feature-icon">
                                    <i class="fas fa-magic text-primary"></i>
                                </div>
                                <h5 class="pro-feature-title">AI Landing Page</h5>
                                <p class="pro-feature-description">Create beautiful, professional landing pages with AI instead of simple redirects.</p>

                                {% if qr_code.ai_landing_page %}
                                <div class="mb-2">
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i>Active
                                    </span>
                                    <small class="text-muted ms-2">{{ qr_code.ai_landing_page.view_count }} views</small>
                                </div>
                                <div class="btn-group w-100">
                                    <a href="{{ qr_code.ai_landing_page.get_absolute_url }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye me-1"></i>Preview
                                    </a>
                                    <a href="{% url 'edit_ai_landing_page' qr_code.id %}" class="btn btn-sm btn-outline-secondary">
                                        <i class="fas fa-edit me-1"></i>Edit
                                    </a>
                                </div>
                                {% else %}
                                <a href="{% url 'create_ai_landing_page' qr_code.id %}" class="btn btn-primary btn-sm w-100">
                                    <i class="fas fa-magic me-1"></i>Create AI Landing Page
                                </a>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Dynamic Redirects -->
                        <div class="col-md-6 mb-3">
                            <div class="pro-feature-card h-100">
                                <div class="pro-feature-icon">
                                    <i class="fas fa-exchange-alt text-success"></i>
                                </div>
                                <h5 class="pro-feature-title">Dynamic Redirect</h5>
                                <p class="pro-feature-description">Change where this QR code redirects without reprinting it.</p>

                                {% if qr_code.short_code %}
                                <div class="mb-2">
                                    <span class="badge bg-info">
                                        <i class="fas fa-link me-1"></i>{{ qr_code.short_code }}
                                    </span>
                                    <small class="text-muted ms-2">Short URL ready</small>
                                </div>
                                {% endif %}

                                <a href="{% url 'edit_qr_redirect' qr_code.id %}" class="btn btn-success btn-sm w-100">
                                    <i class="fas fa-edit me-1"></i>Edit Redirect
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Pro Dashboard Link -->
                    <div class="text-center mt-3 pt-3 border-top">
                        <a href="{% url 'monetization_dashboard' %}" class="btn btn-outline-primary">
                            <i class="fas fa-tachometer-alt me-1"></i>View Pro Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- Delete QR Code Modal -->
<div class="modal fade" id="deleteQRModal" tabindex="-1" aria-labelledby="deleteQRModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteQRModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>Delete QR Code
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <h6 class="alert-heading">
                        <i class="fas fa-exclamation-circle me-2"></i>Warning
                    </h6>
                    <p>
                        Are you sure you want to delete the QR code "<strong>{{ qr_code.name }}</strong>"?
                        This action cannot be undone.
                    </p>
                </div>

                <div class="row mb-3">
                    <div class="col-md-4 text-center">
                        {% if qr_code.image %}<img src="{{ qr_code.image.url }}" alt="{{ qr_code.name }}" class="img-fluid img-thumbnail" style="max-width: 100px;">{% else %}<div class="text-muted text-center p-2" style="max-width: 100px; border: 1px dashed #ccc;"><i class="fas fa-image"></i><br><small>No image</small></div>{% endif %}
                    </div>
                    <div class="col-md-8">
                        <h6>QR Code Details</h6>
                        <table class="table table-sm">
                            <tr>
                                <th>Name:</th>
                                <td>{{ qr_code.name }}</td>
                            </tr>
                            <tr>
                                <th>Type:</th>
                                <td>{{ qr_code.get_qr_type_display }}</td>
                            </tr>
                            <tr>
                                <th>Created:</th>
                                <td>{{ qr_code.created_at|date:"F j, Y" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary modal-close-btn" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancel
                </button>
                <button type="button" class="btn btn-danger" id="confirm-delete-qr">
                    <i class="fas fa-trash me-1"></i>Delete QR Code
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal backdrop fix -->
<style>
    /* Ensure modal backdrop is properly removed */
    .modal-backdrop {
        z-index: 1040 !important;
    }
    .modal {
        z-index: 1050 !important;
    }
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/qr-code-styling@1.6.0-rc.1/lib/qr-code-styling.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="{% static 'js/qr-code-detail.js' %}"></script>
<script>
    // Initialize the analytics chart if user is premium
    {% if is_premium %}
    document.addEventListener('DOMContentLoaded', function() {
        const ctx = document.getElementById('scanChart').getContext('2d');

        // Sample data for the chart (in a real app, this would come from the backend)
        const scanData = {
            labels: ['7 days ago', '6 days ago', '5 days ago', '4 days ago', '3 days ago', '2 days ago', 'Yesterday', 'Today'],
            datasets: [{
                label: 'Daily Scans',
                data: [12, 19, 8, 15, 20, 14, 25, 12],
                backgroundColor: 'rgba(74, 108, 247, 0.2)',
                borderColor: 'rgba(74, 108, 247, 1)',
                borderWidth: 2,
                tension: 0.4,
                fill: true
            }]
        };

        const scanChart = new Chart(ctx, {
            type: 'line',
            data: scanData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        backgroundColor: 'rgba(0, 0, 0, 0.7)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        titleFont: {
                            size: 14
                        },
                        bodyFont: {
                            size: 13
                        },
                        padding: 10,
                        displayColors: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        },
                        ticks: {
                            precision: 0
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    });
    {% endif %}

    // Initialize the delete modal functionality directly in the template
    document.addEventListener('DOMContentLoaded', function() {
        try {
            // Copy link functionality
            const copyLinkBtn = document.getElementById('copyLink');
            if (copyLinkBtn) {
                copyLinkBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const url = this.getAttribute('data-url');

                    // Create a temporary input element
                    const tempInput = document.createElement('input');
                    tempInput.value = url;
                    document.body.appendChild(tempInput);

                    // Select and copy the text
                    tempInput.select();
                    document.execCommand('copy');

                    // Remove the temporary element
                    document.body.removeChild(tempInput);

                    // Show feedback
                    const originalText = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-check me-2"></i>Link Copied!';

                    // Reset after 2 seconds
                    setTimeout(() => {
                        this.innerHTML = originalText;
                    }, 2000);
                });
            }

            // Get elements
            const deleteBtn = document.getElementById('delete-qr-btn');
            const confirmDeleteBtn = document.getElementById('confirm-delete-qr');
            const deleteForm = document.getElementById('delete-qr-form');
            const modalElement = document.getElementById('deleteQRModal');
            const modalCloseBtn = document.querySelector('.modal-close-btn');

            // Make sure the modal element exists
            if (!modalElement) {
                console.error('Modal element not found');
                return;
            }

            // Initialize the modal with specific options
            const deleteModal = new bootstrap.Modal(modalElement, {
                backdrop: 'static',  // Prevents closing when clicking outside
                keyboard: false      // Prevents closing with the keyboard
            });

            // Delete button click handler
            if (deleteBtn) {
                deleteBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    // Show the modal
                    deleteModal.show();
                });
            }

            // Confirm delete button click handler
            if (confirmDeleteBtn) {
                confirmDeleteBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (deleteForm) {
                        // Hide the modal first
                        deleteModal.hide();
                        // Remove backdrop manually
                        $('.modal-backdrop').remove();
                        // Fix body
                        $('body').removeClass('modal-open').css('overflow', '').css('padding-right', '');

                        // Then submit the form after a short delay
                        setTimeout(() => {
                            deleteForm.submit();
                        }, 300);
                    }
                });
            }

            // Close button handler
            if (modalCloseBtn) {
                modalCloseBtn.addEventListener('click', function() {
                    // Hide the modal
                    deleteModal.hide();
                    // Remove backdrop manually
                    $('.modal-backdrop').remove();
                    // Fix body
                    $('body').removeClass('modal-open').css('overflow', '').css('padding-right', '');
                });
            }

            // Handle modal hidden event
            $(modalElement).on('hidden.bs.modal', function() {
                // Remove backdrop manually
                $('.modal-backdrop').remove();
                // Fix body
                $('body').removeClass('modal-open').css('overflow', '').css('padding-right', '');
            });

            // Add a direct close method to the close button
            $('.btn-close').on('click', function() {
                // Hide the modal
                deleteModal.hide();
                // Remove backdrop manually
                $('.modal-backdrop').remove();
                // Fix body
                $('body').removeClass('modal-open').css('overflow', '').css('padding-right', '');
            });

        } catch (error) {
            console.error('Error initializing delete modal:', error);
        }

        // Print QR code functionality
        const printQrBtn = document.getElementById('print-qr');
        if (printQrBtn) {
            printQrBtn.addEventListener('click', function() {
                const qrImage = document.querySelector('#qr-display img');
                const qrName = "{{ qr_code.name }}";

                if (qrImage) {
                    // Create a new window for printing
                    const printWindow = window.open('', '_blank');

                    // Create print-friendly content
                    printWindow.document.write(`
                        <html>
                        <head>
                            <title>Print QR Code - ${qrName}</title>
                            <style>
                                body {
                                    font-family: Arial, sans-serif;
                                    text-align: center;
                                    padding: 20px;
                                }
                                .qr-container {
                                    max-width: 500px;
                                    margin: 0 auto;
                                    padding: 20px;
                                }
                                .qr-title {
                                    font-size: 18px;
                                    font-weight: bold;
                                    margin-bottom: 15px;
                                }
                                .qr-image {
                                    max-width: 100%;
                                    height: auto;
                                }
                                .qr-footer {
                                    margin-top: 15px;
                                    font-size: 12px;
                                    color: #666;
                                }
                                @media print {
                                    .no-print {
                                        display: none;
                                    }
                                }
                            </style>
                        </head>
                        <body>
                            <div class="qr-container">
                                <div class="qr-title">${qrName}</div>
                                <img src="${qrImage.src}" alt="${qrName}" class="qr-image">
                                <div class="qr-footer">
                                    Generated on ${new Date().toLocaleDateString()}
                                </div>
                                <div class="no-print" style="margin-top: 30px;">
                                    <button onclick="window.print();" style="padding: 10px 20px; background: #4a6cf7; color: white; border: none; border-radius: 5px; cursor: pointer;">
                                        Print QR Code
                                    </button>
                                </div>
                            </div>
                        </body>
                        </html>
                    `);

                    // Close the document for printing
                    printWindow.document.close();

                    // Focus the new window
                    printWindow.focus();
                }
            });
        }

        // Download QR code functionality
        const downloadPreviewBtn = document.getElementById('download-preview');
        if (downloadPreviewBtn) {
            downloadPreviewBtn.addEventListener('click', function() {
                const qrImage = document.querySelector('#qr-display img');
                const qrName = "{{ qr_code.name|slugify }}";

                if (qrImage) {
                    // Create a temporary link element
                    const downloadLink = document.createElement('a');
                    downloadLink.href = qrImage.src;
                    downloadLink.download = `qrcode-${qrName}.png`;

                    // Append to body, click, and remove
                    document.body.appendChild(downloadLink);
                    downloadLink.click();
                    document.body.removeChild(downloadLink);
                }
            });
        }

        // Note: QR code customization functionality is handled in qr-code-detail.js
    });
</script>
{% endblock %}
