# AI Services

This Django app provides AI-powered services for the QR Code Generator project, including:

- AI-assisted ad content generation
- Support for multiple languages (English, Swahili, Sheng)
- Integration with Mistral AI, OpenAI, and Groq AI for high-performance inference

## Configuration

The AI services are configured using environment variables in the `.env` file:

```
# AI Provider Settings
AI_PROVIDER=mistral  # 'mistral', 'openai', 'groq', or 'local'

# Mistral AI Settings
MISTRAL_API_KEY=your_mistral_api_key_here
MISTRAL_MODEL=mistral-tiny

# OpenAI Settings
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4-turbo

# Groq AI Settings (High-speed inference)
GROQ_API_KEY=your_groq_api_key_here
GROQ_MODEL=llama3-8b-8192
GROQ_TEMPERATURE=0.7
GROQ_MAX_TOKENS=1024

# Ad Generation Settings
AD_GENERATION_TEMPERATURE=0.7
AD_GENERATION_MAX_TOKENS=150
AD_GENERATION_NUM_SUGGESTIONS=3
```

## API Endpoints

### Generate Ad Suggestions

**URL:** `/ai/generate-ad-suggestions/`

**Method:** `POST`

**Authentication:** Required

**Request Body:**
```json
{
    "language": "english",
    "business_type": "Enterprise Software",
    "target_audience": "Business Professionals",
    "tone": "professional"
}
```

**Response:**
```json
{
    "success": true,
    "suggestions": [
        {
            "title": "Premium Enterprise Solutions for Modern Businesses",
            "content": "Transform your business operations with our cutting-edge enterprise solutions. Designed for scalability, security, and performance. Book a demo today!"
        },
        {
            "title": "Streamline Your Workflow with Smart Technology",
            "content": "Reduce operational costs and boost productivity with our intelligent workflow solutions. Join thousands of satisfied businesses worldwide."
        },
        {
            "title": "Next-Generation Business Intelligence Tools",
            "content": "Make data-driven decisions with confidence using our advanced analytics platform. Unlock insights that drive growth and innovation."
        }
    ]
}
```

## Frontend Integration

The AI services are integrated with the ad creation form in the `smart-engine.js` file. When the user clicks the "Generate Suggestions" button, the JavaScript makes an API call to the `/ai/generate-ad-suggestions/` endpoint and displays the results.

## Supported Languages

- English
- Swahili
- Sheng (Kenyan slang)

## Models

### Mistral AI

- `mistral-tiny`: Free tier model for testing

### OpenAI

- `gpt-4-turbo`: Production model for high-quality content generation
