from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.db.models import Sum, Count
from django.utils import timezone
from django.contrib.auth.models import User
from django.contrib import messages
from datetime import datetime, timedelta
import json
from .models import Ad, AdAnalytics, Transaction, AdType, Campaign

@login_required
def analytics_enterprise(request):
    """
    Enterprise analytics dashboard with enhanced features
    """
    # Handle POST requests (e.g., for scheduling reports)
    if request.method == 'POST':
        action = request.POST.get('action')
        if action == 'schedule_report':
            # Process report scheduling (would be implemented in a real system)
            report_name = request.POST.get('report_name')
            frequency = request.POST.get('frequency')
            report_format = request.POST.get('format')
            email = request.POST.get('email')

            # In a real implementation, save this to the database
            messages.success(request, f"Report '{report_name}' scheduled successfully. It will be sent {frequency} in {report_format} format to {email}.")
            return redirect('ads:analytics_enterprise')

    # Handle export requests
    export_format = request.GET.get('export')
    if export_format in ['csv', 'excel', 'pdf']:
        # In a real implementation, generate the appropriate file
        messages.info(request, f"Your analytics data is being exported in {export_format.upper()} format. You'll receive it shortly.")
        return redirect('ads:analytics_enterprise')

    # Get filter parameters
    start_date_str = request.GET.get('start_date')
    end_date_str = request.GET.get('end_date')
    campaign_id = request.GET.get('campaign')
    ad_type_id = request.GET.get('ad_type')
    date_range = request.GET.get('date_range')

    # Parse custom date range if provided
    custom_start_date = None
    custom_end_date = None
    if date_range and ' - ' in date_range:
        try:
            date_parts = date_range.split(' - ')
            custom_start_date = datetime.strptime(date_parts[0], '%b %d, %Y').date()
            custom_end_date = datetime.strptime(date_parts[1], '%b %d, %Y').date()
        except (ValueError, IndexError):
            # If parsing fails, fall back to default period
            pass

    # Default to last 30 days if no date range is provided
    today = timezone.now().date()
    if start_date_str and end_date_str:
        try:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
        except ValueError:
            start_date = today - timezone.timedelta(days=30)
            end_date = today
    elif custom_start_date and custom_end_date:
        start_date = custom_start_date
        end_date = custom_end_date
    else:
        start_date = today - timezone.timedelta(days=30)
        end_date = today

    # Get previous period for trend calculation
    period_length = (end_date - start_date).days
    prev_start_date = start_date - timezone.timedelta(days=period_length)
    prev_end_date = start_date - timezone.timedelta(days=1)

    # Get user's ads with filters
    user_ads = Ad.objects.filter(user=request.user)

    # Apply campaign filter if provided
    if campaign_id:
        try:
            user_ads = user_ads.filter(campaign_id=int(campaign_id))
        except (ValueError, TypeError):
            pass

    # Apply ad type filter if provided
    if ad_type_id:
        try:
            user_ads = user_ads.filter(ad_type_id=int(ad_type_id))
        except (ValueError, TypeError):
            pass

    # Get counts for different statuses
    active_ads_count = user_ads.filter(status='active').count()

    # Get analytics data for the current period
    current_analytics = AdAnalytics.objects.filter(
        ad__in=user_ads,
        date__gte=start_date,
        date__lte=end_date
    )

    # Get analytics data for the previous period
    previous_analytics = AdAnalytics.objects.filter(
        ad__in=user_ads,
        date__gte=prev_start_date,
        date__lte=prev_end_date
    )

    # Calculate metrics for current period
    total_impressions = current_analytics.aggregate(Sum('impressions'))['impressions__sum'] or 0
    total_clicks = current_analytics.aggregate(Sum('clicks'))['clicks__sum'] or 0
    total_conversions = current_analytics.aggregate(Sum('conversion_count'))['conversion_count__sum'] or 0
    ctr = (total_clicks / total_impressions * 100) if total_impressions > 0 else 0
    conversion_rate = (total_conversions / total_clicks * 100) if total_clicks > 0 else 0

    # Calculate metrics for previous period
    prev_impressions = previous_analytics.aggregate(Sum('impressions'))['impressions__sum'] or 0
    prev_clicks = previous_analytics.aggregate(Sum('clicks'))['clicks__sum'] or 0
    prev_conversions = previous_analytics.aggregate(Sum('conversion_count'))['conversion_count__sum'] or 0
    prev_ctr = (prev_clicks / prev_impressions * 100) if prev_impressions > 0 else 0
    prev_conversion_rate = (prev_conversions / prev_clicks * 100) if prev_clicks > 0 else 0

    # Calculate trends
    impression_trend = calculate_trend(total_impressions, prev_impressions)
    click_trend = calculate_trend(total_clicks, prev_clicks)
    ctr_trend = calculate_trend(ctr, prev_ctr)
    conversion_trend = calculate_trend(total_conversions, prev_conversions)
    conversion_rate_trend = calculate_trend(conversion_rate, prev_conversion_rate)

    # Get top performing ads with detailed metrics
    top_ads = user_ads.order_by('-clicks')[:10]

    # Get top converting ads
    top_converting_ads = user_ads.filter(conversion_count__gt=0).order_by('-conversion_count')[:10]

    # Add status color and metrics for each ad
    for ad in top_ads:
        ad.status_color = get_status_color(ad.status)
        ad.ctr = (ad.clicks / ad.impressions * 100) if ad.impressions > 0 else 0

    # Add conversion rate and status color for top converting ads
    for ad in top_converting_ads:
        ad.status_color = get_status_color(ad.status)
        ad.conversion_rate = (ad.conversion_count / ad.clicks * 100) if ad.clicks > 0 else 0

    # Add admin-specific data for superusers
    if request.user.is_superuser:
        # Get total user count
        user_count = User.objects.count()

        # Get total ads count (all users)
        all_ads_count = Ad.objects.count()

        # Get pending approval count
        pending_approval_count = Ad.objects.filter(status='pending').count()

        # Get total revenue
        total_revenue = Transaction.objects.filter(status='approved').aggregate(Sum('amount'))['amount__sum'] or 0

        # Get system-wide analytics
        system_impressions = AdAnalytics.objects.filter(
            date__gte=start_date,
            date__lte=end_date
        ).aggregate(Sum('impressions'))['impressions__sum'] or 0

        system_clicks = AdAnalytics.objects.filter(
            date__gte=start_date,
            date__lte=end_date
        ).aggregate(Sum('clicks'))['clicks__sum'] or 0

        system_ctr = (system_clicks / system_impressions * 100) if system_impressions > 0 else 0

        # Add to context
        admin_context = {
            'user_count': user_count,
            'all_ads_count': all_ads_count,
            'pending_approval_count': pending_approval_count,
            'total_revenue': total_revenue,
            'system_impressions': system_impressions,
            'system_clicks': system_clicks,
            'system_ctr': system_ctr
        }
    else:
        admin_context = {}

    # Get chart data
    chart_data = prepare_chart_data(current_analytics, start_date, end_date)

    # Get device and location data
    device_data, location_data, browser_data = get_demographic_data(current_analytics)

    # Check if we have real data or empty data
    has_device_data = bool(sum(device_data.values()) > 0)
    has_location_data = bool(location_data)
    has_browser_data = bool(sum(browser_data.values()) > 0)

    # Get user's campaigns
    campaigns = []
    if hasattr(request.user, 'campaigns'):
        campaigns = request.user.campaigns.all()
    else:
        # If the user doesn't have a direct relationship to campaigns, try to get them through ads
        campaign_ids = user_ads.values_list('campaign_id', flat=True).distinct()
        campaigns = Campaign.objects.filter(id__in=campaign_ids)

    # Get campaign data for comparison chart
    campaign_data = prepare_campaign_data(campaigns, start_date, end_date)

    # Get all ad types for filter dropdown
    ad_types = AdType.objects.filter(is_active=True)

    # Format date range for display
    formatted_date_range = f"{start_date.strftime('%b %d, %Y')} - {end_date.strftime('%b %d, %Y')}"

    context = {
        'active_ads_count': active_ads_count,
        'total_impressions': total_impressions,
        'total_clicks': total_clicks,
        'total_conversions': total_conversions,
        'ctr': ctr,
        'impression_trend': impression_trend,
        'click_trend': click_trend,
        'ctr_trend': ctr_trend,
        'conversion_trend': conversion_trend,
        'conversion_rate': conversion_rate,
        'conversion_rate_trend': conversion_rate_trend,
        'top_ads': top_ads,
        'top_converting_ads': top_converting_ads,
        'start_date': start_date,
        'end_date': end_date,
        'date_range': formatted_date_range,
        'campaigns': campaigns,
        'ad_types': ad_types,
        'selected_campaign': campaign_id,
        'selected_ad_type': ad_type_id,
        'chart_data_json': json.dumps(chart_data),
        'device_data_json': json.dumps(device_data),
        'location_data_json': json.dumps(location_data),
        'browser_data_json': json.dumps(browser_data),
        'campaign_data_json': json.dumps(campaign_data),
        'has_device_data': has_device_data,
        'has_location_data': has_location_data,
        'has_browser_data': has_browser_data,
        **admin_context
    }

    return render(request, 'ads/analytics_enterprise.html', context)

# Helper functions
def calculate_trend(current, previous):
    """
    Calculate percentage change between current and previous values
    """
    if previous == 0:
        return 100 if current > 0 else 0

    change = ((current - previous) / previous) * 100
    return round(change, 1)

def get_status_color(status):
    """
    Return the appropriate color class for a status
    """
    status_colors = {
        'active': 'active',
        'pending': 'pending',
        'draft': 'draft',
        'paused': 'paused',
        'rejected': 'rejected',
        'expired': 'expired',
        'completed': 'completed'
    }

    return status_colors.get(status, 'secondary')

def prepare_chart_data(analytics_data, start_date, end_date):
    """
    Prepare chart data for the date range
    """
    # Initialize chart data structure
    chart_data = {
        'labels': [],
        'impressions': [],
        'clicks': [],
        'ctr': [],
        'conversions': []
    }

    # Check if we have any analytics data
    has_data = analytics_data.exists()

    # Group data by date
    date_data = {}

    if has_data:
        for data in analytics_data:
            date_str = data.date.strftime('%Y-%m-%d')
            if date_str not in date_data:
                date_data[date_str] = {'impressions': 0, 'clicks': 0, 'conversions': 0}

            date_data[date_str]['impressions'] += data.impressions
            date_data[date_str]['clicks'] += data.clicks
            date_data[date_str]['conversions'] += data.conversion_count

    # Fill in data for each day in the range
    current_date = start_date
    while current_date <= end_date:
        date_str = current_date.strftime('%Y-%m-%d')
        display_date = current_date.strftime('%b %d')

        chart_data['labels'].append(display_date)

        if date_str in date_data:
            impressions = date_data[date_str]['impressions']
            clicks = date_data[date_str]['clicks']
            conversions = date_data[date_str]['conversions']
        else:
            impressions = 0
            clicks = 0
            conversions = 0

        chart_data['impressions'].append(impressions)
        chart_data['clicks'].append(clicks)
        chart_data['conversions'].append(conversions)

        # Calculate CTR
        if impressions > 0:
            ctr_value = (clicks / impressions) * 100
        else:
            ctr_value = 0

        chart_data['ctr'].append(round(ctr_value, 2))

        current_date += timedelta(days=1)

    return chart_data

def get_demographic_data(analytics_data):
    """
    Extract device, location, and browser data from analytics
    """
    device_data = {}
    location_data = {}
    browser_data = {}

    for data in analytics_data:
        # Process device data
        if data.device_data:
            for device, count in data.device_data.items():
                if device in device_data:
                    device_data[device] += count
                else:
                    device_data[device] = count

        # Process location data
        if data.location_data:
            for location, count in data.location_data.items():
                # Skip 'unknown' locations if there are other locations available
                if location.lower() == 'unknown' and len(data.location_data) > 1:
                    continue

                if location in location_data:
                    location_data[location] += count
                else:
                    location_data[location] = count

        # Process browser data
        if hasattr(data, 'browser_data') and data.browser_data:
            for browser, count in data.browser_data.items():
                if browser in browser_data:
                    browser_data[browser] += count
                else:
                    browser_data[browser] = count

    # If no device data is available, provide empty structure
    if not device_data:
        device_data = {
            'desktop': 0,
            'mobile': 0,
            'tablet': 0
        }

    # If no browser data is available, provide empty structure
    if not browser_data:
        browser_data = {
            'Chrome': 0,
            'Firefox': 0,
            'Safari': 0,
            'Edge': 0,
            'Other': 0
        }

    # If no location data is available, provide empty structure
    if not location_data:
        location_data = {}
    else:
        # Sort and limit location data to top 10 (instead of 5)
        sorted_locations = sorted(location_data.items(), key=lambda x: x[1], reverse=True)[:10]
        location_data = dict(sorted_locations)

    return device_data, location_data, browser_data

def prepare_campaign_data(campaigns, start_date, end_date):
    """
    Prepare data for campaign comparison chart
    """
    campaign_data = {
        'labels': [],
        'impressions': [],
        'clicks': [],
        'ctr': [],
        'conversions': []
    }

    # If no campaigns, return empty data structure
    if not campaigns:
        return campaign_data

    for campaign in campaigns:
        campaign_data['labels'].append(campaign.name)

        # Get analytics for this campaign's ads during the period
        try:
            campaign_ads = campaign.ads.all()

            # If no ads in campaign, add zeros and continue
            if not campaign_ads.exists():
                campaign_data['impressions'].append(0)
                campaign_data['clicks'].append(0)
                campaign_data['conversions'].append(0)
                campaign_data['ctr'].append(0)
                continue

            campaign_analytics = AdAnalytics.objects.filter(
                ad__in=campaign_ads,
                date__gte=start_date,
                date__lte=end_date
            )

            # Calculate metrics
            impressions = campaign_analytics.aggregate(Sum('impressions'))['impressions__sum'] or 0
            clicks = campaign_analytics.aggregate(Sum('clicks'))['clicks__sum'] or 0
            conversions = campaign_analytics.aggregate(Sum('conversion_count'))['conversion_count__sum'] or 0

            campaign_data['impressions'].append(impressions)
            campaign_data['clicks'].append(clicks)
            campaign_data['conversions'].append(conversions)

            # Calculate CTR
            if impressions > 0:
                ctr = (clicks / impressions) * 100
            else:
                ctr = 0

            campaign_data['ctr'].append(round(ctr, 2))

        except Exception as e:
            # If any error occurs, add zeros for this campaign
            print(f"Error processing campaign {campaign.name}: {str(e)}")
            campaign_data['impressions'].append(0)
            campaign_data['clicks'].append(0)
            campaign_data['conversions'].append(0)
            campaign_data['ctr'].append(0)

    return campaign_data
