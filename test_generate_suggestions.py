"""
Test script for the generate_suggestions endpoint
"""
import os
import sys
import django
import json
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'qrcode_project.settings')
django.setup()

# Import Django modules
from django.contrib.auth import get_user_model
from django.test.client import Client

def test_generate_suggestions():
    """Test the generate_suggestions endpoint"""
    print("Testing generate_suggestions endpoint...")
    
    try:
        # Create a test client
        client = Client()
        
        # Log in as a user
        User = get_user_model()
        username = 'peter'
        password = '2587'
        
        # Check if the user exists
        try:
            user = User.objects.get(username=username)
            print(f"User {username} exists")
        except User.DoesNotExist:
            print(f"User {username} does not exist, creating...")
            user = User.objects.create_user(username=username, password=password)
            print(f"User {username} created")
        
        # Log in
        login_successful = client.login(username=username, password=password)
        if login_successful:
            print(f"Logged in as {username}")
        else:
            print(f"Failed to log in as {username}")
            return False
        
        # Make the API request
        url = '/ads/api/generate-suggestions/'
        data = {
            'language': 'english',
            'business_type': 'mobile app',
            'target_audience': 'young professionals',
            'tone': 'professional',
            'title': 'Mobile App Development',
            'creativity': 0.7,
            'length': 'medium',
            'style': 'professional'
        }
        
        print(f"Making API request to {url} with data: {data}")
        response = client.post(url, data=json.dumps(data), content_type='application/json')
        
        # Check the response
        print(f"Response status code: {response.status_code}")
        
        if response.status_code == 200:
            response_data = json.loads(response.content)
            print(f"Response data: {json.dumps(response_data, indent=2)}")
            
            # Check if suggestions are returned
            if 'suggestions' in response_data and len(response_data['suggestions']) > 0:
                print(f"Successfully retrieved {len(response_data['suggestions'])} suggestions")
                
                # Print the suggestions
                for i, suggestion in enumerate(response_data['suggestions']):
                    print(f"\nSuggestion {i+1}:")
                    print(f"Title: {suggestion.get('title', 'No title')}")
                    print(f"Content: {suggestion.get('content', 'No content')}")
                
                return True
            else:
                print("No suggestions returned")
                return False
        else:
            print(f"API request failed with status code {response.status_code}")
            print(f"Response content: {response.content}")
            return False
    except Exception as e:
        print(f"\nError: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_generate_suggestions()
    if success:
        print("\nTest completed successfully!")
    else:
        print("\nTest failed!")
        sys.exit(1)
