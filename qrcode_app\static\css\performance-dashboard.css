/* Performance Dashboard Styles */
:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #60a5fa;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #6366f1;
    --text-dark: #1f2937;
    --text-medium: #4b5563;
    --text-light: #9ca3af;
    --bg-light: #f9fafb;
    --bg-medium: #f3f4f6;
    --border-color: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --transition: all 0.2s ease;
}

/* Dashboard Layout */
.performance-dashboard-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.performance-dashboard-header {
    margin-bottom: 2rem;
}

.performance-dashboard-header h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-dark);
    margin: 0 0 0.5rem 0;
}

.performance-dashboard-header p {
    font-size: 1rem;
    color: var(--text-medium);
    margin: 0 0 1.5rem 0;
}

.dashboard-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1.5rem;
}

.refresh-control {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.refresh-control label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-medium);
}

.refresh-control select {
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    color: var(--text-dark);
    background-color: white;
    transition: var(--transition);
}

.refresh-control select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    outline: none;
}

/* Metrics Grid */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.metric-card {
    background-color: white;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.metric-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.metric-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background-color: rgba(59, 130, 246, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: var(--primary-color);
    flex-shrink: 0;
}

.metric-content {
    flex-grow: 1;
}

.metric-content h3 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-medium);
    margin: 0 0 0.5rem 0;
}

.metric-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin: 0 0 0.25rem 0;
}

.metric-description {
    font-size: 0.75rem;
    color: var(--text-light);
    margin: 0;
}

/* Charts Container */
.charts-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(500px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.chart-card {
    background-color: white;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    padding: 1.5rem;
    border: 1px solid var(--border-color);
}

.chart-card h3 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 1rem 0;
}

.chart-container {
    height: 300px;
    position: relative;
}

/* Optimization Tools */
.optimization-tools {
    margin-bottom: 2rem;
}

.optimization-tools h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 1.5rem 0;
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.tool-card {
    background-color: white;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    overflow: hidden;
    transition: var(--transition);
}

.tool-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.tool-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.25rem 1.5rem;
    background-color: var(--bg-light);
    border-bottom: 1px solid var(--border-color);
}

.tool-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0;
}

.tool-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: rgba(59, 130, 246, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: var(--primary-color);
}

.tool-content {
    padding: 1.5rem;
}

.tool-content p {
    font-size: 0.875rem;
    color: var(--text-medium);
    margin: 0 0 1.25rem 0;
}

.tool-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
}

.tool-link:hover {
    box-shadow: 0 4px 6px -1px rgba(37, 99, 235, 0.3);
    transform: translateY(-1px);
}

.tool-controls {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-medium);
}

.form-group input,
.form-group select {
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    color: var(--text-dark);
    background-color: white;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    outline: none;
}

/* Toggle Switch */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 3rem;
    height: 1.5rem;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--text-light);
    transition: var(--transition);
    border-radius: 1.5rem;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 1.125rem;
    width: 1.125rem;
    left: 0.1875rem;
    bottom: 0.1875rem;
    background-color: white;
    transition: var(--transition);
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: var(--primary-color);
}

input:focus + .toggle-slider {
    box-shadow: 0 0 1px var(--primary-color);
}

input:checked + .toggle-slider:before {
    transform: translateX(1.5rem);
}

/* Buttons */
.primary-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.primary-btn:hover {
    box-shadow: 0 4px 6px -1px rgba(37, 99, 235, 0.3);
    transform: translateY(-1px);
}

.secondary-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background-color: white;
    color: var(--text-dark);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.secondary-btn:hover {
    background-color: var(--bg-light);
}

.danger-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background-color: white;
    color: var(--danger-color);
    border: 1px solid var(--danger-color);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.danger-btn:hover {
    background-color: rgba(239, 68, 68, 0.1);
}

/* Test Results */
.test-results {
    background-color: white;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    padding: 1.5rem;
    margin-top: 2rem;
    border: 1px solid var(--border-color);
}

.test-results h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 1.5rem 0;
}

.test-results-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 1.5rem;
}

.test-summary {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.test-metric {
    background-color: var(--bg-light);
    border-radius: var(--radius-md);
    padding: 1rem;
    text-align: center;
}

.test-metric h4 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-medium);
    margin: 0 0 0.5rem 0;
}

.test-metric p {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-dark);
    margin: 0;
}

.test-chart-container {
    height: 250px;
    position: relative;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .charts-container {
        grid-template-columns: 1fr;
    }
    
    .test-results-content {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .performance-dashboard-container {
        padding: 1.5rem;
    }
    
    .dashboard-actions {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .refresh-control {
        width: 100%;
    }
    
    .refresh-control select {
        flex-grow: 1;
    }
    
    .primary-btn {
        width: 100%;
        justify-content: center;
    }
    
    .metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .tools-grid {
        grid-template-columns: 1fr;
    }
}
