// User Management JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Handle filter form submission
    const filterForm = document.querySelector('.user-filters-form');
    if (filterForm) {
        filterForm.addEventListener('submit', function(e) {
            // Remove empty fields from the form submission
            const formElements = filterForm.elements;
            for (let i = 0; i < formElements.length; i++) {
                if (formElements[i].value === '' && formElements[i].name !== '') {
                    formElements[i].disabled = true;
                }
            }
        });
    }
    
    // Handle profile image preview
    const profileImageInput = document.querySelector('input[type="file"][name="profile_image"]');
    if (profileImageInput) {
        profileImageInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.createElement('div');
                    preview.className = 'mt-2';
                    preview.innerHTML = `
                        <div class="image-preview">
                            <img src="${e.target.result}" alt="Profile Preview" class="img-thumbnail" style="max-height: 200px;">
                        </div>
                    `;
                    
                    // Remove any existing preview
                    const existingPreview = profileImageInput.parentNode.querySelector('.image-preview');
                    if (existingPreview) {
                        existingPreview.parentNode.remove();
                    }
                    
                    profileImageInput.parentNode.appendChild(preview);
                };
                reader.readAsDataURL(file);
            }
        });
    }
    
    // Confirm user deletion
    const deleteForm = document.querySelector('.delete-form');
    if (deleteForm) {
        deleteForm.addEventListener('submit', function(e) {
            if (!confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
                e.preventDefault();
            }
        });
    }
    
    // Toggle password visibility
    const passwordFields = document.querySelectorAll('input[type="password"]');
    passwordFields.forEach(field => {
        const container = field.parentNode;
        
        // Create toggle button
        const toggleBtn = document.createElement('button');
        toggleBtn.type = 'button';
        toggleBtn.className = 'btn btn-outline-secondary password-toggle';
        toggleBtn.innerHTML = '<i class="fas fa-eye"></i>';
        toggleBtn.style.position = 'absolute';
        toggleBtn.style.right = '10px';
        toggleBtn.style.top = '50%';
        toggleBtn.style.transform = 'translateY(-50%)';
        toggleBtn.style.zIndex = '10';
        
        // Set container to relative positioning if not already
        if (getComputedStyle(container).position === 'static') {
            container.style.position = 'relative';
        }
        
        // Add toggle button to container
        container.appendChild(toggleBtn);
        
        // Add event listener to toggle button
        toggleBtn.addEventListener('click', function() {
            if (field.type === 'password') {
                field.type = 'text';
                toggleBtn.innerHTML = '<i class="fas fa-eye-slash"></i>';
            } else {
                field.type = 'password';
                toggleBtn.innerHTML = '<i class="fas fa-eye"></i>';
            }
        });
    });
});
