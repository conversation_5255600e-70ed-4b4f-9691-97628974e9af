{% extends 'base.html' %}

{% block title %}Enterprise QR Code Generator{% endblock %}

{% block content %}
<!-- Homepage Featured Ad -->
{% load ads_filters %}
{% display_homepage_featured_ad %}

<div class="enterprise-hero-section">
    <div class="hero-background">
        <div class="hero-grid-pattern"></div>
        <div class="hero-gradient-overlay"></div>
        <div class="floating-elements">
            <div class="floating-qr floating-qr-1"></div>
            <div class="floating-qr floating-qr-2"></div>
            <div class="floating-qr floating-qr-3"></div>
        </div>
    </div>

    <div class="hero-content">
        <div class="row g-5 align-items-center">
            <div class="col-lg-6 col-md-12">
                <div class="hero-text-content">
                    <div class="hero-badge-container">
                        <div class="enterprise-badge">
                            <i class="fas fa-crown"></i>
                            <span>Enterprise Grade</span>
                        </div>
                    </div>

                    <h1 class="hero-title">
                        <span class="title-line-1">Enterprise QR Code</span>
                        <span class="title-line-2">Generator</span>
                        <div class="title-underline"></div>
                    </h1>

                    <p class="hero-subtitle">Professional QR Code Solutions for Business</p>

                    <div class="hero-features">
                        <div class="feature-pill">
                            <div class="feature-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <span>Enterprise Security</span>
                        </div>
                        <div class="feature-pill">
                            <div class="feature-icon">
                                <i class="fas fa-rocket"></i>
                            </div>
                            <span>High Performance</span>
                        </div>
                        <div class="feature-pill">
                            <div class="feature-icon">
                                <i class="fas fa-certificate"></i>
                            </div>
                            <span>ISO Certified</span>
                        </div>
                    </div>

                    <p class="hero-description">Generate professional QR codes for your business needs with our enterprise-grade platform. Customize, secure, and manage your QR codes with ease.</p>

                    <div class="hero-actions flex gap-4">
                        <a href="{% url 'generate_qr_code' %}" class="btn btn-primary btn-lg icon-button">
                            <span class="icon-generate icon-white"></span>
                            <span>Generate QR Code</span>
                        </a>
                        <a href="{% url 'batch_processing' %}" class="btn btn-accent btn-lg icon-button">
                            <span class="icon-premium icon-white"></span>
                            <span>Batch Processing</span>
                            <span class="badge badge-warning ml-2">Premium</span>
                        </a>
                    </div>


                </div>
            </div>

            <div class="col-lg-6 col-md-12">
                <div class="hero-form-container">
                    <div class="form-glass-card">
                        <div class="form-header">
                            <div class="form-icon">
                                <i class="fas fa-qrcode"></i>
                            </div>
                            <h3 class="form-title">Quick QR Code Generator</h3>
                            <p class="form-subtitle">Create professional QR codes instantly</p>
                        </div>

                        <form method="post" action="{% url 'generate_qr_code' %}" class="hero-form">
                            {% csrf_token %}
                            <div class="form-group">
                                <label for="qr_type" class="form-label">
                                    <span class="icon-qr icon-inline icon-primary"></span>
                                    QR Code Type
                                </label>
                                <select class="form-select" id="qr_type" name="qr_type">
                                    <option value="URL">🌐 URL</option>
                                    <option value="TEXT">📝 Text</option>
                                    <option value="VCARD">👤 vCard</option>
                                    <option value="WIFI">📶 WiFi</option>
                                    <option value="EMAIL">📧 Email</option>
                                    <option value="PHONE">📞 Phone</option>
                                    <option value="LOCATION">📍 Location</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="data" class="form-label">
                                    <span class="icon-analytics icon-inline icon-primary"></span>
                                    Data
                                </label>
                                <input type="text" class="form-input" id="data" name="data" placeholder="Enter URL, text, or other data">
                            </div>

                            <div class="form-group">
                                <label for="name" class="form-label">
                                    <span class="icon-edit icon-inline icon-primary"></span>
                                    QR Code Name
                                </label>
                                <input type="text" class="form-input" id="name" name="name" placeholder="Enter a name for your QR code">
                            </div>

                            <button type="submit" class="btn btn-primary btn-lg w-full icon-button">
                                <span class="icon-generate icon-white"></span>
                                <span>Generate QR Code</span>
                            </button>
                        </form>

                        <div class="form-footer">
                            <div class="security-badge">
                                <i class="fas fa-lock"></i>
                                <span>256-bit SSL Encrypted</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

            <style>
                /* Enterprise Hero Section - 3D Glossy Corporate Design with Smooth Animations */
                .enterprise-hero-section {
                    position: relative;
                    min-height: 100vh;
                    background:
                        linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%),
                        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(255, 215, 0, 0.15) 0%, transparent 50%),
                        linear-gradient(45deg, #1e3c72 0%, #2a5298 50%, #667eea 100%);
                    overflow: hidden;
                    display: flex;
                    align-items: center;
                    padding: 80px 0;
                    box-shadow:
                        inset 0 0 100px rgba(255, 255, 255, 0.1),
                        inset 0 0 200px rgba(255, 215, 0, 0.05);
                    opacity: 0;
                    transform: translateY(20px);
                    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                }

                .hero-background {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    z-index: 1;
                }

                .hero-grid-pattern {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background-image:
                        linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
                        linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px);
                    background-size: 50px 50px;
                    animation: gridMove 20s linear infinite;
                }

                @keyframes gridMove {
                    0% { transform: translate(0, 0); }
                    100% { transform: translate(50px, 50px); }
                }

                .hero-gradient-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: linear-gradient(135deg,
                        rgba(102, 126, 234, 0.9) 0%,
                        rgba(118, 75, 162, 0.9) 100%);
                }

                .floating-elements {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                }

                .floating-qr {
                    position: absolute;
                    width: 80px;
                    height: 80px;
                    background:
                        linear-gradient(145deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.05) 100%),
                        linear-gradient(45deg, rgba(255, 215, 0, 0.1) 0%, transparent 50%);
                    border: 2px solid rgba(255, 255, 255, 0.3);
                    border-radius: 12px;
                    backdrop-filter: blur(15px);
                    box-shadow:
                        0 8px 32px rgba(0, 0, 0, 0.3),
                        inset 0 1px 0 rgba(255, 255, 255, 0.4),
                        inset 0 -1px 0 rgba(0, 0, 0, 0.2);
                    transform-style: preserve-3d;
                    perspective: 1000px;
                    opacity: 0;
                    transform: scale(0.5) rotate(45deg);
                    transition: all 1.2s cubic-bezier(0.34, 1.56, 0.64, 1);
                }

                .floating-qr-1 {
                    top: 20%;
                    right: 15%;
                    animation: float1 6s ease-in-out infinite;
                }

                .floating-qr-2 {
                    top: 60%;
                    right: 25%;
                    animation: float2 8s ease-in-out infinite;
                }

                .floating-qr-3 {
                    top: 40%;
                    right: 5%;
                    animation: float3 7s ease-in-out infinite;
                }

                @keyframes float1 {
                    0%, 100% {
                        transform: translateY(0px) rotateX(0deg) rotateY(0deg) rotateZ(0deg);
                        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                    }
                    25% {
                        transform: translateY(-15px) rotateX(15deg) rotateY(45deg) rotateZ(45deg);
                        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
                    }
                    50% {
                        transform: translateY(-25px) rotateX(0deg) rotateY(90deg) rotateZ(90deg);
                        box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
                    }
                    75% {
                        transform: translateY(-15px) rotateX(-15deg) rotateY(135deg) rotateZ(135deg);
                        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
                    }
                }

                @keyframes float2 {
                    0%, 100% {
                        transform: translateY(0px) rotateX(0deg) rotateY(0deg) rotateZ(0deg);
                        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                    }
                    33% {
                        transform: translateY(-20px) rotateX(-20deg) rotateY(-60deg) rotateZ(60deg);
                        box-shadow: 0 18px 45px rgba(0, 0, 0, 0.45);
                    }
                    66% {
                        transform: translateY(-35px) rotateX(10deg) rotateY(-120deg) rotateZ(120deg);
                        box-shadow: 0 25px 55px rgba(0, 0, 0, 0.55);
                    }
                }

                @keyframes float3 {
                    0%, 100% {
                        transform: translateY(0px) rotateX(0deg) rotateY(0deg) rotateZ(0deg);
                        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                    }
                    40% {
                        transform: translateY(-18px) rotateX(25deg) rotateY(30deg) rotateZ(-30deg);
                        box-shadow: 0 16px 42px rgba(0, 0, 0, 0.42);
                    }
                    80% {
                        transform: translateY(-30px) rotateX(-10deg) rotateY(60deg) rotateZ(-60deg);
                        box-shadow: 0 22px 48px rgba(0, 0, 0, 0.48);
                    }
                }

                .hero-content {
                    position: relative;
                    z-index: 2;
                    width: 100%;
                    max-width: 1200px;
                    margin: 0 auto;
                    padding: 0 20px;
                }

                .hero-text-content {
                    color: white;
                    opacity: 0;
                    transform: translateY(30px);
                    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                }

                .hero-badge-container {
                    margin-bottom: 30px;
                }

                .enterprise-badge {
                    display: inline-flex;
                    align-items: center;
                    gap: 8px;
                    background:
                        linear-gradient(145deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.05) 100%),
                        linear-gradient(45deg, rgba(255, 215, 0, 0.2) 0%, rgba(255, 165, 0, 0.1) 100%);
                    backdrop-filter: blur(15px);
                    border: 2px solid rgba(255, 215, 0, 0.4);
                    padding: 12px 20px;
                    border-radius: 30px;
                    font-size: 14px;
                    font-weight: 700;
                    color: #FFD700;
                    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                    box-shadow:
                        0 8px 25px rgba(255, 215, 0, 0.3),
                        inset 0 1px 0 rgba(255, 255, 255, 0.4),
                        inset 0 -1px 0 rgba(0, 0, 0, 0.2);
                    animation: badgeGlow 3s ease-in-out infinite;
                    transform-style: preserve-3d;
                    opacity: 0;
                    transform: translateY(20px) scale(0.9);
                    transition: all 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);
                }

                @keyframes badgeGlow {
                    0%, 100% {
                        box-shadow:
                            0 8px 25px rgba(255, 215, 0, 0.3),
                            inset 0 1px 0 rgba(255, 255, 255, 0.4),
                            inset 0 -1px 0 rgba(0, 0, 0, 0.2),
                            0 0 20px rgba(255, 215, 0, 0.4);
                        transform: translateZ(0px);
                    }
                    50% {
                        box-shadow:
                            0 12px 35px rgba(255, 215, 0, 0.5),
                            inset 0 2px 0 rgba(255, 255, 255, 0.6),
                            inset 0 -2px 0 rgba(0, 0, 0, 0.3),
                            0 0 40px rgba(255, 215, 0, 0.7);
                        transform: translateZ(5px);
                    }
                }

                .hero-title {
                    font-size: 4rem;
                    font-weight: 800;
                    line-height: 1.1;
                    margin-bottom: 20px;
                    position: relative;
                    transform-style: preserve-3d;
                    perspective: 1000px;
                    opacity: 0;
                    transform: translateY(40px);
                    transition: all 1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                }

                .title-line-1 {
                    display: block;
                    background:
                        linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%),
                        linear-gradient(45deg, rgba(255, 255, 255, 0.8) 0%, rgba(240, 240, 240, 0.6) 100%);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    background-clip: text;
                    text-shadow:
                        0 2px 4px rgba(0, 0, 0, 0.3),
                        0 4px 8px rgba(0, 0, 0, 0.2),
                        0 8px 16px rgba(0, 0, 0, 0.1);
                    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
                }

                .title-line-2 {
                    display: block;
                    background:
                        linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #FF8C00 100%),
                        linear-gradient(45deg, rgba(255, 215, 0, 0.9) 0%, rgba(255, 165, 0, 0.7) 100%);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    background-clip: text;
                    text-shadow:
                        0 3px 6px rgba(255, 140, 0, 0.4),
                        0 6px 12px rgba(255, 140, 0, 0.3),
                        0 12px 24px rgba(255, 140, 0, 0.2);
                    filter: drop-shadow(0 3px 6px rgba(255, 140, 0, 0.4));
                    animation: titleShine 3s ease-in-out infinite;
                }

                @keyframes titleShine {
                    0%, 100% {
                        filter: brightness(1) drop-shadow(0 3px 6px rgba(255, 140, 0, 0.4));
                        transform: translateZ(0px);
                    }
                    50% {
                        filter: brightness(1.3) drop-shadow(0 6px 12px rgba(255, 140, 0, 0.6));
                        transform: translateZ(3px);
                    }
                }

                .title-underline {
                    width: 100px;
                    height: 4px;
                    background: linear-gradient(90deg, #FFD700, #FFA500);
                    border-radius: 2px;
                    margin-top: 15px;
                    animation: underlineGrow 2s ease-out;
                }

                @keyframes underlineGrow {
                    0% { width: 0; }
                    100% { width: 100px; }
                }

                .hero-subtitle {
                    font-size: 1.5rem;
                    font-weight: 300;
                    margin-bottom: 30px;
                    opacity: 0;
                    transform: translateY(30px);
                    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                }

                .hero-features {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 15px;
                    margin-bottom: 30px;
                    opacity: 0;
                    transform: translateY(25px);
                    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                }

                .feature-pill {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    background:
                        linear-gradient(145deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%),
                        linear-gradient(45deg, rgba(255, 215, 0, 0.1) 0%, transparent 50%);
                    backdrop-filter: blur(15px);
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    padding: 12px 18px;
                    border-radius: 30px;
                    font-size: 14px;
                    font-weight: 600;
                    transition: all 0.3s ease;
                    box-shadow:
                        0 4px 15px rgba(0, 0, 0, 0.2),
                        inset 0 1px 0 rgba(255, 255, 255, 0.3),
                        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
                    transform-style: preserve-3d;
                }

                .feature-pill:hover {
                    background:
                        linear-gradient(145deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%),
                        linear-gradient(45deg, rgba(255, 215, 0, 0.2) 0%, transparent 50%);
                    transform: translateY(-3px) translateZ(5px);
                    box-shadow:
                        0 8px 25px rgba(0, 0, 0, 0.3),
                        inset 0 2px 0 rgba(255, 255, 255, 0.4),
                        inset 0 -2px 0 rgba(0, 0, 0, 0.2);
                }

                .feature-icon {
                    width: 20px;
                    height: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: #FFD700;
                }

                .hero-description {
                    font-size: 1.1rem;
                    line-height: 1.6;
                    margin-bottom: 40px;
                    opacity: 0;
                    transform: translateY(25px);
                    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                }

                .hero-actions {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 20px;
                    margin-bottom: 50px;
                    opacity: 0;
                    transform: translateY(30px);
                    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                }

                .btn-hero-primary {
                    display: inline-flex;
                    align-items: center;
                    gap: 10px;
                    background:
                        linear-gradient(145deg, #ffffff 0%, #f8f9fa 50%, #e9ecef 100%),
                        linear-gradient(45deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 249, 250, 0.7) 100%);
                    color: #333;
                    padding: 18px 35px;
                    border-radius: 50px;
                    text-decoration: none;
                    font-weight: 700;
                    font-size: 16px;
                    transition: all 0.3s ease;
                    box-shadow:
                        0 12px 35px rgba(0, 0, 0, 0.25),
                        inset 0 2px 0 rgba(255, 255, 255, 0.8),
                        inset 0 -2px 0 rgba(0, 0, 0, 0.1);
                    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
                    transform-style: preserve-3d;
                }

                .btn-hero-primary:hover {
                    transform: translateY(-4px) translateZ(8px);
                    box-shadow:
                        0 20px 50px rgba(0, 0, 0, 0.35),
                        inset 0 3px 0 rgba(255, 255, 255, 0.9),
                        inset 0 -3px 0 rgba(0, 0, 0, 0.15);
                    color: #333;
                }

                .btn-hero-premium {
                    position: relative;
                    display: inline-block;
                    text-decoration: none;
                    border-radius: 50px;
                    overflow: hidden;
                    transition: all 0.3s ease;
                }

                .btn-premium-content {
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    background:
                        linear-gradient(145deg, #FF6B6B 0%, #FF8E53 50%, #FF4757 100%),
                        linear-gradient(45deg, rgba(255, 107, 107, 0.9) 0%, rgba(255, 142, 83, 0.7) 100%);
                    color: white;
                    padding: 18px 35px;
                    position: relative;
                    z-index: 2;
                    font-weight: 700;
                    font-size: 16px;
                    box-shadow:
                        inset 0 2px 0 rgba(255, 255, 255, 0.3),
                        inset 0 -2px 0 rgba(0, 0, 0, 0.2);
                    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                }

                .premium-badge {
                    background: rgba(255, 255, 255, 0.2);
                    padding: 3px 8px;
                    border-radius: 12px;
                    font-size: 12px;
                    margin-left: 8px;
                }

                .btn-premium-glow {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: linear-gradient(135deg, #FF6B6B 0%, #FF8E53 100%);
                    filter: blur(20px);
                    opacity: 0.7;
                    z-index: 1;
                    animation: premiumPulse 2s ease-in-out infinite;
                }

                @keyframes premiumPulse {
                    0%, 100% { transform: scale(1); opacity: 0.7; }
                    50% { transform: scale(1.1); opacity: 1; }
                }

                .btn-hero-premium:hover {
                    transform: translateY(-4px) translateZ(8px);
                }

                .btn-hero-premium:hover .btn-premium-content {
                    box-shadow:
                        inset 0 3px 0 rgba(255, 255, 255, 0.4),
                        inset 0 -3px 0 rgba(0, 0, 0, 0.3);
                }



                /* Modern Glass Form Styles */
                .hero-form-container {
                    position: relative;
                    z-index: 2;
                    opacity: 0;
                    transform: translateX(40px);
                    transition: all 1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                }

                .form-glass-card {
                    background:
                        linear-gradient(145deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%),
                        linear-gradient(45deg, rgba(255, 215, 0, 0.08) 0%, transparent 50%);
                    backdrop-filter: blur(25px);
                    border: 2px solid rgba(255, 255, 255, 0.3);
                    border-radius: 25px;
                    padding: 45px;
                    box-shadow:
                        0 25px 50px rgba(0, 0, 0, 0.2),
                        inset 0 2px 0 rgba(255, 255, 255, 0.3),
                        inset 0 -2px 0 rgba(0, 0, 0, 0.1);
                    transition: all 0.3s ease;
                    transform-style: preserve-3d;
                }

                .form-glass-card:hover {
                    transform: translateY(-8px) translateZ(10px);
                    box-shadow:
                        0 35px 70px rgba(0, 0, 0, 0.25),
                        inset 0 3px 0 rgba(255, 255, 255, 0.4),
                        inset 0 -3px 0 rgba(0, 0, 0, 0.15);
                }

                .form-header {
                    text-align: center;
                    margin-bottom: 30px;
                    color: white;
                }

                .form-icon {
                    width: 60px;
                    height: 60px;
                    background: linear-gradient(135deg, #FFD700, #FFA500);
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 0 auto 20px;
                    font-size: 24px;
                    color: white;
                    animation: iconPulse 2s ease-in-out infinite;
                }

                @keyframes iconPulse {
                    0%, 100% { transform: scale(1); }
                    50% { transform: scale(1.1); }
                }

                .form-title {
                    font-size: 1.8rem;
                    font-weight: 700;
                    margin-bottom: 10px;
                    color: white;
                }

                .form-subtitle {
                    font-size: 1rem;
                    opacity: 0.8;
                    margin: 0;
                }

                .hero-form {
                    color: white;
                }

                .form-group {
                    margin-bottom: 25px;
                    position: relative;
                }

                .form-label {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    font-size: 14px;
                    font-weight: 600;
                    margin-bottom: 10px;
                    color: rgba(255, 255, 255, 0.9);
                }

                .form-label i {
                    color: #FFD700;
                }

                .select-wrapper {
                    position: relative;
                }

                .form-select-modern {
                    width: 100%;
                    background: rgba(255, 255, 255, 0.1);
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    border-radius: 12px;
                    padding: 15px 45px 15px 15px;
                    color: white;
                    font-size: 16px;
                    appearance: none;
                    transition: all 0.3s ease;
                    backdrop-filter: blur(10px);
                }

                .form-select-modern:focus {
                    outline: none;
                    border-color: #FFD700;
                    box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
                    background: rgba(255, 255, 255, 0.15);
                }

                .form-select-modern option {
                    background: #333;
                    color: white;
                }

                .select-arrow {
                    position: absolute;
                    right: 15px;
                    top: 50%;
                    transform: translateY(-50%);
                    color: #FFD700;
                    pointer-events: none;
                    transition: transform 0.3s ease;
                }

                .form-select-modern:focus + .select-arrow {
                    transform: translateY(-50%) rotate(180deg);
                }

                .form-input-modern {
                    width: 100%;
                    background: rgba(255, 255, 255, 0.1);
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    border-radius: 12px;
                    padding: 15px;
                    color: white;
                    font-size: 16px;
                    transition: all 0.3s ease;
                    backdrop-filter: blur(10px);
                    position: relative;
                    z-index: 2;
                }

                .form-input-modern::placeholder {
                    color: rgba(255, 255, 255, 0.6);
                }

                .form-input-modern:focus {
                    outline: none;
                    border-color: #FFD700;
                    box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
                    background: rgba(255, 255, 255, 0.15);
                }

                .input-focus-line {
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    width: 0;
                    height: 2px;
                    background: linear-gradient(90deg, #FFD700, #FFA500);
                    transition: width 0.3s ease;
                    border-radius: 1px;
                }

                .form-input-modern:focus + .input-focus-line {
                    width: 100%;
                }

                .btn-form-generate {
                    width: 100%;
                    position: relative;
                    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
                    border: none;
                    border-radius: 50px;
                    padding: 18px;
                    color: #333;
                    font-weight: 700;
                    font-size: 16px;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    overflow: hidden;
                    margin-bottom: 20px;
                }

                .btn-form-generate:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 15px 30px rgba(255, 215, 0, 0.4);
                }

                .btn-content {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 10px;
                    position: relative;
                    z-index: 2;
                }

                .btn-glow-effect {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
                    filter: blur(15px);
                    opacity: 0.7;
                    z-index: 1;
                    animation: btnGlow 2s ease-in-out infinite;
                }

                @keyframes btnGlow {
                    0%, 100% { transform: scale(1); opacity: 0.7; }
                    50% { transform: scale(1.05); opacity: 1; }
                }

                .form-footer {
                    text-align: center;
                    margin-top: 20px;
                }

                .security-badge {
                    display: inline-flex;
                    align-items: center;
                    gap: 8px;
                    background: rgba(255, 255, 255, 0.1);
                    backdrop-filter: blur(10px);
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    padding: 8px 16px;
                    border-radius: 20px;
                    font-size: 12px;
                    color: rgba(255, 255, 255, 0.8);
                }

                .security-badge i {
                    color: #28a745;
                }

                /* Enhanced Mobile Responsiveness */
                @media (max-width: 1200px) {
                    .hero-title {
                        font-size: 3.5rem;
                    }

                    .features-grid {
                        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                        gap: 25px;
                    }
                }

                @media (max-width: 992px) {
                    .enterprise-hero-section {
                        padding: 80px 0;
                    }

                    .hero-title {
                        font-size: 3rem;
                    }

                    .hero-subtitle {
                        font-size: 1.3rem;
                    }

                    .features-grid {
                        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                        gap: 20px;
                    }

                    .features-title {
                        font-size: 3rem;
                    }
                }

                @media (max-width: 768px) {
                    .enterprise-hero-section {
                        min-height: auto;
                        padding: 60px 0;
                    }

                    .hero-title {
                        font-size: 2.5rem;
                        line-height: 1.1;
                        margin-bottom: 15px;
                    }

                    .hero-subtitle {
                        font-size: 1.2rem;
                        margin-bottom: 25px;
                    }

                    .hero-features {
                        justify-content: center;
                        gap: 10px;
                        margin-bottom: 25px;
                    }

                    .feature-pill {
                        font-size: 12px;
                        padding: 6px 12px;
                    }

                    .hero-description {
                        font-size: 1rem;
                        margin-bottom: 30px;
                    }

                    .hero-actions {
                        justify-content: center;
                        gap: 15px;
                        margin-bottom: 40px;
                    }

                    .btn-hero-primary,
                    .btn-hero-premium {
                        padding: 14px 25px;
                        font-size: 14px;
                    }

                    .form-glass-card {
                        padding: 30px 20px;
                        margin-top: 40px;
                    }

                    .form-title {
                        font-size: 1.3rem;
                        margin-bottom: 15px;
                    }

                    .form-subtitle {
                        font-size: 0.9rem;
                        margin-bottom: 20px;
                    }

                    .floating-qr {
                        display: none;
                    }

                    /* Features Section Mobile */
                    .enterprise-features-section {
                        padding: 80px 0;
                    }

                    .features-title {
                        font-size: 2.5rem;
                        margin-bottom: 20px;
                    }

                    .features-subtitle {
                        font-size: 1.1rem;
                        margin-bottom: 0;
                    }

                    .features-grid {
                        grid-template-columns: 1fr;
                        gap: 20px;
                        margin-top: 40px;
                    }

                    .enterprise-feature-card {
                        margin-bottom: 20px;
                    }

                    .feature-card-inner {
                        padding: 30px 20px;
                    }

                    .feature-icon-container {
                        width: 60px;
                        height: 60px;
                        margin-bottom: 20px;
                    }

                    .feature-icon {
                        font-size: 24px;
                    }

                    .feature-title {
                        font-size: 1.3rem;
                        margin-bottom: 12px;
                    }

                    .feature-description {
                        font-size: 0.95rem;
                        margin-bottom: 15px;
                    }

                    .highlight-item {
                        font-size: 0.85rem;
                        padding: 5px 10px;
                    }

                    /* CTA Section Mobile */
                    .enterprise-cta-section {
                        padding: 80px 0;
                    }

                    .cta-title {
                        font-size: 2.5rem;
                        margin-bottom: 20px;
                    }

                    .cta-description {
                        font-size: 1.1rem;
                        margin-bottom: 30px;
                    }

                    .cta-actions {
                        flex-direction: column;
                        align-items: center;
                        gap: 15px;
                    }

                    .btn-cta-primary,
                    .btn-cta-secondary {
                        width: 100%;
                        max-width: 300px;
                        justify-content: center;
                        padding: 16px 30px;
                        font-size: 15px;
                    }

                    .cta-trust-indicators {
                        gap: 15px;
                        margin-top: 30px;
                    }

                    .trust-divider {
                        display: none;
                    }

                    .trust-item {
                        font-size: 13px;
                    }
                }

                @media (max-width: 576px) {
                    .enterprise-hero-section {
                        padding: 40px 0;
                    }

                    .hero-title {
                        font-size: 2rem;
                        line-height: 1.1;
                    }

                    .hero-subtitle {
                        font-size: 1.1rem;
                    }

                    .hero-features {
                        flex-direction: column;
                        align-items: center;
                        gap: 8px;
                    }

                    .hero-actions {
                        flex-direction: column;
                        align-items: center;
                        gap: 12px;
                    }

                    .btn-hero-primary,
                    .btn-hero-premium {
                        width: 100%;
                        max-width: 280px;
                        justify-content: center;
                        padding: 12px 20px;
                        font-size: 14px;
                    }

                    .form-glass-card {
                        padding: 25px 15px;
                        margin: 30px 10px 0;
                    }

                    .form-title {
                        font-size: 1.2rem;
                    }

                    .form-input {
                        padding: 12px 15px;
                        font-size: 14px;
                    }

                    .btn-form-generate {
                        padding: 14px;
                        font-size: 14px;
                    }

                    /* Features Section Small Mobile */
                    .enterprise-features-section {
                        padding: 60px 0;
                    }

                    .features-header {
                        margin-bottom: 50px;
                    }

                    .features-title {
                        font-size: 2rem;
                    }

                    .features-subtitle {
                        font-size: 1rem;
                    }

                    .feature-card-inner {
                        padding: 25px 15px;
                    }

                    .feature-content {
                        padding: 15px;
                    }

                    /* CTA Section Small Mobile */
                    .enterprise-cta-section {
                        padding: 60px 0;
                    }

                    .cta-title {
                        font-size: 2rem;
                    }

                    .cta-description {
                        font-size: 1rem;
                        padding: 0 10px;
                    }

                    .btn-cta-primary,
                    .btn-cta-secondary {
                        max-width: 280px;
                        padding: 14px 25px;
                        font-size: 14px;
                    }

                    .cta-trust-indicators {
                        flex-direction: column;
                        gap: 10px;
                    }
                }

                @media (max-width: 480px) {
                    .container {
                        padding: 0 15px;
                    }

                    .hero-title {
                        font-size: 1.8rem;
                    }

                    .hero-subtitle {
                        font-size: 1rem;
                    }

                    .form-glass-card {
                        margin: 20px 5px 0;
                        padding: 20px 12px;
                    }

                    .features-title {
                        font-size: 1.8rem;
                    }

                    .cta-title {
                        font-size: 1.8rem;
                    }

                    .btn-cta-primary,
                    .btn-cta-secondary {
                        max-width: 260px;
                        padding: 12px 20px;
                        font-size: 13px;
                    }
                }

                /* Touch-friendly improvements */
                @media (hover: none) and (pointer: coarse) {
                    .btn-hero-primary,
                    .btn-hero-premium,
                    .btn-cta-primary,
                    .btn-cta-secondary,
                    .btn-form-generate {
                        min-height: 44px;
                        min-width: 44px;
                    }

                    .enterprise-feature-card:hover {
                        transform: none;
                    }

                    .enterprise-feature-card:active {
                        transform: translateY(-5px);
                    }

                    .feature-icon-bg,
                    .feature-icon,
                    .highlight-item {
                        transition: none;
                    }
                }

                /* Fallback: Show content if preloader fails */
                .preloader-fallback .enterprise-hero-section,
                .preloader-fallback .hero-text-content,
                .preloader-fallback .enterprise-badge,
                .preloader-fallback .hero-title,
                .preloader-fallback .hero-subtitle,
                .preloader-fallback .hero-features,
                .preloader-fallback .hero-description,
                .preloader-fallback .hero-actions,
                .preloader-fallback .hero-form-container,
                .preloader-fallback .floating-qr {
                    opacity: 1 !important;
                    transform: none !important;
                }
            </style>

            <script>
                // Ultra-fast fallback: Show content immediately if preloader script doesn't load
                setTimeout(function() {
                    const preloader = document.getElementById('preloader');
                    const heroSection = document.querySelector('.enterprise-hero-section');

                    if (heroSection) {
                        const styles = window.getComputedStyle(heroSection);
                        if (styles.opacity === '0') {
                            console.log('Ultra-fast fallback: Showing content immediately');
                            document.body.classList.add('preloader-fallback');

                            if (preloader) {
                                preloader.style.display = 'none';
                            }

                            document.body.style.overflow = '';
                        }
                    }
                }, 500); // Show content after just 500ms

                // Immediate fallback: Show content right away if DOM is ready
                document.addEventListener('DOMContentLoaded', function() {
                    console.log('DOM ready: Checking if content is visible');

                    // Check if hero content is visible after 1 second
                    setTimeout(function() {
                        const heroSection = document.querySelector('.enterprise-hero-section');
                        if (heroSection) {
                            const styles = window.getComputedStyle(heroSection);
                            if (styles.opacity === '0') {
                                console.log('Hero content not visible, applying fallback');
                                document.body.classList.add('preloader-fallback');

                                // Hide preloader
                                const preloader = document.getElementById('preloader');
                                if (preloader) {
                                    preloader.style.display = 'none';
                                }

                                // Restore body scrolling
                                document.body.style.overflow = '';
                            }
                        }
                    }, 1000);
                });

                // Secondary fallback: Show content after 3 seconds if preloader doesn't work
                setTimeout(function() {
                    console.log('Fallback: Showing hero content after 3 seconds');
                    document.body.classList.add('preloader-fallback');

                    // Also hide preloader if it's still visible
                    const preloader = document.getElementById('preloader');
                    if (preloader) {
                        preloader.style.display = 'none';
                    }

                    // Restore body scrolling
                    document.body.style.overflow = '';
                }, 3000);
            </script>

            <style>
                /* Enterprise Features Section - Modern Corporate Design */
                .enterprise-features-section {
                    position: relative;
                    padding: 120px 0;
                    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                    overflow: hidden;
                }

                .features-background {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    z-index: 1;
                }

                .features-grid-pattern {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background-image:
                        linear-gradient(rgba(102, 126, 234, 0.03) 1px, transparent 1px),
                        linear-gradient(90deg, rgba(102, 126, 234, 0.03) 1px, transparent 1px);
                    background-size: 40px 40px;
                    animation: featuresGridMove 25s linear infinite;
                }

                @keyframes featuresGridMove {
                    0% { transform: translate(0, 0); }
                    100% { transform: translate(40px, 40px); }
                }

                .features-gradient-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background:
                        radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.05) 0%, transparent 50%),
                        radial-gradient(circle at 80% 80%, rgba(255, 215, 0, 0.03) 0%, transparent 50%);
                }

                .container {
                    position: relative;
                    z-index: 2;
                }

                /* Features Header */
                .features-header {
                    text-align: center;
                    margin-bottom: 80px;
                }

                .features-badge {
                    display: inline-flex;
                    align-items: center;
                    gap: 10px;
                    background:
                        linear-gradient(145deg, rgba(102, 126, 234, 0.1) 0%, rgba(102, 126, 234, 0.05) 100%);
                    backdrop-filter: blur(10px);
                    border: 2px solid rgba(102, 126, 234, 0.2);
                    padding: 12px 24px;
                    border-radius: 30px;
                    font-size: 14px;
                    font-weight: 700;
                    color: #667eea;
                    margin-bottom: 30px;
                    box-shadow:
                        0 8px 25px rgba(102, 126, 234, 0.15),
                        inset 0 1px 0 rgba(255, 255, 255, 0.3);
                    animation: featuresBadgeGlow 3s ease-in-out infinite;
                }

                @keyframes featuresBadgeGlow {
                    0%, 100% {
                        box-shadow:
                            0 8px 25px rgba(102, 126, 234, 0.15),
                            inset 0 1px 0 rgba(255, 255, 255, 0.3),
                            0 0 20px rgba(102, 126, 234, 0.2);
                    }
                    50% {
                        box-shadow:
                            0 12px 35px rgba(102, 126, 234, 0.25),
                            inset 0 2px 0 rgba(255, 255, 255, 0.4),
                            0 0 30px rgba(102, 126, 234, 0.3);
                    }
                }

                .features-title {
                    font-size: 3.5rem;
                    font-weight: 800;
                    line-height: 1.2;
                    margin-bottom: 25px;
                    color: #2c3e50;
                }

                .title-main {
                    display: block;
                    color: #2c3e50;
                    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
                    -webkit-background-clip: text;
                    background-clip: text;
                    /* Only use transparent fill if gradient is supported */
                    -webkit-text-fill-color: transparent;
                }

                /* Fallback for browsers that don't support background-clip */
                @supports not (-webkit-background-clip: text) {
                    .title-main {
                        color: #2c3e50 !important;
                        -webkit-text-fill-color: initial !important;
                    }
                }

                .title-accent {
                    display: block;
                    color: #667eea;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    -webkit-background-clip: text;
                    background-clip: text;
                    /* Only use transparent fill if gradient is supported */
                    -webkit-text-fill-color: transparent;
                    animation: titleAccentShine 3s ease-in-out infinite;
                }

                /* Fallback for browsers that don't support background-clip */
                @supports not (-webkit-background-clip: text) {
                    .title-accent {
                        color: #667eea !important;
                        -webkit-text-fill-color: initial !important;
                    }
                }

                @keyframes titleAccentShine {
                    0%, 100% { filter: brightness(1); }
                    50% { filter: brightness(1.2); }
                }

                .features-subtitle {
                    font-size: 1.3rem;
                    line-height: 1.6;
                    color: #6c757d;
                    max-width: 800px;
                    margin: 0 auto;
                }

                /* Features Grid - Modern Layout */
                .features-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
                    gap: 40px;
                    margin-top: 80px;
                    padding: 0 20px;
                }

                /* Modern Enterprise Feature Cards */
                .enterprise-feature-card {
                    position: relative;
                    height: 100%;
                    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                    cursor: pointer;
                }

                .enterprise-feature-card:hover {
                    transform: translateY(-15px) scale(1.02);
                }

                .feature-card-inner {
                    position: relative;
                    height: 100%;
                    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
                    border: 1px solid rgba(0, 0, 0, 0.08);
                    border-radius: 24px;
                    padding: 0;
                    box-shadow:
                        0 8px 32px rgba(0, 0, 0, 0.08),
                        0 2px 8px rgba(0, 0, 0, 0.04);
                    overflow: hidden;
                    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                    display: flex;
                    flex-direction: column;
                }

                .enterprise-feature-card:hover .feature-card-inner {
                    box-shadow:
                        0 20px 64px rgba(0, 0, 0, 0.12),
                        0 8px 24px rgba(0, 0, 0, 0.08);
                    border-color: rgba(102, 126, 234, 0.2);
                    background: linear-gradient(135deg, #ffffff 0%, #f1f3f4 100%);
                }

                /* Modern Card Header with Icon */
                .feature-card-header {
                    position: relative;
                    padding: 40px 40px 20px 40px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    display: flex;
                    align-items: center;
                    gap: 20px;
                    min-height: 120px;
                }

                .feature-icon-container {
                    position: relative;
                    width: 70px;
                    height: 70px;
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    backdrop-filter: blur(10px);
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    transition: all 0.4s ease;
                    flex-shrink: 0;
                }

                .enterprise-feature-card:hover .feature-icon-container {
                    transform: scale(1.1) rotate(5deg);
                    background: rgba(255, 255, 255, 0.3);
                    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
                }

                .feature-icon {
                    width: 32px;
                    height: 32px;
                    fill: white;
                    transition: all 0.4s ease;
                }

                .enterprise-feature-card:hover .feature-icon {
                    transform: scale(1.1);
                    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
                }

                /* Custom SVG Icon Styles */
                .custom-icon {
                    width: 32px;
                    height: 32px;
                    fill: white;
                    transition: all 0.4s ease;
                }

                .enterprise-feature-card:hover .custom-icon {
                    transform: scale(1.1);
                    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
                }

                .feature-header-text {
                    flex: 1;
                    color: white;
                }

                .feature-number {
                    font-size: 14px;
                    font-weight: 600;
                    opacity: 0.8;
                    margin-bottom: 8px;
                    letter-spacing: 1px;
                    text-transform: uppercase;
                }

                .feature-title-header {
                    font-size: 1.4rem;
                    font-weight: 700;
                    line-height: 1.3;
                    margin: 0;
                }

                @keyframes iconGlow {
                    0%, 100% { opacity: 0.3; transform: rotate(45deg) scale(1); }
                    50% { opacity: 0.6; transform: rotate(45deg) scale(1.1); }
                }

                .feature-content {
                    position: relative;
                    flex: 1;
                    padding: 40px;
                    display: flex;
                    flex-direction: column;
                    gap: 24px;
                }

                .feature-description {
                    font-size: 1rem;
                    line-height: 1.7;
                    color: #4a5568;
                    margin: 0;
                    flex: 1;
                }

                .feature-highlights {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
                    gap: 12px;
                    margin-top: auto;
                }

                .highlight-item {
                    font-size: 0.85rem;
                    color: #667eea;
                    font-weight: 600;
                    padding: 10px 12px;
                    background: rgba(102, 126, 234, 0.08);
                    border-radius: 12px;
                    border: 1px solid rgba(102, 126, 234, 0.15);
                    transition: all 0.3s ease;
                    text-align: center;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 6px;
                }

                .enterprise-feature-card:hover .highlight-item {
                    background: rgba(102, 126, 234, 0.12);
                    border-color: rgba(102, 126, 234, 0.25);
                    transform: translateY(-2px);
                }

                .feature-card-glow {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 20px;
                    filter: blur(30px);
                    opacity: 0;
                    transition: opacity 0.4s ease;
                    z-index: -1;
                }

                .enterprise-feature-card:hover .feature-card-glow {
                    opacity: 0.1;
                }

                /* Enterprise CTA Section */
                .enterprise-cta-section {
                    position: relative;
                    padding: 120px 0;
                    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
                    overflow: hidden;
                }

                .cta-background {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    z-index: 1;
                }

                .cta-grid-pattern {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background-image:
                        linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
                        linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
                    background-size: 50px 50px;
                    animation: ctaGridMove 30s linear infinite;
                }

                @keyframes ctaGridMove {
                    0% { transform: translate(0, 0); }
                    100% { transform: translate(50px, 50px); }
                }

                .cta-gradient-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background:
                        radial-gradient(circle at 30% 30%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 70% 70%, rgba(255, 215, 0, 0.05) 0%, transparent 50%);
                }

                .cta-floating-elements {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                }

                .floating-element {
                    position: absolute;
                    width: 40px;
                    height: 40px;
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 50%;
                    backdrop-filter: blur(10px);
                }

                .floating-element-1 {
                    top: 20%;
                    left: 10%;
                    animation: ctaFloat1 8s ease-in-out infinite;
                }

                .floating-element-2 {
                    top: 60%;
                    right: 15%;
                    animation: ctaFloat2 10s ease-in-out infinite;
                }

                .floating-element-3 {
                    bottom: 30%;
                    left: 20%;
                    animation: ctaFloat3 12s ease-in-out infinite;
                }

                @keyframes ctaFloat1 {
                    0%, 100% { transform: translateY(0px) scale(1); }
                    50% { transform: translateY(-20px) scale(1.1); }
                }

                @keyframes ctaFloat2 {
                    0%, 100% { transform: translateY(0px) scale(1); }
                    50% { transform: translateY(-30px) scale(1.2); }
                }

                @keyframes ctaFloat3 {
                    0%, 100% { transform: translateY(0px) scale(1); }
                    50% { transform: translateY(-25px) scale(1.15); }
                }

                .cta-content {
                    position: relative;
                    z-index: 2;
                    text-align: center;
                    color: white;
                }

                .cta-badge {
                    display: inline-flex;
                    align-items: center;
                    gap: 10px;
                    background:
                        linear-gradient(145deg, rgba(255, 215, 0, 0.2) 0%, rgba(255, 215, 0, 0.1) 100%);
                    backdrop-filter: blur(10px);
                    border: 2px solid rgba(255, 215, 0, 0.3);
                    padding: 12px 24px;
                    border-radius: 30px;
                    font-size: 14px;
                    font-weight: 700;
                    color: #FFD700;
                    margin-bottom: 30px;
                    box-shadow:
                        0 8px 25px rgba(255, 215, 0, 0.2),
                        inset 0 1px 0 rgba(255, 255, 255, 0.2);
                    animation: ctaBadgeGlow 3s ease-in-out infinite;
                }

                @keyframes ctaBadgeGlow {
                    0%, 100% {
                        box-shadow:
                            0 8px 25px rgba(255, 215, 0, 0.2),
                            inset 0 1px 0 rgba(255, 255, 255, 0.2),
                            0 0 20px rgba(255, 215, 0, 0.3);
                    }
                    50% {
                        box-shadow:
                            0 12px 35px rgba(255, 215, 0, 0.3),
                            inset 0 2px 0 rgba(255, 255, 255, 0.3),
                            0 0 30px rgba(255, 215, 0, 0.5);
                    }
                }

                .cta-title {
                    font-size: 3.5rem;
                    font-weight: 800;
                    line-height: 1.2;
                    margin-bottom: 25px;
                }

                .title-line-1 {
                    display: block;
                    color: white;
                }

                .title-line-2 {
                    display: block;
                    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    background-clip: text;
                    animation: ctaTitleShine 3s ease-in-out infinite;
                }

                @keyframes ctaTitleShine {
                    0%, 100% { filter: brightness(1); }
                    50% { filter: brightness(1.3); }
                }

                .cta-description {
                    font-size: 1.3rem;
                    line-height: 1.6;
                    color: rgba(255, 255, 255, 0.9);
                    max-width: 700px;
                    margin: 0 auto 40px;
                }

                .cta-actions {
                    display: flex;
                    justify-content: center;
                    gap: 20px;
                    margin-bottom: 50px;
                    flex-wrap: wrap;
                }

                .btn-cta-primary {
                    position: relative;
                    display: inline-flex;
                    align-items: center;
                    gap: 10px;
                    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
                    color: #333;
                    padding: 18px 35px;
                    border-radius: 50px;
                    text-decoration: none;
                    font-weight: 700;
                    font-size: 16px;
                    transition: all 0.3s ease;
                    box-shadow:
                        0 12px 35px rgba(255, 215, 0, 0.3),
                        inset 0 2px 0 rgba(255, 255, 255, 0.3);
                    overflow: hidden;
                }

                .btn-cta-primary:hover {
                    transform: translateY(-3px);
                    box-shadow:
                        0 18px 45px rgba(255, 215, 0, 0.4),
                        inset 0 3px 0 rgba(255, 255, 255, 0.4);
                    color: #333;
                }

                .btn-glow {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
                    filter: blur(20px);
                    opacity: 0.7;
                    z-index: -1;
                    animation: btnGlowPulse 2s ease-in-out infinite;
                }

                @keyframes btnGlowPulse {
                    0%, 100% { transform: scale(1); opacity: 0.7; }
                    50% { transform: scale(1.1); opacity: 1; }
                }

                .btn-cta-secondary {
                    display: inline-flex;
                    align-items: center;
                    gap: 10px;
                    background: rgba(255, 255, 255, 0.1);
                    color: white;
                    padding: 18px 35px;
                    border-radius: 50px;
                    text-decoration: none;
                    font-weight: 600;
                    font-size: 16px;
                    border: 2px solid rgba(255, 255, 255, 0.3);
                    backdrop-filter: blur(10px);
                    transition: all 0.3s ease;
                }

                .btn-cta-secondary:hover {
                    background: rgba(255, 255, 255, 0.2);
                    border-color: rgba(255, 255, 255, 0.5);
                    transform: translateY(-3px);
                    color: white;
                }

                .cta-trust-indicators {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    gap: 30px;
                    flex-wrap: wrap;
                }

                .trust-item {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    color: rgba(255, 255, 255, 0.8);
                    font-size: 14px;
                    font-weight: 500;
                }

                .trust-item i {
                    color: #FFD700;
                    font-size: 16px;
                }

                .trust-divider {
                    width: 1px;
                    height: 30px;
                    background: rgba(255, 255, 255, 0.3);
                }

                /* Responsive Design */
                @media (max-width: 768px) {
                    .features-title {
                        font-size: 2.5rem;
                    }

                    .features-grid {
                        grid-template-columns: 1fr;
                        gap: 20px;
                    }

                    .enterprise-feature-card {
                        margin-bottom: 20px;
                    }

                    .cta-title {
                        font-size: 2.5rem;
                    }

                    .cta-actions {
                        flex-direction: column;
                        align-items: center;
                    }

                    .btn-cta-primary,
                    .btn-cta-secondary {
                        width: 100%;
                        max-width: 300px;
                        justify-content: center;
                    }

                    .cta-trust-indicators {
                        gap: 15px;
                    }

                    .trust-divider {
                        display: none;
                    }
                }
            </style>


<!-- Enterprise Features Section - Modern Corporate Design -->
<div class="enterprise-features-section">
    <div class="features-background">
        <div class="features-grid-pattern"></div>
        <div class="features-gradient-overlay"></div>
    </div>

    <div class="container">
        <div class="features-header">
            <div class="features-badge">
                <i class="fas fa-building"></i>
                <span>Enterprise Grade</span>
            </div>
            <h2 class="features-title">
                <span class="title-main">Enterprise Features</span>
                <span class="title-accent">That Drive Success</span>
            </h2>
            <p class="features-subtitle">Our enterprise-grade QR code generator offers a comprehensive suite of features designed to meet the needs of businesses of all sizes, from startups to Fortune 500 companies.</p>
        </div>

        <div class="features-grid">
            <!-- Security Feature -->
            <div class="enterprise-feature-card" data-feature="security">
                <div class="feature-card-inner">
                    <div class="feature-card-header">
                        <div class="feature-icon-container">
                            <svg class="custom-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="securityGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
                                        <stop offset="100%" style="stop-color:#f0f0f0;stop-opacity:1" />
                                    </linearGradient>
                                </defs>
                                <!-- Elegant Security Vault Icon -->
                                <path d="M12 2L3 7v5c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V7l-9-5z" fill="url(#securityGradient)" stroke="rgba(255,255,255,0.3)" stroke-width="0.5"/>
                                <circle cx="12" cy="12" r="3" fill="none" stroke="rgba(255,255,255,0.8)" stroke-width="1.5"/>
                                <path d="M12 10.5v3M10.5 12h3" stroke="rgba(255,255,255,0.8)" stroke-width="1.2" stroke-linecap="round"/>
                                <circle cx="12" cy="12" r="1" fill="rgba(255,255,255,0.9)"/>
                            </svg>
                        </div>
                        <div class="feature-header-text">
                            <div class="feature-number">Feature 01</div>
                            <h3 class="feature-title-header">Enterprise Security</h3>
                        </div>
                    </div>
                    <div class="feature-content">
                        <p class="feature-description">Role-based access control, secure authentication, and encrypted QR codes for sensitive data protection and compliance.</p>
                        <div class="feature-highlights">
                            <span class="highlight-item">🔐 256-bit Encryption</span>
                            <span class="highlight-item">🛡️ RBAC System</span>
                            <span class="highlight-item">✅ SOC 2 Compliant</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mobile Ready Feature -->
            <div class="enterprise-feature-card" data-feature="mobile">
                <div class="feature-card-inner">
                    <div class="feature-card-header">
                        <div class="feature-icon-container">
                            <svg class="custom-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="mobileGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
                                        <stop offset="100%" style="stop-color:#f0f0f0;stop-opacity:1" />
                                    </linearGradient>
                                </defs>
                                <!-- Elegant Multi-Device Ecosystem Icon -->
                                <rect x="6" y="3" width="12" height="18" rx="2" ry="2" fill="url(#mobileGradient)" stroke="rgba(255,255,255,0.4)" stroke-width="0.8"/>
                                <rect x="7" y="5" width="10" height="12" fill="none" stroke="rgba(255,255,255,0.6)" stroke-width="0.5"/>
                                <circle cx="12" cy="19" r="1" fill="rgba(255,255,255,0.8)"/>
                                <!-- Tablet representation -->
                                <rect x="2" y="8" width="6" height="8" rx="1" ry="1" fill="none" stroke="rgba(255,255,255,0.5)" stroke-width="0.8"/>
                                <!-- Desktop representation -->
                                <rect x="16" y="6" width="6" height="4" rx="0.5" ry="0.5" fill="none" stroke="rgba(255,255,255,0.5)" stroke-width="0.8"/>
                                <line x1="17" y1="10.5" x2="21" y2="10.5" stroke="rgba(255,255,255,0.5)" stroke-width="0.8"/>
                                <!-- Connection lines -->
                                <path d="M8 11 Q12 8 16 9" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="0.5" stroke-dasharray="1,1"/>
                            </svg>
                        </div>
                        <div class="feature-header-text">
                            <div class="feature-number">Feature 02</div>
                            <h3 class="feature-title-header">Mobile Excellence</h3>
                        </div>
                    </div>
                    <div class="feature-content">
                        <p class="feature-description">Enhanced mobile experience with responsive design and touch-friendly controls for seamless operation on any device.</p>
                        <div class="feature-highlights">
                            <span class="highlight-item">📱 Native Feel</span>
                            <span class="highlight-item">⚡ Lightning Fast</span>
                            <span class="highlight-item">🎯 Touch Optimized</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customization Feature -->
            <div class="enterprise-feature-card" data-feature="customization">
                <div class="feature-card-inner">
                    <div class="feature-card-header">
                        <div class="feature-icon-container">
                            <svg class="custom-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="brandGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
                                        <stop offset="100%" style="stop-color:#f0f0f0;stop-opacity:1" />
                                    </linearGradient>
                                </defs>
                                <!-- Sophisticated Brand Identity Icon -->
                                <circle cx="12" cy="12" r="9" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="0.8"/>
                                <path d="M12 4 L16 8 L12 12 L8 8 Z" fill="url(#brandGradient)" stroke="rgba(255,255,255,0.5)" stroke-width="0.5"/>
                                <circle cx="12" cy="12" r="3" fill="none" stroke="rgba(255,255,255,0.7)" stroke-width="1"/>
                                <circle cx="12" cy="12" r="1.5" fill="rgba(255,255,255,0.9)"/>
                                <!-- Brand elements -->
                                <path d="M6 18 Q12 15 18 18" fill="none" stroke="rgba(255,255,255,0.4)" stroke-width="1" stroke-linecap="round"/>
                                <circle cx="6" cy="6" r="1" fill="rgba(255,255,255,0.6)"/>
                                <circle cx="18" cy="6" r="1" fill="rgba(255,255,255,0.6)"/>
                                <circle cx="6" cy="18" r="1" fill="rgba(255,255,255,0.6)"/>
                                <circle cx="18" cy="18" r="1" fill="rgba(255,255,255,0.6)"/>
                            </svg>
                        </div>
                        <div class="feature-header-text">
                            <div class="feature-number">Feature 03</div>
                            <h3 class="feature-title-header">Brand Customization</h3>
                        </div>
                    </div>
                    <div class="feature-content">
                        <p class="feature-description">Advanced customization options for colors, shapes, logos, and styles to match your brand identity perfectly.</p>
                        <div class="feature-highlights">
                            <span class="highlight-item">🎨 Custom Branding</span>
                            <span class="highlight-item">🖼️ Logo Integration</span>
                            <span class="highlight-item">🌈 Color Schemes</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- API Integration Feature -->
            <div class="enterprise-feature-card" data-feature="api">
                <div class="feature-card-inner">
                    <div class="feature-card-header">
                        <div class="feature-icon-container">
                            <svg class="custom-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="apiGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
                                        <stop offset="100%" style="stop-color:#f0f0f0;stop-opacity:1" />
                                    </linearGradient>
                                </defs>
                                <!-- Elegant API Network Icon -->
                                <circle cx="12" cy="12" r="2" fill="url(#apiGradient)" stroke="rgba(255,255,255,0.8)" stroke-width="1"/>
                                <!-- Network nodes -->
                                <circle cx="6" cy="6" r="1.5" fill="rgba(255,255,255,0.7)" stroke="rgba(255,255,255,0.4)" stroke-width="0.5"/>
                                <circle cx="18" cy="6" r="1.5" fill="rgba(255,255,255,0.7)" stroke="rgba(255,255,255,0.4)" stroke-width="0.5"/>
                                <circle cx="6" cy="18" r="1.5" fill="rgba(255,255,255,0.7)" stroke="rgba(255,255,255,0.4)" stroke-width="0.5"/>
                                <circle cx="18" cy="18" r="1.5" fill="rgba(255,255,255,0.7)" stroke="rgba(255,255,255,0.4)" stroke-width="0.5"/>
                                <!-- Connection lines -->
                                <path d="M7.5 7.5 L10.5 10.5" stroke="rgba(255,255,255,0.6)" stroke-width="1" stroke-linecap="round"/>
                                <path d="M16.5 7.5 L13.5 10.5" stroke="rgba(255,255,255,0.6)" stroke-width="1" stroke-linecap="round"/>
                                <path d="M7.5 16.5 L10.5 13.5" stroke="rgba(255,255,255,0.6)" stroke-width="1" stroke-linecap="round"/>
                                <path d="M16.5 16.5 L13.5 13.5" stroke="rgba(255,255,255,0.6)" stroke-width="1" stroke-linecap="round"/>
                                <!-- Data flow indicators -->
                                <circle cx="9" cy="9" r="0.5" fill="rgba(255,255,255,0.8)">
                                    <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" repeatCount="indefinite"/>
                                </circle>
                                <circle cx="15" cy="15" r="0.5" fill="rgba(255,255,255,0.8)">
                                    <animate attributeName="opacity" values="1;0.3;1" dur="2s" repeatCount="indefinite"/>
                                </circle>
                            </svg>
                        </div>
                        <div class="feature-header-text">
                            <div class="feature-number">Feature 04</div>
                            <h3 class="feature-title-header">API Integration</h3>
                        </div>
                    </div>
                    <div class="feature-content">
                        <p class="feature-description">RESTful API for seamless integration with your existing systems and enterprise software solutions.</p>
                        <div class="feature-highlights">
                            <span class="highlight-item">🔗 RESTful API</span>
                            <span class="highlight-item">📚 Full Documentation</span>
                            <span class="highlight-item">⚙️ Easy Integration</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Performance Feature -->
            <div class="enterprise-feature-card" data-feature="performance">
                <div class="feature-card-inner">
                    <div class="feature-card-header">
                        <div class="feature-icon-container">
                            <svg class="custom-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="performanceGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
                                        <stop offset="100%" style="stop-color:#f0f0f0;stop-opacity:1" />
                                    </linearGradient>
                                </defs>
                                <!-- Sophisticated Performance Engine Icon -->
                                <circle cx="12" cy="12" r="8" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="0.8"/>
                                <circle cx="12" cy="12" r="5" fill="none" stroke="rgba(255,255,255,0.5)" stroke-width="1"/>
                                <circle cx="12" cy="12" r="2" fill="url(#performanceGradient)" stroke="rgba(255,255,255,0.8)" stroke-width="0.5"/>
                                <!-- Performance indicators -->
                                <path d="M12 4 L13 7 L12 10 L11 7 Z" fill="rgba(255,255,255,0.7)">
                                    <animateTransform attributeName="transform" type="rotate" values="0 12 12;360 12 12" dur="3s" repeatCount="indefinite"/>
                                </path>
                                <path d="M20 12 L17 13 L14 12 L17 11 Z" fill="rgba(255,255,255,0.6)">
                                    <animateTransform attributeName="transform" type="rotate" values="0 12 12;360 12 12" dur="2s" repeatCount="indefinite"/>
                                </path>
                                <path d="M12 20 L11 17 L12 14 L13 17 Z" fill="rgba(255,255,255,0.5)">
                                    <animateTransform attributeName="transform" type="rotate" values="0 12 12;360 12 12" dur="4s" repeatCount="indefinite"/>
                                </path>
                                <!-- Central core -->
                                <circle cx="12" cy="12" r="1" fill="rgba(255,255,255,0.9)">
                                    <animate attributeName="r" values="0.8;1.2;0.8" dur="1.5s" repeatCount="indefinite"/>
                                </circle>
                            </svg>
                        </div>
                        <div class="feature-header-text">
                            <div class="feature-number">Feature 05</div>
                            <h3 class="feature-title-header">High Performance</h3>
                        </div>
                    </div>
                    <div class="feature-content">
                        <p class="feature-description">High-volume batch processing and optimization for enterprise workloads with minimal resource consumption.</p>
                        <div class="feature-highlights">
                            <span class="highlight-item">⚡ Batch Processing</span>
                            <span class="highlight-item">🚀 Optimized Speed</span>
                            <span class="highlight-item">💾 Low Resource</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Analytics Feature -->
            <div class="enterprise-feature-card" data-feature="analytics">
                <div class="feature-card-inner">
                    <div class="feature-card-header">
                        <div class="feature-icon-container">
                            <svg class="custom-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="analyticsGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
                                        <stop offset="100%" style="stop-color:#f0f0f0;stop-opacity:1" />
                                    </linearGradient>
                                </defs>
                                <!-- Elegant Analytics Dashboard Icon -->
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2" fill="none" stroke="rgba(255,255,255,0.4)" stroke-width="0.8"/>
                                <!-- Data visualization elements -->
                                <circle cx="7" cy="16" r="1" fill="rgba(255,255,255,0.8)"/>
                                <circle cx="12" cy="12" r="1" fill="rgba(255,255,255,0.8)"/>
                                <circle cx="17" cy="8" r="1" fill="rgba(255,255,255,0.8)"/>
                                <!-- Trend line -->
                                <path d="M7 16 Q12 12 17 8" fill="none" stroke="url(#analyticsGradient)" stroke-width="2" stroke-linecap="round"/>
                                <!-- Dashboard grid -->
                                <line x1="5" y1="7" x2="19" y2="7" stroke="rgba(255,255,255,0.3)" stroke-width="0.5"/>
                                <line x1="5" y1="11" x2="19" y2="11" stroke="rgba(255,255,255,0.3)" stroke-width="0.5"/>
                                <line x1="5" y1="15" x2="19" y2="15" stroke="rgba(255,255,255,0.3)" stroke-width="0.5"/>
                                <!-- Data bars -->
                                <rect x="6" y="13" width="2" height="3" fill="rgba(255,255,255,0.6)" rx="0.5"/>
                                <rect x="11" y="10" width="2" height="6" fill="rgba(255,255,255,0.7)" rx="0.5"/>
                                <rect x="16" y="6" width="2" height="10" fill="rgba(255,255,255,0.8)" rx="0.5"/>
                                <!-- Insight indicator -->
                                <circle cx="18" cy="6" r="1.5" fill="none" stroke="rgba(255,255,255,0.5)" stroke-width="0.8">
                                    <animate attributeName="stroke-opacity" values="0.3;0.8;0.3" dur="2s" repeatCount="indefinite"/>
                                </circle>
                            </svg>
                        </div>
                        <div class="feature-header-text">
                            <div class="feature-number">Feature 06</div>
                            <h3 class="feature-title-header">Advanced Analytics</h3>
                        </div>
                    </div>
                    <div class="feature-content">
                        <p class="feature-description">Comprehensive analytics and reporting for your QR code campaigns with actionable insights and data visualization.</p>
                        <div class="feature-highlights">
                            <span class="highlight-item">📊 Real-time Data</span>
                            <span class="highlight-item">📈 Campaign Insights</span>
                            <span class="highlight-item">📋 Custom Reports</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enterprise CTA Section - Modern Corporate Design -->
<div class="enterprise-cta-section">
    <div class="cta-background">
        <div class="cta-grid-pattern"></div>
        <div class="cta-gradient-overlay"></div>
        <div class="cta-floating-elements">
            <div class="floating-element floating-element-1"></div>
            <div class="floating-element floating-element-2"></div>
            <div class="floating-element floating-element-3"></div>
        </div>
    </div>

    <div class="container">
        <div class="cta-content">
            <div class="cta-badge">
                <i class="fas fa-rocket"></i>
                <span>Ready to Transform</span>
            </div>
            <h2 class="cta-title">
                <span class="title-line-1">Ready to elevate</span>
                <span class="title-line-2">your business?</span>
            </h2>
            <p class="cta-description">Join thousands of businesses worldwide that trust our enterprise-grade QR code solutions for their critical operations and digital transformation initiatives.</p>

            <div class="cta-actions">
                <a href="{% url 'generate_qr_code' %}" class="btn-cta-primary">
                    <span>Generate Your First QR Code</span>
                    <i class="fas fa-arrow-right"></i>
                    <div class="btn-glow"></div>
                </a>
                <a href="{% url 'batch_processing' %}" class="btn-cta-secondary">
                    <i class="fas fa-layer-group"></i>
                    <span>Explore Enterprise Features</span>
                </a>
            </div>

            <div class="cta-trust-indicators">
                <div class="trust-item">
                    <i class="fas fa-shield-check"></i>
                    <span>Enterprise Security</span>
                </div>
                <div class="trust-divider"></div>
                <div class="trust-item">
                    <i class="fas fa-clock"></i>
                    <span>24/7 Support</span>
                </div>
                <div class="trust-divider"></div>
                <div class="trust-item">
                    <i class="fas fa-award"></i>
                    <span>ISO Certified</span>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
