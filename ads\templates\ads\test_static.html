{% extends 'base.html' %}
{% load static %}

{% block title %}Test Static Files{% endblock %}

{% block content %}
<div class="container mt-5">
    <h1>Testing Static Files</h1>
    
    <div class="card mt-4">
        <div class="card-header">
            <h2>CSS Test</h2>
        </div>
        <div class="card-body">
            <p>CSS URL: <code>{% static 'ads/css/dashboard.css' %}</code></p>
            <p>Attempting to load CSS file...</p>
            <div id="css-status">Checking...</div>
        </div>
    </div>
    
    <div class="card mt-4">
        <div class="card-header">
            <h2>JavaScript Test</h2>
        </div>
        <div class="card-body">
            <p>JS URL: <code>{% static 'ads/js/dashboard.js' %}</code></p>
            <p>Attempting to load JavaScript file...</p>
            <div id="js-status">Checking...</div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Test CSS file
        var cssLink = document.createElement('link');
        cssLink.rel = 'stylesheet';
        cssLink.href = "{% static 'ads/css/dashboard.css' %}";
        cssLink.onload = function() {
            document.getElementById('css-status').innerHTML = 
                '<span class="text-success">Success! CSS file loaded.</span>';
        };
        cssLink.onerror = function() {
            document.getElementById('css-status').innerHTML = 
                '<span class="text-danger">Error! Failed to load CSS file.</span>';
        };
        document.head.appendChild(cssLink);
        
        // Test JavaScript file
        var jsScript = document.createElement('script');
        jsScript.src = "{% static 'ads/js/dashboard.js' %}";
        jsScript.onload = function() {
            document.getElementById('js-status').innerHTML = 
                '<span class="text-success">Success! JavaScript file loaded.</span>';
        };
        jsScript.onerror = function() {
            document.getElementById('js-status').innerHTML = 
                '<span class="text-danger">Error! Failed to load JavaScript file.</span>';
        };
        document.body.appendChild(jsScript);
    });
</script>
{% endblock %}
