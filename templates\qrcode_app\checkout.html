{% extends "base.html" %}
{% load static %}

{% block title %}Checkout - Enterprise QR{% endblock %}

{% block extra_css %}
<style>
    /* Modern Checkout Styling */
    body {
        background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
        min-height: 100vh;
        font-family: 'Inter', 'Segoe UI', sans-serif !important;
        position: relative;
        overflow-x: hidden;
    }

    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.15) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(236, 72, 153, 0.12) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 60% 60%, rgba(16, 185, 129, 0.08) 0%, transparent 50%);
        z-index: -1;
        animation: modernFloat 100s ease-in-out infinite;
    }

    @keyframes modernFloat {
        0%, 100% {
            transform: translateY(0px) rotate(0deg) scale(1);
            opacity: 1;
        }
        25% {
            transform: translateY(-40px) rotate(2deg) scale(1.02);
            opacity: 0.8;
        }
        50% {
            transform: translateY(-25px) rotate(-1deg) scale(0.98);
            opacity: 0.9;
        }
        75% {
            transform: translateY(-45px) rotate(1.5deg) scale(1.01);
            opacity: 0.85;
        }
    }

    .checkout-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem 1rem;
    }

    .checkout-header {
        text-align: center;
        margin-bottom: 3rem;
        background: linear-gradient(135deg,
            rgba(99, 102, 241, 0.08) 0%,
            rgba(139, 92, 246, 0.06) 25%,
            rgba(59, 130, 246, 0.05) 50%,
            rgba(236, 72, 153, 0.04) 75%,
            rgba(16, 185, 129, 0.03) 100%);
        backdrop-filter: blur(60px) saturate(200%);
        border-radius: 32px;
        padding: 3rem 2rem;
        box-shadow:
            0 32px 64px rgba(0, 0, 0, 0.12),
            0 0 0 1px rgba(255, 255, 255, 0.08),
            inset 0 2px 0 rgba(255, 255, 255, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.06);
    }

    .checkout-title {
        font-size: 2.8rem;
        font-weight: 700;
        margin-bottom: 1rem;
        color: #ffffff;
        letter-spacing: -0.02em;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    }

    .checkout-subtitle {
        color: rgba(255, 255, 255, 0.85);
        font-size: 1.2rem;
        font-weight: 300;
        letter-spacing: 0.01em;
    }

    .checkout-steps {
        display: flex;
        justify-content: space-between;
        margin-bottom: 4rem;
        position: relative;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    .checkout-steps::before {
        content: '';
        position: absolute;
        top: 28px;
        left: 10%;
        right: 10%;
        height: 3px;
        background: linear-gradient(90deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
        z-index: 1;
        border-radius: 2px;
    }

    .checkout-step {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        z-index: 2;
        flex: 1;
    }

    .step-number {
        width: 56px;
        height: 56px;
        border-radius: 50%;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
        color: rgba(255, 255, 255, 0.6);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        margin-bottom: 0.75rem;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        border: 2px solid rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        font-size: 1.1rem;
    }

    .step-label {
        font-size: 0.9rem;
        font-weight: 500;
        color: rgba(255, 255, 255, 0.6);
        text-align: center;
        transition: all 0.3s ease;
    }

    .checkout-step.active .step-number {
        background: linear-gradient(135deg, #3b82f6, #2563eb);
        color: white;
        border-color: #3b82f6;
        box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
        animation: pulse 2s infinite;
    }

    .checkout-step.active .step-label {
        color: rgba(255, 255, 255, 0.9);
        font-weight: 600;
    }

    .checkout-step.completed .step-number {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
        border-color: #10b981;
        box-shadow: 0 8px 24px rgba(16, 185, 129, 0.3);
    }

    .checkout-step.completed .step-label {
        color: rgba(255, 255, 255, 0.9);
        font-weight: 600;
    }

    .checkout-card {
        border: none;
        border-radius: 24px;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.99) 0%,
            rgba(255, 255, 255, 0.97) 100%);
        backdrop-filter: blur(30px) saturate(200%);
        box-shadow:
            0 32px 64px rgba(0, 0, 0, 0.06),
            0 0 0 1px rgba(255, 255, 255, 0.4),
            inset 0 2px 0 rgba(255, 255, 255, 0.8);
        border: 1px solid rgba(255, 255, 255, 0.3);
        overflow: hidden;
        margin-bottom: 2rem;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .checkout-card:hover {
        transform: translateY(-8px);
        box-shadow:
            0 48px 96px rgba(0, 0, 0, 0.08),
            0 0 0 1px rgba(255, 255, 255, 0.5),
            inset 0 2px 0 rgba(255, 255, 255, 0.9);
    }

    .checkout-card-header {
        background: linear-gradient(135deg,
            rgba(248, 250, 252, 0.6) 0%,
            rgba(255, 255, 255, 0.8) 100%);
        padding: 2rem;
        border-bottom: 1px solid rgba(226, 232, 240, 0.2);
    }

    .checkout-card-title {
        font-weight: 600;
        margin-bottom: 0;
        display: flex;
        align-items: center;
        color: #0f172a;
        font-size: 1.2rem;
    }

    .checkout-card-title i {
        margin-right: 0.75rem;
        color: #3b82f6;
        font-size: 1.1rem;
    }

    .checkout-card-body {
        padding: 2rem;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.9) 0%,
            rgba(248, 250, 252, 0.8) 100%);
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        font-weight: 500;
        margin-bottom: 0.5rem;
        color: #334155;
    }

    .form-control {
        border-radius: 0.5rem;
        border: 1px solid #cbd5e1;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: #2563eb;
        box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    }

    .order-summary {
        background-color: #f8fafc;
        border-radius: 0.5rem;
        padding: 1.5rem;
    }

    .summary-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 1rem;
        color: #334155;
    }

    .summary-item.total {
        font-weight: 600;
        font-size: 1.125rem;
        border-top: 1px solid #cbd5e1;
        padding-top: 1rem;
        margin-top: 1rem;
    }

    .original-price {
        text-decoration: line-through;
        color: #94a3b8;
    }

    .summary-item.savings {
        background-color: #f0fdf4;
        padding: 0.5rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }

    .savings-amount {
        font-weight: 700;
        color: #10b981;
    }

    .payment-methods {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .payment-method-option {
        flex: 1;
        min-width: 120px;
        border: 1px solid #cbd5e1;
        border-radius: 0.75rem;
        padding: 1rem;
        display: flex;
        align-items: center;
        cursor: pointer;
        transition: all 0.3s ease;
        background-color: white;
    }

    .payment-method-option:hover {
        border-color: #2563eb;
        background-color: rgba(37, 99, 235, 0.05);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
    }

    .payment-method-option.selected {
        border-color: #2563eb;
        background-color: rgba(37, 99, 235, 0.1);
        box-shadow: 0 4px 12px rgba(37, 99, 235, 0.15);
    }

    .payment-icon {
        height: 30px;
        max-width: 50px;
        object-fit: contain;
        margin-right: 0.75rem;
    }

    .payment-method-option .payment-label {
        font-weight: 600;
        color: #334155;
    }

    .mpesa-option .payment-icon {
        height: 25px;
    }

    .btn-checkout {
        background: linear-gradient(135deg, #2563eb, #1d4ed8);
        color: white;
        border: none;
        border-radius: 0.5rem;
        padding: 1rem 1.5rem;
        font-weight: 600;
        width: 100%;
        transition: all 0.3s ease;
    }

    .btn-checkout:hover {
        background: linear-gradient(135deg, #1d4ed8, #1e40af);
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(37, 99, 235, 0.5);
    }

    .secure-badge {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 1.5rem;
        color: #64748b;
        font-size: 0.875rem;
    }

    .secure-badge i {
        margin-right: 0.5rem;
        color: #10b981;
    }

    .btn-back-home {
        display: inline-flex;
        align-items: center;
        padding: 0.75rem 1.5rem;
        background: linear-gradient(to right, #f8f9fa, #e9ecef);
        color: #1e293b;
        font-weight: 600;
        border-radius: 0.75rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        text-decoration: none;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .btn-back-home:hover {
        background: linear-gradient(to right, #e9ecef, #dee2e6);
        transform: translateY(-2px);
        box-shadow: 0 6px 10px rgba(0, 0, 0, 0.08);
        color: #0f172a;
    }

    .plan-badge {
        display: inline-block;
        padding: 0.35em 0.65em;
        font-size: 0.75em;
        font-weight: 600;
        line-height: 1;
        text-align: center;
        white-space: nowrap;
        vertical-align: baseline;
        border-radius: 0.375rem;
        background: linear-gradient(135deg, #FF9500, #FF2D55);
        color: white;
        margin-left: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="checkout-container">
    <div class="checkout-header">
        <h1 class="checkout-title">Checkout</h1>
        <p class="checkout-subtitle">Complete your purchase to unlock premium features</p>
    </div>

    <div class="checkout-steps">
        <div class="checkout-step completed">
            <div class="step-number">1</div>
            <div class="step-label">Plan Selection</div>
        </div>
        <div class="checkout-step active">
            <div class="step-number">2</div>
            <div class="step-label">Payment Details</div>
        </div>
        <div class="checkout-step">
            <div class="step-number">3</div>
            <div class="step-label">Confirmation</div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="checkout-card">
                <div class="checkout-card-header">
                    <h5 class="checkout-card-title">
                        <i class="fas fa-credit-card"></i> Payment Method
                    </h5>
                </div>
                <div class="checkout-card-body">
                    <div class="payment-methods">
                        <div class="payment-method-option selected">
                            <img src="https://cdn.visa.com/v2/assets/images/logos/visa/blue/logo.png" alt="Credit Card" class="payment-icon">
                            <span class="payment-label">Credit Card</span>
                        </div>
                        <div class="payment-method-option">
                            <img src="https://www.paypalobjects.com/webstatic/mktg/logo/pp_cc_mark_111x69.jpg" alt="PayPal" class="payment-icon">
                            <span class="payment-label">PayPal</span>
                        </div>
                        <div class="payment-method-option">
                            <img src="https://developer.apple.com/apple-pay/marketing/images/apple-pay-mark.svg" alt="Apple Pay" class="payment-icon">
                            <span class="payment-label">Apple Pay</span>
                        </div>
                        <div class="payment-method-option mpesa-option">
                            <img src="https://www.safaricom.co.ke/images/M-PESA_LOGO.png" alt="M-Pesa" class="payment-icon">
                            <span class="payment-label">M-Pesa</span>
                        </div>
                    </div>

                    <form id="payment-form">
                        <div class="row">
                            <div class="col-12">
                                <div class="form-group">
                                    <label for="cardName" class="form-label">Name on Card</label>
                                    <input type="text" class="form-control" id="cardName" placeholder="John Doe">
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="form-group">
                                    <label for="cardNumber" class="form-label">Card Number</label>
                                    <input type="text" class="form-control" id="cardNumber" placeholder="1234 5678 9012 3456">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="expiryDate" class="form-label">Expiry Date</label>
                                    <input type="text" class="form-control" id="expiryDate" placeholder="MM/YY">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="cvv" class="form-label">CVV</label>
                                    <input type="text" class="form-control" id="cvv" placeholder="123">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <div class="checkout-card">
                <div class="checkout-card-header">
                    <h5 class="checkout-card-title">
                        <i class="fas fa-user"></i> Billing Information
                    </h5>
                </div>
                <div class="checkout-card-body">
                    <form id="billing-form">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="firstName" class="form-label">First Name</label>
                                    <input type="text" class="form-control" id="firstName" placeholder="John">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="lastName" class="form-label">Last Name</label>
                                    <input type="text" class="form-control" id="lastName" placeholder="Doe">
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="form-group">
                                    <label for="email" class="form-label">Email Address</label>
                                    <input type="email" class="form-control" id="email" placeholder="<EMAIL>">
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="form-group">
                                    <label for="address" class="form-label">Address</label>
                                    <input type="text" class="form-control" id="address" placeholder="123 Main St">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="city" class="form-label">City</label>
                                    <input type="text" class="form-control" id="city" placeholder="Nairobi">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="zipCode" class="form-label">Zip Code</label>
                                    <input type="text" class="form-control" id="zipCode" placeholder="00100">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="checkout-card">
                <div class="checkout-card-header">
                    <h5 class="checkout-card-title">
                        <i class="fas fa-shopping-cart"></i> Order Summary
                    </h5>
                </div>
                <div class="checkout-card-body">
                    <div class="order-summary">
                        <div class="summary-item">
                            <span>Plan</span>
                            <span>{{ plan|title }} <span class="plan-badge">{{ billing|title }}</span></span>
                        </div>
                        {% if is_annual and original_price %}
                        <div class="summary-item">
                            <span>Regular Price</span>
                            <span class="original-price">${{ original_price }}</span>
                        </div>
                        <div class="summary-item savings">
                            <span>Annual Savings (20%)</span>
                            <span class="savings-amount">${{ savings }}</span>
                        </div>
                        {% endif %}
                        <div class="summary-item">
                            <span>Price</span>
                            <span>${{ price }}</span>
                        </div>
                        <div class="summary-item">
                            <span>Tax</span>
                            <span>$0.00</span>
                        </div>
                        <div class="summary-item total">
                            <span>Total</span>
                            <span>${{ price }}</span>
                        </div>
                    </div>

                    <button type="button" class="btn btn-checkout mt-4">
                        Complete Purchase
                    </button>

                    <div class="secure-badge">
                        <i class="fas fa-lock"></i>
                        <span>Secure checkout - Your data is protected</span>
                    </div>

                    <div class="text-center mt-4">
                        <a href="{% url 'index' %}" class="btn-back-home">
                            <i class="fas fa-arrow-left me-2"></i>Back to Home
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Payment method selection
        const paymentOptions = document.querySelectorAll('.payment-method-option');

        paymentOptions.forEach(option => {
            option.addEventListener('click', function() {
                // Remove selected class from all options
                paymentOptions.forEach(opt => opt.classList.remove('selected'));

                // Add selected class to clicked option
                this.classList.add('selected');
            });
        });

        // Complete purchase button
        const checkoutBtn = document.querySelector('.btn-checkout');

        checkoutBtn.addEventListener('click', function() {
            // Show loading state
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Processing...';
            this.disabled = true;

            // Simulate processing
            setTimeout(() => {
                window.location.href = '{% url "checkout_success" %}';
            }, 2000);
        });
    });
</script>
{% endblock %}
