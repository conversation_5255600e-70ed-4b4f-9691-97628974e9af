#!/usr/bin/env python
"""
Test Script for Smart AI Engine
Tests the complete Smart AI Engine functionality including:
1. Cache-first strategy (6-hour TTL)
2. Groq model priority (llama3-70b → llama3-8b → mixtral)
3. Provider fallback (Groq → Mistral → OpenAI)
4. Best suggestion selection
5. Caching and metadata
"""

import os
import sys
import django
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'qrcode_project.settings')
django.setup()

import logging
import json
import time
from ai_services.clients import SmartAIEngine, get_ai_client
from ai_services.cache_utils import get_cached_suggestions, cache_suggestions
from ai_services.settings import AI_PROVIDER, GROQ_API_KEY, MISTRAL_API_KEY

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_smart_ai_engine():
    """Test the Smart AI Engine functionality"""
    print("🧠 TESTING SMART AI ENGINE")
    print("=" * 50)
    
    # Test parameters
    test_params = {
        'language': 'english',
        'business_type': 'Premium Coffee Shop',
        'target_audience': 'Young Professionals',
        'tone': 'professional',
        'num_suggestions': 3,
        'ad_title': 'Premium Coffee Experience'
    }
    
    print(f"📋 Test Parameters:")
    for key, value in test_params.items():
        print(f"   {key}: {value}")
    print()
    
    # Check configuration
    print(f"⚙️  Configuration:")
    print(f"   AI_PROVIDER: {AI_PROVIDER}")
    print(f"   GROQ_API_KEY: {'✅ Set' if GROQ_API_KEY else '❌ Missing'}")
    print(f"   MISTRAL_API_KEY: {'✅ Set' if MISTRAL_API_KEY else '❌ Missing'}")
    print()
    
    # Test 1: Initialize Smart AI Engine
    print("🔧 Test 1: Initialize Smart AI Engine")
    try:
        smart_engine = SmartAIEngine()
        print("   ✅ SmartAIEngine initialized successfully")
        print(f"   📊 Available clients:")
        print(f"      Groq: {'✅' if smart_engine.groq_client else '❌'}")
        print(f"      Mistral: {'✅' if smart_engine.mistral_client else '❌'}")
        print(f"      OpenAI: {'✅' if smart_engine.openai_client else '❌'}")
    except Exception as e:
        print(f"   ❌ Failed to initialize SmartAIEngine: {e}")
        return False
    print()
    
    # Test 2: Test get_ai_client factory
    print("🏭 Test 2: Test AI Client Factory")
    try:
        ai_client = get_ai_client()
        client_type = type(ai_client).__name__
        print(f"   ✅ get_ai_client() returned: {client_type}")
        if client_type == 'SmartAIEngine':
            print("   🎯 Correctly using SmartAIEngine for current AI_PROVIDER")
        else:
            print(f"   ⚠️  Using {client_type} instead of SmartAIEngine")
    except Exception as e:
        print(f"   ❌ Failed to get AI client: {e}")
        return False
    print()
    
    # Test 3: Clear cache for clean test
    print("🧹 Test 3: Clear Cache for Clean Test")
    try:
        import hashlib
        user_input = f"{test_params['language']}:{test_params['business_type']}:{test_params['target_audience']}:{test_params['tone']}:{test_params['ad_title']}:{test_params['num_suggestions']}".strip().lower()
        cache_key = f"smart_suggestions:{hashlib.md5(user_input.encode()).hexdigest()}"
        print(f"   🔑 Cache key: {cache_key}")
        
        # Check if cache exists
        cached = get_cached_suggestions(cache_key)
        if cached:
            print(f"   🗑️  Found existing cache with {len(cached)} suggestions")
            print("   ⚠️  Note: This will test cache retrieval instead of AI generation")
        else:
            print("   ✅ No existing cache found - will test AI generation")
    except Exception as e:
        print(f"   ❌ Error checking cache: {e}")
    print()
    
    # Test 4: Generate suggestions
    print("🚀 Test 4: Generate Ad Suggestions")
    start_time = time.time()
    try:
        suggestions = smart_engine.generate_ad_suggestions(**test_params)
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"   ✅ Generated {len(suggestions)} suggestions in {response_time:.2f} seconds")
        print(f"   ⚡ Performance: {'Excellent (cached)' if response_time < 0.5 else 'Good (AI generated)' if response_time < 5 else 'Slow'}")
        
        # Analyze suggestions
        if suggestions:
            print(f"\n   📊 Suggestion Analysis:")
            for i, suggestion in enumerate(suggestions, 1):
                print(f"      Suggestion {i}:")
                print(f"         Title: {suggestion.get('title', 'N/A')[:50]}...")
                print(f"         Content: {suggestion.get('content', 'N/A')[:80]}...")
                print(f"         Provider: {suggestion.get('provider', 'N/A')}")
                print(f"         Model: {suggestion.get('model', 'N/A')}")
                print(f"         Cached: {suggestion.get('cached', False)}")
                print(f"         Cache Source: {suggestion.get('cache_source', 'N/A')}")
                print(f"         Smart Engine: {suggestion.get('smart_engine', False)}")
                print(f"         Rank: {suggestion.get('rank', 'N/A')}")
                print(f"         Score: {suggestion.get('selection_score', 'N/A')}")
                print()
        else:
            print("   ❌ No suggestions generated")
            return False
            
    except Exception as e:
        print(f"   ❌ Failed to generate suggestions: {e}")
        import traceback
        traceback.print_exc()
        return False
    print()
    
    # Test 5: Test cache functionality
    print("🔄 Test 5: Test Cache Functionality")
    try:
        # Generate again to test cache
        print("   🔄 Generating suggestions again to test cache...")
        start_time = time.time()
        cached_suggestions = smart_engine.generate_ad_suggestions(**test_params)
        end_time = time.time()
        cache_response_time = end_time - start_time
        
        print(f"   ⚡ Second request took {cache_response_time:.2f} seconds")
        
        if cache_response_time < 0.5:
            print("   ✅ Cache is working - very fast response!")
        elif cache_response_time < 2:
            print("   ⚠️  Possibly cached but slower than expected")
        else:
            print("   ❌ Cache might not be working - slow response")
            
        # Check if suggestions are identical
        if len(suggestions) == len(cached_suggestions):
            identical = all(
                s1.get('title') == s2.get('title') and s1.get('content') == s2.get('content')
                for s1, s2 in zip(suggestions, cached_suggestions)
            )
            if identical:
                print("   ✅ Cache returned identical suggestions")
            else:
                print("   ⚠️  Cache returned different suggestions")
        
    except Exception as e:
        print(f"   ❌ Cache test failed: {e}")
    print()
    
    # Test 6: Validate suggestion quality
    print("🎯 Test 6: Validate Suggestion Quality")
    try:
        quality_score = 0
        total_checks = 0
        
        for i, suggestion in enumerate(suggestions, 1):
            print(f"   📝 Suggestion {i} Quality Check:")
            
            # Check required fields
            has_title = bool(suggestion.get('title'))
            has_content = bool(suggestion.get('content'))
            has_provider = bool(suggestion.get('provider'))
            
            print(f"      Title: {'✅' if has_title else '❌'}")
            print(f"      Content: {'✅' if has_content else '❌'}")
            print(f"      Provider: {'✅' if has_provider else '❌'}")
            
            # Check content relevance
            title = suggestion.get('title', '').lower()
            content = suggestion.get('content', '').lower()
            business_words = test_params['business_type'].lower().split()
            
            relevance_score = sum(1 for word in business_words if word in title or word in content)
            relevance_percentage = (relevance_score / len(business_words)) * 100
            
            print(f"      Business Relevance: {relevance_percentage:.0f}% ({'✅' if relevance_percentage > 50 else '⚠️' if relevance_percentage > 25 else '❌'})")
            
            # Check ad title relevance if provided
            if test_params['ad_title']:
                ad_title_words = test_params['ad_title'].lower().split()
                title_relevance = sum(1 for word in ad_title_words if word in title)
                title_relevance_percentage = (title_relevance / len(ad_title_words)) * 100 if ad_title_words else 0
                print(f"      Title Relevance: {title_relevance_percentage:.0f}% ({'✅' if title_relevance_percentage > 30 else '⚠️' if title_relevance_percentage > 10 else '❌'})")
            
            quality_score += (has_title + has_content + has_provider + (relevance_percentage > 50))
            total_checks += 4
            print()
        
        overall_quality = (quality_score / total_checks) * 100
        print(f"   📊 Overall Quality Score: {overall_quality:.0f}%")
        if overall_quality >= 80:
            print("   ✅ Excellent quality suggestions!")
        elif overall_quality >= 60:
            print("   ⚠️  Good quality suggestions")
        else:
            print("   ❌ Poor quality suggestions")
            
    except Exception as e:
        print(f"   ❌ Quality validation failed: {e}")
    print()
    
    print("🎉 SMART AI ENGINE TEST COMPLETED!")
    print("=" * 50)
    return True

if __name__ == "__main__":
    success = test_smart_ai_engine()
    if success:
        print("✅ All tests completed successfully!")
        print("\n🌐 Ready for browser testing!")
        print("   Navigate to your ad creation page and test with:")
        print("   - Business Type: Premium Coffee Shop")
        print("   - Target Audience: Young Professionals") 
        print("   - Tone: Professional")
        print("   - Ad Title: Premium Coffee Experience")
    else:
        print("❌ Some tests failed. Check the logs above.")
    
    sys.exit(0 if success else 1)
