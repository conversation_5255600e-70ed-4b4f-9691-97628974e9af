{% load static %}

<!-- Sidebar Ads Display -->
{% if ads %}
    <div class="sidebar-ads-container">
        <div class="sidebar-ads-header">
            <h4 class="sidebar-ads-title">
                <i class="fas fa-bullhorn"></i>
                Sponsored
            </h4>
        </div>

        <div class="sidebar-ads-content">
            {% for ad in ads %}
            <div class="sidebar-ad-item" data-ad-id="{{ ad.id }}" data-ad-type="{{ ad.ad_type.name }}" data-ad-location="sidebar">
                {% if ad.media and ad.media.name %}
                <div class="sidebar-ad-media">
                    <img src="/media/{{ ad.media }}" alt="{{ ad.title }}" class="sidebar-ad-image">
                </div>
                {% endif %}

                <div class="sidebar-ad-content">
                    <h5 class="sidebar-ad-title">{{ ad.title }}</h5>
                    <p class="sidebar-ad-description">{{ ad.content|truncatechars:80 }}</p>

                    {% if ad.cta_link %}
                    <a href="{{ ad.cta_link }}" target="_blank" class="sidebar-ad-cta" rel="noopener" onclick="trackAdClick('{{ ad.id }}')">
                        <span>Learn More</span>
                        <i class="fas fa-external-link-alt"></i>
                    </a>
                    {% else %}
                    <div class="sidebar-ad-cta sidebar-ad-cta-disabled">
                        <span>No Link</span>
                        <i class="fas fa-link-slash"></i>
                    </div>
                    {% endif %}
                </div>

                <div class="sidebar-ad-sponsor-label">Sponsored</div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Track impressions -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            {% for ad in ads %}
                trackAdImpression('{{ ad.id }}');
            {% endfor %}
        });
    </script>
{% endif %}

<style>
.sidebar-ads-container {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin: 20px 0;
    overflow: hidden;
}

.sidebar-ads-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 12px 15px;
    color: white;
}

.sidebar-ads-title {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.sidebar-ads-content {
    padding: 0;
}

.sidebar-ad-item {
    padding: 15px;
    border-bottom: 1px solid #e9ecef;
    position: relative;
    transition: background-color 0.3s ease;
}

.sidebar-ad-item:last-child {
    border-bottom: none;
}

.sidebar-ad-item:hover {
    background-color: #f1f3f4;
}

.sidebar-ad-media {
    margin-bottom: 10px;
}

.sidebar-ad-image {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: 6px;
}

.sidebar-ad-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: #333;
    line-height: 1.3;
}

.sidebar-ad-description {
    font-size: 13px;
    color: #666;
    margin: 0 0 12px 0;
    line-height: 1.4;
}

.sidebar-ad-cta {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    background: #007bff;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 12px;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.sidebar-ad-cta:hover {
    background: #0056b3;
    color: white;
    text-decoration: none;
}

.sidebar-ad-sponsor-label {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 500;
}

/* Mobile responsive */
@media (max-width: 768px) {
    .sidebar-ads-container {
        margin: 15px 0;
    }

    .sidebar-ad-item {
        padding: 12px;
    }

    .sidebar-ad-image {
        height: 100px;
    }

    .sidebar-ad-title {
        font-size: 15px;
    }

    .sidebar-ad-description {
        font-size: 12px;
    }
}
</style>
