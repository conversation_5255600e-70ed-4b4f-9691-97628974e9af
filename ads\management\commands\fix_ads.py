from django.core.management.base import BaseCommand
from ads.models import Ad, AdLocation

class Command(BaseCommand):
    help = 'Fixes ads with missing locations or pricing'

    def handle(self, *args, **kwargs):
        self.stdout.write(self.style.SUCCESS('Fixing Ads with Missing Data'))
        self.stdout.write('-' * 50)
        
        # Get default location
        try:
            default_location = AdLocation.objects.filter(is_active=True).first()
            if not default_location:
                self.stdout.write(self.style.ERROR('No active locations found. Cannot fix ads.'))
                return
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error getting default location: {e}'))
            return
            
        # Find ads with missing locations or pricing
        ads_to_fix = Ad.objects.filter(ad_location__isnull=True) | Ad.objects.filter(base_pricing=0) | Ad.objects.filter(final_pricing=0)
        
        if not ads_to_fix:
            self.stdout.write(self.style.SUCCESS('No ads need fixing.'))
            return
            
        self.stdout.write(f'Found {ads_to_fix.count()} ads to fix.')
        
        # Fix each ad
        for ad in ads_to_fix:
            self.stdout.write(f'Fixing ad: {ad.title}')
            
            # Set location if missing
            if not ad.ad_location:
                ad.ad_location = default_location
                self.stdout.write(f'  - Set location to: {default_location.name}')
            
            # Set base pricing if zero
            if ad.base_pricing == 0 and ad.ad_type:
                ad.base_pricing = ad.ad_type.base_price
                self.stdout.write(f'  - Set base pricing to: {ad.base_pricing}')
            
            # Recalculate final pricing
            if ad.ad_location and ad.base_pricing > 0:
                ad.final_pricing = ad.ad_location.get_adjusted_price(ad.base_pricing)
                self.stdout.write(f'  - Set final pricing to: {ad.final_pricing}')
            
            # Save the ad
            ad.save()
            
        self.stdout.write(self.style.SUCCESS(f'Successfully fixed {ads_to_fix.count()} ads'))
