"""
Management command to clear expired cache entries
"""
import logging
from django.core.management.base import BaseCommand
from ai_services.models import CachedSuggestion

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Clear expired cache entries from the database'

    def handle(self, *args, **options):
        """
        Clear expired cache entries
        """
        try:
            # Clear expired entries from the database
            count = CachedSuggestion.clear_expired()
            self.stdout.write(self.style.SUCCESS(f'Successfully cleared {count} expired cache entries'))
            logger.info(f'Successfully cleared {count} expired cache entries')
            
            # Try to clear expired entries from the enhanced cache
            try:
                from ai_services.enhanced_cache import clear_expired_cache
                clear_expired_cache()
                self.stdout.write(self.style.SUCCESS('Successfully cleared expired entries from enhanced cache'))
                logger.info('Successfully cleared expired entries from enhanced cache')
            except ImportError:
                self.stdout.write(self.style.WARNING('Enhanced cache module not available'))
                logger.warning('Enhanced cache module not available')
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error clearing expired cache entries: {str(e)}'))
            logger.error(f'Error clearing expired cache entries: {str(e)}', exc_info=True)
