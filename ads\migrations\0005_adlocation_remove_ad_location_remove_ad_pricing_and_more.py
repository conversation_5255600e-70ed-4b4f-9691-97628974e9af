# Generated by Django 5.1.7 on 2025-05-14 07:24

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ads', '0004_alter_ad_end_date_alter_ad_start_date'),
    ]

    operations = [
        migrations.CreateModel(
            name='AdLocation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('price_multiplier', models.DecimalField(decimal_places=2, default=1.0, help_text='Multiplier applied to base price (e.g., 1.5 = +50%)', max_digits=5)),
                ('visibility', models.CharField(choices=[('high', 'High Visibility'), ('medium', 'Medium Visibility'), ('low', 'Low Visibility')], default='medium', max_length=20)),
                ('is_premium', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('daily_impressions', models.PositiveIntegerField(default=0, help_text='Estimated daily impressions for this location')),
                ('image', models.ImageField(blank=True, help_text='Preview image of this ad location', null=True, upload_to='ad_locations/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.RemoveField(
            model_name='ad',
            name='location',
        ),
        migrations.RemoveField(
            model_name='ad',
            name='pricing',
        ),
        migrations.AddField(
            model_name='ad',
            name='base_pricing',
            field=models.DecimalField(decimal_places=2, default=0, help_text='Base price before location multiplier', max_digits=10),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='ad',
            name='final_pricing',
            field=models.DecimalField(decimal_places=2, default=0, help_text='Final price after all adjustments', max_digits=10),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='ad',
            name='target_location',
            field=models.CharField(blank=True, help_text='Geographic location targeting', max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='ad',
            name='ad_location',
            field=models.ForeignKey(blank=True, help_text='Placement location on the website', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='ads', to='ads.adlocation'),
        ),
    ]
