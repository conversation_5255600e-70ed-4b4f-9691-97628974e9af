{% extends "admin/change_list.html" %}
{% load i18n admin_urls static admin_list %}

{% block content %}
<div id="content-main">
    <div class="module" id="changelist">
        <div class="results">
            <div class="card">
                <div class="card-header">
                    <h3>AI Services Settings</h3>
                </div>
                <div class="card-body">
                    <h4>Current AI Provider: <span class="badge {% if ai_provider == 'mistral' %}bg-primary{% else %}bg-success{% endif %}">{{ ai_provider|title }}</span></h4>
                    
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h5>Mistral AI</h5>
                                </div>
                                <div class="card-body">
                                    <p><strong>API Key:</strong> {{ mistral_api_key|default:"Not configured" }}</p>
                                    <p><strong>Model:</strong> {{ mistral_model }}</p>
                                    <p><small class="text-muted">Free tier model: mistral-tiny</small></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h5>OpenAI</h5>
                                </div>
                                <div class="card-body">
                                    <p><strong>API Key:</strong> {{ openai_api_key|default:"Not configured" }}</p>
                                    <p><strong>Model:</strong> {{ openai_model }}</p>
                                    <p><small class="text-muted">Production model: gpt-4-turbo</small></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info mt-4">
                        <h5>How to Configure</h5>
                        <p>To configure the AI services, update the <code>.env</code> file in the project root with the following settings:</p>
                        <pre>
# AI Provider Settings
AI_PROVIDER=mistral  # 'mistral' or 'openai'

# Mistral AI Settings
MISTRAL_API_KEY=your_mistral_api_key_here
MISTRAL_MODEL=mistral-tiny

# OpenAI Settings
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4-turbo
                        </pre>
                        <p>After updating the .env file, restart the server for the changes to take effect.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
