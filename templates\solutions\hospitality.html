{% extends 'base.html' %}
{% load static %}

{% block title %}Enterprise QR | Hospitality Solutions{% endblock %}

{% block extra_css %}
<!-- Google Fonts - Poppins and Montserrat -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

<link rel="stylesheet" href="{% static 'css/solutions.css' %}">

<style>
    /* Ultra-Premium Sleek Corporate Solutions Styling */
    body {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 15%, #cbd5e1 30%, #94a3b8 50%, #64748b 70%, #475569 85%, #334155 100%);
        min-height: 100vh;
        font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        position: relative;
        overflow-x: hidden;
    }

    /* Elegant animated background with subtle corporate patterns */
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 25% 75%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),
            radial-gradient(circle at 75% 25%, rgba(255, 255, 255, 0.6) 0%, transparent 40%),
            radial-gradient(circle at 15% 85%, rgba(139, 92, 246, 0.06) 0%, transparent 45%),
            radial-gradient(circle at 85% 15%, rgba(16, 185, 129, 0.05) 0%, transparent 35%),
            radial-gradient(circle at 50% 50%, rgba(59, 130, 246, 0.04) 0%, transparent 60%);
        z-index: -1;
        animation: elegantCorporateFloat 120s ease-in-out infinite;
    }

    @keyframes elegantCorporateFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        25% { transform: translateY(-30px) rotate(1deg); }
        50% { transform: translateY(-20px) rotate(-1deg); }
        75% { transform: translateY(-35px) rotate(0.5deg); }
    }

    /* Override solution hero for corporate elegance */
    .solution-hero {
        background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(51, 65, 85, 0.95) 50%, rgba(71, 85, 105, 0.95) 100%) !important;
        backdrop-filter: blur(25px);
        border-radius: 24px;
        margin: 2rem;
        box-shadow:
            0 20px 40px rgba(0, 0, 0, 0.1),
            0 0 0 1px rgba(255, 255, 255, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.1);
        position: relative;
        overflow: hidden;
        animation: slideInDown 0.8s ease-out;
    }

    .solution-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
        animation: shimmer 3s ease-in-out infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    .solution-title {
        font-family: 'Montserrat', sans-serif !important;
        color: #ffffff !important;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 2;
    }

    .solution-subtitle {
        color: rgba(255, 255, 255, 0.9) !important;
        position: relative;
        z-index: 2;
    }

    .hero-cta {
        position: relative;
        z-index: 2;
    }

    /* Enhanced Mobile Responsiveness */
    @media (max-width: 768px) {
        .solution-hero {
            margin: 1rem;
            padding: 2rem 1.5rem;
            border-radius: 16px;
        }

        .solution-title {
            font-size: 2rem !important;
        }

        .solution-subtitle {
            font-size: 1rem !important;
        }

        .hero-cta .btn {
            margin: 0.25rem;
            padding: 0.75rem 1.5rem;
        }
    }

    @media (max-width: 576px) {
        .solution-hero {
            margin: 0.5rem;
            padding: 1.5rem 1rem;
        }

        .solution-title {
            font-size: 1.6rem !important;
        }

        .solution-subtitle {
            font-size: 0.9rem !important;
        }

        .hero-cta {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .hero-cta .btn {
            width: 100%;
            margin: 0;
        }
    }

    /* Enhanced Body Content Styling */
    .solution-overview,
    .solution-features,
    .solution-use-cases,
    .solution-cta {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
        backdrop-filter: blur(25px);
        border-radius: 24px;
        margin: 2rem;
        padding: 4rem 2rem;
        box-shadow:
            0 15px 35px rgba(0, 0, 0, 0.08),
            0 0 0 1px rgba(255, 255, 255, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.4);
        border: 1px solid rgba(255, 255, 255, 0.2);
        animation: slideInUp 0.8s ease-out;
        position: relative;
        overflow: hidden;
    }

    .solution-overview::before,
    .solution-features::before,
    .solution-use-cases::before,
    .solution-cta::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
        animation: shimmer 4s ease-in-out infinite;
        pointer-events: none;
    }

    .section-title {
        font-family: 'Montserrat', sans-serif !important;
        font-size: 2.2rem;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 1.5rem;
        position: relative;
        z-index: 2;
    }

    .section-description {
        font-size: 1.1rem;
        color: #475569;
        line-height: 1.7;
        margin-bottom: 2rem;
        position: relative;
        z-index: 2;
    }

    .feature-list {
        list-style: none;
        padding: 0;
        position: relative;
        z-index: 2;
    }

    .feature-list li {
        padding: 0.75rem 0;
        font-size: 1rem;
        color: #475569;
        display: flex;
        align-items: center;
    }

    .feature-list li i {
        color: #10b981;
        margin-right: 1rem;
        font-size: 1.1rem;
    }

    .feature-card {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
        backdrop-filter: blur(15px);
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow:
            0 10px 25px rgba(0, 0, 0, 0.05),
            0 0 0 1px rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
        position: relative;
        z-index: 2;
    }

    .feature-card:hover {
        transform: translateY(-8px);
        box-shadow:
            0 20px 40px rgba(0, 0, 0, 0.1),
            0 0 0 1px rgba(255, 255, 255, 0.3);
    }

    .feature-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1.5rem;
        box-shadow: 0 8px 20px rgba(139, 92, 246, 0.3);
    }

    .feature-icon i {
        font-size: 1.5rem;
        color: white;
    }

    .feature-card h3 {
        font-family: 'Montserrat', sans-serif !important;
        font-size: 1.3rem;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 1rem;
    }

    .feature-card p {
        color: #475569;
        line-height: 1.6;
        margin: 0;
    }

    .use-case-card {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
        backdrop-filter: blur(15px);
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow:
            0 10px 25px rgba(0, 0, 0, 0.05),
            0 0 0 1px rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
        display: flex;
        align-items: flex-start;
        position: relative;
        z-index: 2;
    }

    .use-case-card:hover {
        transform: translateY(-5px);
        box-shadow:
            0 15px 30px rgba(0, 0, 0, 0.08),
            0 0 0 1px rgba(255, 255, 255, 0.3);
    }

    .use-case-icon {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1.5rem;
        flex-shrink: 0;
        box-shadow: 0 6px 15px rgba(139, 92, 246, 0.3);
    }

    .use-case-icon i {
        font-size: 1.2rem;
        color: white;
    }

    .use-case-content h3 {
        font-family: 'Montserrat', sans-serif !important;
        font-size: 1.2rem;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 0.75rem;
    }

    .use-case-content p {
        color: #475569;
        line-height: 1.6;
        margin: 0;
    }

    .solution-cta {
        background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(51, 65, 85, 0.95) 100%) !important;
        text-align: center;
    }

    .cta-content h2 {
        font-family: 'Montserrat', sans-serif !important;
        font-size: 2.5rem;
        font-weight: 700;
        color: #ffffff;
        margin-bottom: 1rem;
        position: relative;
        z-index: 2;
    }

    .cta-content p {
        font-size: 1.2rem;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 2rem;
        position: relative;
        z-index: 2;
    }

    .cta-buttons {
        position: relative;
        z-index: 2;
    }

    .cta-buttons .btn {
        margin: 0.5rem;
        padding: 0.875rem 2rem;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block content %}
<div class="solution-hero hospitality-hero">
    <div class="solution-hero-content">
        <h1 class="solution-title">
            <i class="fas fa-concierge-bell me-3 text-warning"></i>
            Hospitality QR Solutions
        </h1>
        <p class="solution-subtitle">Enhance guest experiences with seamless QR code technology</p>
        <div class="hero-cta">
            <a href="{% url 'generate_qr_code' %}" class="btn btn-primary btn-lg">Get Started</a>
            <a href="#features" class="btn btn-outline-light btn-lg">Learn More</a>
        </div>
    </div>
    <div class="solution-hero-image">
        <img src="{% static 'img/solutions/hospitality-hero.svg' %}" alt="Hospitality QR Solutions">
    </div>
</div>

<div class="solution-overview">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h2 class="section-title">Elevate Your Guest Experience</h2>
                <p class="section-description">Our hospitality QR solutions help hotels, restaurants, and tourism businesses create seamless, contactless experiences that delight guests while streamlining operations.</p>
                <p>With Enterprise QR's hospitality solutions, you can:</p>
                <ul class="feature-list">
                    <li><i class="fas fa-check-circle"></i> Offer contactless check-in/check-out</li>
                    <li><i class="fas fa-check-circle"></i> Create digital menus and room service</li>
                    <li><i class="fas fa-check-circle"></i> Provide interactive property guides</li>
                    <li><i class="fas fa-check-circle"></i> Streamline guest feedback</li>
                    <li><i class="fas fa-check-circle"></i> Enable touchless payments</li>
                </ul>
            </div>
            <div class="col-lg-6">
                <div class="overview-image">
                    <img src="{% static 'img/solutions/hospitality-overview.svg' %}" alt="Hospitality QR Overview">
                </div>
            </div>
        </div>
    </div>
</div>

<div id="features" class="solution-features">
    <div class="container">
        <h2 class="section-title text-center">Key Features for Hospitality Businesses</h2>
        <div class="row">
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-concierge-bell"></i>
                    </div>
                    <h3>Contactless Service</h3>
                    <p>Enable seamless, touchless experiences from check-in to room service with QR-enabled systems.</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-utensils"></i>
                    </div>
                    <h3>Digital Menus</h3>
                    <p>Create dynamic, multilingual digital menus that can be updated in real-time with seasonal items and specials.</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-map-marked-alt"></i>
                    </div>
                    <h3>Interactive Guides</h3>
                    <p>Provide guests with interactive property maps, local attraction guides, and personalized recommendations.</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <h3>Guest Feedback</h3>
                    <p>Collect real-time feedback at various touchpoints to improve service quality and address issues promptly.</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-wifi"></i>
                    </div>
                    <h3>Wi-Fi Access</h3>
                    <p>Simplify guest Wi-Fi access with scannable QR codes that connect devices automatically to secure networks.</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <h3>Event Management</h3>
                    <p>Streamline conference and event management with QR codes for registration, agenda access, and feedback.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="solution-use-cases">
    <div class="container">
        <h2 class="section-title text-center">Hospitality Use Cases</h2>
        <div class="row">
            <div class="col-lg-6">
                <div class="use-case-card">
                    <div class="use-case-icon">
                        <i class="fas fa-hotel"></i>
                    </div>
                    <div class="use-case-content">
                        <h3>Hotel Room Guides</h3>
                        <p>Replace printed room directories with digital guides accessible via QR codes, including room controls, amenities, and service requests.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="use-case-card">
                    <div class="use-case-icon">
                        <i class="fas fa-key"></i>
                    </div>
                    <div class="use-case-content">
                        <h3>Digital Room Keys</h3>
                        <p>Enable secure, contactless room access with QR code-based digital keys that can be sent directly to guests' smartphones.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="use-case-card">
                    <div class="use-case-icon">
                        <i class="fas fa-glass-cheers"></i>
                    </div>
                    <div class="use-case-content">
                        <h3>Restaurant Ordering</h3>
                        <p>Streamline the dining experience with QR code menus that allow guests to browse, order, and pay directly from their table.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="use-case-card">
                    <div class="use-case-icon">
                        <i class="fas fa-suitcase"></i>
                    </div>
                    <div class="use-case-content">
                        <h3>Tour & Activity Bookings</h3>
                        <p>Facilitate easy booking of local tours, activities, and experiences through scannable QR codes placed throughout your property.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="solution-cta">
    <div class="container">
        <div class="cta-content">
            <h2>Ready to Transform Your Guest Experience?</h2>
            <p>Join leading hospitality brands that trust Enterprise QR for their QR code solutions.</p>
            <div class="cta-buttons">
                <a href="{% url 'generate_qr_code' %}" class="btn btn-primary btn-lg">Get Started Now</a>
                <a href="#" class="btn btn-outline-light btn-lg">Contact Sales</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
