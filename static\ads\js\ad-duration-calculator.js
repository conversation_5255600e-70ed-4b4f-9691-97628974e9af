/**
 * Ad Duration Calculator JavaScript
 * Handles duration calculation and end time display for ad creation/edit forms
 * This is a standalone version for pages that don't use the consolidated script
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Ad Duration Calculator: Initializing...');

    const durationOption = document.getElementById('durationOption');
    const customDurationFields = document.getElementById('customDurationFields');
    const startDate = document.getElementById('startDate');
    const startTime = document.getElementById('startTime');
    const endDate = document.getElementById('endDate');
    const endTime = document.getElementById('endTime');
    const endDateTimeDisplay = document.getElementById('endDateTimeDisplay');
    const calculatedEndTime = document.getElementById('calculatedEndTime');

    // Set default start date if empty
    if (startDate && !startDate.value) {
        const today = new Date();
        startDate.value = today.toISOString().split('T')[0];
    }

    // Set default start time if empty
    if (startTime && !startTime.value) {
        const now = new Date();
        startTime.value = now.toTimeString().slice(0, 5);
    }

    // Update calculated end time
    function updateCalculatedEndTime() {
        if (!startDate?.value || !startTime?.value) return;

        const start = new Date(`${startDate.value}T${startTime.value}`);
        let durationDays = 7;
        let end;

        if (durationOption?.value === 'custom' && endDate?.value && endTime?.value) {
            end = new Date(`${endDate.value}T${endTime.value}`);
            const diffTime = Math.abs(end - start);
            durationDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            if (durationDays < 1) durationDays = 1;
        } else {
            switch(durationOption?.value) {
                case '7days': durationDays = 7; break;
                case '2weeks': durationDays = 14; break;
                case 'monthly': durationDays = 30; break;
                case 'yearly': durationDays = 365; break;
            }
            end = new Date(start);
            end.setDate(end.getDate() + durationDays);
        }

        // Add 2 hours bonus
        end.setHours(end.getHours() + 2);

        if (endDateTimeDisplay) {
            const options = {
                weekday: 'long', year: 'numeric', month: 'long', day: 'numeric',
                hour: '2-digit', minute: '2-digit'
            };
            endDateTimeDisplay.textContent = end.toLocaleDateString('en-US', options);
        }

        // Show/hide calculated end time display
        if (calculatedEndTime) {
            if (durationOption?.value === 'custom') {
                calculatedEndTime.style.display = 'none';
            } else {
                calculatedEndTime.style.display = 'block';
            }
        }

        // Update duration in review if function exists
        if (typeof updateDurationInReview === 'function') {
            updateDurationInReview(durationDays);
        }

        // Update pricing if function exists
        if (typeof updatePricing === 'function') {
            updatePricing();
        }
    }

    // Duration option change handler
    if (durationOption) {
        durationOption.addEventListener('change', function() {
            if (customDurationFields) {
                customDurationFields.style.display = this.value === 'custom' ? 'block' : 'none';
            }
            updateCalculatedEndTime();
        });
    }

    // Date/time change handlers
    [startDate, startTime, endDate, endTime].forEach(element => {
        if (element) {
            element.addEventListener('change', updateCalculatedEndTime);
        }
    });

    // Initialize
    updateCalculatedEndTime();

    // Export function for external use
    window.updateCalculatedEndTime = updateCalculatedEndTime;

    console.log('Ad Duration Calculator: Initialized successfully');
});
