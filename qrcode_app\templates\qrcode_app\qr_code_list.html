{% extends "base.html" %}
{% load static %}

{% block title %}My QR Codes | Enterprise QR{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/qr-code-list.css' %}">
<style>
    /* Ultra-Premium Enterprise Corporate Styling */
    body {
        background:
            linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #0f3460 50%, #533483 75%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        position: relative;
        overflow-x: hidden;
    }

    /* Premium animated background with corporate patterns */
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 15% 85%, rgba(102, 126, 234, 0.4) 0%, transparent 40%),
            radial-gradient(circle at 85% 15%, rgba(255, 255, 255, 0.15) 0%, transparent 35%),
            radial-gradient(circle at 45% 55%, rgba(118, 75, 162, 0.3) 0%, transparent 45%),
            radial-gradient(circle at 75% 75%, rgba(83, 52, 131, 0.25) 0%, transparent 30%);
        z-index: -1;
        animation: premiumBackgroundFloat 25s ease-in-out infinite;
    }

    /* Corporate grid pattern overlay */
    body::after {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image:
            linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
            linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
        background-size: 50px 50px;
        z-index: -1;
        opacity: 0.5;
        animation: gridPulse 8s ease-in-out infinite;
    }

    @keyframes premiumBackgroundFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
        25% { transform: translateY(-15px) rotate(1deg) scale(1.02); }
        50% { transform: translateY(5px) rotate(-0.5deg) scale(0.98); }
        75% { transform: translateY(-8px) rotate(0.8deg) scale(1.01); }
    }

    @keyframes gridPulse {
        0%, 100% { opacity: 0.3; }
        50% { opacity: 0.6; }
    }

    /* Premium Container */
    .qr-list-container {
        max-width: 1400px;
        padding: 3rem 2rem;
        position: relative;
        z-index: 1;
    }

    /* Premium Page Title */
    .qr-list-title {
        color: white;
        font-weight: 800;
        font-size: 2.5rem;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
        margin-bottom: 1.5rem;
        background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .qr-list-title i {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #ffffff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
    }

    /* Premium Page Info */
    .qr-page-info {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.1) 100%);
        border-radius: 25px;
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
        color: rgba(255, 255, 255, 0.9);
        box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        display: inline-flex;
        align-items: center;
        margin-bottom: 2rem;
        border: 1px solid rgba(255, 255, 255, 0.2);
        font-weight: 600;
        backdrop-filter: blur(20px);
    }

    .qr-page-info i {
        color: rgba(255, 255, 255, 0.8);
        margin-right: 8px;
    }

    .qr-page-info strong {
        color: white;
        font-weight: 700;
    }

    /* Premium Search Container */
    .qr-search-container {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 3rem;
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow:
            0 20px 40px rgba(0, 0, 0, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }

    /* Premium Form Controls */
    .qr-search-input {
        border-radius: 20px;
        padding: 1rem 1.5rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        font-weight: 500;
        transition: all 0.4s ease;
    }

    .qr-search-input:focus {
        border-color: rgba(255, 255, 255, 0.4);
        box-shadow:
            0 0 0 0.25rem rgba(255, 255, 255, 0.15),
            0 8px 25px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
        background: rgba(255, 255, 255, 1);
    }

    .input-group-text {
        border-radius: 20px 0 0 20px;
        border: 2px solid rgba(255, 255, 255, 0.2);
        border-right: none;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
    }

    /* Premium Buttons */
    .qr-search-btn {
        border-radius: 20px;
        padding: 1rem 2rem;
        font-weight: 700;
        font-size: 0.9rem;
        letter-spacing: 0.5px;
        transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        text-transform: uppercase;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow:
            0 8px 25px rgba(102, 126, 234, 0.3),
            0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .qr-search-btn:hover {
        background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        transform: translateY(-4px) scale(1.02);
        box-shadow:
            0 15px 40px rgba(102, 126, 234, 0.4),
            0 8px 25px rgba(0, 0, 0, 0.15);
        color: white;
        border-color: rgba(255, 255, 255, 0.3);
    }

    /* Ultra-Premium QR Cards */
    .qr-card {
        border-radius: 32px;
        overflow: hidden;
        transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.2),
            0 15px 30px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        border: 2px solid rgba(255, 255, 255, 0.25);
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.95) 0%,
            rgba(248, 249, 250, 0.9) 50%,
            rgba(255, 255, 255, 0.95) 100%);
        backdrop-filter: blur(30px);
        position: relative;
        animation: cardEntry 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }

    .qr-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg,
            rgba(102, 126, 234, 0.08) 0%,
            rgba(255, 255, 255, 0.1) 50%,
            rgba(118, 75, 162, 0.08) 100%);
        z-index: 0;
        pointer-events: none;
        border-radius: 32px;
    }

    .qr-card:hover {
        box-shadow:
            0 40px 80px rgba(0, 0, 0, 0.25),
            0 20px 40px rgba(0, 0, 0, 0.15),
            0 0 60px rgba(102, 126, 234, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.4);
        transform: translateY(-12px) scale(1.03);
        border-color: rgba(255, 255, 255, 0.4);
    }

    @keyframes cardEntry {
        0% {
            opacity: 0;
            transform: translateY(40px) scale(0.95);
            filter: blur(10px);
        }
        100% {
            opacity: 1;
            transform: translateY(0) scale(1);
            filter: blur(0px);
        }
    }

    /* Premium Enterprise Badge */
    .enterprise-badge {
        position: absolute;
        top: 15px;
        right: 15px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 12px;
        font-size: 0.7rem;
        font-weight: 800;
        letter-spacing: 1px;
        box-shadow:
            0 8px 25px rgba(102, 126, 234, 0.4),
            0 4px 15px rgba(0, 0, 0, 0.2);
        z-index: 3;
        transition: all 0.4s ease;
        text-transform: uppercase;
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .qr-card:hover .enterprise-badge {
        transform: translateY(-5px) scale(1.05);
        box-shadow:
            0 12px 35px rgba(102, 126, 234, 0.5),
            0 6px 20px rgba(0, 0, 0, 0.3);
    }

    /* Premium QR Card Counter */
    .qr-card-counter {
        position: absolute;
        top: -15px;
        left: -15px;
        width: 45px;
        height: 45px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 800;
        font-size: 1rem;
        box-shadow:
            0 8px 25px rgba(102, 126, 234, 0.4),
            0 4px 15px rgba(0, 0, 0, 0.2);
        z-index: 3;
        transition: all 0.4s ease;
        border: 2px solid rgba(255, 255, 255, 0.3);
    }

    .qr-card:hover .qr-card-counter {
        transform: scale(1.15) rotate(5deg);
        box-shadow:
            0 12px 35px rgba(102, 126, 234, 0.5),
            0 6px 20px rgba(0, 0, 0, 0.3);
    }

    /* Premium QR Image Container */
    .qr-card-image-container {
        position: relative;
        margin-bottom: 2rem;
        padding: 15px;
        border-radius: 24px;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 249, 250, 0.8) 100%);
        box-shadow:
            0 15px 30px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.5);
        transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .qr-card:hover .qr-card-image-container {
        box-shadow:
            0 25px 50px rgba(102, 126, 234, 0.2),
            0 12px 24px rgba(0, 0, 0, 0.15),
            inset 0 1px 0 rgba(255, 255, 255, 0.6);
        transform: scale(1.02);
    }

    .qr-card-image {
        border-radius: 16px;
        transition: all 0.4s ease;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .qr-card:hover .qr-card-image {
        transform: scale(1.05);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    }
</style>
{% endblock %}

{% block content %}
<div class="container qr-list-container">
    <!-- Page Header -->
    <div class="row mb-4 align-items-center">
        <div class="col-lg-8 col-md-7">
            <h1 class="h3 qr-list-title">
                <i class="fas fa-qrcode me-2"></i>My QR Codes
            </h1>
            {% if page_obj and page_obj.paginator.count > 0 %}
            <div class="qr-page-info">
                <i class="fas fa-info-circle me-1"></i> Showing
                <strong>{{ page_obj.start_index }} - {{ page_obj.end_index }}</strong>
                of <strong>{{ page_obj.paginator.count }}</strong> QR codes
            </div>
            {% endif %}
        </div>
        <div class="col-lg-4 col-md-5 text-md-end mt-3 mt-md-0">
            <a href="{% url 'generate_qr_code' %}" class="btn qr-search-btn">
                <i class="fas fa-plus me-2"></i>Generate New QR Code
            </a>
        </div>
    </div>

    <!-- Search Container -->
    <div class="qr-search-container">
        <form method="get" class="row g-2">
            <div class="col-lg-10 col-md-9 col-sm-8">
                <div class="input-group">
                    <span class="input-group-text bg-white border-end-0">
                        <i class="fas fa-search text-muted"></i>
                    </span>
                    <input type="text" name="q" class="form-control qr-search-input border-start-0"
                           placeholder="Search by name, type, or content..."
                           value="{{ query|default:'' }}"
                           aria-label="Search QR codes">
                </div>
            </div>
            <div class="col-lg-2 col-md-3 col-sm-4">
                <button type="submit" class="btn qr-search-btn w-100">
                    <i class="fas fa-search me-2"></i>Search
                </button>
            </div>
        </form>
    </div>

    <!-- QR Code Cards -->
    {% if page_obj %}
        <div class="row qr-card-container">
            {% for qr_code in page_obj %}
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="qr-card">
                        <div class="qr-card-header">
                            <h5 class="qr-card-title" title="{{ qr_code.name }}">{{ qr_code.name }}</h5>
                            <span class="qr-card-type">{{ qr_code.get_qr_type_display }}</span>
                        </div>
                        <div class="qr-card-body">
                            <div class="qr-card-image-container">
                                <div class="qr-card-counter">{{ forloop.counter }}</div>
                                <a href="{% url 'qr_code_detail' qr_code.pk %}">
                                    {% if qr_code.image %}
                                        <img src="{{ qr_code.image.url }}" alt="{{ qr_code.name }}" class="qr-card-image">
                                    {% else %}
                                        <div class="qr-card-placeholder">
                                            <i class="fas fa-qrcode" style="font-size: 3rem; color: #dee2e6;"></i>
                                            <p style="margin-top: 10px; color: #6c757d; font-size: 0.9rem;">No image available</p>
                                        </div>
                                    {% endif %}
                                </a>
                                {% if qr_code.is_premium %}
                                <span class="enterprise-badge">PREMIUM</span>
                                {% elif forloop.counter == 1 %}
                                <span class="enterprise-badge" style="background: linear-gradient(135deg, #20c997, #0ca678);">LATEST</span>
                                {% endif %}
                            </div>
                            <div class="qr-card-date">
                                <i class="far fa-calendar-alt me-1"></i> Created: {{ qr_code.created_at|date:"F d, Y" }}
                            </div>
                            <div class="qr-card-stats" style="display: flex; gap: 15px; justify-content: center; margin-bottom: 1rem;">
                                <div class="qr-stat" style="display: flex; align-items: center; background: rgba(248, 249, 250, 0.7); padding: 0.4rem 0.8rem; border-radius: 20px; font-size: 0.8rem; color: #6c757d; border: 1px solid rgba(233, 236, 239, 0.5);">
                                    <i class="fas fa-eye me-1" style="color: #4a6cf7;"></i> 0 Views
                                </div>
                                <div class="qr-stat" style="display: flex; align-items: center; background: rgba(248, 249, 250, 0.7); padding: 0.4rem 0.8rem; border-radius: 20px; font-size: 0.8rem; color: #6c757d; border: 1px solid rgba(233, 236, 239, 0.5);">
                                    <i class="fas fa-download me-1" style="color: #4a6cf7;"></i> 0 Downloads
                                </div>
                            </div>
                        </div>
                        <div class="qr-card-footer">
                            <div class="qr-card-actions">
                                <a href="{% url 'generate_qr_code' %}?edit={{ qr_code.pk }}" class="btn qr-action-btn qr-view-btn">
                                    <i class="fas fa-edit"></i><span class="d-none d-sm-inline">Edit</span>
                                </a>
                                {% if qr_code.image %}<a href="{{ qr_code.image.url }}" download="qrcode-{{ qr_code.name|slugify }}.png" class="btn qr-action-btn qr-download-btn">{% else %}<a href="#" class="btn qr-action-btn qr-download-btn disabled" title="No image available">{% endif %}
                                    <i class="fas fa-download"></i><span class="d-none d-sm-inline">Download</span>
                                </a>
                                <a href="https://api.whatsapp.com/send?text={{ request.build_absolute_uri }}{% url 'qr_code_detail' qr_code.pk %}"
                                   target="_blank"
                                   class="btn qr-action-btn qr-share-btn share-whatsapp-btn"
                                   data-qr-id="{{ qr_code.pk }}"
                                   data-qr-name="{{ qr_code.name }}"
                                   data-qr-url="{{ request.build_absolute_uri }}{% url 'qr_code_detail' qr_code.pk %}">
                                    <i class="fab fa-whatsapp"></i><span class="d-none d-sm-inline">Share</span>
                                </a>
                                <a href="{% url 'delete_qr_code' qr_code.pk %}"
                                   class="btn qr-action-btn qr-delete-btn delete-qr-btn"
                                   data-qr-id="{{ qr_code.pk }}"
                                   data-qr-name="{{ qr_code.name }}">
                                    <i class="fas fa-trash"></i><span class="d-none d-sm-inline">Delete</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
        <nav aria-label="QR code pagination" class="qr-pagination">
            <ul class="pagination">
                {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page=1{% if query %}&q={{ query }}{% endif %}" aria-label="First">
                        <i class="fas fa-angle-double-left"></i>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if query %}&q={{ query }}{% endif %}" aria-label="Previous">
                        <i class="fas fa-angle-left"></i>
                    </a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link"><i class="fas fa-angle-double-left"></i></span>
                </li>
                <li class="page-item disabled">
                    <span class="page-link"><i class="fas fa-angle-left"></i></span>
                </li>
                {% endif %}

                {% for i in page_obj.paginator.page_range %}
                    {% if page_obj.number == i %}
                    <li class="page-item active">
                        <span class="page-link">{{ i }}</span>
                    </li>
                    {% elif i > page_obj.number|add:'-3' and i < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ i }}{% if query %}&q={{ query }}{% endif %}">{{ i }}</a>
                    </li>
                    {% endif %}
                {% endfor %}

                {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if query %}&q={{ query }}{% endif %}" aria-label="Next">
                        <i class="fas fa-angle-right"></i>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if query %}&q={{ query }}{% endif %}" aria-label="Last">
                        <i class="fas fa-angle-double-right"></i>
                    </a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link"><i class="fas fa-angle-right"></i></span>
                </li>
                <li class="page-item disabled">
                    <span class="page-link"><i class="fas fa-angle-double-right"></i></span>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    {% else %}
        <!-- Empty State -->
        <div class="qr-empty-state">
            <div class="qr-empty-icon">
                <i class="fas fa-qrcode"></i>
            </div>
            <h3 class="qr-empty-title">No QR Codes Found</h3>
            <p class="qr-empty-text">You haven't created any QR codes yet or none match your search criteria.</p>
            <a href="{% url 'generate_qr_code' %}" class="btn qr-search-btn">
                <i class="fas fa-plus me-2"></i>Generate Your First QR Code
            </a>
        </div>
    {% endif %}
</div>
<!-- Delete QR Code Modal -->
<div class="modal fade" id="deleteQRModal" tabindex="-1" aria-labelledby="deleteQRModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteQRModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>Delete QR Code
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <h6 class="alert-heading">
                        <i class="fas fa-exclamation-circle me-2"></i>Warning
                    </h6>
                    <p id="delete-warning-text">
                        Are you sure you want to delete this QR code? This action cannot be undone.
                    </p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary modal-close-btn" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancel
                </button>
                <a href="#" class="btn btn-danger" id="confirm-delete-qr">
                    <i class="fas fa-trash me-1"></i>Delete QR Code
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Modal backdrop fix and animations -->
<style>
    /* Ensure modal backdrop is properly removed */
    .modal-backdrop {
        z-index: 1040 !important;
    }
    .modal {
        z-index: 1050 !important;
    }

    /* WhatsApp share button animation */
    @keyframes share-pulse {
        0% {
            transform: scale(1) translateY(-3px);
        }
        50% {
            transform: scale(1.1) translateY(-3px);
        }
        100% {
            transform: scale(1) translateY(-3px);
        }
    }

    .animate-share {
        animation: share-pulse 0.5s ease;
    }

    /* Additional button animations */
    @keyframes button-pop {
        0% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.05);
        }
        100% {
            transform: scale(1);
        }
    }

    .qr-action-btn:active {
        animation: button-pop 0.3s ease;
    }

    /* QR Card Placeholder Styling */
    .qr-card-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 200px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: 2px dashed #dee2e6;
        border-radius: 8px;
        text-align: center;
        transition: all 0.3s ease;
    }

    .qr-card-placeholder:hover {
        background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
        border-color: #adb5bd;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    try {
        // WhatsApp share functionality
        const shareButtons = document.querySelectorAll('.share-whatsapp-btn');
        shareButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // The href is already set to the WhatsApp share URL
                // Just add animation for visual feedback
                this.classList.add('animate-share');
                setTimeout(() => {
                    this.classList.remove('animate-share');
                }, 500);
            });
        });

        // Delete QR code functionality
        const deleteButtons = document.querySelectorAll('.delete-qr-btn');
        const deleteModalElement = document.getElementById('deleteQRModal');
        const deleteWarningText = document.getElementById('delete-warning-text');
        const confirmDeleteBtn = document.getElementById('confirm-delete-qr');
        const modalCloseBtn = document.querySelector('.modal-close-btn');

        // Make sure the modal element exists
        if (!deleteModalElement) {
            console.error('Modal element not found');
            return;
        }

        // Initialize the modal with specific options
        const deleteModal = new bootstrap.Modal(deleteModalElement, {
            backdrop: 'static',  // Prevents closing when clicking outside
            keyboard: false      // Prevents closing with the keyboard
        });

        deleteButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();

                // Get QR code details
                const qrId = this.getAttribute('data-qr-id');
                const qrName = this.getAttribute('data-qr-name');
                const deleteUrl = this.getAttribute('href');

                // Update modal content
                deleteWarningText.innerHTML = `Are you sure you want to delete the QR code "<strong>${qrName}</strong>"? This action cannot be undone.`;
                confirmDeleteBtn.setAttribute('href', deleteUrl);

                // Show the modal
                deleteModal.show();
            });
        });

        // Add click event to confirm delete button
        if (confirmDeleteBtn) {
            confirmDeleteBtn.addEventListener('click', function(e) {
                // Hide the modal first
                deleteModal.hide();
                // Remove backdrop manually
                $('.modal-backdrop').remove();
                // Fix body
                $('body').removeClass('modal-open').css('overflow', '').css('padding-right', '');

                // The href attribute is already set to the delete URL
                // We'll let the default behavior (following the href) work
                console.log('Delete confirmed, redirecting to:', this.getAttribute('href'));
            });
        }

        // Close button handler
        if (modalCloseBtn) {
            modalCloseBtn.addEventListener('click', function() {
                // Hide the modal
                deleteModal.hide();
                // Remove backdrop manually
                $('.modal-backdrop').remove();
                // Fix body
                $('body').removeClass('modal-open').css('overflow', '').css('padding-right', '');
            });
        }

        // Handle modal hidden event
        $(deleteModalElement).on('hidden.bs.modal', function() {
            // Remove backdrop manually
            $('.modal-backdrop').remove();
            // Fix body
            $('body').removeClass('modal-open').css('overflow', '').css('padding-right', '');
        });

        // Add a direct close method to the close button
        $('.btn-close').on('click', function() {
            // Hide the modal
            deleteModal.hide();
            // Remove backdrop manually
            $('.modal-backdrop').remove();
            // Fix body
            $('body').removeClass('modal-open').css('overflow', '').css('padding-right', '');
        });

        // Add hover effects to QR cards
        const qrCards = document.querySelectorAll('.qr-card');
        qrCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                // Add subtle glow effect
                this.style.boxShadow = '0 10px 25px rgba(74, 108, 247, 0.15)';
            });

            card.addEventListener('mouseleave', function() {
                // Remove glow effect
                this.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.05)';
            });
        });

        // Add page transition effect
        const pageLinks = document.querySelectorAll('.page-link');
        pageLinks.forEach(link => {
            if (!link.parentElement.classList.contains('disabled') &&
                !link.parentElement.classList.contains('active')) {

                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Get the href
                    const href = this.getAttribute('href');

                    // Add fade-out effect to the cards
                    const cardContainer = document.querySelector('.qr-card-container');
                    if (cardContainer) {
                        cardContainer.style.opacity = '0';
                        cardContainer.style.transition = 'opacity 0.3s ease';
                    }

                    // Navigate after animation
                    setTimeout(() => {
                        window.location.href = href;
                    }, 300);
                });
            }
        });

        // WhatsApp Share Functionality
        const shareButtons = document.querySelectorAll('.share-whatsapp-btn');
        shareButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();

                // Get QR code details
                const qrName = this.getAttribute('data-qr-name');
                const qrUrl = this.getAttribute('data-qr-url');

                // Create share message
                const shareMessage = `Check out my QR code "${qrName}": ${qrUrl}`;

                // Encode the message for WhatsApp
                const encodedMessage = encodeURIComponent(shareMessage);

                // Create WhatsApp share URL
                const whatsappUrl = `https://wa.me/?text=${encodedMessage}`;

                // Add animation to the button
                this.classList.add('animate-share');
                setTimeout(() => {
                    this.classList.remove('animate-share');
                }, 500);

                // Open WhatsApp share in a new window
                window.open(whatsappUrl, '_blank');
            });
        });

    } catch (error) {
        console.error('Error initializing functionality:', error);
    }
});
</script>
{% endblock %}
