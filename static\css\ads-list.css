/* Ads List Styles */

/* Table styles */
.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* Badge styles */
.badge {
    font-size: 0.85rem;
    padding: 0.4em 0.6em;
}

/* Card styles */
.card {
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card-header {
    font-weight: 600;
}

/* Ensure text is visible on dark backgrounds */
.bg-dark h1, .bg-dark h2, .bg-dark h3, .bg-dark h4, .bg-dark h5, .bg-dark h6,
.bg-dark .h1, .bg-dark .h2, .bg-dark .h3, .bg-dark .h4, .bg-dark .h5, .bg-dark .h6 {
    color: #ffffff !important;
}

/* Button group styles */
.btn-group .btn {
    margin-right: 2px;
}

/* Pagination styles */
.pagination {
    margin-top: 1.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.9rem;
    }

    .btn-sm {
        padding: 0.2rem 0.4rem;
        font-size: 0.75rem;
    }
}
