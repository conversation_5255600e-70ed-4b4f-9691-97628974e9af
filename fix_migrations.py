import sqlite3
import os
import sys

# Path to the database file
db_path = 'db.sqlite3'

# Check if the database file exists
if not os.path.exists(db_path):
    print(f"Database file {db_path} not found!")
    sys.exit(1)

# Connect to the database
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

# Check if the migrations table exists
cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='django_migrations'")
if not cursor.fetchone():
    print("Migrations table not found!")
    conn.close()
    sys.exit(1)

# Check if the migrations are already applied
cursor.execute("SELECT app, name FROM django_migrations WHERE app='ads' AND name IN ('0002_aifeedback', '0011_aifeedback', '0012_merge_20250519_0952')")
applied_migrations = cursor.fetchall()
applied_migration_names = [name for _, name in applied_migrations]

print(f"Applied migrations: {applied_migration_names}")

# Mark migrations as applied if they're not already
migrations_to_apply = []
if '0002_aifeedback' not in applied_migration_names:
    migrations_to_apply.append(('ads', '0002_aifeedback'))
if '0011_aifeedback' not in applied_migration_names:
    migrations_to_apply.append(('ads', '0011_aifeedback'))
if '0012_merge_20250519_0952' not in applied_migration_names:
    migrations_to_apply.append(('ads', '0012_merge_20250519_0952'))

if migrations_to_apply:
    print(f"Marking {len(migrations_to_apply)} migrations as applied: {[name for _, name in migrations_to_apply]}")
    cursor.executemany(
        "INSERT INTO django_migrations (app, name, applied) VALUES (?, ?, datetime('now'))",
        migrations_to_apply
    )
    conn.commit()
    print("Migrations marked as applied successfully!")
else:
    print("No migrations need to be applied.")

# Close the connection
conn.close()

print("Migration fix completed successfully!")
