"""
AI Services Abstraction Layer
Provides functions to abstract AI provider details from users
"""
import logging
from typing import Dict, Any, List, Optional
from django.contrib.auth.models import User

logger = logging.getLogger(__name__)

# Constants for user-facing engine names
ENGINE_SMART = "Smart Engine"
ENGINE_STANDARD = "Standard Engine"
ENGINE_BASIC = "Basic Engine"

# Mapping from internal provider names to user-facing engine names
PROVIDER_TO_ENGINE_MAP = {
    'smart': 'Ultra Smart Engine',
    'groq': 'Lightning Engine',
    'mistral': 'Turbo Engine',
    'openai': ENGINE_SMART,
    'local': ENGINE_STANDARD,
    'offline': ENGINE_BASIC,
    'fallback': ENGINE_BASIC
}

# Mapping from internal model names to user-facing model descriptions
MODEL_TO_DESCRIPTION_MAP = {
    'smart_engine': "Ultra Smart Engine",
    'mistral-tiny': "Turbo Engine",
    'mistral-small': "Turbo Engine",
    'mistral-medium': "Turbo Engine",
    'mistral-large': "Turbo Engine Premium",
    'gpt-3.5-turbo': "Advanced AI",
    'gpt-4': "Premium AI",
    'gpt-4-turbo': "Premium AI",
    'llama3-8b-8192': "Lightning Engine",
    'llama3-70b-8192': "Lightning Engine Pro",
    'mixtral-8x7b-32768': "Lightning Engine Ultra",
    'offline': "Basic AI",
    'offline-template': "Basic AI"
}

def get_user_facing_engine_name(provider: str) -> str:
    """
    Get the user-facing engine name for a provider

    Args:
        provider: The internal provider name

    Returns:
        The user-facing engine name
    """
    return PROVIDER_TO_ENGINE_MAP.get(provider, ENGINE_BASIC)

def get_user_facing_model_description(model: str) -> str:
    """
    Get the user-facing model description for a model

    Args:
        model: The internal model name

    Returns:
        The user-facing model description
    """
    return MODEL_TO_DESCRIPTION_MAP.get(model, "Basic AI")

def sanitize_suggestion_for_user(suggestion: Dict[str, Any], user: Optional[User] = None) -> Dict[str, Any]:
    """
    Sanitize a suggestion for a user by removing or abstracting internal details

    Args:
        suggestion: The suggestion to sanitize
        user: The user to sanitize for (if None, assume regular user)

    Returns:
        The sanitized suggestion
    """
    # Make a copy of the suggestion to avoid modifying the original
    sanitized = suggestion.copy()

    # Check if the user is an admin or superuser
    is_admin = user and (user.is_staff or user.is_superuser) if user else False

    # If not an admin, remove or abstract internal details
    if not is_admin:
        # Replace model with user-facing description
        if 'model' in sanitized:
            model = sanitized['model']
            sanitized['engine'] = get_user_facing_model_description(model)
            del sanitized['model']

        # Remove internal fields
        for field in ['ai_generated', 'processing_time', 'offline', 'fallback']:
            if field in sanitized:
                del sanitized[field]
    else:
        # For admins, keep all fields but add user-facing descriptions
        if 'model' in sanitized:
            model = sanitized['model']
            sanitized['engine'] = get_user_facing_model_description(model)

    return sanitized

def sanitize_suggestions_for_user(suggestions: List[Dict[str, Any]], user: Optional[User] = None) -> List[Dict[str, Any]]:
    """
    Sanitize a list of suggestions for a user

    Args:
        suggestions: The suggestions to sanitize
        user: The user to sanitize for (if None, assume regular user)

    Returns:
        The sanitized suggestions
    """
    return [sanitize_suggestion_for_user(suggestion, user) for suggestion in suggestions]

def get_engine_status_for_user(status: Dict[str, Any], user: Optional[User] = None) -> Dict[str, Any]:
    """
    Get the engine status information appropriate for a user

    Args:
        status: The raw engine status
        user: The user to get status for (if None, assume regular user)

    Returns:
        The sanitized engine status
    """
    # Check if the user is an admin or superuser
    is_admin = user and (user.is_staff or user.is_superuser) if user else False

    if is_admin:
        # Admins see all details
        return status
    else:
        # Regular users see simplified status
        simplified = {}

        # Convert provider status to engine status
        for provider, provider_status in status.items():
            engine_name = get_user_facing_engine_name(provider)

            # Only include availability information
            simplified[engine_name] = {
                'available': provider_status.get('available', False)
            }

        return simplified
