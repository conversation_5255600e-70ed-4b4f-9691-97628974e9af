{% extends 'base.html' %}
{% load static %}

{% block title %}Admin - Pending Ads{% endblock %}

{% block extra_css %}
<!-- Google Fonts - Poppins and Montserrat -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

<!-- Include common ads CSS -->
{% include 'ads/includes/ads_common_css.html' %}

<style>
    /* Ultra-Premium Sleek Corporate Admin Pending Ads Styling */
    body {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 15%, #cbd5e1 30%, #94a3b8 50%, #64748b 70%, #475569 85%, #334155 100%);
        min-height: 100vh;
        font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        position: relative;
        overflow-x: hidden;
    }

    /* Elegant animated background with subtle corporate patterns */
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 25% 75%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),
            radial-gradient(circle at 75% 25%, rgba(255, 255, 255, 0.6) 0%, transparent 40%),
            radial-gradient(circle at 15% 85%, rgba(139, 92, 246, 0.06) 0%, transparent 45%),
            radial-gradient(circle at 85% 15%, rgba(16, 185, 129, 0.05) 0%, transparent 35%),
            radial-gradient(circle at 50% 50%, rgba(59, 130, 246, 0.04) 0%, transparent 60%);
        z-index: -1;
        animation: elegantCorporateFloat 120s ease-in-out infinite;
    }

    @keyframes elegantCorporateFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        25% { transform: translateY(-30px) rotate(1deg); }
        50% { transform: translateY(-20px) rotate(-1deg); }
        75% { transform: translateY(-35px) rotate(0.5deg); }
    }

    .admin-badge {
        background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
        color: white;
        padding: 0.6rem 1.2rem;
        border-radius: 16px;
        font-size: 0.9rem;
        font-weight: 600;
        box-shadow: 0 6px 20px rgba(59, 130, 246, 0.25);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .ad-user {
        font-weight: 600;
        color: #1e293b;
    }

    .action-buttons {
        display: flex;
        gap: 8px;
        justify-content: flex-start;
        align-items: center;
    }

    .action-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        color: white;
        transition: all 0.3s ease;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        border: none;
        cursor: pointer;
    }

    .action-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .view-btn {
        background: linear-gradient(135deg, #3949ab, #1e88e5);
    }

    .view-btn:hover {
        background: linear-gradient(135deg, #283593, #1565c0);
    }

    .approve-btn {
        background: linear-gradient(135deg, #43a047, #2e7d32);
    }

    .approve-btn:hover {
        background: linear-gradient(135deg, #388e3c, #1b5e20);
    }

    .reject-btn {
        background: linear-gradient(135deg, #e53935, #c62828);
    }

    .reject-btn:hover {
        background: linear-gradient(135deg, #d32f2f, #b71c1c);
    }
</style>
{% endblock %}

{% block content %}
<div class="enterprise-container">
    <div class="container">
        <!-- Premium Enterprise Header -->
        <div class="enterprise-header">
            <nav aria-label="breadcrumb" class="mb-3">
                <ol class="breadcrumb" style="background: rgba(255, 255, 255, 0.1); border-radius: 12px; padding: 0.75rem 1.5rem;">
                    <li class="breadcrumb-item"><a href="{% url 'index' %}" style="color: rgba(255, 255, 255, 0.8);">Home</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'ads:dashboard' %}" style="color: rgba(255, 255, 255, 0.8);">Ads Dashboard</a></li>
                    <li class="breadcrumb-item active" aria-current="page" style="color: white; font-weight: 600;">Admin - Pending Ads</li>
                </ol>
            </nav>

            <div class="text-center">
                <h1 class="enterprise-title">
                    <i class="fas fa-clock me-3 text-warning"></i>
                    Pending Advertisement Review
                </h1>
                <p class="enterprise-subtitle">
                    Review and approve advertisements awaiting administrative approval
                </p>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-12">
                <div class="enterprise-card">
                    <div class="enterprise-card-header">
                        <h3 class="enterprise-card-title">
                            <i class="fas fa-hourglass-half me-2"></i>
                            Pending Advertisements
                        </h3>
                        <span class="admin-badge">{{ total_count }} Ads</span>
                    </div>

                <div class="card-body">
                    {% if page_obj %}
                    <div class="table-responsive">
                        <table class="ads-table" id="pendingAdsTable">
                            <thead>
                                <tr>
                                    <th style="width: 30%;">Title</th>
                                    <th style="width: 15%;">User</th>
                                    <th style="width: 15%;">Type</th>
                                    <th style="width: 15%;">Submitted</th>
                                    <th style="width: 25%;">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for ad in page_obj %}
                                <tr>
                                    <td title="{{ ad.title }}">{{ ad.title }}</td>
                                    <td><span class="ad-user">{{ ad.user.username }}</span></td>
                                    <td>{{ ad.ad_type.name }}</td>
                                    <td>{{ ad.updated_at|date:"M d, Y" }}</td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="{% url 'ads:admin_view_ad' ad.slug %}" class="action-btn view-btn" data-bs-toggle="tooltip" data-bs-placement="top" title="View Ad Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if ad.status == 'pending' %}
                                            <a href="{% url 'ads:admin_approve_ad' ad.slug %}" class="action-btn approve-btn" data-bs-toggle="tooltip" data-bs-placement="top" title="Approve Ad" onclick="return confirm('Are you sure you want to approve this ad?')">
                                                <i class="fas fa-check"></i>
                                            </a>
                                            <a href="{% url 'ads:admin_reject_ad' ad.slug %}" class="action-btn reject-btn" data-bs-toggle="tooltip" data-bs-placement="top" title="Reject Ad">
                                                <i class="fas fa-times"></i>
                                            </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <h4>No pending advertisements</h4>
                        <p>There are no advertisements waiting for approval at this time.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="row mt-4">
        <div class="col-12">
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link" aria-hidden="true">&laquo;</span>
                    </li>
                    {% endif %}

                    {% for i in page_obj.paginator.page_range %}
                        {% if page_obj.number == i %}
                        <li class="page-item active">
                            <span class="page-link">{{ i }}</span>
                        </li>
                        {% else %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ i }}">{{ i }}</a>
                        </li>
                        {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link" aria-hidden="true">&raquo;</span>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
</script>
{% endblock %}