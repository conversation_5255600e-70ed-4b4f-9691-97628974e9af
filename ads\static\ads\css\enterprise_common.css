/* Common Enterprise Dashboard Styles - <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Luxurious */

/* Font Styles */
body {
    font-family: 'Poppins', 'Montserrat', sans-serif !important;
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Poppins', 'Montserrat', sans-serif !important;
    font-weight: 600;
}

p, span, div, a, button, input, select, textarea {
    font-family: 'Poppins', 'Montserrat', sans-serif !important;
}

/* Enterprise Dashboard Structure */
.enterprise-dashboard {
    background-color: #f8f9fa;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Dashboard Header */
.dashboard-header {
    background: linear-gradient(135deg, #1a237e 0%, #283593 100%);
    padding: 20px 0;
    color: white;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 10;
}

/* Glossy Header Effect */
.dashboard-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 40%;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
    pointer-events: none;
}

.dashboard-header::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 5%;
    right: 5%;
    height: 10px;
    background: rgba(0, 0, 0, 0.05);
    filter: blur(5px);
    border-radius: 50%;
    z-index: -1;
}

.dashboard-welcome {
    padding: 10px 0;
}

.welcome-title {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 5px;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.welcome-subtitle {
    font-size: 14px;
    opacity: 0.9;
    margin-bottom: 0;
}

.dashboard-actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 15px;
}

/* Dashboard Content */
.dashboard-content {
    flex: 1;
    padding: 30px 0;
}

/* Sidebar Navigation */
.dashboard-sidebar {
    padding-right: 0;
}

.sidebar-container {
    background: linear-gradient(to bottom, #f0f2f5, #e8eaed);
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    height: calc(100vh - 140px);
    position: sticky;
    top: 90px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f1f1f1;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, #1a237e 0%, #283593 100%);
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    position: relative;
    overflow: hidden;
}

/* Glossy Sidebar Header Effect */
.sidebar-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
    pointer-events: none;
}

.sidebar-title {
    font-size: 18px;
    font-weight: 700;
    color: white;
    margin: 0;
    position: relative;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Dashboard Cards */
.dashboard-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    margin-bottom: 30px;
    transition: all 0.3s ease;
    height: 100%;
    position: relative;
    overflow: hidden;
}

/* Glossy Card Effect */
.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 30%;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0));
    pointer-events: none;
    z-index: 1;
    opacity: 0.5;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
}

.dashboard-card:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    z-index: 2;
}

.card-title {
    font-size: 18px;
    font-weight: 700;
    color: #1a237e;
    margin: 0;
    position: relative;
}

/* Buttons and Actions */
.btn-primary {
    background: linear-gradient(135deg, #1a237e, #3949ab);
    border: none;
    box-shadow: 0 4px 10px rgba(26, 35, 126, 0.2);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #3949ab, #1a237e);
    box-shadow: 0 6px 15px rgba(26, 35, 126, 0.3);
    transform: translateY(-2px);
}

.btn-outline-primary {
    color: #1a237e;
    border-color: #1a237e;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background-color: #1a237e;
    color: white;
    box-shadow: 0 4px 10px rgba(26, 35, 126, 0.2);
    transform: translateY(-2px);
}

/* Responsive Styles */
@media (max-width: 992px) {
    .dashboard-sidebar {
        padding-right: 15px;
        margin-bottom: 30px;
    }

    .dashboard-main {
        padding-left: 15px;
    }

    .sidebar-container {
        height: auto;
        position: relative;
        top: 0;
    }
}

@media (max-width: 768px) {
    .dashboard-welcome {
        text-align: center;
        margin-bottom: 15px;
    }

    .dashboard-actions {
        justify-content: center;
    }
}
