# Docker Setup for QR Code Generator

This document explains how to run the QR Code Generator application using Docker.

## Prerequisites

- Docker
- Docker Compose

## Services

The application consists of two main services:

1. **web** - Django web application
2. **ai_engine** - FastAPI-based AI engine for ad generation

## Getting Started

### Building and Running the Services

1. Build and start the containers:

```bash
docker-compose up -d
```

2. Check if the containers are running:

```bash
docker-compose ps
```

3. Check the logs:

```bash
docker-compose logs -f
```

### Accessing the Services

Once the containers are running, you can access the services at:

- Django Web App: http://localhost:8000/
- AI Engine API: http://localhost:8001/

### Stopping the Services

```bash
docker-compose down
```

## Configuration

### Environment Variables

The Docker Compose setup includes several environment variables:

#### Web Service

- `DEBUG`: Set to `true` for development, `false` for production
- `AI_PROVIDER`: Set to `local` to use the local AI Engine
- `AI_ENGINE_URL`: URL of the AI Engine service
- `AI_ENGINE_API_TOKEN`: API token for authenticating with the AI Engine

#### AI Engine Service

- `AI_ENGINE_ENABLE_AUTH`: Set to `true` to enable authentication
- `AI_ENGINE_API_TOKENS`: Comma-separated list of allowed API tokens
- `ENVIRONMENT`: Set to `development` to enable hot reload, `production` otherwise
- `RELOAD`: Set to `true` to force hot reload regardless of environment

## Volumes

The Docker setup includes several volumes:

- `ai_models`: Persistent volume for AI models
- `static_volume`: Volume for Django static files
- `media_volume`: Volume for Django media files

## Networks

The services are connected through a custom network called `app_network`, which allows the Django application to communicate with the AI Engine using the service name as the hostname:

```python
# Example of calling the AI Engine from Django
response = requests.post(
    "http://ai_engine:8001/generate_ad",
    json=payload,
    headers=headers
)
```

## Development Workflow

For development, you can mount your local code directories as volumes to enable hot reloading:

```yaml
volumes:
  - ./ai_engine:/app  # For AI Engine
  - .:/app  # For Django
```

This allows you to make changes to the code and see them reflected immediately without rebuilding the containers.

## Troubleshooting

If you encounter any issues, check the logs:

```bash
docker-compose logs -f web  # For Django logs
docker-compose logs -f ai_engine  # For AI Engine logs
```

If the AI Engine fails to start, make sure the models are downloaded correctly. You can manually download them:

```bash
docker-compose exec ai_engine python download_models.py --models mistral-7b-instruct
```
