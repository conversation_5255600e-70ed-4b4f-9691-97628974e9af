{% extends "base.html" %}
{% load static %}

{% block title %}API Documentation - Enterprise QR{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'api/css/api-docs.css' %}">
{% endblock %}

{% block content %}
<div class="api-docs-container">
    <div class="api-docs-sidebar">
        <div class="api-docs-sidebar-header">
            <h3>API Documentation</h3>
        </div>
        <div class="api-docs-sidebar-content">
            <ul class="api-docs-nav">
                <li><a href="#introduction" class="active">Introduction</a></li>
                <li><a href="#authentication">Authentication</a></li>
                <li><a href="#rate-limiting">Rate Limiting</a></li>
                <li><a href="#error-handling">Error Handling</a></li>
                <li class="api-docs-nav-header">Endpoints</li>
                <li><a href="#auth-endpoints">Authentication</a></li>
                <li><a href="#qrcode-endpoints">QR Codes</a></li>
                <li class="api-docs-nav-header">Resources</li>
                <li><a href="#client-libraries">Client Libraries</a></li>
                <li><a href="#code-examples">Code Examples</a></li>
                <li><a href="#api-keys">API Keys</a></li>
            </ul>
        </div>
    </div>
    <div class="api-docs-content">
        <div class="api-docs-section" id="introduction">
            <h1>Enterprise QR API Documentation</h1>
            <p class="api-docs-version">API Version: 1.0.0</p>
            <p>The Enterprise QR API provides programmatic access to the QR code generation and management capabilities of the Enterprise QR platform. This RESTful API allows you to integrate QR code functionality into your own applications, services, and workflows.</p>
            
            <h2>Base URL</h2>
            <div class="api-endpoint">
                <code>https://api.enterpriseqr.com/api</code>
            </div>
            
            <h2>API Features</h2>
            <ul class="api-features">
                <li><i class="fas fa-check-circle"></i> Generate QR codes programmatically</li>
                <li><i class="fas fa-check-circle"></i> Customize QR code appearance and properties</li>
                <li><i class="fas fa-check-circle"></i> Store and manage QR codes</li>
                <li><i class="fas fa-check-circle"></i> Track QR code usage and analytics</li>
                <li><i class="fas fa-check-circle"></i> Secure authentication and authorization</li>
            </ul>
        </div>
        
        <div class="api-docs-section" id="authentication">
            <h2>Authentication</h2>
            <p>The Enterprise QR API uses API keys for authentication. You can obtain an API key by registering for an account or logging in to your existing account.</p>
            
            <h3>API Key Authentication</h3>
            <p>Include your API key in the <code>X-API-Key</code> header with all API requests:</p>
            <div class="code-block">
                <pre><code>X-API-Key: your_api_key_here</code></pre>
            </div>
            
            <h3>JWT Authentication</h3>
            <p>Alternatively, you can use JWT token authentication by including the token in the <code>Authorization</code> header:</p>
            <div class="code-block">
                <pre><code>Authorization: Bearer your_jwt_token_here</code></pre>
            </div>
        </div>
        
        <!-- More sections would go here -->
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'api/js/api-docs.js' %}"></script>
{% endblock %}
