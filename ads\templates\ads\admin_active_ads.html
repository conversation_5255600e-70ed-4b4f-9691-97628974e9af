{% extends 'base.html' %}
{% load static %}

{% block title %}Admin - Active Ads{% endblock %}

{% block extra_css %}
<!-- Include common ads CSS -->
{% include 'ads/includes/ads_common_css.html' %}
<style>
    .admin-badge {
        background-color: #1a237e;
        color: white;
        padding: 3px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 600;
        margin-left: 10px;
    }

    .ad-user {
        font-weight: 600;
        color: #1a237e;
    }

    .action-buttons {
        display: flex;
        gap: 8px;
        justify-content: flex-start;
        align-items: center;
    }

    .action-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        color: white;
        transition: all 0.3s ease;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        border: none;
        cursor: pointer;
    }

    .action-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .view-btn {
        background: linear-gradient(135deg, #3949ab, #1e88e5);
    }

    .view-btn:hover {
        background: linear-gradient(135deg, #283593, #1565c0);
    }

    .status-btn {
        background: linear-gradient(135deg, #5e35b1, #7b1fa2);
    }

    .status-btn:hover {
        background: linear-gradient(135deg, #4527a0, #6a1b9a);
    }

    .analytics-btn {
        background: linear-gradient(135deg, #fb8c00, #ef6c00);
    }

    .analytics-btn:hover {
        background: linear-gradient(135deg, #f57c00, #e65100);
    }

    .metrics-badge {
        display: inline-flex;
        align-items: center;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 600;
        margin-right: 8px;
    }

    .metrics-badge i {
        margin-right: 4px;
    }

    .impressions-badge {
        background-color: rgba(33, 150, 243, 0.1);
        color: #2196f3;
    }

    .clicks-badge {
        background-color: rgba(76, 175, 80, 0.1);
        color: #4caf50;
    }

    .ctr-badge {
        background-color: rgba(156, 39, 176, 0.1);
        color: #9c27b0;
    }

    .ad-location {
        font-weight: 500;
        color: #3949ab;
    }

    .ad-dates {
        font-size: 13px;
        color: #666;
    }

    .filter-section {
        background-color: rgba(57, 73, 171, 0.03);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .filter-section .form-label {
        font-weight: 600;
        color: #1a237e;
    }
</style>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="ads-page-header">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <nav aria-label="breadcrumb" class="ads-breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'index' %}">Home</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'ads:dashboard' %}">Dashboard</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Admin - Active Ads</li>
                    </ol>
                </nav>

                <h1 class="display-6 text-center mb-1 ads-page-title">Admin - Active Ads</h1>
                <p class="lead text-center mb-2 ads-page-subtitle">Monitor and manage active advertisements</p>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="container mt-4">
    <div class="row mb-4">
        <div class="col-12">
            <div class="ads-card">
                <div class="ads-card-header">
                    <h3 class="ads-card-title">Active Advertisements</h3>
                    <span class="badge bg-success">{{ total_count }} Ads</span>
                </div>

                <div class="card-body">
                    <div class="filter-section mb-4">
                        <form method="get" class="row g-3">
                            <div class="col-md-4">
                                <label for="ad_type" class="form-label">Filter by Ad Type</label>
                                <select class="form-select" id="ad_type" name="ad_type">
                                    <option value="">All Types</option>
                                    <!-- Add options dynamically -->
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="ad_location" class="form-label">Filter by Location</label>
                                <select class="form-select" id="ad_location" name="ad_location">
                                    <option value="">All Locations</option>
                                    <!-- Add options dynamically -->
                                </select>
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <button type="submit" class="ads-btn ads-btn-primary">Apply Filters</button>
                                <a href="{% url 'ads:admin_active_ads' %}" class="ads-btn ads-btn-outline ms-2">Reset</a>
                            </div>
                        </form>
                    </div>

                    {% if page_obj %}
                    <div class="table-responsive">
                        <table class="ads-table" id="activeAdsTable">
                            <thead>
                                <tr>
                                    <th style="width: 25%;">Title</th>
                                    <th style="width: 15%;">User</th>
                                    <th style="width: 15%;">Location</th>
                                    <th style="width: 15%;">Dates</th>
                                    <th style="width: 15%;">Metrics</th>
                                    <th style="width: 15%;">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for ad in page_obj %}
                                <tr>
                                    <td title="{{ ad.title }}">{{ ad.title }}</td>
                                    <td><span class="ad-user">{{ ad.user.username }}</span></td>
                                    <td>
                                        {% if ad.ad_location %}
                                        <span class="ad-location">{{ ad.ad_location.name }}</span>
                                        {% else %}
                                        <span class="text-muted">Not specified</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="ad-dates">
                                            <div>Start: {{ ad.start_date|date:"M d, Y" }}</div>
                                            <div>End: {{ ad.end_date|date:"M d, Y" }}</div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="metrics-badge impressions-badge" title="Impressions">
                                            <i class="fas fa-eye"></i> {{ ad.impressions }}
                                        </span>
                                        <span class="metrics-badge clicks-badge" title="Clicks">
                                            <i class="fas fa-mouse-pointer"></i> {{ ad.clicks }}
                                        </span>
                                        <span class="metrics-badge ctr-badge" title="Click-Through Rate">
                                            <i class="fas fa-percentage"></i>
                                            {% if ad.impressions > 0 %}
                                                {{ ad.clicks|floatformat:0|default:0|stringformat:"s"|add:".0"|floatformat:2 }}%
                                            {% else %}
                                                0.00%
                                            {% endif %}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="{% url 'ads:admin_view_ad' ad.slug %}" class="action-btn view-btn" data-bs-toggle="tooltip" data-bs-placement="top" title="View Ad Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'ads:admin_update_ad_status' ad.slug %}" class="action-btn status-btn" data-bs-toggle="tooltip" data-bs-placement="top" title="Update Status">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'ads:ad_analytics' ad.slug %}" class="action-btn analytics-btn" data-bs-toggle="tooltip" data-bs-placement="top" title="View Analytics">
                                                <i class="fas fa-chart-bar"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <h4>No active advertisements</h4>
                        <p>There are no active advertisements at this time.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="row mt-4">
        <div class="col-12">
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link" aria-hidden="true">&laquo;</span>
                    </li>
                    {% endif %}

                    {% for i in page_obj.paginator.page_range %}
                        {% if page_obj.number == i %}
                        <li class="page-item active">
                            <span class="page-link">{{ i }}</span>
                        </li>
                        {% else %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ i }}">{{ i }}</a>
                        </li>
                        {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link" aria-hidden="true">&raquo;</span>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
</script>
{% endblock %}