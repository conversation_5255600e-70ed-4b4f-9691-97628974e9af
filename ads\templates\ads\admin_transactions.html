{% extends 'base.html' %}
{% load static %}

{% block title %}Transaction Management - Admin{% endblock %}

{% block extra_css %}
<!-- Google Fonts - Poppins and Montserrat -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

<!-- Include common ads CSS -->
{% include 'ads/includes/ads_common_css.html' %}

<style>
    /* Ultra-Premium Enterprise Admin Transactions Styling */
    body {
        background: linear-gradient(135deg, #000000 0%, #0a0a0a 10%, #1a1a2e 25%, #16213e 40%, #0f3460 60%, #533483 80%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        position: relative;
        overflow-x: hidden;
    }

    /* Premium animated background with enterprise patterns */
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 25% 75%, rgba(102, 126, 234, 0.6) 0%, transparent 45%),
            radial-gradient(circle at 75% 25%, rgba(255, 255, 255, 0.25) 0%, transparent 40%),
            radial-gradient(circle at 15% 85%, rgba(118, 75, 162, 0.5) 0%, transparent 50%),
            radial-gradient(circle at 85% 15%, rgba(83, 52, 131, 0.4) 0%, transparent 35%),
            radial-gradient(circle at 50% 50%, rgba(102, 126, 234, 0.3) 0%, transparent 45%);
        z-index: -1;
        animation: enterpriseAdminFloat 105s ease-in-out infinite;
    }

    @keyframes enterpriseAdminFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        25% { transform: translateY(-90px) rotate(5deg); }
        50% { transform: translateY(-70px) rotate(-5deg); }
        75% { transform: translateY(-100px) rotate(2.5deg); }
    }

    .transaction-status {
        display: inline-block;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
    }

    .transaction-status-paid {
        background-color: rgba(40, 167, 69, 0.1);
        color: #28a745;
    }

    .transaction-status-pending {
        background-color: rgba(255, 193, 7, 0.1);
        color: #ffc107;
    }

    .transaction-status-processing {
        background-color: rgba(13, 110, 253, 0.1);
        color: #0d6efd;
    }

    .transaction-status-failed {
        background-color: rgba(220, 53, 69, 0.1);
        color: #dc3545;
    }

    .transaction-status-refunded {
        background-color: rgba(108, 117, 125, 0.1);
        color: #6c757d;
    }

    .payment-method {
        display: inline-flex;
        align-items: center;
        gap: 5px;
    }

    .payment-method-icon {
        font-size: 16px;
    }

    .transaction-amount {
        font-weight: 700;
        color: #1a237e;
    }

    .transaction-date {
        color: #6c757d;
        font-size: 14px;
    }

    .transaction-user {
        font-weight: 600;
    }

    .transaction-ad-title {
        color: #3949ab;
        font-weight: 600;
    }

    .transaction-ad-title:hover {
        text-decoration: underline;
    }

    .filter-card {
        margin-bottom: 20px;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    .filter-card-header {
        padding: 15px 20px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        background-color: rgba(0, 0, 0, 0.02);
        border-radius: 10px 10px 0 0;
    }

    .filter-card-title {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #495057;
    }

    .filter-card-body {
        padding: 20px;
    }

    .filter-form .form-group {
        margin-bottom: 15px;
    }

    .filter-form label {
        font-weight: 500;
        color: #495057;
        margin-bottom: 5px;
    }

    .filter-form .btn-filter {
        background-color: #3949ab;
        border-color: #3949ab;
    }

    .filter-form .btn-reset {
        background-color: #6c757d;
        border-color: #6c757d;
    }

    .summary-card {
        margin-bottom: 20px;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    .summary-card-body {
        padding: 20px;
        display: flex;
        justify-content: space-between;
    }

    .summary-item {
        text-align: center;
    }

    .summary-label {
        font-size: 14px;
        color: #6c757d;
        margin-bottom: 5px;
    }

    .summary-value {
        font-size: 24px;
        font-weight: 700;
        color: #1a237e;
    }

    .empty-state {
        text-align: center;
        padding: 50px 20px;
    }

    .empty-state-icon {
        font-size: 48px;
        color: #adb5bd;
        margin-bottom: 15px;
    }

    .empty-state-text {
        font-size: 18px;
        color: #6c757d;
        margin-bottom: 10px;
    }

    .empty-state-subtext {
        font-size: 14px;
        color: #adb5bd;
    }
</style>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="ads-page-header">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <nav aria-label="breadcrumb" class="ads-breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'index' %}">Home</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'ads:dashboard' %}">Ads Dashboard</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Transaction Management</li>
                    </ol>
                </nav>

                <h1 class="display-6 text-center mb-1 ads-page-title">Transaction Management</h1>
                <p class="lead text-center mb-2 ads-page-subtitle">View and filter all payment transactions</p>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="container mt-4">
    <div class="row">
        <!-- Filters -->
        <div class="col-lg-3">
            <div class="filter-card animate__animated animate__fadeIn">
                <div class="filter-card-header">
                    <h3 class="filter-card-title">Filters</h3>
                </div>
                <div class="filter-card-body">
                    <form class="filter-form" method="get">
                        <div class="form-group">
                            <label for="user">User</label>
                            <input type="text" class="form-control" id="user" name="user" value="{{ filters.user }}" placeholder="Username">
                        </div>
                        <div class="form-group">
                            <label for="ad">Ad Title</label>
                            <input type="text" class="form-control" id="ad" name="ad" value="{{ filters.ad }}" placeholder="Ad title">
                        </div>
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">All Statuses</option>
                                {% for status_code, status_name in status_choices %}
                                    <option value="{{ status_code }}" {% if filters.status == status_code %}selected{% endif %}>{{ status_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="payment_gateway">Payment Method</label>
                            <select class="form-select" id="payment_gateway" name="payment_gateway">
                                <option value="">All Methods</option>
                                {% for gateway_code, gateway_name in payment_gateway_choices %}
                                    <option value="{{ gateway_code }}" {% if filters.payment_gateway == gateway_code %}selected{% endif %}>{{ gateway_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="date_from">Date From</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" value="{{ filters.date_from|date:'Y-m-d' }}">
                        </div>
                        <div class="form-group">
                            <label for="date_to">Date To</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" value="{{ filters.date_to|date:'Y-m-d' }}">
                        </div>
                        <div class="form-group">
                            <label for="amount_min">Min Amount (KSH)</label>
                            <input type="number" class="form-control" id="amount_min" name="amount_min" value="{{ filters.amount_min }}" placeholder="0">
                        </div>
                        <div class="form-group">
                            <label for="amount_max">Max Amount (KSH)</label>
                            <input type="number" class="form-control" id="amount_max" name="amount_max" value="{{ filters.amount_max }}" placeholder="10000">
                        </div>
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-filter">Apply Filters</button>
                            <a href="{% url 'ads:admin_transactions' %}" class="btn btn-secondary btn-reset">Reset Filters</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Transactions List -->
        <div class="col-lg-9">
            <!-- Summary -->
            <div class="summary-card animate__animated animate__fadeIn">
                <div class="summary-card-body">
                    <div class="summary-item">
                        <div class="summary-label">Total Transactions</div>
                        <div class="summary-value">{{ total_transactions }}</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-label">Total Amount</div>
                        <div class="summary-value">{{ total_amount|floatformat:2 }} KSH</div>
                    </div>
                </div>
            </div>

            <!-- Transactions Table -->
            <div class="ads-card animate__animated animate__fadeIn">
                <div class="ads-card-header d-flex justify-content-between align-items-center">
                    <h3 class="ads-card-title">Transactions</h3>
                    <a href="{% url 'ads:admin_pending_payments' %}" class="btn btn-sm btn-primary">
                        <i class="fas fa-check-circle"></i> Pending Approvals
                    </a>
                </div>

                <div class="card-body">
                    {% if page_obj %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>User</th>
                                        <th>Ad</th>
                                        <th>Amount</th>
                                        <th>Method</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for transaction in page_obj %}
                                        <tr>
                                            <td>{{ transaction.id }}</td>
                                            <td class="transaction-user">{{ transaction.user.username }}</td>
                                            <td>
                                                <a href="{% url 'ads:admin_view_ad' transaction.ad.slug %}" class="transaction-ad-title">
                                                    {{ transaction.ad.title }}
                                                </a>
                                            </td>
                                            <td class="transaction-amount">{{ transaction.amount }} KSH</td>
                                            <td>
                                                <div class="payment-method">
                                                    {% if transaction.payment_gateway == 'mpesa' %}
                                                        <i class="fas fa-mobile-alt payment-method-icon"></i> M-PESA
                                                    {% elif transaction.payment_gateway == 'card' %}
                                                        <i class="far fa-credit-card payment-method-icon"></i> Card
                                                    {% elif transaction.payment_gateway == 'bank' %}
                                                        <i class="fas fa-university payment-method-icon"></i> Bank
                                                    {% elif transaction.payment_gateway == 'paypal' %}
                                                        <i class="fab fa-paypal payment-method-icon"></i> PayPal
                                                    {% endif %}
                                                </div>
                                            </td>
                                            <td>
                                                <span class="transaction-status transaction-status-{{ transaction.status }}">
                                                    {{ transaction.get_status_display }}
                                                </span>
                                            </td>
                                            <td class="transaction-date">{{ transaction.timestamp|date:"M d, Y H:i" }}</td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if page_obj.has_other_pages %}
                            <nav aria-label="Page navigation" class="mt-4">
                                <ul class="pagination justify-content-center">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page=1{% for key, value in filters.items %}{% if value %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="First">
                                                <span aria-hidden="true">&laquo;&laquo;</span>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in filters.items %}{% if value %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Previous">
                                                <span aria-hidden="true">&laquo;</span>
                                            </a>
                                        </li>
                                    {% endif %}

                                    {% for num in page_obj.paginator.page_range %}
                                        {% if page_obj.number == num %}
                                            <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                            <li class="page-item"><a class="page-link" href="?page={{ num }}{% for key, value in filters.items %}{% if value %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ num }}</a></li>
                                        {% endif %}
                                    {% endfor %}

                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in filters.items %}{% if value %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Next">
                                                <span aria-hidden="true">&raquo;</span>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% for key, value in filters.items %}{% if value %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Last">
                                                <span aria-hidden="true">&raquo;&raquo;</span>
                                            </a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="empty-state">
                            <div class="empty-state-icon">
                                <i class="fas fa-money-check-alt"></i>
                            </div>
                            <div class="empty-state-text">No transactions found</div>
                            <div class="empty-state-subtext">Try adjusting your filters or create some transactions</div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
