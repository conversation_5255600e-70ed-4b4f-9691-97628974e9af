/**
 * performance-monitor.js
 * 
 * Monitors and optimizes performance for mobile devices.
 * Implements performance tracking and optimization techniques.
 */

(function() {
    // Configuration
    const config = {
        // Whether to log performance metrics to console
        enableLogging: false,
        
        // Whether to enable performance optimizations
        enableOptimizations: true,
        
        // Threshold for slow interactions (in ms)
        slowInteractionThreshold: 100,
        
        // Threshold for slow page loads (in ms)
        slowPageLoadThreshold: 3000,
        
        // Threshold for slow animations (in ms)
        slowAnimationThreshold: 16, // ~60fps
        
        // Maximum number of elements to optimize at once
        maxElementsToOptimize: 100
    };
    
    // Performance metrics
    let metrics = {
        pageLoadTime: 0,
        domContentLoadedTime: 0,
        firstPaintTime: 0,
        firstContentfulPaintTime: 0,
        interactionTimes: [],
        animationFrameTimes: [],
        resourceLoadTimes: {},
        memoryUsage: {}
    };
    
    // Initialize performance monitoring
    function init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', onDOMContentLoaded);
        } else {
            onDOMContentLoaded();
        }
        
        // Track page load time
        window.addEventListener('load', onPageLoad);
        
        // Track performance metrics
        trackPerformanceMetrics();
        
        // Apply performance optimizations if enabled
        if (config.enableOptimizations) {
            applyPerformanceOptimizations();
        }
    }
    
    // Handle DOMContentLoaded event
    function onDOMContentLoaded() {
        metrics.domContentLoadedTime = performance.now();
        
        if (config.enableLogging) {
            console.log('DOMContentLoaded time:', metrics.domContentLoadedTime, 'ms');
        }
        
        // Track interaction times
        trackInteractionTimes();
        
        // Track animation frame times
        trackAnimationFrameTimes();
    }
    
    // Handle page load event
    function onPageLoad() {
        metrics.pageLoadTime = performance.now();
        
        if (config.enableLogging) {
            console.log('Page load time:', metrics.pageLoadTime, 'ms');
        }
        
        // Track resource load times
        trackResourceLoadTimes();
        
        // Track memory usage
        trackMemoryUsage();
        
        // Apply post-load optimizations
        if (config.enableOptimizations) {
            applyPostLoadOptimizations();
        }
    }
    
    // Track performance metrics
    function trackPerformanceMetrics() {
        // Track first paint time
        const paintEntries = performance.getEntriesByType('paint');
        for (const entry of paintEntries) {
            if (entry.name === 'first-paint') {
                metrics.firstPaintTime = entry.startTime;
            } else if (entry.name === 'first-contentful-paint') {
                metrics.firstContentfulPaintTime = entry.startTime;
            }
        }
        
        // If Paint Timing API is not available, use a fallback
        if (!metrics.firstPaintTime) {
            const navStart = performance.timing.navigationStart;
            const fpTime = performance.timing.domContentLoadedEventEnd - navStart;
            metrics.firstPaintTime = fpTime;
        }
        
        if (config.enableLogging) {
            console.log('First paint time:', metrics.firstPaintTime, 'ms');
            console.log('First contentful paint time:', metrics.firstContentfulPaintTime, 'ms');
        }
    }
    
    // Track interaction times
    function trackInteractionTimes() {
        const interactionEvents = ['click', 'touchstart', 'touchend', 'keydown', 'input'];
        
        interactionEvents.forEach(eventType => {
            document.addEventListener(eventType, event => {
                const startTime = performance.now();
                
                // Use requestAnimationFrame to measure when the browser responds to the interaction
                requestAnimationFrame(() => {
                    const endTime = performance.now();
                    const duration = endTime - startTime;
                    
                    metrics.interactionTimes.push({
                        type: eventType,
                        target: event.target.tagName,
                        duration: duration
                    });
                    
                    if (duration > config.slowInteractionThreshold && config.enableLogging) {
                        console.warn('Slow interaction:', eventType, 'on', event.target.tagName, duration, 'ms');
                    }
                });
            }, { passive: true });
        });
    }
    
    // Track animation frame times
    function trackAnimationFrameTimes() {
        let lastFrameTime = performance.now();
        
        function frameCallback(timestamp) {
            const frameDuration = timestamp - lastFrameTime;
            lastFrameTime = timestamp;
            
            metrics.animationFrameTimes.push(frameDuration);
            
            if (frameDuration > config.slowAnimationThreshold && config.enableLogging) {
                console.warn('Slow animation frame:', frameDuration, 'ms');
            }
            
            requestAnimationFrame(frameCallback);
        }
        
        requestAnimationFrame(frameCallback);
    }
    
    // Track resource load times
    function trackResourceLoadTimes() {
        const resources = performance.getEntriesByType('resource');
        
        for (const resource of resources) {
            metrics.resourceLoadTimes[resource.name] = {
                duration: resource.duration,
                size: resource.transferSize,
                type: resource.initiatorType
            };
            
            if (resource.duration > 1000 && config.enableLogging) {
                console.warn('Slow resource load:', resource.name, resource.duration, 'ms');
            }
        }
    }
    
    // Track memory usage
    function trackMemoryUsage() {
        if (performance.memory) {
            metrics.memoryUsage = {
                totalJSHeapSize: performance.memory.totalJSHeapSize,
                usedJSHeapSize: performance.memory.usedJSHeapSize,
                jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
            };
            
            if (config.enableLogging) {
                console.log('Memory usage:', metrics.memoryUsage);
            }
        }
    }
    
    // Apply performance optimizations
    function applyPerformanceOptimizations() {
        // Optimize images
        optimizeImages();
        
        // Optimize animations
        optimizeAnimations();
        
        // Optimize event listeners
        optimizeEventListeners();
        
        // Optimize DOM operations
        optimizeDOMOperations();
    }
    
    // Apply post-load optimizations
    function applyPostLoadOptimizations() {
        // Lazy load non-critical resources
        lazyLoadResources();
        
        // Remove unused elements
        removeUnusedElements();
        
        // Optimize for idle time
        optimizeForIdleTime();
    }
    
    // Optimize images
    function optimizeImages() {
        const images = document.querySelectorAll('img:not([loading="lazy"])');
        
        for (let i = 0; i < Math.min(images.length, config.maxElementsToOptimize); i++) {
            const img = images[i];
            
            // Skip images that are already optimized
            if (img.hasAttribute('data-optimized')) continue;
            
            // Add lazy loading attribute
            img.setAttribute('loading', 'lazy');
            
            // Mark as optimized
            img.setAttribute('data-optimized', 'true');
        }
    }
    
    // Optimize animations
    function optimizeAnimations() {
        // Use transform and opacity for animations
        const animatedElements = document.querySelectorAll('.animated, [data-animated]');
        
        for (let i = 0; i < Math.min(animatedElements.length, config.maxElementsToOptimize); i++) {
            const el = animatedElements[i];
            
            // Skip elements that are already optimized
            if (el.hasAttribute('data-optimized')) continue;
            
            // Add will-change property
            el.style.willChange = 'transform, opacity';
            
            // Mark as optimized
            el.setAttribute('data-optimized', 'true');
        }
    }
    
    // Optimize event listeners
    function optimizeEventListeners() {
        // Use passive event listeners for touch events
        const touchEvents = ['touchstart', 'touchmove', 'touchend', 'touchcancel'];
        
        touchEvents.forEach(eventType => {
            document.addEventListener(eventType, () => {}, { passive: true });
        });
    }
    
    // Optimize DOM operations
    function optimizeDOMOperations() {
        // Use document fragments for batch DOM operations
        window.optimizedAppendChildren = function(parent, children) {
            const fragment = document.createDocumentFragment();
            
            for (let i = 0; i < children.length; i++) {
                fragment.appendChild(children[i]);
            }
            
            parent.appendChild(fragment);
        };
    }
    
    // Lazy load resources
    function lazyLoadResources() {
        // Lazy load images
        const lazyImages = document.querySelectorAll('img[data-src]');
        
        for (let i = 0; i < Math.min(lazyImages.length, config.maxElementsToOptimize); i++) {
            const img = lazyImages[i];
            
            // Skip images that are already loaded
            if (img.hasAttribute('data-loaded')) continue;
            
            // Create an observer
            const observer = new IntersectionObserver(entries => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.getAttribute('data-src');
                        img.setAttribute('data-loaded', 'true');
                        observer.unobserve(img);
                    }
                });
            });
            
            observer.observe(img);
        }
    }
    
    // Remove unused elements
    function removeUnusedElements() {
        // Remove hidden elements that are not needed
        const hiddenElements = document.querySelectorAll('[data-remove-after-load="true"]');
        
        for (let i = 0; i < hiddenElements.length; i++) {
            hiddenElements[i].remove();
        }
    }
    
    // Optimize for idle time
    function optimizeForIdleTime() {
        // Use requestIdleCallback for non-critical operations
        if ('requestIdleCallback' in window) {
            requestIdleCallback(() => {
                // Perform non-critical operations here
                if (config.enableLogging) {
                    console.log('Performing idle time optimizations');
                }
                
                // Clean up memory
                cleanupMemory();
            });
        }
    }
    
    // Clean up memory
    function cleanupMemory() {
        // Clear unnecessary caches
        if ('caches' in window) {
            caches.keys().then(cacheNames => {
                cacheNames.forEach(cacheName => {
                    if (cacheName.startsWith('temp-')) {
                        caches.delete(cacheName);
                    }
                });
            });
        }
    }
    
    // Initialize
    init();
    
    // Expose public API
    window.PerformanceMonitor = {
        getMetrics: function() {
            return metrics;
        },
        enableLogging: function(enable) {
            config.enableLogging = enable;
        }
    };
})();
