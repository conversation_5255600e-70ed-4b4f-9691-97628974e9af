<!-- Upgrade Plan Modal -->
<div class="modal fade" id="upgradeModal" tabindex="-1" aria-labelledby="upgradeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content upgrade-modal-content">
            <div class="modal-header upgrade-modal-header">
                <div class="upgrade-header-content">
                    <div class="upgrade-icon">
                        <i class="fas fa-rocket"></i>
                    </div>
                    <div class="upgrade-title-section">
                        <h4 class="modal-title upgrade-title" id="upgradeModalLabel">
                            Upgrade Your Plan
                        </h4>
                        <p class="upgrade-subtitle">
                            You've reached your current plan's limit. Upgrade to continue enjoying our premium features.
                        </p>
                    </div>
                </div>
                <button type="button" class="btn-close upgrade-close" data-bs-dismiss="modal" aria-label="Close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="modal-body upgrade-modal-body">
                <!-- Current Plan Section -->
                <div class="current-plan-section">
                    <div class="current-plan-header">
                        <h5><i class="fas fa-user-circle"></i> Your Current Plan</h5>
                    </div>
                    <div class="current-plan-card">
                        <div class="plan-info">
                            <div class="plan-name" id="currentPlanName">Free Plan</div>
                            <div class="plan-price" id="currentPlanPrice">$0/month</div>
                        </div>
                        <div class="plan-usage">
                            <div class="usage-item">
                                <span class="usage-label">QR Codes:</span>
                                <span class="usage-value" id="currentQRUsage">5/10</span>
                                <div class="usage-bar">
                                    <div class="usage-progress" id="currentQRProgress" style="width: 50%"></div>
                                </div>
                            </div>
                            <div class="usage-item">
                                <span class="usage-label">Monthly Scans:</span>
                                <span class="usage-value" id="currentScanUsage">450/1,000</span>
                                <div class="usage-bar">
                                    <div class="usage-progress" id="currentScanProgress" style="width: 45%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Upgrade Options Section -->
                <div class="upgrade-options-section">
                    <div class="upgrade-options-header">
                        <h5><i class="fas fa-star"></i> Recommended Upgrades</h5>
                        <p>Choose the plan that best fits your needs</p>
                    </div>
                    
                    <div class="upgrade-plans-grid" id="upgradePlansGrid">
                        <!-- Plans will be loaded dynamically -->
                        <div class="upgrade-plan-card" data-plan-id="2">
                            <div class="plan-badge">Most Popular</div>
                            <div class="plan-header">
                                <h6 class="plan-name">Professional</h6>
                                <div class="plan-price">
                                    <span class="price">$19</span>
                                    <span class="period">/month</span>
                                </div>
                            </div>
                            <div class="plan-features">
                                <ul>
                                    <li><i class="fas fa-check"></i> 100 QR Codes</li>
                                    <li><i class="fas fa-check"></i> 10,000 Scans/Month</li>
                                    <li><i class="fas fa-check"></i> AI Landing Pages</li>
                                    <li><i class="fas fa-check"></i> Advanced Analytics</li>
                                    <li><i class="fas fa-check"></i> Custom Branding</li>
                                </ul>
                            </div>
                            <button class="btn upgrade-btn" onclick="upgradeToplan(2)">
                                <i class="fas fa-arrow-up"></i> Upgrade Now
                            </button>
                        </div>

                        <div class="upgrade-plan-card" data-plan-id="3">
                            <div class="plan-badge enterprise">Enterprise</div>
                            <div class="plan-header">
                                <h6 class="plan-name">Enterprise</h6>
                                <div class="plan-price">
                                    <span class="price">$49</span>
                                    <span class="period">/month</span>
                                </div>
                            </div>
                            <div class="plan-features">
                                <ul>
                                    <li><i class="fas fa-check"></i> Unlimited QR Codes</li>
                                    <li><i class="fas fa-check"></i> Unlimited Scans</li>
                                    <li><i class="fas fa-check"></i> All AI Features</li>
                                    <li><i class="fas fa-check"></i> API Access</li>
                                    <li><i class="fas fa-check"></i> Priority Support</li>
                                </ul>
                            </div>
                            <button class="btn upgrade-btn enterprise" onclick="upgradeToplan(3)">
                                <i class="fas fa-crown"></i> Upgrade Now
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Benefits Section -->
                <div class="upgrade-benefits-section">
                    <div class="benefits-header">
                        <h6><i class="fas fa-gift"></i> What You Get With Upgrade</h6>
                    </div>
                    <div class="benefits-grid">
                        <div class="benefit-item">
                            <i class="fas fa-infinity"></i>
                            <span>Higher Limits</span>
                        </div>
                        <div class="benefit-item">
                            <i class="fas fa-chart-line"></i>
                            <span>Advanced Analytics</span>
                        </div>
                        <div class="benefit-item">
                            <i class="fas fa-palette"></i>
                            <span>Custom Branding</span>
                        </div>
                        <div class="benefit-item">
                            <i class="fas fa-headset"></i>
                            <span>Priority Support</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="modal-footer upgrade-modal-footer">
                <div class="footer-content">
                    <div class="security-badges">
                        <div class="security-item">
                            <i class="fas fa-shield-alt"></i>
                            <span>Secure Payment</span>
                        </div>
                        <div class="security-item">
                            <i class="fas fa-undo"></i>
                            <span>30-Day Money Back</span>
                        </div>
                        <div class="security-item">
                            <i class="fas fa-times-circle"></i>
                            <span>Cancel Anytime</span>
                        </div>
                    </div>
                    <div class="footer-actions">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            Maybe Later
                        </button>
                        <button type="button" class="btn btn-primary" onclick="viewAllPlans()">
                            <i class="fas fa-eye"></i> View All Plans
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Upgrade Modal Styles -->
<style>
.upgrade-modal-content {
    border: none;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    overflow: hidden;
}

.upgrade-modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 25px 30px;
    position: relative;
}

.upgrade-header-content {
    display: flex;
    align-items: center;
    gap: 20px;
}

.upgrade-icon {
    background: rgba(255, 255, 255, 0.2);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.upgrade-title {
    margin: 0;
    font-size: 24px;
    font-weight: 700;
}

.upgrade-subtitle {
    margin: 5px 0 0 0;
    opacity: 0.9;
    font-size: 14px;
}

.upgrade-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.upgrade-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.upgrade-modal-body {
    padding: 30px;
    background: #f8f9fa;
}

.current-plan-section {
    margin-bottom: 30px;
}

.current-plan-header h5 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 15px;
}

.current-plan-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.plan-info .plan-name {
    font-size: 18px;
    font-weight: 600;
    color: #495057;
}

.plan-info .plan-price {
    font-size: 14px;
    color: #6c757d;
}

.plan-usage {
    flex: 1;
    max-width: 300px;
    margin-left: 20px;
}

.usage-item {
    margin-bottom: 10px;
}

.usage-item:last-child {
    margin-bottom: 0;
}

.usage-label {
    font-size: 12px;
    color: #6c757d;
    display: inline-block;
    width: 100px;
}

.usage-value {
    font-size: 12px;
    font-weight: 600;
    color: #495057;
    margin-left: 10px;
}

.usage-bar {
    background: #e9ecef;
    height: 6px;
    border-radius: 3px;
    margin-top: 5px;
    overflow: hidden;
}

.usage-progress {
    background: linear-gradient(90deg, #28a745, #20c997);
    height: 100%;
    border-radius: 3px;
    transition: width 0.3s ease;
}

.upgrade-options-header h5 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 5px;
}

.upgrade-options-header p {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 20px;
}

.upgrade-plans-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.upgrade-plan-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    position: relative;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.upgrade-plan-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #667eea;
}

.plan-badge {
    position: absolute;
    top: -10px;
    left: 20px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.plan-badge.enterprise {
    background: linear-gradient(135deg, #f093fb, #f5576c);
}

.plan-header {
    text-align: center;
    margin-bottom: 20px;
}

.plan-header .plan-name {
    font-size: 18px;
    font-weight: 700;
    color: #495057;
    margin-bottom: 10px;
}

.plan-price .price {
    font-size: 28px;
    font-weight: 700;
    color: #667eea;
}

.plan-price .period {
    font-size: 14px;
    color: #6c757d;
}

.plan-features ul {
    list-style: none;
    padding: 0;
    margin: 0 0 20px 0;
}

.plan-features li {
    padding: 8px 0;
    font-size: 14px;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 10px;
}

.plan-features li i {
    color: #28a745;
    font-size: 12px;
}

.upgrade-btn {
    width: 100%;
    padding: 12px;
    border-radius: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    color: white;
}

.upgrade-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
}

.upgrade-btn.enterprise {
    background: linear-gradient(135deg, #f093fb, #f5576c);
}

.upgrade-btn.enterprise:hover {
    box-shadow: 0 8px 20px rgba(240, 147, 251, 0.4);
}

.benefits-header h6 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 15px;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
}

.benefit-item {
    text-align: center;
    padding: 15px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.benefit-item i {
    font-size: 24px;
    color: #667eea;
    margin-bottom: 8px;
    display: block;
}

.benefit-item span {
    font-size: 12px;
    color: #495057;
    font-weight: 500;
}

.upgrade-modal-footer {
    background: white;
    border: none;
    padding: 25px 30px;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.security-badges {
    display: flex;
    gap: 20px;
}

.security-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #6c757d;
}

.security-item i {
    color: #28a745;
}

.footer-actions {
    display: flex;
    gap: 15px;
}

.footer-actions .btn {
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .upgrade-plans-grid {
        grid-template-columns: 1fr;
    }
    
    .benefits-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .current-plan-card {
        flex-direction: column;
        gap: 15px;
    }
    
    .plan-usage {
        margin-left: 0;
        width: 100%;
    }
    
    .footer-content {
        flex-direction: column;
        gap: 20px;
    }
    
    .security-badges {
        flex-wrap: wrap;
        justify-content: center;
    }
}
</style>
