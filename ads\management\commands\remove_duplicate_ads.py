from django.core.management.base import BaseCommand
from ads.models import Ad
from collections import defaultdict

class Command(BaseCommand):
    help = 'Removes duplicate ads based on title'

    def handle(self, *args, **kwargs):
        self.stdout.write(self.style.SUCCESS('Removing Duplicate Ads'))
        self.stdout.write('-' * 50)
        
        # Group ads by title
        ads_by_title = defaultdict(list)
        for ad in Ad.objects.all():
            ads_by_title[ad.title].append(ad)
        
        # Find duplicates
        duplicates = {title: ads for title, ads in ads_by_title.items() if len(ads) > 1}
        
        if not duplicates:
            self.stdout.write(self.style.SUCCESS('No duplicate ads found.'))
            return
            
        self.stdout.write(f'Found {len(duplicates)} titles with duplicate ads.')
        
        # Remove duplicates
        removed_count = 0
        for title, ads in duplicates.items():
            # Keep the first ad, remove the rest
            keep_ad = ads[0]
            for ad in ads[1:]:
                self.stdout.write(f'Removing duplicate ad: {ad.title} (ID: {ad.id})')
                ad.delete()
                removed_count += 1
        
        self.stdout.write(self.style.SUCCESS(f'Successfully removed {removed_count} duplicate ads'))
