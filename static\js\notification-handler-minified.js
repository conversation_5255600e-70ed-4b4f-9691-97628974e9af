(function(){'use strict';if(window.notificationHandlerLoaded)return;window.notificationHandlerLoaded=true;const u=window.SharedUtils||{};function getCsrfToken(){const n='csrftoken',c=document.cookie.split(';');for(let cookie of c){const t=cookie.trim();if(t.startsWith(n+'='))return decodeURIComponent(t.substring(n.length+1))}return null}function showNotificationError(c){if(!c)return;c.innerHTML='<div class="notification-dropdown-error p-3 text-center"><i class="fas fa-exclamation-circle text-danger mb-2"></i><p class="mb-0">Unable to load notifications. Please try again later.</p></div>';c.style.opacity='1';if(u.updateNotificationCounter)u.updateNotificationCounter(0)}function updateNotificationContent(d){const c=document.getElementById('notification-dropdown-content');if(!c)return;c.style.opacity='0';setTimeout(()=>{if(!d.notifications||d.notifications.length===0){c.innerHTML='<div class="notification-dropdown-empty p-3 text-center"><i class="fas fa-bell-slash"></i><p>0 notifications</p></div>';c.style.opacity='1';return}const itemsPerPage=4,totalPages=Math.ceil(d.notifications.length/itemsPerPage);let currentPage=1;function renderPage(p){const start=(p-1)*itemsPerPage,end=Math.min(start+itemsPerPage,d.notifications.length),pageNotifs=d.notifications.slice(start,end);let h='';pageNotifs.forEach(n=>{let ic='fas fa-bell',icc='';switch(n.notification_type){case'success':ic='fas fa-check-circle';icc='success';break;case'warning':ic='fas fa-exclamation-triangle';icc='warning';break;case'error':ic='fas fa-exclamation-circle';icc='danger';break;case'info':ic='fas fa-info-circle';icc='info';break}switch(n.category){case'ad':ic='fas fa-ad';break;case'payment':ic='fas fa-credit-card';break;case'qr_code':ic='fas fa-qrcode';break;case'user':ic='fas fa-user';break;case'campaign':ic='fas fa-bullhorn';break;case'analytics':ic='fas fa-chart-bar';break}const ft=n.time_ago||(n.created_at?u.formatTimestamp?u.formatTimestamp(new Date(n.created_at)):'':'');h+=`<div class="notification-item ${!n.is_read?'unread':''}" data-notification-id="${n.id}"><div class="notification-item-icon ${icc}"><i class="${ic}"></i></div><div class="notification-item-content"><div class="notification-item-title">${n.title}</div><div class="notification-item-message">${n.message}</div><div class="notification-item-time">${ft}</div></div><div class="notification-item-actions"><button class="notification-action-toggle" aria-label="More actions"><i class="fas fa-ellipsis-v"></i></button><div class="notification-action-dropdown"><button class="notification-action-btn mark-read" data-action="mark-read"><i class="fas fa-check"></i> Mark as ${!n.is_read?'read':'unread'}</button><button class="notification-action-btn archive" data-action="archive"><i class="fas fa-archive"></i> Archive</button><button class="notification-action-btn delete" data-action="delete"><i class="fas fa-trash-alt"></i> Delete</button></div></div>${n.url?`<a href="${n.url}" class="notification-item-link" aria-label="View details"></a>`:'}</div>`});if(totalPages>1){h+='<div class="notification-pagination">';h+=`<button class="notification-pagination-btn prev ${p===1?'disabled':''}" ${p===1?'disabled':''}><i class="fas fa-chevron-left"></i></button>`;for(let i=1;i<=totalPages;i++)h+=`<button class="notification-pagination-btn page ${i===p?'active':''}" data-page="${i}">${i}</button>`;h+=`<button class="notification-pagination-btn next ${p===totalPages?'disabled':''}" ${p===totalPages?'disabled':''}><i class="fas fa-chevron-right"></i></button></div>`}c.innerHTML=h;if(totalPages>1){document.querySelectorAll('.notification-pagination-btn.page').forEach(b=>b.addEventListener('click',function(){currentPage=parseInt(this.getAttribute('data-page'));renderPage(currentPage)}));const pb=document.querySelector('.notification-pagination-btn.prev');if(pb&&!pb.disabled)pb.addEventListener('click',()=>{if(currentPage>1){currentPage--;renderPage(currentPage)}});const nb=document.querySelector('.notification-pagination-btn.next');if(nb&&!nb.disabled)nb.addEventListener('click',()=>{if(currentPage<totalPages){currentPage++;renderPage(currentPage)}})}}renderPage(currentPage);setupNotificationActions();setTimeout(()=>c.style.opacity='1',50)},200)}function setupNotificationActions(){document.querySelectorAll('.notification-action-toggle').forEach(t=>t.addEventListener('click',function(e){e.preventDefault();e.stopPropagation();document.querySelectorAll('.notification-action-dropdown.show').forEach(d=>{if(d!==this.nextElementSibling)d.classList.remove('show')});this.nextElementSibling.classList.toggle('show')}));document.querySelectorAll('.notification-action-btn').forEach(b=>b.addEventListener('click',function(e){e.preventDefault();e.stopPropagation();const i=this.closest('.notification-item'),id=i.getAttribute('data-notification-id'),a=this.getAttribute('data-action');switch(a){case'mark-read':handleMarkReadUnread(id,i);break;case'archive':handleArchiveNotification(id,i);break;case'delete':showDeleteConfirmation(id,i);break}this.closest('.notification-action-dropdown').classList.remove('show')}));document.addEventListener('click',function(e){if(!e.target.closest('.notification-item-actions'))document.querySelectorAll('.notification-action-dropdown.show').forEach(d=>d.classList.remove('show'))})}function handleMarkReadUnread(id,i){const isUnread=i.classList.contains('unread'),url=`/notifications/${id}/mark-as-${isUnread?'read':'unread'}/`;fetch(url,{method:'POST',headers:{'X-CSRFToken':getCsrfToken(),'Content-Type':'application/json'}}).then(r=>r.json()).then(d=>{i.classList.toggle('unread');const b=i.querySelector('.notification-action-btn.mark-read');if(b)b.innerHTML=`<i class="fas fa-check"></i> Mark as ${isUnread?'unread':'read'}`;if(u.updateNotificationCounter)u.updateNotificationCounter(d.unread_count)}).catch(e=>console.error('Error updating notification:',e))}function handleArchiveNotification(id,i){fetch(`/notifications/${id}/archive/`,{method:'POST',headers:{'X-CSRFToken':getCsrfToken(),'X-Requested-With':'XMLHttpRequest','Content-Type':'application/json'}}).then(r=>r.json()).then(d=>{removeNotificationWithAnimation(i);showNotificationToast('Notification archived successfully.','success');if(u.updateNotificationCounter)u.updateNotificationCounter(d.unread_count)}).catch(e=>{console.error('Error archiving notification:',e);showNotificationToast('Failed to archive notification.','error')})}function showDeleteConfirmation(id,i){let d=document.getElementById('notification-confirm-dialog');if(!d){d=document.createElement('div');d.id='notification-confirm-dialog';d.className='notification-confirm-dialog';d.innerHTML='<div class="notification-confirm-content"><div class="notification-confirm-title"><i class="fas fa-exclamation-triangle"></i> Delete Notification</div><div class="notification-confirm-message">Are you sure you want to delete this notification? This action cannot be undone.</div><div class="notification-confirm-actions"><button class="notification-confirm-btn cancel">Cancel</button><button class="notification-confirm-btn confirm">Delete</button></div></div>';document.body.appendChild(d);d.querySelector('.cancel').addEventListener('click',()=>d.classList.remove('show'));d.addEventListener('click',e=>{if(e.target===d)d.classList.remove('show')})}const cb=d.querySelector('.confirm'),ncb=cb.cloneNode(true);cb.parentNode.replaceChild(ncb,cb);ncb.addEventListener('click',()=>{handleDeleteNotification(id,i);d.classList.remove('show')});d.classList.add('show')}function handleDeleteNotification(id,i){fetch(`/notifications/${id}/delete/`,{method:'POST',headers:{'X-CSRFToken':getCsrfToken(),'X-Requested-With':'XMLHttpRequest','Content-Type':'application/json'}}).then(r=>r.json()).then(d=>{removeNotificationWithAnimation(i);showNotificationToast('Notification deleted successfully.','success');if(u.updateNotificationCounter)u.updateNotificationCounter(d.unread_count)}).catch(e=>{console.error('Error deleting notification:',e);showNotificationToast('Failed to delete notification.','error')})}function removeNotificationWithAnimation(i){i.style.opacity='0';i.style.height=i.offsetHeight+'px';i.style.overflow='hidden';setTimeout(()=>{i.style.height='0';i.style.margin='0';i.style.padding='0';setTimeout(()=>{i.remove();const r=document.querySelectorAll('.notification-item');if(r.length===0){const c=document.getElementById('notification-dropdown-content');if(c)c.innerHTML='<div class="notification-dropdown-empty p-3 text-center"><i class="fas fa-bell-slash"></i><p>0 notifications</p></div>'}},300)},50)}function showNotificationToast(m,t='info'){let c=document.getElementById('notification-toast-container');if(!c){c=document.createElement('div');c.id='notification-toast-container';c.style.cssText='position:fixed;top:20px;right:20px;z-index:9999';document.body.appendChild(c)}const toast=document.createElement('div'),colors={success:'#4caf50',error:'#f44336',warning:'#ff9800',info:'#2196f3'},icons={success:'check-circle',error:'exclamation-circle',warning:'exclamation-triangle',info:'info-circle'};toast.style.cssText=`background:${colors[t]};color:white;padding:12px 20px;border-radius:4px;margin-bottom:10px;box-shadow:0 2px 10px rgba(0,0,0,0.1);display:flex;align-items:center;justify-content:space-between;opacity:0;transform:translateX(50px);transition:opacity 0.3s ease,transform 0.3s ease`;toast.innerHTML=`<div style="display:flex;align-items:center"><i class="fas fa-${icons[t]}" style="margin-right:10px"></i><span>${m}</span></div><button style="background:none;border:none;color:white;cursor:pointer;margin-left:10px"><i class="fas fa-times"></i></button>`;c.appendChild(toast);setTimeout(()=>{toast.style.opacity='1';toast.style.transform='translateX(0)'},10);const cb=toast.querySelector('button'),hide=()=>{toast.style.opacity='0';toast.style.transform='translateX(50px)';setTimeout(()=>toast.remove(),300)};cb.addEventListener('click',hide);setTimeout(hide,5000)}window.NotificationHandler={showNotificationError,updateNotificationContent,setupNotificationActions,showNotificationToast}})();
