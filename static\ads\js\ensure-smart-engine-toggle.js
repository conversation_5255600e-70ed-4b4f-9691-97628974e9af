/**
 * Ensure Smart Engine Toggle
 * This script ensures that the useSmartEngine element exists
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Ensure Smart Engine Toggle loaded');
    
    // Check if the useSmartEngine element exists
    let useSmartEngine = document.getElementById('useSmartEngine');
    
    // If it doesn't exist, create it
    if (!useSmartEngine) {
        console.log('Creating useSmartEngine element');
        
        // Create the toggle HTML
        const toggleHTML = `
            <div class="form-check form-switch mb-3">
                <input class="form-check-input" type="checkbox" id="useSmartEngine">
                <label class="form-check-label" for="useSmartEngine">
                    <span class="fw-bold">Use Smart Engine</span>
                    <span class="badge bg-warning text-dark ms-2">+20 KSH</span>
                </label>
                <div class="form-text">
                    <i class="fas fa-info-circle me-1"></i> Smart Engine uses AI to generate professional ad content
                </div>
            </div>
        `;
        
        // Create a temporary container
        const tempContainer = document.createElement('div');
        tempContainer.innerHTML = toggleHTML;
        
        // Get the toggle element
        const toggleElement = tempContainer.firstElementChild;
        
        // Find the ad title field
        const adTitle = document.getElementById('adTitle');
        
        if (adTitle) {
            // Find the parent form group
            const formGroup = adTitle.closest('.mb-4');
            
            if (formGroup) {
                // Insert the toggle after the form group
                formGroup.parentNode.insertBefore(toggleElement, formGroup.nextSibling);
            } else {
                // If all else fails, add it to the body
                document.body.appendChild(toggleElement);
            }
        } else {
            // If all else fails, add it to the body
            document.body.appendChild(toggleElement);
        }
        
        // Get the newly created toggle
        useSmartEngine = document.getElementById('useSmartEngine');
    }
    
    // Add event listener to the toggle
    useSmartEngine.addEventListener('change', function() {
        // Get the smart engine options
        const smartEngineOptions = document.getElementById('smartEngineOptions');
        
        if (smartEngineOptions) {
            // Show/hide the options based on the toggle state
            smartEngineOptions.style.display = this.checked ? 'block' : 'none';
        }
        
        // Get the generate suggestions button
        const generateSuggestions = document.getElementById('generateSuggestions');
        
        if (generateSuggestions) {
            // Show/hide the button based on the toggle state
            generateSuggestions.style.display = this.checked ? 'block' : 'none';
        }
        
        // Get the AI suggestions container
        const aiSuggestionsContainer = document.getElementById('aiSuggestionsContainer');
        
        if (aiSuggestionsContainer) {
            // Hide the suggestions container when the toggle is turned off
            if (!this.checked) {
                aiSuggestionsContainer.style.display = 'none';
            }
        }
        
        // Set the used_ai field
        const usedAiInput = document.getElementById('usedAiInput');
        
        if (usedAiInput) {
            usedAiInput.value = this.checked ? 'true' : 'false';
        }
    });
    
    console.log('useSmartEngine element:', useSmartEngine);
});
