from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse, HttpResponse, Http404
from django.contrib import messages
from django.utils import timezone
from django.db.models import Sum, Count
from django.core.paginator import Paginator
from django.urls import reverse
from datetime import datetime, timedelta
import json
from django.contrib.auth.models import User

from .models import AdType, AdLocation, Ad, Transaction, AdAnalytics, Campaign

# Simple test views
def test_view(request):
    return render(request, 'ads/test.html')

def test_static(request):
    return render(request, 'ads/test_static.html')

def direct_static(request):
    return render(request, 'ads/direct_static.html')



def dashboard_test(request):
    """
    Test version of the dashboard with minimal styling
    """
    # Reuse the same context as the main dashboard
    context = {}

    # Check if user is authenticated
    if request.user.is_authenticated:
        # Get user's ads
        user_ads = Ad.objects.filter(user=request.user)

        # Get counts for different statuses
        active_ads_count = user_ads.filter(status='active').count()
        pending_ads_count = user_ads.filter(status='pending').count()
        draft_ads_count = user_ads.filter(status='draft').count()
        total_ads_count = user_ads.count()

        # Get total impressions and clicks
        total_impressions = user_ads.aggregate(Sum('impressions'))['impressions__sum'] or 0
        total_clicks = user_ads.aggregate(Sum('clicks'))['clicks__sum'] or 0

        # Calculate CTR (Click-Through Rate)
        ctr = (total_clicks / total_impressions * 100) if total_impressions > 0 else 0

        # Calculate CTR for each ad in top_ads
        top_ads_with_ctr = []
        for ad in user_ads.order_by('-clicks')[:3]:
            ad_ctr = (ad.clicks / ad.impressions * 100) if ad.impressions > 0 else 0
            top_ads_with_ctr.append({
                'id': ad.id,
                'title': ad.title,
                'slug': ad.slug,
                'impressions': ad.impressions,
                'clicks': ad.clicks,
                'ctr': ad_ctr
            })

        # Get recent ads
        recent_ads = user_ads.order_by('-created_at')[:5]

        # Get all active ad types
        ad_types = AdType.objects.filter(is_active=True)

        context = {
            'active_ads_count': active_ads_count,
            'pending_ads_count': pending_ads_count,
            'draft_ads_count': draft_ads_count,
            'total_ads_count': total_ads_count,
            'total_impressions': total_impressions,
            'total_clicks': total_clicks,
            'ctr': ctr,
            'recent_ads': recent_ads,
            'top_ads': top_ads_with_ctr,
            'ad_types': ad_types,
        }
    else:
        # For unauthenticated users, show a simplified dashboard
        ad_types = AdType.objects.filter(is_active=True)
        context = {
            'active_ads_count': 0,
            'pending_ads_count': 0,
            'draft_ads_count': 0,
            'total_ads_count': 0,
            'total_impressions': 0,
            'total_clicks': 0,
            'ctr': 0,
            'recent_ads': [],
            'top_ads': [],
            'ad_types': ad_types,
        }

    return render(request, 'ads/dashboard_test.html', context)

# Legacy Dashboard view (now redirects to enterprise dashboard)
def dashboard(request):
    """
    Legacy dashboard - now redirects to the enterprise dashboard
    """
    # Show a message about the redirect
    messages.info(request, "You've been redirected to our new Enterprise Dashboard. The old dashboard is being phased out.")

    # Redirect to the enterprise dashboard
    return redirect('ads:dashboard')

# Ad management views
@login_required
# The ad_create view has been moved to views_enterprise.py
# We now use the enterprise version (ad_create_enterprise) as the main ad creation view
# See urls.py for the routing configuration

@login_required
def ad_list(request):
    """
    List all ads for the current user
    """
    # Get user's ads
    user_ads = Ad.objects.filter(user=request.user)

    # Initialize filter context
    filter_context = {
        'status': None,
        'ad_type': None,
        'date_range': None,
    }

    # Filter by status if provided
    status = request.GET.get('status')
    if status:
        user_ads = user_ads.filter(status=status)
        filter_context['status'] = status

    # Filter by ad type if provided
    ad_type_id = request.GET.get('ad_type')
    if ad_type_id:
        user_ads = user_ads.filter(ad_type_id=ad_type_id)
        filter_context['ad_type'] = ad_type_id
        try:
            filter_context['ad_type_name'] = AdType.objects.get(id=ad_type_id).name
        except AdType.DoesNotExist:
            filter_context['ad_type_name'] = "Unknown"

    # Filter by date range if provided
    date_range = request.GET.get('date_range')
    if date_range:
        today = timezone.now().date()
        if date_range == 'today':
            user_ads = user_ads.filter(created_at__date=today)
        elif date_range == 'week':
            start_of_week = today - timezone.timedelta(days=today.weekday())
            user_ads = user_ads.filter(created_at__date__gte=start_of_week)
        elif date_range == 'month':
            start_of_month = today.replace(day=1)
            user_ads = user_ads.filter(created_at__date__gte=start_of_month)
        elif date_range == 'year':
            start_of_year = today.replace(month=1, day=1)
            user_ads = user_ads.filter(created_at__date__gte=start_of_year)
        filter_context['date_range'] = date_range

    # Paginate the results
    paginator = Paginator(user_ads, 10)  # Show 10 ads per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get all active ad types for the filter dropdown
    ad_types = AdType.objects.filter(is_active=True)

    context = {
        'page_obj': page_obj,
        'ad_types': ad_types,
        'filter_context': filter_context,
        'total_count': user_ads.count(),
    }

    return render(request, 'ads/ad_list.html', context)

@login_required
def ad_detail(request, slug):
    """
    View details of a specific ad
    """
    # Try to get the ad by slug only first
    try:
        ad = Ad.objects.get(slug=slug)

        # If the ad is not owned by the current user, check if it's active
        if ad.user != request.user and ad.status != 'active':
            # If the user is not an admin and the ad is not active, show 404
            if not request.user.is_staff:
                raise Http404("Ad not found")
    except Ad.DoesNotExist:
        raise Http404("Ad not found")

    # Check if there's a paid transaction for this ad
    has_paid_transaction = False
    latest_transaction = None

    if ad.user == request.user:
        # Only check transactions for the ad owner
        has_paid_transaction = Transaction.objects.filter(
            ad=ad,
            status__in=['pending', 'completed']
        ).exists()

        latest_transaction = Transaction.objects.filter(
            ad=ad
        ).order_by('-timestamp').first()

    context = {
        'ad': ad,
        'has_paid_transaction': has_paid_transaction,
        'latest_transaction': latest_transaction,
        'is_owner': ad.user == request.user,
    }

    return render(request, 'ads/ad_detail.html', context)

@login_required
def ad_edit(request, slug):
    """
    Edit an existing ad
    """
    # Get the ad
    ad = get_object_or_404(Ad, slug=slug, user=request.user)

    # Check if the ad can be edited
    if ad.status not in ['draft', 'rejected']:
        messages.error(request, "This ad cannot be edited in its current status.")
        return redirect('ads:ad_detail', slug=slug)

    # Get all active ad types and locations
    ad_types = AdType.objects.filter(is_active=True)
    ad_locations = AdLocation.objects.filter(is_active=True)

    if request.method == 'POST':
        # Process form data
        title = request.POST.get('title')
        ad_type_id = request.POST.get('ad_type')
        ad_location_id = request.POST.get('ad_location')
        duration_option = request.POST.get('duration_option')
        start_date = request.POST.get('start_date')
        start_time = request.POST.get('start_time')
        end_date = request.POST.get('end_date')
        end_time = request.POST.get('end_time')
        content = request.POST.get('content')
        cta_link = request.POST.get('cta_link')
        target_location = request.POST.get('target_location')
        target_audience = request.POST.get('target_audience')
        requires_ai = request.POST.get('requires_ai') == 'true'
        wants_social = request.POST.get('wants_social') == 'true'
        base_pricing = request.POST.get('base_pricing')
        final_pricing = request.POST.get('final_pricing')
        keep_media = request.POST.get('keep_media') == 'true'

        # Get media file if uploaded
        media = request.FILES.get('media')

        # Get ad type and location
        ad_type = get_object_or_404(AdType, id=ad_type_id)
        ad_location = None
        if ad_location_id:
            ad_location = get_object_or_404(AdLocation, id=ad_location_id)

        # Combine date and time for start_date
        start_datetime_str = f"{start_date}T{start_time}:00"
        start_datetime = timezone.make_aware(datetime.strptime(start_datetime_str, "%Y-%m-%dT%H:%M:%S"))

        # Calculate end_datetime based on duration option
        if duration_option == 'custom' and end_date and end_time:
            # Use custom end date/time
            end_datetime_str = f"{end_date}T{end_time}:00"
            end_datetime = timezone.make_aware(datetime.strptime(end_datetime_str, "%Y-%m-%dT%H:%M:%S"))
        else:
            # Calculate based on preset duration
            end_datetime = start_datetime
            if duration_option == '7days':
                end_datetime = start_datetime + timezone.timedelta(days=7)
            elif duration_option == '2weeks':
                end_datetime = start_datetime + timezone.timedelta(days=14)
            elif duration_option == 'monthly':
                end_datetime = start_datetime + timezone.timedelta(days=30)
            else:
                # Default to 7 days
                end_datetime = start_datetime + timezone.timedelta(days=7)

        # Add 2-hour bonus to end time
        end_datetime = end_datetime + timezone.timedelta(hours=2)

        # Update ad
        ad.title = title
        ad.ad_type = ad_type
        ad.ad_location = ad_location
        ad.content = content
        ad.cta_link = cta_link
        ad.target_location = target_location
        ad.target_audience = target_audience
        ad.start_date = start_datetime
        ad.end_date = end_datetime
        ad.requires_ai = requires_ai
        ad.wants_social = wants_social
        ad.base_pricing = base_pricing
        ad.final_pricing = final_pricing

        # Handle media
        if media:
            # If new media is uploaded, use it
            ad.media = media
        elif not keep_media:
            # If keep_media is not checked and no new media is uploaded, clear the media
            ad.media = None

        ad.save()

        messages.success(request, 'Advertisement updated successfully!')
        return redirect('ads:ad_detail', slug=ad.slug)

    # Get initial campaigns for the dropdown
    initial_campaigns = Campaign.objects.filter(user=request.user).order_by('-created_at')[:5]

    context = {
        'ad': ad,
        'ad_types': ad_types,
        'ad_locations': ad_locations,
        'campaigns': initial_campaigns,
        'api_search_url': reverse('ads:api_search_campaigns'),
    }

    return render(request, 'ads/ad_edit.html', context)

@login_required
def ad_delete(request, slug):
    """
    Delete an ad
    """
    # Get the ad
    ad = get_object_or_404(Ad, slug=slug, user=request.user)

    if request.method == 'POST':
        ad.delete()
        messages.success(request, "Advertisement deleted successfully.")
        return redirect('ads:ad_list')

    context = {
        'ad': ad,
    }

    return render(request, 'ads/ad_delete.html', context)

# Ad status management views
@login_required
def ad_submit(request, slug):
    """
    Submit an ad for approval
    """
    # Get the ad
    ad = get_object_or_404(Ad, slug=slug, user=request.user)

    # Check if the ad can be submitted
    if ad.status not in ['draft', 'rejected']:
        messages.error(request, "This ad cannot be submitted in its current status.")
        return redirect('ads:ad_detail', slug=slug)

    # Update the status
    ad.status = 'pending'
    ad.save()

    # Redirect to the submission confirmation page instead of the detail page
    return redirect('ads:ad_submitted', slug=slug)

@login_required
def ad_submitted(request, slug):
    """
    Display a confirmation page after an ad has been created or submitted for approval
    """
    # Get the ad
    ad = get_object_or_404(Ad, slug=slug, user=request.user)

    # Allow both draft and pending statuses for the thank you page
    # Draft: Ad just created, Pending: Ad submitted for approval
    if ad.status not in ['draft', 'pending']:
        messages.warning(request, "This ad confirmation page is only available for newly created or pending ads.")
        return redirect('ads:ad_detail', slug=slug)

    context = {
        'ad': ad,
    }

    return render(request, 'ads/ad_submitted.html', context)

@login_required
def ad_pause(request, slug):
    """
    Pause an active ad
    """
    # Get the ad
    ad = get_object_or_404(Ad, slug=slug, user=request.user)

    # Check if the ad can be paused
    if ad.status != 'active':
        messages.error(request, "Only active ads can be paused.")
        return redirect('ads:ad_detail', slug=slug)

    # Update the status
    ad.status = 'paused'
    ad.save()

    messages.success(request, "Advertisement paused successfully.")
    return redirect('ads:ad_detail', slug=slug)

@login_required
def ad_resume(request, slug):
    """
    Resume a paused ad
    """
    # Get the ad
    ad = get_object_or_404(Ad, slug=slug, user=request.user)

    # Check if the ad can be resumed
    if ad.status != 'paused':
        messages.error(request, "Only paused ads can be resumed.")
        return redirect('ads:ad_detail', slug=slug)

    # Update the status
    ad.status = 'active'
    ad.save()

    messages.success(request, "Advertisement resumed successfully.")
    return redirect('ads:ad_detail', slug=slug)

# Transaction views
@login_required
def transaction_list(request):
    """
    List all transactions for the current user
    """
    # Get user's transactions
    transactions = Transaction.objects.filter(user=request.user).order_by('-timestamp')

    # Paginate the results
    paginator = Paginator(transactions, 10)  # Show 10 transactions per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
    }

    return render(request, 'ads/transaction_list.html', context)

@login_required
def transaction_detail(request, transaction_id):
    """
    View details of a specific transaction
    """
    # Get the transaction
    transaction = get_object_or_404(Transaction, id=transaction_id, user=request.user)

    context = {
        'transaction': transaction,
    }

    return render(request, 'ads/transaction_detail.html', context)

@login_required
def payment_process(request, slug):
    """
    Process payment for an ad
    """
    # Get the ad
    ad = get_object_or_404(Ad, slug=slug, user=request.user)

    # Check if the ad can be paid for
    if ad.status != 'approved':
        messages.error(request, "Only approved ads can be paid for.")
        return redirect('ads:ad_detail', slug=slug)

    # Check if there's already a paid transaction for this ad
    has_paid_transaction = Transaction.objects.filter(
        ad=ad,
        status__in=['pending', 'paid', 'processing']
    ).exists()

    # Get the latest transaction for this ad
    latest_transaction = Transaction.objects.filter(
        ad=ad
    ).order_by('-timestamp').first()

    if request.method == 'POST':
        # Only process if no existing payment
        if not has_paid_transaction:
            # Get form data
            payment_gateway = request.POST.get('payment_gateway')

            # Create a new transaction
            transaction = Transaction(
                user=request.user,
                ad=ad,
                amount=ad.final_pricing,
                status='paid',  # Set to paid immediately for demo purposes
                payment_gateway=payment_gateway,
                transaction_id=f"{payment_gateway.upper()}-{ad.id}-{timezone.now().strftime('%Y%m%d%H%M%S')}"
            )

            # Save payment details based on gateway
            payment_details = {}
            if payment_gateway == 'mpesa':
                payment_details['phone_number'] = request.POST.get('phone_number')
            elif payment_gateway == 'card':
                # Mask card number for security
                card_number = request.POST.get('card_number', '')
                if card_number:
                    masked_number = '*' * (len(card_number) - 4) + card_number[-4:]
                    payment_details['card_number'] = masked_number
                    payment_details['expiry_date'] = request.POST.get('expiry_date')
                    payment_details['card_name'] = request.POST.get('card_name')

            transaction.payment_details = payment_details
            transaction.save()

            # Notify admin about the new payment
            from notifications.services import NotificationService
            admins = User.objects.filter(is_superuser=True)
            NotificationService.create_notification_for_many_users(
                users=admins,
                title="New Payment Received",
                message=f"A new payment of {transaction.amount} KSH has been received from {request.user.username} for ad '{ad.title}'.",
                notification_type="info",
                category="payment",
                content_object=transaction,
                action_url=reverse('ads:admin_pending_payments')
            )

            messages.success(request, "Payment processed successfully. Your ad will be activated after admin approval.")

            # Refresh the page to show the payment details
            return redirect('ads:payment_process', slug=slug)
        else:
            messages.warning(request, "You have already submitted a payment for this ad.")

    # Refresh the transaction status after POST processing
    has_paid_transaction = Transaction.objects.filter(
        ad=ad,
        status__in=['pending', 'paid', 'processing']
    ).exists()

    latest_transaction = Transaction.objects.filter(
        ad=ad
    ).order_by('-timestamp').first()

    context = {
        'ad': ad,
        'has_paid_transaction': has_paid_transaction,
        'latest_transaction': latest_transaction,
    }

    return render(request, 'ads/payment_process.html', context)

# Analytics views
@login_required
def analytics_dashboard(request):
    """
    Main analytics dashboard
    """
    # Get filter parameters
    time_period = request.GET.get('period', '30days')
    metric_type = request.GET.get('metric', 'clicks')
    campaign_id = request.GET.get('campaign', '')
    ad_type_id = request.GET.get('ad_type', '')
    date_range = request.GET.get('date_range', 'Last 30 Days')

    # Get user's ads
    user_ads = Ad.objects.filter(user=request.user)

    # Apply campaign filter if provided
    if campaign_id and campaign_id.isdigit():
        user_ads = user_ads.filter(campaign_id=int(campaign_id))

    # Apply ad type filter if provided
    if ad_type_id and ad_type_id.isdigit():
        user_ads = user_ads.filter(ad_type_id=int(ad_type_id))

    # Get total impressions and clicks
    total_impressions = user_ads.aggregate(Sum('impressions'))['impressions__sum'] or 0
    total_clicks = user_ads.aggregate(Sum('clicks'))['clicks__sum'] or 0

    # Calculate CTR (Click-Through Rate)
    ctr = (total_clicks / total_impressions * 100) if total_impressions > 0 else 0

    # Get top performing ads (only include ads with at least one impression)
    top_ads = user_ads.filter(impressions__gt=0).order_by('-clicks')[:5]

    # If no ads have impressions, just get the most recent ads
    if not top_ads:
        top_ads = user_ads.order_by('-created_at')[:5]

    # Get active ads count
    active_ads_count = user_ads.filter(status='active').count()

    # Get active campaigns
    active_campaigns = Campaign.objects.filter(user=request.user, status='active')
    active_campaigns_count = active_campaigns.count()

    # Calculate CTR for each ad
    for ad in top_ads:
        ad.ctr = (ad.clicks / ad.impressions * 100) if ad.impressions > 0 else 0

    # Get date range based on selected period or custom date range
    today = timezone.now().date()

    # Check if we have a custom date range from the date picker
    if date_range and date_range != 'Last 30 Days':
        try:
            # Parse the date range string (format: "MMM D, YYYY - MMM D, YYYY")
            date_parts = date_range.split(' - ')
            if len(date_parts) == 2:
                from datetime import datetime
                start_date_str, end_date_str = date_parts
                start_date = datetime.strptime(start_date_str, '%b %d, %Y').date()
                end_date = datetime.strptime(end_date_str, '%b %d, %Y').date()
            else:
                # Default to 30 days if format is unexpected
                start_date = today - timezone.timedelta(days=30)
                end_date = today
        except Exception as e:
            print(f"Error parsing date range: {e}")
            # Default to 30 days if there's an error
            start_date = today - timezone.timedelta(days=30)
            end_date = today
    else:
        # Use the period filter
        if time_period == '7days':
            start_date = today - timezone.timedelta(days=7)
        elif time_period == '30days':
            start_date = today - timezone.timedelta(days=30)
        elif time_period == '90days':
            start_date = today - timezone.timedelta(days=90)
        elif time_period == 'year':
            start_date = today - timezone.timedelta(days=365)
        else:
            # Default to 30 days
            start_date = today - timezone.timedelta(days=30)
        end_date = today

    # Get analytics data for the selected period
    analytics_data = AdAnalytics.objects.filter(
        ad__in=user_ads,
        date__gte=start_date,
        date__lte=end_date
    ).order_by('date')

    # Prepare data for charts
    chart_data = {
        'labels': [],
        'impressions': [],
        'clicks': [],
        'ctr': []
    }

    # Group data by date
    date_data = {}
    for data in analytics_data:
        date_str = data.date.strftime('%Y-%m-%d')
        if date_str not in date_data:
            date_data[date_str] = {'impressions': 0, 'clicks': 0}

        date_data[date_str]['impressions'] += data.impressions
        date_data[date_str]['clicks'] += data.clicks

    # Fill in missing dates
    current_date = start_date
    while current_date <= end_date:
        date_str = current_date.strftime('%Y-%m-%d')
        display_date = current_date.strftime('%b %d')

        chart_data['labels'].append(display_date)

        if date_str in date_data:
            impressions = date_data[date_str]['impressions']
            clicks = date_data[date_str]['clicks']
        else:
            impressions = 0
            clicks = 0

        chart_data['impressions'].append(impressions)
        chart_data['clicks'].append(clicks)

        # Calculate CTR for this date
        if impressions > 0:
            ctr_value = (clicks / impressions) * 100
        else:
            ctr_value = 0

        chart_data['ctr'].append(round(ctr_value, 2))

        current_date += timezone.timedelta(days=1)

    # Get device and location data
    device_data = {}
    location_data = {}

    for data in analytics_data:
        if data.device_data:
            for device, count in data.device_data.items():
                if device in device_data:
                    device_data[device] += count
                else:
                    device_data[device] = count

        if data.location_data:
            for location, count in data.location_data.items():
                if location in location_data:
                    location_data[location] += count
                else:
                    location_data[location] = count

    # Sort and limit location data to top 5
    sorted_locations = sorted(location_data.items(), key=lambda x: x[1], reverse=True)[:5]
    location_data = dict(sorted_locations)

    # Convert data to JSON for JavaScript
    import json

    # Get all campaigns for the user for the filter dropdown
    all_campaigns = Campaign.objects.filter(user=request.user)

    # Get all ad types for the filter dropdown
    all_ad_types = AdType.objects.filter(is_active=True)

    context = {
        'total_impressions': total_impressions,
        'total_clicks': total_clicks,
        'ctr': ctr,
        'top_ads': top_ads,
        'active_ads_count': active_ads_count,
        'active_campaigns': active_campaigns,
        'active_campaigns_count': active_campaigns_count,
        'chart_data_json': json.dumps({
            'labels': chart_data['labels'],
            'impressions': chart_data['impressions'],
            'clicks': chart_data['clicks'],
            'ctr': chart_data['ctr']
        }),
        'device_data_json': json.dumps(device_data),
        'location_data_json': json.dumps(location_data),
        'selected_period': time_period,
        'selected_metric': metric_type,
        'campaigns': all_campaigns,
        'ad_types': all_ad_types,
        'selected_campaign': campaign_id,
        'selected_ad_type': ad_type_id,
        'date_range': date_range
    }

    return render(request, 'ads/analytics_dashboard.html', context)

@login_required
def ad_analytics(request, slug):
    """
    View analytics for a specific ad
    """
    print(f"DEBUG: Accessing analytics for ad with slug: {slug}")
    print(f"DEBUG: Current user: {request.user.username}, Is superuser: {request.user.is_superuser}")

    # Get the ad by ID instead of slug if the slug is numeric
    if slug.isdigit():
        try:
            ad = Ad.objects.get(id=int(slug))
            print(f"DEBUG: Found ad by ID: {ad.id}, Title: {ad.title}")
        except Ad.DoesNotExist:
            print(f"DEBUG: Ad with ID {slug} not found")
            raise Http404("Ad not found")
    else:
        try:
            # Try to get the ad by slug
            ad = Ad.objects.get(slug=slug)
            print(f"DEBUG: Found ad by slug: {ad.id}, Title: {ad.title}")
        except Ad.DoesNotExist:
            print(f"DEBUG: Ad with slug '{slug}' not found")
            raise Http404("Ad not found")

    # Check permissions
    if not request.user.is_superuser and ad.user != request.user:
        print(f"DEBUG: Permission denied. Ad owner: {ad.user.username}, Current user: {request.user.username}")
        return redirect('ads:dashboard')

    # Get analytics data
    analytics_data = AdAnalytics.objects.filter(ad=ad).order_by('-date')

    # Process data for charts
    chart_data = {
        'dates': [],
        'impressions': [],
        'clicks': [],
        'device_data': {},
        'location_data': {}
    }

    # Aggregate device and location data
    total_device_data = {}
    total_location_data = {}

    # Check if we have any analytics data
    has_analytics_data = analytics_data.exists()

    # If no analytics data, provide empty datasets but with today's date
    if not has_analytics_data:
        today = timezone.now().date()
        chart_data['dates'].append(today.strftime('%b %d'))
        chart_data['impressions'].append(0)
        chart_data['clicks'].append(0)
    else:
        for data in analytics_data:
            # Add date and metrics to chart data
            chart_data['dates'].append(data.date.strftime('%b %d'))
            chart_data['impressions'].append(data.impressions)
            chart_data['clicks'].append(data.clicks)

            # Aggregate device data
            if data.device_data:
                for device, count in data.device_data.items():
                    if device in total_device_data:
                        total_device_data[device] += count
                    else:
                        total_device_data[device] = count

            # Aggregate location data
            if data.location_data:
                for location, count in data.location_data.items():
                    if location in total_location_data:
                        total_location_data[location] += count
                    else:
                        total_location_data[location] = count

    # Sort and limit location data to top 5
    sorted_locations = sorted(total_location_data.items(), key=lambda x: x[1], reverse=True)[:5]
    chart_data['location_data'] = dict(sorted_locations)

    # Add device data
    chart_data['device_data'] = total_device_data

    context = {
        'ad': ad,
        'analytics_data': analytics_data,
        'chart_data': chart_data,
    }

    return render(request, 'ads/ad_analytics.html', context)

# Admin views for superusers
@login_required
def admin_pending_ads(request):
    """
    View all pending ads for approval (superuser only)
    """
    # Check if user is a superuser
    if not request.user.is_superuser:
        messages.error(request, "You don't have permission to access this page.")
        return redirect('ads:dashboard')

    # Get all pending ads (ensure we're only getting truly pending ads)
    pending_ads = Ad.objects.filter(status='pending').order_by('-created_at')

    # Paginate the results
    paginator = Paginator(pending_ads, 10)  # Show 10 ads per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'total_count': pending_ads.count(),
    }

    return render(request, 'ads/admin_pending_ads.html', context)

@login_required
def admin_approve_ad(request, slug):
    """
    Approve a pending ad (superuser only)
    """
    # Check if user is a superuser
    if not request.user.is_superuser:
        messages.error(request, "You don't have permission to access this page.")
        return redirect('ads:dashboard')

    # Get the ad
    ad = get_object_or_404(Ad, slug=slug)

    # Check if the ad is already approved
    if ad.status == 'approved':
        messages.info(request, f"Advertisement '{ad.title}' is already approved.")
        return redirect('ads:admin_pending_ads')

    # Check if the ad is pending
    if ad.status != 'pending':
        messages.error(request, "Only pending ads can be approved.")
        return redirect('ads:admin_pending_ads')

    # Check if the ad uses AI and requires payment
    if ad.used_ai:
        # Check if there's a paid transaction for this ad
        has_paid_transaction = Transaction.objects.filter(
            ad=ad,
            status__in=['paid', 'approved', 'completed']
        ).exists()

        # If no paid transaction exists, redirect to payment page
        if not has_paid_transaction:
            messages.warning(request, f"Advertisement '{ad.title}' uses AI content and requires payment before approval. Please create a transaction record first.")
            return redirect('ads:admin_view_ad', slug=slug)

    # Update the status
    ad.status = 'approved'
    ad.save()

    # Create a notification for the user with a link to the payment page
    from notifications.services import NotificationService
    payment_url = reverse('ads:payment_process', kwargs={'slug': ad.slug})
    NotificationService.create_notification(
        user=ad.user,
        title="Ad Approved - Payment Required",
        message=f"Your advertisement '{ad.title}' has been approved! Please complete payment to activate your ad.",
        notification_type="success",
        category="ad",
        content_object=ad,
        action_url=payment_url
    )

    messages.success(request, f"Advertisement '{ad.title}' has been approved and notification sent to user.")
    return redirect('ads:admin_pending_ads')

@login_required
def admin_reject_ad(request, slug):
    """
    Reject a pending ad (superuser only)
    """
    # Check if user is a superuser
    if not request.user.is_superuser:
        messages.error(request, "You don't have permission to access this page.")
        return redirect('ads:dashboard')

    # Get the ad
    ad = get_object_or_404(Ad, slug=slug)

    # Check if the ad is pending
    if ad.status != 'pending':
        messages.error(request, "Only pending ads can be rejected.")
        return redirect('ads:admin_pending_ads')

    if request.method == 'POST':
        # Get rejection reason
        rejection_reason = request.POST.get('rejection_reason')

        # Update the status and rejection reason
        ad.status = 'rejected'
        ad.rejection_reason = rejection_reason
        ad.save()

        # Create a notification for the user with a link to the edit page
        from notifications.services import NotificationService
        edit_url = reverse('ads:ad_edit', kwargs={'slug': ad.slug})
        NotificationService.create_notification(
            user=ad.user,
            title="Ad Rejected - Edits Required",
            message=f"Your advertisement '{ad.title}' has been rejected. Reason: {rejection_reason}. Please make the necessary changes and resubmit.",
            notification_type="error",
            category="ad",
            content_object=ad,
            action_url=edit_url
        )

        messages.success(request, f"Advertisement '{ad.title}' has been rejected and notification sent to user.")
        return redirect('ads:admin_pending_ads')

    context = {
        'ad': ad,
    }

    return render(request, 'ads/admin_reject_ad.html', context)

@login_required
def admin_view_ad(request, slug):
    """
    View details of a specific ad (superuser only)
    """
    # Check if user is a superuser
    if not request.user.is_superuser:
        messages.error(request, "You don't have permission to access this page.")
        return redirect('ads:dashboard')

    # Get the ad
    ad = get_object_or_404(Ad, slug=slug)

    # Get analytics data
    analytics_data = AdAnalytics.objects.filter(ad=ad).order_by('-date')

    context = {
        'ad': ad,
        'analytics_data': analytics_data,
    }

    return render(request, 'ads/admin_view_ad.html', context)

@login_required
def admin_active_ads(request):
    """
    View all active ads for monitoring (superuser only)
    """
    # Check if user is a superuser
    if not request.user.is_superuser:
        messages.error(request, "You don't have permission to access this page.")
        return redirect('ads:dashboard')

    # Get all active ads
    active_ads = Ad.objects.filter(status='active').order_by('-start_date')

    # Paginate the results
    paginator = Paginator(active_ads, 10)  # Show 10 ads per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'total_count': active_ads.count(),
    }

    return render(request, 'ads/admin_active_ads.html', context)

@login_required
def admin_all_ads(request):
    """
    View all ads in the system (superuser only)
    """
    # Check if user is a superuser
    if not request.user.is_superuser:
        messages.error(request, "You don't have permission to access this page.")
        return redirect('ads:dashboard')

    # Get filter parameters
    status = request.GET.get('status', '')
    username = request.GET.get('username', '')

    # Get all ads
    all_ads = Ad.objects.all().order_by('-created_at')

    # Apply filters
    if status:
        all_ads = all_ads.filter(status=status)

    if username:
        all_ads = all_ads.filter(user__username__icontains=username)

    # Get unique usernames for filter dropdown
    usernames = Ad.objects.values_list('user__username', flat=True).distinct()

    # Paginate the results
    paginator = Paginator(all_ads, 10)  # Show 10 ads per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'total_count': all_ads.count(),
        'status_choices': Ad.STATUS_CHOICES,
        'current_status': status,
        'usernames': usernames,
        'current_username': username,
    }

    return render(request, 'ads/admin_all_ads.html', context)

@login_required
def admin_update_ad_status(request, slug):
    """
    Update ad status (superuser only)
    """
    # Check if user is a superuser
    if not request.user.is_superuser:
        messages.error(request, "You don't have permission to access this page.")
        return redirect('ads:dashboard')

    # Get the ad
    ad = get_object_or_404(Ad, slug=slug)

    if request.method == 'POST':
        # Get new status
        new_status = request.POST.get('status')

        # Validate status
        valid_statuses = [status[0] for status in Ad.STATUS_CHOICES]
        if new_status not in valid_statuses:
            messages.error(request, "Invalid status.")
            return redirect('ads:admin_view_ad', slug=slug)

        # Prevent changing status if already approved
        if ad.status == 'approved' and new_status == 'approved':
            messages.info(request, f"Advertisement '{ad.title}' is already approved.")
            return redirect('ads:admin_view_ad', slug=slug)

        # Prevent changing from approved to pending
        if ad.status == 'approved' and new_status == 'pending':
            messages.warning(request, f"Cannot change an approved advertisement back to pending status.")
            return redirect('ads:admin_view_ad', slug=slug)

        # Update the status
        ad.status = new_status
        ad.save()

        # If changing to approved status, send notification to user
        if new_status == 'approved':
            # Create a notification for the user with a link to the payment page
            from notifications.services import NotificationService
            payment_url = reverse('ads:payment_process', kwargs={'slug': ad.slug})
            NotificationService.create_notification(
                user=ad.user,
                title="Ad Approved - Payment Required",
                message=f"Your advertisement '{ad.title}' has been approved! Please complete payment to activate your ad.",
                notification_type="success",
                category="ad",
                content_object=ad,
                action_url=payment_url
            )
            messages.success(request, f"Advertisement '{ad.title}' status updated to {ad.get_status_display()} and notification sent to user.")
        else:
            messages.success(request, f"Advertisement '{ad.title}' status updated to {ad.get_status_display()}.")

        # Redirect based on new status
        if new_status == 'active':
            return redirect('ads:admin_active_ads')
        elif new_status == 'pending':
            return redirect('ads:admin_pending_ads')
        else:
            return redirect('ads:admin_view_ad', slug=slug)

    context = {
        'ad': ad,
        'status_choices': Ad.STATUS_CHOICES,
    }

    return render(request, 'ads/admin_update_ad_status.html', context)

# API endpoints
def calculate_price(request):
    """
    Calculate the price for an ad based on parameters
    """
    if request.method == 'GET':
        # Get parameters
        ad_type_id = request.GET.get('ad_type_id')
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')
        requires_ai = request.GET.get('requires_ai') == 'true'
        wants_social = request.GET.get('wants_social') == 'true'

        # Validate parameters
        if not all([ad_type_id, start_date, end_date]):
            return JsonResponse({'error': 'Missing required parameters'}, status=400)

        try:
            # Get the ad type
            ad_type = AdType.objects.get(id=ad_type_id)

            # Calculate base price
            base_price = ad_type.base_price

            # Calculate duration
            from datetime import datetime
            start = datetime.strptime(start_date, '%Y-%m-%d').date()
            end = datetime.strptime(end_date, '%Y-%m-%d').date()
            duration = (end - start).days + 1  # +1 to include both start and end days

            # Calculate additional costs
            ai_price = 50.00 if requires_ai else 0.00
            social_price = 30.00 if wants_social else 0.00

            # Calculate total price
            total_price = base_price * duration + ai_price + social_price

            # Return the price details
            return JsonResponse({
                'base_price': float(base_price),
                'duration': duration,
                'ai_price': float(ai_price),
                'social_price': float(social_price),
                'total_price': float(total_price),
            })

        except AdType.DoesNotExist:
            return JsonResponse({'error': 'Invalid ad type'}, status=400)
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=400)

    return JsonResponse({'error': 'Invalid request method'}, status=405)

def track_impression(request):
    """
    Track an ad impression
    """
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            ad_id = data.get('ad_id')
            device_info = data.get('device_info', {})
            location_info = data.get('location_info', {})

            print(f"DEBUG: Tracking impression for ad ID: {ad_id}")

            # Get the ad
            ad = Ad.objects.get(id=ad_id)

            # Only track impressions for active ads (meaning they have been paid for)
            if ad.status != 'active':
                print(f"DEBUG: Ad {ad_id} status is {ad.status}, not tracking impression")
                return JsonResponse({'error': f'Ad status is {ad.status}, not active. Only active ads can be tracked.', 'status': ad.status}, status=400)

            print(f"DEBUG: Ad {ad_id} status is {ad.status}, tracking impression")

            # Increment the ad's impression count
            ad.impressions += 1
            ad.save()

            # Get or create today's analytics record
            today = timezone.now().date()
            analytics, created = AdAnalytics.objects.get_or_create(
                ad=ad,
                date=today,
                defaults={
                    'impressions': 0,
                    'clicks': 0,
                    'unique_views': 0,
                    'conversion_count': 0,
                    'device_data': {},
                    'location_data': {}
                }
            )

            # Update analytics
            analytics.impressions += 1

            # Update device data
            device_data = analytics.device_data or {}
            device_type = device_info.get('type', 'unknown')
            device_data[device_type] = device_data.get(device_type, 0) + 1
            analytics.device_data = device_data

            # Update location data
            location_data = analytics.location_data or {}
            location = location_info.get('city', 'unknown')
            location_data[location] = location_data.get(location, 0) + 1
            analytics.location_data = location_data

            analytics.save()

            return JsonResponse({'success': True})

        except Ad.DoesNotExist:
            return JsonResponse({'error': 'Ad not found'}, status=404)
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=400)

    return JsonResponse({'error': 'Invalid request method'}, status=405)

def geolocation_test(request):
    """
    Test page for geolocation tracking
    """
    return render(request, 'ads/geolocation_test.html')

def ad_display_test(request):
    """
    Test page for ad display functionality
    """
    return render(request, 'ads/ad_test.html')

def ad_size_guide(request):
    """
    Guide for standard ad sizes
    """
    return render(request, 'ads/ad_size_guide.html')

def track_click(request):
    """
    Track an ad click
    """
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            ad_id = data.get('ad_id')
            device_info = data.get('device_info', {})
            location_info = data.get('location_info', {})

            print(f"DEBUG: Tracking click for ad ID: {ad_id}")

            # Get the ad
            ad = Ad.objects.get(id=ad_id)

            # Only track clicks for active ads (meaning they have been paid for)
            if ad.status != 'active':
                print(f"DEBUG: Ad {ad_id} status is {ad.status}, not tracking click")
                return JsonResponse({'error': f'Ad status is {ad.status}, not active. Only active ads can be tracked.', 'status': ad.status}, status=400)

            print(f"DEBUG: Ad {ad_id} status is {ad.status}, tracking click")

            # Increment the ad's click count
            ad.clicks += 1
            ad.save()

            # Get or create today's analytics record
            today = timezone.now().date()
            analytics, created = AdAnalytics.objects.get_or_create(
                ad=ad,
                date=today,
                defaults={
                    'impressions': 0,
                    'clicks': 0,
                    'unique_views': 0,
                    'conversion_count': 0,
                    'device_data': {},
                    'location_data': {}
                }
            )

            # Update analytics
            analytics.clicks += 1

            # Update device data for clicks
            device_data = analytics.device_data or {}
            device_type = device_info.get('type', 'unknown')

            # Update location data for clicks
            location_data = analytics.location_data or {}
            location = location_info.get('city', 'unknown')
            location_data[location] = location_data.get(location, 0) + 1
            analytics.location_data = location_data

            analytics.save()

            return JsonResponse({'success': True})

        except Ad.DoesNotExist:
            return JsonResponse({'error': 'Ad not found'}, status=404)
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=400)

    return JsonResponse({'error': 'Invalid request method'}, status=405)

from django.views.decorators.csrf import csrf_exempt

@csrf_exempt
def test_smart_ai_engine(request):
    """
    Test endpoint for Smart AI Engine without authentication
    """
    from ai_services.clients import get_ai_client
    import json
    import time
    import logging

    logger = logging.getLogger(__name__)

    if request.method == 'POST':
        try:
            # Parse request body
            data = json.loads(request.body)

            # Extract parameters
            language = data.get('language', 'english')
            business_type = data.get('business_type', 'Premium Coffee Shop')
            target_audience = data.get('target_audience', 'Young Professionals')
            tone = data.get('tone', 'professional')
            ad_title = data.get('title', 'Premium Coffee Experience')
            num_suggestions = data.get('num_suggestions', 3)

            # Get the Smart AI Engine
            from ai_services.settings import AI_PROVIDER
            logger.info(f"🔧 AI_PROVIDER setting: {AI_PROVIDER}")

            # Debug the get_ai_client function
            logger.info(f"🔍 get_ai_client function: {get_ai_client}")
            logger.info(f"🔍 get_ai_client module: {get_ai_client.__module__}")

            ai_client = get_ai_client()
            client_type = type(ai_client).__name__
            logger.info(f"🤖 get_ai_client() returned: {client_type}")

            # Generate suggestions
            start_time = time.time()
            suggestions = ai_client.generate_ad_suggestions(
                language=language,
                business_type=business_type,
                target_audience=target_audience,
                tone=tone,
                num_suggestions=num_suggestions,
                ad_title=ad_title
            )
            end_time = time.time()
            response_time = end_time - start_time

            # Determine source
            source = 'smart_engine'
            if suggestions and len(suggestions) > 0:
                first_suggestion = suggestions[0]
                if first_suggestion.get('cached'):
                    source = 'cache'
                elif first_suggestion.get('provider') == 'groq':
                    source = 'groq'
                elif first_suggestion.get('provider') == 'mistral':
                    source = 'mistral'
                elif first_suggestion.get('provider') == 'openai':
                    source = 'openai'
                elif first_suggestion.get('offline'):
                    source = 'offline'

            return JsonResponse({
                'success': True,
                'suggestions': suggestions,
                'source': source,
                'engine': client_type,
                'response_time': response_time,
                'test_mode': True
            })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': str(e),
                'test_mode': True
            })

    # GET request - show test form
    return render(request, 'ads/test_smart_ai.html')

@csrf_exempt
def test_ai_suggestions_debug(request):
    """Debug page for AI suggestions"""
    return render(request, 'ads/test_ai_suggestions_debug.html')