{% extends "base.html" %}
{% load static %}
{% load widget_tweaks %}

{% block title %}Settings - Enterprise QR{% endblock %}

{% block extra_css %}
<!-- Google Fonts - Poppins and Montserrat -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

<style>
    /* Ultra-Premium Enterprise Settings Styling */
    body {
        background: linear-gradient(135deg, #000000 0%, #0a0a0a 10%, #1a1a2e 25%, #16213e 40%, #0f3460 60%, #533483 80%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        position: relative;
        overflow-x: hidden;
    }

    /* Premium animated background with enterprise patterns */
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 20% 80%, rgba(102, 126, 234, 0.7) 0%, transparent 40%),
            radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.3) 0%, transparent 35%),
            radial-gradient(circle at 40% 60%, rgba(118, 75, 162, 0.6) 0%, transparent 45%),
            radial-gradient(circle at 60% 40%, rgba(83, 52, 131, 0.5) 0%, transparent 30%),
            radial-gradient(circle at 50% 50%, rgba(102, 126, 234, 0.4) 0%, transparent 40%);
        z-index: -1;
        animation: enterpriseSettingsFloat 55s ease-in-out infinite;
    }

    @keyframes enterpriseSettingsFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        25% { transform: translateY(-30px) rotate(2deg); }
        50% { transform: translateY(-20px) rotate(-2deg); }
        75% { transform: translateY(-40px) rotate(1deg); }
    }

    /* Enterprise Container */
    .enterprise-container {
        position: relative;
        z-index: 1;
        padding: 2rem 0;
        min-height: 100vh;
    }

    /* Premium Header Section */
    .enterprise-header {
        background: linear-gradient(135deg, rgba(26, 35, 126, 0.95) 0%, rgba(40, 53, 147, 0.95) 50%, rgba(63, 81, 181, 0.95) 100%);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2.5rem;
        margin-bottom: 2rem;
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        position: relative;
        overflow: hidden;
        animation: slideInDown 0.8s ease-out;
    }

    .enterprise-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
        animation: shimmer 3s ease-in-out infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    .enterprise-title {
        font-family: 'Montserrat', sans-serif !important;
        font-size: 2.5rem;
        font-weight: 700;
        color: #ffffff;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 2;
    }

    .enterprise-subtitle {
        font-size: 1.1rem;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 0;
        font-weight: 400;
        position: relative;
        z-index: 2;
    }

    .enterprise-icon {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        width: 60px;
        height: 60px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.8rem;
        color: white;
        margin-right: 1.5rem;
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        position: relative;
        z-index: 2;
    }

    /* Premium Settings Sidebar */
    .enterprise-sidebar {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        overflow: hidden;
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.15),
            0 0 0 1px rgba(255, 255, 255, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        animation: slideInLeft 0.8s ease-out 0.2s both;
        margin-bottom: 2rem;
    }

    .enterprise-sidebar .nav-pills {
        padding: 1rem;
    }

    .enterprise-sidebar .nav-pills .nav-link {
        color: #1a237e !important;
        font-weight: 600;
        padding: 1rem 1.5rem;
        margin-bottom: 0.5rem;
        border-radius: 12px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        border: 2px solid transparent;
    }

    .enterprise-sidebar .nav-pills .nav-link::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
        transition: left 0.5s;
    }

    .enterprise-sidebar .nav-pills .nav-link:hover::before {
        left: 100%;
    }

    .enterprise-sidebar .nav-pills .nav-link:hover {
        color: #667eea !important;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        border-color: rgba(102, 126, 234, 0.2);
        transform: translateX(5px);
    }

    .enterprise-sidebar .nav-pills .nav-link.active {
        color: #ffffff !important;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-color: #667eea;
        transform: translateX(5px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }

    .enterprise-sidebar .nav-pills .nav-link i {
        color: inherit;
        margin-right: 0.75rem;
        font-size: 1.1rem;
    }

    /* Premium Content Area */
    .enterprise-content {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        overflow: hidden;
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.15),
            0 0 0 1px rgba(255, 255, 255, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        animation: slideInRight 0.8s ease-out 0.4s both;
        min-height: 600px;
    }

    .enterprise-content-header {
        background: linear-gradient(135deg, #1a237e 0%, #283593 100%);
        color: white;
        padding: 1.5rem 2rem;
        font-weight: 600;
        font-size: 1.2rem;
        position: relative;
    }

    .enterprise-content-header::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    }

    .enterprise-content-body {
        padding: 2rem;
    }

    /* Premium Form Styling */
    .enterprise-form .form-label {
        font-weight: 600;
        color: #1a237e;
        margin-bottom: 0.75rem;
        font-size: 1rem;
    }

    .enterprise-form .form-control,
    .enterprise-form .form-select {
        border: 2px solid rgba(102, 126, 234, 0.2);
        border-radius: 12px;
        padding: 0.875rem 1.25rem;
        font-weight: 500;
        background: rgba(255, 255, 255, 0.9);
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        font-size: 1rem;
    }

    .enterprise-form .form-control:focus,
    .enterprise-form .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 8px 25px rgba(0, 0, 0, 0.1);
        outline: none;
        background: rgba(255, 255, 255, 1);
    }

    .enterprise-form .form-text {
        color: #667eea;
        font-weight: 500;
        font-size: 0.9rem;
        margin-top: 0.5rem;
    }

    /* Premium Buttons */
    .enterprise-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 12px;
        padding: 0.875rem 2rem;
        color: white;
        font-weight: 600;
        font-size: 1rem;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        position: relative;
        overflow: hidden;
        cursor: pointer;
    }

    .enterprise-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .enterprise-btn:hover::before {
        left: 100%;
    }

    .enterprise-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    .enterprise-btn-secondary {
        background: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%);
        box-shadow: 0 8px 25px rgba(107, 114, 128, 0.3);
    }

    .enterprise-btn-secondary:hover {
        box-shadow: 0 12px 35px rgba(107, 114, 128, 0.4);
    }

    /* Premium Alert Styling */
    .enterprise-alert {
        border-radius: 16px;
        border: none;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
        font-weight: 500;
    }

    .enterprise-alert.alert-success {
        background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(255, 255, 255, 0.9) 100%);
        border: 2px solid rgba(16, 185, 129, 0.2);
        color: #065f46;
    }

    .enterprise-alert.alert-danger {
        background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(255, 255, 255, 0.9) 100%);
        border: 2px solid rgba(239, 68, 68, 0.2);
        color: #991b1b;
    }

    .enterprise-alert.alert-info {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(255, 255, 255, 0.9) 100%);
        border: 2px solid rgba(102, 126, 234, 0.2);
        color: #1e3a8a;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .enterprise-title {
            font-size: 2rem;
        }

        .enterprise-header {
            padding: 2rem 1.5rem;
        }

        .enterprise-sidebar,
        .enterprise-content {
            margin-bottom: 1.5rem;
        }

        .enterprise-content-body {
            padding: 1.5rem;
        }

        .enterprise-form .form-control,
        .enterprise-form .form-select {
            padding: 0.75rem 1rem;
        }

        .enterprise-btn {
            padding: 0.75rem 1.5rem;
            font-size: 0.95rem;
        }
    }

    @media (max-width: 576px) {
        .enterprise-title {
            font-size: 1.8rem;
        }

        .enterprise-header {
            padding: 1.5rem 1rem;
        }

        .enterprise-sidebar,
        .enterprise-content {
            border-radius: 15px;
        }

        .enterprise-content-header {
            padding: 1rem 1.5rem;
            font-size: 1.1rem;
        }

        .enterprise-content-body {
            padding: 1rem;
        }

        .enterprise-sidebar .nav-pills {
            padding: 0.75rem;
        }

        .enterprise-sidebar .nav-pills .nav-link {
            padding: 0.875rem 1.25rem;
            font-size: 0.95rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="enterprise-container">
    <div class="container">
        <!-- Premium Header Section -->
        <div class="enterprise-header">
            <div class="d-flex align-items-center">
                <div class="enterprise-icon">
                    <i class="fas fa-cog"></i>
                </div>
                <div>
                    <h1 class="enterprise-title mb-0">Settings</h1>
                    <p class="enterprise-subtitle">Manage your account settings and preferences with enterprise-grade control</p>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Premium Settings Sidebar -->
            <div class="col-lg-3">
                <div class="enterprise-sidebar">
                    <div class="nav flex-column nav-pills" id="settings-tab" role="tablist" aria-orientation="vertical">
                        <button class="nav-link active" id="profile-tab" data-bs-toggle="pill" data-bs-target="#profile" type="button" role="tab" aria-controls="profile" aria-selected="true">
                            <i class="fas fa-user"></i>Profile
                        </button>
                        <button class="nav-link" id="account-tab" data-bs-toggle="pill" data-bs-target="#account" type="button" role="tab" aria-controls="account" aria-selected="false">
                            <i class="fas fa-user-shield"></i>Account
                        </button>
                        <button class="nav-link" id="appearance-tab" data-bs-toggle="pill" data-bs-target="#appearance" type="button" role="tab" aria-controls="appearance" aria-selected="false">
                            <i class="fas fa-paint-brush"></i>Appearance
                        </button>
                        <button class="nav-link" id="notifications-tab" data-bs-toggle="pill" data-bs-target="#notifications" type="button" role="tab" aria-controls="notifications" aria-selected="false">
                            <i class="fas fa-bell"></i>Notifications
                        </button>
                        <button class="nav-link" id="billing-tab" data-bs-toggle="pill" data-bs-target="#billing" type="button" role="tab" aria-controls="billing" aria-selected="false">
                            <i class="fas fa-credit-card"></i>Billing
                        </button>
                    </div>
                </div>
            </div>

            <!-- Premium Content Area -->
            <div class="col-lg-9">
                <div class="tab-content" id="settings-tabContent">
                <!-- Profile Tab -->
                <div class="tab-pane fade show active" id="profile" role="tabpanel" aria-labelledby="profile-tab">
                    <div class="enterprise-content">
                        <div class="enterprise-content-header">
                            <i class="fas fa-user me-2"></i>Profile Information
                        </div>
                        <div class="enterprise-content-body">
                            <form method="post" enctype="multipart/form-data" class="enterprise-form">
                                {% csrf_token %}

                                <div class="mb-4 text-center">
                                    <div class="profile-image-container">
                                        {% if form.instance.profile_image %}
                                            <img src="{{ form.instance.profile_image.url }}" alt="{{ user.username }}" class="profile-image">
                                        {% else %}
                                            <div class="profile-image-placeholder">
                                                <i class="fas fa-user"></i>
                                            </div>
                                        {% endif %}
                                    </div>
                                    <div class="mt-3">
                                        <label for="{{ form.profile_image.id_for_label }}" class="enterprise-btn enterprise-btn-secondary">
                                            <i class="fas fa-upload"></i>Change Photo
                                        </label>
                                        {{ form.profile_image|add_class:"d-none" }}
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="{{ form.first_name.id_for_label }}" class="form-label">First Name</label>
                                            {{ form.first_name|add_class:"form-control" }}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="{{ form.last_name.id_for_label }}" class="form-label">Last Name</label>
                                            {{ form.last_name|add_class:"form-control" }}
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="{{ form.email.id_for_label }}" class="form-label">Email Address</label>
                                    {{ form.email|add_class:"form-control" }}
                                </div>

                                <div class="mb-3">
                                    <label for="{{ form.company.id_for_label }}" class="form-label">Company</label>
                                    {{ form.company|add_class:"form-control" }}
                                </div>

                                <div class="mb-3">
                                    <label for="{{ form.phone.id_for_label }}" class="form-label">Phone Number</label>
                                    {{ form.phone|add_class:"form-control" }}
                                </div>

                                <div class="mb-3">
                                    <label for="{{ form.address.id_for_label }}" class="form-label">Address</label>
                                    {{ form.address|add_class:"form-control" }}
                                </div>

                                <div class="d-grid gap-2">
                                    <button type="submit" class="enterprise-btn">
                                        <i class="fas fa-save"></i>Save Changes
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Account Tab -->
                <div class="tab-pane fade" id="account" role="tabpanel" aria-labelledby="account-tab">
                    <div class="enterprise-content">
                        <div class="enterprise-content-header">
                            <i class="fas fa-user-shield me-2"></i>Account Settings
                        </div>
                        <div class="enterprise-content-body">
                            <h3 class="h6 mb-3" style="color: #1a237e; font-weight: 600;">Change Password</h3>
                            <form class="enterprise-form">
                                <div class="mb-3">
                                    <label for="currentPassword" class="form-label">Current Password</label>
                                    <input type="password" class="form-control" id="currentPassword">
                                </div>
                                <div class="mb-3">
                                    <label for="newPassword" class="form-label">New Password</label>
                                    <input type="password" class="form-control" id="newPassword">
                                </div>
                                <div class="mb-3">
                                    <label for="confirmPassword" class="form-label">Confirm New Password</label>
                                    <input type="password" class="form-control" id="confirmPassword">
                                </div>
                                <button type="submit" class="enterprise-btn">Update Password</button>
                            </form>

                            <hr style="border-color: rgba(102, 126, 234, 0.2); margin: 2rem 0;">

                            <h3 class="h6 mb-3" style="color: #1a237e; font-weight: 600;">Two-Factor Authentication</h3>
                            <p style="color: #667eea; font-weight: 500;">Enhance your account security by enabling two-factor authentication.</p>
                            <button class="enterprise-btn enterprise-btn-secondary">Enable 2FA</button>

                            <hr style="border-color: rgba(102, 126, 234, 0.2); margin: 2rem 0;">

                            <h3 class="h6 mb-3" style="color: #dc2626; font-weight: 600;">Danger Zone</h3>
                            <p style="color: #991b1b; font-weight: 500;">Once you delete your account, there is no going back. Please be certain.</p>
                            <button class="enterprise-btn" style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);">Delete Account</button>
                        </div>
                    </div>
                </div>

                <!-- Appearance Tab -->
                <div class="tab-pane fade" id="appearance" role="tabpanel" aria-labelledby="appearance-tab">
                    <div class="enterprise-content">
                        <div class="enterprise-content-header">
                            <i class="fas fa-paint-brush me-2"></i>Appearance Settings
                        </div>
                        <div class="enterprise-content-body">
                            <div class="enterprise-alert alert-info">
                                <i class="fas fa-crown me-2"></i>
                                <strong>Premium Feature:</strong> Appearance settings are available for premium users.
                            </div>
                            <a href="#" class="enterprise-btn">Upgrade to Premium</a>
                        </div>
                    </div>
                </div>

                <!-- Notifications Tab -->
                <div class="tab-pane fade" id="notifications" role="tabpanel" aria-labelledby="notifications-tab">
                    <div class="enterprise-content">
                        <div class="enterprise-content-header">
                            <i class="fas fa-bell me-2"></i>Notification Settings
                        </div>
                        <div class="enterprise-content-body">
                            <div class="enterprise-alert alert-info">
                                <i class="fas fa-crown me-2"></i>
                                <strong>Premium Feature:</strong> Notification settings are available for premium users.
                            </div>
                            <a href="#" class="enterprise-btn">Upgrade to Premium</a>
                        </div>
                    </div>
                </div>

                <!-- Billing Tab -->
                <div class="tab-pane fade" id="billing" role="tabpanel" aria-labelledby="billing-tab">
                    <div class="enterprise-content">
                        <div class="enterprise-content-header">
                            <i class="fas fa-credit-card me-2"></i>Billing Information
                        </div>
                        <div class="enterprise-content-body">
                            <div class="enterprise-alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <strong>Current Plan:</strong> You are currently on the Free plan.
                            </div>
                            <a href="#" class="enterprise-btn">Upgrade to Premium</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'qrcode_app/js/settings.js' %}"></script>
{% endblock %}
