from django.core.management.base import BaseCommand
from ads.models import AdLocation
from decimal import Decimal

class Command(BaseCommand):
    help = 'Creates initial ad locations with different price multipliers'

    def handle(self, *args, **kwargs):
        # Define the initial ad locations
        locations = [
            {
                'name': 'Premium Header',
                'description': 'Highly visible placement directly under the main navigation bar. This premium location attracts the most attention and has the highest visibility across all pages.',
                'price_multiplier': Decimal('2.0'),
                'visibility': 'high',
                'is_premium': True,
                'daily_impressions': 5000,
            },
            {
                'name': 'Sidebar Top',
                'description': 'Prominent placement at the top of the sidebar. Visible on most pages and attracts good attention.',
                'price_multiplier': Decimal('1.5'),
                'visibility': 'high',
                'is_premium': True,
                'daily_impressions': 3500,
            },
            {
                'name': 'Homepage Featured',
                'description': 'Featured placement on the homepage in a dedicated section. Great for brand awareness campaigns.',
                'price_multiplier': Decimal('1.75'),
                'visibility': 'high',
                'is_premium': True,
                'daily_impressions': 4000,
            },
            {
                'name': 'Content Inline',
                'description': 'Placement within content sections on various pages. Good for contextual advertising.',
                'price_multiplier': Decimal('1.25'),
                'visibility': 'medium',
                'is_premium': False,
                'daily_impressions': 2500,
            },
            {
                'name': 'Sidebar Middle',
                'description': 'Placement in the middle of the sidebar. Moderate visibility on most pages.',
                'price_multiplier': Decimal('1.0'),
                'visibility': 'medium',
                'is_premium': False,
                'daily_impressions': 2000,
            },
            {
                'name': 'Footer Banner',
                'description': 'Banner placement in the footer section. Visible on all pages but with lower attention.',
                'price_multiplier': Decimal('0.75'),
                'visibility': 'low',
                'is_premium': False,
                'daily_impressions': 1500,
            },
            {
                'name': 'QR Code Results Page',
                'description': 'Placement on the QR code results page. Targeted visibility for users viewing generated QR codes.',
                'price_multiplier': Decimal('1.25'),
                'visibility': 'medium',
                'is_premium': False,
                'daily_impressions': 2200,
            },
            {
                'name': 'Profile Dashboard',
                'description': 'Placement on user profile dashboards. Targeted visibility for logged-in users.',
                'price_multiplier': Decimal('1.1'),
                'visibility': 'medium',
                'is_premium': False,
                'daily_impressions': 1800,
            },
        ]

        # Create the locations
        created_count = 0
        for location_data in locations:
            location, created = AdLocation.objects.get_or_create(
                name=location_data['name'],
                defaults=location_data
            )
            
            if created:
                created_count += 1
                self.stdout.write(self.style.SUCCESS(f'Created ad location: {location.name}'))
            else:
                self.stdout.write(f'Ad location already exists: {location.name}')
        
        self.stdout.write(self.style.SUCCESS(f'Successfully created {created_count} ad locations'))
