{% extends 'base.html' %}
{% load static %}

{% block title %}{% if is_create %}Create User{% else %}Edit User{% endif %} | User Management{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'users/css/user_management.css' %}">
{% endblock %}

{% block content %}
<div class="user-form-container">
    <div class="user-form-header">
        <div class="d-flex justify-content-between align-items-center">
            <h1 class="section-title">{% if is_create %}Create New User{% else %}Edit User{% endif %}</h1>
            <div class="user-form-actions">
                <a href="{% if is_create %}{% url 'users:user_list' %}{% else %}{% url 'users:user_detail' user_id=user_obj.id %}{% endif %}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-2"></i>{% if is_create %}Back to Users{% else %}Back to User Details{% endif %}
                </a>
            </div>
        </div>
    </div>

    <div class="user-form-content">
        <form method="post" enctype="multipart/form-data">
            {% csrf_token %}

            <div class="card mb-4">
                <div class="card-header">
                    <h4>Account Information</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="{{ user_form.username.id_for_label }}">Username</label>
                                {{ user_form.username }}
                                {% if user_form.username.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ user_form.username.errors }}
                                </div>
                                {% endif %}
                                {% if user_form.username.help_text %}
                                <small class="form-text text-muted">{{ user_form.username.help_text }}</small>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="{{ user_form.email.id_for_label }}">Email</label>
                                {{ user_form.email }}
                                {% if user_form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ user_form.email.errors }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="{{ user_form.first_name.id_for_label }}">First Name</label>
                                {{ user_form.first_name }}
                                {% if user_form.first_name.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ user_form.first_name.errors }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="{{ user_form.last_name.id_for_label }}">Last Name</label>
                                {{ user_form.last_name }}
                                {% if user_form.last_name.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ user_form.last_name.errors }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="{{ user_form.password.id_for_label }}">Password</label>
                                {{ user_form.password }}
                                {% if user_form.password.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ user_form.password.errors }}
                                </div>
                                {% endif %}
                                {% if user_form.password.help_text %}
                                <small class="form-text text-muted">{{ user_form.password.help_text }}</small>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="{{ user_form.confirm_password.id_for_label }}">Confirm Password</label>
                                {{ user_form.confirm_password }}
                                {% if user_form.confirm_password.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ user_form.confirm_password.errors }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h4>Profile Information</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="{{ profile_form.role.id_for_label }}">Role</label>
                                {{ profile_form.role }}
                                {% if profile_form.role.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ profile_form.role.errors }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="{{ profile_form.phone.id_for_label }}">Phone Number</label>
                                {{ profile_form.phone }}
                                {% if profile_form.phone.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ profile_form.phone.errors }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="{{ profile_form.company.id_for_label }}">Company</label>
                                {{ profile_form.company }}
                                {% if profile_form.company.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ profile_form.company.errors }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="{{ profile_form.job_title.id_for_label }}">Job Title</label>
                                {{ profile_form.job_title }}
                                {% if profile_form.job_title.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ profile_form.job_title.errors }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group mb-3">
                                <label for="{{ profile_form.address.id_for_label }}">Address</label>
                                {{ profile_form.address }}
                                {% if profile_form.address.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ profile_form.address.errors }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group mb-3">
                                <label for="{{ profile_form.bio.id_for_label }}">Bio</label>
                                {{ profile_form.bio }}
                                {% if profile_form.bio.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ profile_form.bio.errors }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group mb-3">
                                <label for="{{ profile_form.profile_image.id_for_label }}">Profile Image</label>
                                {{ profile_form.profile_image }}
                                {% if profile_form.profile_image.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ profile_form.profile_image.errors }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h4>Permissions</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-check mb-3">
                                <input type="checkbox" class="form-check-input" id="{{ user_form.is_active.id_for_label }}" name="{{ user_form.is_active.html_name }}" {% if user_form.is_active.value %}checked{% endif %}>
                                <label class="form-check-label" for="{{ user_form.is_active.id_for_label }}">Active</label>
                                {% if user_form.is_active.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ user_form.is_active.errors }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check mb-3">
                                <input type="checkbox" class="form-check-input" id="{{ user_form.is_staff.id_for_label }}" name="{{ user_form.is_staff.html_name }}" {% if user_form.is_staff.value %}checked{% endif %}>
                                <label class="form-check-label" for="{{ user_form.is_staff.id_for_label }}">Staff</label>
                                {% if user_form.is_staff.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ user_form.is_staff.errors }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check mb-3">
                                <input type="checkbox" class="form-check-input" id="{{ user_form.is_superuser.id_for_label }}" name="{{ user_form.is_superuser.html_name }}" {% if user_form.is_superuser.value %}checked{% endif %}>
                                <label class="form-check-label" for="{{ user_form.is_superuser.id_for_label }}">Superuser</label>
                                {% if user_form.is_superuser.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ user_form.is_superuser.errors }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group mb-3">
                                <label for="{{ user_form.groups.id_for_label }}">Groups</label>
                                {{ user_form.groups }}
                                {% if user_form.groups.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ user_form.groups.errors }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-actions">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>{% if is_create %}Create User{% else %}Update User{% endif %}
                </button>
                <a href="{% if is_create %}{% url 'users:user_list' %}{% else %}{% url 'users:user_detail' user_id=user_obj.id %}{% endif %}" class="btn btn-secondary">
                    <i class="fas fa-times me-2"></i>Cancel
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'users/js/user_management.js' %}"></script>
{% endblock %}
