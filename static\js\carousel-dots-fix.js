/**
 * Carousel Dots Fix
 * This script ensures the carousel dots maintain their proper size
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Carousel dots fix script loaded');

    // Fix carousel dots on initial load
    fixCarouselDots();

    // Fix carousel dots after a short delay (for any dynamic content)
    setTimeout(fixCarouselDots, 500);

    // Fix carousel dots after window load (when all resources are loaded)
    window.addEventListener('load', fixCarouselDots);

    // Fix carousel dots on window resize
    window.addEventListener('resize', fixCarouselDots);

    // Create a MutationObserver to watch for DOM changes
    const observer = new MutationObserver(function(mutations) {
        // Fix carousel dots when DOM changes
        fixCarouselDots();
    });

    // Start observing the document with the configured parameters
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    /**
     * Fix carousel dots size and appearance
     */
    function fixCarouselDots() {
        // Get all premium carousel indicators
        const indicators = document.querySelectorAll('.premium-ad-indicator');

        if (indicators.length > 0) {
            console.log(`Fixing ${indicators.length} carousel dots`);

            // Apply fixed size to all indicators
            indicators.forEach(function(dot) {
                // Force consistent size
                dot.style.width = '6px';
                dot.style.height = '6px';
                dot.style.minWidth = '6px';
                dot.style.minHeight = '6px';
                dot.style.maxWidth = '6px';
                dot.style.maxHeight = '6px';

                // Ensure proper styling
                dot.style.borderRadius = '50%';
                dot.style.padding = '0';
                dot.style.margin = '0 3px';

                // Apply proper transition
                dot.style.transition = 'all 0.3s ease';

                // Fix any transform issues
                if (dot.classList.contains('active')) {
                    dot.style.transform = 'scale(1.2)';
                } else {
                    dot.style.transform = 'scale(1)';
                }
            });

            // Apply responsive sizing based on screen width
            if (window.innerWidth <= 576) {
                indicators.forEach(function(dot) {
                    dot.style.width = '4px';
                    dot.style.height = '4px';
                    dot.style.minWidth = '4px';
                    dot.style.minHeight = '4px';
                    dot.style.maxWidth = '4px';
                    dot.style.maxHeight = '4px';
                    dot.style.margin = '0 1px';

                    if (dot.classList.contains('active')) {
                        dot.style.transform = 'scale(1.1)';
                    }
                });

                // Fix indicator container position on small screens
                const indicatorContainers = document.querySelectorAll('.premium-ad-carousel-indicators');
                indicatorContainers.forEach(function(container) {
                    container.style.bottom = '35px';
                    container.style.right = '10px';
                    container.style.left = 'auto';
                    container.style.transform = 'none';
                    container.style.zIndex = '20';
                    container.style.background = 'rgba(255,255,255,0.6)';
                    container.style.borderRadius = '10px';
                });

                // Ensure CTA button is not overlapped
                const ctaButtons = document.querySelectorAll('.premium-ad-cta');
                ctaButtons.forEach(function(btn) {
                    btn.style.marginBottom = '5px';
                    btn.style.position = 'relative';
                    btn.style.zIndex = '15';
                });
            } else if (window.innerWidth <= 768) {
                indicators.forEach(function(dot) {
                    dot.style.width = '5px';
                    dot.style.height = '5px';
                    dot.style.minWidth = '5px';
                    dot.style.minHeight = '5px';
                    dot.style.maxWidth = '5px';
                    dot.style.maxHeight = '5px';
                    dot.style.margin = '0 2px';
                });

                // Adjust CTA button on medium screens
                const ctaButtons = document.querySelectorAll('.premium-ad-cta');
                ctaButtons.forEach(function(btn) {
                    btn.style.marginBottom = '25px';
                });
            }

            // Fix indicator container
            const indicatorContainers = document.querySelectorAll('.premium-ad-carousel-indicators');
            indicatorContainers.forEach(function(container) {
                container.style.display = 'flex';
                container.style.justifyContent = 'center';
                container.style.alignItems = 'center';
                container.style.gap = window.innerWidth <= 576 ? '3px' : '6px';
                container.style.padding = window.innerWidth <= 576 ? '2px 5px' : '3px 8px';
                container.style.maxWidth = '80%';
                container.style.height = 'auto';
                container.style.width = 'auto';
            });
        }
    }
});
