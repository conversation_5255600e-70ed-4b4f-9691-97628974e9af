# Generated by Django 5.1.7 on 2025-05-29 07:52

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('qrcode_app', '0015_add_webhook_endpoint'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Plan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True)),
                ('plan_type', models.CharField(choices=[('FREE', 'Free'), ('PRO', 'Pro'), ('ENTERPRISE', 'Enterprise')], default='FREE', max_length=20)),
                ('description', models.TextField(blank=True, help_text='Plan description for users')),
                ('max_qr_codes', models.IntegerField(default=5, help_text='Maximum QR codes user can create')),
                ('max_scans_per_month', models.IntegerField(default=100, help_text='Maximum scans per month')),
                ('max_dynamic_redirects', models.IntegerField(default=0, help_text='Maximum dynamic redirects')),
                ('max_ai_pages', models.IntegerField(default=0, help_text='Maximum AI landing pages')),
                ('max_webhooks', models.IntegerField(default=0, help_text='Maximum webhook endpoints')),
                ('max_scan_alerts', models.IntegerField(default=0, help_text='Maximum scan alerts')),
                ('ai_enabled', models.BooleanField(default=False, help_text='Enable AI landing pages')),
                ('alerts_enabled', models.BooleanField(default=False, help_text='Enable scan alerts')),
                ('webhooks_enabled', models.BooleanField(default=False, help_text='Enable webhook integration')),
                ('analytics_enabled', models.BooleanField(default=True, help_text='Enable basic analytics')),
                ('advanced_analytics_enabled', models.BooleanField(default=False, help_text='Enable advanced analytics')),
                ('branding_enabled', models.BooleanField(default=False, help_text='Enable custom branding')),
                ('batch_processing_enabled', models.BooleanField(default=False, help_text='Enable batch QR generation')),
                ('api_access_enabled', models.BooleanField(default=False, help_text='Enable API access')),
                ('priority_support_enabled', models.BooleanField(default=False, help_text='Enable priority support')),
                ('price', models.DecimalField(decimal_places=2, default=0.0, help_text='Monthly price in USD', max_digits=8)),
                ('yearly_price', models.DecimalField(decimal_places=2, default=0.0, help_text='Yearly price in USD', max_digits=8)),
                ('is_active', models.BooleanField(default=True, help_text='Is this plan available for subscription')),
                ('is_default', models.BooleanField(default=False, help_text='Default plan for new users')),
                ('sort_order', models.PositiveIntegerField(default=0, help_text='Display order on pricing page')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Subscription Plan',
                'verbose_name_plural': 'Subscription Plans',
                'ordering': ['sort_order', 'price'],
            },
        ),
        migrations.CreateModel(
            name='Subscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('ACTIVE', 'Active'), ('CANCELLED', 'Cancelled'), ('EXPIRED', 'Expired'), ('SUSPENDED', 'Suspended')], default='ACTIVE', max_length=20)),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('expires_at', models.DateTimeField(blank=True, help_text='When subscription expires', null=True)),
                ('cancelled_at', models.DateTimeField(blank=True, null=True)),
                ('scans_this_month', models.IntegerField(default=0, help_text='Number of scans this month')),
                ('last_scan_reset', models.DateField(auto_now_add=True, help_text='Last time scan count was reset')),
                ('total_qr_codes_created', models.IntegerField(default=0, help_text='Total QR codes created')),
                ('total_dynamic_redirects', models.IntegerField(default=0, help_text='Total dynamic redirects created')),
                ('total_ai_pages', models.IntegerField(default=0, help_text='Total AI pages created')),
                ('total_webhooks', models.IntegerField(default=0, help_text='Total webhooks created')),
                ('total_scan_alerts', models.IntegerField(default=0, help_text='Total scan alerts created')),
                ('stripe_subscription_id', models.CharField(blank=True, help_text='Stripe subscription ID', max_length=255)),
                ('stripe_customer_id', models.CharField(blank=True, help_text='Stripe customer ID', max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('plan', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='qrcode_app.plan')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='subscription', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'User Subscription',
                'verbose_name_plural': 'User Subscriptions',
                'ordering': ['-created_at'],
            },
        ),
    ]
