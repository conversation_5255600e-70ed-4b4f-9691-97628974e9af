/**
 * device-testing.js
 * 
 * Utility for detecting device information and applying device-specific fixes
 * to ensure consistent experience across all devices.
 */

(function() {
    // Wait for DOM to be ready
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize device detection
        initDeviceDetection();
    });

    /**
     * Initialize device detection and apply fixes
     */
    function initDeviceDetection() {
        // Get device information
        const deviceInfo = getDeviceInfo();
        
        // Log device information for debugging
        console.log('Device Info:', deviceInfo);
        
        // Apply device-specific fixes
        applyDeviceFixes(deviceInfo);
        
        // Add device info class to body for CSS targeting
        addDeviceClasses(deviceInfo);
    }

    /**
     * Get detailed device information
     * @returns {Object} Device information
     */
    function getDeviceInfo() {
        const ua = navigator.userAgent;
        const platform = navigator.platform;
        const deviceInfo = {
            // Device type
            isMobile: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(ua),
            isTablet: /iPad|Android(?!.*Mobile)/i.test(ua),
            isDesktop: !(/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(ua)),
            
            // Operating system
            isIOS: /iPhone|iPad|iPod/i.test(ua),
            isAndroid: /Android/i.test(ua),
            isWindows: /Windows/i.test(ua),
            isMac: /Macintosh/i.test(ua),
            
            // Browser
            isChrome: /Chrome/i.test(ua) && !/Edge/i.test(ua),
            isSafari: /Safari/i.test(ua) && !/Chrome/i.test(ua),
            isFirefox: /Firefox/i.test(ua),
            isEdge: /Edge/i.test(ua),
            isIE: /Trident/i.test(ua),
            
            // Viewport
            viewportWidth: window.innerWidth,
            viewportHeight: window.innerHeight,
            devicePixelRatio: window.devicePixelRatio || 1,
            
            // Touch support
            hasTouch: 'ontouchstart' in window || navigator.maxTouchPoints > 0,
            
            // User agent
            userAgent: ua
        };
        
        return deviceInfo;
    }

    /**
     * Apply device-specific fixes
     * @param {Object} deviceInfo Device information
     */
    function applyDeviceFixes(deviceInfo) {
        // iOS specific fixes
        if (deviceInfo.isIOS) {
            // Fix for iOS 100vh issue
            fixIOSViewportHeight();
            
            // Fix for iOS hover state
            fixIOSHoverState();
            
            // Fix for iOS input zoom
            fixIOSInputZoom();
        }
        
        // Android specific fixes
        if (deviceInfo.isAndroid) {
            // Fix for Android keyboard issues
            fixAndroidKeyboard();
        }
        
        // Mobile specific fixes (both iOS and Android)
        if (deviceInfo.isMobile) {
            // Fix for mobile menu scrolling
            fixMobileMenuScrolling();
            
            // Fix for mobile fixed positioning
            fixMobileFixedPositioning();
        }
    }

    /**
     * Add device-specific classes to body for CSS targeting
     * @param {Object} deviceInfo Device information
     */
    function addDeviceClasses(deviceInfo) {
        const body = document.body;
        
        // Add device type classes
        if (deviceInfo.isMobile) body.classList.add('is-mobile');
        if (deviceInfo.isTablet) body.classList.add('is-tablet');
        if (deviceInfo.isDesktop) body.classList.add('is-desktop');
        
        // Add OS classes
        if (deviceInfo.isIOS) body.classList.add('is-ios');
        if (deviceInfo.isAndroid) body.classList.add('is-android');
        if (deviceInfo.isWindows) body.classList.add('is-windows');
        if (deviceInfo.isMac) body.classList.add('is-mac');
        
        // Add browser classes
        if (deviceInfo.isChrome) body.classList.add('is-chrome');
        if (deviceInfo.isSafari) body.classList.add('is-safari');
        if (deviceInfo.isFirefox) body.classList.add('is-firefox');
        if (deviceInfo.isEdge) body.classList.add('is-edge');
        if (deviceInfo.isIE) body.classList.add('is-ie');
        
        // Add touch class
        if (deviceInfo.hasTouch) body.classList.add('has-touch');
    }

    /**
     * Fix for iOS 100vh issue
     * iOS Safari includes the address bar in 100vh calculations
     */
    function fixIOSViewportHeight() {
        // Set CSS variable for real viewport height
        const setViewportHeight = () => {
            const vh = window.innerHeight * 0.01;
            document.documentElement.style.setProperty('--vh', `${vh}px`);
        };
        
        // Set on initial load
        setViewportHeight();
        
        // Update on resize and orientation change
        window.addEventListener('resize', setViewportHeight);
        window.addEventListener('orientationchange', setViewportHeight);
    }

    /**
     * Fix for iOS hover state
     * iOS has issues with hover states
     */
    function fixIOSHoverState() {
        // Add empty touchstart listener to enable :active states
        document.addEventListener('touchstart', function() {}, {passive: true});
    }

    /**
     * Fix for iOS input zoom
     * iOS zooms in on inputs with font-size < 16px
     */
    function fixIOSInputZoom() {
        // Add meta viewport tag to prevent zooming
        const viewportMeta = document.querySelector('meta[name="viewport"]');
        if (viewportMeta) {
            viewportMeta.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
        }
    }

    /**
     * Fix for Android keyboard issues
     * Android has issues with fixed positioning when keyboard is open
     */
    function fixAndroidKeyboard() {
        const inputs = document.querySelectorAll('input, textarea, select');
        
        inputs.forEach(input => {
            input.addEventListener('focus', () => {
                document.body.classList.add('keyboard-open');
            });
            
            input.addEventListener('blur', () => {
                document.body.classList.remove('keyboard-open');
            });
        });
    }

    /**
     * Fix for mobile menu scrolling
     * Prevent body scrolling when mobile menu is open
     */
    function fixMobileMenuScrolling() {
        // Find mobile menu elements
        const mobileMenuTriggers = document.querySelectorAll('.mobile-menu-trigger, .new-navbar-toggle');
        
        mobileMenuTriggers.forEach(trigger => {
            trigger.addEventListener('click', () => {
                // Toggle body scroll lock class
                document.body.classList.toggle('menu-open');
            });
        });
    }

    /**
     * Fix for mobile fixed positioning
     * Mobile browsers have issues with fixed positioning
     */
    function fixMobileFixedPositioning() {
        // Add padding to body to account for bottom navigation
        const bottomNav = document.querySelector('.enterprise-bottom-nav');
        if (bottomNav) {
            const bottomNavHeight = bottomNav.offsetHeight;
            document.body.style.paddingBottom = `${bottomNavHeight}px`;
        }
    }
})();
