from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib.auth.models import User, Group
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q
from .forms import UserForm, UserProfileForm
from qrcode_app.models import UserProfile

def is_admin(user):
    """Check if user is an admin or superuser"""
    return user.is_superuser or (hasattr(user, 'profile') and user.profile.role in ['admin', 'superadmin'])

@login_required
@user_passes_test(is_admin)
def user_list(request):
    """
    Display a list of all users with filtering and search capabilities
    """
    # Get all users
    users = User.objects.all().order_by('-date_joined')

    # Filter by role if provided
    role = request.GET.get('role')
    if role:
        users = users.filter(profile__role=role)

    # Filter by status if provided
    status = request.GET.get('status')
    if status == 'active':
        users = users.filter(is_active=True)
    elif status == 'inactive':
        users = users.filter(is_active=False)

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        users = users.filter(
            Q(username__icontains=search_query) |
            Q(email__icontains=search_query) |
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query)
        )

    # Paginate the results
    paginator = Paginator(users, 10)  # Show 10 users per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get available roles for filtering
    roles = UserProfile.UserRole.choices

    context = {
        'users': page_obj,
        'page_obj': page_obj,
        'roles': roles,
        'current_role': role,
        'current_status': status,
        'search_query': search_query,
        'total_users': users.count(),
    }

    return render(request, 'users/user_list.html', context)

@login_required
@user_passes_test(is_admin)
def user_detail(request, user_id):
    """
    Display detailed information about a specific user
    """
    user = get_object_or_404(User, id=user_id)

    # Get user's profile
    profile, created = UserProfile.objects.get_or_create(user=user)

    # Get user's groups
    groups = user.groups.all()

    context = {
        'user_obj': user,
        'profile': profile,
        'groups': groups,
    }

    return render(request, 'users/user_detail.html', context)

@login_required
@user_passes_test(is_admin)
def user_create(request):
    """
    Create a new user
    """
    if request.method == 'POST':
        user_form = UserForm(request.POST)
        profile_form = UserProfileForm(request.POST)

        if user_form.is_valid() and profile_form.is_valid():
            # Create user
            user = user_form.save()

            # Create profile
            profile = profile_form.save(commit=False)
            profile.user = user
            profile.save()

            messages.success(request, f"User '{user.username}' created successfully.")
            return redirect('users:user_detail', user_id=user.id)
    else:
        user_form = UserForm()
        profile_form = UserProfileForm()

    context = {
        'user_form': user_form,
        'profile_form': profile_form,
        'is_create': True,
    }

    return render(request, 'users/user_form.html', context)

@login_required
@user_passes_test(is_admin)
def user_edit(request, user_id):
    """
    Edit an existing user
    """
    user = get_object_or_404(User, id=user_id)
    profile, created = UserProfile.objects.get_or_create(user=user)

    if request.method == 'POST':
        user_form = UserForm(request.POST, instance=user)
        profile_form = UserProfileForm(request.POST, instance=profile)

        if user_form.is_valid() and profile_form.is_valid():
            # Update user
            user = user_form.save()

            # Update profile
            profile = profile_form.save()

            messages.success(request, f"User '{user.username}' updated successfully.")
            return redirect('users:user_detail', user_id=user.id)
    else:
        user_form = UserForm(instance=user)
        profile_form = UserProfileForm(instance=profile)

    context = {
        'user_form': user_form,
        'profile_form': profile_form,
        'is_create': False,
        'user_obj': user,
    }

    return render(request, 'users/user_form.html', context)

@login_required
@user_passes_test(is_admin)
def user_delete(request, user_id):
    """
    Delete a user
    """
    user = get_object_or_404(User, id=user_id)

    # Prevent deleting yourself
    if user == request.user:
        messages.error(request, "You cannot delete your own account.")
        return redirect('users:user_list')

    if request.method == 'POST':
        username = user.username
        user.delete()
        messages.success(request, f"User '{username}' deleted successfully.")
        return redirect('users:user_list')

    context = {
        'user_obj': user,
    }

    return render(request, 'users/user_delete.html', context)

@login_required
@user_passes_test(is_admin)
def user_toggle_active(request, user_id):
    """
    Toggle user active status
    """
    user = get_object_or_404(User, id=user_id)

    # Prevent deactivating yourself
    if user == request.user:
        messages.error(request, "You cannot change your own active status.")
        return redirect('users:user_detail', user_id=user.id)

    # Toggle active status
    user.is_active = not user.is_active
    user.save()

    status = "activated" if user.is_active else "deactivated"
    messages.success(request, f"User '{user.username}' {status} successfully.")

    return redirect('users:user_detail', user_id=user.id)
