"""
URL configuration for qrcode_project project.
"""

from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.views.generic import TemplateView

# Import custom admin sites
from qrcode_project.admin_sites import qr_admin_site, ads_admin_site, ai_admin_site, billing_admin_site

urlpatterns = [
    # Main admin (all modules)
    path('admin/', admin.site.urls),

    # Specialized admin sections
    path('admin/qr/', qr_admin_site.urls),
    path('admin/ads/', ads_admin_site.urls),
    path('admin/ai/', ai_admin_site.urls),
    path('admin/billing/', billing_admin_site.urls),

    # Application URLs
    path('accounts/profile/', TemplateView.as_view(template_name='account/profile.html'), name='account_profile'),
    path('accounts/', include('allauth.urls')),
    path('api/', include('api.urls')),
    path('ads/', include('ads.urls')),
    path('users/', include('users.urls')),
    path('notifications/', include('notifications.urls')),
    path('ai/', include('ai_services.urls')),
    path('billing/', include('qrcode_app.billing_urls')),
    path('', include('qrcode_app.urls')),
]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
