"""
URL configuration for qrcode_project project.
"""

from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.views.generic import TemplateView

urlpatterns = [
    path('admin/', admin.site.urls),
    path('accounts/profile/', TemplateView.as_view(template_name='account/profile.html'), name='account_profile'),
    path('accounts/', include('allauth.urls')),
    path('api/', include('api.urls')),
    path('ads/', include('ads.urls')),
    path('users/', include('users.urls')),
    path('notifications/', include('notifications.urls')),
    path('ai/', include('ai_services.urls')),
    path('billing/', include('qrcode_app.billing_urls')),
    path('', include('qrcode_app.urls')),
]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
