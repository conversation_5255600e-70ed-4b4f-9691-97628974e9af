{% extends 'base.html' %}
{% load static %}

{% block title %}Login{% endblock %}

{% block extra_css %}
<style>
.login-container {
    padding: 4rem 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.login-card {
    background: white;
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    max-width: 500px;
    margin: 0 auto;
    width: 100%;
}

.login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.login-header h1 {
    color: #333;
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
}

.login-header p {
    color: #6c757d;
    font-size: 1.1rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
    display: block;
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    outline: none;
}

.btn-login {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    font-size: 1.1rem;
    width: 100%;
    transition: all 0.3s ease;
    cursor: pointer;
}

.btn-login:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
}

.test-accounts {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 2rem;
    margin-top: 2rem;
}

.test-accounts h5 {
    color: #333;
    font-weight: bold;
    margin-bottom: 1rem;
    text-align: center;
}

.account-option {
    background: white;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid #e9ecef;
    cursor: pointer;
    transition: all 0.3s ease;
}

.account-option:hover {
    border-color: #667eea;
    transform: translateY(-1px);
}

.account-option:last-child {
    margin-bottom: 0;
}

.account-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.account-details h6 {
    color: #333;
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.account-details small {
    color: #6c757d;
}

.account-badge {
    background: #e3f2fd;
    color: #1976d2;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.account-badge.admin {
    background: #f3e5f5;
    color: #7b1fa2;
}

.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 1rem;
    border-radius: 10px;
    margin-bottom: 1rem;
    text-align: center;
}

.success-message {
    background: #d4edda;
    color: #155724;
    padding: 1rem;
    border-radius: 10px;
    margin-bottom: 1rem;
    text-align: center;
}
</style>
{% endblock %}

{% block content %}
<div class="login-container">
    <div class="container">
        <div class="login-card">
            <div class="login-header">
                <h1>🔐 Login</h1>
                <p>Access your QR Generator account</p>
            </div>

            {% if form.errors %}
                <div class="error-message">
                    <strong>Login failed!</strong> Please check your username and password.
                </div>
            {% endif %}

            {% if messages %}
                {% for message in messages %}
                    <div class="{% if message.tags == 'error' %}error-message{% else %}success-message{% endif %}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}

            <form method="post">
                {% csrf_token %}
                
                <div class="form-group">
                    <label for="id_username" class="form-label">Username</label>
                    <input type="text" name="username" id="id_username" class="form-control" 
                           placeholder="Enter your username" required>
                </div>

                <div class="form-group">
                    <label for="id_password" class="form-label">Password</label>
                    <input type="password" name="password" id="id_password" class="form-control" 
                           placeholder="Enter your password" required>
                </div>

                <button type="submit" class="btn-login">
                    <i class="fas fa-sign-in-alt me-2"></i>Login
                </button>
            </form>

            <div class="test-accounts">
                <h5>🧪 Test Accounts</h5>
                <p class="text-center text-muted mb-3">Click to auto-fill credentials</p>
                
                <div class="account-option" onclick="fillCredentials('apollo', '2587')">
                    <div class="account-info">
                        <div class="account-details">
                            <h6>Apollo</h6>
                            <small>Username: apollo | Password: 2587</small>
                        </div>
                        <span class="account-badge">Free User</span>
                    </div>
                </div>

                <div class="account-option" onclick="fillCredentials('peter', '2587')">
                    <div class="account-info">
                        <div class="account-details">
                            <h6>Peter</h6>
                            <small>Username: peter | Password: 2587</small>
                        </div>
                        <span class="account-badge admin">Admin</span>
                    </div>
                </div>
            </div>

            <div class="text-center mt-3">
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    No registration required - use test accounts above
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function fillCredentials(username, password) {
    document.getElementById('id_username').value = username;
    document.getElementById('id_password').value = password;
    
    // Add visual feedback
    const usernameField = document.getElementById('id_username');
    const passwordField = document.getElementById('id_password');
    
    usernameField.style.borderColor = '#28a745';
    passwordField.style.borderColor = '#28a745';
    
    setTimeout(() => {
        usernameField.style.borderColor = '#667eea';
        passwordField.style.borderColor = '#667eea';
    }, 1000);
}

// Auto-focus username field
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('id_username').focus();
});
</script>
{% endblock %}
