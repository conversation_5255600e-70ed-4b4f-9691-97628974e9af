/**
 * Analytics Dashboard JavaScript
 * Handles sidebar menu functionality and tab switching for both user and admin views
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize sidebar menu
    initSidebarMenu();
    
    // Initialize tab content
    initTabContent();
    
    // Initialize date range picker if it exists
    initDateRangePicker();
    
    // Initialize charts
    initCharts();
    
    // Check for admin-specific functionality
    if (document.querySelector('[data-superuser="true"]')) {
        initAdminFunctionality();
    }
});

/**
 * Initialize sidebar menu functionality
 */
function initSidebarMenu() {
    const sidebarLinks = document.querySelectorAll('.sidebar-nav .nav-link');
    
    sidebarLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Only handle tab links (those with data-tab attribute)
            if (this.getAttribute('data-tab')) {
                e.preventDefault();
                
                // Remove active class from all links
                sidebarLinks.forEach(l => l.classList.remove('active'));
                
                // Add active class to clicked link
                this.classList.add('active');
                
                // Show the corresponding tab
                const tabId = this.getAttribute('data-tab');
                showTab(tabId);
                
                // Update URL hash
                window.location.hash = tabId;
            }
        });
    });
    
    // Check if there's a hash in the URL and show that tab
    if (window.location.hash) {
        const tabId = window.location.hash.substring(1);
        const tabLink = document.querySelector(`.sidebar-nav .nav-link[data-tab="${tabId}"]`);
        
        if (tabLink) {
            // Simulate a click on the tab link
            tabLink.click();
        }
    }
}

/**
 * Show the specified tab and hide others
 * @param {string} tabId - The ID of the tab to show
 */
function showTab(tabId) {
    // Hide all tabs
    const tabs = document.querySelectorAll('.tab-pane');
    tabs.forEach(tab => tab.classList.remove('active'));
    
    // Show the selected tab
    const selectedTab = document.getElementById(tabId);
    if (selectedTab) {
        selectedTab.classList.add('active');
        
        // Trigger resize event to fix chart rendering issues
        window.dispatchEvent(new Event('resize'));
    }
}

/**
 * Initialize tab content
 */
function initTabContent() {
    // Hide all tabs initially except the first one
    const tabs = document.querySelectorAll('.tab-pane');
    
    if (tabs.length > 0) {
        tabs.forEach((tab, index) => {
            if (index === 0) {
                tab.classList.add('active');
            } else {
                tab.classList.remove('active');
            }
        });
    }
}

/**
 * Initialize date range picker
 */
function initDateRangePicker() {
    const dateRangePicker = document.getElementById('dateRangePicker');
    
    if (dateRangePicker) {
        // Initialize date range picker with options
        $(dateRangePicker).daterangepicker({
            ranges: {
                'Today': [moment(), moment()],
                'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                'Last 7 Days': [moment().subtract(6, 'days'), moment()],
                'Last 30 Days': [moment().subtract(29, 'days'), moment()],
                'This Month': [moment().startOf('month'), moment().endOf('month')],
                'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
            },
            startDate: moment().subtract(29, 'days'),
            endDate: moment(),
            alwaysShowCalendars: true,
            opens: 'left'
        }, function(start, end, label) {
            // When date range changes, update the display and reload data
            document.getElementById('dateRangeText').textContent = start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY');
            
            // Reload data with new date range
            loadAnalyticsData(start.format('YYYY-MM-DD'), end.format('YYYY-MM-DD'));
        });
    }
}

/**
 * Load analytics data for the specified date range
 * @param {string} startDate - Start date in YYYY-MM-DD format
 * @param {string} endDate - End date in YYYY-MM-DD format
 */
function loadAnalyticsData(startDate, endDate) {
    // Show loading indicator
    const loadingIndicator = document.getElementById('loadingIndicator');
    if (loadingIndicator) {
        loadingIndicator.style.display = 'flex';
    }
    
    // Fetch data from the server
    fetch(`/ads/api/analytics/?start_date=${startDate}&end_date=${endDate}`)
        .then(response => response.json())
        .then(data => {
            // Update metrics
            updateMetrics(data);
            
            // Update charts
            updateCharts(data);
            
            // Hide loading indicator
            if (loadingIndicator) {
                loadingIndicator.style.display = 'none';
            }
        })
        .catch(error => {
            console.error('Error loading analytics data:', error);
            
            // Hide loading indicator
            if (loadingIndicator) {
                loadingIndicator.style.display = 'none';
            }
            
            // Show error message
            alert('Failed to load analytics data. Please try again later.');
        });
}

/**
 * Initialize admin-specific functionality
 */
function initAdminFunctionality() {
    // Add event listeners for admin-specific actions
    const approvalButtons = document.querySelectorAll('.approval-btn');
    
    approvalButtons.forEach(button => {
        button.addEventListener('click', function() {
            const adId = this.getAttribute('data-ad-id');
            const action = this.getAttribute('data-action');
            
            // Confirm action
            if (confirm(`Are you sure you want to ${action} this ad?`)) {
                // Send request to server
                fetch(`/ads/api/admin/ad/${adId}/${action}/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': getCsrfToken(),
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show success message
                        alert(`Ad ${action}ed successfully.`);
                        
                        // Reload page
                        window.location.reload();
                    } else {
                        // Show error message
                        alert(`Failed to ${action} ad: ${data.error}`);
                    }
                })
                .catch(error => {
                    console.error(`Error ${action}ing ad:`, error);
                    alert(`Failed to ${action} ad. Please try again later.`);
                });
            }
        });
    });
}

/**
 * Get CSRF token from cookies
 * @returns {string} CSRF token
 */
function getCsrfToken() {
    const name = 'csrftoken';
    let cookieValue = null;
    
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    
    return cookieValue;
}
