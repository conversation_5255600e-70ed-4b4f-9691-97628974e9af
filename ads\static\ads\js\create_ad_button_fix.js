// Create Ad Button Fix
document.addEventListener('DOMContentLoaded', function() {
    console.log('Create Ad Button Fix loaded');
    
    // Fix for Create Ad buttons in the dashboard
    const createAdButtons = document.querySelectorAll('.create-ad-btn, a[href*="ad_create"], a[href*="ad_create_enterprise"]');
    
    createAdButtons.forEach(button => {
        console.log('Found Create Ad button:', button);
        
        // Remove any existing click event listeners
        const newButton = button.cloneNode(true);
        button.parentNode.replaceChild(newButton, button);
        
        // Add new click event listener
        newButton.addEventListener('click', function(event) {
            console.log('Create Ad button clicked');
            
            // Get the href attribute
            const href = this.getAttribute('href');
            
            // If href exists, navigate to it
            if (href) {
                console.log('Navigating to:', href);
                window.location.href = href;
            }
        });
    });
    
    // Fix for empty state Create Ad buttons
    const emptyStateButtons = document.querySelectorAll('.empty-state a[href*="ad_create"], .empty-state a[href*="ad_create_enterprise"]');
    
    emptyStateButtons.forEach(button => {
        console.log('Found empty state Create Ad button:', button);
        
        // Remove any existing click event listeners
        const newButton = button.cloneNode(true);
        button.parentNode.replaceChild(newButton, button);
        
        // Add new click event listener
        newButton.addEventListener('click', function(event) {
            console.log('Empty state Create Ad button clicked');
            
            // Get the href attribute
            const href = this.getAttribute('href');
            
            // If href exists, navigate to it
            if (href) {
                console.log('Navigating to:', href);
                window.location.href = href;
            }
        });
    });
});
