-- Check if the migration 0002_aifeedback exists but is not applied
INSERT INTO django_migrations (app, name, applied)
SELECT 'ads', '0002_aifeedback', datetime('now')
WHERE NOT EXISTS (
    SELECT 1 FROM django_migrations WHERE app = 'ads' AND name = '0002_aifeedback'
);

-- Check if the migration 0011_aifeedback exists but is not applied
INSERT INTO django_migrations (app, name, applied)
SELECT 'ads', '0011_aifeedback', datetime('now')
WHERE NOT EXISTS (
    SELECT 1 FROM django_migrations WHERE app = 'ads' AND name = '0011_aifeedback'
);

-- Check if the migration 0012_merge_20250519_0952 exists but is not applied
INSERT INTO django_migrations (app, name, applied)
SELECT 'ads', '0012_merge_20250519_0952', datetime('now')
WHERE NOT EXISTS (
    SELECT 1 FROM django_migrations WHERE app = 'ads' AND name = '0012_merge_20250519_0952'
);
