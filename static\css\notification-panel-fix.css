/*
 * Notification Panel Fix
 * This CSS fixes the issue with notification panel content not being fully visible
 */

/* Fix for notification panel positioning */
.dashboard-notification-panel {
    position: fixed !important;
    top: 80px !important;
    right: 20px !important;
    width: 350px !important;
    max-width: 90vw !important;
    max-height: 80vh !important;
    z-index: 2000 !important; /* Ensure it's above other elements */
    overflow: hidden !important;
}

/* Ensure panel is visible when shown */
.dashboard-notification-panel.show {
    transform: translateY(0) !important;
    opacity: 1 !important;
    pointer-events: auto !important;
}

/* Fix panel content scrolling */
.dashboard-notification-panel .panel-content {
    max-height: calc(80vh - 60px) !important;
    overflow-y: auto !important;
}

/* Mobile-specific fixes */
@media (max-width: 767.98px) {
    .dashboard-notification-panel {
        top: 60px !important; /* Adjust for smaller header on mobile */
        right: 10px !important;
        width: calc(100% - 20px) !important;
        max-width: 350px !important;
    }

    /* Ensure panel doesn't overlap with bottom navigation */
    .dashboard-notification-panel .panel-content {
        max-height: calc(80vh - 125px) !important; /* Account for bottom nav */
    }
}

/* Fix for notification bell in navbar */
.notification-bell {
    position: relative !important;
    z-index: 1500 !important; /* Below panel but above most content */
}

/* Fix for notification panel in mobile menu */
.mobile-menu .notification-panel {
    position: fixed !important;
    top: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 90% !important;
    max-width: 360px !important;
    z-index: 2100 !important; /* Above mobile menu */
    transform: translateX(0) !important;
    opacity: 1 !important;
    pointer-events: auto !important;
}

/* Fix for notification items */
.notification-item {
    display: flex !important;
    padding: 15px 20px !important;
    border-bottom: 1px solid #e5e7eb !important;
    cursor: pointer !important;
}

/* Fix for notification content */
.notification-content {
    flex: 1 !important;
    overflow: hidden !important; /* Prevent text overflow */
}

.notification-title {
    font-weight: 600 !important;
    margin-bottom: 5px !important;
    white-space: normal !important; /* Allow wrapping */
    word-break: break-word !important; /* Break long words */
}

.notification-message {
    font-size: 0.9rem !important;
    margin-bottom: 5px !important;
    white-space: normal !important; /* Allow wrapping */
    word-break: break-word !important; /* Break long words */
}

/* Fix for notification panel animation */
.dashboard-notification-panel {
    transition: transform 0.3s ease, opacity 0.3s ease !important;
}

/* Fix for notification panel backdrop */
.notification-panel-backdrop {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
    z-index: 1999 !important; /* Just below the panel */
    opacity: 0 !important;
    pointer-events: none !important;
    transition: opacity 0.3s ease !important;
}

.notification-panel-backdrop.show {
    opacity: 1 !important;
    pointer-events: auto !important;
}

/* Fix for notification bell badge */
.notification-badge {
    position: absolute !important;
    top: -5px !important;
    right: -5px !important;
    background-color: #ef4444 !important;
    color: white !important;
    border-radius: 50% !important;
    width: 20px !important;
    height: 20px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 0.75rem !important;
    font-weight: bold !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2) !important;
    z-index: 1 !important;
}

/* Fix for notification panel close button */
.dashboard-notification-panel .close-panel {
    background: none !important;
    border: none !important;
    color: white !important;
    font-size: 1rem !important;
    cursor: pointer !important;
    padding: 5px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: transform 0.2s ease !important;
    z-index: 1 !important;
}

.dashboard-notification-panel .close-panel:hover {
    transform: scale(1.1) !important;
}

/* Fix for notification panel empty state */
.dashboard-notification-panel .empty-state {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 40px 20px !important;
    color: #6b7280 !important;
}

/* Fix for notification panel loading spinner */
.dashboard-notification-panel .loading-spinner {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 30px 20px !important;
    color: #6b7280 !important;
}

/* Fix for notification panel error state */
.dashboard-notification-panel .error-state {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 30px 20px !important;
    color: #6b7280 !important;
}

/* Mobile hamburger menu submenu styling */
.mobile-menu-item.has-submenu {
    position: relative !important;
}

.mobile-menu-item.has-submenu::after {
    content: '\f107' !important;
    font-family: 'Font Awesome 5 Free' !important;
    font-weight: 900 !important;
    position: absolute !important;
    right: 15px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    transition: transform 0.3s ease !important;
}

.mobile-menu-item.has-submenu.active::after {
    transform: translateY(-50%) rotate(180deg) !important;
}

.mobile-submenu {
    overflow: hidden !important;
    transition: max-height 0.3s ease, opacity 0.3s ease !important;
    background-color: rgba(255, 255, 255, 0.05) !important;
    border-radius: 8px !important;
    margin-top: 5px !important;
    margin-bottom: 10px !important;
}

/* Mobile account dropdown styling */
.account-dropdown-toggle {
    display: flex !important;
    align-items: center !important;
    padding: 10px 15px !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    transition: background-color 0.3s ease !important;
}

.account-dropdown-toggle:hover {
    background-color: rgba(255, 255, 255, 0.05) !important;
}

.dropdown-indicator {
    margin-left: auto !important;
    color: rgba(255, 255, 255, 0.7) !important;
}

.dropdown-indicator i {
    transition: transform 0.3s ease !important;
}

.mobile-account-dropdown {
    overflow: hidden !important;
    transition: max-height 0.3s ease, opacity 0.3s ease !important;
}

/* Ripple effect for account toggle */
.menu-ripple {
    position: absolute !important;
    background: rgba(255, 255, 255, 0.1) !important;
    border-radius: 50% !important;
    transform: scale(0) !important;
    opacity: 0 !important;
    pointer-events: none !important;
    transition: transform 0.5s, opacity 0.5s !important;
}

.menu-ripple.active {
    transform: scale(1) !important;
    opacity: 1 !important;
}
