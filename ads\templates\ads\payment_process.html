{% extends 'base.html' %}
{% load static %}

{% block title %}Process Payment - {{ ad.title }}{% endblock %}

{% block extra_css %}
<!-- Google Fonts - Poppins and Montserrat -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

<!-- Include common ads CSS -->
{% include 'ads/includes/ads_common_css.html' %}

<style>
    /* Ultra-Premium Enterprise Payment Process Styling */
    body {
        background: linear-gradient(135deg, #000000 0%, #0a0a0a 10%, #1a1a2e 25%, #16213e 40%, #0f3460 60%, #533483 80%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        position: relative;
        overflow-x: hidden;
    }

    /* Premium animated background with enterprise patterns */
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 30% 70%, rgba(102, 126, 234, 0.4) 0%, transparent 50%),
            radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.15) 0%, transparent 40%),
            radial-gradient(circle at 10% 90%, rgba(118, 75, 162, 0.3) 0%, transparent 45%),
            radial-gradient(circle at 90% 10%, rgba(83, 52, 131, 0.25) 0%, transparent 35%),
            radial-gradient(circle at 50% 50%, rgba(102, 126, 234, 0.2) 0%, transparent 50%);
        z-index: -1;
        animation: enterprisePaymentFloat 100s ease-in-out infinite;
    }

    @keyframes enterprisePaymentFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        25% { transform: translateY(-60px) rotate(3deg); }
        50% { transform: translateY(-40px) rotate(-3deg); }
        75% { transform: translateY(-70px) rotate(1.5deg); }
    }

    /* Enterprise Container */
    .enterprise-container {
        position: relative;
        z-index: 1;
        padding: 2rem 0;
        min-height: 100vh;
    }

    /* Premium Header Section */
    .enterprise-header {
        background: linear-gradient(135deg, rgba(26, 35, 126, 0.95) 0%, rgba(40, 53, 147, 0.95) 50%, rgba(63, 81, 181, 0.95) 100%);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2.5rem;
        margin-bottom: 2rem;
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        position: relative;
        overflow: hidden;
        animation: slideInDown 0.8s ease-out;
    }

    .enterprise-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
        animation: shimmer 3s ease-in-out infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    .enterprise-title {
        font-family: 'Montserrat', sans-serif !important;
        font-size: 2.5rem;
        font-weight: 700;
        color: #ffffff;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 2;
    }

    .enterprise-subtitle {
        font-size: 1.1rem;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 1.5rem;
        font-weight: 400;
        position: relative;
        z-index: 2;
    }

    .payment-methods {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .payment-method {
        flex: 1;
        min-width: 150px;
        padding: 20px;
        border: 1px solid rgba(0,0,0,0.1);
        border-radius: 10px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .payment-method:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.05);
    }

    .payment-method.active {
        border-color: #3949ab;
        background-color: rgba(57, 73, 171, 0.05);
        box-shadow: 0 5px 15px rgba(57, 73, 171, 0.1);
    }

    .payment-method-icon {
        font-size: 24px;
        margin-bottom: 10px;
        color: #3949ab;
    }

    .payment-method-name {
        font-weight: 600;
        color: #1a237e;
    }

    .payment-form {
        margin-top: 30px;
    }

    .payment-form label {
        font-weight: 600;
        color: #1a237e;
        margin-bottom: 8px;
    }

    .payment-form .form-control {
        border-radius: 8px;
        padding: 12px 15px;
        border: 1px solid rgba(0,0,0,0.1);
    }

    .payment-form .form-control:focus {
        border-color: #3949ab;
        box-shadow: 0 0 0 3px rgba(57, 73, 171, 0.1);
    }

    .payment-summary {
        background-color: rgba(57, 73, 171, 0.03);
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 25px;
    }

    .payment-summary-title {
        font-weight: 700;
        color: #1a237e;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid rgba(0,0,0,0.1);
    }

    .payment-summary-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
    }

    .payment-summary-label {
        color: #495057;
    }

    .payment-summary-value {
        font-weight: 600;
        color: #212529;
    }

    .payment-summary-total {
        display: flex;
        justify-content: space-between;
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid rgba(0,0,0,0.1);
        font-weight: 700;
        font-size: 18px;
    }

    .payment-summary-total-label {
        color: #1a237e;
    }

    .payment-summary-total-value {
        color: #1a237e;
    }

    .action-buttons {
        display: flex;
        gap: 10px;
        margin-top: 30px;
    }

    @media (max-width: 768px) {
        .payment-methods {
            flex-direction: column;
        }

        .payment-method {
            width: 100%;
        }

        .action-buttons {
            flex-direction: column;
        }

        .action-buttons .ads-btn {
            width: 100%;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="enterprise-container">
    <div class="container">
        <!-- Premium Enterprise Header -->
        <div class="enterprise-header">
            <nav aria-label="breadcrumb" class="mb-3">
                <ol class="breadcrumb" style="background: rgba(255, 255, 255, 0.1); border-radius: 12px; padding: 0.75rem 1.5rem;">
                    <li class="breadcrumb-item"><a href="{% url 'index' %}" style="color: rgba(255, 255, 255, 0.8);">Home</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'ads:dashboard' %}" style="color: rgba(255, 255, 255, 0.8);">Ads Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'ads:ad_detail' ad.slug %}" style="color: rgba(255, 255, 255, 0.8);">{{ ad.title }}</a></li>
                    <li class="breadcrumb-item active" aria-current="page" style="color: white; font-weight: 600;">Process Payment</li>
                </ol>
            </nav>

            <div class="text-center">
                <h1 class="enterprise-title">
                    <i class="fas fa-credit-card me-3 text-warning"></i>
                    Secure Payment Gateway
                </h1>
                <p class="enterprise-subtitle">
                    Complete your payment securely to activate your premium advertisement
                </p>
            </div>
        </div>
    <div class="row">
        <div class="col-lg-8">
            <div class="ads-card animate__animated animate__fadeIn">
                <div class="ads-card-header">
                    <h3 class="ads-card-title">Payment Method</h3>
                </div>

                <div class="card-body">
                    {% if has_paid_transaction %}
                        <div class="alert alert-warning mb-4">
                            <i class="fas fa-exclamation-triangle"></i> You have already submitted a payment for this ad.
                            <p class="mt-2">Your payment is being processed. Please wait for admin approval.</p>
                            {% if latest_transaction %}
                                <div class="mt-3">
                                    <p><strong>Payment Details:</strong></p>
                                    <p>Method: {{ latest_transaction.get_payment_gateway_display }}</p>
                                    <p>Transaction ID: {{ latest_transaction.transaction_id|default:"Processing" }}</p>
                                    <p>Amount: {{ latest_transaction.amount }} KSH</p>
                                    <p>Date: {{ latest_transaction.timestamp|date:"M d, Y H:i" }}</p>
                                </div>
                            {% endif %}
                            <div class="mt-3">
                                <a href="{% url 'ads:ad_detail' ad.slug %}" class="btn btn-primary">
                                    <i class="fas fa-arrow-left"></i> Back to Ad Details
                                </a>
                            </div>
                        </div>
                    {% else %}
                    <div class="payment-methods">
                        <div class="payment-method active" data-method="mpesa">
                            <div class="payment-method-icon">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                            <div class="payment-method-name">M-PESA</div>
                        </div>

                        <div class="payment-method" data-method="card">
                            <div class="payment-method-icon">
                                <i class="far fa-credit-card"></i>
                            </div>
                            <div class="payment-method-name">Credit/Debit Card</div>
                        </div>

                        <div class="payment-method" data-method="bank">
                            <div class="payment-method-icon">
                                <i class="fas fa-university"></i>
                            </div>
                            <div class="payment-method-name">Bank Transfer</div>
                        </div>

                        <div class="payment-method" data-method="paypal">
                            <div class="payment-method-icon">
                                <i class="fab fa-paypal"></i>
                            </div>
                            <div class="payment-method-name">PayPal</div>
                        </div>
                    </div>

                    <form method="post" class="payment-form" id="mpesaForm">
                        {% csrf_token %}
                        <input type="hidden" name="payment_gateway" value="mpesa">

                        <div class="mb-3">
                            <label for="phone_number">M-PESA Phone Number</label>
                            <input type="tel" class="form-control" id="phone_number" name="phone_number" placeholder="e.g., ************" required>
                            <div class="form-text">Enter your M-PESA registered phone number starting with country code (254)</div>
                        </div>

                        <div class="action-buttons">
                            <button type="submit" class="ads-btn ads-btn-primary">
                                <i class="fas fa-check-circle"></i> Complete Payment
                            </button>
                            <a href="{% url 'ads:ad_detail' ad.slug %}" class="ads-btn ads-btn-outline">
                                <i class="fas fa-arrow-left"></i> Back to Ad
                            </a>
                        </div>
                    </form>

                    <form method="post" class="payment-form d-none" id="cardForm">
                        {% csrf_token %}
                        <input type="hidden" name="payment_gateway" value="card">

                        <div class="mb-3">
                            <label for="card_number">Card Number</label>
                            <input type="text" class="form-control" id="card_number" name="card_number" placeholder="1234 5678 9012 3456" required>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="expiry_date">Expiry Date</label>
                                <input type="text" class="form-control" id="expiry_date" name="expiry_date" placeholder="MM/YY" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="cvv">CVV</label>
                                <input type="text" class="form-control" id="cvv" name="cvv" placeholder="123" required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="card_name">Name on Card</label>
                            <input type="text" class="form-control" id="card_name" name="card_name" placeholder="John Doe" required>
                        </div>

                        <div class="action-buttons">
                            <button type="submit" class="ads-btn ads-btn-primary">
                                <i class="fas fa-check-circle"></i> Complete Payment
                            </button>
                            <a href="{% url 'ads:ad_detail' ad.slug %}" class="ads-btn ads-btn-outline">
                                <i class="fas fa-arrow-left"></i> Back to Ad
                            </a>
                        </div>
                    </form>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="ads-card animate__animated animate__fadeIn">
                <div class="ads-card-header">
                    <h3 class="ads-card-title">Payment Summary</h3>
                </div>

                <div class="card-body">
                    <div class="payment-summary">
                        <h4 class="payment-summary-title">{{ ad.title }}</h4>

                        <div class="payment-summary-item">
                            <div class="payment-summary-label">Ad Type</div>
                            <div class="payment-summary-value">{{ ad.ad_type.name }}</div>
                        </div>

                        <div class="payment-summary-item">
                            <div class="payment-summary-label">Duration</div>
                            <div class="payment-summary-value">{{ ad.start_date|date:"M d" }} - {{ ad.end_date|date:"M d, Y" }}</div>
                        </div>

                        <div class="payment-summary-item">
                            <div class="payment-summary-label">Base Price</div>
                            <div class="payment-summary-value">{{ ad.base_pricing }} KSH</div>
                        </div>

                        {% if ad.requires_ai %}
                        <div class="payment-summary-item">
                            <div class="payment-summary-label">AI Enhancement</div>
                            <div class="payment-summary-value">50.00 KSH</div>
                        </div>
                        {% endif %}

                        {% if ad.wants_social %}
                        <div class="payment-summary-item">
                            <div class="payment-summary-label">Social Sharing</div>
                            <div class="payment-summary-value">30.00 KSH</div>
                        </div>
                        {% endif %}

                        <div class="payment-summary-total">
                            <div class="payment-summary-total-label">Total</div>
                            <div class="payment-summary-total-value">{{ ad.final_pricing }} KSH</div>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> Your ad will be activated after admin approval of your payment.
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Payment method selection
        const paymentMethods = document.querySelectorAll('.payment-method');
        const paymentForms = {
            mpesa: document.getElementById('mpesaForm'),
            card: document.getElementById('cardForm')
        };

        paymentMethods.forEach(method => {
            method.addEventListener('click', function() {
                // Remove active class from all methods
                paymentMethods.forEach(m => m.classList.remove('active'));

                // Add active class to clicked method
                this.classList.add('active');

                // Hide all forms
                Object.values(paymentForms).forEach(form => {
                    if (form) form.classList.add('d-none');
                });

                // Show selected form
                const selectedMethod = this.getAttribute('data-method');
                if (paymentForms[selectedMethod]) {
                    paymentForms[selectedMethod].classList.remove('d-none');
                }
            });
        });

        // Disable payment buttons after submission to prevent double payments
        const paymentButtons = document.querySelectorAll('.payment-form button[type="submit"]');

        paymentButtons.forEach(button => {
            button.form.addEventListener('submit', function() {
                // Disable all payment buttons
                paymentButtons.forEach(btn => {
                    btn.disabled = true;
                    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
                    btn.classList.add('disabled');
                });

                // Disable payment method selection
                paymentMethods.forEach(method => {
                    method.style.pointerEvents = 'none';
                    method.style.opacity = '0.6';
                });

                // Show processing message
                const processingMsg = document.createElement('div');
                processingMsg.className = 'alert alert-info mt-3';
                processingMsg.innerHTML = '<i class="fas fa-info-circle"></i> Your payment is being processed. Please do not refresh the page.';
                button.form.appendChild(processingMsg);

                // Submit the form
                return true;
            });
        });
    });
</script>
{% endblock %}
