{% extends 'base.html' %}
{% load static %}

{% block title %}Geolocation Test{% endblock %}

{% block extra_css %}
<!-- Include common ads CSS -->
{% include 'ads/includes/ads_common_css.html' %}
<style>
    .test-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 30px;
    }
    
    .test-card {
        background: white;
        border-radius: 10px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        padding: 25px;
        margin-bottom: 30px;
    }
    
    .test-header {
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #eee;
    }
    
    .test-title {
        font-size: 24px;
        font-weight: 600;
        margin: 0;
        color: #1a237e;
    }
    
    .test-subtitle {
        font-size: 16px;
        color: #666;
        margin-top: 5px;
    }
    
    .test-section {
        margin-bottom: 20px;
    }
    
    .test-section-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 15px;
        color: #1a237e;
    }
    
    .data-display {
        background: #f5f5f5;
        border-radius: 5px;
        padding: 15px;
        font-family: monospace;
        white-space: pre-wrap;
        margin-bottom: 20px;
        max-height: 200px;
        overflow-y: auto;
    }
    
    .test-button {
        background: linear-gradient(135deg, #3949ab, #1e88e5);
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .test-button:hover {
        background: linear-gradient(135deg, #283593, #1565c0);
        transform: translateY(-2px);
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    }
    
    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
    }
    
    .status-success {
        background-color: #4caf50;
    }
    
    .status-error {
        background-color: #f44336;
    }
    
    .status-pending {
        background-color: #ff9800;
    }
    
    .status-text {
        font-weight: 600;
    }
    
    .test-ad {
        border: 2px dashed #e0e0e0;
        border-radius: 8px;
        padding: 20px;
        text-align: center;
        margin-bottom: 20px;
    }
    
    .test-ad-title {
        font-size: 18px;
        font-weight: 600;
        color: #1a237e;
        margin-bottom: 10px;
    }
    
    .test-ad-description {
        color: #666;
        margin-bottom: 15px;
    }
    
    .test-ad-button {
        display: inline-block;
        background: linear-gradient(135deg, #3949ab, #1e88e5);
        color: white;
        padding: 8px 15px;
        border-radius: 5px;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .test-ad-button:hover {
        background: linear-gradient(135deg, #283593, #1565c0);
        transform: translateY(-2px);
    }
</style>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="ads-page-header">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <nav aria-label="breadcrumb" class="ads-breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'index' %}">Home</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'ads:dashboard' %}">Ads Dashboard</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Geolocation Test</li>
                    </ol>
                </nav>

                <h1 class="display-6 text-center mb-1 ads-page-title">Geolocation Tracking Test</h1>
                <p class="lead text-center mb-2 ads-page-subtitle">Verify that real geolocation tracking is working</p>
            </div>
        </div>
    </div>
</div>

<div class="container test-container">
    <div class="test-card">
        <div class="test-header">
            <h2 class="test-title">Geolocation API Test</h2>
            <p class="test-subtitle">This page tests if the geolocation tracking is working properly with your ipinfo.io token.</p>
        </div>
        
        <div class="test-section">
            <h3 class="test-section-title">1. Test Ad (for Impression & Click Tracking)</h3>
            <p>This test ad will trigger impression and click tracking with geolocation data:</p>
            
            <div class="test-ad" data-ad-id="test-123" data-ad-type="test" data-ad-location="test-page">
                <h4 class="test-ad-title">Test Advertisement</h4>
                <p class="test-ad-description">This is a test ad to verify geolocation tracking. Click the button below to trigger click tracking.</p>
                <a href="#" class="test-ad-button">Click Me</a>
            </div>
        </div>
        
        <div class="test-section">
            <h3 class="test-section-title">2. IP-based Geolocation Test</h3>
            <p>Test the ipinfo.io API with your token:</p>
            
            <div class="data-display" id="ipGeolocationResult">Waiting for test to run...</div>
            
            <button class="test-button" id="testIpGeolocation">Test IP Geolocation</button>
        </div>
        
        <div class="test-section">
            <h3 class="test-section-title">3. Browser Geolocation Test</h3>
            <p>Test the browser's built-in geolocation API (requires permission):</p>
            
            <div class="data-display" id="browserGeolocationResult">Waiting for test to run...</div>
            
            <button class="test-button" id="testBrowserGeolocation">Test Browser Geolocation</button>
        </div>
        
        <div class="test-section">
            <h3 class="test-section-title">4. Tracking API Test</h3>
            <p>Test if the tracking endpoints are receiving and processing geolocation data:</p>
            
            <div class="data-display" id="trackingApiResult">Waiting for test to run...</div>
            
            <button class="test-button" id="testTrackingApi">Test Tracking API</button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Test IP Geolocation
        document.getElementById('testIpGeolocation').addEventListener('click', function() {
            const resultDisplay = document.getElementById('ipGeolocationResult');
            resultDisplay.innerHTML = 'Testing ipinfo.io API...';
            
            fetch('https://ipinfo.io/json?token=082682c8d0e7a8')
                .then(response => response.json())
                .then(data => {
                    resultDisplay.innerHTML = `<span class="status-indicator status-success"></span> <span class="status-text">Success!</span>\n\nYour location data:\n${JSON.stringify(data, null, 2)}`;
                    console.log('IP Geolocation data:', data);
                })
                .catch(error => {
                    resultDisplay.innerHTML = `<span class="status-indicator status-error"></span> <span class="status-text">Error:</span> ${error.message}`;
                    console.error('IP Geolocation error:', error);
                });
        });
        
        // Test Browser Geolocation
        document.getElementById('testBrowserGeolocation').addEventListener('click', function() {
            const resultDisplay = document.getElementById('browserGeolocationResult');
            resultDisplay.innerHTML = 'Requesting browser geolocation...';
            
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                    // Success
                    function(position) {
                        const coords = {
                            latitude: position.coords.latitude,
                            longitude: position.coords.longitude,
                            accuracy: position.coords.accuracy
                        };
                        
                        resultDisplay.innerHTML = `<span class="status-indicator status-success"></span> <span class="status-text">Success!</span>\n\nYour coordinates:\n${JSON.stringify(coords, null, 2)}`;
                        console.log('Browser Geolocation data:', coords);
                        
                        // Try reverse geocoding
                        resultDisplay.innerHTML += '\n\nReverse geocoding...';
                        
                        fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${coords.latitude}&lon=${coords.longitude}&zoom=10`)
                            .then(response => response.json())
                            .then(data => {
                                resultDisplay.innerHTML += `\n\nReverse geocoded address:\n${JSON.stringify(data.address, null, 2)}`;
                            })
                            .catch(error => {
                                resultDisplay.innerHTML += `\n\nReverse geocoding error: ${error.message}`;
                            });
                    },
                    // Error
                    function(error) {
                        resultDisplay.innerHTML = `<span class="status-indicator status-error"></span> <span class="status-text">Error:</span> ${error.message} (code: ${error.code})`;
                        console.error('Browser Geolocation error:', error);
                    },
                    // Options
                    {
                        enableHighAccuracy: true,
                        timeout: 5000,
                        maximumAge: 0
                    }
                );
            } else {
                resultDisplay.innerHTML = `<span class="status-indicator status-error"></span> <span class="status-text">Error:</span> Browser does not support geolocation`;
            }
        });
        
        // Test Tracking API
        document.getElementById('testTrackingApi').addEventListener('click', function() {
            const resultDisplay = document.getElementById('trackingApiResult');
            resultDisplay.innerHTML = 'Testing tracking API...';
            
            // Get CSRF token
            function getCsrfToken() {
                const name = 'csrftoken';
                let cookieValue = null;
                if (document.cookie && document.cookie !== '') {
                    const cookies = document.cookie.split(';');
                    for (let i = 0; i < cookies.length; i++) {
                        const cookie = cookies[i].trim();
                        if (cookie.substring(0, name.length + 1) === (name + '=')) {
                            cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                            break;
                        }
                    }
                }
                return cookieValue;
            }
            
            // Get device info
            function getDeviceInfo() {
                let deviceType = 'desktop';
                if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
                    deviceType = /iPad|tablet|Tablet/i.test(navigator.userAgent) ? 'tablet' : 'mobile';
                }
                
                return {
                    type: deviceType,
                    browser: navigator.userAgent,
                    screenWidth: window.innerWidth,
                    screenHeight: window.innerHeight
                };
            }
            
            // First get location data
            fetch('https://ipinfo.io/json?token=082682c8d0e7a8')
                .then(response => response.json())
                .then(locationData => {
                    // Format location data
                    const locationInfo = {
                        city: locationData.city || 'unknown',
                        region: locationData.region || 'unknown',
                        country: locationData.country || 'unknown',
                        loc: locationData.loc || 'unknown'
                    };
                    
                    // Now test the tracking API
                    fetch('/ads/api/track-impression/', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': getCsrfToken()
                        },
                        body: JSON.stringify({
                            ad_id: 'test-123',
                            device_info: getDeviceInfo(),
                            location_info: locationInfo
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        resultDisplay.innerHTML = `<span class="status-indicator status-success"></span> <span class="status-text">Success!</span>\n\nTracking API response:\n${JSON.stringify(data, null, 2)}\n\nLocation data sent:\n${JSON.stringify(locationInfo, null, 2)}`;
                        console.log('Tracking API response:', data);
                    })
                    .catch(error => {
                        resultDisplay.innerHTML = `<span class="status-indicator status-error"></span> <span class="status-text">Error:</span> ${error.message}`;
                        console.error('Tracking API error:', error);
                    });
                })
                .catch(error => {
                    resultDisplay.innerHTML = `<span class="status-indicator status-error"></span> <span class="status-text">Error getting location:</span> ${error.message}`;
                    console.error('Location error:', error);
                });
        });
    });
</script>
{% endblock %}
