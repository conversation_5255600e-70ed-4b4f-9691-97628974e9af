from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>
from . import views

router = DefaultRouter()
router.register(r'qrcodes', views.QRCodeViewSet, basename='qrcode')
router.register(r'profiles', views.UserProfileViewSet, basename='profile')
router.register(r'apikeys', views.APIKeyViewSet, basename='apikey')
router.register(r'batches', views.QRCodeBatchViewSet, basename='batch')
router.register(r'users', views.UserViewSet, basename='user')

urlpatterns = [
    path('', include(router.urls)),
    path('generate/', views.generate_qr_code, name='api_generate_qr_code'),
    path('user-info/', views.user_info, name='api_user_info'),
    path('auth/', include('rest_framework.urls', namespace='rest_framework')),
]
