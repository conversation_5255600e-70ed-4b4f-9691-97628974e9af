// Preloader is now handled by preloader-scripts.js

document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    const generateBtn = document.getElementById('generate-btn');
    const qrCodeContainer = document.getElementById('qr-code-container');
    const downloadBtn = document.getElementById('download-btn');
    const downloadLink = document.getElementById('download-link');
    const shareBtn = document.getElementById('share-btn');
    const saveBtn = document.getElementById('save-btn');
    const qrInfo = document.querySelector('.qr-info');
    const savedCodesContainer = document.getElementById('saved-codes-container');

    // Navigation and Modal Elements
    const loginBtn = document.querySelector('.login-btn');
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const desktopLinks = document.querySelector('.desktop-links');
    const loginModal = document.getElementById('login-modal');
    const modalOverlay = document.querySelector('.modal-overlay');
    const closeModalBtns = document.querySelectorAll('.close-modal');
    const togglePasswordBtn = document.querySelector('.toggle-password');
    const passwordInput = document.getElementById('password');
    const announcementSlider = document.querySelector('.announcement-slider');
    const navLinks = document.querySelectorAll('.nav-link');

    // Mobile Menu Elements
    const mobileMenu = document.querySelector('.mobile-menu');
    const mobileMenuClose = document.querySelector('.mobile-menu-close');
    const mobileSubmenuToggles = document.querySelectorAll('.submenu-toggle');
    const mobileLoginBtn = document.querySelector('.mobile-login-btn');
    const adMenuBtn = document.querySelector('.ad-menu-btn');
    const adsMenuBtn = document.querySelector('.ads-menu-btn');
    const mobilePremiumToggle = document.querySelector('.mobile-premium-toggle');
    const mobilePremiumClose = document.querySelector('.mobile-premium-close');
    const premiumAdBar = document.querySelector('.premium-ad-bar');

    // Input elements
    const urlInput = document.getElementById('url-input');
    const textInput = document.getElementById('text-input');
    const fileInput = document.getElementById('file-input');
    const fileDropArea = document.getElementById('file-drop-area');
    const logoInput = document.getElementById('logo-input');
    const logoPreview = document.getElementById('logo-preview');

    // Customization elements
    const qrTemplateSelect = document.getElementById('qr-template');
    const cornerStyleSelect = document.getElementById('corner-style');
    const templateTitleInput = document.getElementById('template-title');
    const templateSubtitleInput = document.getElementById('template-subtitle');
    const templateTitleGroup = document.getElementById('template-title-group');
    const templateSubtitleGroup = document.getElementById('template-subtitle-group');
    const guidingTextInput = document.getElementById('guiding-text');
    const guidingTextPositionSelect = document.getElementById('guiding-text-position');
    const fontFamilySelect = document.getElementById('font-family');
    const fontWeightSelect = document.getElementById('font-weight');
    const foregroundColorPicker = document.getElementById('foreground-color');
    const backgroundColorPicker = document.getElementById('background-color');
    const accentColorPicker = document.getElementById('accent-color');
    const errorCorrectionSelect = document.getElementById('error-correction');
    const logoSizeSelect = document.getElementById('logo-size');
    const logoStyleSelect = document.getElementById('logo-style');
    const gradientOverlaySelect = document.getElementById('gradient-overlay');
    const livePreviewToggle = document.getElementById('live-preview-toggle');

    // vCard elements
    const vcardName = document.getElementById('vcard-name');
    const vcardEmail = document.getElementById('vcard-email');
    const vcardPhone = document.getElementById('vcard-phone');
    const vcardCompany = document.getElementById('vcard-company');
    const vcardTitle = document.getElementById('vcard-title');

    // WiFi elements
    const wifiSsid = document.getElementById('wifi-ssid');
    const wifiPassword = document.getElementById('wifi-password');
    const wifiEncryption = document.getElementById('wifi-encryption');

    // State variables
    let currentTab = 'url';
    let currentTemplate = 'standard';
    let cornerStyle = 'square';
    let uploadedFile = null;
    let uploadedLogo = null;
    let savedCodes = JSON.parse(localStorage.getItem('savedQRCodes')) || [];
    let livePreviewEnabled = true;
    let guidingText = 'Scan me';
    let guidingTextPosition = 'bottom';
    let fontFamily = 'Inter';
    let fontWeight = 'normal';
    let logoSize = 'medium';
    let logoStyle = 'circle';
    let gradientOverlay = 'none';

    // Initialize jscolor
    jscolor.install();

    // Tab navigation
    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const tabName = button.getAttribute('data-tab');
            switchTab(tabName);
        });
    });

    function switchTab(tabName) {
        // Update active tab button
        tabButtons.forEach(btn => {
            if (btn.getAttribute('data-tab') === tabName) {
                btn.classList.add('active');
            } else {
                btn.classList.remove('active');
            }
        });

        // Update active tab content
        tabContents.forEach(content => {
            if (content.id === tabName + '-tab') {
                content.classList.add('active');
            } else {
                content.classList.remove('active');
            }
        });

        currentTab = tabName;

        // Update live preview if enabled
        if (livePreviewEnabled) {
            updateLivePreview();
        }
    }

    // Template selection
    qrTemplateSelect.addEventListener('change', function() {
        currentTemplate = this.value;
        updateTemplateOptions();

        // Update live preview if enabled
        if (livePreviewEnabled) {
            updateLivePreview();
        }
    });

    // Corner style selection
    cornerStyleSelect.addEventListener('change', function() {
        cornerStyle = this.value;

        // Update live preview if enabled
        if (livePreviewEnabled) {
            updateLivePreview();
        }
    });

    // Logo size selection
    logoSizeSelect.addEventListener('change', function() {
        logoSize = this.value;

        // Update live preview if enabled
        if (livePreviewEnabled) {
            updateLivePreview();
        }
    });

    // Logo style selection
    logoStyleSelect.addEventListener('change', function() {
        logoStyle = this.value;

        // Update live preview if enabled
        if (livePreviewEnabled) {
            updateLivePreview();
        }
    });

    // Gradient overlay selection
    gradientOverlaySelect.addEventListener('change', function() {
        gradientOverlay = this.value;

        // Update live preview if enabled
        if (livePreviewEnabled) {
            updateLivePreview();
        }
    });

    // Initialize template options
    updateTemplateOptions();

    function updateTemplateOptions() {
        // Hide all template options by default
        templateTitleGroup.classList.remove('active');
        templateSubtitleGroup.classList.remove('active');

        // Show relevant options based on selected template
        switch (currentTemplate) {
            case 'standard':
                // Standard template doesn't need title/subtitle
                break;

            case 'event':
            case 'mobile':
            case 'business':
            case 'social':
                // These templates use title and subtitle
                templateTitleGroup.classList.add('active');
                templateSubtitleGroup.classList.add('active');
                break;
        }
    }

    // Live preview toggle
    livePreviewToggle.addEventListener('change', function() {
        livePreviewEnabled = this.checked;
        if (livePreviewEnabled) {
            updateLivePreview();
        }
    });

    // Guiding text input
    guidingTextInput.addEventListener('input', function() {
        guidingText = this.value;
        if (livePreviewEnabled) {
            updateLivePreview();
        }
    });

    // Guiding text position
    guidingTextPositionSelect.addEventListener('change', function() {
        guidingTextPosition = this.value;
        if (livePreviewEnabled) {
            updateLivePreview();
        }
    });

    // Font family selection
    fontFamilySelect.addEventListener('change', function() {
        fontFamily = this.value;
        if (livePreviewEnabled) {
            updateLivePreview();
        }
    });

    // Font weight selection
    fontWeightSelect.addEventListener('change', function() {
        fontWeight = this.value;
        if (livePreviewEnabled) {
            updateLivePreview();
        }
    });

    // Live preview update on input changes
    [templateTitleInput, templateSubtitleInput, foregroundColorPicker, backgroundColorPicker, accentColorPicker].forEach(input => {
        input.addEventListener('input', function() {
            if (livePreviewEnabled) {
                updateLivePreview();
            }
        });
    });

    // Event listeners
    generateBtn.addEventListener('click', generateQRCode);

    urlInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            generateQRCode();
        }
    });

    textInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && e.ctrlKey) {
            generateQRCode();
        }
    });

    // Update live preview when inputs change
    urlInput.addEventListener('input', function() {
        if (livePreviewEnabled && currentTab === 'url') {
            updateLivePreview();
        }
    });

    textInput.addEventListener('input', function() {
        if (livePreviewEnabled && currentTab === 'text') {
            updateLivePreview();
        }
    });

    // Function to update live preview
    function updateLivePreview() {
        // Only update if we have valid input
        const qrData = getQRData(true); // true = silent mode (no error notifications)
        if (!qrData) return;

        // Show loading in preview area
        qrCodeContainer.innerHTML = '<div class="loading-spinner"></div>';

        // Generate QR code with current settings
        setTimeout(() => {
            generateQRCodeWithTemplate(qrData, true); // true = preview mode
        }, 300);
    }

    // File upload handling
    fileInput.addEventListener('change', handleFileSelect);

    // Drag and drop functionality
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        fileDropArea.addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    ['dragenter', 'dragover'].forEach(eventName => {
        fileDropArea.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        fileDropArea.addEventListener(eventName, unhighlight, false);
    });

    function highlight() {
        fileDropArea.classList.add('highlight');
    }

    function unhighlight() {
        fileDropArea.classList.remove('highlight');
    }

    fileDropArea.addEventListener('drop', handleDrop, false);

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;

        if (files.length) {
            fileInput.files = files;
            handleFileSelect();
        }
    }

    function handleFileSelect() {
        if (fileInput.files.length) {
            const file = fileInput.files[0];

            // Check file size (max 5MB)
            if (file.size > 5 * 1024 * 1024) {
                showNotification('File size exceeds 5MB limit', 'error');
                return;
            }

            // Check file type
            const validTypes = ['application/pdf', 'image/jpeg', 'image/png'];
            if (!validTypes.includes(file.type)) {
                showNotification('Only PDF, JPG, and PNG files are supported', 'error');
                return;
            }

            uploadedFile = file;

            // Update UI to show selected file
            const fileNameElement = document.createElement('p');
            fileNameElement.textContent = `Selected: ${file.name}`;
            fileNameElement.className = 'selected-file';

            // Remove any previously selected file info
            const prevFileInfo = fileDropArea.querySelector('.selected-file');
            if (prevFileInfo) {
                prevFileInfo.remove();
            }

            fileDropArea.appendChild(fileNameElement);
        }
    }

    // Logo upload handling
    logoInput.addEventListener('change', handleLogoSelect);

    function handleLogoSelect() {
        if (logoInput.files.length) {
            const file = logoInput.files[0];

            // Check file type
            if (!file.type.startsWith('image/')) {
                showNotification('Please select an image file for the logo', 'error');
                return;
            }

            uploadedLogo = file;

            // Show preview
            const reader = new FileReader();
            reader.onload = function(e) {
                logoPreview.innerHTML = `<img src="${e.target.result}" alt="Logo preview">`;
            };
            reader.readAsDataURL(file);
        }
    }

    // Show loading animation
    function showLoading() {
        qrCodeContainer.innerHTML = '<div class="loading-spinner"></div>';
        qrInfo.textContent = 'Generating QR code...';
        downloadBtn.disabled = true;
        shareBtn.disabled = true;
        saveBtn.disabled = true;
    }

    // Validate URL
    function isValidURL(url) {
        try {
            new URL(url);
            return true;
        } catch (e) {
            // If the URL doesn't start with http:// or https://, add https://
            if (!url.startsWith('http://') && !url.startsWith('https://')) {
                return isValidURL('https://' + url);
            }
            return false;
        }
    }

    // Generate QR code with professional styling
    function generateQRCode() {
        // Get QR code data based on current tab
        const qrData = getQRData();

        if (!qrData) {
            return; // Error message already shown by getQRData
        }

        // Show loading animation
        showLoading();

        // Generate with a small delay for loading animation
        setTimeout(() => {
            generateQRCodeWithTemplate(qrData, false);
        }, 800);
    }

    // Generate QR code with template
    function generateQRCodeWithTemplate(qrData, isPreview = false, advancedOptions = {}) {
        // Get customization options
        const fgColor = foregroundColorPicker.value || '#000000';
        const bgColor = backgroundColorPicker.value || '#FFFFFF';
        const accentColor = accentColorPicker.value || '#2563eb';
        const errorCorrection = errorCorrectionSelect.value || 'H';
        const title = templateTitleInput.value || '';
        const subtitle = templateSubtitleInput.value || '';
        const guideText = guidingTextInput.value || 'Scan me';
        const guidePosition = guidingTextPositionSelect.value || 'bottom';
        const fontFam = fontFamilySelect.value || 'Inter';
        const fontWt = fontWeightSelect.value || 'normal';

        // Set default values for new options if elements don't exist yet
        let cornerType = 'square';
        let logoSz = 'medium';
        let logoStl = 'circle';
        let gradientType = 'none';

        // Only use these values if the elements exist
        if (cornerStyleSelect) cornerType = cornerStyleSelect.value || 'square';
        if (logoSizeSelect) logoSz = logoSizeSelect.value || 'medium';
        if (logoStyleSelect) logoStl = logoStyleSelect.value || 'circle';
        if (gradientOverlaySelect) gradientType = gradientOverlaySelect.value || 'none';

        // Get advanced customization options
        const qrShape = document.getElementById('qr-shape')?.value || advancedOptions.qrShape || 'standard';
        const eyeStyle = document.getElementById('eye-style')?.value || advancedOptions.eyeStyle || 'standard';
        const frameStyle = document.getElementById('frame-style')?.value || advancedOptions.frameStyle || 'none';
        const backgroundPattern = document.getElementById('background-pattern')?.value || advancedOptions.backgroundPattern || 'solid';
        const animationEffect = document.getElementById('animation-effect')?.value || advancedOptions.animationEffect || 'none';

        // Clear previous QR code
        if (!isPreview) {
            qrCodeContainer.innerHTML = '';
        }

        // Generate QR code with specified error correction
        const qr = qrcode(0, errorCorrection);
        qr.addData(qrData);
        qr.make();

        // Create QR code image with appropriate size
        const qrImage = qr.createImgTag(8);
        const tempContainer = document.createElement('div');
        tempContainer.innerHTML = qrImage;

        // Get the image element
        const img = tempContainer.querySelector('img');

        // Create a canvas to convert the QR code to a downloadable image
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // Set canvas dimensions based on template
        let canvasWidth, canvasHeight, qrSize, qrX, qrY;

        // Wait for the image to load
        img.onload = function() {
            const baseSize = img.width;

            // Set dimensions based on template
            switch (currentTemplate) {
                case 'standard':
                    qrSize = baseSize;
                    canvasWidth = qrSize + 80; // 40px padding on each side
                    canvasHeight = qrSize + 80;
                    qrX = 40;
                    qrY = 40;
                    break;

                case 'event':
                    qrSize = baseSize;
                    canvasWidth = qrSize + 80;
                    canvasHeight = qrSize + 160; // Extra space for title
                    qrX = 40;
                    qrY = 100;
                    break;

                case 'mobile':
                    qrSize = baseSize * 0.8; // Smaller QR for mobile template
                    canvasWidth = qrSize + 120;
                    canvasHeight = qrSize + 200;
                    qrX = 60;
                    qrY = 120;
                    break;

                case 'business':
                    qrSize = baseSize * 0.7; // Smaller QR for business card
                    canvasWidth = qrSize + 300; // Extra space for text
                    canvasHeight = qrSize + 80;
                    qrX = canvasWidth - qrSize - 40;
                    qrY = 40;
                    break;

                case 'social':
                    qrSize = baseSize;
                    canvasWidth = qrSize + 80;
                    canvasHeight = qrSize + 160;
                    qrX = 40;
                    qrY = 100;
                    break;

                default:
                    qrSize = baseSize;
                    canvasWidth = qrSize + 80;
                    canvasHeight = qrSize + 80;
                    qrX = 40;
                    qrY = 40;
            }

            // Set canvas dimensions
            canvas.width = canvasWidth;
            canvas.height = canvasHeight;

            // Apply template
            applyTemplate(ctx, canvas.width, canvas.height, qrSize, qrX, qrY, img, {
                template: currentTemplate,
                fgColor: fgColor,
                bgColor: bgColor,
                accentColor: accentColor,
                title: title,
                subtitle: subtitle,
                guideText: guideText,
                guidePosition: guidePosition,
                fontFam: fontFam,
                fontWt: fontWt,
                cornerType: cornerType,
                logoSz: logoSz,
                logoStl: logoStl,
                gradientType: gradientType,
                qrShape: qrShape,
                eyeStyle: eyeStyle,
                frameStyle: frameStyle,
                backgroundPattern: backgroundPattern,
                animationEffect: animationEffect
            });

            // Add logo if uploaded
            if (uploadedLogo) {
                addLogoToQRCode(ctx, qrX, qrY, qrSize);
            }

            // Convert canvas to data URL
            const dataURL = canvas.toDataURL('image/png');

            // Create a new image to display the styled QR code
            const styledImg = new Image();
            styledImg.src = dataURL;
            styledImg.style.maxWidth = '100%';
            styledImg.style.borderRadius = '8px';
            styledImg.style.boxShadow = 'var(--shadow-md)';
            styledImg.style.opacity = '0';
            styledImg.style.transition = 'opacity 0.3s ease-in-out';

            // Apply animation effect if specified
            if (animationEffect && animationEffect !== 'none') {
                styledImg.classList.add(`qr-animation-${animationEffect}`);
            }

            // Replace the QR code with the styled one
            qrCodeContainer.innerHTML = '';
            qrCodeContainer.appendChild(styledImg);

            if (!isPreview) {
                // Set download link
                downloadLink.href = dataURL;
                downloadBtn.disabled = false;
                shareBtn.disabled = false;
                saveBtn.disabled = false;

                // Update QR info text
                qrInfo.textContent = 'Scan to test';

                // Save QR code to history
                saveToHistory(dataURL, qrData);
            }

            // Fade in the QR code
            setTimeout(() => {
                styledImg.style.opacity = '1';
            }, 100);
        };
    }

    // Apply template to canvas
    function applyTemplate(ctx, width, height, qrSize, qrX, qrY, qrImg, options) {
        const {
            template, fgColor, bgColor, accentColor, title, subtitle,
            guideText, guidePosition, fontFam, fontWt, cornerType,
            logoSz, logoStl, gradientType, qrShape, eyeStyle,
            frameStyle, backgroundPattern, animationEffect
        } = options;

        // Set global cornerStyle for QR code styling
        cornerStyle = cornerType || 'square';

        // Clear canvas
        ctx.clearRect(0, 0, width, height);

        switch (template) {
            case 'standard':
                // Standard corporate template
                ctx.fillStyle = bgColor;
                ctx.fillRect(0, 0, width, height);

                // Add subtle background pattern
                addCorporateBackground(ctx, width, height, bgColor);

                // Draw QR code
                ctx.drawImage(qrImg, qrX, qrY, qrSize, qrSize);

                // Apply color styling to QR code
                applyQRCodeStyling(ctx, qrX, qrY, qrSize, qrSize, fgColor, bgColor);

                // Add border
                addProfessionalBorder(ctx, width, height);

                // Add guiding text if enabled
                if (guidePosition !== 'none' && guideText) {
                    addGuidingText(ctx, width, height, qrX, qrY, qrSize, guideText, guidePosition, fgColor, fontFam, fontWt);
                }
                break;

            case 'premium':
                // Premium corporate template with elegant design
                // Create gradient background
                const premiumGradient = ctx.createLinearGradient(0, 0, width, height);
                premiumGradient.addColorStop(0, bgColor);
                premiumGradient.addColorStop(1, shadeColor(bgColor, -10)); // Slightly darker shade
                ctx.fillStyle = premiumGradient;
                ctx.fillRect(0, 0, width, height);

                // Add subtle pattern
                ctx.save();
                const patternColor = isLightColor(bgColor) ? 'rgba(0, 0, 0, 0.02)' : 'rgba(255, 255, 255, 0.03)';
                ctx.strokeStyle = patternColor;
                ctx.lineWidth = 1;

                // Draw diagonal lines for premium look
                const lineSpacing = 20;
                for (let i = -height; i < width + height; i += lineSpacing) {
                    ctx.beginPath();
                    ctx.moveTo(i, 0);
                    ctx.lineTo(i + height, height);
                    ctx.stroke();
                }
                ctx.restore();

                // Add elegant frame
                const frameWidth = 15;
                const innerMargin = 5;

                // Outer frame
                ctx.strokeStyle = isLightColor(bgColor) ? 'rgba(0, 0, 0, 0.1)' : 'rgba(255, 255, 255, 0.15)';
                ctx.lineWidth = 1;
                ctx.strokeRect(frameWidth/2, frameWidth/2, width - frameWidth, height - frameWidth);

                // Inner frame
                ctx.strokeStyle = isLightColor(bgColor) ? 'rgba(0, 0, 0, 0.05)' : 'rgba(255, 255, 255, 0.1)';
                ctx.lineWidth = 1;
                ctx.strokeRect(frameWidth + innerMargin, frameWidth + innerMargin,
                               width - 2*(frameWidth + innerMargin), height - 2*(frameWidth + innerMargin));

                // Draw QR code
                ctx.drawImage(qrImg, qrX, qrY, qrSize, qrSize);

                // Apply color styling to QR code
                applyQRCodeStyling(ctx, qrX, qrY, qrSize, qrSize, fgColor, bgColor);

                // Add title if provided
                if (title) {
                    ctx.font = `${fontWt} 18px "${fontFam}", sans-serif`;
                    ctx.fillStyle = fgColor;
                    ctx.textAlign = 'center';
                    ctx.fillText(title, width / 2, frameWidth + innerMargin * 2 + 20);
                }

                // Add guiding text if enabled
                if (guidePosition !== 'none' && guideText) {
                    addGuidingText(ctx, width, height, qrX, qrY, qrSize, guideText, guidePosition, fgColor, fontFam, fontWt);
                }

                // Add corner accents
                const accentSize = 10;
                ctx.strokeStyle = accentColor;
                ctx.lineWidth = 2;

                // Top-left corner
                ctx.beginPath();
                ctx.moveTo(frameWidth, frameWidth + accentSize);
                ctx.lineTo(frameWidth, frameWidth);
                ctx.lineTo(frameWidth + accentSize, frameWidth);
                ctx.stroke();

                // Top-right corner
                ctx.beginPath();
                ctx.moveTo(width - frameWidth - accentSize, frameWidth);
                ctx.lineTo(width - frameWidth, frameWidth);
                ctx.lineTo(width - frameWidth, frameWidth + accentSize);
                ctx.stroke();

                // Bottom-left corner
                ctx.beginPath();
                ctx.moveTo(frameWidth, height - frameWidth - accentSize);
                ctx.lineTo(frameWidth, height - frameWidth);
                ctx.lineTo(frameWidth + accentSize, height - frameWidth);
                ctx.stroke();

                // Bottom-right corner
                ctx.beginPath();
                ctx.moveTo(width - frameWidth - accentSize, height - frameWidth);
                ctx.lineTo(width - frameWidth, height - frameWidth);
                ctx.lineTo(width - frameWidth, height - frameWidth - accentSize);
                ctx.stroke();
                break;

            case 'minimal':
                // Minimal clean template with lots of whitespace
                ctx.fillStyle = bgColor;
                ctx.fillRect(0, 0, width, height);

                // Draw QR code
                ctx.drawImage(qrImg, qrX, qrY, qrSize, qrSize);

                // Apply color styling to QR code
                applyQRCodeStyling(ctx, qrX, qrY, qrSize, qrSize, fgColor, bgColor);

                // Add subtle shadow to QR code for depth
                ctx.save();
                ctx.shadowColor = 'rgba(0, 0, 0, 0.05)';
                ctx.shadowBlur = 10;
                ctx.shadowOffsetX = 0;
                ctx.shadowOffsetY = 0;
                ctx.strokeStyle = 'rgba(0, 0, 0, 0.02)';
                ctx.lineWidth = 1;
                ctx.strokeRect(qrX, qrY, qrSize, qrSize);
                ctx.restore();

                // Add minimal border - just a single thin line
                ctx.strokeStyle = isLightColor(bgColor) ? 'rgba(0, 0, 0, 0.05)' : 'rgba(255, 255, 255, 0.1)';
                ctx.lineWidth = 1;
                ctx.strokeRect(10, 10, width - 20, height - 20);

                // Add guiding text if enabled - with minimal styling
                if (guidePosition !== 'none' && guideText) {
                    ctx.font = `300 14px "${fontFam}", sans-serif`; // Always use light weight for minimal
                    ctx.fillStyle = isLightColor(bgColor) ? 'rgba(0, 0, 0, 0.6)' : 'rgba(255, 255, 255, 0.8)';
                    ctx.textAlign = 'center';

                    if (guidePosition === 'top') {
                        ctx.fillText(guideText, width / 2, qrY - 20);
                    } else {
                        ctx.fillText(guideText, width / 2, qrY + qrSize + 25);
                    }
                }
                break;

            case 'event':
                // Event ticket template
                ctx.fillStyle = bgColor;
                ctx.fillRect(0, 0, width, height);

                // Add header bar
                ctx.fillStyle = accentColor;
                ctx.fillRect(0, 0, width, 60);

                // Add title and subtitle
                if (title) {
                    ctx.font = 'bold 18px "Inter", sans-serif';
                    ctx.fillStyle = '#FFFFFF';
                    ctx.textAlign = 'center';
                    ctx.fillText(title, width / 2, 30);
                }

                if (subtitle) {
                    ctx.font = '14px "Inter", sans-serif';
                    ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
                    ctx.textAlign = 'center';
                    ctx.fillText(subtitle, width / 2, 50);
                }

                // Draw QR code
                ctx.drawImage(qrImg, qrX, qrY, qrSize, qrSize);

                // Apply color styling to QR code
                applyQRCodeStyling(ctx, qrX, qrY, qrSize, qrSize, fgColor, bgColor);

                // Add border
                ctx.strokeStyle = accentColor;
                ctx.lineWidth = 2;
                ctx.strokeRect(0, 0, width, height);

                // Add guiding text if enabled
                if (guidePosition !== 'none' && guideText) {
                    addGuidingText(ctx, width, height, qrX, qrY, qrSize, guideText, guidePosition, '#333333', fontFam, fontWt);
                }
                break;

            case 'mobile':
                // Mobile phone template
                ctx.fillStyle = '#333333';
                ctx.fillRect(0, 0, width, height);

                // Phone screen
                ctx.fillStyle = '#f8f9fa';
                ctx.fillRect(10, 30, width - 20, height - 60);

                // Phone notch
                ctx.fillStyle = '#333333';
                ctx.fillRect(width / 2 - 30, 30, 60, 10);

                // Add title and subtitle
                if (title) {
                    ctx.font = 'bold 16px "Inter", sans-serif';
                    ctx.fillStyle = '#333333';
                    ctx.textAlign = 'center';
                    ctx.fillText(title, width / 2, 70);
                }

                if (subtitle) {
                    ctx.font = '12px "Inter", sans-serif';
                    ctx.fillStyle = '#666666';
                    ctx.textAlign = 'center';
                    ctx.fillText(subtitle, width / 2, 90);
                }

                // Draw QR code
                ctx.drawImage(qrImg, qrX, qrY, qrSize, qrSize);

                // Apply color styling to QR code
                applyQRCodeStyling(ctx, qrX, qrY, qrSize, qrSize, fgColor, bgColor);

                // Add guiding text if enabled
                if (guidePosition !== 'none' && guideText) {
                    addGuidingText(ctx, width, height, qrX, qrY, qrSize, guideText, guidePosition, '#333333', fontFam, fontWt);
                }
                break;

            case 'business':
                // Business card template
                ctx.fillStyle = bgColor;
                ctx.fillRect(0, 0, width, height);

                // Add accent color strip
                ctx.fillStyle = accentColor;
                ctx.fillRect(0, 0, 10, height);

                // Add title and subtitle
                if (title) {
                    ctx.font = 'bold 18px "Inter", sans-serif';
                    ctx.fillStyle = '#333333';
                    ctx.textAlign = 'left';
                    ctx.fillText(title, 30, height / 2 - 15);
                }

                if (subtitle) {
                    ctx.font = '14px "Inter", sans-serif';
                    ctx.fillStyle = '#666666';
                    ctx.textAlign = 'left';
                    ctx.fillText(subtitle, 30, height / 2 + 15);
                }

                // Draw QR code
                ctx.drawImage(qrImg, qrX, qrY, qrSize, qrSize);

                // Apply color styling to QR code
                applyQRCodeStyling(ctx, qrX, qrY, qrSize, qrSize, fgColor, bgColor);

                // Add border
                ctx.strokeStyle = '#DDDDDD';
                ctx.lineWidth = 1;
                ctx.strokeRect(0, 0, width, height);

                // Add guiding text if enabled
                if (guidePosition !== 'none' && guideText) {
                    // For business card, always put text near the QR code
                    const textX = qrX + qrSize / 2;
                    const textY = qrY + qrSize + 20;

                    ctx.font = `${fontWt} 12px "${fontFam}", sans-serif`;
                    ctx.fillStyle = '#666666';
                    ctx.textAlign = 'center';
                    ctx.fillText(guideText, textX, textY);
                }
                break;

            case 'social':
                // Social media template
                // Create gradient background
                const gradient = ctx.createLinearGradient(0, 0, width, height);
                gradient.addColorStop(0, accentColor);
                gradient.addColorStop(1, shadeColor(accentColor, -30)); // Darker shade
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, width, height);

                // Add title and subtitle
                if (title) {
                    ctx.font = 'bold 18px "Inter", sans-serif';
                    ctx.fillStyle = '#FFFFFF';
                    ctx.textAlign = 'center';
                    ctx.fillText(title, width / 2, 30);
                }

                if (subtitle) {
                    ctx.font = '14px "Inter", sans-serif';
                    ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
                    ctx.textAlign = 'center';
                    ctx.fillText(subtitle, width / 2, 50);
                }

                // White background for QR code
                ctx.fillStyle = '#FFFFFF';
                ctx.fillRect(qrX - 10, qrY - 10, qrSize + 20, qrSize + 20);
                ctx.fillStyle = '#FFFFFF';
                ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
                ctx.lineWidth = 2;
                ctx.strokeRect(qrX - 15, qrY - 15, qrSize + 30, qrSize + 30);

                // Draw QR code
                ctx.drawImage(qrImg, qrX, qrY, qrSize, qrSize);

                // Apply color styling to QR code
                applyQRCodeStyling(ctx, qrX, qrY, qrSize, qrSize, fgColor, bgColor);

                // Add guiding text if enabled
                if (guidePosition !== 'none' && guideText) {
                    addGuidingText(ctx, width, height, qrX, qrY, qrSize, guideText, guidePosition, '#FFFFFF', fontFam, fontWt);
                }
                break;
        }
    }

    // Helper function to add guiding text
    function addGuidingText(ctx, width, height, qrX, qrY, qrSize, text, position, color, fontFam = 'Inter', fontWt = 'normal') {
        if (!text || position === 'none') return;

        // Set font with custom options
        const fontSize = 14;
        ctx.font = `${fontWt} ${fontSize}px "${fontFam}", sans-serif`;
        ctx.fillStyle = color;
        ctx.textAlign = 'center';

        if (position === 'top') {
            // Position above QR code
            const textY = qrY - 20;
            ctx.fillText(text, width / 2, textY);
        } else {
            // Position below QR code (default)
            const textY = qrY + qrSize + 25;
            ctx.fillText(text, width / 2, textY);
        }
    }

    // Helper function to shade a color (positive amt = lighter, negative = darker)
    function shadeColor(color, percent) {
        const num = parseInt(color.replace('#', ''), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) + amt;
        const G = (num >> 8 & 0x00FF) + amt;
        const B = (num & 0x0000FF) + amt;

        return '#' + (
            0x1000000 +
            (R < 255 ? (R < 0 ? 0 : R) : 255) * 0x10000 +
            (G < 255 ? (G < 0 ? 0 : G) : 255) * 0x100 +
            (B < 255 ? (B < 0 ? 0 : B) : 255)
        ).toString(16).slice(1);
    }

    // Get QR code data based on current tab
    function getQRData(silent = false) {
        switch (currentTab) {
            case 'url':
                let url = urlInput.value.trim();

                if (!url) {
                    if (!silent) showNotification('Please enter a URL', 'error');
                    return null;
                }

                // Add https:// if not present
                if (!url.startsWith('http://') && !url.startsWith('https://')) {
                    url = 'https://' + url;
                    if (!silent) urlInput.value = url;
                }

                if (!isValidURL(url)) {
                    if (!silent) showNotification('Please enter a valid URL', 'error');
                    return null;
                }

                return url;

            case 'text':
                const text = textInput.value.trim();

                if (!text) {
                    if (!silent) showNotification('Please enter some text', 'error');
                    return null;
                }

                return text;

            case 'file':
                if (!uploadedFile) {
                    if (!silent) showNotification('Please select a file', 'error');
                    return null;
                }

                // For files, we'd typically upload to a server and return a URL
                // For this demo, we'll create a placeholder URL
                return `file://${uploadedFile.name}`;

            case 'vcard':
                const name = vcardName.value.trim();
                const email = vcardEmail.value.trim();
                const phone = vcardPhone.value.trim();
                const company = vcardCompany.value.trim();
                const jobTitle = vcardTitle.value.trim();

                if (!name && !email && !phone) {
                    if (!silent) showNotification('Please enter at least name, email, or phone', 'error');
                    return null;
                }

                // Create vCard format
                let vcard = 'BEGIN:VCARD\nVERSION:3.0\n';

                if (name) vcard += `FN:${name}\n`;
                if (email) vcard += `EMAIL:${email}\n`;
                if (phone) vcard += `TEL:${phone}\n`;
                if (company) vcard += `ORG:${company}\n`;
                if (jobTitle) vcard += `TITLE:${jobTitle}\n`;

                vcard += 'END:VCARD';

                return vcard;

            case 'wifi':
                const ssid = wifiSsid.value.trim();
                const password = wifiPassword.value.trim();
                const encryption = wifiEncryption.value;

                if (!ssid) {
                    if (!silent) showNotification('Please enter the WiFi network name', 'error');
                    return null;
                }

                // Create WiFi format
                let wifi = `WIFI:S:${ssid};`;

                if (encryption !== 'nopass') {
                    if (!password) {
                        if (!silent) showNotification('Please enter the WiFi password', 'error');
                        return null;
                    }
                    wifi += `T:${encryption};P:${password};`;
                } else {
                    wifi += 'T:nopass;';
                }

                wifi += ';';

                return wifi;

            default:
                if (!silent) showNotification('Please select a valid input type', 'error');
                return null;
        }
    }

    // Function to add corporate background pattern
    function addCorporateBackground(ctx, width, height, bgColor) {
        ctx.save();

        // Parse background color to determine if it's light or dark
        const isLightBg = isLightColor(bgColor);

        // Create subtle patterns based on template
        switch (currentTemplate) {
            case 'standard':
                // Professional grid pattern
                const gridColor = isLightBg ? 'rgba(0, 0, 0, 0.03)' : 'rgba(255, 255, 255, 0.05)';
                ctx.strokeStyle = gridColor;
                ctx.lineWidth = 1;

                // Create a grid pattern
                const gridSize = 20;

                // Draw vertical lines
                for (let x = 0; x <= width; x += gridSize) {
                    ctx.beginPath();
                    ctx.moveTo(x, 0);
                    ctx.lineTo(x, height);
                    ctx.stroke();
                }

                // Draw horizontal lines
                for (let y = 0; y <= height; y += gridSize) {
                    ctx.beginPath();
                    ctx.moveTo(0, y);
                    ctx.lineTo(width, y);
                    ctx.stroke();
                }

                // Add subtle corner accents
                const accentColor = isLightBg ? 'rgba(0, 0, 0, 0.05)' : 'rgba(255, 255, 255, 0.08)';
                const accentSize = 40;

                // Top-left accent
                ctx.strokeStyle = accentColor;
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(0, accentSize);
                ctx.lineTo(0, 0);
                ctx.lineTo(accentSize, 0);
                ctx.stroke();

                // Bottom-right accent
                ctx.beginPath();
                ctx.moveTo(width - accentSize, height);
                ctx.lineTo(width, height);
                ctx.lineTo(width, height - accentSize);
                ctx.stroke();
                break;

            case 'event':
                // No additional background for event template
                break;

            case 'mobile':
                // No additional background for mobile template
                break;

            case 'business':
                // Subtle dot pattern for business card
                const dotColor = isLightBg ? 'rgba(0, 0, 0, 0.03)' : 'rgba(255, 255, 255, 0.05)';
                ctx.fillStyle = dotColor;

                const dotSpacing = 20;
                const dotSize = 2;

                for (let x = dotSpacing; x < width; x += dotSpacing) {
                    for (let y = dotSpacing; y < height; y += dotSpacing) {
                        ctx.beginPath();
                        ctx.arc(x, y, dotSize / 2, 0, Math.PI * 2);
                        ctx.fill();
                    }
                }
                break;

            case 'social':
                // Add subtle radial gradient overlay
                const gradientColor = isLightBg ? 'rgba(0, 0, 0, 0.03)' : 'rgba(255, 255, 255, 0.05)';
                const radialGradient = ctx.createRadialGradient(
                    width / 2, height / 2, 0,
                    width / 2, height / 2, Math.max(width, height) / 2
                );

                radialGradient.addColorStop(0, 'rgba(255, 255, 255, 0)');
                radialGradient.addColorStop(1, gradientColor);

                ctx.fillStyle = radialGradient;
                ctx.fillRect(0, 0, width, height);
                break;

            default:
                // Default subtle grid
                const defaultColor = isLightBg ? 'rgba(0, 0, 0, 0.02)' : 'rgba(255, 255, 255, 0.03)';
                ctx.strokeStyle = defaultColor;
                ctx.lineWidth = 1;

                const defaultGridSize = 30;

                // Draw diagonal lines
                for (let i = -height; i < width + height; i += defaultGridSize) {
                    ctx.beginPath();
                    ctx.moveTo(i, 0);
                    ctx.lineTo(i + height, height);
                    ctx.stroke();
                }
        }

        ctx.restore();
    }

    // Function to apply professional styling to the QR code
    function applyQRCodeStyling(ctx, x, y, width, height, fgColor, bgColor) {
        // Get the QR code image data
        const imageData = ctx.getImageData(x, y, width, height);
        const data = imageData.data;

        // Convert hex colors to RGB
        const fgRGB = hexToRgb(fgColor);
        const bgRGB = hexToRgb(bgColor);

        // Simple approach - just replace colors for better readability
        for (let i = 0; i < data.length; i += 4) {
            // If it's a dark pixel (QR code data)
            if (data[i] < 128) {
                // Set to foreground color
                data[i] = fgRGB.r;       // R
                data[i+1] = fgRGB.g;     // G
                data[i+2] = fgRGB.b;     // B
                data[i+3] = 255;         // Full opacity
            } else {
                // Set to background color
                data[i] = bgRGB.r;       // R
                data[i+1] = bgRGB.g;     // G
                data[i+2] = bgRGB.b;     // B
                data[i+3] = 255;         // Full opacity
            }
        }

        // Put the styled QR code back
        ctx.putImageData(imageData, x, y);

        // Add subtle shadow for depth if using a light background
        if (isLightColor(bgColor)) {
            ctx.save();
            ctx.shadowColor = 'rgba(0, 0, 0, 0.1)';
            ctx.shadowBlur = 3;
            ctx.shadowOffsetX = 1;
            ctx.shadowOffsetY = 1;
            ctx.strokeStyle = 'rgba(0, 0, 0, 0.05)';
            ctx.lineWidth = 0.5;
            ctx.strokeRect(x, y, width, height);
            ctx.restore();
        }
    }

    // Function to add logo to QR code
    function addLogoToQRCode(ctx, qrX, qrY, qrSize) {
        if (!uploadedLogo) return;

        const reader = new FileReader();
        reader.onload = function(e) {
            const logoImg = new Image();
            logoImg.onload = function() {
                // Calculate logo size based on selected option
                let logoSizePercent = 0.25; // Default medium (25%)

                // Use a simpler approach for logo size
                if (logoSz === 'small') {
                    logoSizePercent = 0.15;
                } else if (logoSz === 'medium') {
                    logoSizePercent = 0.25;
                } else if (logoSz === 'large') {
                    logoSizePercent = 0.35;
                }

                const logoSize = qrSize * logoSizePercent;

                // Calculate position (center of QR code)
                const logoX = qrX + (qrSize - logoSize) / 2;
                const logoY = qrY + (qrSize - logoSize) / 2;

                // Save context for clipping
                ctx.save();

                // Apply different logo styles - use logoStl instead of logoStyle
                if (logoStl === 'circle') {
                    // Create circular clipping path for logo
                    ctx.beginPath();
                    ctx.arc(logoX + logoSize/2, logoY + logoSize/2, logoSize/2, 0, Math.PI * 2);
                    ctx.closePath();
                    ctx.clip();
                } else if (logoStl === 'square') {
                    // Create square clipping path
                    ctx.beginPath();
                    ctx.rect(logoX, logoY, logoSize, logoSize);
                    ctx.closePath();
                    ctx.clip();
                } else if (logoStl === 'rounded') {
                    // Create rounded square clipping path
                    const radius = logoSize * 0.2;
                    ctx.beginPath();
                    ctx.moveTo(logoX + radius, logoY);
                    ctx.lineTo(logoX + logoSize - radius, logoY);
                    ctx.quadraticCurveTo(logoX + logoSize, logoY, logoX + logoSize, logoY + radius);
                    ctx.lineTo(logoX + logoSize, logoY + logoSize - radius);
                    ctx.quadraticCurveTo(logoX + logoSize, logoY + logoSize, logoX + logoSize - radius, logoY + logoSize);
                    ctx.lineTo(logoX + radius, logoY + logoSize);
                    ctx.quadraticCurveTo(logoX, logoY + logoSize, logoX, logoY + logoSize - radius);
                    ctx.lineTo(logoX, logoY + radius);
                    ctx.quadraticCurveTo(logoX, logoY, logoX + radius, logoY);
                    ctx.closePath();
                    ctx.clip();
                }
                // For 'transparent', we don't need a clipping path

                // Create white background for logo (except for transparent style)
                if (logoStl !== 'transparent') {
                    ctx.fillStyle = '#FFFFFF';
                    ctx.fillRect(logoX, logoY, logoSize, logoSize);
                }

                // Draw logo
                ctx.drawImage(logoImg, logoX, logoY, logoSize, logoSize);

                // Apply gradient overlay if selected
                if (gradientType !== 'none') {
                    let overlayOpacity;
                    switch (gradientType) {
                        case 'light':
                            overlayOpacity = 0.2;
                            break;
                        case 'medium':
                            overlayOpacity = 0.4;
                            break;
                        case 'strong':
                            overlayOpacity = 0.6;
                            break;
                        default:
                            overlayOpacity = 0;
                    }

                    if (overlayOpacity > 0) {
                        // Create gradient with accent color
                        const gradient = ctx.createLinearGradient(logoX, logoY, logoX + logoSize, logoY + logoSize);
                        const accentColor = accentColorPicker.value || '#2563eb';

                        gradient.addColorStop(0, `rgba(${hexToRgb(accentColor).r}, ${hexToRgb(accentColor).g}, ${hexToRgb(accentColor).b}, 0)`);
                        gradient.addColorStop(1, `rgba(${hexToRgb(accentColor).r}, ${hexToRgb(accentColor).g}, ${hexToRgb(accentColor).b}, ${overlayOpacity})`);

                        ctx.fillStyle = gradient;

                        if (logoStl === 'circle') {
                            ctx.beginPath();
                            ctx.arc(logoX + logoSize/2, logoY + logoSize/2, logoSize/2, 0, Math.PI * 2);
                            ctx.fill();
                        } else if (logoStl === 'rounded') {
                            const radius = logoSize * 0.2;
                            ctx.beginPath();
                            ctx.moveTo(logoX + radius, logoY);
                            ctx.lineTo(logoX + logoSize - radius, logoY);
                            ctx.quadraticCurveTo(logoX + logoSize, logoY, logoX + logoSize, logoY + radius);
                            ctx.lineTo(logoX + logoSize, logoY + logoSize - radius);
                            ctx.quadraticCurveTo(logoX + logoSize, logoY + logoSize, logoX + logoSize - radius, logoY + logoSize);
                            ctx.lineTo(logoX + radius, logoY + logoSize);
                            ctx.quadraticCurveTo(logoX, logoY + logoSize, logoX, logoY + logoSize - radius);
                            ctx.lineTo(logoX, logoY + radius);
                            ctx.quadraticCurveTo(logoX, logoY, logoX + radius, logoY);
                            ctx.fill();
                        } else {
                            ctx.fillRect(logoX, logoY, logoSize, logoSize);
                        }
                    }
                }

                // Restore context
                ctx.restore();

                // Add subtle shadow around logo (except for transparent style)
                if (logoStl !== 'transparent') {
                    ctx.save();

                    if (logoStl === 'circle') {
                        ctx.beginPath();
                        ctx.arc(logoX + logoSize/2, logoY + logoSize/2, logoSize/2, 0, Math.PI * 2);
                        ctx.closePath();
                    } else if (logoStl === 'rounded') {
                        const radius = logoSize * 0.2;
                        ctx.beginPath();
                        ctx.moveTo(logoX + radius, logoY);
                        ctx.lineTo(logoX + logoSize - radius, logoY);
                        ctx.quadraticCurveTo(logoX + logoSize, logoY, logoX + logoSize, logoY + radius);
                        ctx.lineTo(logoX + logoSize, logoY + logoSize - radius);
                        ctx.quadraticCurveTo(logoX + logoSize, logoY + logoSize, logoX + logoSize - radius, logoY + logoSize);
                        ctx.lineTo(logoX + radius, logoY + logoSize);
                        ctx.quadraticCurveTo(logoX, logoY + logoSize, logoX, logoY + logoSize - radius);
                        ctx.lineTo(logoX, logoY + radius);
                        ctx.quadraticCurveTo(logoX, logoY, logoX + radius, logoY);
                        ctx.closePath();
                    } else {
                        ctx.beginPath();
                        ctx.rect(logoX, logoY, logoSize, logoSize);
                        ctx.closePath();
                    }

                    ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
                    ctx.shadowBlur = 5;
                    ctx.shadowOffsetX = 0;
                    ctx.shadowOffsetY = 0;
                    ctx.strokeStyle = '#FFFFFF';
                    ctx.lineWidth = 3;
                    ctx.stroke();
                    ctx.restore();

                    // Add white border around logo
                    if (logoStl === 'circle') {
                        ctx.beginPath();
                        ctx.arc(logoX + logoSize/2, logoY + logoSize/2, logoSize/2, 0, Math.PI * 2);
                        ctx.strokeStyle = '#FFFFFF';
                        ctx.lineWidth = 2;
                        ctx.stroke();
                    } else if (logoStl === 'rounded') {
                        const radius = logoSize * 0.2;
                        ctx.beginPath();
                        ctx.moveTo(logoX + radius, logoY);
                        ctx.lineTo(logoX + logoSize - radius, logoY);
                        ctx.quadraticCurveTo(logoX + logoSize, logoY, logoX + logoSize, logoY + radius);
                        ctx.lineTo(logoX + logoSize, logoY + logoSize - radius);
                        ctx.quadraticCurveTo(logoX + logoSize, logoY + logoSize, logoX + logoSize - radius, logoY + logoSize);
                        ctx.lineTo(logoX + radius, logoY + logoSize);
                        ctx.quadraticCurveTo(logoX, logoY + logoSize, logoX, logoY + logoSize - radius);
                        ctx.lineTo(logoX, logoY + radius);
                        ctx.quadraticCurveTo(logoX, logoY, logoX + radius, logoY);
                        ctx.strokeStyle = '#FFFFFF';
                        ctx.lineWidth = 2;
                        ctx.stroke();
                    } else if (logoStl === 'square') {
                        ctx.beginPath();
                        ctx.rect(logoX, logoY, logoSize, logoSize);
                        ctx.strokeStyle = '#FFFFFF';
                        ctx.lineWidth = 2;
                        ctx.stroke();
                    }
                }

                // Update the QR code image
                const dataURL = canvas.toDataURL('image/png');
                const styledImg = qrCodeContainer.querySelector('img');
                if (styledImg) {
                    styledImg.src = dataURL;
                }

                // Update download link
                downloadLink.href = dataURL;
            };
            logoImg.src = e.target.result;
        };
        reader.readAsDataURL(uploadedLogo);
    }

    // Function to add professional border
    function addProfessionalBorder(ctx, width, height) {
        ctx.save();

        // Add subtle double border for enterprise look
        // Outer border
        ctx.strokeStyle = 'rgba(0, 0, 0, 0.08)';
        ctx.lineWidth = 1;
        ctx.strokeRect(0, 0, width, height);

        // Inner border
        ctx.strokeStyle = 'rgba(0, 0, 0, 0.05)';
        ctx.lineWidth = 1;
        ctx.strokeRect(3, 3, width - 6, height - 6);

        // Add subtle corner accents
        const accentSize = 15;
        ctx.strokeStyle = 'rgba(0, 0, 0, 0.1)';
        ctx.lineWidth = 1.5;

        // Top-left corner
        ctx.beginPath();
        ctx.moveTo(0, accentSize);
        ctx.lineTo(0, 0);
        ctx.lineTo(accentSize, 0);
        ctx.stroke();

        // Top-right corner
        ctx.beginPath();
        ctx.moveTo(width - accentSize, 0);
        ctx.lineTo(width, 0);
        ctx.lineTo(width, accentSize);
        ctx.stroke();

        // Bottom-left corner
        ctx.beginPath();
        ctx.moveTo(0, height - accentSize);
        ctx.lineTo(0, height);
        ctx.lineTo(accentSize, height);
        ctx.stroke();

        // Bottom-right corner
        ctx.beginPath();
        ctx.moveTo(width - accentSize, height);
        ctx.lineTo(width, height);
        ctx.lineTo(width, height - accentSize);
        ctx.stroke();

        ctx.restore();
    }

    // Save QR code to history
    function saveToHistory(dataURL, qrData) {
        // Create a new history item
        const historyItem = {
            id: Date.now(),
            dataURL: dataURL,
            data: qrData,
            timestamp: new Date().toISOString()
        };

        // Add to saved codes array
        savedCodes.unshift(historyItem);

        // Limit to 10 items
        if (savedCodes.length > 10) {
            savedCodes = savedCodes.slice(0, 10);
        }

        // Save to localStorage
        localStorage.setItem('savedQRCodes', JSON.stringify(savedCodes));

        // Update UI
        updateSavedCodesUI();
    }

    // Update saved codes UI
    function updateSavedCodesUI() {
        // Clear container
        savedCodesContainer.innerHTML = '';

        if (savedCodes.length === 0) {
            savedCodesContainer.innerHTML = '<p class="empty-state">Your generated QR codes will appear here</p>';
            return;
        }

        // Add each saved code
        savedCodes.forEach(item => {
            const codeItem = document.createElement('div');
            codeItem.className = 'saved-code-item';
            codeItem.innerHTML = `<img src="${item.dataURL}" alt="Saved QR Code">`;

            // Add click event to load this QR code
            codeItem.addEventListener('click', () => {
                loadSavedQRCode(item);
            });

            savedCodesContainer.appendChild(codeItem);
        });
    }

    // Load a saved QR code
    function loadSavedQRCode(item) {
        // Display the QR code
        qrCodeContainer.innerHTML = `<img src="${item.dataURL}" alt="QR Code" style="max-width: 100%; border-radius: 8px; box-shadow: var(--shadow-md);">`;

        // Enable buttons
        downloadBtn.disabled = false;
        shareBtn.disabled = false;
        saveBtn.disabled = false;

        // Set download link
        downloadLink.href = item.dataURL;

        // Update QR info text
        qrInfo.textContent = 'Loaded from history';
    }

    // Share QR code
    shareBtn.addEventListener('click', shareQRCode);

    function shareQRCode() {
        if (navigator.share && downloadLink.href) {
            navigator.share({
                title: 'QR Code',
                text: 'Check out this QR code I created',
                url: downloadLink.href
            })
            .catch(error => {
                showNotification('Error sharing QR code', 'error');
            });
        } else {
            showNotification('Sharing is not supported on this browser', 'info');
        }
    }

    // Save QR code to saved codes
    saveBtn.addEventListener('click', function() {
        if (downloadLink.href) {
            showNotification('QR code saved to history', 'success');
        }
    });

    // Helper function to convert hex color to RGB
    function hexToRgb(hex) {
        // Remove # if present
        hex = hex.replace('#', '');

        // Parse hex values
        const r = parseInt(hex.substring(0, 2), 16);
        const g = parseInt(hex.substring(2, 4), 16);
        const b = parseInt(hex.substring(4, 6), 16);

        return { r, g, b };
    }

    // Helper function to determine if a color is light or dark
    function isLightColor(color) {
        const rgb = hexToRgb(color);
        const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;
        return brightness > 128;
    }

    // Show notification
    function showNotification(message, type = 'info') {
        // Create notification container if it doesn't exist
        let notificationContainer = document.getElementById('notification-container');
        if (!notificationContainer) {
            notificationContainer = document.createElement('div');
            notificationContainer.id = 'notification-container';
            document.body.appendChild(notificationContainer);
        }

        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;

        // Set icon based on type
        let icon;
        switch (type) {
            case 'success':
                icon = 'fa-check-circle';
                break;
            case 'error':
                icon = 'fa-exclamation-circle';
                break;
            case 'warning':
                icon = 'fa-exclamation-triangle';
                break;
            default:
                icon = 'fa-info-circle';
        }

        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas ${icon}"></i>
                <p>${message}</p>
            </div>
        `;

        // Add to container
        notificationContainer.appendChild(notification);

        // Fade in
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        // Remove after delay
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                notificationContainer.removeChild(notification);
            }, 300);
        }, 3000);
    }

    // Add notification styles
    const notificationStyle = document.createElement('style');
    notificationStyle.textContent = `
        #notification-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 10px;
            max-width: 300px;
        }

        .notification {
            background-color: var(--bg-white);
            border-left: 4px solid var(--primary-color);
            border-radius: var(--radius-md);
            padding: 12px 15px;
            box-shadow: var(--shadow-md);
            transform: translateX(120%);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            border-left-color: #10b981;
        }

        .notification.error {
            border-left-color: #ef4444;
        }

        .notification.warning {
            border-left-color: #f59e0b;
        }

        .notification-content {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .notification-content i {
            font-size: 1.2rem;
            color: var(--primary-color);
        }

        .notification.success i {
            color: #10b981;
        }

        .notification.error i {
            color: #ef4444;
        }

        .notification.warning i {
            color: #f59e0b;
        }

        .notification-content p {
            margin: 0;
            font-size: 0.875rem;
            color: var(--text-dark);
        }

        .highlight {
            border-color: var(--primary-color) !important;
            background-color: rgba(37, 99, 235, 0.1) !important;
        }

        .selected-file {
            margin-top: 10px;
            font-size: 0.875rem;
            color: var(--text-dark);
            font-weight: 500;
        }
    `;
    document.head.appendChild(notificationStyle);

    // Initialize saved codes UI
    updateSavedCodesUI();

    // Modal and Navigation Functionality

    // Login Modal
    loginBtn.addEventListener('click', function() {
        openModal(loginModal);
    });



    // Close Modals
    closeModalBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            closeAllModals();
        });
    });

    // Close modals when clicking overlay
    modalOverlay.addEventListener('click', function() {
        closeAllModals();
    });

    // Toggle password visibility
    if (togglePasswordBtn) {
        togglePasswordBtn.addEventListener('click', function() {
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                togglePasswordBtn.innerHTML = '<i class="fas fa-eye-slash"></i>';
            } else {
                passwordInput.type = 'password';
                togglePasswordBtn.innerHTML = '<i class="fas fa-eye"></i>';
            }
        });
    }

    // Mobile menu toggle
    if (mobileMenuBtn && mobileMenu) {
        // Create overlay element for mobile menu
        const overlay = document.createElement('div');
        overlay.className = 'mobile-menu-overlay';
        document.body.appendChild(overlay);

        mobileMenuBtn.addEventListener('click', function() {
            mobileMenu.classList.add('active');
            overlay.classList.add('active');
            document.body.style.overflow = 'hidden'; // Prevent scrolling

            // Toggle animation for the menu button
            this.classList.add('active');
        });

        // Close mobile menu
        if (mobileMenuClose) {
            mobileMenuClose.addEventListener('click', function() {
                mobileMenu.classList.remove('active');
                overlay.classList.remove('active');
                document.body.style.overflow = ''; // Enable scrolling
                mobileMenuBtn.classList.remove('active');
            });
        }

        // Close mobile menu when clicking overlay
        overlay.addEventListener('click', function() {
            mobileMenu.classList.remove('active');
            this.classList.remove('active');
            document.body.style.overflow = ''; // Enable scrolling
            mobileMenuBtn.classList.remove('active');
        });

        // Toggle submenu
        if (mobileSubmenuToggles) {
            mobileSubmenuToggles.forEach(toggle => {
                toggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    const menuItem = this.closest('.has-submenu');
                    menuItem.classList.toggle('active');
                });
            });
        }

        // Mobile login button
        if (mobileLoginBtn) {
            mobileLoginBtn.addEventListener('click', function() {
                mobileMenu.classList.remove('active');
                overlay.classList.remove('active');
                document.body.style.overflow = ''; // Enable scrolling
                mobileMenuBtn.classList.remove('active');

                // Open login modal
                openModal(loginModal);
            });
        }


    }

    // Premium button (desktop)
    if (adMenuBtn) {
        // Set initial state
        if (premiumAdBar && window.innerWidth > 768) {
            premiumAdBar.style.display = 'block';
            premiumAdBar.style.opacity = '1';

            // Make sure button text is correct
            const btnText = adMenuBtn.querySelector('span');
            if (btnText) {
                btnText.textContent = 'Hide Features';
            }
        }

        adMenuBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            if (premiumAdBar) {
                // Toggle premium ad bar visibility with animation
                if (window.innerWidth > 768) {
                    // Desktop behavior
                    const currentOpacity = window.getComputedStyle(premiumAdBar).getPropertyValue('opacity');
                    const isVisible = currentOpacity > 0.1;

                    if (isVisible) {
                        // Hide premium features
                        premiumAdBar.style.opacity = '0';
                        setTimeout(() => {
                            premiumAdBar.style.display = 'none';
                        }, 300);

                        // Update button text
                        const btnText = this.querySelector('span');
                        if (btnText) {
                            btnText.textContent = 'Show Features';
                        }

                        // Show notification
                        showNotification('Premium features hidden. Click again to show.', 'info');
                    } else {
                        // Show premium features
                        premiumAdBar.style.display = 'block';
                        setTimeout(() => {
                            premiumAdBar.style.opacity = '1';
                        }, 10);

                        // Update button text
                        const btnText = this.querySelector('span');
                        if (btnText) {
                            btnText.textContent = 'Hide Features';
                        }

                        // Show notification
                        showNotification('Premium features now visible.', 'success');
                    }
                } else {
                    // Mobile behavior - slide up from bottom
                    premiumAdBar.classList.toggle('mobile-visible');
                }
            }
        });
    }

    // Advertisement menu button
    if (adsMenuBtn) {
        const adsDropdown = document.querySelector('.ads-dropdown');

        adsMenuBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Show notification when clicked on mobile
            if (window.innerWidth <= 768) {
                showNotification('Access your advertising dashboard at ads.enterpriseqr.com', 'info');
            } else {
                // Toggle dropdown visibility on desktop
                if (adsDropdown) {
                    const isVisible = window.getComputedStyle(adsDropdown).getPropertyValue('visibility') === 'visible';

                    if (isVisible) {
                        adsDropdown.style.opacity = '0';
                        adsDropdown.style.visibility = 'hidden';
                        adsDropdown.style.transform = 'translateY(10px)';
                    } else {
                        adsDropdown.style.display = 'block';
                        setTimeout(() => {
                            adsDropdown.style.opacity = '1';
                            adsDropdown.style.visibility = 'visible';
                            adsDropdown.style.transform = 'translateY(0)';
                        }, 10);
                    }
                }
            }
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (adsDropdown && !adsMenuBtn.contains(e.target) && !adsDropdown.contains(e.target)) {
                adsDropdown.style.opacity = '0';
                adsDropdown.style.visibility = 'hidden';
                adsDropdown.style.transform = 'translateY(10px)';
            }
        });

        // Add click events to all ad options
        document.querySelectorAll('.ads-option').forEach(option => {
            option.addEventListener('click', function(e) {
                e.preventDefault();
                const adType = this.querySelector('h5').textContent;

                // Different notifications based on the ad type
                switch(adType) {
                    case 'Ad Dashboard':
                        window.location.href = 'ad-platform.html';
                        setTimeout(() => {
                            showNotification('Ad Dashboard loaded successfully!', 'success');
                        }, 1500);
                        break;
                    case 'Create Campaign':
                        showNotification('Starting new campaign wizard...', 'info');
                        setTimeout(() => {
                            showNotification('Campaign wizard ready! Complete your campaign details.', 'success');
                        }, 1500);
                        break;
                    case 'Ad Listings':
                        showNotification('Loading your ad listings...', 'info');
                        setTimeout(() => {
                            showNotification('You have 3 active campaigns and 5 completed campaigns.', 'success');
                        }, 1500);
                        break;
                    case 'Performance Analytics':
                        showNotification('Loading analytics data...', 'info');
                        setTimeout(() => {
                            showNotification('Your ads have received 12,345 impressions and 567 clicks this month.', 'success');
                        }, 1500);
                        break;
                    case 'Audience Builder':
                        showNotification('Loading audience segments...', 'info');
                        setTimeout(() => {
                            showNotification('You have 5 saved audience segments ready for targeting.', 'success');
                        }, 1500);
                        break;
                    case 'Ad Designer':
                        showNotification('Loading ad designer tool...', 'info');
                        setTimeout(() => {
                            showNotification('Ad designer ready! Create professional ads with our templates.', 'success');
                        }, 1500);
                        break;
                    default:
                        showNotification(`You selected ${adType}. Our team will contact you shortly.`, 'success');
                }
            });
        });

        // Add click event to contact button
        const contactBtn = document.querySelector('.ads-contact-btn');
        if (contactBtn) {
            contactBtn.addEventListener('click', function(e) {
                e.preventDefault();
                showNotification('Thank you for your interest! Our advertising team will contact you within 24 hours.', 'success');
            });
        }

        // Add click events to mobile ad items
        document.querySelectorAll('.mobile-menu-item.ads-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                const adType = this.querySelector('span').textContent;

                // Close mobile menu
                const mobileMenu = document.querySelector('.mobile-menu');
                const overlay = document.querySelector('.mobile-menu-overlay');
                const mobileMenuBtn = document.querySelector('.mobile-menu-btn');

                if (mobileMenu && overlay) {
                    mobileMenu.classList.remove('active');
                    overlay.classList.remove('active');
                    document.body.style.overflow = ''; // Enable scrolling
                    if (mobileMenuBtn) mobileMenuBtn.classList.remove('active');
                }

                // Different notifications based on the ad type
                switch(adType) {
                    case 'Ad Dashboard':
                        window.location.href = 'ad-platform.html';
                        setTimeout(() => {
                            showNotification('Ad Dashboard loaded successfully!', 'success');
                        }, 1500);
                        break;
                    case 'Create Campaign':
                        showNotification('Starting new campaign wizard...', 'info');
                        setTimeout(() => {
                            showNotification('Campaign wizard ready! Complete your campaign details.', 'success');
                        }, 1500);
                        break;
                    case 'Ad Listings':
                        showNotification('Loading your ad listings...', 'info');
                        setTimeout(() => {
                            showNotification('You have 3 active campaigns and 5 completed campaigns.', 'success');
                        }, 1500);
                        break;
                    case 'Performance Analytics':
                        showNotification('Loading analytics data...', 'info');
                        setTimeout(() => {
                            showNotification('Your ads have received 12,345 impressions and 567 clicks this month.', 'success');
                        }, 1500);
                        break;
                    case 'Audience Builder':
                        showNotification('Loading audience segments...', 'info');
                        setTimeout(() => {
                            showNotification('You have 5 saved audience segments ready for targeting.', 'success');
                        }, 1500);
                        break;
                    case 'Ad Designer':
                        showNotification('Loading ad designer tool...', 'info');
                        setTimeout(() => {
                            showNotification('Ad designer ready! Create professional ads with our templates.', 'success');
                        }, 1500);
                        break;
                    case 'Contact Ad Team':
                        showNotification('Thank you for your interest! Our advertising team will contact you within 24 hours.', 'success');
                        break;
                    default:
                        showNotification(`You selected ${adType}. Our team will contact you shortly.`, 'success');
                }
            });
        });
    }

    // Mobile premium toggle
    if (mobilePremiumToggle) {
        mobilePremiumToggle.addEventListener('click', function() {
            if (premiumAdBar) {
                // Add haptic feedback (vibration) if supported
                if (navigator.vibrate) {
                    navigator.vibrate(50);
                }

                // Close mobile menu with animation
                mobileMenu.style.transition = 'right 0.3s ease';
                mobileMenu.classList.remove('active');

                const overlay = document.querySelector('.mobile-menu-overlay');
                if (overlay) {
                    overlay.style.transition = 'opacity 0.3s ease';
                    overlay.classList.remove('active');
                }

                document.body.style.overflow = ''; // Enable scrolling
                if (mobileMenuBtn) mobileMenuBtn.classList.remove('active');

                // Show premium ad bar with animation
                setTimeout(() => {
                    // Add animation classes to premium items
                    const adItems = premiumAdBar.querySelectorAll('.ad-item');
                    adItems.forEach((item, index) => {
                        item.classList.remove('animate__fadeInUp');
                        void item.offsetWidth; // Trigger reflow
                        item.style.animationDelay = `${0.1 * index}s`;
                        item.classList.add('animate__fadeInUp');
                    });

                    // Show the premium bar
                    premiumAdBar.classList.add('mobile-visible');

                    // Show notification
                    showNotification('Premium features available! Explore our advanced options.', 'info');
                }, 300);
            }
        });
    }

    // Mobile premium close button
    if (mobilePremiumClose) {
        mobilePremiumClose.addEventListener('click', function() {
            if (premiumAdBar) {
                // Add haptic feedback (vibration) if supported
                if (navigator.vibrate) {
                    navigator.vibrate(50);
                }

                // Hide premium ad bar with animation
                premiumAdBar.style.transition = 'transform 0.3s ease';
                premiumAdBar.classList.remove('mobile-visible');

                // Show notification
                showNotification('Premium features hidden. Access them anytime from the menu.', 'info');
            }
        });

        // Add swipe down to close premium bar
        let touchStartY = 0;
        let touchEndY = 0;

        premiumAdBar.addEventListener('touchstart', function(e) {
            touchStartY = e.changedTouches[0].screenY;
        }, false);

        premiumAdBar.addEventListener('touchend', function(e) {
            touchEndY = e.changedTouches[0].screenY;
            handleSwipe();
        }, false);

        function handleSwipe() {
            if (touchEndY - touchStartY > 70) { // Swipe down
                mobilePremiumClose.click();
            }
        }
    }

    // Add hover effect to nav links
    navLinks.forEach(link => {
        link.addEventListener('mouseenter', function() {
            if (!this.classList.contains('active')) {
                const indicator = this.querySelector('.nav-link-indicator');
                if (indicator) {
                    indicator.style.transform = 'scaleX(1)';
                }
            }
        });

        link.addEventListener('mouseleave', function() {
            if (!this.classList.contains('active')) {
                const indicator = this.querySelector('.nav-link-indicator');
                if (indicator) {
                    indicator.style.transform = 'scaleX(0)';
                }
            }
        });
    });

    // VPN Activate button
    if (vpnActivateBtn) {
        vpnActivateBtn.addEventListener('click', function() {
            // Change button text and style
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Activating...';
            this.disabled = true;

            // Simulate activation
            setTimeout(() => {
                // Update VPN status
                const vpnStatus = vpnModal.querySelector('.vpn-status');
                vpnStatus.style.backgroundColor = 'rgba(16, 185, 129, 0.1)';

                const vpnIcon = vpnModal.querySelector('.vpn-icon');
                vpnIcon.style.backgroundColor = 'rgba(16, 185, 129, 0.2)';
                vpnIcon.style.color = 'var(--success-color)';
                vpnIcon.innerHTML = '<i class="fas fa-shield-alt"></i>';

                const vpnInfoTitle = vpnModal.querySelector('.vpn-info h4');
                vpnInfoTitle.textContent = 'Your connection is secure';

                const vpnInfoText = vpnModal.querySelector('.vpn-info p');
                vpnInfoText.textContent = 'Enterprise VPN is active and protecting your data.';

                // Update button
                this.innerHTML = '<i class="fas fa-check"></i> VPN Activated';
                this.style.backgroundColor = 'var(--success-color)';
                this.disabled = false;

                // Update VPN link in nav
                vpnLink.innerHTML = '<i class="fas fa-shield-alt"></i> VPN Active';

                // Show notification
                showNotification('VPN activated successfully', 'success');
            }, 2000);
        });
    }

    // Function to open modal
    function openModal(modal) {
        closeAllModals(); // Close any open modals first
        modal.classList.add('active');
        modalOverlay.classList.add('active');
        document.body.style.overflow = 'hidden'; // Prevent scrolling
    }

    // Function to close all modals
    function closeAllModals() {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            modal.classList.remove('active');
        });
        modalOverlay.classList.remove('active');
        document.body.style.overflow = ''; // Restore scrolling
    }

    // Function to show notifications
    function showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;

        // Create notification content
        const icon = document.createElement('i');
        icon.className = type === 'success' ? 'fas fa-check-circle' :
                         type === 'error' ? 'fas fa-exclamation-circle' :
                         'fas fa-info-circle';

        const text = document.createElement('span');
        text.textContent = message;

        const closeBtn = document.createElement('button');
        closeBtn.className = 'notification-close';
        closeBtn.innerHTML = '<i class="fas fa-times"></i>';

        // Add elements to notification
        notification.appendChild(icon);
        notification.appendChild(text);
        notification.appendChild(closeBtn);

        // Add notification to body
        document.body.appendChild(notification);

        // Show notification with animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        // Close button functionality
        closeBtn.addEventListener('click', () => {
            notification.classList.remove('show');
            setTimeout(() => {
                notification.remove();
            }, 300);
        });

        // Auto close after 5 seconds
        setTimeout(() => {
            if (document.body.contains(notification)) {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        notification.remove();
                    }
                }, 300);
            }
        }, 5000);
    }

    // Close modals with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeAllModals();
        }
    });

    // Add event listeners for ad items
    const adItems = document.querySelectorAll('.ad-item');
    adItems.forEach((adItem, index) => {
        const adCta = adItem.querySelector('.ad-cta');
        const adText = adItem.querySelector('.ad-text').textContent;

        if (adCta) {
            adCta.addEventListener('click', function(e) {
                e.preventDefault();

                let message = '';
                let type = 'info';

                switch (index) {
                    case 0: // Bulk QR Generation
                        message = 'Bulk QR Generation is a premium feature. Upgrade to access.';
                        type = 'info';
                        break;
                    case 1: // Advanced Analytics
                        message = 'Advanced Analytics dashboard is now available in your account.';
                        type = 'success';
                        break;
                    case 2: // Encrypted QR Codes
                        message = 'Encrypted QR Codes require Enterprise subscription.';
                        type = 'info';
                        break;
                    default:
                        message = 'Feature coming soon!';
                }

                showNotification(message, type);
            });
        }

        // Make the entire ad item clickable
        adItem.addEventListener('click', function(e) {
            if (!e.target.classList.contains('ad-cta')) {
                const adCta = this.querySelector('.ad-cta');
                if (adCta) {
                    adCta.click();
                }
            }
        });
    });

    // Add event listener for footer CTA button
    const footerCtaBtn = document.querySelector('.footer-cta-btn');
    if (footerCtaBtn) {
        footerCtaBtn.addEventListener('click', function(e) {
            e.preventDefault();
            showNotification('Thank you for your interest! Our team will contact you within 24 hours.', 'success');

            // Scroll to top to show the QR generator
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });

            // Focus on the URL input after a short delay
            setTimeout(() => {
                const urlInput = document.getElementById('url-input');
                if (urlInput) {
                    urlInput.focus();
                }
            }, 1000);
        });
    }

    // Initialize user dropdown menu
    initUserDropdown();

    // Initialize RBAC system
    initRBAC();

    // Initialize advanced customization options
    if (typeof initAdvancedCustomization === 'function') {
        initAdvancedCustomization();
    }

    // Initialize advanced options toggle
    initAdvancedOptionsToggle();
});

// Initialize user dropdown menu
function initUserDropdown() {
    const userMenu = document.getElementById('user-menu');

    if (userMenu) {
        const userMenuBtn = userMenu.querySelector('.user-menu-btn');
        const userNameElement = userMenu.querySelector('.user-name');

        // Update user name if user info exists
        const userInfo = JSON.parse(localStorage.getItem('user_info'));
        if (userInfo && userInfo.name) {
            userNameElement.textContent = userInfo.name;
        }

        // Toggle dropdown on click
        userMenuBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            userMenu.classList.toggle('active');
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!userMenu.contains(e.target)) {
                userMenu.classList.remove('active');
            }
        });
    }
}

// Initialize Role-Based Access Control
function initRBAC() {
    // Check if auth-roles.js is loaded
    if (typeof rbac !== 'undefined') {
        // If user info exists but no roles are set, set default role
        const userInfo = JSON.parse(localStorage.getItem('user_info'));
        const userRoles = JSON.parse(localStorage.getItem('user_roles'));

        if (userInfo && (!userRoles || userRoles.length === 0)) {
            // Set default role to VIEWER for security
            rbac.setUser(userInfo, ['VIEWER']);
        } else if (userInfo && userRoles) {
            // Set user with existing roles
            rbac.setUser(userInfo, userRoles);
        }

        // Update UI based on permissions
        rbac.updateUI();
    } else {
        // Load auth-roles.js dynamically if not already loaded
        const script = document.createElement('script');
        script.src = 'js/auth-roles.js';
        script.onload = function() {
            // Initialize RBAC after script is loaded
            if (typeof rbac !== 'undefined') {
                const userInfo = JSON.parse(localStorage.getItem('user_info'));
                const userRoles = JSON.parse(localStorage.getItem('user_roles'));

                if (userInfo && userRoles) {
                    rbac.setUser(userInfo, userRoles);
                } else if (userInfo) {
                    rbac.setUser(userInfo, ['VIEWER']);
                }

                rbac.updateUI();
            }
        };
        document.head.appendChild(script);
    }
}

// Initialize advanced options toggle
function initAdvancedOptionsToggle() {
    const advancedOptionsToggle = document.getElementById('advanced-options-toggle');
    const advancedOptionsContent = document.getElementById('advanced-options-content');

    if (advancedOptionsToggle && advancedOptionsContent) {
        advancedOptionsToggle.addEventListener('click', function() {
            // Toggle the collapsed class
            advancedOptionsContent.classList.toggle('collapsed');

            // Update the toggle icon
            const icon = this.querySelector('i');
            if (icon) {
                if (advancedOptionsContent.classList.contains('collapsed')) {
                    icon.className = 'fas fa-chevron-down';
                } else {
                    icon.className = 'fas fa-chevron-up';

                    // Show notification when advanced options are expanded
                    showNotification('Advanced customization options are now available', 'info');
                }
            }
        });
    }
}
