import requests
import json
import time

# Get the Mistral API key from the .env file
with open('.env', 'r') as f:
    for line in f:
        if line.startswith('MISTRAL_API_KEY='):
            api_key = line.strip().split('=')[1]
            break

# Get the Mistral model from the .env file
with open('.env', 'r') as f:
    for line in f:
        if line.startswith('MISTRAL_MODEL='):
            model = line.strip().split('=')[1]
            break
    else:
        model = "mistral-tiny"  # Default model

print(f"Using API key: {api_key[:4]}...{api_key[-4:]}")
print(f"Using model: {model}")

# API endpoint
url = "https://api.mistral.ai/v1/chat/completions"

# Headers
headers = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {api_key}"
}

# Simple test payload
test_payload = {
    "model": model,
    "messages": [
        {"role": "user", "content": "Hello, are you working?"}
    ],
    "temperature": 0.7,
    "max_tokens": 10
}

print("Sending simple test request...")
start_time = time.time()

try:
    # Make the request
    response = requests.post(url, headers=headers, json=test_payload, timeout=30)
    
    # Print the response status code
    print(f"Response status code: {response.status_code}")
    
    # Print the response time
    print(f"Response time: {time.time() - start_time:.2f} seconds")
    
    # Print the response content
    if response.status_code == 200:
        print("Response content:")
        response_json = response.json()
        print(json.dumps(response_json, indent=2))
        
        # Extract the message content
        if "choices" in response_json and len(response_json["choices"]) > 0:
            message = response_json["choices"][0]["message"]
            if "content" in message:
                print(f"\nMessage content: {message['content']}")
    else:
        print("Error response:")
        print(response.text)
except Exception as e:
    print(f"Error: {str(e)}")
    import traceback
    traceback.print_exc()
