{% extends "base.html" %}
{% load static %}

{% block title %}Delete User - Enterprise QR{% endblock %}

{% block extra_css %}
<style>
    .delete-container {
        max-width: 600px;
        margin: 0 auto;
    }
    
    .card {
        border: none;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        border-radius: 0.5rem;
    }
    
    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        padding: 1.25rem 1.5rem;
    }
    
    .card-title {
        margin-bottom: 0;
        font-weight: 600;
        color: #dc3545;
    }
    
    .card-body {
        padding: 1.5rem;
    }
    
    .warning-icon {
        font-size: 4rem;
        color: #dc3545;
        margin-bottom: 1.5rem;
    }
    
    .user-info {
        background-color: #f8f9fa;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    
    .user-avatar {
        width: 64px;
        height: 64px;
        border-radius: 50%;
        background-color: #e9ecef;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: #6c757d;
    }
    
    .btn-toolbar {
        display: flex;
        justify-content: space-between;
        margin-top: 2rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="h3">
                <i class="fas fa-user-times me-2"></i>Delete User
            </h1>
            <p class="text-muted">Permanently remove a user account</p>
        </div>
        <div class="col-md-4 text-md-end">
            <a href="{% url 'user_management' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to User Management
            </a>
        </div>
    </div>
    
    <div class="delete-container">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-exclamation-triangle me-2"></i>Confirm Deletion
                </h5>
            </div>
            <div class="card-body text-center">
                <div class="warning-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                
                <h4 class="mb-3">Are you sure you want to delete this user?</h4>
                <p class="text-muted mb-4">This action cannot be undone. All data associated with this user will be permanently deleted.</p>
                
                <div class="user-info d-flex align-items-center">
                    <div class="user-avatar me-3">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="text-start">
                        <h5 class="mb-1">{{ user_to_delete.get_full_name|default:user_to_delete.username }}</h5>
                        <p class="text-muted mb-0">{{ user_to_delete.email }}</p>
                        <p class="text-muted mb-0">Username: {{ user_to_delete.username }}</p>
                    </div>
                </div>
                
                <form method="post">
                    {% csrf_token %}
                    <div class="btn-toolbar">
                        <a href="{% url 'edit_user' user_to_delete.id %}" class="btn btn-outline-secondary">Cancel</a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-2"></i>Delete User Permanently
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
