{% extends "base.html" %}
{% load static %}

{% block title %}Create AI Landing Page - {{ qr_code.name }}{% endblock %}

{% block extra_css %}
<style>
    .ai-page-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }
    
    .ai-card {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }
    
    .ai-header {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border-radius: 10px;
        padding: 2rem;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .page-type-selector {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .page-type-option {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 1.5rem;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        background: white;
    }
    
    .page-type-option:hover {
        border-color: #667eea;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .page-type-option.selected {
        border-color: #667eea;
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
    }
    
    .page-type-icon {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .color-preview {
        display: flex;
        gap: 1rem;
        margin-top: 1rem;
    }
    
    .color-sample {
        width: 50px;
        height: 50px;
        border-radius: 10px;
        border: 2px solid #dee2e6;
    }
    
    .btn-generate {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        color: white;
        padding: 1rem 2rem;
        border-radius: 25px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: transform 0.3s ease;
        width: 100%;
    }
    
    .btn-generate:hover {
        transform: translateY(-2px);
        color: white;
    }
    
    .ai-features {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .loading-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        z-index: 9999;
        justify-content: center;
        align-items: center;
        color: white;
        text-align: center;
    }
    
    .loading-content {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 3rem;
        max-width: 400px;
    }
    
    .spinner {
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top: 4px solid white;
        width: 50px;
        height: 50px;
        animation: spin 1s linear infinite;
        margin: 0 auto 1rem;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block content %}
<div class="ai-page-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <!-- Header -->
                <div class="ai-header">
                    <h1><i class="fas fa-magic me-2"></i>AI Landing Page Generator</h1>
                    <p class="mb-0">Create beautiful, professional landing pages with AI for: <strong>{{ qr_code.name }}</strong></p>
                </div>

                <!-- AI Features -->
                <div class="ai-features">
                    <h3><i class="fas fa-robot me-2"></i>Powered by Advanced AI</h3>
                    <p class="mb-0">Our AI creates stunning, mobile-responsive landing pages tailored to your needs in seconds!</p>
                </div>

                <!-- Form -->
                <div class="ai-card">
                    <form method="post" id="aiPageForm">
                        {% csrf_token %}
                        
                        <!-- Page Type Selection -->
                        <div class="form-group">
                            <label class="form-label"><i class="fas fa-layer-group me-2"></i>{{ form.page_type.label }}</label>
                            <div class="page-type-selector">
                                <div class="page-type-option" data-type="EVENT">
                                    <div class="page-type-icon"><i class="fas fa-calendar-alt"></i></div>
                                    <h6>Event</h6>
                                    <small>Invitations, conferences, parties</small>
                                </div>
                                <div class="page-type-option" data-type="PRODUCT">
                                    <div class="page-type-icon"><i class="fas fa-box"></i></div>
                                    <h6>Product</h6>
                                    <small>Showcase products, features</small>
                                </div>
                                <div class="page-type-option" data-type="MENU">
                                    <div class="page-type-icon"><i class="fas fa-utensils"></i></div>
                                    <h6>Menu</h6>
                                    <small>Restaurant, cafe menus</small>
                                </div>
                                <div class="page-type-option" data-type="BUSINESS">
                                    <div class="page-type-icon"><i class="fas fa-briefcase"></i></div>
                                    <h6>Business</h6>
                                    <small>Business cards, services</small>
                                </div>
                                <div class="page-type-option" data-type="PORTFOLIO">
                                    <div class="page-type-icon"><i class="fas fa-user"></i></div>
                                    <h6>Portfolio</h6>
                                    <small>Resume, personal showcase</small>
                                </div>
                                <div class="page-type-option" data-type="ANNOUNCEMENT">
                                    <div class="page-type-icon"><i class="fas fa-bullhorn"></i></div>
                                    <h6>Announcement</h6>
                                    <small>News, updates, alerts</small>
                                </div>
                                <div class="page-type-option" data-type="CONTACT">
                                    <div class="page-type-icon"><i class="fas fa-address-card"></i></div>
                                    <h6>Contact</h6>
                                    <small>Contact info, location</small>
                                </div>
                                <div class="page-type-option selected" data-type="CUSTOM">
                                    <div class="page-type-icon"><i class="fas fa-magic"></i></div>
                                    <h6>Custom</h6>
                                    <small>Anything you imagine</small>
                                </div>
                            </div>
                            {{ form.page_type }}
                        </div>

                        <!-- Title -->
                        <div class="form-group">
                            <label for="{{ form.title.id_for_label }}" class="form-label">
                                <i class="fas fa-heading me-2"></i>{{ form.title.label }}
                            </label>
                            {{ form.title }}
                            {% if form.title.help_text %}
                            <div class="form-text">{{ form.title.help_text }}</div>
                            {% endif %}
                        </div>

                        <!-- Prompt -->
                        <div class="form-group">
                            <label for="{{ form.prompt.id_for_label }}" class="form-label">
                                <i class="fas fa-comment-dots me-2"></i>{{ form.prompt.label }}
                            </label>
                            {{ form.prompt }}
                            {% if form.prompt.help_text %}
                            <div class="form-text">{{ form.prompt.help_text }}</div>
                            {% endif %}
                        </div>

                        <!-- Colors -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.primary_color.id_for_label }}" class="form-label">
                                        <i class="fas fa-palette me-2"></i>{{ form.primary_color.label }}
                                    </label>
                                    {{ form.primary_color }}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.secondary_color.id_for_label }}" class="form-label">
                                        <i class="fas fa-palette me-2"></i>{{ form.secondary_color.label }}
                                    </label>
                                    {{ form.secondary_color }}
                                </div>
                            </div>
                        </div>

                        <!-- Color Preview -->
                        <div class="color-preview">
                            <div class="color-sample" id="primaryPreview" style="background-color: #667eea;"></div>
                            <div class="color-sample" id="secondaryPreview" style="background-color: #764ba2;"></div>
                            <div style="flex: 1; padding-left: 1rem;">
                                <small class="text-muted">Color preview - your landing page will use these colors</small>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-grid gap-2 mt-4">
                            <button type="submit" class="btn btn-generate">
                                <i class="fas fa-magic me-2"></i>Generate AI Landing Page
                            </button>
                        </div>
                    </form>
                </div>

                <!-- How It Works -->
                <div class="ai-card">
                    <h4><i class="fas fa-info-circle me-2"></i>How It Works</h4>
                    <div class="row">
                        <div class="col-md-4 text-center mb-3">
                            <div class="mb-2">
                                <i class="fas fa-edit fa-3x text-primary"></i>
                            </div>
                            <h6>1. Describe Your Page</h6>
                            <p class="small">Tell our AI what you want your landing page to include</p>
                        </div>
                        <div class="col-md-4 text-center mb-3">
                            <div class="mb-2">
                                <i class="fas fa-robot fa-3x text-success"></i>
                            </div>
                            <h6>2. AI Generates</h6>
                            <p class="small">Advanced AI creates a beautiful, professional page</p>
                        </div>
                        <div class="col-md-4 text-center mb-3">
                            <div class="mb-2">
                                <i class="fas fa-qrcode fa-3x text-info"></i>
                            </div>
                            <h6>3. QR Code Ready</h6>
                            <p class="small">Your QR code now leads to your custom landing page</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div class="loading-overlay" id="loadingOverlay">
    <div class="loading-content">
        <div class="spinner"></div>
        <h4>Creating Your AI Landing Page</h4>
        <p>Our AI is crafting a beautiful, professional landing page just for you...</p>
        <small>This usually takes 10-30 seconds</small>
    </div>
</div>

<script>
// Page type selection
document.querySelectorAll('.page-type-option').forEach(option => {
    option.addEventListener('click', function() {
        // Remove selected class from all options
        document.querySelectorAll('.page-type-option').forEach(opt => opt.classList.remove('selected'));
        
        // Add selected class to clicked option
        this.classList.add('selected');
        
        // Update hidden select field
        document.getElementById('pageType').value = this.dataset.type;
    });
});

// Color preview updates
document.getElementById('primaryColor').addEventListener('change', function() {
    document.getElementById('primaryPreview').style.backgroundColor = this.value;
});

document.getElementById('secondaryColor').addEventListener('change', function() {
    document.getElementById('secondaryPreview').style.backgroundColor = this.value;
});

// Form submission with loading overlay
document.getElementById('aiPageForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Show loading overlay
    document.getElementById('loadingOverlay').style.display = 'flex';
    
    // Submit form
    const formData = new FormData(this);
    
    fetch(this.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Success - redirect to view page
            window.location.href = data.page_url;
        } else {
            // Error
            document.getElementById('loadingOverlay').style.display = 'none';
            alert('Error: ' + (data.error || 'Unknown error occurred'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        document.getElementById('loadingOverlay').style.display = 'none';
        alert('An error occurred while creating your AI landing page');
    });
});
</script>
{% endblock %}
