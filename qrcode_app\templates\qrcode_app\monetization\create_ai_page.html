{% extends "base.html" %}
{% load static %}

{% block title %}Create AI Landing Page - {{ qr_code.name }}{% endblock %}

{% block extra_css %}
<style>
    .ai-page-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .ai-card {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }

    .ai-header {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border-radius: 10px;
        padding: 2rem;
        margin-bottom: 2rem;
        text-align: center;
    }

    .page-type-selector {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .page-type-option {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 1.5rem;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        background: white;
    }

    .page-type-option:hover {
        border-color: #667eea;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .page-type-option.selected {
        border-color: #667eea;
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
    }

    .page-type-icon {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .color-preview {
        display: flex;
        gap: 1rem;
        margin-top: 1rem;
    }

    .color-sample {
        width: 50px;
        height: 50px;
        border-radius: 10px;
        border: 2px solid #dee2e6;
    }

    .btn-generate {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        color: white;
        padding: 1rem 2rem;
        border-radius: 25px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: transform 0.3s ease;
        width: 100%;
    }

    .btn-generate:hover {
        transform: translateY(-2px);
        color: white;
    }

    .ai-features {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        text-align: center;
    }

    .loading-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        z-index: 9999;
        justify-content: center;
        align-items: center;
        color: white;
        text-align: center;
    }

    .loading-content {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 3rem;
        max-width: 400px;
    }

    .spinner {
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top: 4px solid white;
        width: 50px;
        height: 50px;
        animation: spin 1s linear infinite;
        margin: 0 auto 1rem;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-8">
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="text-center mb-12">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-primary-100 rounded-full mb-6">
                <span class="icon-premium icon-primary text-2xl"></span>
            </div>
            <h1 class="text-4xl font-bold text-gray-900 mb-4">AI Landing Page Generator</h1>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto">Create beautiful, professional landing pages with AI for: <strong class="text-primary-600">{{ qr_code.name }}</strong></p>
        </div>

        <!-- AI Features -->
        <div class="card mb-8">
            <div class="card-body text-center">
                <div class="inline-flex items-center justify-center w-12 h-12 bg-accent-100 rounded-lg mb-4">
                    <span class="icon-premium icon-accent text-xl"></span>
                </div>
                <h3 class="text-2xl font-semibold text-gray-900 mb-2">Powered by Advanced AI</h3>
                <p class="text-gray-600">Our AI creates stunning, mobile-responsive landing pages tailored to your needs in seconds!</p>
            </div>
        </div>

        <!-- Form -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-xl font-semibold text-gray-900">Create Your AI Landing Page</h3>
            </div>
            <div class="card-body">
                <form method="post" id="aiPageForm" class="space-y-6">
                    {% csrf_token %}

                    <!-- Page Type Selection -->
                    <div class="form-group">
                        <label class="form-label">
                            <span class="icon-premium icon-primary mr-2"></span>
                            Page Type
                        </label>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                            <div class="page-type-option card cursor-pointer hover:ring-2 hover:ring-primary-200 transition-all" data-type="EVENT">
                                <div class="card-body text-center py-4">
                                    <div class="inline-flex items-center justify-center w-8 h-8 bg-info-100 rounded-lg mb-2">
                                        <span class="icon-notification icon-info"></span>
                                    </div>
                                    <h6 class="font-semibold text-gray-900 text-sm">Event</h6>
                                    <small class="text-gray-500 text-xs">Invitations, conferences</small>
                                </div>
                            </div>
                            <div class="page-type-option card cursor-pointer hover:ring-2 hover:ring-primary-200 transition-all" data-type="PRODUCT">
                                <div class="card-body text-center py-4">
                                    <div class="inline-flex items-center justify-center w-8 h-8 bg-success-100 rounded-lg mb-2">
                                        <span class="icon-premium icon-success"></span>
                                    </div>
                                    <h6 class="font-semibold text-gray-900 text-sm">Product</h6>
                                    <small class="text-gray-500 text-xs">Showcase products</small>
                                </div>
                            </div>
                            <div class="page-type-option card cursor-pointer hover:ring-2 hover:ring-primary-200 transition-all" data-type="MENU">
                                <div class="card-body text-center py-4">
                                    <div class="inline-flex items-center justify-center w-8 h-8 bg-warning-100 rounded-lg mb-2">
                                        <span class="icon-premium icon-warning"></span>
                                    </div>
                                    <h6 class="font-semibold text-gray-900 text-sm">Menu</h6>
                                    <small class="text-gray-500 text-xs">Restaurant menus</small>
                                </div>
                            </div>
                            <div class="page-type-option card cursor-pointer hover:ring-2 hover:ring-primary-200 transition-all" data-type="BUSINESS">
                                <div class="card-body text-center py-4">
                                    <div class="inline-flex items-center justify-center w-8 h-8 bg-primary-100 rounded-lg mb-2">
                                        <span class="icon-user icon-primary"></span>
                                    </div>
                                    <h6 class="font-semibold text-gray-900 text-sm">Business</h6>
                                    <small class="text-gray-500 text-xs">Business cards</small>
                                </div>
                            </div>
                            <div class="page-type-option card cursor-pointer hover:ring-2 hover:ring-primary-200 transition-all" data-type="PORTFOLIO">
                                <div class="card-body text-center py-4">
                                    <div class="inline-flex items-center justify-center w-8 h-8 bg-accent-100 rounded-lg mb-2">
                                        <span class="icon-user icon-accent"></span>
                                    </div>
                                    <h6 class="font-semibold text-gray-900 text-sm">Portfolio</h6>
                                    <small class="text-gray-500 text-xs">Resume, showcase</small>
                                </div>
                            </div>
                            <div class="page-type-option card cursor-pointer hover:ring-2 hover:ring-primary-200 transition-all" data-type="ANNOUNCEMENT">
                                <div class="card-body text-center py-4">
                                    <div class="inline-flex items-center justify-center w-8 h-8 bg-error-100 rounded-lg mb-2">
                                        <span class="icon-notification icon-error"></span>
                                    </div>
                                    <h6 class="font-semibold text-gray-900 text-sm">Announcement</h6>
                                    <small class="text-gray-500 text-xs">News, updates</small>
                                </div>
                            </div>
                            <div class="page-type-option card cursor-pointer hover:ring-2 hover:ring-primary-200 transition-all" data-type="CONTACT">
                                <div class="card-body text-center py-4">
                                    <div class="inline-flex items-center justify-center w-8 h-8 bg-info-100 rounded-lg mb-2">
                                        <span class="icon-user icon-info"></span>
                                    </div>
                                    <h6 class="font-semibold text-gray-900 text-sm">Contact</h6>
                                    <small class="text-gray-500 text-xs">Contact info</small>
                                </div>
                            </div>
                            <div class="page-type-option card cursor-pointer ring-2 ring-primary-500 selected" data-type="CUSTOM">
                                <div class="card-body text-center py-4">
                                    <div class="inline-flex items-center justify-center w-8 h-8 bg-primary-100 rounded-lg mb-2">
                                        <span class="icon-premium icon-primary"></span>
                                    </div>
                                    <h6 class="font-semibold text-gray-900 text-sm">Custom</h6>
                                    <small class="text-gray-500 text-xs">Anything you imagine</small>
                                </div>
                            </div>
                        </div>
                        {{ form.page_type }}
                    </div>

                    <!-- Title -->
                    <div class="form-group">
                        <label for="{{ form.title.id_for_label }}" class="form-label">
                            <span class="icon-edit icon-primary mr-2"></span>
                            {{ form.title.label }}
                        </label>
                        {{ form.title }}
                        {% if form.title.help_text %}
                        <p class="text-sm text-gray-500 mt-1">{{ form.title.help_text }}</p>
                        {% endif %}
                    </div>

                    <!-- Prompt -->
                    <div class="form-group">
                        <label for="{{ form.prompt.id_for_label }}" class="form-label">
                            <span class="icon-analytics icon-primary mr-2"></span>
                            {{ form.prompt.label }}
                        </label>
                        {{ form.prompt }}
                        {% if form.prompt.help_text %}
                        <p class="text-sm text-gray-500 mt-1">{{ form.prompt.help_text }}</p>
                        {% endif %}
                    </div>

                    <!-- Colors -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="form-group">
                            <label for="{{ form.primary_color.id_for_label }}" class="form-label">
                                <span class="icon-premium icon-primary mr-2"></span>
                                {{ form.primary_color.label }}
                            </label>
                            {{ form.primary_color }}
                        </div>
                        <div class="form-group">
                            <label for="{{ form.secondary_color.id_for_label }}" class="form-label">
                                <span class="icon-premium icon-primary mr-2"></span>
                                {{ form.secondary_color.label }}
                            </label>
                            {{ form.secondary_color }}
                        </div>
                    </div>

                    <!-- Color Preview -->
                    <div class="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
                        <div class="w-8 h-8 rounded-lg shadow-sm" id="primaryPreview" style="background-color: #667eea;"></div>
                        <div class="w-8 h-8 rounded-lg shadow-sm" id="secondaryPreview" style="background-color: #764ba2;"></div>
                        <div class="flex-1">
                            <p class="text-sm text-gray-600">Color preview - your landing page will use these colors</p>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="pt-4">
                        <button type="submit" class="btn btn-primary btn-lg w-full icon-button">
                            <span class="icon-premium icon-white"></span>
                            <span>Generate AI Landing Page</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- How It Works -->
        <div class="card">
            <div class="card-header">
                <h4 class="text-xl font-semibold text-gray-900">How It Works</h4>
            </div>
            <div class="card-body">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center">
                        <div class="inline-flex items-center justify-center w-12 h-12 bg-primary-100 rounded-lg mb-4">
                            <span class="icon-edit icon-primary text-xl"></span>
                        </div>
                        <h6 class="font-semibold text-gray-900 mb-2">1. Describe Your Page</h6>
                        <p class="text-sm text-gray-600">Tell our AI what you want your landing page to include</p>
                    </div>
                    <div class="text-center">
                        <div class="inline-flex items-center justify-center w-12 h-12 bg-success-100 rounded-lg mb-4">
                            <span class="icon-premium icon-success text-xl"></span>
                        </div>
                        <h6 class="font-semibold text-gray-900 mb-2">2. AI Generates</h6>
                        <p class="text-sm text-gray-600">Advanced AI creates a beautiful, professional page</p>
                    </div>
                    <div class="text-center">
                        <div class="inline-flex items-center justify-center w-12 h-12 bg-info-100 rounded-lg mb-4">
                            <span class="icon-qr icon-info text-xl"></span>
                        </div>
                        <h6 class="font-semibold text-gray-900 mb-2">3. QR Code Ready</h6>
                        <p class="text-sm text-gray-600">Your QR code now leads to your custom landing page</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div class="loading-overlay" id="loadingOverlay">
    <div class="loading-content">
        <div class="spinner"></div>
        <h4>Creating Your AI Landing Page</h4>
        <p>Our AI is crafting a beautiful, professional landing page just for you...</p>
        <small>This usually takes 10-30 seconds</small>
    </div>
</div>

<script>
// Page type selection
document.querySelectorAll('.page-type-option').forEach(option => {
    option.addEventListener('click', function() {
        // Remove selected class from all options
        document.querySelectorAll('.page-type-option').forEach(opt => opt.classList.remove('selected'));

        // Add selected class to clicked option
        this.classList.add('selected');

        // Update hidden select field
        document.getElementById('pageType').value = this.dataset.type;
    });
});

// Color preview updates
document.getElementById('primaryColor').addEventListener('change', function() {
    document.getElementById('primaryPreview').style.backgroundColor = this.value;
});

document.getElementById('secondaryColor').addEventListener('change', function() {
    document.getElementById('secondaryPreview').style.backgroundColor = this.value;
});

// Form submission with loading overlay
document.getElementById('aiPageForm').addEventListener('submit', function(e) {
    e.preventDefault();

    // Show loading overlay
    document.getElementById('loadingOverlay').style.display = 'flex';

    // Submit form
    const formData = new FormData(this);

    fetch(this.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Success - redirect to view page
            window.location.href = data.page_url;
        } else {
            // Error
            document.getElementById('loadingOverlay').style.display = 'none';
            alert('Error: ' + (data.error || 'Unknown error occurred'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        document.getElementById('loadingOverlay').style.display = 'none';
        alert('An error occurred while creating your AI landing page');
    });
});
</script>
{% endblock %}
