from django.urls import path, include
from django.shortcuts import render
from . import views, enterprise_views

urlpatterns = [
    path('', views.index, name='index'),
    path('generate/', views.generate_qr_code, name='generate_qr_code'),
    path('qr-codes/', views.qr_code_list, name='qr_code_list'),
    path('qr-codes/<int:pk>/', views.qr_code_detail, name='qr_code_detail'),
    path('qr-codes/<int:pk>/delete/', views.delete_qr_code, name='delete_qr_code'),
    path('qr/<uuid:unique_id>/', views.qr_landing, name='qr_landing'),
    path('user-management/', views.user_management, name='user_management'),
    path('user-management/add/', views.add_user, name='add_user'),
    path('user-management/edit/<int:user_id>/', views.edit_user, name='edit_user'),
    path('user-management/delete/<int:user_id>/', views.delete_user, name='delete_user'),
    path('role-management/', views.role_management, name='role_management'),
    path('api-keys/', views.api_keys, name='api_keys'),
    path('settings/', views.settings_view, name='settings'),
    path('performance-dashboard/', views.performance_dashboard, name='performance_dashboard'),
    path('batch-processing/', views.batch_processing, name='batch_processing'),
    path('batch-processing/<int:pk>/', views.batch_detail, name='batch_detail'),
    path('login/', views.custom_login, name='custom_login'),
    path('simple-login/', views.simple_login, name='simple_login'),
    path('dev-logout/', views.dev_logout, name='dev_logout'),
    path('user-switcher/', views.user_switcher, name='user_switcher'),
    path('test-login-as/', views.test_login_as, name='test_login_as'),
    path('quick-access/', lambda request: render(request, 'testing/quick_access.html'), name='quick_access'),

    # Solution pages
    path('solutions/corporate/', views.corporate_solution, name='corporate_solution'),
    path('solutions/retail/', views.retail_solution, name='retail_solution'),
    path('solutions/hospitality/', views.hospitality_solution, name='hospitality_solution'),
    path('solutions/education/', views.education_solution, name='education_solution'),
    path('solutions/events/', views.events_solution, name='events_solution'),
    path('solutions/marketing/', views.marketing_solution, name='marketing_solution'),

    # Test pages
    path('new-navbar-test/', views.new_navbar_test, name='new_navbar_test'),
    path('dropdown-test/', lambda request: render(request, 'dropdown_test.html'), name='dropdown_test'),
    path('minimal-dropdown-test/', lambda request: render(request, 'minimal_dropdown_test.html'), name='minimal_dropdown_test'),
    path('debug-user-info/', views.debug_user_info, name='debug_user_info'),
    path('test-simple/', views.test_simple, name='test_simple'),

    # Premium feature pages
    path('premium/bulk-generation/', views.bulk_generation, name='bulk_generation'),
    path('premium/analytics/', views.advanced_analytics, name='advanced_analytics'),
    path('premium/encrypted/', views.encrypted_qr, name='encrypted_qr'),
    path('premium/hosting/', views.dedicated_hosting, name='dedicated_hosting'),
    path('premium/support/', views.priority_support, name='priority_support'),

    # Pricing and checkout
    path('pricing/', views.pricing, name='pricing'),
    path('checkout/', views.checkout, name='checkout'),
    path('checkout/success/', views.checkout_success, name='checkout_success'),

    # Enterprise features
    path('enterprise/', enterprise_views.enterprise_dashboard, name='enterprise_dashboard'),
    path('enterprise/heatmap-data/', enterprise_views.scan_heatmap_data, name='scan_heatmap_data'),
    path('enterprise/branding/', enterprise_views.branding_management, name='branding_management'),
    path('enterprise/short-links/', enterprise_views.short_links_management, name='short_links_management'),
    path('enterprise/admin/approval/', enterprise_views.admin_approval_dashboard, name='admin_approval_dashboard'),
    path('enterprise/admin/approve-qr/<int:qr_id>/', enterprise_views.approve_qr_code, name='approve_qr_code'),
    path('enterprise/admin/reject-qr/<int:qr_id>/', enterprise_views.reject_qr_code, name='reject_qr_code'),

    # Bonus Enterprise features
    path('enterprise/country-analytics/', enterprise_views.country_analytics_dashboard, name='country_analytics_dashboard'),

    # QR Scan Analytics Dashboard (Admin-only)
    path('qr-admin-dashboard/', views.scan_analytics_dashboard, name='qr_dashboard'),

    # QR Scan Map View (Admin-only)
    path('qr-admin-map/', views.scan_map_view, name='qr_map'),
    path('qr-admin-map-data/', views.scan_map_data, name='qr_map_data'),

    # Monetization Features (Premium)
    path('monetization/', include('qrcode_app.monetization_urls')),

    # MODULE 2: Dynamic QR Redirect (Short URLs)
    path('r/<str:code>/', views.dynamic_qr_redirect, name='dynamic_qr_redirect'),

    # MODULE 3: AI Landing Pages
    path('ai/<str:code>/', views.serve_ai_landing, name='serve_ai_landing'),

    # API endpoints
    path('api/user/subscription-status/', views.subscription_status_api, name='subscription_status_api'),

    # QR landing page (must be last to avoid conflicts)
    path('qr/<uuid:unique_id>/', views.qr_landing, name='qr_landing'),
]
