/* 
 * Footer Margin Fix
 * This CSS file specifically addresses the inconsistent margin-top on the footer
 * between user and admin enterprise dashboards
 */

/* Override the default margin-top from enterprise-footer.css for all pages */
.enterprise-footer {
    margin-top: 0 !important;
}

/* Ensure proper spacing after content when needed */
.corporate-section {
    padding-bottom: 4rem;
}

/* But remove extra padding when inside enterprise dashboard */
.enterprise-dashboard .corporate-section {
    padding-bottom: 0;
}

/* Fix for any other containers that might need spacing */
.container:last-of-type:not(.enterprise-dashboard):not(.enterprise-dashboard *) {
    margin-bottom: 4rem;
}

/* Ensure enterprise dashboard takes full height */
.enterprise-dashboard {
    min-height: calc(100vh - 65px);
    display: flex;
    flex-direction: column;
}

/* Fix for admin tab content */
#admin.tab-pane {
    margin-bottom: 0 !important;
}

/* Fix for any nested containers in admin tab */
#admin .dashboard-section:last-child {
    margin-bottom: 0 !important;
}

/* Mobile adjustments */
@media (max-width: 991.98px) {
    .corporate-section {
        padding-bottom: 2rem;
    }
    
    .container:last-of-type:not(.enterprise-dashboard):not(.enterprise-dashboard *) {
        margin-bottom: 2rem;
    }
}
