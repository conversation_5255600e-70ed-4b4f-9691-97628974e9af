# Generated by Django 5.1.7 on 2025-05-28 14:32

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('qrcode_app', '0005_merge_20250528_1323'),
    ]

    operations = [
        migrations.AddField(
            model_name='qrcode',
            name='original_url',
            field=models.TextField(blank=True, help_text='Original destination URL before landing page', null=True),
        ),
        migrations.AddField(
            model_name='qrcode',
            name='unique_id',
            field=models.UUIDField(default=uuid.uuid4, help_text='Unique identifier for QR landing page'),
        ),
        migrations.AddField(
            model_name='qrcodescan',
            name='city',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='qrcodescan',
            name='country',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='qrcodescan',
            name='latitude',
            field=models.DecimalField(blank=True, decimal_places=7, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='qrcodescan',
            name='longitude',
            field=models.DecimalField(blank=True, decimal_places=7, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='qrcodescan',
            name='postal_code',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='qrcodescan',
            name='region',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='qrcodescan',
            name='scanner_type',
            field=models.CharField(choices=[('native_camera', 'Native Camera'), ('third_party_app', 'Third Party App'), ('unknown', 'Unknown')], default='unknown', max_length=20),
        ),
        migrations.AddField(
            model_name='qrcodescan',
            name='timezone',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
    ]
