{% extends 'base.html' %}
{% load static %}

{% block title %}Payment Successful{% endblock %}

{% block extra_css %}
<style>
.success-section {
    padding: 4rem 0;
    text-align: center;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    min-height: 60vh;
    display: flex;
    align-items: center;
}

.success-content {
    max-width: 600px;
    margin: 0 auto;
}

.success-icon {
    font-size: 5rem;
    margin-bottom: 2rem;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-30px);
    }
    60% {
        transform: translateY(-15px);
    }
}

.success-title {
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 1rem;
}

.success-message {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.success-details {
    background: rgba(255,255,255,0.1);
    border-radius: 15px;
    padding: 2rem;
    margin: 2rem 0;
    backdrop-filter: blur(10px);
}

.detail-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255,255,255,0.2);
}

.detail-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.detail-label {
    font-weight: 500;
    opacity: 0.8;
}

.detail-value {
    font-weight: bold;
}

.success-actions {
    margin-top: 3rem;
}

.btn-success-action {
    background: white;
    color: #28a745;
    border: none;
    padding: 1rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    font-size: 1.1rem;
    margin: 0.5rem;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-success-action:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(255,255,255,0.3);
    color: #28a745;
}

.btn-secondary-action {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.btn-secondary-action:hover {
    background: white;
    color: #28a745;
}

.next-steps {
    background: white;
    color: #333;
    padding: 3rem 0;
}

.next-steps-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.next-steps h3 {
    font-size: 2rem;
    margin-bottom: 2rem;
    color: #333;
}

.steps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.step-card {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    transition: transform 0.3s ease;
}

.step-card:hover {
    transform: translateY(-5px);
}

.step-icon {
    font-size: 3rem;
    color: #28a745;
    margin-bottom: 1rem;
}

.step-title {
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 1rem;
    color: #333;
}

.step-description {
    color: #6c757d;
    line-height: 1.6;
}

.step-link {
    display: inline-block;
    margin-top: 1rem;
    color: #28a745;
    text-decoration: none;
    font-weight: 500;
}

.step-link:hover {
    text-decoration: underline;
}
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-success-50 to-primary-50 py-12">
    <div class="container">
        <div class="max-w-2xl mx-auto text-center">
            <div class="mb-8">
                <div class="inline-flex items-center justify-center w-24 h-24 bg-success-500 rounded-full mb-6 animate-bounce">
                    <span class="icon-success icon-white text-4xl"></span>
                </div>
            </div>

            <h1 class="text-5xl font-bold text-gray-900 mb-6">Payment Successful!</h1>

            <p class="text-xl text-gray-600 mb-8">
                🎉 Congratulations! Your subscription has been activated and you now have access to all premium features.
            </p>

            {% if session %}
            <div class="card max-w-md mx-auto mb-8">
                <div class="card-body">
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Payment Status:</span>
                            <span class="badge badge-success">✅ Completed</span>
                        </div>
                        {% if subscription_id %}
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Subscription ID:</span>
                            <span class="text-sm font-mono text-gray-800">{{ subscription_id|truncatechars:20 }}</span>
                        </div>
                        {% endif %}
                        {% if customer_id %}
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Customer ID:</span>
                            <span class="text-sm font-mono text-gray-800">{{ customer_id|truncatechars:20 }}</span>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}

            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{% url 'dashboard' %}" class="btn btn-primary btn-lg icon-button">
                    <span class="icon-dashboard icon-white"></span>
                    <span>Go to Dashboard</span>
                </a>
                <a href="{% url 'billing_portal' %}" class="btn btn-secondary btn-lg icon-button">
                    <span class="icon-settings icon-primary"></span>
                    <span>Manage Billing</span>
                </a>
            </div>
        </div>
    </div>
</div>

<div class="py-16 bg-gray-50">
    <div class="container">
        <div class="text-center mb-12">
            <h3 class="text-3xl font-bold text-gray-900 mb-4">What's Next?</h3>
            <p class="text-lg text-gray-600">Now that you have premium access, here's what you can do:</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
            <div class="card">
                <div class="card-body text-center">
                    <div class="inline-flex items-center justify-center w-12 h-12 bg-primary-100 rounded-lg mb-4">
                        <span class="icon-qr icon-primary text-xl"></span>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">Create QR Codes</h4>
                    <p class="text-gray-600 mb-4">
                        Generate unlimited QR codes with advanced customization options and branding.
                    </p>
                    <a href="{% url 'generate_qr_code' %}" class="btn btn-primary btn-sm icon-button">
                        <span class="icon-generate icon-white"></span>
                        <span>Create QR Code</span>
                    </a>
                </div>
            </div>

            <div class="card">
                <div class="card-body text-center">
                    <div class="inline-flex items-center justify-center w-12 h-12 bg-accent-100 rounded-lg mb-4">
                        <span class="icon-premium icon-accent text-xl"></span>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">AI Landing Pages</h4>
                    <p class="text-gray-600 mb-4">
                        Create professional landing pages automatically with our AI-powered system.
                    </p>
                    <a href="{% url 'dashboard' %}" class="btn btn-accent btn-sm icon-button">
                        <span class="icon-premium icon-white"></span>
                        <span>Try AI Pages</span>
                    </a>
                </div>
            </div>

            <div class="card">
                <div class="card-body text-center">
                    <div class="inline-flex items-center justify-center w-12 h-12 bg-info-100 rounded-lg mb-4">
                        <span class="icon-api icon-info text-xl"></span>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">Webhook Integration</h4>
                    <p class="text-gray-600 mb-4">
                        Connect your QR scans to Zapier, CRM systems, and automation platforms.
                    </p>
                    <a href="{% url 'webhook_dashboard' %}" class="btn btn-secondary btn-sm icon-button">
                        <span class="icon-api icon-primary"></span>
                        <span>Setup Webhooks</span>
                    </a>
                </div>
            </div>

            <div class="card">
                <div class="card-body text-center">
                    <div class="inline-flex items-center justify-center w-12 h-12 bg-warning-100 rounded-lg mb-4">
                        <span class="icon-notification icon-warning text-xl"></span>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">Scan Alerts</h4>
                    <p class="text-gray-600 mb-4">
                        Get notified when your QR codes are scanned from specific locations or devices.
                    </p>
                    <a href="{% url 'dashboard' %}" class="btn btn-secondary btn-sm icon-button">
                        <span class="icon-notification icon-primary"></span>
                        <span>Create Alerts</span>
                    </a>
                </div>
            </div>

            <div class="card">
                <div class="card-body text-center">
                    <div class="inline-flex items-center justify-center w-12 h-12 bg-success-100 rounded-lg mb-4">
                        <span class="icon-analytics icon-success text-xl"></span>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">Advanced Analytics</h4>
                    <p class="text-gray-600 mb-4">
                        Track detailed scan analytics with geographic data and device insights.
                    </p>
                    <a href="{% url 'qr_map' %}" class="btn btn-secondary btn-sm icon-button">
                        <span class="icon-analytics icon-primary"></span>
                        <span>View Analytics</span>
                    </a>
                </div>
            </div>

            <div class="card">
                <div class="card-body text-center">
                    <div class="inline-flex items-center justify-center w-12 h-12 bg-primary-100 rounded-lg mb-4">
                        <span class="icon-premium icon-primary text-xl"></span>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">Priority Support</h4>
                    <p class="text-gray-600 mb-4">
                        Get priority customer support and assistance with your QR code campaigns.
                    </p>
                    <a href="mailto:<EMAIL>" class="btn btn-secondary btn-sm icon-button">
                        <span class="icon-premium icon-primary"></span>
                        <span>Contact Support</span>
                    </a>
                </div>
            </div>
        </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-redirect to dashboard after 10 seconds
setTimeout(function() {
    const redirectBtn = document.querySelector('a[href*="dashboard"]');
    if (redirectBtn && !document.hidden) {
        // Show countdown
        let countdown = 5;
        const originalText = redirectBtn.innerHTML;

        const countdownInterval = setInterval(function() {
            redirectBtn.innerHTML = `<i class="fas fa-tachometer-alt me-2"></i>Go to Dashboard (${countdown}s)`;
            countdown--;

            if (countdown < 0) {
                clearInterval(countdownInterval);
                window.location.href = redirectBtn.href;
            }
        }, 1000);
    }
}, 5000);
</script>
{% endblock %}
