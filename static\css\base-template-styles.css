/* Base Template Styles */

/* Body styles */
.corporate-body {
    margin: 0;
    padding: 0;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Main content area */
main.fade-in {
    flex: 1 0 auto;
    display: flex;
    flex-direction: column;
}

/* Footer styles */
.enterprise-footer {
    flex-shrink: 0;
}

/* Animation delay styles for nav items */
.nav-item.animate__animated[data-delay="0.1s"] {
    animation-delay: 0.1s;
}

.nav-item.animate__animated[data-delay="0.15s"] {
    animation-delay: 0.15s;
}

.nav-item.animate__animated[data-delay="0.2s"] {
    animation-delay: 0.2s;
}

.nav-item.animate__animated[data-delay="0.3s"] {
    animation-delay: 0.3s;
}

.nav-item.animate__animated[data-delay="0.4s"] {
    animation-delay: 0.4s;
}

.nav-item.animate__animated[data-delay="0.5s"] {
    animation-delay: 0.5s;
}

.nav-item.animate__animated[data-delay="0.55s"] {
    animation-delay: 0.55s;
}

.nav-item.animate__animated[data-delay="0.6s"] {
    animation-delay: 0.6s;
}

/* VPN modal styles */
.vpn-status.coming-soon {
    background-color: rgba(79, 70, 229, 0.1);
}

.vpn-icon.coming-soon {
    background-color: rgba(79, 70, 229, 0.2);
    color: #4F46E5;
}

.vpn-activate-btn.notify {
    background: linear-gradient(135deg, #4F46E5, #6366F1);
}

.vpn-activate-btn.confirmed {
    background: linear-gradient(135deg, #10B981, #059669);
}

/* Responsive styles */
@media (max-width: 767.98px) {
    /* Mobile specific styles */
}
