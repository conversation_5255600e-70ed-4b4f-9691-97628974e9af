/**
 * Ad Creation Enterprise CSS
 * Styles for the enterprise-grade ad creation experience
 */

/* Notification Styles */
.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: #ff5252;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    border: 2px solid #1a237e;
}

/* Enterprise Ad Creation Styles */
.enterprise-dashboard {
    background-color: #f8f9fa;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    margin-bottom: 0; /* Ensure no margin at the bottom */
}

.dashboard-header {
    background: linear-gradient(135deg, #1a237e 0%, #283593 100%);
    padding: 20px 0;
    color: white;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 10;
}

/* Glossy Header Effect */
.dashboard-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 40%;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
    pointer-events: none;
}

.dashboard-card {
    background: linear-gradient(to bottom, #ffffff, #f9f9f9);
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    margin-bottom: 30px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

/* Glossy Card Effect */
.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 30%;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0));
    pointer-events: none;
    z-index: 1;
    opacity: 0.5;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
}

.dashboard-card:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    z-index: 2;
    background: linear-gradient(to right, rgba(26, 35, 126, 0.05), rgba(26, 35, 126, 0.01));
}

.card-title {
    font-size: 18px;
    font-weight: 700;
    color: #1a237e;
    margin: 0;
    position: relative;
}

/* Form Styling */
.form-label {
    font-weight: 600;
    color: #1a237e;
    margin-bottom: 8px;
}

.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    padding: 12px 15px;
    background-color: #fff;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
}

.form-control:focus, .form-select:focus {
    border-color: #3949ab;
    box-shadow: 0 0 0 3px rgba(57, 73, 171, 0.1);
}

/* Tab Navigation */
.nav-tabs {
    border-bottom: none;
    margin-bottom: 0;
}

.nav-tabs .nav-link {
    border: none;
    border-radius: 8px 8px 0 0;
    padding: 12px 20px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.8);
    background-color: rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    margin-right: 5px;
}

.nav-tabs .nav-link:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.2);
}

.nav-tabs .nav-link.active {
    color: #1a237e;
    background-color: white;
    font-weight: 700;
}

/* Button Styling */
.btn-primary {
    background: linear-gradient(135deg, #1a237e, #3949ab);
    border: none;
    box-shadow: 0 4px 10px rgba(26, 35, 126, 0.2);
    transition: all 0.3s ease;
    font-weight: 600;
    padding: 10px 20px;
    border-radius: 8px;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #3949ab, #1a237e);
    box-shadow: 0 6px 15px rgba(26, 35, 126, 0.3);
    transform: translateY(-2px);
}

.btn-secondary {
    background: linear-gradient(135deg, #757575, #9e9e9e);
    border: none;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    font-weight: 600;
    padding: 10px 20px;
    border-radius: 8px;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #9e9e9e, #757575);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.btn-success {
    background: linear-gradient(135deg, #2e7d32, #4caf50);
    border: none;
    box-shadow: 0 4px 10px rgba(46, 125, 50, 0.2);
    transition: all 0.3s ease;
    font-weight: 600;
    padding: 10px 20px;
    border-radius: 8px;
}

.btn-success:hover {
    background: linear-gradient(135deg, #4caf50, #2e7d32);
    box-shadow: 0 6px 15px rgba(46, 125, 50, 0.3);
    transform: translateY(-2px);
}

/* Select2 Styling */
.select2-container--default .select2-selection--single {
    height: 48px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 48px;
    padding-left: 15px;
    color: #333;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 46px;
    right: 10px;
}

.select2-dropdown {
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.select2-search__field {
    padding: 10px 15px !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
    border-radius: 8px !important;
}

.select2-results__option {
    padding: 10px 15px;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #1a237e !important;
}

/* Ad Preview Styles */
.ad-preview-container {
    border-radius: 12px;
    padding: 20px;
    background: linear-gradient(to bottom, #f8f9fa, #f0f2f5);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
    margin-top: 15px;
    position: relative;
    overflow: hidden;
}

.ad-preview-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 30%;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0));
    pointer-events: none;
    z-index: 1;
}

.ad-preview-location-label {
    font-weight: 700;
    margin-bottom: 15px;
    color: #1a237e;
    text-align: center;
    position: relative;
    z-index: 2;
}

.ad-preview-wrapper {
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;
    background-color: white;
    margin: 0 auto;
    position: relative;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.ad-preview-wrapper:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}
