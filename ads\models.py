from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from django.utils.text import slugify
from decimal import Decimal
import uuid

class AdType(models.Model):
    """
    Model for different types of advertisements with base pricing
    """
    name = models.CharField(max_length=100)
    description = models.TextField()
    base_price = models.DecimalField(max_digits=10, decimal_places=2)
    is_active = models.BooleanField(default=True)
    is_premium = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

class AdLocation(models.Model):
    """
    Model for different advertisement placement locations with price multipliers
    """
    VISIBILITY_CHOICES = (
        ('high', 'High Visibility'),
        ('medium', 'Medium Visibility'),
        ('low', 'Low Visibility'),
    )

    name = models.CharField(max_length=100)
    description = models.TextField()
    price_multiplier = models.DecimalField(max_digits=5, decimal_places=2, default=1.0,
                                          help_text="Multiplier applied to base price (e.g., 1.5 = +50%)")
    visibility = models.CharField(max_length=20, choices=VISIBILITY_CHOICES, default='medium')
    is_premium = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    daily_impressions = models.PositiveIntegerField(default=0,
                                                  help_text="Estimated daily impressions for this location")
    image = models.ImageField(upload_to='ad_locations/', blank=True, null=True,
                             help_text="Preview image of this ad location")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def get_adjusted_price(self, base_price):
        """Calculate the adjusted price based on the location multiplier"""
        return Decimal(base_price) * Decimal(self.price_multiplier)

    def __str__(self):
        return f"{self.name} ({self.visibility} visibility)"

class Ad(models.Model):
    """
    Main advertisement model with all details
    """
    STATUS_CHOICES = (
        ('draft', 'Draft'),
        ('pending', 'Pending Approval'),
        ('approved', 'Approved'),
        ('active', 'Active'),
        ('paused', 'Paused'),
        ('rejected', 'Rejected'),
        ('expired', 'Expired'),
    )

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='ads')
    ad_type = models.ForeignKey(AdType, on_delete=models.CASCADE, related_name='ads')
    campaign = models.ForeignKey('Campaign', on_delete=models.SET_NULL, null=True, blank=True, related_name='ads')
    title = models.CharField(max_length=200)
    content = models.TextField()
    media = models.FileField(upload_to='ads/', blank=True, null=True)
    cta_link = models.URLField(blank=True, null=True)
    # Renamed from 'location' to 'target_location' to avoid confusion with ad_location
    target_location = models.CharField(max_length=200, blank=True, null=True,
                                     help_text="Geographic location targeting")
    ad_location = models.ForeignKey(AdLocation, on_delete=models.SET_NULL,
                                   related_name='ads', null=True, blank=True,
                                   help_text="Placement location on the website")
    target_audience = models.CharField(max_length=200, blank=True, null=True)
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    requires_ai = models.BooleanField(default=False)
    used_ai = models.BooleanField(default=False, help_text="Whether AI was actually used in the final content")
    ai_language = models.CharField(max_length=10, null=True, blank=True, help_text="Language used for AI generation")
    ai_suggestion = models.JSONField(null=True, blank=True, help_text="The selected AI suggestion data")
    wants_social = models.BooleanField(default=False)
    base_pricing = models.DecimalField(max_digits=10, decimal_places=2,
                                     help_text="Base price before location multiplier")
    final_pricing = models.DecimalField(max_digits=10, decimal_places=2,
                                      help_text="Final price after all adjustments")
    impressions = models.PositiveIntegerField(default=0)
    clicks = models.PositiveIntegerField(default=0)
    conversion_count = models.PositiveIntegerField(default=0)
    rejection_reason = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    slug = models.SlugField(max_length=250, unique=True, blank=True)

    def save(self, *args, **kwargs):
        # Generate slug if not present
        if not self.slug:
            self.slug = slugify(self.title)
            # Ensure uniqueness by adding a timestamp if needed
            if Ad.objects.filter(slug=self.slug).exists():
                self.slug = f"{self.slug}-{int(timezone.now().timestamp())}"

        # Calculate final pricing based on location multiplier
        if self.ad_location and hasattr(self, 'base_pricing'):
            self.final_pricing = self.ad_location.get_adjusted_price(self.base_pricing)
        else:
            # If no location selected, final price equals base price
            if hasattr(self, 'base_pricing'):
                self.final_pricing = self.base_pricing

        super().save(*args, **kwargs)

    def __str__(self):
        return self.title

    class Meta:
        ordering = ['-created_at']

class Transaction(models.Model):
    """
    Payment records for advertisements
    """
    STATUS_CHOICES = (
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('paid', 'Paid'),
        ('failed', 'Failed'),
        ('refunded', 'Refunded'),
    )

    PAYMENT_GATEWAY_CHOICES = (
        ('mpesa', 'M-PESA'),
        ('card', 'Credit/Debit Card'),
        ('bank', 'Bank Transfer'),
        ('paypal', 'PayPal'),
    )

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='ad_transactions')
    ad = models.ForeignKey(Ad, on_delete=models.CASCADE, related_name='transactions')
    transaction_id = models.CharField(max_length=100, blank=True, null=True)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    payment_gateway = models.CharField(max_length=20, choices=PAYMENT_GATEWAY_CHOICES)
    payment_details = models.JSONField(blank=True, null=True)
    timestamp = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Transaction {self.id} - {self.ad.title}"

class Campaign(models.Model):
    """
    Campaign model for grouping ads
    """
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('paused', 'Paused'),
        ('completed', 'Completed'),
        ('draft', 'Draft'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='campaigns')
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    budget = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    target_audience = models.CharField(max_length=255, blank=True)
    target_location = models.CharField(max_length=255, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    slug = models.SlugField(max_length=255, unique=True, blank=True)

    class Meta:
        verbose_name = 'Campaign'
        verbose_name_plural = 'Campaigns'
        ordering = ['-created_at']

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        # Generate slug if not provided
        if not self.slug:
            self.slug = slugify(f"{self.name}-{uuid.uuid4().hex[:8]}")
        super().save(*args, **kwargs)

    @property
    def total_impressions(self):
        return sum(ad.impressions for ad in self.ads.all())

    @property
    def total_clicks(self):
        return sum(ad.clicks for ad in self.ads.all())

    @property
    def ctr(self):
        if self.total_impressions > 0:
            return (self.total_clicks / self.total_impressions) * 100
        return 0

    @property
    def ads_count(self):
        return self.ads.count()

    @property
    def active_ads_count(self):
        return self.ads.filter(status='active').count()

class AdAnalytics(models.Model):
    """
    Performance tracking for advertisements
    """
    ad = models.ForeignKey(Ad, on_delete=models.CASCADE, related_name='analytics')
    date = models.DateField()
    impressions = models.PositiveIntegerField(default=0)
    clicks = models.PositiveIntegerField(default=0)
    unique_views = models.PositiveIntegerField(default=0)
    conversion_count = models.PositiveIntegerField(default=0)
    device_data = models.JSONField(blank=True, null=True)
    location_data = models.JSONField(blank=True, null=True)
    browser_data = models.JSONField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Analytics for {self.ad.title} on {self.date}"

    @property
    def ctr(self):
        """Calculate Click-Through Rate"""
        if self.impressions > 0:
            return (self.clicks / self.impressions) * 100
        return 0

    @property
    def conversion_rate(self):
        """Calculate Conversion Rate"""
        if self.clicks > 0:
            return (self.conversion_count / self.clicks) * 100
        return 0

    class Meta:
        verbose_name_plural = "Ad Analytics"
        unique_together = ('ad', 'date')

class CampaignAnalytics(models.Model):
    """
    Analytics data for campaigns
    """
    campaign = models.ForeignKey(Campaign, on_delete=models.CASCADE, related_name='analytics')
    date = models.DateField()
    impressions = models.PositiveIntegerField(default=0)
    clicks = models.PositiveIntegerField(default=0)
    unique_views = models.PositiveIntegerField(default=0)
    conversion_count = models.PositiveIntegerField(default=0)
    device_data = models.JSONField(blank=True, null=True)
    location_data = models.JSONField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Campaign Analytics'
        verbose_name_plural = 'Campaign Analytics'
        unique_together = ('campaign', 'date')

    def __str__(self):
        return f"{self.campaign.name} - {self.date}"

class AiFeedback(models.Model):
    """
    User feedback on AI-generated content for monitoring and improvement
    """
    FEEDBACK_CHOICES = (
        ('like', 'Like'),
        ('dislike', 'Dislike'),
        ('neutral', 'Neutral'),
    )

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='ai_feedback')
    ad = models.ForeignKey(Ad, on_delete=models.SET_NULL, null=True, blank=True, related_name='ai_feedback')
    suggestion_data = models.JSONField(help_text="The AI suggestion that received feedback")
    feedback = models.CharField(max_length=10, choices=FEEDBACK_CHOICES)
    comments = models.TextField(blank=True, null=True, help_text="Optional user comments about the suggestion")
    model_used = models.CharField(max_length=50, blank=True, null=True, help_text="The AI model that generated the content")
    language = models.CharField(max_length=20, blank=True, null=True)
    page = models.CharField(max_length=255, blank=True, null=True, help_text="The page where feedback was given")
    processing_time = models.FloatField(default=0, help_text="Time taken to generate the suggestion in seconds")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = 'AI Feedback'
        verbose_name_plural = 'AI Feedback'
        ordering = ['-created_at']

    def __str__(self):
        return f"Feedback from {self.user.username} - {self.feedback} - {self.created_at.strftime('%Y-%m-%d')}"
