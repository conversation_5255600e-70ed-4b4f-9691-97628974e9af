# Generated by Django 5.1.7 on 2025-05-29 08:15

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('qrcode_app', '0016_add_plan_subscription_models'),
    ]

    operations = [
        migrations.CreateModel(
            name='StripeProduct',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stripe_product_id', models.CharField(help_text='Stripe Product ID', max_length=100)),
                ('stripe_price_id', models.Char<PERSON>ield(help_text='Stripe Price ID for monthly billing', max_length=100)),
                ('stripe_yearly_price_id', models.CharField(blank=True, help_text='Stripe Price ID for yearly billing', max_length=100)),
                ('is_active', models.BooleanField(default=True, help_text='Is this Stripe product active')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('plan', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='stripe_product', to='qrcode_app.plan')),
            ],
            options={
                'verbose_name': 'Stripe Product',
                'verbose_name_plural': 'Stripe Products',
                'ordering': ['plan__sort_order'],
            },
        ),
    ]
