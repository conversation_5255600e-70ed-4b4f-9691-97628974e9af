from django.core.management.base import BaseCommand
from ads.models import Ad

class Command(BaseCommand):
    help = 'Checks the created ads and their pricing'

    def handle(self, *args, **kwargs):
        self.stdout.write(self.style.SUCCESS('Checking Created Ads'))
        self.stdout.write('-' * 50)
        
        # Get all ads
        ads = Ad.objects.all()
        
        if not ads:
            self.stdout.write(self.style.ERROR('No ads found.'))
            return
            
        # Print header
        self.stdout.write(f"{'Title':<30} {'Ad Type':<15} {'Location':<20} {'Base Price':<15} {'Final Price':<15}")
        self.stdout.write('-' * 95)
        
        # Check each ad
        for ad in ads:
            title = ad.title[:27] + '...' if len(ad.title) > 30 else ad.title
            ad_type = ad.ad_type.name if ad.ad_type else 'None'
            location = ad.ad_location.name if ad.ad_location else 'None'
            base_price = ad.base_pricing
            final_price = ad.final_pricing
            
            self.stdout.write(
                f"{title:<30} "
                f"{ad_type:<15} "
                f"{location:<20} "
                f"{base_price:<15} "
                f"{final_price:<15}"
            )
        
        self.stdout.write('-' * 95)
        self.stdout.write(self.style.SUCCESS(f'Total ads: {ads.count()}'))
