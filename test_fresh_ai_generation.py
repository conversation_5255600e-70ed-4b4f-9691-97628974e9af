#!/usr/bin/env python
"""
Test Fresh AI Generation - Clear cache and test Smart AI Engine
"""

import os
import sys
import django
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'qrcode_project.settings')
django.setup()

import logging
import json
import time
import hashlib
import shutil
from ai_services.clients import SmartAIEngine, get_ai_client
from ai_services.cache_utils import get_cached_suggestions, cache_suggestions
from ai_services.settings import AI_PROVIDER, GROQ_API_KEY, MISTRAL_API_KEY

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def clear_cache():
    """Clear the cache directory"""
    cache_dir = Path(__file__).parent / 'ai_services' / 'cache'
    if cache_dir.exists():
        try:
            shutil.rmtree(cache_dir)
            print(f"   ✅ Cleared cache directory: {cache_dir}")
            return True
        except Exception as e:
            print(f"   ❌ Failed to clear cache: {e}")
            return False
    else:
        print(f"   ℹ️  Cache directory doesn't exist: {cache_dir}")
        return True

def test_fresh_ai_generation():
    """Test fresh AI generation with cleared cache"""
    print("🧠 TESTING FRESH AI GENERATION")
    print("=" * 50)
    
    # Test parameters - using different parameters to avoid cache
    test_params = {
        'language': 'english',
        'business_type': 'Tech Startup',
        'target_audience': 'Entrepreneurs',
        'tone': 'professional',
        'num_suggestions': 3,
        'ad_title': 'Innovation Hub'
    }
    
    print(f"📋 Test Parameters:")
    for key, value in test_params.items():
        print(f"   {key}: {value}")
    print()
    
    # Check configuration
    print(f"⚙️  Configuration:")
    print(f"   AI_PROVIDER: {AI_PROVIDER}")
    print(f"   GROQ_API_KEY: {'✅ Set' if GROQ_API_KEY else '❌ Missing'}")
    print(f"   MISTRAL_API_KEY: {'✅ Set' if MISTRAL_API_KEY else '❌ Missing'}")
    print()
    
    # Clear cache
    print("🧹 Step 1: Clear Cache")
    if clear_cache():
        print("   ✅ Cache cleared successfully")
    else:
        print("   ❌ Failed to clear cache")
        return False
    print()
    
    # Test AI client factory
    print("🏭 Step 2: Test AI Client Factory")
    try:
        ai_client = get_ai_client()
        client_type = type(ai_client).__name__
        print(f"   ✅ get_ai_client() returned: {client_type}")
        if client_type == 'SmartAIEngine':
            print("   🎯 Correctly using SmartAIEngine!")
        else:
            print(f"   ⚠️  Using {client_type} instead of SmartAIEngine")
    except Exception as e:
        print(f"   ❌ Failed to get AI client: {e}")
        return False
    print()
    
    # Initialize Smart AI Engine directly
    print("🔧 Step 3: Initialize Smart AI Engine")
    try:
        smart_engine = SmartAIEngine()
        print("   ✅ SmartAIEngine initialized successfully")
        print(f"   📊 Available clients:")
        print(f"      Groq: {'✅' if smart_engine.groq_client else '❌'}")
        print(f"      Mistral: {'✅' if smart_engine.mistral_client else '❌'}")
        print(f"      OpenAI: {'✅' if smart_engine.openai_client else '❌'}")
    except Exception as e:
        print(f"   ❌ Failed to initialize SmartAIEngine: {e}")
        return False
    print()
    
    # Verify no cache exists
    print("🔍 Step 4: Verify No Cache")
    user_input = f"{test_params['language']}:{test_params['business_type']}:{test_params['target_audience']}:{test_params['tone']}:{test_params['ad_title']}:{test_params['num_suggestions']}".strip().lower()
    cache_key = f"smart_suggestions:{hashlib.md5(user_input.encode()).hexdigest()}"
    print(f"   🔑 Cache key: {cache_key}")
    
    cached = get_cached_suggestions(cache_key)
    if cached:
        print(f"   ⚠️  Found existing cache with {len(cached)} suggestions")
        print("   🗑️  This shouldn't happen after clearing cache")
    else:
        print("   ✅ No cache found - will test fresh AI generation")
    print()
    
    # Test fresh AI generation
    print("🚀 Step 5: Generate Fresh AI Suggestions")
    start_time = time.time()
    try:
        suggestions = smart_engine.generate_ad_suggestions(**test_params)
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"   ✅ Generated {len(suggestions)} suggestions in {response_time:.2f} seconds")
        print(f"   ⚡ Performance: {'Excellent (cached)' if response_time < 0.5 else 'Good (AI generated)' if response_time < 10 else 'Slow'}")
        
        # Analyze suggestions in detail
        if suggestions:
            print(f"\n   📊 Detailed Suggestion Analysis:")
            for i, suggestion in enumerate(suggestions, 1):
                print(f"      🎯 Suggestion {i}:")
                print(f"         Title: {suggestion.get('title', 'N/A')}")
                print(f"         Content: {suggestion.get('content', 'N/A')[:100]}...")
                print(f"         Provider: {suggestion.get('provider', 'N/A')}")
                print(f"         Model: {suggestion.get('model', 'N/A')}")
                print(f"         Cached: {suggestion.get('cached', False)}")
                print(f"         Cache Source: {suggestion.get('cache_source', 'N/A')}")
                print(f"         Smart Engine: {suggestion.get('smart_engine', False)}")
                print(f"         Rank: {suggestion.get('rank', 'N/A')}")
                print(f"         Selection Score: {suggestion.get('selection_score', 'N/A')}")
                print(f"         AI Generated: {suggestion.get('ai_generated', False)}")
                print()
        else:
            print("   ❌ No suggestions generated")
            return False
            
    except Exception as e:
        print(f"   ❌ Failed to generate suggestions: {e}")
        import traceback
        traceback.print_exc()
        return False
    print()
    
    # Test cache creation
    print("🔄 Step 6: Test Cache Creation")
    try:
        # Generate again to test if cache was created
        print("   🔄 Generating suggestions again to test cache creation...")
        start_time = time.time()
        cached_suggestions = smart_engine.generate_ad_suggestions(**test_params)
        end_time = time.time()
        cache_response_time = end_time - start_time
        
        print(f"   ⚡ Second request took {cache_response_time:.2f} seconds")
        
        if cache_response_time < 0.5:
            print("   ✅ Cache was created and is working - very fast response!")
        elif cache_response_time < 2:
            print("   ⚠️  Possibly cached but slower than expected")
        else:
            print("   ❌ Cache might not be working - slow response")
            
        # Check if suggestions are identical
        if len(suggestions) == len(cached_suggestions):
            identical = all(
                s1.get('title') == s2.get('title') and s1.get('content') == s2.get('content')
                for s1, s2 in zip(suggestions, cached_suggestions)
            )
            if identical:
                print("   ✅ Cache returned identical suggestions")
            else:
                print("   ⚠️  Cache returned different suggestions")
        
    except Exception as e:
        print(f"   ❌ Cache test failed: {e}")
    print()
    
    # Analyze provider usage
    print("🎯 Step 7: Analyze Provider Usage")
    try:
        providers_used = set()
        models_used = set()
        
        for suggestion in suggestions:
            provider = suggestion.get('provider', 'unknown')
            model = suggestion.get('model', 'unknown')
            providers_used.add(provider)
            models_used.add(model)
        
        print(f"   📊 Providers Used: {', '.join(providers_used)}")
        print(f"   🤖 Models Used: {', '.join(models_used)}")
        
        # Check if Groq was prioritized
        groq_suggestions = [s for s in suggestions if s.get('provider') == 'groq']
        mistral_suggestions = [s for s in suggestions if s.get('provider') == 'mistral']
        
        print(f"   ⚡ Groq Suggestions: {len(groq_suggestions)}")
        print(f"   🔄 Mistral Suggestions: {len(mistral_suggestions)}")
        
        if groq_suggestions:
            print("   ✅ Groq was used successfully!")
            # Check model priority
            groq_models = [s.get('model') for s in groq_suggestions]
            print(f"   🎯 Groq Models Used: {groq_models}")
        elif mistral_suggestions:
            print("   ⚠️  Only Mistral was used (Groq might have failed)")
        else:
            print("   ❌ No major AI providers were used")
            
    except Exception as e:
        print(f"   ❌ Provider analysis failed: {e}")
    print()
    
    print("🎉 FRESH AI GENERATION TEST COMPLETED!")
    print("=" * 50)
    return True

if __name__ == "__main__":
    success = test_fresh_ai_generation()
    if success:
        print("✅ Fresh AI generation test completed successfully!")
        print("\n🌐 Ready for browser testing!")
        print("   The Smart AI Engine is working with:")
        print("   - Cache-first strategy (6-hour TTL)")
        print("   - Groq model priority (if available)")
        print("   - Intelligent suggestion selection")
        print("   - Provider metadata tracking")
    else:
        print("❌ Fresh AI generation test failed. Check the logs above.")
    
    sys.exit(0 if success else 1)
