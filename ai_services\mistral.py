"""
Mistral AI client for generating suggestions
"""
import json
import logging
import os
import time
from typing import List, Dict, Optional

# Get the Mistral API key from environment variable
MISTRAL_API_KEY = os.environ.get('MISTRAL_API_KEY', '37OphnjoLD2IJN2kCZaFoHzgfcmJjltK')
MISTRAL_MODEL = os.environ.get('MISTRAL_MODEL', 'mistral-tiny')

logger = logging.getLogger(__name__)

def generate_with_mistral(
    user_input: str,
    language: str = "english",
    business_type: str = "",
    target_audience: str = "",
    tone: str = "professional",
    title: str = "",
    num_suggestions: int = 3,
    timeout: int = 6
) -> Optional[List[Dict[str, str]]]:
    """
    Generate suggestions using Mistral AI with robust error handling

    Args:
        user_input: The user input to generate suggestions for
        language: The language to generate suggestions in
        business_type: The type of business
        target_audience: The target audience
        tone: The tone of the suggestions
        title: The title to use for the suggestions
        num_suggestions: The number of suggestions to generate
        timeout: The timeout in seconds

    Returns:
        A list of suggestions or None if generation failed
    """
    try:
        logger.info(f"Generating suggestions with Mistral for: {user_input[:50]}")

        # Try to import the mistralai client
        try:
            from mistralai.client import MistralClient
            from mistralai.models.chat_completion import ChatMessage

            # Create the prompt
            prompt = create_prompt(
                language=language,
                business_type=business_type,
                target_audience=target_audience,
                tone=tone,
                num_suggestions=num_suggestions,
                title=title
            )

            # Initialize the client
            client = MistralClient(api_key=MISTRAL_API_KEY)

            # Create the message
            messages = [ChatMessage(role="user", content=prompt)]

            # Generate a response
            logger.info(f"Making API request to Mistral with model: {MISTRAL_MODEL}")
            start_time = time.time()

            response = client.chat(
                model=MISTRAL_MODEL,
                messages=messages,
                temperature=0.7,
                max_tokens=150,
                timeout=timeout
            )

            # Calculate processing time
            processing_time = time.time() - start_time
            logger.info(f"Mistral API request completed in {processing_time:.2f} seconds")

            # Process the response
            content = response.choices[0].message.content
            suggestions = parse_suggestions(content)

            if suggestions and len(suggestions) > 0:
                logger.info(f"Successfully generated {len(suggestions)} suggestions with Mistral")
                return suggestions
            else:
                logger.warning("No valid suggestions in Mistral response")
                return None

        except ImportError:
            logger.warning("mistralai client not installed, falling back to direct API call")
            return generate_with_mistral_direct_api(
                language=language,
                business_type=business_type,
                target_audience=target_audience,
                tone=tone,
                title=title,
                num_suggestions=num_suggestions,
                timeout=timeout
            )

    except Exception as e:
        logger.error(f"Error generating suggestions with Mistral: {str(e)}")
        return None

def generate_with_mistral_direct_api(
    language: str = "english",
    business_type: str = "",
    target_audience: str = "",
    tone: str = "professional",
    title: str = "",
    num_suggestions: int = 3,
    timeout: int = 6
) -> Optional[List[Dict[str, str]]]:
    """
    Generate suggestions using direct Mistral API call

    This is a fallback for when the mistralai client is not available
    """
    try:
        import requests

        # Create the prompt
        prompt = create_prompt(
            language=language,
            business_type=business_type,
            target_audience=target_audience,
            tone=tone,
            num_suggestions=num_suggestions,
            title=title
        )

        # Prepare the request payload
        payload = {
            "model": MISTRAL_MODEL,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0.7,
            "max_tokens": 150
        }

        # Prepare the headers
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {MISTRAL_API_KEY}"
        }

        # Make the API request
        logger.info(f"Making direct API request to Mistral with model: {MISTRAL_MODEL}")
        start_time = time.time()

        response = requests.post(
            "https://api.mistral.ai/v1/chat/completions",
            headers=headers,
            json=payload,
            timeout=timeout
        )

        # Calculate processing time
        processing_time = time.time() - start_time
        logger.info(f"Mistral API request completed in {processing_time:.2f} seconds")

        # Check if the request was successful
        if response.status_code == 200:
            # Parse the response
            response_data = response.json()

            # Extract the content from the response
            message_content = response_data.get('choices', [{}])[0].get('message', {}).get('content', '')

            if message_content:
                # Parse the suggestions
                suggestions = parse_suggestions(message_content)

                if suggestions and len(suggestions) > 0:
                    logger.info(f"Successfully generated {len(suggestions)} suggestions with direct Mistral API")
                    return suggestions
                else:
                    logger.warning("No valid suggestions in Mistral response")
                    return None
            else:
                logger.warning("Empty content in Mistral response")
                return None
        else:
            logger.warning(f"Mistral API request failed with status code: {response.status_code}")
            logger.warning(f"Response: {response.text[:200]}...")
            return None

    except Exception as e:
        logger.error(f"Error generating suggestions with direct Mistral API: {str(e)}")
        return None

def create_prompt(
    language: str = "english",
    business_type: str = "",
    target_audience: str = "",
    tone: str = "professional",
    num_suggestions: int = 3,
    title: str = ""
) -> str:
    """
    Create a prompt for generating ad suggestions
    """
    # Determine title instruction and format based on whether a title was provided
    title_instruction = ""
    title_format = "[catchy title]"

    if title:
        title_instruction = f"The ad title is: {title}\nGenerate content that aligns with this title."
        title_format = title

    # Create the prompt
    prompt = f"""
Generate {num_suggestions} creative and professional advertisement suggestions for a business.
Each suggestion should include a catchy title and compelling content.
The advertisements should be in {language} language.
The business type or product is: {business_type}
The target audience is: {target_audience}
The tone should be: {tone}
{title_instruction}

Format each suggestion as:
Title: {title_format}
Content: [compelling content that is concise and persuasive]

Keep titles under 60 characters and content under 150 characters.
Make the ads sound professional, engaging, and persuasive.
"""

    return prompt

def parse_suggestions(content: str) -> List[Dict[str, str]]:
    """
    Parse the AI response into a list of suggestions
    """
    suggestions = []
    current_suggestion = {}

    # Standard parsing
    for line in content.strip().split('\n'):
        line = line.strip()
        if not line:
            continue

        if line.lower().startswith('title:'):
            # If we have a previous suggestion, add it to the list
            if current_suggestion and 'title' in current_suggestion and 'content' in current_suggestion:
                suggestions.append(current_suggestion)
                current_suggestion = {}

            current_suggestion['title'] = line[6:].strip()

        elif line.lower().startswith('content:'):
            current_suggestion['content'] = line[8:].strip()

    # Add the last suggestion
    if current_suggestion and 'title' in current_suggestion and 'content' in current_suggestion:
        suggestions.append(current_suggestion)

    # If no suggestions were parsed, try a simpler approach
    if not suggestions:
        # Try to split by "Title:" and "Content:"
        parts = content.split("Title:")
        for part in parts[1:]:  # Skip the first part (before the first "Title:")
            try:
                title_content = part.strip()
                title_content_parts = title_content.split("Content:")

                if len(title_content_parts) > 1:
                    title = title_content_parts[0].strip()
                    content_text = title_content_parts[1].strip()

                    suggestions.append({
                        "title": title,
                        "content": content_text
                    })
            except:
                continue

    return suggestions
