"""
AI Services Caching Module
Provides caching functionality for AI responses to improve performance and reliability
"""
import os
import json
import time
import hashlib
import logging
from typing import Dict, Any, Optional, List, Union
from pathlib import Path

logger = logging.getLogger(__name__)

# Cache directory
CACHE_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'cache')
os.makedirs(CACHE_DIR, exist_ok=True)

# Cache settings
CACHE_EXPIRY = 60 * 60 * 24 * 7  # 7 days in seconds
MAX_CACHE_SIZE = 100  # Maximum number of cache entries


def generate_cache_key(prompt: str, model: str, language: str, **kwargs) -> str:
    """
    Generate a cache key based on the input parameters

    Args:
        prompt: The prompt text
        model: The model name
        language: The language
        **kwargs: Additional parameters to include in the cache key

    Returns:
        A unique cache key string
    """
    # Create a dictionary of all parameters
    params = {
        'prompt': prompt,
        'model': model,
        'language': language,
        **kwargs
    }

    # Convert to a sorted JSON string to ensure consistent ordering
    params_str = json.dumps(params, sort_keys=True)

    # Generate a hash of the parameters
    return hashlib.md5(params_str.encode()).hexdigest()


def get_cache_path(cache_key: str) -> str:
    """
    Get the file path for a cache key

    Args:
        cache_key: The cache key

    Returns:
        The file path for the cache
    """
    return os.path.join(CACHE_DIR, f"{cache_key}.json")


def save_to_cache(cache_key: str, data: Any) -> bool:
    """
    Save data to the cache

    Args:
        cache_key: The cache key
        data: The data to cache

    Returns:
        True if successful, False otherwise
    """
    try:
        cache_path = get_cache_path(cache_key)

        # Add timestamp to the cached data
        cache_data = {
            'timestamp': time.time(),
            'data': data
        }

        with open(cache_path, 'w') as f:
            json.dump(cache_data, f)

        logger.debug(f"Saved data to cache: {cache_key}")

        # Clean up old cache entries if needed
        cleanup_cache()

        return True
    except Exception as e:
        logger.error(f"Error saving to cache: {str(e)}")
        return False


def get_from_cache(cache_key: str, max_age: int = CACHE_EXPIRY) -> Optional[Any]:
    """
    Get data from the cache

    Args:
        cache_key: The cache key
        max_age: Maximum age of the cache in seconds

    Returns:
        The cached data, or None if not found or expired
    """
    global _cache_hits, _cache_misses

    try:
        cache_path = get_cache_path(cache_key)

        if not os.path.exists(cache_path):
            _cache_misses += 1
            return None

        with open(cache_path, 'r') as f:
            cache_data = json.load(f)

        # Check if the cache is expired
        timestamp = cache_data.get('timestamp', 0)
        if time.time() - timestamp > max_age:
            logger.debug(f"Cache expired: {cache_key}")
            _cache_misses += 1
            return None

        logger.debug(f"Retrieved data from cache: {cache_key}")
        _cache_hits += 1
        return cache_data.get('data')
    except Exception as e:
        logger.error(f"Error retrieving from cache: {str(e)}")
        _cache_misses += 1
        return None


def cleanup_cache() -> None:
    """
    Clean up old cache entries to prevent the cache from growing too large
    """
    try:
        # Get all cache files
        cache_files = list(Path(CACHE_DIR).glob("*.json"))

        # If we're under the limit, no need to clean up
        if len(cache_files) <= MAX_CACHE_SIZE:
            return

        # Get file info with timestamps
        file_info = []
        for file_path in cache_files:
            try:
                with open(file_path, 'r') as f:
                    cache_data = json.load(f)
                    timestamp = cache_data.get('timestamp', 0)
                    file_info.append((file_path, timestamp))
            except:
                # If we can't read the file, assume it's old
                file_info.append((file_path, 0))

        # Sort by timestamp (oldest first)
        file_info.sort(key=lambda x: x[1])

        # Delete oldest files until we're under the limit
        files_to_delete = len(file_info) - MAX_CACHE_SIZE
        for i in range(files_to_delete):
            try:
                os.remove(file_info[i][0])
                logger.debug(f"Deleted old cache file: {file_info[i][0]}")
            except Exception as e:
                logger.error(f"Error deleting cache file: {str(e)}")
    except Exception as e:
        logger.error(f"Error cleaning up cache: {str(e)}")


def clear_cache() -> bool:
    """
    Clear all cache entries

    Returns:
        True if successful, False otherwise
    """
    try:
        cache_files = list(Path(CACHE_DIR).glob("*.json"))
        for file_path in cache_files:
            try:
                os.remove(file_path)
            except:
                pass

        logger.info(f"Cleared {len(cache_files)} cache entries")
        return True
    except Exception as e:
        logger.error(f"Error clearing cache: {str(e)}")
        return False


# Cache hit/miss counters
_cache_hits = 0
_cache_misses = 0

def get_cache_stats() -> Dict[str, Any]:
    """
    Get statistics about the cache

    Returns:
        A dictionary with cache statistics
    """
    try:
        cache_files = list(Path(CACHE_DIR).glob("*.json"))
        total_size = sum(os.path.getsize(f) for f in cache_files)

        # Get age of oldest and newest cache entries
        timestamps = []
        for file_path in cache_files:
            try:
                with open(file_path, 'r') as f:
                    cache_data = json.load(f)
                    timestamp = cache_data.get('timestamp', 0)
                    timestamps.append(timestamp)
            except:
                pass

        oldest = min(timestamps) if timestamps else 0
        newest = max(timestamps) if timestamps else 0

        # Get most common cache keys
        most_common = []
        try:
            from django.db.models import Count
            from ads.models import AiFeedback

            # Get the most common models used in the last 7 days
            from django.utils import timezone
            last_week = timezone.now() - timezone.timedelta(days=7)
            model_counts = AiFeedback.objects.filter(created_at__gte=last_week).values('model_used').annotate(count=Count('id')).order_by('-count')[:5]

            most_common = [{'model': item['model_used'], 'count': item['count']} for item in model_counts]
        except Exception as e:
            logger.error(f"Error getting most common cache keys: {str(e)}")

        return {
            'entries': len(cache_files),
            'size': total_size,
            'hits': _cache_hits,
            'misses': _cache_misses,
            'hit_ratio': _cache_hits / (_cache_hits + _cache_misses) if (_cache_hits + _cache_misses) > 0 else 0,
            'oldest_timestamp': oldest,
            'oldest_age_hours': (time.time() - oldest) / 3600 if oldest else 0,
            'newest_timestamp': newest,
            'newest_age_hours': (time.time() - newest) / 3600 if newest else 0,
            'most_common': most_common
        }
    except Exception as e:
        logger.error(f"Error getting cache stats: {str(e)}")
        return {
            'error': str(e),
            'entries': 0,
            'size': 0,
            'hits': _cache_hits,
            'misses': _cache_misses,
            'hit_ratio': 0,
            'most_common': []
        }
