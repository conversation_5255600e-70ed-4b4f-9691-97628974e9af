{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "User Profile" %} - Enterprise QR{% endblock %}

{% block extra_css %}
<style>
    /* Enterprise Profile Redirect Styles */
    .profile-container {
        min-height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        position: relative;
        overflow: hidden;
        display: flex;
        align-items: center;
        padding: 40px 0;
    }

    .profile-background {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 1;
    }

    .profile-grid-pattern {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: 
            linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
            linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
        background-size: 40px 40px;
        animation: profileGridMove 25s linear infinite;
    }

    @keyframes profileGridMove {
        0% { transform: translate(0, 0); }
        100% { transform: translate(40px, 40px); }
    }

    .floating-qr {
        position: absolute;
        width: 60px;
        height: 60px;
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        justify-content: center;
        color: rgba(255, 255, 255, 0.7);
        font-size: 24px;
    }

    .floating-qr-1 {
        top: 15%;
        left: 10%;
        animation: floatQR1 8s ease-in-out infinite;
    }

    .floating-qr-2 {
        top: 70%;
        right: 15%;
        animation: floatQR2 10s ease-in-out infinite;
    }

    .floating-qr-3 {
        bottom: 20%;
        left: 20%;
        animation: floatQR3 12s ease-in-out infinite;
    }

    @keyframes floatQR1 {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(5deg); }
    }

    @keyframes floatQR2 {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-30px) rotate(-5deg); }
    }

    @keyframes floatQR3 {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-25px) rotate(3deg); }
    }

    .profile-card {
        position: relative;
        z-index: 2;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 20px;
        box-shadow: 
            0 20px 60px rgba(0, 0, 0, 0.1),
            inset 0 2px 0 rgba(255, 255, 255, 0.5),
            inset 0 -2px 0 rgba(0, 0, 0, 0.05);
        overflow: hidden;
        max-width: 450px;
        width: 100%;
        margin: 0 auto;
    }

    .profile-header {
        text-align: center;
        padding: 40px 40px 20px;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        border-bottom: 1px solid rgba(102, 126, 234, 0.1);
    }

    .profile-logo {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        transform: rotate(45deg);
    }

    .profile-logo i {
        font-size: 32px;
        color: white;
        transform: rotate(-45deg);
    }

    .profile-title {
        font-size: 2rem;
        font-weight: 800;
        color: #2c3e50;
        margin-bottom: 10px;
    }

    .profile-subtitle {
        color: #6c757d;
        font-size: 1rem;
        margin-bottom: 0;
        line-height: 1.5;
    }

    .profile-body {
        padding: 40px;
        text-align: center;
    }

    .enterprise-badge {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        border: 2px solid rgba(102, 126, 234, 0.3);
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 700;
        color: #667eea;
        margin-bottom: 30px;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
    }

    .redirect-message {
        background: rgba(40, 167, 69, 0.1);
        border: 2px solid rgba(40, 167, 69, 0.2);
        border-radius: 12px;
        padding: 30px;
        margin-bottom: 30px;
        color: #28a745;
        font-weight: 500;
    }

    .redirect-message i {
        font-size: 48px;
        margin-bottom: 15px;
        display: block;
    }

    .redirect-message h5 {
        color: #28a745;
        font-weight: 700;
        margin-bottom: 10px;
    }

    .btn-enterprise {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        padding: 15px 30px;
        border-radius: 12px;
        font-weight: 700;
        font-size: 16px;
        transition: all 0.3s ease;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        position: relative;
        overflow: hidden;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 10px;
    }

    .btn-enterprise:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-enterprise::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
    }

    .btn-enterprise:hover::before {
        left: 100%;
    }

    .countdown {
        font-size: 18px;
        font-weight: 700;
        color: #667eea;
        margin-bottom: 20px;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .profile-container {
            padding: 20px;
        }

        .profile-card {
            margin: 20px;
        }

        .profile-header,
        .profile-body {
            padding: 30px 25px;
        }

        .profile-title {
            font-size: 1.5rem;
        }

        .floating-qr {
            display: none;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="profile-container">
    <!-- Animated Background -->
    <div class="profile-background">
        <div class="profile-grid-pattern"></div>
        <div class="profile-floating-elements">
            <div class="floating-qr floating-qr-1">
                <i class="fas fa-user"></i>
            </div>
            <div class="floating-qr floating-qr-2">
                <i class="fas fa-dashboard"></i>
            </div>
            <div class="floating-qr floating-qr-3">
                <i class="fas fa-qrcode"></i>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="profile-card">
            <!-- Header -->
            <div class="profile-header">
                <div class="profile-logo">
                    <i class="fas fa-user"></i>
                </div>
                <h1 class="profile-title">{% trans "Welcome Back!" %}</h1>
                <p class="profile-subtitle">{% trans "Redirecting you to your Enterprise QR dashboard" %}</p>
            </div>

            <!-- Body -->
            <div class="profile-body">
                <!-- Enterprise Badge -->
                <div class="enterprise-badge">
                    <i class="fas fa-rocket"></i>
                    <span>{% trans "Enterprise Dashboard" %}</span>
                </div>

                <!-- Redirect Message -->
                <div class="redirect-message">
                    <i class="fas fa-check-circle"></i>
                    <h5>{% trans "Authentication Successful!" %}</h5>
                    <p>{% trans "You're being redirected to your Enterprise QR dashboard where you can manage your QR codes, view analytics, and access all premium features." %}</p>
                </div>

                <!-- Countdown -->
                <div class="countdown" id="countdown">
                    {% trans "Redirecting in" %} <span id="timer">3</span> {% trans "seconds..." %}
                </div>

                <!-- Manual Redirect Button -->
                <a href="{% url 'index' %}" class="btn-enterprise">
                    <i class="fas fa-dashboard"></i>
                    {% trans "Go to Dashboard Now" %}
                </a>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-redirect countdown
let timeLeft = 3;
const timerElement = document.getElementById('timer');
const countdownElement = document.getElementById('countdown');

const countdown = setInterval(() => {
    timeLeft--;
    timerElement.textContent = timeLeft;
    
    if (timeLeft <= 0) {
        clearInterval(countdown);
        countdownElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i> {% trans "Redirecting now..." %}';
        window.location.href = "{% url 'index' %}";
    }
}, 1000);

// Immediate redirect if user clicks anywhere
document.addEventListener('click', function(e) {
    if (!e.target.closest('.btn-enterprise')) {
        clearInterval(countdown);
        window.location.href = "{% url 'index' %}";
    }
});
</script>
{% endblock %}
