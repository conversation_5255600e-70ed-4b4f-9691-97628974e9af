/* Ad Containers Styling */

/* Header Ad Container */
.header-ad-container {
    width: 100%;
    max-width: 100%;
    padding: 15px 0;
    background-color: #f8f9fa;
    text-align: center;
    margin-bottom: 20px;
    border-bottom: 1px solid #e9ecef;
}

/* Sidebar Ad Containers */
.sidebar-ad-container {
    margin-bottom: 20px;
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

/* Content Ad Containers */
.content-top-ad-container,
.content-bottom-ad-container {
    width: 100%;
    margin: 20px 0;
    text-align: center;
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

/* Footer Ad Container */
.footer-ad-container {
    width: 100%;
    padding: 15px 0;
    background-color: #f8f9fa;
    text-align: center;
    border-top: 1px solid #e9ecef;
    border-bottom: 1px solid #e9ecef;
}

/* Popup Ad Container */
.popup-ad-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    max-width: 300px;
    box-shadow: 0 5px 25px rgba(0,0,0,0.15);
    border-radius: 8px;
    overflow: hidden;
}

/* Close button for popup ads */
.popup-close-btn {
    position: absolute;
    top: 5px;
    right: 5px;
    background-color: rgba(0,0,0,0.5);
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 12px;
    z-index: 10;
}

.popup-close-btn:hover {
    background-color: rgba(0,0,0,0.7);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .popup-ad-container {
        bottom: 10px;
        right: 10px;
        max-width: 250px;
    }
    
    .header-ad-container,
    .footer-ad-container {
        padding: 10px 0;
    }
}
