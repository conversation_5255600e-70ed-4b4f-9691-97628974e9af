from django.core.management.base import BaseCommand
from django.utils import timezone
from ads.models import Ad, AdAnalytics
import random
from datetime import timedelta

class Command(BaseCommand):
    help = 'Simulates impressions and clicks for active ads'

    def add_arguments(self, parser):
        parser.add_argument('--impressions', type=int, default=100, help='Number of impressions to simulate')
        parser.add_argument('--ctr', type=float, default=2.5, help='Click-through rate (percentage)')
        parser.add_argument('--days', type=int, default=7, help='Number of days to spread the data over')

    def handle(self, *args, **options):
        impressions = options['impressions']
        ctr = options['ctr']
        days = options['days']
        
        # Get active ads
        active_ads = Ad.objects.filter(status='active')
        
        if not active_ads:
            self.stdout.write(self.style.ERROR('No active ads found'))
            return
        
        self.stdout.write(f'Simulating metrics for {active_ads.count()} active ads')
        
        # Calculate clicks based on CTR
        clicks = int(impressions * (ctr / 100))
        
        # Get dates for the past N days
        today = timezone.now().date()
        dates = [today - timedelta(days=i) for i in range(days)]
        
        # Device types and their distribution
        device_types = {
            'desktop': 0.6,
            'mobile': 0.35,
            'tablet': 0.05
        }
        
        # Locations and their distribution
        locations = {
            'Nairobi': 0.5,
            'Mombasa': 0.2,
            'Kisumu': 0.1,
            'Nakuru': 0.1,
            'Other': 0.1
        }
        
        for ad in active_ads:
            self.stdout.write(f'Processing ad: {ad.title}')
            
            # Reset ad metrics
            ad.impressions = 0
            ad.clicks = 0
            ad.save()
            
            # Delete existing analytics
            AdAnalytics.objects.filter(ad=ad).delete()
            
            # Distribute impressions and clicks across days
            for date in dates:
                # Distribute impressions across days (more recent days get more)
                days_ago = (today - date).days
                day_weight = 1 - (days_ago / (days * 1.5))  # Weight decreases with age
                day_impressions = int(impressions * day_weight / days)
                day_clicks = int(clicks * day_weight / days)
                
                # Ensure at least some activity each day
                day_impressions = max(day_impressions, 5)
                day_clicks = max(day_clicks, 1)
                
                # Create device data
                device_data = {}
                for device, percentage in device_types.items():
                    device_impressions = int(day_impressions * percentage)
                    device_data[device] = device_impressions
                
                # Create location data
                location_data = {}
                for location, percentage in locations.items():
                    location_impressions = int(day_impressions * percentage)
                    location_data[location] = location_impressions
                
                # Create analytics record
                analytics = AdAnalytics.objects.create(
                    ad=ad,
                    date=date,
                    impressions=day_impressions,
                    clicks=day_clicks,
                    unique_views=int(day_impressions * 0.8),  # 80% of impressions are unique
                    device_data=device_data,
                    location_data=location_data
                )
                
                self.stdout.write(f'  {date}: {day_impressions} impressions, {day_clicks} clicks')
                
                # Update ad totals
                ad.impressions += day_impressions
                ad.clicks += day_clicks
            
            # Save updated ad metrics
            ad.save()
            
            self.stdout.write(self.style.SUCCESS(f'Completed: {ad.title} - {ad.impressions} impressions, {ad.clicks} clicks'))
        
        self.stdout.write(self.style.SUCCESS('Simulation complete'))
