/**
 * Role-Based Access Control (RBAC) System
 *
 * This file implements a role-based access control system for the Enterprise QR Code Generator.
 * It manages user roles, permissions, and access control to various features of the application.
 */

// RBAC Configuration
const rbac = {
    // Current user information (will be populated on login)
    currentUser: null,

    // Available roles and their permissions
    roles: {
        guest: {
            name: 'Guest',
            permissions: [
                'qr:create:basic',
                'qr:read:own'
            ]
        },
        user: {
            name: 'User',
            permissions: [
                'qr:create:basic',
                'qr:create:advanced',
                'qr:read:own',
                'qr:update:own',
                'qr:delete:own',
                'profile:read',
                'profile:update'
            ]
        },
        premium: {
            name: 'Premium User',
            permissions: [
                'qr:create:basic',
                'qr:create:advanced',
                'qr:create:bulk',
                'qr:read:own',
                'qr:update:own',
                'qr:delete:own',
                'qr:analytics:basic',
                'profile:read',
                'profile:update'
            ]
        },
        admin: {
            name: 'Administrator',
            permissions: [
                'qr:create:basic',
                'qr:create:advanced',
                'qr:create:bulk',
                'qr:read:own',
                'qr:read:all',
                'qr:update:own',
                'qr:update:all',
                'qr:delete:own',
                'qr:delete:all',
                'qr:analytics:basic',
                'qr:analytics:advanced',
                'user:read',
                'user:create',
                'user:update',
                'user:delete',
                'role:read',
                'role:create',
                'role:update',
                'role:delete',
                'profile:read',
                'profile:update',
                'settings:read',
                'settings:update',
                'api:manage'
            ]
        },
        superadmin: {
            name: 'Super Administrator',
            permissions: [
                'qr:create:basic',
                'qr:create:advanced',
                'qr:create:bulk',
                'qr:read:own',
                'qr:read:all',
                'qr:update:own',
                'qr:update:all',
                'qr:delete:own',
                'qr:delete:all',
                'qr:analytics:basic',
                'qr:analytics:advanced',
                'user:read',
                'user:create',
                'user:update',
                'user:delete',
                'role:read',
                'role:create',
                'role:update',
                'role:delete',
                'profile:read',
                'profile:update',
                'settings:read',
                'settings:update',
                'api:manage',
                'system:backup',
                'system:restore',
                'system:logs',
                'system:config'
            ]
        }
    },

    /**
     * Initialize the RBAC system
     */
    init: function() {
        console.log('Initializing RBAC system...');

        // Load user from localStorage if available
        this.loadUser();

        // Apply permissions to UI elements
        this.applyPermissions();

        // Set up event listeners
        this.setupEventListeners();

        console.log('RBAC system initialized.');
    },

    /**
     * Load user from localStorage
     */
    loadUser: function() {
        const userInfo = localStorage.getItem('user_info');

        if (userInfo) {
            try {
                this.currentUser = JSON.parse(userInfo);
                console.log('User loaded:', this.currentUser.name, '(Role:', this.currentUser.role, ')');

                // Update UI with user info
                this.updateUserUI();
            } catch (error) {
                console.error('Error parsing user info:', error);
                this.currentUser = null;
            }
        } else {
            // Set default guest user for demo purposes
            // In a real application, this would be null until login
            this.currentUser = {
                id: 'guest-' + Date.now(),
                name: 'Guest User',
                email: '<EMAIL>',
                role: 'admin', // Set to 'admin' for demo purposes
                lastLogin: new Date().toISOString()
            };

            // Save to localStorage
            localStorage.setItem('user_info', JSON.stringify(this.currentUser));

            // Update UI with user info
            this.updateUserUI();

            console.log('Default guest user created with admin role for demo purposes.');
        }
    },

    /**
     * Update UI elements with user information
     */
    updateUserUI: function() {
        if (!this.currentUser) return;

        // Update user name in header
        const userNameElements = document.querySelectorAll('.user-name');
        userNameElements.forEach(element => {
            element.textContent = this.currentUser.name;
        });

        // Update user role if element exists
        const userRoleElements = document.querySelectorAll('.user-role, .mobile-user-role');
        userRoleElements.forEach(element => {
            element.textContent = this.roles[this.currentUser.role]?.name || this.currentUser.role;
        });
    },

    /**
     * Apply permissions to UI elements
     */
    applyPermissions: function() {
        console.log('Applying permissions, user role:', this.currentUser?.role);

        // If no user is logged in, hide all restricted elements
        if (!this.currentUser) {
            this.hideRestrictedElements();
            console.log('No user logged in, hiding restricted elements');
            return;
        }

        // Get user permissions based on role
        const userPermissions = this.roles[this.currentUser.role]?.permissions || [];
        console.log('User permissions:', userPermissions);

        // Find all elements with permission requirements
        const restrictedElements = document.querySelectorAll('[data-requires-permission]');
        console.log('Found', restrictedElements.length, 'restricted elements');

        restrictedElements.forEach(element => {
            const requiredPermission = element.getAttribute('data-requires-permission');
            const hasPermission = userPermissions.includes(requiredPermission);

            console.log('Element permission check:', requiredPermission,
                        'Has permission:', hasPermission,
                        'Element:', element);

            // Check if user has the required permission
            if (hasPermission) {
                // User has permission, show the element
                element.style.display = '';
                element.classList.remove('permission-hidden');
                console.log('Showing element with permission:', requiredPermission);
            } else {
                // User doesn't have permission, hide the element
                element.style.display = 'none';
                element.classList.add('permission-hidden');
                console.log('Hiding element with permission:', requiredPermission);
            }
        });

        // Special handling for admin panel elements
        if (this.currentUser.role === 'admin' || this.currentUser.role === 'superadmin') {
            const adminElements = document.querySelectorAll('.admin-panel, [data-admin="true"]');
            console.log('Found', adminElements.length, 'admin elements');

            adminElements.forEach(element => {
                element.style.display = '';
                element.classList.remove('permission-hidden');
                console.log('Showing admin element:', element);
            });
        }

        console.log('Permissions applied to UI elements.');
    },

    /**
     * Hide all elements that require permissions
     */
    hideRestrictedElements: function() {
        const restrictedElements = document.querySelectorAll('[data-requires-permission]');

        restrictedElements.forEach(element => {
            element.style.display = 'none';
            element.classList.add('permission-hidden');
        });
    },

    /**
     * Set up event listeners
     */
    setupEventListeners: function() {
        // Listen for login/logout events
        document.addEventListener('user:login', (event) => {
            this.currentUser = event.detail;
            localStorage.setItem('user_info', JSON.stringify(this.currentUser));
            this.updateUserUI();
            this.applyPermissions();
        });

        document.addEventListener('user:logout', () => {
            this.currentUser = null;
            localStorage.removeItem('user_info');
            this.hideRestrictedElements();
        });

        // Listen for role change events
        document.addEventListener('user:roleChange', (event) => {
            if (this.currentUser) {
                this.currentUser.role = event.detail.role;
                localStorage.setItem('user_info', JSON.stringify(this.currentUser));
                this.updateUserUI();
                this.applyPermissions();
            }
        });
    },

    /**
     * Check if user has a specific permission
     * @param {string} permission - Permission to check
     * @returns {boolean} - Whether user has the permission
     */
    hasPermission: function(permission) {
        if (!this.currentUser) return false;

        const userPermissions = this.roles[this.currentUser.role]?.permissions || [];
        return userPermissions.includes(permission);
    },

    /**
     * Check if user has a specific role
     * @param {string} role - Role to check
     * @returns {boolean} - Whether user has the role
     */
    hasRole: function(role) {
        if (!this.currentUser) return false;

        return this.currentUser.role === role;
    },

    /**
     * Login user with credentials
     * @param {string} email - User email
     * @param {string} password - User password
     * @returns {Promise} - Promise resolving to user object
     */
    login: function(email, password) {
        // This is a mock implementation
        // In a real application, this would make an API call

        return new Promise((resolve, reject) => {
            setTimeout(() => {
                // Mock successful login
                if (email && password) {
                    const user = {
                        id: 'user-' + Date.now(),
                        name: email.split('@')[0],
                        email: email,
                        role: 'admin', // Set to 'admin' for demo purposes
                        lastLogin: new Date().toISOString()
                    };

                    this.currentUser = user;
                    localStorage.setItem('user_info', JSON.stringify(user));

                    // Update UI
                    this.updateUserUI();
                    this.applyPermissions();

                    // Dispatch login event
                    document.dispatchEvent(new CustomEvent('user:login', { detail: user }));

                    resolve(user);
                } else {
                    reject(new Error('Invalid credentials'));
                }
            }, 1000);
        });
    },

    /**
     * Logout current user
     */
    logout: function() {
        this.currentUser = null;
        localStorage.removeItem('user_info');

        // Hide restricted elements
        this.hideRestrictedElements();

        // Dispatch logout event
        document.dispatchEvent(new CustomEvent('user:logout'));

        // Redirect to login page
        window.location.href = 'auth.html';
    }
};

// Initialize RBAC system when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    rbac.init();
});

// Add a delayed permission check after page load
window.addEventListener('load', function() {
    console.log('Window loaded, applying permissions with delay');
    setTimeout(function() {
        if (rbac && typeof rbac.applyPermissions === 'function') {
            rbac.applyPermissions();
            console.log('Delayed permission check applied');
        }
    }, 500);
});
