<!-- jVectorMap Scripts -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-jvectormap/2.0.5/jquery-jvectormap.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-jvectormap/2.0.5/jquery-mousewheel.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-jvectormap/2.0.5/maps/jquery-jvectormap-world-mill.min.js"></script>

<!-- Country Code Mapping -->
<script>
    // Map of city/region names to country codes for jVectorMap
    const countryCodeMap = {
        // Africa
        'nairobi': 'ke',
        'mombasa': 'ke',
        'kisumu': 'ke',
        'nakuru': 'ke',
        'eldoret': 'ke',
        'kenya': 'ke',
        'uganda': 'ug',
        'tanzania': 'tz',
        'ethiopia': 'et',
        'nigeria': 'ng',
        'south africa': 'za',
        'ghana': 'gh',
        'egypt': 'eg',
        'morocco': 'ma',
        'algeria': 'dz',
        'tunisia': 'tn',
        'rwanda': 'rw',
        'somalia': 'so',
        'sudan': 'sd',
        'cameroon': 'cm',
        'senegal': 'sn',
        'ivory coast': 'ci',
        'mali': 'ml',
        'zambia': 'zm',
        'zimbabwe': 'zw',
        
        // Americas
        'united states': 'us',
        'usa': 'us',
        'canada': 'ca',
        'mexico': 'mx',
        'brazil': 'br',
        'argentina': 'ar',
        'colombia': 'co',
        'peru': 'pe',
        'chile': 'cl',
        'new york': 'us',
        'los angeles': 'us',
        'chicago': 'us',
        'toronto': 'ca',
        'vancouver': 'ca',
        'montreal': 'ca',
        
        // Europe
        'united kingdom': 'gb',
        'uk': 'gb',
        'france': 'fr',
        'germany': 'de',
        'italy': 'it',
        'spain': 'es',
        'portugal': 'pt',
        'netherlands': 'nl',
        'belgium': 'be',
        'switzerland': 'ch',
        'austria': 'at',
        'sweden': 'se',
        'norway': 'no',
        'denmark': 'dk',
        'finland': 'fi',
        'poland': 'pl',
        'london': 'gb',
        'paris': 'fr',
        'berlin': 'de',
        'rome': 'it',
        'madrid': 'es',
        
        // Asia
        'china': 'cn',
        'japan': 'jp',
        'india': 'in',
        'south korea': 'kr',
        'indonesia': 'id',
        'malaysia': 'my',
        'singapore': 'sg',
        'thailand': 'th',
        'vietnam': 'vn',
        'philippines': 'ph',
        'pakistan': 'pk',
        'bangladesh': 'bd',
        'hong kong': 'cn',
        'taiwan': 'tw',
        'beijing': 'cn',
        'shanghai': 'cn',
        'tokyo': 'jp',
        'delhi': 'in',
        'mumbai': 'in',
        'seoul': 'kr',
        'jakarta': 'id',
        
        // Oceania
        'australia': 'au',
        'new zealand': 'nz',
        'sydney': 'au',
        'melbourne': 'au',
        'auckland': 'nz',
        'wellington': 'nz',
        
        // Default for unknown
        'unknown': 'unknown'
    };
    
    // Function to get country code from location name
    function getCountryCode(location) {
        if (!location) return 'unknown';
        
        // Convert to lowercase for case-insensitive matching
        const locationLower = location.toLowerCase();
        
        // Check if we have a direct match
        if (countryCodeMap[locationLower]) {
            return countryCodeMap[locationLower];
        }
        
        // Check for partial matches (e.g., "New York City" should match "new york")
        for (const [key, value] of Object.entries(countryCodeMap)) {
            if (locationLower.includes(key)) {
                return value;
            }
        }
        
        // Default to unknown if no match found
        return 'unknown';
    }
</script>
