/**
 * Mobile Navigation Stability CSS
 * Fixes jumpiness and layout shifts in mobile navigation
 */

/* Prevent layout shifts during page load */
html {
    scroll-behavior: smooth;
}

body {
    /* Prevent content jumps when scrollbar appears/disappears */
    overflow-y: scroll;
    /* Prevent horizontal scrolling */
    overflow-x: hidden;
    /* Ensure consistent width calculation */
    width: 100%;
    /* Prevent iOS text size adjustment */
    -webkit-text-size-adjust: 100%;
}

/* Prevent content jumps when modal opens/closes */
body.modal-open {
    padding-right: 0 !important;
}

/* Prevent content jumps when mobile menu opens/closes */
body.no-scroll {
    position: fixed;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

/* Ensure mobile menu has consistent dimensions */
#mobile-slideout-menu, .mobile-slideout-menu {
    /* Use hardware acceleration for smoother animations */
    transform: translateX(100%) translateZ(0);
    will-change: transform;
    /* Ensure consistent width */
    width: 80%;
    max-width: 320px;
    /* Ensure consistent height */
    height: 100vh;
    /* Ensure consistent positioning */
    position: fixed;
    top: 0;
    right: 0;
    /* Ensure consistent z-index */
    z-index: 9998;
    /* Ensure consistent overflow behavior */
    overflow-y: auto;
    overflow-x: hidden;
    /* Ensure consistent transition */
    transition: transform 0.3s cubic-bezier(0.16, 1, 0.3, 1), opacity 0.3s ease;
    /* Ensure consistent display */
    display: block;
    /* Ensure consistent visibility */
    visibility: hidden;
    /* Ensure consistent opacity */
    opacity: 0;
    /* Ensure consistent backdrop filter */
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
}

/* Ensure mobile menu is visible when active */
#mobile-slideout-menu.visible, .mobile-slideout-menu.visible {
    transform: translateX(0) translateZ(0);
    opacity: 1;
    visibility: visible;
}

/* Ensure mobile menu overlay has consistent dimensions */
.mobile-menu-overlay {
    /* Use hardware acceleration for smoother animations */
    will-change: opacity;
    /* Ensure consistent width */
    width: 100%;
    /* Ensure consistent height */
    height: 100%;
    /* Ensure consistent positioning */
    position: fixed;
    top: 0;
    left: 0;
    /* Ensure consistent z-index */
    z-index: 9997;
    /* Ensure consistent transition */
    transition: opacity 0.3s ease;
    /* Ensure consistent display */
    display: none;
    /* Ensure consistent backdrop filter */
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    /* Ensure consistent background */
    background-color: rgba(0, 0, 0, 0.5);
    opacity: 0;
}

/* Ensure mobile menu overlay is visible when active */
.mobile-menu-overlay.active {
    opacity: 1;
}

/* Ensure mobile menu items have consistent dimensions */
.mobile-menu-item {
    /* Ensure consistent height for touch targets */
    min-height: 44px;
    /* Ensure consistent padding */
    padding: 12px 16px;
    /* Ensure consistent display */
    display: flex;
    align-items: center;
}

/* Ensure mobile menu dropdowns have consistent dimensions */
.mobile-menu-submenu, .submenu {
    /* Ensure consistent max height */
    max-height: 0;
    /* Ensure consistent overflow behavior */
    overflow: hidden;
    /* Ensure consistent transition */
    transition: max-height 0.3s ease, opacity 0.3s ease, transform 0.3s ease;
    /* Ensure consistent opacity */
    opacity: 0;
    /* Ensure consistent transform */
    transform: translateY(-10px);
    /* Ensure consistent display */
    display: none;
}

/* Ensure mobile menu dropdowns are visible when active */
.mobile-menu-dropdown.active .mobile-menu-submenu,
.mobile-menu-dropdown.active .submenu {
    /* Ensure consistent max height */
    max-height: 1000px;
    /* Ensure consistent opacity */
    opacity: 1;
    /* Ensure consistent transform */
    transform: translateY(0);
    /* Ensure consistent display */
    display: block;
}

/* Ensure hamburger button has consistent dimensions */
.new-navbar-toggle, #hamburger-btn {
    /* Ensure consistent width */
    width: 36px;
    /* Ensure consistent height */
    height: 36px;
    /* Ensure consistent padding */
    padding: 10px 8px;
    /* Ensure consistent position */
    position: relative;
    /* Ensure consistent z-index */
    z-index: 1000;
    /* Ensure consistent background */
    background: transparent;
    /* Ensure consistent border */
    border: none;
    /* Ensure consistent cursor */
    cursor: pointer;
    /* Ensure consistent outline */
    outline: none;
}

/* Ensure hamburger bars have consistent dimensions */
.hamburger-bar {
    /* Ensure consistent width */
    width: 100%;
    /* Ensure consistent height */
    height: 2px;
    /* Ensure consistent transition */
    transition: transform 0.3s ease, opacity 0.3s ease;
    /* Ensure consistent background */
    background-color: #333;
    /* Ensure consistent display */
    display: block;
    /* Ensure consistent margin */
    margin: 4px 0;
}

/* Ensure navbar has consistent dimensions */
.new-navbar {
    /* Ensure consistent height */
    height: 60px;
    /* Ensure consistent z-index */
    z-index: 1000;
    /* Ensure consistent position */
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
}

/* Ensure mobile top avatar has consistent dimensions */
.mobile-top-avatar-wrapper {
    /* Ensure consistent display */
    display: flex;
    align-items: center;
    /* Ensure consistent margin */
    margin-left: 15px;
}

/* Ensure mobile top avatar block has consistent dimensions */
.mobile-top-avatar-block {
    /* Ensure consistent display */
    display: flex;
    align-items: center;
    /* Ensure consistent cursor */
    cursor: pointer;
}

/* Ensure mobile top user avatar has consistent dimensions */
.mobile-top-user-avatar {
    /* Ensure consistent width */
    width: 32px;
    /* Ensure consistent height */
    height: 32px;
    /* Ensure consistent border radius */
    border-radius: 50%;
    /* Ensure consistent overflow */
    overflow: hidden;
    /* Ensure consistent margin */
    margin-right: 8px;
}

/* Ensure mobile top user avatar image has consistent dimensions */
.mobile-top-user-avatar img {
    /* Ensure consistent width */
    width: 100%;
    /* Ensure consistent height */
    height: 100%;
    /* Ensure consistent object fit */
    object-fit: cover;
}

/* Ensure mobile top user name has consistent dimensions */
.mobile-top-user-name {
    /* Ensure consistent font size */
    font-size: 14px;
    /* Ensure consistent font weight */
    font-weight: 600;
    /* Ensure consistent color */
    color: #333;
}

/* Ensure mobile top avatar dropdown has consistent dimensions */
.mobile-top-avatar-dropdown {
    /* Ensure consistent position */
    position: absolute;
    top: 60px;
    right: 10px;
    /* Ensure consistent width */
    width: 200px;
    /* Ensure consistent background */
    background-color: #fff;
    /* Ensure consistent border radius */
    border-radius: 8px;
    /* Ensure consistent box shadow */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    /* Ensure consistent z-index */
    z-index: 1001;
    /* Ensure consistent transition */
    transition: opacity 0.3s ease, transform 0.3s ease;
    /* Ensure consistent display */
    display: none;
    /* Ensure consistent opacity */
    opacity: 0;
    /* Ensure consistent transform */
    transform: scale(0.95);
    /* Ensure consistent pointer events */
    pointer-events: none;
}

/* Ensure mobile top avatar dropdown is visible when active */
.mobile-top-avatar-dropdown.active {
    /* Ensure consistent opacity */
    opacity: 1;
    /* Ensure consistent transform */
    transform: scale(1);
    /* Ensure consistent pointer events */
    pointer-events: auto;
}

/* Ensure notification bell has consistent dimensions */
.notification-bell {
    /* Ensure consistent display */
    display: flex;
    align-items: center;
    /* Ensure consistent margin */
    margin-right: 15px;
}

/* Ensure notification bell icon has consistent dimensions */
.bell-icon {
    /* Ensure consistent font size */
    font-size: 18px;
    /* Ensure consistent color */
    color: #333;
    /* Ensure consistent position */
    position: relative;
}

/* Ensure notification count has consistent dimensions */
.notification-count {
    /* Ensure consistent position */
    position: absolute;
    top: -5px;
    right: -5px;
    /* Ensure consistent width */
    min-width: 18px;
    /* Ensure consistent height */
    height: 18px;
    /* Ensure consistent border radius */
    border-radius: 9px;
    /* Ensure consistent background */
    background-color: #ff3b30;
    /* Ensure consistent color */
    color: #fff;
    /* Ensure consistent font size */
    font-size: 10px;
    /* Ensure consistent font weight */
    font-weight: 600;
    /* Ensure consistent display */
    display: flex;
    align-items: center;
    justify-content: center;
    /* Ensure consistent padding */
    padding: 0 5px;
    /* Ensure consistent opacity */
    opacity: 0;
    /* Ensure consistent transform */
    transform: scale(0);
    /* Ensure consistent transition */
    transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Ensure notification count is visible when active */
.notification-count.active {
    /* Ensure consistent opacity */
    opacity: 1;
    /* Ensure consistent transform */
    transform: scale(1);
}

/* Ensure notification count has consistent dimensions when there are notifications */
.notification-count.has-notifications {
    /* Ensure consistent background */
    background-color: #ff3b30;
}
