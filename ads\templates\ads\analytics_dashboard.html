{% extends 'base.html' %}
{% load static %}

{% block title %}Ad Analytics Dashboard{% endblock %}

{% block extra_css %}
<!-- Google Fonts - Poppins and Montserrat -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css">

<style>
    /* Ultra-Premium Enterprise Analytics Dashboard Styling */
    body {
        background: linear-gradient(135deg, #000000 0%, #0a0a0a 10%, #1a1a2e 25%, #16213e 40%, #0f3460 60%, #533483 80%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        position: relative;
        overflow-x: hidden;
    }

    /* Premium animated background with enterprise patterns */
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 30% 70%, rgba(102, 126, 234, 0.6) 0%, transparent 45%),
            radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.2) 0%, transparent 40%),
            radial-gradient(circle at 10% 90%, rgba(118, 75, 162, 0.5) 0%, transparent 50%),
            radial-gradient(circle at 90% 10%, rgba(83, 52, 131, 0.4) 0%, transparent 35%),
            radial-gradient(circle at 50% 50%, rgba(102, 126, 234, 0.3) 0%, transparent 45%);
        z-index: -1;
        animation: enterpriseDashboardFloat 90s ease-in-out infinite;
    }

    @keyframes enterpriseDashboardFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        25% { transform: translateY(-80px) rotate(3deg); }
        50% { transform: translateY(-60px) rotate(-3deg); }
        75% { transform: translateY(-90px) rotate(1.5deg); }
    }

    .analytics-container {
        padding: 2rem 0;
        position: relative;
        z-index: 1;
        min-height: 100vh;
    }

    /* Premium Enterprise Header */
    .enterprise-header {
        background: linear-gradient(135deg, rgba(26, 35, 126, 0.95) 0%, rgba(40, 53, 147, 0.95) 50%, rgba(63, 81, 181, 0.95) 100%);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2.5rem;
        margin-bottom: 2rem;
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        position: relative;
        overflow: hidden;
        animation: slideInDown 0.8s ease-out;
    }

    .enterprise-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
        animation: shimmer 3s ease-in-out infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    .enterprise-title {
        font-family: 'Montserrat', sans-serif !important;
        font-size: 2.5rem;
        font-weight: 700;
        color: #ffffff;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 2;
    }

    .enterprise-subtitle {
        font-size: 1.1rem;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 1.5rem;
        font-weight: 400;
        position: relative;
        z-index: 2;
    }

    /* Premium Analytics Filters */
    .analytics-filters {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.15),
            0 0 0 1px rgba(255, 255, 255, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        animation: slideInUp 0.8s ease-out 0.2s both;
    }

    .filter-title {
        font-family: 'Montserrat', sans-serif !important;
        font-size: 1.2rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        color: #1a237e;
        display: flex;
        align-items: center;
    }

    .filter-title i {
        color: #667eea;
        margin-right: 0.5rem;
    }

    .date-range-picker {
        position: relative;
    }

    .date-range-picker .input-group-text {
        background-color: #fff;
        border-left: none;
    }

    .export-buttons {
        display: flex;
        gap: 10px;
        margin-top: 15px;
    }

    .export-button {
        padding: 8px 15px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .export-button:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .trend-indicator {
        display: inline-flex;
        align-items: center;
        font-size: 14px;
        margin-left: 10px;
    }

    .trend-up {
        color: #28a745;
    }

    .trend-down {
        color: #dc3545;
    }

    .trend-neutral {
        color: #6c757d;
    }

    .view-all-link {
        color: white;
        text-decoration: none;
        font-size: 0.8rem;
        display: flex;
        align-items: center;
        transition: all 0.3s ease;
    }

    .view-all-link:hover {
        color: rgba(255, 255, 255, 0.8);
        transform: translateX(2px);
    }

    .campaign-comparison {
        margin-top: 30px;
    }

    /* Premium Enterprise Statistics */
    .stats-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.15),
            0 0 0 1px rgba(255, 255, 255, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        border: 2px solid transparent;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
        transition: left 0.5s;
        z-index: 0;
    }

    .stat-card:hover::before {
        left: 100%;
    }

    .stat-card:hover {
        transform: translateY(-8px);
        border-color: rgba(102, 126, 234, 0.3);
        box-shadow:
            0 35px 70px rgba(0, 0, 0, 0.2),
            0 0 0 1px rgba(255, 255, 255, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.4);
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        margin: 0 auto 1rem;
        position: relative;
        z-index: 2;
    }

    .stat-card.impressions .stat-icon {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
    }

    .stat-card.clicks .stat-icon {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);
    }

    .stat-card.ctr .stat-icon {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        box-shadow: 0 8px 20px rgba(245, 158, 11, 0.3);
    }

    .stat-card.campaigns .stat-icon {
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        box-shadow: 0 8px 20px rgba(139, 92, 246, 0.3);
    }

    .stat-value {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        color: #1a237e;
        position: relative;
        z-index: 2;
    }

    .stat-label {
        font-weight: 600;
        color: #667eea;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        position: relative;
        z-index: 2;
    }

    /* Premium Enterprise Cards */
    .chart-container {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        overflow: hidden;
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.15),
            0 0 0 1px rgba(255, 255, 255, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        margin-bottom: 2rem;
        transition: all 0.3s ease;
    }

    .chart-container:hover {
        transform: translateY(-5px);
        box-shadow:
            0 35px 70px rgba(0, 0, 0, 0.2),
            0 0 0 1px rgba(255, 255, 255, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.4);
    }

    .chart-header {
        background: linear-gradient(135deg, #1a237e 0%, #283593 100%);
        color: white;
        padding: 1.5rem 2rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
    }

    .chart-header::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    }

    .chart-title {
        font-family: 'Montserrat', sans-serif !important;
        font-size: 1.2rem;
        font-weight: 700;
        margin: 0;
        position: relative;
        z-index: 2;
    }

    .chart-actions {
        display: flex;
        gap: 10px;
    }

    .chart-filter {
        padding: 6px 12px;
        border-radius: 6px;
        border: 1px solid #ddd;
        background-color: #f8f9fa;
        font-size: 14px;
        transition: all 0.2s ease;
    }

    .chart-placeholder {
        height: 300px;
        background-color: #f8f9fa;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #6c757d;
        font-size: 16px;
    }

    .top-ads-container {
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.05);
        padding: 25px;
        margin-bottom: 30px;
        transition: all 0.3s ease;
    }

    .top-ads-container:hover {
        box-shadow: 0 8px 30px rgba(0,0,0,0.1);
    }

    .top-ads-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #f0f0f0;
    }

    .top-ads-title {
        font-size: 18px;
        font-weight: 700;
        color: #343a40;
        margin: 0;
    }

    .top-ads-actions {
        display: flex;
        gap: 10px;
    }

    .ads-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
    }

    .ads-table th {
        background-color: #f8f9fa;
        color: #495057;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 12px;
        letter-spacing: 0.5px;
        padding: 15px;
        border-bottom: 2px solid #dee2e6;
        text-align: left;
    }

    .ads-table td {
        padding: 15px;
        border-bottom: 1px solid #eee;
        vertical-align: middle;
        transition: background-color 0.2s ease;
    }

    .ads-table tr:hover td {
        background-color: #f8f9fa;
    }

    .ads-table tr:last-child td {
        border-bottom: none;
    }

    .ad-title {
        font-weight: 600;
        color: #212529;
    }

    .ad-status {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 30px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
    }

    .status-active {
        background-color: #d4edda;
        color: #155724;
    }

    .status-paused {
        background-color: #e2e3e5;
        color: #383d41;
    }

    .status-pending {
        background-color: #fff3cd;
        color: #856404;
    }

    .status-rejected {
        background-color: #f8d7da;
        color: #721c24;
    }

    .empty-state {
        text-align: center;
        padding: 40px 20px;
    }

    .empty-state-icon {
        font-size: 48px;
        color: #adb5bd;
        margin-bottom: 20px;
    }

    .empty-state-title {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 10px;
        color: #495057;
    }

    .empty-state-description {
        color: #6c757d;
        max-width: 500px;
        margin: 0 auto 20px;
    }

    .action-buttons {
        display: flex;
        justify-content: center;
        gap: 15px;
        margin-top: 30px;
    }

    .action-button {
        padding: 12px 24px;
        border-radius: 6px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    .action-button i {
        margin-right: 8px;
    }

    .primary-button {
        background-color: #007bff;
        color: white;
        border: none;
    }

    .primary-button:hover {
        background-color: #0069d9;
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0, 105, 217, 0.3);
    }

    .secondary-button {
        background-color: #f8f9fa;
        color: #212529;
        border: 1px solid #dee2e6;
    }

    .secondary-button:hover {
        background-color: #e9ecef;
        color: #212529;
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    /* Mobile responsiveness */
    @media (max-width: 992px) {
        .stat-card {
            min-width: calc(50% - 20px);
        }

        .chart-header, .top-ads-header {
            flex-direction: column;
            align-items: flex-start;
        }

        .chart-actions, .top-ads-actions {
            margin-top: 10px;
        }
    }

    @media (max-width: 768px) {
        .ads-table {
            display: block;
            overflow-x: auto;
            white-space: nowrap;
        }

        .action-buttons {
            flex-direction: column;
        }

        .action-button {
            width: 100%;
        }
    }

    @media (max-width: 576px) {
        .stat-card {
            min-width: 100%;
        }

        .stat-value {
            font-size: 28px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container analytics-container">
    <!-- Premium Enterprise Header -->
    <div class="enterprise-header">
        <nav aria-label="breadcrumb" class="mb-3">
            <ol class="breadcrumb" style="background: rgba(255, 255, 255, 0.1); border-radius: 12px; padding: 0.75rem 1.5rem;">
                <li class="breadcrumb-item"><a href="{% url 'index' %}" style="color: rgba(255, 255, 255, 0.8);">Home</a></li>
                <li class="breadcrumb-item"><a href="/ads/" style="color: rgba(255, 255, 255, 0.8);">Ad Dashboard</a></li>
                <li class="breadcrumb-item active" aria-current="page" style="color: white; font-weight: 600;">Analytics</li>
            </ol>
        </nav>

        <div class="text-center">
            <h1 class="enterprise-title">
                <i class="fas fa-chart-line me-3 text-warning"></i>
                Analytics Dashboard
            </h1>
            <p class="enterprise-subtitle">
                Comprehensive insights and performance analytics for your advertising campaigns
            </p>
        </div>
    </div>

    <!-- Enhanced Analytics Filters -->
    <div class="analytics-filters animate__animated animate__fadeIn">
        <div class="row">
            <div class="col-12">
                <h4 class="filter-title"><i class="fas fa-filter me-2"></i>Analytics Filters</h4>
            </div>
        </div>
        <form id="enhanced-filter-form" method="get" action="{% url 'ads:analytics_dashboard' %}">
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label for="date-range">Date Range</label>
                        <div class="input-group date-range-picker">
                            <input type="text" id="date-range" name="date_range" class="form-control" value="{{ date_range|default:'Last 30 Days' }}">
                            <div class="input-group-append">
                                <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group mb-3">
                        <label for="campaign-filter">Campaign</label>
                        <select id="campaign-filter" name="campaign" class="form-control">
                            <option value="">All Campaigns</option>
                            {% for campaign in campaigns %}
                            <option value="{{ campaign.id }}" {% if selected_campaign == campaign.id %}selected{% endif %}>{{ campaign.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group mb-3">
                        <label for="ad-type-filter">Ad Type</label>
                        <select id="ad-type-filter" name="ad_type" class="form-control">
                            <option value="">All Types</option>
                            {% for ad_type in ad_types %}
                            <option value="{{ ad_type.id }}" {% if selected_ad_type == ad_type.id %}selected{% endif %}>{{ ad_type.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group mb-3">
                        <label>&nbsp;</label>
                        <button type="submit" class="btn btn-primary btn-block w-100">
                            <i class="fas fa-search me-2"></i> Apply
                        </button>
                    </div>
                </div>
            </div>
        </form>

        <!-- Export Options -->
        <div class="row mt-3">
            <div class="col-md-6">
                <h5 class="mb-2">Export Data</h5>
                <div class="export-buttons">
                    <a href="{% url 'ads:analytics_dashboard' %}?{{ request.GET.urlencode }}&export=csv" class="export-button btn btn-outline-primary">
                        <i class="fas fa-file-csv me-2"></i> CSV
                    </a>
                    <a href="{% url 'ads:analytics_dashboard' %}?{{ request.GET.urlencode }}&export=excel" class="export-button btn btn-outline-success">
                        <i class="fas fa-file-excel me-2"></i> Excel
                    </a>
                    <a href="{% url 'ads:analytics_dashboard' %}?{{ request.GET.urlencode }}&export=pdf" class="export-button btn btn-outline-danger">
                        <i class="fas fa-file-pdf me-2"></i> PDF
                    </a>
                </div>
            </div>
            <div class="col-md-6 text-end">
                <h5 class="mb-2">Scheduled Reports</h5>
                <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#scheduleReportModal">
                    <i class="fas fa-calendar-alt me-2"></i> Schedule Report
                </button>
            </div>
        </div>
    </div>

    <div class="stats-row">
        <div class="stat-card impressions animate__animated animate__fadeInUp">
            <div class="stat-icon">
                <i class="fas fa-eye"></i>
            </div>
            <div class="stat-value">{{ total_impressions }}</div>
            <div class="stat-label">Total Impressions</div>
            <div class="trend-indicator {% if impression_trend > 0 %}trend-up{% elif impression_trend < 0 %}trend-down{% else %}trend-neutral{% endif %}">
                <i class="fas fa-{% if impression_trend > 0 %}arrow-up{% elif impression_trend < 0 %}arrow-down{% else %}minus{% endif %} me-1"></i>
                <span>{{ impression_trend|default:0|floatformat:1 }}%</span>
            </div>
        </div>

        <div class="stat-card clicks animate__animated animate__fadeInUp" style="animation-delay: 0.1s;">
            <div class="stat-icon">
                <i class="fas fa-mouse-pointer"></i>
            </div>
            <div class="stat-value">{{ total_clicks }}</div>
            <div class="stat-label">Total Clicks</div>
            <div class="trend-indicator {% if click_trend > 0 %}trend-up{% elif click_trend < 0 %}trend-down{% else %}trend-neutral{% endif %}">
                <i class="fas fa-{% if click_trend > 0 %}arrow-up{% elif click_trend < 0 %}arrow-down{% else %}minus{% endif %} me-1"></i>
                <span>{{ click_trend|default:0|floatformat:1 }}%</span>
            </div>
        </div>

        <div class="stat-card ctr animate__animated animate__fadeInUp" style="animation-delay: 0.2s;">
            <div class="stat-icon">
                <i class="fas fa-percentage"></i>
            </div>
            <div class="stat-value">{{ ctr|floatformat:2 }}%</div>
            <div class="stat-label">Click-Through Rate</div>
            <div class="trend-indicator {% if ctr_trend > 0 %}trend-up{% elif ctr_trend < 0 %}trend-down{% else %}trend-neutral{% endif %}">
                <i class="fas fa-{% if ctr_trend > 0 %}arrow-up{% elif ctr_trend < 0 %}arrow-down{% else %}minus{% endif %} me-1"></i>
                <span>{{ ctr_trend|default:0|floatformat:1 }}%</span>
            </div>
        </div>

        <div class="stat-card animate__animated animate__fadeInUp" style="animation-delay: 0.3s;">
            <div class="stat-icon">
                <i class="fas fa-ad"></i>
            </div>
            <div class="stat-value">{{ active_ads_count }}</div>
            <div class="stat-label">Active Ads</div>
        </div>

        <div class="stat-card campaigns animate__animated animate__fadeInUp" style="animation-delay: 0.4s;">
            <div class="stat-icon">
                <i class="fas fa-bullhorn"></i>
            </div>
            <div class="stat-value">{{ active_campaigns_count }}</div>
            <div class="stat-label">Active Campaigns</div>
            {% if active_campaigns_count > 0 %}
            <div class="trend-indicator trend-neutral">
                <a href="{% url 'ads:campaign_list' %}?status=active" class="view-all-link">
                    <i class="fas fa-external-link-alt me-1"></i>
                    <span>View All</span>
                </a>
            </div>
            {% endif %}
        </div>

        <div class="stat-card conversions animate__animated animate__fadeInUp" style="animation-delay: 0.4s;">
            <div class="stat-icon">
                <i class="fas fa-exchange-alt"></i>
            </div>
            <div class="stat-value">{{ total_conversions|default:0 }}</div>
            <div class="stat-label">Conversions</div>
            <div class="trend-indicator {% if conversion_trend > 0 %}trend-up{% elif conversion_trend < 0 %}trend-down{% else %}trend-neutral{% endif %}">
                <i class="fas fa-{% if conversion_trend > 0 %}arrow-up{% elif conversion_trend < 0 %}arrow-down{% else %}minus{% endif %} me-1"></i>
                <span>{{ conversion_trend|default:0|floatformat:1 }}%</span>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="chart-container animate__animated animate__fadeIn">
                <div class="chart-header">
                    <h3 class="chart-title text-dark">Performance Trends</h3>
                    <div class="chart-actions">
                        <form id="chart-filter-form" method="get" action="{% url 'ads:analytics_dashboard' %}">
                            <select class="chart-filter" name="period" id="period-filter">
                                <option value="7days" {% if selected_period == '7days' %}selected{% endif %}>Last 7 Days</option>
                                <option value="30days" {% if selected_period == '30days' or not selected_period %}selected{% endif %}>Last 30 Days</option>
                                <option value="90days" {% if selected_period == '90days' %}selected{% endif %}>Last 90 Days</option>
                                <option value="year" {% if selected_period == 'year' %}selected{% endif %}>Last Year</option>
                            </select>
                            <select class="chart-filter" name="metric" id="metric-filter">
                                <option value="impressions" {% if selected_metric == 'impressions' %}selected{% endif %}>Impressions</option>
                                <option value="clicks" {% if selected_metric == 'clicks' or not selected_metric %}selected{% endif %}>Clicks</option>
                                <option value="ctr" {% if selected_metric == 'ctr' %}selected{% endif %}>CTR</option>
                            </select>
                        </form>
                    </div>
                </div>

                <div class="p-3">
                    {% if total_impressions == 0 and total_clicks == 0 %}
                        <div class="no-data-message">
                            <i class="fas fa-chart-line mb-3" style="font-size: 48px; color: #667eea;"></i>
                            <p>No analytics data available yet</p>
                            <small class="text-muted">Data will appear here as your ads receive impressions and clicks</small>
                            <div class="mt-3">
                                <p class="text-muted">To start collecting data:</p>
                                <ol class="text-start text-muted small">
                                    <li>Make sure your ads are in "active" status</li>
                                    <li>Share your ads with potential customers</li>
                                    <li>Analytics will update automatically as users interact with your ads</li>
                                </ol>
                            </div>
                        </div>
                    {% else %}
                        <canvas id="performanceChart" height="300"></canvas>
                    {% endif %}
                </div>
            </div>

            <!-- Campaign Comparison Chart -->
            <div class="chart-container campaign-comparison animate__animated animate__fadeIn" style="animation-delay: 0.2s;">
                <div class="chart-header">
                    <h3 class="chart-title text-dark">Campaign Comparison</h3>
                    <div class="chart-actions">
                        <select class="chart-filter" id="campaign-metric-selector">
                            <option value="impressions">Compare by Impressions</option>
                            <option value="clicks" selected>Compare by Clicks</option>
                            <option value="ctr">Compare by CTR</option>
                            <option value="conversions">Compare by Conversions</option>
                        </select>
                    </div>
                </div>

                <div class="p-3">
                    {% if campaigns|length > 0 and total_impressions > 0 %}
                        <canvas id="campaignComparisonChart" height="300"></canvas>
                    {% elif campaigns|length > 0 and total_impressions == 0 %}
                        <div class="no-data-message">
                            <i class="fas fa-chart-bar mb-3" style="font-size: 36px; color: #667eea;"></i>
                            <p>No campaign performance data yet</p>
                            <small class="text-muted">You have {{ campaigns|length }} campaign(s), but no performance data yet</small>
                            <p class="text-muted small mt-3">Data will appear here as your ads receive impressions and clicks</p>
                        </div>
                    {% else %}
                        <div class="no-data-message">
                            <i class="fas fa-chart-bar mb-3" style="font-size: 36px; color: #667eea;"></i>
                            <p>No campaigns available</p>
                            <small class="text-muted">Create campaigns to organize your ads and compare their performance</small>
                            <div class="mt-3">
                                <a href="{% url 'ads:campaign_create' %}" class="btn btn-sm btn-primary">Create Campaign</a>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>

            <div class="top-ads-container animate__animated animate__fadeIn" style="animation-delay: 0.3s;">
                <div class="top-ads-header">
                    <h3 class="top-ads-title text-dark">Top Performing Ads</h3>
                    <div class="top-ads-actions">
                        <select class="chart-filter" id="top-ads-sort">
                            <option value="clicks" selected>Sort by Clicks</option>
                            <option value="impressions">Sort by Impressions</option>
                            <option value="ctr">Sort by CTR</option>
                            <option value="conversions">Sort by Conversions</option>
                            <option value="newest">Newest First</option>
                        </select>
                    </div>
                </div>

                {% if top_ads %}
                    <div class="table-responsive">
                        <table class="ads-table">
                            <thead>
                                <tr>
                                    <th>Ad Title</th>
                                    <th>Campaign</th>
                                    <th>Impressions</th>
                                    <th>Clicks</th>
                                    <th>CTR</th>
                                    <th>Conversions</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for ad in top_ads %}
                                    <tr class="animate__animated animate__fadeIn" style="animation-delay: {{ forloop.counter0|floatformat:1 }}s;">
                                        <td class="ad-title">{{ ad.title }}</td>
                                        <td>{{ ad.campaign.name|default:"—" }}</td>
                                        <td>{{ ad.impressions }}</td>
                                        <td>{{ ad.clicks }}</td>
                                        <td>
                                            {{ ad.ctr|floatformat:2 }}%
                                        </td>
                                        <td>{{ ad.conversion_count|default:0 }}</td>
                                        <td>
                                            <span class="ad-status status-{{ ad.status }}">
                                                {{ ad.get_status_display }}
                                            </span>
                                        </td>
                                        <td>
                                            <a href="{% url 'ads:ad_analytics' ad.slug %}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-chart-bar"></i> Details
                                            </a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="empty-state">
                        <div class="empty-state-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h3 class="empty-state-title">No ads to display</h3>
                        <p class="empty-state-description">
                            Create some ads to see performance metrics and analytics.
                        </p>
                        <a href="{% url 'ads:ad_create_consolidated' %}" class="btn btn-primary">Create New Ad</a>
                    </div>
                {% endif %}
            </div>
        </div>

        <div class="col-lg-4">
            <div class="chart-container animate__animated animate__fadeIn" style="animation-delay: 0.1s;">
                <div class="chart-header">
                    <h3 class="chart-title">Device Breakdown</h3>
                </div>

                <div class="p-3">
                    <canvas id="deviceChart" height="200"></canvas>
                    {% if total_impressions == 0 and total_clicks == 0 %}
                        <div class="text-center mt-2">
                            <small class="text-muted">Device information will be collected when users view your ads</small>
                        </div>
                    {% endif %}
                </div>
            </div>

            <div class="chart-container animate__animated animate__fadeIn" style="animation-delay: 0.2s;">
                <div class="chart-header">
                    <h3 class="chart-title">Geographic Distribution</h3>
                </div>

                <div class="p-3">
                    <canvas id="locationChart" height="200"></canvas>
                    {% if total_impressions == 0 and total_clicks == 0 %}
                        <div class="text-center mt-2">
                            <small class="text-muted">Geographic data will be collected when users interact with your ads</small>
                        </div>
                    {% endif %}
                </div>
            </div>

            <div class="chart-container animate__animated animate__fadeIn" style="animation-delay: 0.3s;">
                <div class="chart-header">
                    <h3 class="chart-title">Performance Metrics</h3>
                </div>

                <div class="metrics-container">
                    <div class="metric-item">
                        <div class="metric-label">Average CTR</div>
                        <div class="metric-value">{{ ctr|floatformat:2 }}%</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Avg. Daily Impressions</div>
                        <div class="metric-value" id="avgImpressions">-</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Avg. Daily Clicks</div>
                        <div class="metric-value" id="avgClicks">-</div>
                    </div>
                    {% if total_impressions == 0 and total_clicks == 0 %}
                        <div class="text-center mt-3 w-100">
                            <small class="text-muted">Performance metrics will be calculated as your ads receive impressions and clicks</small>
                            {% if active_ads_count == 0 %}
                                <div class="mt-2">
                                    <a href="{% url 'ads:ad_create_consolidated' %}" class="btn btn-sm btn-primary">Create Your First Ad</a>
                                </div>
                            {% elif active_ads_count > 0 %}
                                <div class="mt-2">
                                    <p class="text-muted small">You have {{ active_ads_count }} active ad(s). Share them to start collecting data.</p>
                                </div>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="action-buttons">
        <a href="/ads/" class="action-button secondary-button">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
        <a href="{% url 'ads:ad_create_consolidated' %}" class="action-button primary-button">
            <i class="fas fa-plus"></i> Create New Ad
        </a>
        <a href="/ads/list/" class="action-button secondary-button">
            <i class="fas fa-list"></i> View All Ads
        </a>
    </div>
</div>
{% endblock %}

<!-- Schedule Report Modal -->
<div class="modal fade" id="scheduleReportModal" tabindex="-1" aria-labelledby="scheduleReportModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="scheduleReportModalLabel">Schedule Automated Report</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="schedule-report-form" action="{% url 'ads:analytics_dashboard' %}" method="post">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="schedule_report">

                    <div class="mb-3">
                        <label for="report-name" class="form-label">Report Name</label>
                        <input type="text" class="form-control" id="report-name" name="report_name" required>
                    </div>

                    <div class="mb-3">
                        <label for="report-frequency" class="form-label">Frequency</label>
                        <select class="form-select" id="report-frequency" name="frequency" required>
                            <option value="daily">Daily</option>
                            <option value="weekly" selected>Weekly</option>
                            <option value="monthly">Monthly</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="report-format" class="form-label">Format</label>
                        <select class="form-select" id="report-format" name="format" required>
                            <option value="pdf">PDF</option>
                            <option value="excel">Excel</option>
                            <option value="csv">CSV</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="report-email" class="form-label">Email Recipients</label>
                        <input type="email" class="form-control" id="report-email" name="email" value="{{ request.user.email }}" required>
                        <div class="form-text">Separate multiple emails with commas</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="schedule-report-form" class="btn btn-primary">Schedule Report</button>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/moment/min/moment.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
<script src="{% static 'js/analytics-dashboard.js' %}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add animations with delays
        const statCards = document.querySelectorAll('.stat-card');
        statCards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
        });

        // Add hover effects
        const chartContainers = document.querySelectorAll('.chart-container, .top-ads-container');
        chartContainers.forEach(container => {
            container.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
                this.style.boxShadow = '0 8px 30px rgba(0,0,0,0.1)';
            });

            container.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '0 4px 20px rgba(0,0,0,0.05)';
            });
        });

        // Initialize date range picker with current value or default
        const currentDateRange = '{{ date_range }}';
        let startDate, endDate;

        // Parse the current date range if it's in the format "MMM D, YYYY - MMM D, YYYY"
        if (currentDateRange && currentDateRange.includes(' - ')) {
            const [start, end] = currentDateRange.split(' - ');
            startDate = moment(start, 'MMM D, YYYY');
            endDate = moment(end, 'MMM D, YYYY');
        } else {
            // Default to last 30 days
            startDate = moment().subtract(29, 'days');
            endDate = moment();
        }

        $('#date-range').daterangepicker({
            ranges: {
               'Today': [moment(), moment()],
               'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
               'Last 7 Days': [moment().subtract(6, 'days'), moment()],
               'Last 30 Days': [moment().subtract(29, 'days'), moment()],
               'This Month': [moment().startOf('month'), moment().endOf('month')],
               'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
            },
            startDate: startDate,
            endDate: endDate,
            locale: {
                format: 'MMM D, YYYY'
            }
        });

        // Submit form when date range changes
        $('#date-range').on('apply.daterangepicker', function(ev, picker) {
            $('#enhanced-filter-form').submit();
        });

        // Handle campaign and ad type filter changes
        $('#campaign-filter, #ad-type-filter').on('change', function() {
            // Submit the form when a filter is changed
            $('#enhanced-filter-form').submit();
        });

        // Top ads sorting
        const topAdsSort = document.getElementById('top-ads-sort');
        if (topAdsSort) {
            topAdsSort.addEventListener('change', function() {
                const sortBy = this.value;
                const table = document.querySelector('.ads-table');
                const tbody = table.querySelector('tbody');
                const rows = Array.from(tbody.querySelectorAll('tr'));

                // Get the column index based on the sort option
                let columnIndex;
                switch(sortBy) {
                    case 'impressions': columnIndex = 2; break;
                    case 'clicks': columnIndex = 3; break;
                    case 'ctr': columnIndex = 4; break;
                    case 'conversions': columnIndex = 5; break;
                    case 'newest': columnIndex = null; break; // Special case
                    default: columnIndex = 3; // Default to clicks
                }

                // Sort the rows
                rows.sort((a, b) => {
                    if (sortBy === 'newest') {
                        // Sort by data attribute if available, otherwise keep original order
                        const dateA = a.getAttribute('data-created') || '0';
                        const dateB = b.getAttribute('data-created') || '0';
                        return dateB.localeCompare(dateA);
                    } else {
                        const cellA = a.querySelectorAll('td')[columnIndex].textContent.trim();
                        const cellB = b.querySelectorAll('td')[columnIndex].textContent.trim();

                        // Parse numbers from the cells
                        const valueA = parseFloat(cellA.replace('%', '')) || 0;
                        const valueB = parseFloat(cellB.replace('%', '')) || 0;

                        return valueB - valueA; // Descending order
                    }
                });

                // Reappend rows in the new order
                rows.forEach(row => tbody.appendChild(row));
            });
        }

        // Filter functionality
        const periodFilter = document.getElementById('period-filter');
        const metricFilter = document.getElementById('metric-filter');
        const filterForm = document.getElementById('chart-filter-form');

        periodFilter.addEventListener('change', function() {
            filterForm.submit();
        });

        metricFilter.addEventListener('change', function() {
            filterForm.submit();
        });

        // Performance Chart
        const selectedMetric = '{{ selected_metric|default:"clicks" }}';

        // Chart data
        const chartData = JSON.parse('{{ chart_data_json|escapejs }}');
        const chartLabels = chartData.labels;
        const impressionsData = chartData.impressions;
        const clicksData = chartData.clicks;
        const ctrData = chartData.ctr;

        // Only initialize charts if we have data and the elements exist
        const performanceChartElement = document.getElementById('performanceChart');
        let performanceChart, deviceChart, locationChart;

        // Determine which dataset to display based on selected metric
        let displayData, dataLabel, borderColor, backgroundColor;

        if (selectedMetric === 'impressions') {
            displayData = impressionsData;
            dataLabel = 'Impressions';
            borderColor = 'rgba(75, 192, 192, 1)';
            backgroundColor = 'rgba(75, 192, 192, 0.2)';
        } else if (selectedMetric === 'ctr') {
            displayData = ctrData;
            dataLabel = 'CTR (%)';
            borderColor = 'rgba(153, 102, 255, 1)';
            backgroundColor = 'rgba(153, 102, 255, 0.2)';
        } else {
            // Default to clicks
            displayData = clicksData;
            dataLabel = 'Clicks';
            borderColor = 'rgba(255, 159, 64, 1)';
            backgroundColor = 'rgba(255, 159, 64, 0.2)';
        }

        if (performanceChartElement && {{ total_impressions }} > 0) {
            const performanceCtx = performanceChartElement.getContext('2d');
            performanceChart = new Chart(performanceCtx, {
                type: 'line',
                data: {
                    labels: chartLabels,
                    datasets: [{
                        label: dataLabel,
                        data: displayData,
                        borderColor: borderColor,
                        backgroundColor: backgroundColor,
                        borderWidth: 2,
                        tension: 0.3,
                        fill: true,
                        pointBackgroundColor: borderColor,
                        pointRadius: 3,
                        pointHoverRadius: 5
                    }]
                },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (selectedMetric === 'ctr') {
                                    label += context.parsed.y.toFixed(2) + '%';
                                } else {
                                    label += context.parsed.y;
                                }
                                return label;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                if (selectedMetric === 'ctr') {
                                    return value + '%';
                                }
                                return value;
                            }
                        }
                    }
                }
            }
        });

        // Device Chart
        let deviceData = {};
        try {
            deviceData = JSON.parse('{{ device_data_json|escapejs }}');
            console.log("Device data:", deviceData);
            // If empty object, provide default structure
            if (Object.keys(deviceData).length === 0) {
                deviceData = {
                    'desktop': 0,
                    'mobile': 0,
                    'tablet': 0
                };
            }
        } catch (e) {
            console.error("Error parsing device data:", e);
            deviceData = {
                'desktop': 0,
                'mobile': 0,
                'tablet': 0
            };
        }
        const deviceLabels = Object.keys(deviceData);
        const deviceValues = Object.values(deviceData);
        const deviceColors = [
            'rgba(255, 99, 132, 0.7)',
            'rgba(54, 162, 235, 0.7)',
            'rgba(255, 206, 86, 0.7)',
            'rgba(75, 192, 192, 0.7)',
            'rgba(153, 102, 255, 0.7)'
        ];

        const deviceChartElement = document.getElementById('deviceChart');
        if (deviceChartElement) {
            const deviceCtx = deviceChartElement.getContext('2d');
            deviceChart = new Chart(deviceCtx, {
            type: 'doughnut',
            data: {
                labels: deviceLabels.map(label => label.charAt(0).toUpperCase() + label.slice(1)),
                datasets: [{
                    data: deviceValues,
                    backgroundColor: deviceColors,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    }
                }
            }
        });
        }

        // Location Chart
        let locationData = {};
        try {
            locationData = JSON.parse('{{ location_data_json|escapejs }}');
            console.log("Location data:", locationData);
            // If empty object, provide default structure
            if (Object.keys(locationData).length === 0) {
                locationData = {
                    'Nairobi': 0,
                    'Mombasa': 0,
                    'Kisumu': 0
                };
            }
        } catch (e) {
            console.error("Error parsing location data:", e);
            locationData = {
                'Nairobi': 0,
                'Mombasa': 0,
                'Kisumu': 0
            };
        }
        const locationLabels = Object.keys(locationData);
        const locationValues = Object.values(locationData);
        const locationColors = [
            'rgba(255, 99, 132, 0.7)',
            'rgba(54, 162, 235, 0.7)',
            'rgba(255, 206, 86, 0.7)',
            'rgba(75, 192, 192, 0.7)',
            'rgba(153, 102, 255, 0.7)'
        ];

        const locationChartElement = document.getElementById('locationChart');
        if (locationChartElement) {
            const locationCtx = locationChartElement.getContext('2d');
            locationChart = new Chart(locationCtx, {
            type: 'bar',
            data: {
                labels: locationLabels,
                datasets: [{
                    label: 'Impressions by Location',
                    data: locationValues,
                    backgroundColor: locationColors,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        }

        // Calculate and display average metrics
        const calculateAverage = (data) => {
            if (!data || data.length === 0) return 0;
            const sum = data.reduce((a, b) => Number(a) + Number(b), 0);
            return Math.round(sum / data.length);
        };

        const avgImpressionsElement = document.getElementById('avgImpressions');
        const avgClicksElement = document.getElementById('avgClicks');

        if (avgImpressionsElement) {
            avgImpressionsElement.textContent = calculateAverage(impressionsData);
        }

        if (avgClicksElement) {
            avgClicksElement.textContent = calculateAverage(clicksData);
        }

        // Campaign Comparison Chart
        const campaignComparisonElement = document.getElementById('campaignComparisonChart');
        if (campaignComparisonElement) {
            // This would normally come from the backend
            const campaignData = {
                labels: [{% for campaign in campaigns %}'{{ campaign.name }}',{% endfor %}],
                impressions: [{% for campaign in campaigns %}{{ campaign.total_impressions }},{% endfor %}],
                clicks: [{% for campaign in campaigns %}{{ campaign.total_clicks }},{% endfor %}],
                ctr: [{% for campaign in campaigns %}{{ campaign.ctr|floatformat:2 }},{% endfor %}],
                conversions: [{% for campaign in campaigns %}{{ campaign.total_conversions|default:0 }},{% endfor %}]
            };

            const campaignCtx = campaignComparisonElement.getContext('2d');
            const campaignMetricSelector = document.getElementById('campaign-metric-selector');

            // Default to clicks if not specified
            let selectedCampaignMetric = campaignMetricSelector ? campaignMetricSelector.value : 'clicks';

            // Function to update the chart based on selected metric
            const updateCampaignChart = (metric) => {
                let chartData, chartLabel, chartColor;

                switch(metric) {
                    case 'impressions':
                        chartData = campaignData.impressions;
                        chartLabel = 'Impressions';
                        chartColor = 'rgba(75, 192, 192, 0.7)';
                        break;
                    case 'ctr':
                        chartData = campaignData.ctr;
                        chartLabel = 'CTR (%)';
                        chartColor = 'rgba(153, 102, 255, 0.7)';
                        break;
                    case 'conversions':
                        chartData = campaignData.conversions;
                        chartLabel = 'Conversions';
                        chartColor = 'rgba(255, 99, 132, 0.7)';
                        break;
                    default: // clicks
                        chartData = campaignData.clicks;
                        chartLabel = 'Clicks';
                        chartColor = 'rgba(255, 159, 64, 0.7)';
                }

                // Create or update chart
                if (window.campaignChart) {
                    window.campaignChart.data.datasets[0].data = chartData;
                    window.campaignChart.data.datasets[0].label = chartLabel;
                    window.campaignChart.data.datasets[0].backgroundColor = chartColor;
                    window.campaignChart.update();
                } else {
                    window.campaignChart = new Chart(campaignCtx, {
                        type: 'bar',
                        data: {
                            labels: campaignData.labels,
                            datasets: [{
                                label: chartLabel,
                                data: chartData,
                                backgroundColor: chartColor,
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        callback: function(value) {
                                            if (metric === 'ctr') {
                                                return value + '%';
                                            }
                                            return value;
                                        }
                                    }
                                }
                            }
                        }
                    });
                }
            };

            // Initialize chart with default metric
            updateCampaignChart(selectedCampaignMetric);

            // Add event listener for metric selector
            if (campaignMetricSelector) {
                campaignMetricSelector.addEventListener('change', function() {
                    updateCampaignChart(this.value);
                });
            }
        }
    });
</script>
<style>
    .metrics-container {
        display: flex;
        flex-direction: column;
        gap: 15px;
        padding: 15px;
    }

    .metric-item {
        background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
        border-radius: 8px;
        padding: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
    }

    .metric-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .metric-label {
        font-size: 14px;
        color: #6c757d;
        margin-bottom: 5px;
    }

    .metric-value {
        font-size: 24px;
        font-weight: 700;
        color: #212529;
    }

    .no-data-message {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 30px;
        text-align: center;
        color: #6c757d;
        height: 100%;
        min-height: 200px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 8px;
    }

    .no-data-message p {
        margin-bottom: 5px;
        font-weight: 500;
    }

    .no-data-message small {
        max-width: 80%;
    }

    .chart-content {
        padding: 15px;
        background-color: #fff;
        border-radius: 0 0 8px 8px;
        height: 250px;
        position: relative;
    }
</style>
{% endblock %}
