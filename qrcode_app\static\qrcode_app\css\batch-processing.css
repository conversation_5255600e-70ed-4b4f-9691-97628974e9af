/* Batch Processing Styles */
:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #60a5fa;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #6366f1;
    --text-dark: #1f2937;
    --text-medium: #4b5563;
    --text-light: #9ca3af;
    --bg-light: #f9fafb;
    --bg-medium: #f3f4f6;
    --border-color: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --transition: all 0.2s ease;
}

/* Batch Processing Layout */
.batch-processing-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.batch-processing-header {
    margin-bottom: 2rem;
}

.batch-processing-header h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-dark);
    margin: 0 0 0.5rem 0;
}

.batch-processing-header p {
    font-size: 1rem;
    color: var(--text-medium);
    margin: 0;
}

/* Batch Steps */
.batch-steps {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
}

.batch-step {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background-color: white;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.batch-step.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    box-shadow: 0 4px 6px -1px rgba(37, 99, 235, 0.3);
}

.step-number {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--bg-medium);
    color: var(--text-medium);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
    transition: var(--transition);
}

.batch-step.active .step-number {
    background-color: white;
    color: var(--primary-color);
}

.step-content h3 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 0.25rem 0;
    transition: var(--transition);
}

.step-content p {
    font-size: 0.75rem;
    color: var(--text-medium);
    margin: 0;
    transition: var(--transition);
}

.batch-step.active .step-content h3,
.batch-step.active .step-content p {
    color: white;
}

.step-connector {
    flex-grow: 1;
    height: 2px;
    background-color: var(--border-color);
    margin: 0 0.5rem;
}

/* Batch Step Content */
.batch-step-content {
    background-color: white;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
    padding: 2rem;
    margin-bottom: 2rem;
    display: none;
}

.batch-step-content.active {
    display: block;
}

/* Step Actions */
.step-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 2rem;
}

.primary-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.primary-btn:hover {
    box-shadow: 0 4px 6px -1px rgba(37, 99, 235, 0.3);
    transform: translateY(-1px);
}

.primary-btn:disabled {
    background: linear-gradient(135deg, #9ca3af, #6b7280);
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
}

.secondary-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background-color: white;
    color: var(--text-dark);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.secondary-btn:hover {
    background-color: var(--bg-light);
}

.danger-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background-color: white;
    color: var(--danger-color);
    border: 1px solid var(--danger-color);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.danger-btn:hover {
    background-color: rgba(239, 68, 68, 0.1);
}

/* Input Methods */
.input-methods {
    margin-bottom: 2rem;
}

.input-method-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 1.5rem;
}

.input-method-tab {
    padding: 0.75rem 1.5rem;
    background: none;
    border: none;
    border-bottom: 2px solid transparent;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-medium);
    cursor: pointer;
    transition: var(--transition);
}

.input-method-tab:hover {
    color: var(--primary-color);
}

.input-method-tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.input-method-content {
    display: none;
}

.input-method-content.active {
    display: block;
}

/* CSV Upload */
.csv-upload-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 1.5rem;
}

.csv-upload-area {
    border: 2px dashed var(--border-color);
    border-radius: var(--radius-md);
    padding: 2rem;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
}

.csv-upload-area:hover {
    border-color: var(--primary-color);
    background-color: rgba(59, 130, 246, 0.05);
}

.csv-upload-area i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.csv-upload-area h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 0.5rem 0;
}

.csv-upload-area p {
    font-size: 0.875rem;
    color: var(--text-medium);
    margin: 0 0 1rem 0;
}

.csv-upload-btn {
    display: inline-block;
    padding: 0.5rem 1rem;
    background-color: var(--primary-color);
    color: white;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.csv-upload-btn:hover {
    background-color: var(--primary-dark);
}

.csv-template {
    background-color: var(--bg-light);
    border-radius: var(--radius-md);
    padding: 1.5rem;
}

.csv-template h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 0.5rem 0;
}

.csv-template p {
    font-size: 0.875rem;
    color: var(--text-medium);
    margin: 0 0 1rem 0;
}

.csv-preview {
    margin-top: 2rem;
}

.csv-preview h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 1rem 0;
}

.csv-preview-table-container {
    overflow-x: auto;
    margin-bottom: 1.5rem;
}

.csv-preview-table {
    width: 100%;
    border-collapse: collapse;
}

.csv-preview-table th,
.csv-preview-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.875rem;
}

.csv-preview-table th {
    font-weight: 600;
    color: var(--text-dark);
    background-color: var(--bg-light);
}

.csv-preview-table td {
    color: var(--text-medium);
}

.csv-mapping {
    background-color: var(--bg-light);
    border-radius: var(--radius-md);
    padding: 1.5rem;
}

.csv-mapping h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 1rem 0;
}

/* Manual Input */
.manual-input-container {
    max-width: 800px;
}

/* API Input */
.api-input-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.api-endpoint-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.api-response-preview {
    background-color: var(--bg-light);
    border-radius: var(--radius-md);
    padding: 1.5rem;
}

.api-response-preview h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 1rem 0;
}

.api-response-container {
    background-color: var(--text-dark);
    border-radius: var(--radius-md);
    padding: 1rem;
    margin-bottom: 1.5rem;
    overflow-x: auto;
}

.api-response-container pre {
    margin: 0;
    font-family: 'Courier New', Courier, monospace;
    font-size: 0.875rem;
    line-height: 1.6;
    color: white;
}

.api-data-preview {
    margin-top: 1.5rem;
}

.api-data-container {
    background-color: white;
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
    padding: 1rem;
    overflow-x: auto;
}

.api-data-container pre {
    margin: 0;
    font-family: 'Courier New', Courier, monospace;
    font-size: 0.875rem;
    line-height: 1.6;
    color: var(--text-dark);
}

/* Form Elements */
.form-group {
    margin-bottom: 1.25rem;
}

.form-group label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-medium);
    margin-bottom: 0.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    color: var(--text-dark);
    background-color: white;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    outline: none;
}

.form-group input[type="color"] {
    height: 2.5rem;
    padding: 0.25rem;
}

.form-help {
    font-size: 0.75rem;
    color: var(--text-light);
    margin: 0.5rem 0 0 0;
}

.form-help i {
    margin-right: 0.25rem;
}

/* Toggle Switch */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 3rem;
    height: 1.5rem;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--text-light);
    transition: var(--transition);
    border-radius: 1.5rem;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 1.125rem;
    width: 1.125rem;
    left: 0.1875rem;
    bottom: 0.1875rem;
    background-color: white;
    transition: var(--transition);
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: var(--primary-color);
}

input:focus + .toggle-slider {
    box-shadow: 0 0 1px var(--primary-color);
}

input:checked + .toggle-slider:before {
    transform: translateX(1.5rem);
}

/* Configuration */
.batch-configuration {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.config-section {
    background-color: var(--bg-light);
    border-radius: var(--radius-md);
    padding: 1.5rem;
}

.config-section h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 1.25rem 0;
}

.config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
}

/* Processing Status */
.batch-processing-status {
    margin-bottom: 2rem;
}

.processing-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.processing-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0;
}

.processing-actions {
    display: flex;
    gap: 1rem;
}

.processing-progress {
    margin-bottom: 1.5rem;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.progress-info span {
    font-size: 0.875rem;
    color: var(--text-medium);
}

.progress-bar-container {
    height: 0.5rem;
    background-color: var(--bg-medium);
    border-radius: var(--radius-sm);
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    border-radius: var(--radius-sm);
    transition: width 0.3s ease;
}

.processing-stats {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-card {
    background-color: var(--bg-light);
    border-radius: var(--radius-md);
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(59, 130, 246, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: var(--primary-color);
}

.stat-content h4 {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-medium);
    margin: 0 0 0.25rem 0;
}

.stat-content p {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--text-dark);
    margin: 0;
}

.processing-log {
    background-color: var(--bg-light);
    border-radius: var(--radius-md);
    padding: 1.5rem;
}

.processing-log h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 1rem 0;
}

.log-container {
    background-color: white;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: 1rem;
    height: 200px;
    overflow-y: auto;
}

.log-entry {
    font-size: 0.875rem;
    color: var(--text-medium);
    margin-bottom: 0.5rem;
    display: flex;
    gap: 0.75rem;
}

.log-time {
    color: var(--text-light);
    flex-shrink: 0;
}

/* Download */
.batch-download {
    margin-bottom: 2rem;
}

.download-summary {
    background-color: var(--bg-light);
    border-radius: var(--radius-md);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.download-summary h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 1rem 0;
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
}

.summary-stat {
    background-color: white;
    border-radius: var(--radius-md);
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.stat-label {
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--text-medium);
}

.stat-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-dark);
}

.stat-value.success {
    color: var(--success-color);
}

.stat-value.error {
    color: var(--danger-color);
}

.download-options {
    background-color: white;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid var(--border-color);
}

.download-options h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 1rem 0;
}

.download-buttons {
    display: flex;
    gap: 1rem;
}

.download-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.download-btn:hover {
    background-color: var(--primary-dark);
}

.qr-preview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
    margin-top: 1.5rem;
}

.qr-preview-item {
    background-color: white;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    padding: 1rem;
    border: 1px solid var(--border-color);
    text-align: center;
}

.qr-preview-item img {
    max-width: 100%;
    height: auto;
    border-radius: var(--radius-sm);
}

.qr-preview-item p {
    font-size: 0.75rem;
    color: var(--text-medium);
    margin: 0.5rem 0 0 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .batch-configuration {
        grid-template-columns: 1fr;
    }
    
    .api-input-container {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .batch-processing-container {
        padding: 1.5rem;
    }
    
    .batch-steps {
        flex-direction: column;
        gap: 1rem;
    }
    
    .batch-step {
        width: 100%;
    }
    
    .step-connector {
        width: 2px;
        height: 1rem;
        margin: 0;
    }
    
    .csv-upload-container {
        grid-template-columns: 1fr;
    }
    
    .download-buttons {
        flex-direction: column;
    }
}
