from django.test import TestCase
from django.urls import reverse
from django.contrib.auth.models import User
from .models import QRCode, UserProfile, APIKey, QRCodeBatch


class QRCodeModelTest(TestCase):
    def setUp(self):
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        
        # Create a user profile
        self.profile = UserProfile.objects.create(
            user=self.user,
            role='user'
        )
        
        # Create a test QR code
        self.qr_code = QRCode.objects.create(
            user=self.user,
            name='Test QR Code',
            data='https://example.com',
            qr_type='url',
            foreground_color='#000000',
            background_color='#FFFFFF'
        )
    
    def test_qr_code_creation(self):
        """Test QR code creation"""
        self.assertEqual(self.qr_code.name, 'Test QR Code')
        self.assertEqual(self.qr_code.data, 'https://example.com')
        self.assertEqual(self.qr_code.qr_type, 'url')
        self.assertEqual(self.qr_code.user, self.user)
    
    def test_qr_code_str_method(self):
        """Test QR code string representation"""
        self.assertEqual(str(self.qr_code), 'Test QR Code')


class UserProfileModelTest(TestCase):
    def setUp(self):
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        
        # Create a user profile
        self.profile = UserProfile.objects.create(
            user=self.user,
            role='admin',
            company='Test Company',
            phone='1234567890',
            address='123 Test St'
        )
    
    def test_user_profile_creation(self):
        """Test user profile creation"""
        self.assertEqual(self.profile.user, self.user)
        self.assertEqual(self.profile.role, 'admin')
        self.assertEqual(self.profile.company, 'Test Company')
    
    def test_user_profile_str_method(self):
        """Test user profile string representation"""
        self.assertEqual(str(self.profile), 'testuser Profile')


class APIKeyModelTest(TestCase):
    def setUp(self):
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        
        # Create an API key
        self.api_key = APIKey.objects.create(
            user=self.user,
            name='Test API Key'
        )
    
    def test_api_key_creation(self):
        """Test API key creation"""
        self.assertEqual(self.api_key.user, self.user)
        self.assertEqual(self.api_key.name, 'Test API Key')
        self.assertTrue(self.api_key.key)  # Key should be auto-generated
        self.assertTrue(self.api_key.is_active)  # Should be active by default
    
    def test_api_key_str_method(self):
        """Test API key string representation"""
        self.assertEqual(str(self.api_key), 'Test API Key')


class ViewsTest(TestCase):
    def setUp(self):
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        
        # Create a user profile
        self.profile = UserProfile.objects.create(
            user=self.user,
            role='user'
        )
    
    def test_index_view(self):
        """Test index view"""
        response = self.client.get(reverse('index'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'base.html')
    
    def test_generate_qr_code_view_requires_login(self):
        """Test that generate QR code view requires login"""
        response = self.client.get(reverse('generate_qr_code'))
        self.assertEqual(response.status_code, 302)  # Redirect to login
        
        # Login and try again
        self.client.login(username='testuser', password='testpassword')
        response = self.client.get(reverse('generate_qr_code'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'qrcode_app/generate_qr_code.html')
