/* Dashboard Notification Panel Styles */

.dashboard-notification-panel {
    position: fixed;
    top: 80px;
    right: 20px;
    width: 350px;
    max-width: 90vw;
    max-height: 80vh;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    z-index: 1050;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    transform: translateY(-20px);
    opacity: 0;
    pointer-events: none;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.dashboard-notification-panel.show {
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
}

/* Panel Header */
.dashboard-notification-panel .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #e5e7eb;
    background: linear-gradient(135deg, #1a2a6c, #2a4065);
    color: white;
}

.dashboard-notification-panel .panel-header h4 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.dashboard-notification-panel .close-panel {
    background: none;
    border: none;
    color: white;
    font-size: 1rem;
    cursor: pointer;
    padding: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.2s ease;
}

.dashboard-notification-panel .close-panel:hover {
    transform: scale(1.1);
}

/* Panel Content */
.dashboard-notification-panel .panel-content {
    padding: 0;
    overflow-y: auto;
    flex: 1;
    max-height: calc(80vh - 60px);
}

/* Loading Spinner */
.dashboard-notification-panel .loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px 20px;
    color: #6b7280;
}

.dashboard-notification-panel .loading-spinner i {
    font-size: 2rem;
    margin-bottom: 15px;
    color: #3b82f6;
}

/* Empty State */
.dashboard-notification-panel .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: #6b7280;
}

.dashboard-notification-panel .empty-state i {
    font-size: 2.5rem;
    margin-bottom: 15px;
    color: #9ca3af;
}

/* Error State */
.dashboard-notification-panel .error-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px 20px;
    color: #6b7280;
}

.dashboard-notification-panel .error-state i {
    font-size: 2rem;
    margin-bottom: 15px;
    color: #ef4444;
}

.dashboard-notification-panel .error-state .retry-btn {
    margin-top: 15px;
}

/* Notification Item */
.dashboard-notification-panel .notification-item {
    display: flex;
    padding: 15px 20px;
    border-bottom: 1px solid #e5e7eb;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.dashboard-notification-panel .notification-item:hover {
    background-color: #f9fafb;
}

.dashboard-notification-panel .notification-item.unread {
    background-color: rgba(59, 130, 246, 0.05);
}

.dashboard-notification-panel .notification-item.unread:hover {
    background-color: rgba(59, 130, 246, 0.1);
}

.dashboard-notification-panel .notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(59, 130, 246, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    flex-shrink: 0;
}

.dashboard-notification-panel .notification-icon i {
    color: #3b82f6;
    font-size: 1.2rem;
}

.dashboard-notification-panel .notification-content {
    flex: 1;
}

.dashboard-notification-panel .notification-title {
    font-weight: 600;
    margin-bottom: 5px;
    color: #1f2937;
}

.dashboard-notification-panel .notification-message {
    font-size: 0.9rem;
    color: #4b5563;
    margin-bottom: 5px;
}

.dashboard-notification-panel .notification-time {
    font-size: 0.8rem;
    color: #6b7280;
}

/* Notification Types */
.dashboard-notification-panel .notification-item.success .notification-icon {
    background-color: rgba(16, 185, 129, 0.1);
}

.dashboard-notification-panel .notification-item.success .notification-icon i {
    color: #10b981;
}

.dashboard-notification-panel .notification-item.warning .notification-icon {
    background-color: rgba(245, 158, 11, 0.1);
}

.dashboard-notification-panel .notification-item.warning .notification-icon i {
    color: #f59e0b;
}

.dashboard-notification-panel .notification-item.error .notification-icon {
    background-color: rgba(239, 68, 68, 0.1);
}

.dashboard-notification-panel .notification-item.error .notification-icon i {
    color: #ef4444;
}

/* View All Link */
.dashboard-notification-panel .view-all-link {
    display: block;
    text-align: center;
    padding: 15px;
    background-color: #f9fafb;
    color: #3b82f6;
    font-weight: 500;
    text-decoration: none;
    border-top: 1px solid #e5e7eb;
}

.dashboard-notification-panel .view-all-link:hover {
    background-color: #f3f4f6;
    text-decoration: underline;
}
