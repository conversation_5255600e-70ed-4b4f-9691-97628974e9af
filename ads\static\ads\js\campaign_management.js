// Campaign Management JavaScript

// Initialize components when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Setup campaign search functionality
    setupCampaignSearch();
    
    // Setup tab navigation
    setupTabNavigation();
    
    // Setup form validation
    setupFormValidation();
});

// Setup campaign search functionality
function setupCampaignSearch() {
    const searchInput = document.getElementById('campaign-search');
    if (!searchInput) return;
    
    searchInput.addEventListener('keyup', function() {
        const searchTerm = this.value.toLowerCase();
        const campaignRows = document.querySelectorAll('tbody tr');
        
        campaignRows.forEach(row => {
            const campaignName = row.querySelector('.campaign-name').textContent.toLowerCase();
            
            if (campaignName.includes(searchTerm)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    });
}

// Setup tab navigation
function setupTabNavigation() {
    const navLinks = document.querySelectorAll('.nav-link[data-bs-toggle="tab"]');
    if (navLinks.length === 0) return;
    
    navLinks.forEach(link => {
        link.addEventListener('shown.bs.tab', function(event) {
            // Update URL hash to allow direct linking to tabs
            window.location.hash = event.target.getAttribute('href');
        });
    });
    
    // Activate tab based on URL hash
    if (window.location.hash) {
        const activeTab = document.querySelector(`.nav-link[href="${window.location.hash}"]`);
        if (activeTab) {
            const tab = new bootstrap.Tab(activeTab);
            tab.show();
        }
    }
}

// Setup form validation
function setupFormValidation() {
    const campaignForm = document.getElementById('campaign-form');
    if (!campaignForm) return;
    
    campaignForm.addEventListener('submit', function(event) {
        if (!validateCampaignForm()) {
            event.preventDefault();
            event.stopPropagation();
        }
    });
}

// Validate campaign form
function validateCampaignForm() {
    let isValid = true;
    
    // Get form elements
    const nameInput = document.getElementById('name');
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');
    const budgetInput = document.getElementById('budget');
    
    // Validate name
    if (!nameInput.value.trim()) {
        showValidationError(nameInput, 'Campaign name is required');
        isValid = false;
    } else {
        clearValidationError(nameInput);
    }
    
    // Validate start date
    if (!startDateInput.value) {
        showValidationError(startDateInput, 'Start date is required');
        isValid = false;
    } else {
        clearValidationError(startDateInput);
    }
    
    // Validate end date
    if (!endDateInput.value) {
        showValidationError(endDateInput, 'End date is required');
        isValid = false;
    } else if (new Date(endDateInput.value) <= new Date(startDateInput.value)) {
        showValidationError(endDateInput, 'End date must be after start date');
        isValid = false;
    } else {
        clearValidationError(endDateInput);
    }
    
    // Validate budget
    if (budgetInput.value < 0) {
        showValidationError(budgetInput, 'Budget cannot be negative');
        isValid = false;
    } else {
        clearValidationError(budgetInput);
    }
    
    return isValid;
}

// Show validation error
function showValidationError(inputElement, errorMessage) {
    inputElement.classList.add('is-invalid');
    
    // Create or update error message
    let errorElement = inputElement.nextElementSibling;
    if (!errorElement || !errorElement.classList.contains('invalid-feedback')) {
        errorElement = document.createElement('div');
        errorElement.className = 'invalid-feedback';
        inputElement.parentNode.insertBefore(errorElement, inputElement.nextSibling);
    }
    
    errorElement.textContent = errorMessage;
}

// Clear validation error
function clearValidationError(inputElement) {
    inputElement.classList.remove('is-invalid');
    
    // Remove error message if it exists
    const errorElement = inputElement.nextElementSibling;
    if (errorElement && errorElement.classList.contains('invalid-feedback')) {
        errorElement.textContent = '';
    }
}

// Format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2
    }).format(amount);
}

// Format percentage
function formatPercentage(value) {
    return new Intl.NumberFormat('en-US', {
        style: 'percent',
        minimumFractionDigits: 1,
        maximumFractionDigits: 1
    }).format(value / 100);
}

// Format date
function formatDate(dateString) {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    }).format(date);
}
