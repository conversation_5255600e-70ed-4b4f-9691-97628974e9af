/**
 * Enterprise QR Code Generator API Client
 * 
 * This module provides a client-side interface for interacting with the
 * Enterprise QR Code Generator API.
 */

class EnterpriseQRApi {
    /**
     * Initialize the API client
     * @param {string} baseUrl - Base URL of the API
     */
    constructor(baseUrl = 'http://localhost:3000/api') {
        this.baseUrl = baseUrl;
        this.apiKey = localStorage.getItem('qr_api_key') || null;
        this.token = localStorage.getItem('qr_auth_token') || null;
    }
    
    /**
     * Set the API key
     * @param {string} apiKey - API key
     */
    setApiKey(apiKey) {
        this.apiKey = apiKey;
        localStorage.setItem('qr_api_key', apiKey);
    }
    
    /**
     * Set the authentication token
     * @param {string} token - JWT token
     */
    setToken(token) {
        this.token = token;
        localStorage.setItem('qr_auth_token', token);
    }
    
    /**
     * Clear authentication credentials
     */
    clearAuth() {
        this.apiKey = null;
        this.token = null;
        localStorage.removeItem('qr_api_key');
        localStorage.removeItem('qr_auth_token');
    }
    
    /**
     * Get headers for API requests
     * @param {boolean} useToken - Whether to use JWT token instead of API key
     * @returns {Object} - Headers object
     */
    getHeaders(useToken = false) {
        const headers = {
            'Content-Type': 'application/json'
        };
        
        if (useToken && this.token) {
            headers['Authorization'] = `Bearer ${this.token}`;
        } else if (this.apiKey) {
            headers['X-API-Key'] = this.apiKey;
        }
        
        return headers;
    }
    
    /**
     * Make an API request
     * @param {string} endpoint - API endpoint
     * @param {string} method - HTTP method
     * @param {Object} data - Request data
     * @param {boolean} useToken - Whether to use JWT token instead of API key
     * @returns {Promise} - Promise resolving to API response
     */
    async request(endpoint, method = 'GET', data = null, useToken = false) {
        const url = `${this.baseUrl}${endpoint}`;
        const options = {
            method,
            headers: this.getHeaders(useToken)
        };
        
        if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
            options.body = JSON.stringify(data);
        }
        
        try {
            const response = await fetch(url, options);
            const responseData = await response.json();
            
            if (!response.ok) {
                throw new Error(responseData.error || 'API request failed');
            }
            
            return responseData;
        } catch (error) {
            console.error('API request error:', error);
            throw error;
        }
    }
    
    /**
     * Register a new user
     * @param {Object} userData - User registration data
     * @returns {Promise} - Promise resolving to registration response
     */
    async register(userData) {
        const response = await this.request('/auth/register', 'POST', userData);
        
        if (response.data.apiKey) {
            this.setApiKey(response.data.apiKey);
        }
        
        if (response.data.token) {
            this.setToken(response.data.token);
        }
        
        return response;
    }
    
    /**
     * Login a user
     * @param {Object} credentials - User login credentials
     * @returns {Promise} - Promise resolving to login response
     */
    async login(credentials) {
        const response = await this.request('/auth/login', 'POST', credentials);
        
        if (response.data.apiKey) {
            this.setApiKey(response.data.apiKey);
        }
        
        if (response.data.token) {
            this.setToken(response.data.token);
        }
        
        return response;
    }
    
    /**
     * Logout the current user
     */
    logout() {
        this.clearAuth();
    }
    
    /**
     * Check if the user is authenticated
     * @returns {boolean} - Whether the user is authenticated
     */
    isAuthenticated() {
        return !!this.apiKey || !!this.token;
    }
    
    /**
     * Generate a QR code
     * @param {string} data - QR code data
     * @param {Object} options - QR code options
     * @returns {Promise} - Promise resolving to QR code data
     */
    async generateQRCode(data, options = {}) {
        return this.request('/qrcodes', 'POST', { data, options });
    }
    
    /**
     * Get all QR codes for the current user
     * @returns {Promise} - Promise resolving to QR codes data
     */
    async getQRCodes() {
        return this.request('/qrcodes');
    }
    
    /**
     * Get a specific QR code
     * @param {string} id - QR code ID
     * @returns {Promise} - Promise resolving to QR code data
     */
    async getQRCode(id) {
        return this.request(`/qrcodes/${id}`);
    }
    
    /**
     * Delete a QR code
     * @param {string} id - QR code ID
     * @returns {Promise} - Promise resolving to deletion response
     */
    async deleteQRCode(id) {
        return this.request(`/qrcodes/${id}`, 'DELETE');
    }
    
    /**
     * Check API health
     * @returns {Promise} - Promise resolving to health check response
     */
    async checkHealth() {
        return this.request('/health');
    }
}

// Create global instance
const enterpriseQRApi = new EnterpriseQRApi();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        EnterpriseQRApi,
        enterpriseQRApi
    };
}
