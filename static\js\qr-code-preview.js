/**
 * Enhanced Enterprise QR Code Generator with Real-Time Preview
 * Corporate-grade QR code generation with instant preview and advanced customization
 */

document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const qrForm = document.getElementById('qr-form');
    const qrPreview = document.getElementById('qr-preview');
    const qrTypeSelect = document.getElementById('id_qr_type');
    const qrDataInput = document.getElementById('id_data');
    const qrNameInput = document.getElementById('id_name');
    const fgColorInput = document.getElementById('id_foreground_color');
    const bgColorInput = document.getElementById('id_background_color');
    const logoInput = document.getElementById('id_logo');
    const encryptedInput = document.getElementById('id_is_encrypted');
    const previewTypeDisplay = document.getElementById('preview-type');
    const refreshPreviewBtn = document.getElementById('refresh-preview');
    const downloadBtn = document.getElementById('download-qr');

    // Type-specific form elements
    const urlInput = document.getElementById('url-input');
    const vcardNameInput = document.getElementById('vcard-name');
    const vcardCompanyInput = document.getElementById('vcard-company');
    const vcardPhoneInput = document.getElementById('vcard-phone');
    const vcardEmailInput = document.getElementById('vcard-email');
    const vcardAddressInput = document.getElementById('vcard-address');
    const vcardWebsiteInput = document.getElementById('vcard-website');
    const wifiSsidInput = document.getElementById('wifi-ssid');
    const wifiPasswordInput = document.getElementById('wifi-password');
    const wifiTypeInput = document.getElementById('wifi-type');

    // Customization elements
    const customizeBtn = document.getElementById('customize-btn');
    const customizationPanel = document.getElementById('customization-panel');
    const frameSelect = document.getElementById('frame-select');
    const cornerStyleSelect = document.getElementById('corner-style-select');
    const dotStyleSelect = document.getElementById('dot-style-select');
    const titleInput = document.getElementById('title-text');
    const guidingTextInput = document.getElementById('guiding-text');
    const guidingTextPosition = document.getElementById('guiding-text-position');

    // Initialize QR code library with premium default options
    let qrCode;

    // Wait for the library to be fully loaded
    setTimeout(() => {
        try {
            // Check if QRCodeStyling is available
            if (typeof QRCodeStyling === 'undefined') {
                console.error('QRCodeStyling library not loaded');
                if (qrPreview) {
                    qrPreview.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            QR code library not loaded. Please refresh the page.
                        </div>
                    `;
                }
                return;
            }

            qrCode = new QRCodeStyling({
                width: 300,
                height: 300,
                type: "svg",
                data: "https://example.com",
                image: "",
                dotsOptions: {
                    color: "#4a6cf7",
                    type: "rounded"
                },
                backgroundOptions: {
                    color: "#FFFFFF",
                },
                cornersSquareOptions: {
                    color: "#4a6cf7",
                    type: "extra-rounded"
                },
                cornersDotOptions: {
                    color: "#4a6cf7",
                    type: "dot"
                },
                imageOptions: {
                    crossOrigin: "anonymous",
                    margin: 10
                }
            });

            console.log("QR Code library initialized successfully");

            // Initial render with welcome message
            renderInitialQRCode();

            // Show animation on page load
            animatePreview();
        } catch (error) {
            console.error('Error initializing QR code:', error);
            if (qrPreview) {
                qrPreview.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        Error initializing QR code: ${error.message}. Please refresh the page.
                    </div>
                `;
            }
        }
    }, 500); // Wait 500ms to ensure library is loaded

    // Log initialization for debugging
    console.log("QR Code Preview initialized");

    // Debug: Check if all elements exist
    console.log("=== ELEMENT DEBUG ===");
    console.log("customizeBtn:", customizeBtn);
    console.log("customizationPanel:", customizationPanel);
    console.log("qrTypeSelect:", qrTypeSelect);
    console.log("qrDataInput:", qrDataInput);
    console.log("qrPreview:", qrPreview);
    console.log("====================");

    // Set initial preview type
    if (previewTypeDisplay && qrTypeSelect) {
        previewTypeDisplay.textContent = qrTypeSelect.options[qrTypeSelect.selectedIndex].text;
    }

    // Event listeners for form inputs with real-time updates
    // Main form fields
    if (qrNameInput) qrNameInput.addEventListener('input', debounce(updateQRCode, 100));
    if (qrDataInput) qrDataInput.addEventListener('input', debounce(updateQRCode, 100));
    if (fgColorInput) fgColorInput.addEventListener('input', debounce(updateQRCode, 100));
    if (bgColorInput) bgColorInput.addEventListener('input', debounce(updateQRCode, 100));

    // QR Type selection with immediate preview update
    if (qrTypeSelect) {
        // Log the initial QR type
        console.log("Initial QR type:", qrTypeSelect.value);

        // Function to handle QR type change
        function handleQRTypeChange() {
            console.log("QR type changed to:", qrTypeSelect.value);

            // Update data field and form elements
            updateDataField();

            // Update preview type display
            if (previewTypeDisplay) {
                previewTypeDisplay.textContent = qrTypeSelect.options[qrTypeSelect.selectedIndex].text;
            }

            // Generate a default QR code for the selected type
            const qrType = qrTypeSelect.value;
            let defaultData = getDefaultDataForType(qrType);
            console.log("Default data for type:", defaultData);

            // Always populate data field with default data for immediate preview
            if (qrDataInput) {
                qrDataInput.value = defaultData;
                console.log("Setting data field to:", defaultData);
            }

            // Update QR code preview immediately
            setTimeout(updateQRCode, 100);

            // Show animation
            animatePreview();
        }

        // If a type is already selected (editing mode), show a preview immediately
        if (qrTypeSelect.value) {
            console.log("Type already selected, showing preview");
            setTimeout(handleQRTypeChange, 600);
        }

        // Add change event listener
        qrTypeSelect.addEventListener('change', handleQRTypeChange);
    }

    // Type-specific form fields with real-time updates
    if (urlInput) urlInput.addEventListener('input', debounce(updateTypeSpecificData, 100));

    // vCard fields
    if (vcardNameInput) vcardNameInput.addEventListener('input', debounce(updateTypeSpecificData, 100));
    if (vcardCompanyInput) vcardCompanyInput.addEventListener('input', debounce(updateTypeSpecificData, 100));
    if (vcardPhoneInput) vcardPhoneInput.addEventListener('input', debounce(updateTypeSpecificData, 100));
    if (vcardEmailInput) vcardEmailInput.addEventListener('input', debounce(updateTypeSpecificData, 100));
    if (vcardAddressInput) vcardAddressInput.addEventListener('input', debounce(updateTypeSpecificData, 100));
    if (vcardWebsiteInput) vcardWebsiteInput.addEventListener('input', debounce(updateTypeSpecificData, 100));

    // WiFi fields
    if (wifiSsidInput) wifiSsidInput.addEventListener('input', debounce(updateTypeSpecificData, 100));
    if (wifiPasswordInput) wifiPasswordInput.addEventListener('input', debounce(updateTypeSpecificData, 100));
    if (wifiTypeInput) wifiTypeInput.addEventListener('change', debounce(updateTypeSpecificData, 100));

    // Other interactions
    if (logoInput) logoInput.addEventListener('change', handleLogoUpload);
    if (refreshPreviewBtn) refreshPreviewBtn.addEventListener('click', function() {
        animatePreview();
        updateQRCode();
    });

    // Customize button event listener
    if (customizeBtn) {
        console.log("Customize button found, adding event listener");
        customizeBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log("Customize button clicked");
            toggleCustomizationPanel();
        });
    } else {
        console.error("Customize button not found!");
    }

    // Global test function for debugging
    window.testCustomizePanel = function() {
        console.log("=== MANUAL TEST ===");
        console.log("customizeBtn exists:", !!customizeBtn);
        console.log("customizationPanel exists:", !!customizationPanel);
        if (customizationPanel) {
            console.log("Panel current classes:", customizationPanel.className);
            console.log("Panel current display:", window.getComputedStyle(customizationPanel).display);
        }
        if (customizeBtn) {
            console.log("Button text:", customizeBtn.innerHTML);
        }
        console.log("Calling toggleCustomizationPanel...");
        toggleCustomizationPanel();
        console.log("===================");
    };

    // Global test function for QR preview
    window.testQRPreview = function() {
        console.log("=== QR PREVIEW TEST ===");
        console.log("qrCode exists:", !!qrCode);
        console.log("qrPreview exists:", !!qrPreview);
        console.log("qrDataInput exists:", !!qrDataInput);
        if (qrDataInput) {
            console.log("Current data:", qrDataInput.value);
        }
        console.log("Calling updateQRCode...");
        updateQRCode();
        console.log("=======================");
    };

    // Customization panel fields with real-time updates
    if (frameSelect) frameSelect.addEventListener('change', debounce(updateQRCode, 100));
    if (cornerStyleSelect) cornerStyleSelect.addEventListener('change', debounce(updateQRCode, 100));
    if (dotStyleSelect) dotStyleSelect.addEventListener('change', debounce(updateQRCode, 100));
    if (titleInput) titleInput.addEventListener('input', debounce(updateQRCode, 100));
    if (guidingTextInput) guidingTextInput.addEventListener('input', debounce(updateQRCode, 100));
    if (guidingTextPosition) guidingTextPosition.addEventListener('change', debounce(updateQRCode, 100));

    // Function to animate the preview with elegant effect
    function animatePreview() {
        if (qrPreview) {
            qrPreview.classList.add('animate-preview');
            setTimeout(() => {
                qrPreview.classList.remove('animate-preview');
            }, 800);
        }
    }

    // Function to render initial QR code with welcome message
    function renderInitialQRCode() {
        // Check if qrCode is initialized
        if (!qrCode) {
            console.log("QR code not initialized yet, will render later");
            return;
        }

        // Check if a QR type is already selected (for editing mode)
        if (qrTypeSelect && qrTypeSelect.value) {
            // Generate a default QR code based on the selected type
            const qrType = qrTypeSelect.value;
            let defaultData = getDefaultDataForType(qrType);

            console.log("Rendering initial QR code with type:", qrType, "and data:", defaultData);

            qrCode.update({
                data: defaultData
            });
        } else {
            // Default welcome QR code
            console.log("Rendering welcome QR code");
            qrCode.update({
                data: "https://example.com/welcome"
            });
        }
        renderQRCode();
    }

    // Function to get default data for a QR type
    function getDefaultDataForType(qrType) {
        switch(qrType) {
            case 'URL':
                return "https://example.com";
            case 'TEXT':
                return "Sample Text";
            case 'VCARD':
                return "BEGIN:VCARD\nVERSION:3.0\nFN:John Doe\nEND:VCARD";
            case 'WIFI':
                return "WIFI:S:NetworkName;T:WPA;P:Password;;";
            case 'EMAIL':
                return "mailto:<EMAIL>";
            case 'PHONE':
                return "tel:+1234567890";
            case 'LOCATION':
                return "geo:37.786971,-122.399677";
            case 'PDF':
                return "Sample PDF Document";
            case 'IMAGE':
                return "Sample Image";
            case 'DOCUMENT':
                return "Sample Document";
            case 'AUDIO':
                return "Sample Audio";
            case 'VIDEO':
                return "Sample Video";
            case 'CALENDAR':
                return "BEGIN:VCALENDAR\nVERSION:2.0\nBEGIN:VEVENT\nSUMMARY:Event\nEND:VEVENT\nEND:VCALENDAR";
            case 'PRODUCT':
                return "Sample Product";
            case 'APP':
                return "https://play.google.com/store/apps/details?id=com.example.app";
            default:
                return "Sample QR Code";
        }
    }

    // File upload handling
    const uploadedFileField = document.getElementById('id_uploaded_file');
    const filePreviewContainer = document.getElementById('file-upload-preview');

    if (uploadedFileField) {
        uploadedFileField.addEventListener('change', function(e) {
            if (e.target.files && e.target.files.length > 0) {
                const file = e.target.files[0];

                // Show file preview
                if (filePreviewContainer) {
                    // Get file type icon
                    let iconClass = 'fas fa-file';
                    let bgColor = '#6c757d';

                    if (file.type.startsWith('image/')) {
                        iconClass = 'fas fa-file-image';
                        bgColor = 'linear-gradient(135deg, #4CAF50, #8BC34A)';
                    } else if (file.type === 'application/pdf') {
                        iconClass = 'fas fa-file-pdf';
                        bgColor = 'linear-gradient(135deg, #FF5722, #F44336)';
                    } else if (file.type.startsWith('audio/')) {
                        iconClass = 'fas fa-file-audio';
                        bgColor = 'linear-gradient(135deg, #9C27B0, #673AB7)';
                    } else if (file.type.startsWith('video/')) {
                        iconClass = 'fas fa-file-video';
                        bgColor = 'linear-gradient(135deg, #2196F3, #03A9F4)';
                    } else if (file.type.includes('word') || file.type.includes('document')) {
                        iconClass = 'fas fa-file-word';
                        bgColor = 'linear-gradient(135deg, #3F51B5, #2196F3)';
                    }

                    // Format file size
                    const fileSize = formatFileSize(file.size);

                    // Create preview HTML
                    filePreviewContainer.innerHTML = `
                        <div class="file-preview-item">
                            <div class="file-preview-icon" style="background: ${bgColor}">
                                <i class="${iconClass}"></i>
                            </div>
                            <div class="file-preview-info">
                                <div class="file-preview-name">${file.name}</div>
                                <div class="file-preview-meta">${file.type} • ${fileSize}</div>
                            </div>
                        </div>
                    `;

                    filePreviewContainer.classList.add('show');

                    // If data field is empty, use file name as QR code data
                    if (qrDataInput && !qrDataInput.value) {
                        qrDataInput.value = file.name;
                        updateQRCode();
                    }
                }
            } else {
                // Clear preview if no file selected
                if (filePreviewContainer) {
                    filePreviewContainer.innerHTML = '';
                    filePreviewContainer.classList.remove('show');
                }
            }
        });
    }

    // Download button
    const downloadBtn = document.getElementById('download-qr');
    if (downloadBtn) {
        downloadBtn.addEventListener('click', function() {
            if (qrDataInput && qrDataInput.value) {
                downloadBtn.disabled = false;
                qrCode.download({
                    extension: 'png',
                    name: 'qrcode-' + (qrNameInput ? qrNameInput.value : 'download')
                });
            }
        });
    }

    // Helper function to format file size
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Customization event listeners
    if (frameSelect) frameSelect.addEventListener('change', updateQRCode);
    if (cornerStyleSelect) cornerStyleSelect.addEventListener('change', updateQRCode);
    if (dotStyleSelect) dotStyleSelect.addEventListener('change', updateQRCode);
    if (titleInput) titleInput.addEventListener('input', debounce(updateQRCode, 300));
    if (guidingTextInput) guidingTextInput.addEventListener('input', debounce(updateQRCode, 300));
    if (guidingTextPosition) guidingTextPosition.addEventListener('change', updateQRCode);

    // Function to toggle customization panel
    function toggleCustomizationPanel() {
        console.log("toggleCustomizationPanel called");

        if (customizationPanel) {
            console.log("Customization panel found, current classes:", customizationPanel.className);

            // Toggle the show class
            customizationPanel.classList.toggle('show');

            console.log("After toggle, classes:", customizationPanel.className);

            // Update button text based on panel visibility
            if (customizeBtn) {
                if (customizationPanel.classList.contains('show')) {
                    customizeBtn.innerHTML = '<i class="fas fa-times me-2"></i>Hide Customization';
                    console.log("Customization panel shown");
                } else {
                    customizeBtn.innerHTML = '<i class="fas fa-palette me-2"></i>Customize QR Code';
                    console.log("Customization panel hidden");
                }
            }

            // Trigger window resize to ensure QR code is properly centered
            setTimeout(() => {
                window.dispatchEvent(new Event('resize'));
                // Update QR code to reflect any customization changes
                updateQRCode();
            }, 300);
        } else {
            console.error("Customization panel not found!");
        }
    }

    // Function to update data field based on QR type
    function updateDataField() {
        if (!qrTypeSelect || !qrDataInput) return;

        const qrType = qrTypeSelect.value;
        let placeholder = "";
        let helpText = "";

        // Get file upload field
        const uploadedFileField = document.getElementById('id_uploaded_file');
        const uploadedFileContainer = document.getElementById('file-upload-container');
        const fileUploadHelp = document.getElementById('file-upload-help');

        // Define file types that need upload
        const fileTypes = ['PDF', 'IMAGE', 'DOCUMENT', 'AUDIO', 'VIDEO'];
        const isFileType = fileTypes.includes(qrType);

        // Show/hide file upload field
        if (uploadedFileContainer) {
            if (isFileType) {
                uploadedFileContainer.style.display = 'block';
                uploadedFileContainer.classList.add('highlight-field');

                // Update help text based on file type
                if (fileUploadHelp) {
                    switch(qrType) {
                        case 'PDF':
                            fileUploadHelp.textContent = 'Upload a PDF file from your computer to generate a QR code.';
                            break;
                        case 'IMAGE':
                            fileUploadHelp.textContent = 'Upload an image file from your computer to generate a QR code.';
                            break;
                        case 'DOCUMENT':
                            fileUploadHelp.textContent = 'Upload a document file from your computer to generate a QR code.';
                            break;
                        case 'AUDIO':
                            fileUploadHelp.textContent = 'Upload an audio file from your computer to generate a QR code.';
                            break;
                        case 'VIDEO':
                            fileUploadHelp.textContent = 'Upload a video file from your computer to generate a QR code.';
                            break;
                        default:
                            fileUploadHelp.textContent = 'Upload a file from your computer to generate a QR code.';
                    }
                }

                // Add prominent styling
                uploadedFileContainer.classList.add('active-upload');

                setTimeout(() => {
                    uploadedFileContainer.classList.remove('highlight-field');
                }, 2000);
            } else {
                uploadedFileContainer.style.display = 'none';
                uploadedFileContainer.classList.remove('active-upload');
            }
        }

        switch(qrType) {
            case 'URL':
                placeholder = "https://example.com";
                helpText = "Enter a valid URL starting with http:// or https://";
                break;
            case 'TEXT':
                placeholder = "Enter your text here";
                helpText = "Enter any text you want to encode";
                break;
            case 'VCARD':
                placeholder = "BEGIN:VCARD\nVERSION:3.0\nN:Doe;John;;;\nFN:John Doe\nORG:Example Corp.\nTITLE:Software Engineer\nTEL;TYPE=WORK,VOICE:(*************\nEMAIL:<EMAIL>\nEND:VCARD";
                helpText = "Enter vCard format data or use the form below";
                break;
            case 'WIFI':
                placeholder = "WIFI:S:NetworkName;T:WPA;P:Password;;";
                helpText = "Enter WiFi network details or use the form below";
                break;
            case 'EMAIL':
                placeholder = "mailto:<EMAIL>?subject=Subject&body=Body";
                helpText = "Enter email address with optional subject and body";
                break;
            case 'PHONE':
                placeholder = "tel:+1234567890";
                helpText = "Enter phone number with country code";
                break;
            case 'LOCATION':
                placeholder = "geo:37.786971,-122.399677";
                helpText = "Enter geographic coordinates (latitude,longitude)";
                break;
            case 'PDF':
                placeholder = "https://example.com/document.pdf";
                helpText = "Upload a PDF file or enter a URL to a PDF document";
                break;
            case 'IMAGE':
                placeholder = "https://example.com/image.jpg";
                helpText = "Upload an image file or enter a URL to an image";
                break;
            case 'DOCUMENT':
                placeholder = "https://example.com/document.docx";
                helpText = "Upload a document file or enter a URL to a document";
                break;
            case 'AUDIO':
                placeholder = "https://example.com/audio.mp3";
                helpText = "Upload an audio file or enter a URL to an audio file";
                break;
            case 'VIDEO':
                placeholder = "https://example.com/video.mp4";
                helpText = "Upload a video file or enter a URL to a video file";
                break;
            case 'CALENDAR':
                placeholder = "BEGIN:VCALENDAR\nVERSION:2.0\nBEGIN:VEVENT\nSUMMARY:Event Title\nDTSTART:20230101T120000Z\nDTEND:20230101T130000Z\nLOCATION:Event Location\nDESCRIPTION:Event Description\nEND:VEVENT\nEND:VCALENDAR";
                helpText = "Enter calendar event details in iCalendar format";
                break;
            case 'PRODUCT':
                placeholder = "Enter product information or URL";
                helpText = "Enter product details or a URL to a product page";
                break;
            case 'APP':
                placeholder = "https://play.google.com/store/apps/details?id=com.example.app";
                helpText = "Enter app store URL (Google Play, App Store, etc.)";
                break;
            default:
                placeholder = "Enter data for QR code";
                helpText = "Enter the data to encode in the QR code";
        }

        qrDataInput.placeholder = placeholder;
        const helpElement = document.getElementById('data-help');
        if (helpElement) {
            helpElement.textContent = helpText;
        }

        // Update data field label based on type
        const dataLabel = document.querySelector('label[for="id_data"]');
        if (dataLabel) {
            if (isFileType) {
                dataLabel.textContent = 'URL or Description (Optional)';
            } else {
                dataLabel.textContent = 'Data';
            }
        }

        // Show/hide type-specific form sections
        toggleTypeSpecificForms(qrType);

        // Update QR code preview
        updateQRCode();
    }

    // Function to toggle type-specific form sections
    function toggleTypeSpecificForms(qrType) {
        const formSections = document.querySelectorAll('.qr-type-specific');
        formSections.forEach(section => {
            section.style.display = 'none';
        });

        const activeSection = document.getElementById(`${qrType.toLowerCase()}-form`);
        if (activeSection) {
            activeSection.style.display = 'block';
        }
    }

    // Function to handle logo upload
    function handleLogoUpload() {
        if (!logoInput || !logoInput.files || logoInput.files.length === 0) return;

        const file = logoInput.files[0];
        const reader = new FileReader();

        reader.onload = function(e) {
            qrCode.update({
                image: e.target.result
            });
            renderQRCode();
        };

        reader.readAsDataURL(file);
    }

    // Function to update type-specific data in real-time
    function updateTypeSpecificData() {
        const qrType = qrTypeSelect ? qrTypeSelect.value : 'TEXT';
        let data = '';

        switch(qrType) {
            case 'URL':
                if (urlInput && urlInput.value) {
                    data = urlInput.value;
                }
                break;

            case 'VCARD':
                if (vcardNameInput || vcardCompanyInput || vcardPhoneInput || vcardEmailInput) {
                    // Generate vCard format
                    data = 'BEGIN:VCARD\nVERSION:3.0\n';

                    if (vcardNameInput && vcardNameInput.value) {
                        data += `FN:${vcardNameInput.value}\n`;
                        data += `N:${vcardNameInput.value};;;\n`;
                    }

                    if (vcardCompanyInput && vcardCompanyInput.value) {
                        data += `ORG:${vcardCompanyInput.value}\n`;
                    }

                    if (vcardPhoneInput && vcardPhoneInput.value) {
                        data += `TEL:${vcardPhoneInput.value}\n`;
                    }

                    if (vcardEmailInput && vcardEmailInput.value) {
                        data += `EMAIL:${vcardEmailInput.value}\n`;
                    }

                    if (vcardAddressInput && vcardAddressInput.value) {
                        data += `ADR:;;${vcardAddressInput.value};;;\n`;
                    }

                    if (vcardWebsiteInput && vcardWebsiteInput.value) {
                        data += `URL:${vcardWebsiteInput.value}\n`;
                    }

                    data += 'END:VCARD';
                }
                break;

            case 'WIFI':
                if (wifiSsidInput && wifiSsidInput.value) {
                    const ssid = wifiSsidInput.value;
                    const password = wifiPasswordInput ? wifiPasswordInput.value : '';
                    const encryptionType = wifiTypeInput ? wifiTypeInput.value : 'WPA';

                    data = `WIFI:S:${ssid};T:${encryptionType};P:${password};;`;
                }
                break;
        }

        // Update the main data input field
        if (data && qrDataInput) {
            qrDataInput.value = data;
            // Trigger QR code update
            updateQRCode();
        }
    }

    // Function to update QR code options with real-time preview
    function updateQRCode() {
        console.log("updateQRCode called");

        // Check if qrCode is initialized
        if (!qrCode) {
            console.error("QR code not initialized yet, will try again in 500ms");
            setTimeout(updateQRCode, 500);
            return;
        }

        // Show elegant loading animation
        animatePreview();

        // Update preview type display
        if (previewTypeDisplay && qrTypeSelect) {
            previewTypeDisplay.textContent = qrTypeSelect.options[qrTypeSelect.selectedIndex].text;
        }

        // Log current data for debugging
        if (qrDataInput) {
            console.log("Current data:", qrDataInput.value);
        }

        try {
            // Check if we have data to generate QR code
            if (!qrDataInput || !qrDataInput.value) {
                // If QR type is selected, use default data for that type
                if (qrTypeSelect && qrTypeSelect.value) {
                    const qrType = qrTypeSelect.value;
                    const defaultData = getDefaultDataForType(qrType);

                    console.log("Using default data for type:", qrType, defaultData);

                    // Update QR code with default data
                    qrCode.update({
                        data: defaultData,
                        dotsOptions: {
                            color: fgColorInput ? fgColorInput.value : '#4a6cf7',
                            type: dotStyleSelect ? dotStyleSelect.value : 'rounded'
                        },
                        backgroundOptions: {
                            color: bgColorInput ? bgColorInput.value : '#FFFFFF'
                        }
                    });

                    renderQRCode();

                    // Add a "Sample" watermark to indicate it's a default QR code
                    const svgElement = qrPreview.querySelector('svg');
                    if (svgElement) {
                        // Remove any existing watermark first
                        const existingWatermark = qrPreview.querySelector('.qr-watermark');
                        if (existingWatermark) {
                            existingWatermark.remove();
                        }

                        const watermark = document.createElement('div');
                        watermark.className = 'qr-watermark';
                        watermark.innerHTML = '<span>Sample</span>';
                        qrPreview.appendChild(watermark);
                    }

                    if (downloadBtn) downloadBtn.disabled = true;
                    return;
                } else {
                    // Show placeholder with animation if no type selected
                    qrPreview.innerHTML = `
                        <div class="text-center">
                            <div class="mb-3">
                                <i class="fas fa-keyboard fa-3x text-primary opacity-50"></i>
                            </div>
                            <p class="text-muted">Start typing to see your QR code appear instantly</p>
                        </div>
                    `;

                    if (downloadBtn) downloadBtn.disabled = true;
                    return;
                }
            }

            // Get values from form with elegant defaults
            const data = qrDataInput.value;
            const fgColor = fgColorInput ? fgColorInput.value : '#4a6cf7';
            const bgColor = bgColorInput ? bgColorInput.value : '#FFFFFF';
            const name = qrNameInput ? qrNameInput.value : 'Enterprise QR Code';

            // Get customization values with premium defaults
            const frame = frameSelect ? frameSelect.value : 'none';
            const dotStyle = dotStyleSelect ? dotStyleSelect.value : 'rounded';
            const cornerStyle = cornerStyleSelect ? cornerStyleSelect.value : 'extra-rounded';
            const title = titleInput ? titleInput.value : name;
            const guidingText = guidingTextInput ? guidingTextInput.value : 'Scan me';
            const textPosition = guidingTextPosition ? guidingTextPosition.value : 'below';

            console.log("Updating QR code with data:", data.substring(0, 30) + (data.length > 30 ? '...' : ''));
            console.log("Customization options:", {
                fgColor, bgColor, dotStyle, cornerStyle, frame, title: title.substring(0, 20)
            });

            // Update QR code options with premium styling
            const options = {
                data: data,
                dotsOptions: {
                    color: fgColor,
                    type: dotStyle
                },
                backgroundOptions: {
                    color: bgColor
                },
                cornersSquareOptions: {
                    color: fgColor,
                    type: cornerStyle
                },
                cornersDotOptions: {
                    color: fgColor,
                    type: cornerStyle === 'dot' ? 'dot' : (dotStyle === 'dots' ? 'dot' : dotStyle)
                }
            };

            // Apply the update with elegant animation
            qrCode.update(options);
            renderQRCode();

            // Remove any existing watermark
            const existingWatermark = qrPreview.querySelector('.qr-watermark');
            if (existingWatermark) {
                existingWatermark.remove();
            }

            // Add frame if selected
            if (frame !== 'none') {
                addFrame(frame, title, guidingText, textPosition);
            }

            // Enable download button with animation
            if (downloadBtn) {
                downloadBtn.disabled = false;
                downloadBtn.classList.add('btn-pulse');
                setTimeout(() => {
                    downloadBtn.classList.remove('btn-pulse');
                }, 1000);
            }

            // Add a subtle animation to the preview for visual feedback
            const svgElement = qrPreview.querySelector('svg');
            if (svgElement) {
                svgElement.style.transform = 'scale(1.05)';
                svgElement.style.transition = 'transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
                setTimeout(() => {
                    svgElement.style.transform = 'scale(1)';
                }, 300);
            }
        } catch (error) {
            console.error("Error updating QR code:", error);
            qrPreview.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    Error updating QR code: ${error.message}. Please try again.
                </div>
            `;

            if (downloadBtn) downloadBtn.disabled = true;
        }
    }

    // Function to render QR code
    function renderQRCode() {
        // Check if qrCode is initialized and qrPreview exists
        if (!qrCode || !qrPreview) {
            console.error("Cannot render QR code - qrCode or qrPreview not available");
            return;
        }

        try {
            qrPreview.innerHTML = '';
            qrCode.append(qrPreview);
            console.log("QR code rendered successfully");
        } catch (error) {
            console.error("Error rendering QR code:", error);
            qrPreview.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    Error rendering QR code. Please try again.
                </div>
            `;
        }
    }

    // Function to add frame to QR code
    function addFrame(frameType, title, guidingText, textPosition) {
        try {
            const qrElement = qrPreview.querySelector('svg');
            if (!qrElement) {
                console.error("No SVG element found in QR preview");
                return;
            }

            console.log(`Adding frame: type=${frameType}, title=${title}, text=${guidingText}, position=${textPosition}`);

            // Create wrapper for framed QR code
            const wrapper = document.createElement('div');
            wrapper.className = `qr-frame qr-frame-${frameType}`;

            // Add title if provided
            if (title) {
                const titleElement = document.createElement('div');
                titleElement.className = 'qr-title';
                titleElement.textContent = title;
                wrapper.appendChild(titleElement);
            }

            // Add guiding text above if position is 'above'
            if (guidingText && textPosition === 'above') {
                const textElement = document.createElement('div');
                textElement.className = 'qr-guiding-text';
                textElement.textContent = guidingText;
                wrapper.appendChild(textElement);
            }

            // Add the QR code
            const qrContainer = document.createElement('div');
            qrContainer.className = 'qr-container';

            // Clone the SVG element to avoid DOM manipulation issues
            const qrClone = qrElement.cloneNode(true);
            qrContainer.appendChild(qrClone);
            wrapper.appendChild(qrContainer);

            // Add guiding text below if position is 'below'
            if (guidingText && textPosition === 'below') {
                const textElement = document.createElement('div');
                textElement.className = 'qr-guiding-text';
                textElement.textContent = guidingText;
                wrapper.appendChild(textElement);
            }

            // Replace the QR code with the framed version
            qrPreview.innerHTML = '';
            qrPreview.appendChild(wrapper);

            console.log("Frame added successfully");
        } catch (error) {
            console.error("Error adding frame:", error);
        }
    }

    // Utility function for debouncing
    function debounce(func, wait) {
        let timeout;
        return function() {
            const context = this;
            const args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                func.apply(context, args);
            }, wait);
        };
    }

    // Initialize
    updateDataField();

    // If we're editing an existing QR code, trigger the preview immediately
    if (qrDataInput && qrDataInput.value) {
        // Trigger the preview if there's already data in the field (editing mode)
        setTimeout(updateQRCode, 500);
    }

    // Handle form submission
    const qrForm = document.getElementById('qr-form');
    const generateBtn = document.getElementById('generate-qr-btn');

    if (qrForm && generateBtn) {
        console.log("Form and generate button found");

        // Add submit event listener to the form
        qrForm.addEventListener('submit', function(e) {
            console.log("Form submitted");
            console.log("Form action:", qrForm.action);
            console.log("Form method:", qrForm.method);

            // Make sure the form has the required fields
            const nameField = document.getElementById('id_name');
            const typeField = document.getElementById('id_qr_type');
            const dataField = document.getElementById('id_data');

            console.log("Name field value:", nameField ? nameField.value : 'Not found');
            console.log("Type field value:", typeField ? typeField.value : 'Not found');
            console.log("Data field value:", dataField ? dataField.value : 'Not found');

            // Don't prevent default - let the form submit normally
            // The view will handle the redirect to qr_code_detail.html
        });

        // Add a click event listener to the generate button for logging purposes
        generateBtn.addEventListener('click', function() {
            console.log("Generate QR Code button clicked");

            // Make sure the form has the required fields before submitting
            const nameField = document.getElementById('id_name');
            const typeField = document.getElementById('id_qr_type');
            const dataField = document.getElementById('id_data');

            if (nameField && nameField.value && typeField && typeField.value && dataField && dataField.value) {
                console.log("All required fields are filled, submitting form");
                // Let the form submit normally
            } else {
                console.log("Missing required fields");
                // Show an error message
                alert("Please fill in all required fields: Name, QR Code Type, and Data");
                return false;
            }
        });
    }
});
