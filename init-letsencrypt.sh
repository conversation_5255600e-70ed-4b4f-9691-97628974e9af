#!/bin/bash

# This script will initialize Let's Encrypt certificates for your domain
# Usage: ./init-letsencrypt.sh yourdomain.com [<EMAIL>]

if [ -z "$1" ]; then
  echo "Error: No domain specified."
  echo "Usage: $0 yourdomain.com [<EMAIL>]"
  exit 1
fi

domain=$1
email=${2:-"admin@$domain"}
staging=0 # Set to 1 if you're testing your setup to avoid hitting request limits

# Create required directories
mkdir -p ./nginx
mkdir -p ./data/certbot/conf/live/$domain
mkdir -p ./data/certbot/www

# Create dummy certificate
if [ ! -e "./data/certbot/conf/live/$domain/fullchain.pem" ]; then
  echo "Creating dummy certificate for $domain..."
  openssl req -x509 -nodes -newkey rsa:4096 -days 1 \
    -keyout ./data/certbot/conf/live/$domain/privkey.pem \
    -out ./data/certbot/conf/live/$domain/fullchain.pem \
    -subj "/CN=localhost"
fi

# Update domain in nginx.conf
echo "Updating domain in nginx.conf..."
sed -i "s/example.com/$domain/g" ./nginx/nginx.conf

# Start nginx
echo "Starting nginx..."
docker-compose up --force-recreate -d nginx

# Wait for nginx to start
echo "Waiting for nginx to start..."
sleep 5

# Request Let's Encrypt certificate
echo "Requesting Let's Encrypt certificate for $domain..."
if [ $staging -eq 1 ]; then
  staging_arg="--staging"
else
  staging_arg=""
fi

docker-compose run --rm certbot certonly --webroot -w /var/www/certbot \
  $staging_arg \
  --email $email \
  -d $domain \
  --rsa-key-size 4096 \
  --agree-tos \
  --force-renewal \
  --non-interactive

# Reload nginx
echo "Reloading nginx..."
docker-compose exec nginx nginx -s reload

echo "Done! Let's Encrypt certificate has been obtained for $domain."
echo "You can now start all services with: docker-compose up -d"
