/**
 * Ensure Smart Engine Options
 * This script ensures that the smartEngineOptions element exists
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Ensure Smart Engine Options loaded');
    
    // Check if the smartEngineOptions element exists
    let smartEngineOptions = document.getElementById('smartEngineOptions');
    
    // If it doesn't exist, create it
    if (!smartEngineOptions) {
        console.log('Creating smartEngineOptions element');
        
        // Create the container
        smartEngineOptions = document.createElement('div');
        smartEngineOptions.id = 'smartEngineOptions';
        smartEngineOptions.className = 'mt-3';
        
        // Create the options HTML
        smartEngineOptions.innerHTML = `
            <div class="card mb-3">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Smart Engine Options</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="aiLanguage" class="form-label">Language</label>
                            <select class="form-select" id="aiLanguage">
                                <option value="english" selected>English</option>
                                <option value="swahili">Swahili</option>
                                <option value="french">French</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="aiStyle" class="form-label">Style</label>
                            <select class="form-select" id="aiStyle">
                                <option value="standard" selected>Standard</option>
                                <option value="professional">Professional</option>
                                <option value="casual">Casual</option>
                                <option value="persuasive">Persuasive</option>
                            </select>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="aiLength" class="form-label">Length</label>
                            <select class="form-select" id="aiLength">
                                <option value="short">Short</option>
                                <option value="medium" selected>Medium</option>
                                <option value="long">Long</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="aiCreativity" class="form-label">Creativity: <span id="creativityValue">0.7</span></label>
                            <input type="range" class="form-range" id="aiCreativity" min="0.1" max="1" step="0.1" value="0.7">
                        </div>
                    </div>
                    <div class="d-grid">
                        <button type="button" class="btn btn-primary" id="generateSuggestions">
                            <i class="fas fa-magic me-2"></i> Generate Suggestions
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        // Find the useSmartEngine element
        const useSmartEngine = document.getElementById('useSmartEngine');
        
        if (useSmartEngine) {
            // Find the parent form group
            const formGroup = useSmartEngine.closest('.form-check');
            
            if (formGroup) {
                // Insert the container after the form group
                formGroup.parentNode.insertBefore(smartEngineOptions, formGroup.nextSibling);
            } else {
                // If all else fails, add it to the body
                document.body.appendChild(smartEngineOptions);
            }
        } else {
            // If useSmartEngine doesn't exist, find the ad title field
            const adTitle = document.getElementById('adTitle');
            
            if (adTitle) {
                // Find the parent form group
                const formGroup = adTitle.closest('.mb-4');
                
                if (formGroup) {
                    // Insert the container after the form group
                    formGroup.parentNode.insertBefore(smartEngineOptions, formGroup.nextSibling);
                } else {
                    // If all else fails, add it to the body
                    document.body.appendChild(smartEngineOptions);
                }
            } else {
                // If all else fails, add it to the body
                document.body.appendChild(smartEngineOptions);
            }
        }
        
        // Add event listener to the creativity slider
        const aiCreativity = document.getElementById('aiCreativity');
        const creativityValue = document.getElementById('creativityValue');
        
        if (aiCreativity && creativityValue) {
            aiCreativity.addEventListener('input', function() {
                creativityValue.textContent = this.value;
            });
        }
    }
    
    // Hide the options initially if Smart Engine is not checked
    const useSmartEngine = document.getElementById('useSmartEngine');
    
    if (useSmartEngine && !useSmartEngine.checked) {
        smartEngineOptions.style.display = 'none';
    }
    
    console.log('smartEngineOptions element:', smartEngineOptions);
});
