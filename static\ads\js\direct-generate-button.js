/**
 * Direct Generate Button
 * This script provides a direct, simple handler for the Generate Suggestions button
 * that overrides all other handlers to ensure it works.
 */

// Execute immediately to ensure it runs before DOMContentLoaded
(function() {
    console.log('Direct Generate Button script loaded - immediate execution');

    // Function to initialize the button
    function initGenerateButton() {
        console.log('Initializing Generate Button');

        // Get the button
        const generateBtn = document.getElementById('generateSuggestions');
        if (!generateBtn) {
            console.error('Generate Suggestions button not found!');
            return;
        }

        console.log('Generate Suggestions button found, setting up direct handler');

        // Instead of replacing the button, just add our handler directly
        // First, try to remove any existing click handlers by setting onclick to null
        generateBtn.onclick = null;

        // Then add our direct handler
        generateBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            directGenerateHandler();
            return false;
        }, true);

        // Also set the onclick attribute directly as a backup
        generateBtn.setAttribute('onclick', 'if(window.directGenerateHandler) { window.directGenerateHandler(); } return false;');

        // Make the handler function globally available
        window.directGenerateHandler = directGenerateHandler;

        console.log('Direct handler attached to Generate Suggestions button');
    }

    // Direct handler function for generating suggestions
    function directGenerateHandler() {
        console.log('Direct Generate Handler called');

        // Get required elements
        const adTitleInput = document.getElementById('adTitle');
        const adContentTextarea = document.getElementById('adContent');
        const aiLanguageSelect = document.getElementById('aiLanguage');
        const aiCreativityInput = document.getElementById('aiCreativity');
        const aiLengthSelect = document.getElementById('aiLength');
        const aiStyleSelect = document.getElementById('aiStyle');
        const aiSuggestionsContainer = document.getElementById('aiSuggestionsContainer');
        const formErrorContainer = document.getElementById('formErrorContainer');
        const useSmartEngineCheckbox = document.getElementById('useSmartEngine');

        // Check if title is provided
        if (!adTitleInput || !adTitleInput.value.trim()) {
            showError('Please enter an ad title before generating suggestions.');
            if (adTitleInput) adTitleInput.focus();
            return;
        }

        // Get the button again (in case it was replaced)
        const generateBtn = document.getElementById('generateSuggestions');

        // Show loading state
        if (generateBtn) {
            generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Generating...';
            generateBtn.disabled = true;
        }

        // Get parameters
        const title = adTitleInput.value.trim();
        const language = aiLanguageSelect ? aiLanguageSelect.value : 'english';

        // Get creativity value from the hidden input or radio buttons
        let creativity = 0.7; // Default value
        if (aiCreativityInput) {
            creativity = aiCreativityInput.value;
        } else {
            const conservativeRadio = document.getElementById('aiCreativityConservative');
            const balancedRadio = document.getElementById('aiCreativityBalanced');
            const creativeRadio = document.getElementById('aiCreativityCreative');

            if (conservativeRadio && conservativeRadio.checked) {
                creativity = conservativeRadio.value;
            } else if (balancedRadio && balancedRadio.checked) {
                creativity = balancedRadio.value;
            } else if (creativeRadio && creativeRadio.checked) {
                creativity = creativeRadio.value;
            }
        }

        const length = aiLengthSelect ? aiLengthSelect.value : 'medium';
        const style = aiStyleSelect ? aiStyleSelect.value : 'standard';
        const targetAudience = document.getElementById('targetAudience') ? document.getElementById('targetAudience').value : '';

        console.log('Generating suggestions with parameters:', {
            title, language, creativity, length, style, targetAudience
        });

        // Call the API with fallback suggestions
        console.log('Calling API with payload:', {
            title, language, creativity, length, style, targetAudience
        });

        // Use fallback suggestions immediately
        const fallbackSuggestions = [
            {
                title: title || "Special Offer",
                content: "Limited time offer! Don't miss out on our amazing products and services. Visit us today and discover the difference quality makes.",
                model: "offline",
                ai_generated: false,
                fallback: true
            },
            {
                title: "Exclusive Deal",
                content: "Experience excellence with our premium services. Designed for those who demand the best. Contact us today!",
                model: "offline",
                ai_generated: false,
                fallback: true
            },
            {
                title: "Quality You Can Trust",
                content: "Join thousands of satisfied customers who have chosen our services. Reliable, professional, and affordable.",
                model: "offline",
                ai_generated: false,
                fallback: true
            }
        ];

        // Display fallback suggestions immediately
        displaySuggestions(fallbackSuggestions);

        // Mark as used AI
        const usedAiInput = document.getElementById('usedAiInput');
        if (usedAiInput) {
            usedAiInput.value = 'true';
            console.log('Set usedAi to true');
        }

        // Show success message
        showSuccess('Suggestions generated successfully!');

        // Reset button state
        if (generateBtn) {
            generateBtn.innerHTML = '<i class="fas fa-magic me-2"></i> Generate Suggestions';
            generateBtn.disabled = false;
        }

        // Also try the API call for real suggestions
        console.log('Trying primary API endpoint: /ads/api/generate-suggestions/');
        fetch('/ads/api/generate-suggestions/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCSRFToken()
            },
            body: JSON.stringify({
                language: language,
                business_type: title,
                target_audience: targetAudience,
                tone: style,
                title: title,
                creativity: creativity,
                length: length,
                style: style
            })
        })
        .then(response => {
            console.log('Primary API response status:', response.status);

            if (!response.ok) {
                throw new Error(`Network response was not ok: ${response.status} ${response.statusText}`);
            }

            return response.json();
        })
        .then(data => {
            console.log('Primary API response data:', data);

            // If we got real suggestions, update the display
            if (data.success && data.suggestions && data.suggestions.length > 0) {
                console.log('Received real AI suggestions, updating display');
                displaySuggestions(data.suggestions);
                showSuccess('AI suggestions updated with real data!');
            }
        })
        .catch(error => {
            console.error('Error in primary API call:', error);

            // Try the alternative endpoint
            console.log('Trying alternative API endpoint: /ads/api/generate-suggestions-alt/');
            fetch('/ads/api/generate-suggestions-alt/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCSRFToken()
                },
                body: JSON.stringify({
                    language: language,
                    business_type: title,
                    target_audience: targetAudience,
                    tone: style,
                    title: title,
                    creativity: creativity,
                    length: length,
                    style: style
                })
            })
            .then(response => {
                console.log('Alternative API response status:', response.status);

                if (!response.ok) {
                    throw new Error(`Alternative API response was not ok: ${response.status} ${response.statusText}`);
                }

                return response.json();
            })
            .then(data => {
                console.log('Alternative API response data:', data);

                // If we got real suggestions, update the display
                if (data.success && data.suggestions && data.suggestions.length > 0) {
                    console.log('Received real AI suggestions from alternative API, updating display');
                    displaySuggestions(data.suggestions);
                    showSuccess('AI suggestions updated with real data!');
                }
            })
            .catch(alternativeError => {
                console.error('Error in alternative API call:', alternativeError);
                // We already displayed fallback suggestions, so no need to show an error
            });
        });
    }

    // Helper function to display suggestions
    function displaySuggestions(suggestions) {
        const aiSuggestionsContainer = document.getElementById('aiSuggestionsContainer');
        if (!aiSuggestionsContainer) return;

        // Get template
        const template = document.getElementById('suggestionCardsTemplate');
        if (template) {
            // Use template
            aiSuggestionsContainer.innerHTML = template.innerHTML;
        } else {
            // Fallback to basic structure
            aiSuggestionsContainer.innerHTML = `
                <h5>AI Suggestions</h5>
                <div class="ai-suggestions"></div>
            `;
        }

        // Get suggestions container
        const suggestionsContainer = aiSuggestionsContainer.querySelector('.ai-suggestions');
        if (!suggestionsContainer) return;

        // Clear existing suggestions
        suggestionsContainer.innerHTML = '';

        // Add each suggestion
        suggestions.forEach((suggestion, index) => {
            const card = document.createElement('div');
            card.className = 'ai-suggestion-card mb-3 p-3 border rounded';
            card.innerHTML = `
                <div class="form-check mb-2">
                    <input class="form-check-input ai-suggestion-select" type="radio" name="ai_suggestion" id="suggestion${index + 1}" value="${index + 1}">
                    <label class="form-check-label fw-bold" for="suggestion${index + 1}">Suggestion ${index + 1}</label>
                    <button type="button" class="btn btn-sm btn-primary float-end apply-suggestion-btn" data-index="${index}">
                        <i class="fas fa-check me-1"></i> Apply
                    </button>
                </div>
                <div class="ai-suggestion-title mb-1">${suggestion.title || 'No title'}</div>
                <div class="ai-suggestion-content small text-muted">${suggestion.content || 'No content'}</div>
            `;
            suggestionsContainer.appendChild(card);
        });

        // Store suggestions in hidden input
        const aiSuggestionDataInput = document.getElementById('aiSuggestionData');
        if (aiSuggestionDataInput) {
            aiSuggestionDataInput.value = JSON.stringify(suggestions);
        }

        // Setup apply buttons
        setupApplySuggestionButtons(suggestions);
    }

    // Helper function to setup apply suggestion buttons
    function setupApplySuggestionButtons(suggestions) {
        const applyButtons = document.querySelectorAll('.apply-suggestion-btn');
        applyButtons.forEach(button => {
            button.addEventListener('click', function() {
                const index = parseInt(this.dataset.index || '0');
                if (suggestions[index]) {
                    applySuggestion(suggestions[index]);

                    // Select the radio button
                    const radio = document.getElementById(`suggestion${index + 1}`);
                    if (radio) {
                        radio.checked = true;
                    }
                }
            });
        });
    }

    // Helper function to apply a suggestion
    function applySuggestion(suggestion) {
        // Apply title
        const adTitleInput = document.getElementById('adTitle');
        if (adTitleInput && suggestion.title) {
            adTitleInput.value = suggestion.title;
        }

        // Apply content
        const adContentTextarea = document.getElementById('adContent');
        if (adContentTextarea && suggestion.content) {
            adContentTextarea.value = suggestion.content;
        }

        // Mark as used AI
        const usedAiInput = document.getElementById('usedAiInput');
        if (usedAiInput) {
            usedAiInput.value = 'true';
            console.log('Set usedAi to true');
        }

        // Show success message
        showSuccess('Suggestion applied successfully!');
    }

    // Helper function to show success message
    function showSuccess(message) {
        // Create a success alert
        const successAlert = document.createElement('div');
        successAlert.className = 'alert alert-success alert-dismissible fade show';
        successAlert.innerHTML = `
            <i class="fas fa-check-circle me-2"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;

        // Add to the page
        const form = document.getElementById('adCreationForm');
        if (form) {
            form.prepend(successAlert);

            // Remove after 3 seconds
            setTimeout(() => {
                successAlert.remove();
            }, 3000);
        }
    }

    // Helper function to show error message
    function showError(message) {
        const formErrorContainer = document.getElementById('formErrorContainer');
        if (formErrorContainer) {
            formErrorContainer.textContent = message;
            formErrorContainer.style.display = 'block';

            // Hide after 5 seconds
            setTimeout(() => {
                formErrorContainer.style.display = 'none';
            }, 5000);
        } else {
            // Fallback to alert
            alert(message);
        }
    }

    // Helper function to get CSRF token
    function getCSRFToken() {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.startsWith('csrftoken=')) {
                return cookie.substring('csrftoken='.length, cookie.length);
            }
        }
        return '';
    }

    // Initialize immediately
    initGenerateButton();

    // Also initialize on DOMContentLoaded for safety
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOMContentLoaded - initializing Generate Button again');
        initGenerateButton();
    });

    // Also initialize after a short delay to ensure it runs after other scripts
    setTimeout(function() {
        console.log('Delayed initialization - initializing Generate Button again');
        initGenerateButton();
    }, 500);
})();
