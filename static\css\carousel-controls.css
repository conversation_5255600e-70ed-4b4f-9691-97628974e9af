/*
 * Enhanced Carousel Controls
 * Sleek, corporate, and elegant carousel navigation
 */

/* Base carousel styling */
.ad-carousel {
    position: relative;
    width: 100%;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    margin: 20px auto;
    max-width: calc(100% - 30px); /* Add some margin on the sides */
}

/* Carousel inner content */
.ad-carousel-inner {
    display: flex;
    transition: transform 0.5s ease;
    width: 100%;
    position: relative;
}

.ad-carousel-item {
    flex: 0 0 100%;
    width: 100%;
    padding: 0;
    position: relative;
}

/* Carousel controls container */
.ad-carousel-controls {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 12px;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
    z-index: 10;
    height: 50px;
    margin-bottom: 10px; /* Increased margin between controls and CTA */
}

/* Previous and Next buttons */
.ad-carousel-prev,
.ad-carousel-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.85);
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    color: #0078d4;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    opacity: 0.7;
    z-index: 20;
}

/* Position the buttons on the sides */
.ad-carousel-prev {
    left: 10px;
}

.ad-carousel-next {
    right: 10px;
}

/* Hover effects */
.ad-carousel-prev:hover,
.ad-carousel-next:hover {
    background: rgba(255, 255, 255, 0.95);
    opacity: 1;
    transform: translateY(-50%) scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Active state */
.ad-carousel-prev:active,
.ad-carousel-next:active {
    transform: translateY(-50%) scale(0.95);
}

/* Indicators container */
.ad-carousel-indicators {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    z-index: 15;
    margin-bottom: 10px; /* Add margin between dots and CTA */
}

/* Indicator dots */
.ad-carousel-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Active indicator */
.ad-carousel-dot.active {
    background-color: #ffffff;
    transform: scale(1.2);
    box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
}

/* Hover effect for indicators */
.ad-carousel-dot:hover {
    background-color: rgba(255, 255, 255, 0.8);
}

/* Premium carousel controls - more elegant styling */
.premium-ad-carousel-control {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 38px;
    height: 38px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
    color: #0078d4;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    opacity: 0.8;
    z-index: 20;
}

.premium-ad-carousel-prev {
    left: 10px;
}

.premium-ad-carousel-next {
    right: 10px;
}

.premium-ad-carousel-control:hover {
    background: #ffffff;
    opacity: 1;
    transform: translateY(-50%) scale(1.05);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.premium-ad-carousel-control:active {
    transform: translateY(-50%) scale(0.95);
}

/* Premium carousel container */
.premium-ad-carousel {
    position: relative;
    overflow: hidden;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin: 20px auto;
    max-width: calc(100% - 30px); /* Add some margin on the sides */
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid rgba(26, 35, 126, 0.1);
}

/* Golden HR Line */
.golden-hr {
    height: 3px;
    background: linear-gradient(90deg, rgba(255, 215, 0, 0.1), rgba(255, 215, 0, 0.8), rgba(255, 215, 0, 0.1));
    border: none;
    margin: 15px auto 25px;
    max-width: calc(100% - 60px);
    border-radius: 3px;
    box-shadow: 0 1px 3px rgba(255, 215, 0, 0.2);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    /* Adjust size for mobile */
    .ad-carousel-prev,
    .ad-carousel-next {
        width: 32px;
        height: 32px;
        font-size: 14px;
    }

    .ad-carousel-prev {
        left: 8px;
    }

    .ad-carousel-next {
        right: 8px;
    }

    /* Adjust indicators for mobile */
    .ad-carousel-dot {
        width: 6px;
        height: 6px;
    }

    /* Adjust premium controls for mobile */
    .premium-ad-carousel-control {
        width: 32px;
        height: 32px;
        font-size: 12px;
    }

    .premium-ad-carousel-prev {
        left: 8px;
    }

    .premium-ad-carousel-next {
        right: 8px;
    }

    /* Adjust carousel containers for mobile */
    .ad-carousel,
    .premium-ad-carousel {
        max-width: calc(100% - 20px);
        margin: 15px auto;
    }
}
