# Generated by Django 5.1.7 on 2025-05-28 20:06

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('qrcode_app', '0009_qrcode_block_tor_scans_qrcode_block_vpn_scans_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='QRScanLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.Char<PERSON>ield(help_text='QR code identifier or unique_id', max_length=50)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('ip_address', models.GenericIPAddressField()),
                ('user_agent', models.TextField()),
                ('country', models.CharField(blank=True, help_text='ISO country code', max_length=2, null=True)),
                ('city', models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                ('org', models.Char<PERSON><PERSON>(blank=True, help_text='Organization/ISP from IPinfo', max_length=255, null=True)),
            ],
            options={
                'verbose_name': 'QR Scan Log',
                'verbose_name_plural': 'QR Scan Logs',
                'ordering': ['-timestamp'],
            },
        ),
    ]
