# Generated by Django 4.2.7 on 2025-05-21 07:29

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='CachedSuggestion',
            fields=[
                ('prompt_hash', models.CharField(max_length=32, primary_key=True, serialize=False)),
                ('content', models.TextField()),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('expires_at', models.DateTimeField()),
            ],
            options={
                'verbose_name': 'Cached Suggestion',
                'verbose_name_plural': 'Cached Suggestions',
                'db_table': 'ai_cached_suggestions',
                'indexes': [models.Index(fields=['expires_at'], name='ai_cached_s_expires_d4baa8_idx')],
            },
        ),
    ]
