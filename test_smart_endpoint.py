#!/usr/bin/env python
"""
Test Smart AI Engine Endpoint - Test the updated API endpoint
"""

import requests
import json
import time

# Test configuration
BASE_URL = "http://127.0.0.1:8000"
ENDPOINT = "/ads/test/smart-ai/"
TEST_PARAMS = {
    'language': 'english',
    'business_type': 'Fashion Boutique',
    'target_audience': 'Young Professionals',
    'tone': 'professional',
    'title': 'Stylish Fashion',
    'num_suggestions': 3
}

def test_smart_ai_endpoint():
    """Test the Smart AI Engine endpoint"""
    print("🧠 TESTING SMART AI ENGINE ENDPOINT")
    print("=" * 50)

    # Test parameters
    print(f"📋 Test Parameters:")
    for key, value in TEST_PARAMS.items():
        print(f"   {key}: {value}")
    print()

    # Test endpoint
    url = BASE_URL + ENDPOINT
    print(f"🔗 Testing endpoint: {url}")
    print()

    try:
        # Make request
        print("🚀 Making request to Smart AI Engine...")
        start_time = time.time()

        response = requests.post(
            url,
            data=json.dumps(TEST_PARAMS),
            headers={
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            timeout=30
        )

        end_time = time.time()
        response_time = end_time - start_time

        print(f"⚡ Response time: {response_time:.2f} seconds")
        print(f"📊 Status code: {response.status_code}")

        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ Request successful!")
                print(f"📄 Response keys: {list(data.keys())}")

                if data.get('success'):
                    suggestions = data.get('suggestions', [])
                    source = data.get('source', 'unknown')
                    engine = data.get('engine', 'unknown')

                    print(f"🎯 Generated {len(suggestions)} suggestions!")
                    print(f"🤖 Engine: {engine}")
                    print(f"📡 Source: {source}")
                    print()

                    # Display suggestions
                    for i, suggestion in enumerate(suggestions, 1):
                        print(f"   📝 Suggestion {i}:")
                        print(f"      Title: {suggestion.get('title', 'N/A')}")
                        print(f"      Content: {suggestion.get('content', 'N/A')[:80]}...")
                        print(f"      Provider: {suggestion.get('provider', 'N/A')}")
                        print(f"      Model: {suggestion.get('model', 'N/A')}")
                        print(f"      Cached: {suggestion.get('cached', False)}")
                        print(f"      Cache Source: {suggestion.get('cache_source', 'N/A')}")
                        print(f"      Smart Engine: {suggestion.get('smart_engine', False)}")
                        print(f"      Rank: {suggestion.get('rank', 'N/A')}")
                        print(f"      Selection Score: {suggestion.get('selection_score', 'N/A')}")
                        print(f"      AI Generated: {suggestion.get('ai_generated', False)}")
                        print()

                    # Test cache by making another request
                    print("🔄 Testing cache with second request...")
                    start_time = time.time()

                    response2 = requests.post(
                        url,
                        data=json.dumps(TEST_PARAMS),
                        headers={
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        timeout=30
                    )

                    end_time = time.time()
                    cache_response_time = end_time - start_time

                    print(f"⚡ Second request time: {cache_response_time:.2f} seconds")

                    if cache_response_time < 0.5:
                        print("✅ Cache is working - very fast response!")
                    elif cache_response_time < 2:
                        print("⚠️  Possibly cached but slower than expected")
                    else:
                        print("❌ Cache might not be working - slow response")

                    if response2.status_code == 200:
                        data2 = response2.json()
                        if data2.get('success'):
                            source2 = data2.get('source', 'unknown')
                            print(f"📡 Second request source: {source2}")

                            if source2 == 'cache':
                                print("✅ Cache confirmed working!")
                            else:
                                print(f"⚠️  Expected cache but got: {source2}")

                    return True

                else:
                    error = data.get('error', 'Unknown error')
                    print(f"❌ Request failed: {error}")
                    return False

            except json.JSONDecodeError as e:
                print(f"❌ Failed to parse JSON response: {e}")
                print(f"📄 Response content: {response.text[:200]}")
                return False

        else:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"📄 Response: {response.text[:200]}")
            return False

    except requests.exceptions.Timeout:
        print("⏰ Request timed out (>30s)")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False

if __name__ == "__main__":
    success = test_smart_ai_endpoint()

    print("\n📊 FINAL RESULT:")
    print("=" * 50)

    if success:
        print("🎉 SMART AI ENGINE ENDPOINT IS WORKING!")
        print("   ✅ Successfully generated suggestions")
        print("   ✅ Smart AI Engine is operational")
        print("   ✅ Cache system is functional")
        print("   ✅ Provider metadata is complete")
        print("\n🌐 Ready for browser testing!")
        print(f"   Navigate to: {BASE_URL}/ads/create/")
        print("   Use the test parameters shown above")
    else:
        print("❌ SMART AI ENGINE ENDPOINT FAILED")
        print("   Check the Django server logs for errors")
        print("   Ensure the server is running and accessible")

    print(f"\n🔗 Endpoint tested: {BASE_URL}{ENDPOINT}")
    print("📋 Parameters used:")
    for key, value in TEST_PARAMS.items():
        print(f"   {key}: {value}")
