from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
        ('ads', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='AiFeedback',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('suggestion_data', models.J<PERSON>NField(help_text='The AI suggestion that received feedback')),
                ('feedback', models.CharField(choices=[('like', 'Like'), ('dislike', 'Dislike'), ('neutral', 'Neutral')], max_length=10)),
                ('comments', models.TextField(blank=True, help_text='Optional user comments about the suggestion', null=True)),
                ('model_used', models.CharField(blank=True, help_text='The AI model that generated the content', max_length=50, null=True)),
                ('language', models.Char<PERSON><PERSON>(blank=True, max_length=20, null=True)),
                ('page', models.Char<PERSON>ield(blank=True, help_text='The page where feedback was given', max_length=255, null=True)),
                ('processing_time', models.FloatField(default=0, help_text='Time taken to generate the suggestion in seconds')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('ad', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='ai_feedback', to='ads.ad')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ai_feedback', to='auth.user')),
            ],
            options={
                'verbose_name': 'AI Feedback',
                'verbose_name_plural': 'AI Feedback',
                'ordering': ['-created_at'],
            },
        ),
    ]
