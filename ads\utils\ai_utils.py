"""
Utility functions for AI integration in the ad creation process.
"""

import time
import logging
import requests
from functools import wraps
from typing import Dict, Any, Callable, Optional, List, Tuple, Union
from django.conf import settings

logger = logging.getLogger(__name__)

# Default retry settings
DEFAULT_MAX_RETRIES = 3
DEFAULT_RETRY_DELAY = 1  # seconds
DEFAULT_BACKOFF_FACTOR = 2  # exponential backoff
DEFAULT_RETRY_ERRORS = (
    requests.exceptions.Timeout,
    requests.exceptions.ConnectionError,
    requests.exceptions.RequestException,
)

# Error message templates
ERROR_MESSAGES = {
    "connection": "We're having trouble connecting to our AI service. Please try again in a few moments.",
    "timeout": "The AI service is taking longer than expected to respond. Please try again with a simpler request.",
    "server": "Our AI service is experiencing technical difficulties. Our team has been notified and is working on it.",
    "model_not_found": "The requested AI model is currently unavailable. We've automatically switched to an alternative model.",
    "invalid_input": "Your input couldn't be processed by our AI. Please try with different or more specific details.",
    "rate_limit": "You've reached the maximum number of AI requests. Please try again in a few minutes.",
    "unknown": "Something went wrong with the AI service. Please try again later.",
    "auth": "Authentication with the AI service failed. Please contact support if this persists.",
}

# Cache for AI responses
ai_response_cache = {}

def with_retry(
    max_retries: int = DEFAULT_MAX_RETRIES,
    retry_delay: float = DEFAULT_RETRY_DELAY,
    backoff_factor: float = DEFAULT_BACKOFF_FACTOR,
    retry_errors: tuple = DEFAULT_RETRY_ERRORS,
):
    """
    Decorator for retrying functions that may fail due to network or service issues.
    
    Args:
        max_retries: Maximum number of retry attempts
        retry_delay: Initial delay between retries in seconds
        backoff_factor: Factor by which the delay increases with each retry
        retry_errors: Tuple of exceptions that should trigger a retry
        
    Returns:
        Decorated function with retry logic
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            delay = retry_delay
            
            for attempt in range(max_retries + 1):
                try:
                    if attempt > 0:
                        logger.info(f"Retry attempt {attempt}/{max_retries} for {func.__name__}")
                    
                    return func(*args, **kwargs)
                
                except retry_errors as e:
                    last_exception = e
                    
                    if attempt < max_retries:
                        logger.warning(
                            f"Attempt {attempt + 1}/{max_retries + 1} failed for {func.__name__}: {str(e)}. "
                            f"Retrying in {delay} seconds..."
                        )
                        time.sleep(delay)
                        delay *= backoff_factor
                    else:
                        logger.error(f"All {max_retries + 1} attempts failed for {func.__name__}: {str(e)}")
            
            # If we get here, all retries failed
            raise last_exception
        
        return wrapper
    
    return decorator

def get_user_friendly_error_message(exception: Exception) -> str:
    """
    Convert technical error messages to user-friendly messages.
    
    Args:
        exception: The exception that occurred
        
    Returns:
        A user-friendly error message
    """
    error_type = type(exception).__name__
    error_message = str(exception)
    
    if isinstance(exception, requests.exceptions.Timeout):
        return ERROR_MESSAGES["timeout"]
    
    if isinstance(exception, requests.exceptions.ConnectionError):
        return ERROR_MESSAGES["connection"]
    
    if isinstance(exception, requests.exceptions.HTTPError):
        status_code = exception.response.status_code if hasattr(exception, 'response') else 0
        
        if status_code == 401 or status_code == 403:
            return ERROR_MESSAGES["auth"]
        
        if status_code == 404:
            return ERROR_MESSAGES["model_not_found"]
        
        if status_code == 429:
            return ERROR_MESSAGES["rate_limit"]
        
        if status_code >= 500:
            return ERROR_MESSAGES["server"]
    
    # Check for specific error messages in the exception text
    if "model not found" in error_message.lower() or "model doesn't exist" in error_message.lower():
        return ERROR_MESSAGES["model_not_found"]
    
    if "rate limit" in error_message.lower() or "too many requests" in error_message.lower():
        return ERROR_MESSAGES["rate_limit"]
    
    if "invalid input" in error_message.lower() or "validation error" in error_message.lower():
        return ERROR_MESSAGES["invalid_input"]
    
    # Default to unknown error
    return ERROR_MESSAGES["unknown"]

def cache_ai_response(cache_key: str, ttl: int = 3600) -> Callable:
    """
    Decorator to cache AI responses to avoid redundant API calls.
    
    Args:
        cache_key: Function to generate a cache key from the function arguments
        ttl: Time-to-live for cache entries in seconds (default: 1 hour)
        
    Returns:
        Decorated function with caching
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            key = cache_key(*args, **kwargs) if callable(cache_key) else cache_key
            
            # Check if result is in cache and not expired
            if key in ai_response_cache:
                entry = ai_response_cache[key]
                if time.time() - entry["timestamp"] < ttl:
                    logger.info(f"Cache hit for {func.__name__} with key {key}")
                    return entry["result"]
            
            # Call the function
            result = func(*args, **kwargs)
            
            # Cache the result
            ai_response_cache[key] = {
                "result": result,
                "timestamp": time.time()
            }
            
            return result
        
        return wrapper
    
    return decorator

def generate_cache_key(language: str, business_type: str, target_audience: str, tone: str, title: str = "") -> str:
    """
    Generate a cache key for AI responses based on request parameters.
    
    Args:
        language: The language for the ad
        business_type: The business type or product
        target_audience: The target audience
        tone: The tone of the ad
        title: Optional ad title
        
    Returns:
        A string cache key
    """
    # Normalize inputs to create consistent keys
    normalized_inputs = [
        language.lower().strip(),
        business_type.lower().strip(),
        target_audience.lower().strip(),
        tone.lower().strip(),
        title.lower().strip() if title else ""
    ]
    
    return ":".join(normalized_inputs)
