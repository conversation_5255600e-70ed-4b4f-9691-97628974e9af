{% extends 'base.html' %}
{% load static %}

{% block title %}Enterprise Analytics Dashboard{% endblock %}

{% block extra_css %}
<!-- Include enterprise common CSS -->
{% include 'ads/includes/enterprise_common_css.html' %}
<link rel="stylesheet" href="{% static 'ads/css/dashboard_enterprise.css' %}">
<link rel="stylesheet" href="{% static 'ads/css/analytics_enterprise.css' %}">
<!-- Chart.js -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.css">
<!-- Date Range Picker -->
<link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
<!-- Geographic Map Styles -->
{% include 'ads/includes/geo_map_styles.html' %}
<!-- Footer Fix -->
<link rel="stylesheet" href="{% static 'ads/css/footer-fix.css' %}">
<style>
    /* Additional styles specific to this page */
    .dashboard-card {
        background: linear-gradient(to bottom, #ffffff, #f9f9f9);
    }

    .card-header {
        background: linear-gradient(to right, rgba(26, 35, 126, 0.05), rgba(26, 35, 126, 0.01));
    }

    .chart-container {
        position: relative;
        padding: 20px;
        border-radius: 8px;
        background: linear-gradient(to bottom, #ffffff, #f8f9fa);
        box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
    }

    .chart-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 30%;
        background: linear-gradient(to bottom, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0));
        pointer-events: none;
        z-index: 1;
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
    }

    .table th {
        background: linear-gradient(to right, rgba(26, 35, 126, 0.08), rgba(26, 35, 126, 0.03));
        color: #1a237e;
        font-weight: 600;
    }

    .table td {
        vertical-align: middle;
    }

    .badge {
        padding: 0.5em 0.8em;
        font-weight: 500;
        border-radius: 30px;
    }

    .conversion-stat-card {
        background: linear-gradient(to bottom, #ffffff, #f9f9f9);
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        height: 100%;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .conversion-stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 30%;
        background: linear-gradient(to bottom, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0));
        pointer-events: none;
        z-index: 1;
        border-top-left-radius: 12px;
        border-top-right-radius: 12px;
    }

    .conversion-stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .stat-title {
        font-size: 16px;
        color: #6c757d;
        margin-bottom: 10px;
        position: relative;
        z-index: 2;
    }

    .stat-value {
        font-size: 32px;
        font-weight: 700;
        color: #1a237e;
        margin-bottom: 10px;
        position: relative;
        z-index: 2;
    }

    .stat-trend {
        font-size: 14px;
        color: #6c757d;
        position: relative;
        z-index: 2;
    }

    .btn-group .btn-outline-primary.active {
        background-color: #1a237e;
        color: white;
        border-color: #1a237e;
    }

    /* Geography and Audience Tabs Styles */
    .geo-view-container {
        position: relative;
    }

    .geo-view {
        display: none;
    }

    .geo-view.active {
        display: block;
    }

    .empty-state.friendly-message {
        text-align: center;
        padding: 40px 20px;
        background: linear-gradient(to bottom, rgba(255,255,255,0.8), rgba(248,249,250,0.8));
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.03);
    }

    .empty-state-icon {
        font-size: 48px;
        color: #c5cae9;
        margin-bottom: 20px;
    }

    .empty-state-title {
        font-size: 20px;
        color: #3949ab;
        margin-bottom: 10px;
        font-weight: 600;
    }

    .empty-state-description {
        color: #5c6bc0;
        font-size: 16px;
        max-width: 500px;
        margin: 0 auto 15px;
    }

    .empty-state-tip {
        font-size: 14px;
        color: #5c6bc0;
        font-style: italic;
        margin-top: 15px;
    }

    .demographic-card {
        background: linear-gradient(to bottom, #ffffff, #f9f9f9);
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        margin-bottom: 20px;
        position: relative;
        overflow: hidden;
    }

    .demographic-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 30%;
        background: linear-gradient(to bottom, rgba(255,255,255,0.8), rgba(255,255,255,0));
        pointer-events: none;
        z-index: 1;
    }

    .demographic-title {
        font-size: 16px;
        color: #3949ab;
        margin-bottom: 15px;
        font-weight: 600;
        position: relative;
        z-index: 2;
    }

    .demographic-title i {
        margin-right: 8px;
        color: #5c6bc0;
    }
</style>
{% endblock %}

{% block content %}
<!-- Enterprise Analytics Dashboard Container -->
<div class="enterprise-dashboard analytics-dashboard">
    <!-- Context-aware Header -->
    <div class="dashboard-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="dashboard-welcome">
                        <h1 class="welcome-title">Analytics Dashboard</h1>
                        <p class="welcome-subtitle">Detailed insights into your advertising performance</p>
                        <a href="{% url 'ads:dashboard' %}" class="btn btn-sm btn-outline-light mt-2">
                            <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
                        </a>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="dashboard-actions">
                        <div class="action-item">
                            <div class="date-range-picker-container">
                                <button class="btn btn-light" id="daterange-btn">
                                    <i class="fas fa-calendar-alt me-2"></i>
                                    <span>{{ date_range|default:'Last 30 Days' }}</span>
                                    <i class="fas fa-caret-down ms-2"></i>
                                </button>
                            </div>
                        </div>
                        <div class="action-item">
                            <div class="dropdown">
                                <button class="btn btn-light dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-download me-2"></i> Export Data
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="exportDropdown">
                                    <li><a class="dropdown-item" href="{% url 'ads:analytics_enterprise' %}?{{ request.GET.urlencode }}&export=csv"><i class="fas fa-file-csv me-2"></i> CSV</a></li>
                                    <li><a class="dropdown-item" href="{% url 'ads:analytics_enterprise' %}?{{ request.GET.urlencode }}&export=excel"><i class="fas fa-file-excel me-2"></i> Excel</a></li>
                                    <li><a class="dropdown-item" href="{% url 'ads:analytics_enterprise' %}?{{ request.GET.urlencode }}&export=pdf"><i class="fas fa-file-pdf me-2"></i> PDF</a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="action-item">
                            <button class="btn btn-light" data-bs-toggle="modal" data-bs-target="#scheduleReportModal">
                                <i class="fas fa-calendar-alt me-2"></i> Schedule Report
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Dashboard Content -->
    <div class="dashboard-content">
        <div class="container-fluid">
            <div class="row">
                <!-- Sidebar Navigation -->
                <div class="col-lg-2 dashboard-sidebar">
                    <div class="sidebar-container">
                        <div class="sidebar-header">
                            <h2 class="sidebar-title">Analytics</h2>
                        </div>
                        <nav class="sidebar-nav">
                            <ul class="nav-list">
                                <li class="nav-item active">
                                    <a href="#overview" class="nav-link" data-tab="overview">
                                        <i class="fas fa-chart-line"></i>
                                        <span>Overview</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="#performance" class="nav-link" data-tab="performance">
                                        <i class="fas fa-tachometer-alt"></i>
                                        <span>Performance</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="#audience" class="nav-link" data-tab="audience">
                                        <i class="fas fa-users"></i>
                                        <span>Audience</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="#geo" class="nav-link" data-tab="geo">
                                        <i class="fas fa-globe-africa"></i>
                                        <span>Geography</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="#comparison" class="nav-link" data-tab="comparison">
                                        <i class="fas fa-balance-scale"></i>
                                        <span>Comparison</span>
                                    </a>
                                </li>
                                {% if user.is_superuser %}
                                <li class="nav-item nav-divider">
                                    <span class="divider-label">Admin</span>
                                </li>
                                <li class="nav-item">
                                    <a href="#system" class="nav-link" data-tab="system">
                                        <i class="fas fa-server"></i>
                                        <span>System Analytics</span>
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                </div>

                <!-- Main Content Area -->
                <div class="col-lg-10 dashboard-main">
                    <!-- Tab Content -->
                    <div class="tab-content">
                        <!-- Overview Tab -->
                        <div class="tab-pane active" id="overview">
                            <!-- KPI Stats Row -->
                            <div class="dashboard-section">
                                <div class="row stats-row">
                                    <div class="col-md-3 col-sm-6">
                                        <div class="stat-card impressions">
                                            <div class="stat-icon">
                                                <i class="fas fa-eye"></i>
                                            </div>
                                            <div class="stat-value">{{ total_impressions }}</div>
                                            <div class="stat-label">Total Impressions</div>
                                            <div class="stat-trend">
                                                <i class="fas fa-arrow-up"></i> {{ impression_trend }}% from previous period
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="stat-card clicks">
                                            <div class="stat-icon">
                                                <i class="fas fa-mouse-pointer"></i>
                                            </div>
                                            <div class="stat-value">{{ total_clicks }}</div>
                                            <div class="stat-label">Total Clicks</div>
                                            <div class="stat-trend">
                                                <i class="fas fa-arrow-up"></i> {{ click_trend }}% from previous period
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="stat-card ctr">
                                            <div class="stat-icon">
                                                <i class="fas fa-percentage"></i>
                                            </div>
                                            <div class="stat-value">{{ ctr|floatformat:2 }}%</div>
                                            <div class="stat-label">Click-Through Rate</div>
                                            <div class="stat-trend">
                                                <i class="fas fa-arrow-up"></i> {{ ctr_trend }}% from previous period
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="stat-card conversions">
                                            <div class="stat-icon">
                                                <i class="fas fa-exchange-alt"></i>
                                            </div>
                                            <div class="stat-value">{{ total_conversions|default:0 }}</div>
                                            <div class="stat-label">Conversions</div>
                                            <div class="stat-trend">
                                                <i class="fas fa-{% if conversion_trend > 0 %}arrow-up{% elif conversion_trend < 0 %}arrow-down{% else %}minus{% endif %}"></i>
                                                {{ conversion_trend|floatformat:1 }}% from previous period
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Main Chart -->
                            <div class="dashboard-section">
                                <div class="dashboard-card">
                                    <div class="card-header">
                                        <h3 class="card-title">Performance Over Time</h3>
                                        <div class="card-actions">
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-outline-primary active" data-metric="impressions">Impressions</button>
                                                <button type="button" class="btn btn-sm btn-outline-primary" data-metric="clicks">Clicks</button>
                                                <button type="button" class="btn btn-sm btn-outline-primary" data-metric="ctr">CTR</button>
                                                <button type="button" class="btn btn-sm btn-outline-primary" data-metric="conversions">Conversions</button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="chart-container">
                                            <canvas id="performanceChart" height="300"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Campaign Comparison Chart -->
                            <div class="dashboard-section">
                                <div class="dashboard-card">
                                    <div class="card-header">
                                        <h3 class="card-title">Campaign Comparison</h3>
                                        <div class="card-actions">
                                            <select class="form-select form-select-sm" id="campaign-metric-selector">
                                                <option value="impressions">Compare by Impressions</option>
                                                <option value="clicks" selected>Compare by Clicks</option>
                                                <option value="ctr">Compare by CTR</option>
                                                <option value="conversions">Compare by Conversions</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        {% if campaigns|length > 0 %}
                                            <div class="chart-container">
                                                <canvas id="campaignComparisonChart" height="300"></canvas>
                                            </div>
                                        {% else %}
                                            <div class="empty-state">
                                                <div class="empty-state-icon">
                                                    <i class="fas fa-chart-bar"></i>
                                                </div>
                                                <h3 class="empty-state-title">No campaign data available</h3>
                                                <p class="empty-state-description">Create campaigns to compare their performance</p>
                                                <a href="{% url 'ads:campaign_create' %}" class="btn btn-primary">Create Campaign</a>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Top Performing Ads -->
                            <div class="dashboard-section">
                                <div class="dashboard-card">
                                    <div class="card-header">
                                        <h3 class="card-title">Top Performing Ads</h3>
                                        <div class="card-actions">
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" id="sortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                                    Sort By
                                                </button>
                                                <ul class="dropdown-menu" aria-labelledby="sortDropdown">
                                                    <li><a class="dropdown-item active" href="#" data-sort="impressions">Impressions</a></li>
                                                    <li><a class="dropdown-item" href="#" data-sort="clicks">Clicks</a></li>
                                                    <li><a class="dropdown-item" href="#" data-sort="ctr">CTR</a></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>Ad Title</th>
                                                        <th>Campaign</th>
                                                        <th>Type</th>
                                                        <th>Impressions</th>
                                                        <th>Clicks</th>
                                                        <th>CTR</th>
                                                        <th>Conversions</th>
                                                        <th>Status</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {% for ad in top_ads %}
                                                    <tr>
                                                        <td>{{ ad.title }}</td>
                                                        <td>{{ ad.campaign.name|default:"—" }}</td>
                                                        <td>{{ ad.ad_type.name }}</td>
                                                        <td>{{ ad.impressions }}</td>
                                                        <td>{{ ad.clicks }}</td>
                                                        <td>{{ ad.ctr|floatformat:2 }}%</td>
                                                        <td>{{ ad.conversion_count|default:0 }}</td>
                                                        <td><span class="badge bg-{{ ad.status_color }}">{{ ad.get_status_display }}</span></td>
                                                        <td>
                                                            <a href="{% url 'ads:ad_analytics' ad.slug %}" class="btn btn-sm btn-outline-primary">
                                                                <i class="fas fa-chart-bar"></i>
                                                            </a>
                                                            <a href="{% url 'ads:ad_detail' ad.slug %}" class="btn btn-sm btn-outline-secondary">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                    {% endfor %}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Conversions Tab -->
                            <div class="tab-pane" id="conversions">
                                <div class="dashboard-section">
                                    <div class="dashboard-card">
                                        <div class="card-header">
                                            <h3 class="card-title">Conversion Analytics</h3>
                                            <div class="card-actions">
                                                <div class="time-selector">
                                                    <select class="form-select form-select-sm" id="conversion-period-selector">
                                                        <option value="7days">Last 7 days</option>
                                                        <option value="30days" selected>Last 30 days</option>
                                                        <option value="90days">Last 90 days</option>
                                                        <option value="year">Last year</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="row mb-4">
                                                <div class="col-md-6">
                                                    <div class="conversion-stat-card">
                                                        <h4 class="stat-title">Total Conversions</h4>
                                                        <div class="stat-value">{{ total_conversions|default:0 }}</div>
                                                        <div class="stat-trend">
                                                            <i class="fas fa-{% if conversion_trend > 0 %}arrow-up{% elif conversion_trend < 0 %}arrow-down{% else %}minus{% endif %}"></i>
                                                            <span>{{ conversion_trend|floatformat:1 }}% from previous period</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="conversion-stat-card">
                                                        <h4 class="stat-title">Conversion Rate</h4>
                                                        <div class="stat-value">{{ conversion_rate|default:0|floatformat:2 }}%</div>
                                                        <div class="stat-trend">
                                                            <i class="fas fa-{% if conversion_rate_trend > 0 %}arrow-up{% elif conversion_rate_trend < 0 %}arrow-down{% else %}minus{% endif %}"></i>
                                                            <span>{{ conversion_rate_trend|default:0|floatformat:1 }}% from previous period</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="chart-container">
                                                <canvas id="conversionTrendsChart" height="300"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="dashboard-section">
                                    <div class="dashboard-card">
                                        <div class="card-header">
                                            <h3 class="card-title">Top Converting Ads</h3>
                                            <div class="card-actions">
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" id="sortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                                        Sort By
                                                    </button>
                                                    <ul class="dropdown-menu" aria-labelledby="sortDropdown">
                                                        <li><a class="dropdown-item" href="#" data-sort="conversions">Conversions</a></li>
                                                        <li><a class="dropdown-item" href="#" data-sort="conversion_rate">Conversion Rate</a></li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="table-responsive">
                                                <table class="table table-hover">
                                                    <thead>
                                                        <tr>
                                                            <th>Ad Title</th>
                                                            <th>Campaign</th>
                                                            <th>Clicks</th>
                                                            <th>Conversions</th>
                                                            <th>Conversion Rate</th>
                                                            <th>Status</th>
                                                            <th>Actions</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        {% for ad in top_converting_ads %}
                                                        <tr>
                                                            <td>{{ ad.title }}</td>
                                                            <td>{{ ad.campaign.name|default:"—" }}</td>
                                                            <td>{{ ad.clicks }}</td>
                                                            <td>{{ ad.conversion_count|default:0 }}</td>
                                                            <td>{{ ad.conversion_rate|floatformat:2 }}%</td>
                                                            <td><span class="badge bg-{{ ad.status_color }}">{{ ad.get_status_display }}</span></td>
                                                            <td>
                                                                <a href="{% url 'ads:ad_analytics' ad.slug %}" class="btn btn-sm btn-outline-primary">
                                                                    <i class="fas fa-chart-bar"></i>
                                                                </a>
                                                                <a href="{% url 'ads:ad_detail' ad.slug %}" class="btn btn-sm btn-outline-secondary">
                                                                    <i class="fas fa-eye"></i>
                                                                </a>
                                                            </td>
                                                        </tr>
                                                        {% empty %}
                                                        <tr>
                                                            <td colspan="7" class="text-center py-4">
                                                                <div class="empty-state">
                                                                    <div class="empty-state-icon">
                                                                        <i class="fas fa-exchange-alt"></i>
                                                                    </div>
                                                                    <h3 class="empty-state-title">No conversion data available</h3>
                                                                    <p class="empty-state-description">Start tracking conversions to see data here</p>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                        {% endfor %}
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Geography Tab -->
                            <div class="tab-pane" id="geo">
                                <div class="dashboard-section">
                                    <div class="dashboard-card">
                                        <div class="card-header">
                                            <h3 class="card-title">Geographic Distribution</h3>
                                            <div class="card-actions">
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary active" data-view="map">Map View</button>
                                                    <button type="button" class="btn btn-sm btn-outline-primary" data-view="chart">Chart View</button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="geo-view-container">
                                                <div id="geo-map-view" class="geo-view active">
                                                    {% if location_data_json and location_data_json != '{}' %}
                                                        <div id="world-map" style="height: 400px;"></div>
                                                    {% else %}
                                                        <div class="empty-state friendly-message">
                                                            <div class="empty-state-icon">
                                                                <i class="fas fa-globe"></i>
                                                            </div>
                                                            <h3 class="empty-state-title">No geographic data available yet</h3>
                                                            <p class="empty-state-description">
                                                                As users interact with your ads, we'll collect geographic data to show you where your audience is located.
                                                            </p>
                                                            <p class="empty-state-tip">
                                                                <i class="fas fa-lightbulb text-warning"></i> Tip: Share your ads to start collecting geographic insights.
                                                            </p>
                                                        </div>
                                                    {% endif %}
                                                </div>
                                                <div id="geo-chart-view" class="geo-view">
                                                    <canvas id="locationChart" height="400"></canvas>
                                                    {% if not location_data_json or location_data_json == '{}' %}
                                                        <div class="text-center mt-3">
                                                            <small class="text-muted">
                                                                We'll display a chart of your audience's locations once data is collected.
                                                            </small>
                                                        </div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="dashboard-section">
                                    <div class="dashboard-card">
                                        <div class="card-header">
                                            <h3 class="card-title">Audience Demographics</h3>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="demographic-card">
                                                        <h4 class="demographic-title">
                                                            <i class="fas fa-globe-americas"></i> Top Regions
                                                        </h4>
                                                        {% if location_data_json and location_data_json != '{}' %}
                                                            <div class="table-responsive">
                                                                <table class="table table-hover">
                                                                    <thead>
                                                                        <tr>
                                                                            <th>Region</th>
                                                                            <th>Impressions</th>
                                                                            <th>% of Total</th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody id="regions-table-body">
                                                                        <!-- Will be populated by JavaScript -->
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        {% else %}
                                                            <div class="empty-state friendly-message">
                                                                <p>No region data available yet. We'll show your top regions once users interact with your ads.</p>
                                                            </div>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="demographic-card">
                                                        <h4 class="demographic-title">
                                                            <i class="fas fa-city"></i> Top Cities
                                                        </h4>
                                                        {% if location_data_json and location_data_json != '{}' %}
                                                            <div class="table-responsive">
                                                                <table class="table table-hover">
                                                                    <thead>
                                                                        <tr>
                                                                            <th>City</th>
                                                                            <th>Impressions</th>
                                                                            <th>% of Total</th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody id="cities-table-body">
                                                                        <!-- Will be populated by JavaScript -->
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        {% else %}
                                                            <div class="empty-state friendly-message">
                                                                <p>No city data available yet. We'll show your top cities once users interact with your ads.</p>
                                                            </div>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Audience Tab -->
                            <div class="tab-pane" id="audience">
                                <div class="dashboard-section">
                                    <div class="dashboard-card">
                                        <div class="card-header">
                                            <h3 class="card-title">Device Distribution</h3>
                                        </div>
                                        <div class="card-body">
                                            {% if device_data_json and device_data_json != '{}' %}
                                                <div class="chart-container">
                                                    <canvas id="deviceChart" height="300"></canvas>
                                                </div>
                                            {% else %}
                                                <div class="empty-state friendly-message">
                                                    <div class="empty-state-icon">
                                                        <i class="fas fa-mobile-alt"></i>
                                                    </div>
                                                    <h3 class="empty-state-title">No device data available yet</h3>
                                                    <p class="empty-state-description">
                                                        We'll show you what devices your audience is using once they interact with your ads.
                                                    </p>
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <div class="dashboard-section">
                                    <div class="dashboard-card">
                                        <div class="card-header">
                                            <h3 class="card-title">Browser Distribution</h3>
                                        </div>
                                        <div class="card-body">
                                            {% if browser_data_json and browser_data_json != '{}' %}
                                                <div class="chart-container">
                                                    <canvas id="browserChart" height="300"></canvas>
                                                </div>
                                            {% else %}
                                                <div class="empty-state friendly-message">
                                                    <div class="empty-state-icon">
                                                        <i class="fas fa-globe"></i>
                                                    </div>
                                                    <h3 class="empty-state-title">No browser data available yet</h3>
                                                    <p class="empty-state-description">
                                                        We'll show you what browsers your audience is using once they interact with your ads.
                                                    </p>
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

<!-- Schedule Report Modal -->
<div class="modal fade" id="scheduleReportModal" tabindex="-1" aria-labelledby="scheduleReportModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="scheduleReportModalLabel">Schedule Automated Report</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="schedule-report-form" action="{% url 'ads:analytics_enterprise' %}" method="post">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="schedule_report">

                    <div class="mb-3">
                        <label for="report-name" class="form-label">Report Name</label>
                        <input type="text" class="form-control" id="report-name" name="report_name" required>
                    </div>

                    <div class="mb-3">
                        <label for="report-frequency" class="form-label">Frequency</label>
                        <select class="form-select" id="report-frequency" name="frequency" required>
                            <option value="daily">Daily</option>
                            <option value="weekly" selected>Weekly</option>
                            <option value="monthly">Monthly</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="report-format" class="form-label">Format</label>
                        <select class="form-select" id="report-format" name="format" required>
                            <option value="pdf">PDF</option>
                            <option value="excel">Excel</option>
                            <option value="csv">CSV</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="report-email" class="form-label">Email Recipients</label>
                        <input type="email" class="form-control" id="report-email" name="email" value="{{ request.user.email }}" required>
                        <div class="form-text">Separate multiple emails with commas</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="schedule-report-form" class="btn btn-primary">Schedule Report</button>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
<!-- Moment.js -->
<script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
<!-- Date Range Picker -->
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
<!-- Geographic Map Scripts -->
{% include 'ads/includes/geo_map_scripts.html' %}
<!-- Enterprise Dashboard JS -->
<script src="{% static 'ads/js/dashboard_enterprise.js' %}"></script>
<!-- Analytics Dashboard JS -->
<script src="{% static 'ads/js/analytics_enterprise.js' %}"></script>
<!-- New Analytics Dashboard JS -->
<script src="{% static 'js/analytics-dashboard.js' %}"></script>
<!-- Admin Footer Fix JS -->
<script src="{% static 'ads/js/admin-footer-fix.js' %}"></script>

<script>
    // Pass data to JavaScript
    const chartData = {{ chart_data_json|safe }};
    const deviceData = {{ device_data_json|safe }};
    const locationData = {{ location_data_json|safe }};
    const browserData = {{ browser_data_json|safe }};
    const campaignData = {{ campaign_data_json|safe }};
</script>
{% endblock %}
