{% extends 'base.html' %}
{% load static %}

{% block title %}Create Webhook{% endblock %}

{% block extra_css %}
<style>
.webhook-form-container {
    padding: 2rem 0;
}

.form-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.form-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    text-align: center;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-check {
    margin-bottom: 1rem;
}

.form-check-input {
    margin-top: 0.25rem;
}

.form-check-label {
    font-weight: 500;
}

.help-text {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.btn-create {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-create:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    color: white;
}

.btn-cancel {
    background: #6c757d;
    border: none;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    margin-right: 1rem;
}

.webhook-info {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.info-section {
    margin-bottom: 1.5rem;
}

.info-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.payload-example {
    background: #2d3748;
    color: #e2e8f0;
    border-radius: 8px;
    padding: 1rem;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    overflow-x: auto;
}

.integration-logos {
    display: flex;
    gap: 1rem;
    align-items: center;
    margin-top: 1rem;
}

.integration-logo {
    width: 40px;
    height: 40px;
    background: white;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    font-weight: bold;
    font-size: 0.8rem;
}

.error-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.error-list li {
    background: #f8d7da;
    color: #721c24;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    margin-bottom: 0.5rem;
}
</style>
{% endblock %}

{% block content %}
<div class="container webhook-form-container">
    <!-- Header -->
    <div class="form-header">
        <h1 class="mb-3">
            <i class="fas fa-plus-circle me-3"></i>Create New Webhook
        </h1>
        <p class="mb-0">Connect your QR scans to external services and automation platforms</p>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Form -->
            <div class="form-card">
                <form method="post" id="webhookForm">
                    {% csrf_token %}
                    
                    <!-- QR Code Selection -->
                    <div class="form-group">
                        <label for="{{ form.qr_code.id_for_label }}" class="form-label">
                            {{ form.qr_code.label }}
                        </label>
                        {{ form.qr_code }}
                        {% if form.qr_code.help_text %}
                            <div class="help-text">{{ form.qr_code.help_text }}</div>
                        {% endif %}
                        {% if form.qr_code.errors %}
                            <ul class="error-list">
                                {% for error in form.qr_code.errors %}
                                    <li>{{ error }}</li>
                                {% endfor %}
                            </ul>
                        {% endif %}
                    </div>

                    <!-- Webhook URL -->
                    <div class="form-group">
                        <label for="{{ form.url.id_for_label }}" class="form-label">
                            {{ form.url.label }}
                        </label>
                        {{ form.url }}
                        {% if form.url.help_text %}
                            <div class="help-text">{{ form.url.help_text }}</div>
                        {% endif %}
                        {% if form.url.errors %}
                            <ul class="error-list">
                                {% for error in form.url.errors %}
                                    <li>{{ error }}</li>
                                {% endfor %}
                            </ul>
                        {% endif %}
                    </div>

                    <!-- Trigger Options -->
                    <div class="form-group">
                        <label class="form-label">Trigger Events</label>
                        
                        <div class="form-check">
                            {{ form.trigger_on_scan }}
                            <label class="form-check-label" for="{{ form.trigger_on_scan.id_for_label }}">
                                {{ form.trigger_on_scan.label }}
                            </label>
                            {% if form.trigger_on_scan.help_text %}
                                <div class="help-text">{{ form.trigger_on_scan.help_text }}</div>
                            {% endif %}
                        </div>

                        <div class="form-check">
                            {{ form.trigger_on_alert }}
                            <label class="form-check-label" for="{{ form.trigger_on_alert.id_for_label }}">
                                {{ form.trigger_on_alert.label }}
                            </label>
                            {% if form.trigger_on_alert.help_text %}
                                <div class="help-text">{{ form.trigger_on_alert.help_text }}</div>
                            {% endif %}
                        </div>

                        {% if form.trigger_on_scan.errors or form.trigger_on_alert.errors %}
                            <ul class="error-list">
                                {% for error in form.trigger_on_scan.errors %}
                                    <li>{{ error }}</li>
                                {% endfor %}
                                {% for error in form.trigger_on_alert.errors %}
                                    <li>{{ error }}</li>
                                {% endfor %}
                            </ul>
                        {% endif %}
                    </div>

                    <!-- Active Status -->
                    <div class="form-group">
                        <div class="form-check">
                            {{ form.active }}
                            <label class="form-check-label" for="{{ form.active.id_for_label }}">
                                {{ form.active.label }}
                            </label>
                            {% if form.active.help_text %}
                                <div class="help-text">{{ form.active.help_text }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Secret Key -->
                    <div class="form-group">
                        <label for="{{ form.secret_key.id_for_label }}" class="form-label">
                            {{ form.secret_key.label }}
                        </label>
                        {{ form.secret_key }}
                        {% if form.secret_key.help_text %}
                            <div class="help-text">{{ form.secret_key.help_text }}</div>
                        {% endif %}
                        {% if form.secret_key.errors %}
                            <ul class="error-list">
                                {% for error in form.secret_key.errors %}
                                    <li>{{ error }}</li>
                                {% endfor %}
                            </ul>
                        {% endif %}
                    </div>

                    <!-- Form Errors -->
                    {% if form.non_field_errors %}
                        <ul class="error-list">
                            {% for error in form.non_field_errors %}
                                <li>{{ error }}</li>
                            {% endfor %}
                        </ul>
                    {% endif %}

                    <!-- Submit Buttons -->
                    <div class="form-group">
                        <a href="{% url 'webhook_dashboard' %}" class="btn btn-cancel">Cancel</a>
                        <button type="submit" class="btn btn-create">
                            <i class="fas fa-save me-2"></i>Create Webhook
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Information Panel -->
            <div class="webhook-info">
                <div class="info-section">
                    <div class="info-title">
                        <i class="fas fa-info-circle me-2"></i>How Webhooks Work
                    </div>
                    <p>When your QR codes are scanned, we'll send real-time data to your webhook URL. This enables instant integration with your existing systems.</p>
                </div>

                <div class="info-section">
                    <div class="info-title">
                        <i class="fas fa-code me-2"></i>Payload Example
                    </div>
                    <div class="payload-example">
{
  "qr_code": "abc123",
  "qr_name": "Business Card",
  "ip": "*************",
  "location": "New York, US",
  "org": "Google LLC",
  "device_type": "Mobile",
  "browser": "Chrome",
  "timestamp": "2025-05-29T10:30:00Z",
  "user_email": "<EMAIL>"
}
                    </div>
                </div>

                <div class="info-section">
                    <div class="info-title">
                        <i class="fas fa-plug me-2"></i>Popular Integrations
                    </div>
                    <p>Connect with these popular services:</p>
                    <div class="integration-logos">
                        <div class="integration-logo" title="Zapier">Z</div>
                        <div class="integration-logo" title="Salesforce">SF</div>
                        <div class="integration-logo" title="HubSpot">HS</div>
                        <div class="integration-logo" title="Slack">SL</div>
                        <div class="integration-logo" title="Google Sheets">GS</div>
                    </div>
                </div>

                <div class="info-section">
                    <div class="info-title">
                        <i class="fas fa-shield-alt me-2"></i>Security
                    </div>
                    <p>Use the optional secret key for webhook verification. We'll send it in the <code>X-Webhook-Secret</code> header.</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.getElementById('webhookForm').addEventListener('submit', function(e) {
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating...';
    submitBtn.disabled = true;
});

// Auto-generate secret key
function generateSecretKey() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < 32; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    document.getElementById('{{ form.secret_key.id_for_label }}').value = result;
}

// Add generate button next to secret key field
document.addEventListener('DOMContentLoaded', function() {
    const secretKeyField = document.getElementById('{{ form.secret_key.id_for_label }}');
    if (secretKeyField) {
        const generateBtn = document.createElement('button');
        generateBtn.type = 'button';
        generateBtn.className = 'btn btn-outline-secondary btn-sm mt-2';
        generateBtn.innerHTML = '<i class="fas fa-key me-1"></i>Generate';
        generateBtn.onclick = generateSecretKey;
        secretKeyField.parentNode.appendChild(generateBtn);
    }
});
</script>
{% endblock %}
