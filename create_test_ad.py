import os
import django
import sys

# Set up Django environment
sys.path.append('.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'qrcode_generator.settings')
django.setup()

# Import models
from django.contrib.auth.models import User
from ads.models import Ad, AdType, AdLocation
from django.utils import timezone
from datetime import timed<PERSON><PERSON>

def create_test_ad():
    """Create a test ad for demonstration purposes"""
    
    # Check if we already have test ads
    if Ad.objects.filter(title__startswith='Test Ad').exists():
        print("Test ads already exist. Skipping creation.")
        return
    
    # Get or create a test user
    user, created = User.objects.get_or_create(
        username='testuser',
        defaults={
            'email': '<EMAIL>',
            'is_active': True
        }
    )
    
    if created:
        user.set_password('testpassword')
        user.save()
        print(f"Created test user: {user.username}")
    else:
        print(f"Using existing user: {user.username}")
    
    # Get or create ad types
    banner_type, created = AdType.objects.get_or_create(
        name='Banner',
        defaults={
            'description': 'Standard banner advertisement',
            'base_price': 100.00,
            'is_active': True,
            'visibility': 'public'
        }
    )
    
    # Get or create ad locations
    locations = {
        'header': 'Top of page header position',
        'sidebar': 'Right sidebar position',
        'content': 'Within content position',
        'footer': 'Footer position'
    }
    
    location_objects = {}
    for key, description in locations.items():
        location, created = AdLocation.objects.get_or_create(
            name=key.capitalize(),
            defaults={
                'description': description,
                'price_multiplier': 1.0,
                'is_active': True,
                'visibility': 'public'
            }
        )
        location_objects[key] = location
        if created:
            print(f"Created location: {location.name}")
        else:
            print(f"Using existing location: {location.name}")
    
    # Create test ads for each location
    for location_key, location in location_objects.items():
        # Create a test ad
        ad = Ad.objects.create(
            user=user,
            ad_type=banner_type,
            ad_location=location,
            title=f'Test Ad - {location.name}',
            content=f'This is a test advertisement for the {location.name} position.',
            cta_link='https://example.com',
            target_location='Kenya',
            target_audience='All',
            start_date=timezone.now(),
            end_date=timezone.now() + timedelta(days=30),
            status='active',
            base_pricing=100.00,
            final_pricing=100.00 * location.price_multiplier
        )
        print(f"Created test ad: {ad.title}")

if __name__ == '__main__':
    create_test_ad()
    print("Test ad creation complete!")
