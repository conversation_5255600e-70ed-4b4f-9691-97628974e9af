{% extends 'base.html' %}
{% load static %}

{% block title %}Enterprise Analytics Dashboard{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
<style>
    /* Modern Enterprise Dashboard Styles */
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);

        --glass-bg: rgba(255, 255, 255, 0.1);
        --glass-border: rgba(255, 255, 255, 0.2);
        --text-primary: #2d3748;
        --text-secondary: #718096;
        --shadow-soft: 0 10px 25px rgba(0, 0, 0, 0.1);
        --shadow-medium: 0 20px 40px rgba(0, 0, 0, 0.15);
        --shadow-strong: 0 30px 60px rgba(0, 0, 0, 0.2);

        --border-radius: 20px;
        --border-radius-sm: 12px;
        --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .enterprise-dashboard {
        background: var(--primary-gradient);
        min-height: 100vh;
        padding: 2rem 0;
        position: relative;
        overflow-x: hidden;
    }

    /* Animated Background Elements */
    .enterprise-dashboard::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
            radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
        animation: backgroundFloat 20s ease-in-out infinite;
        pointer-events: none;
    }

    @keyframes backgroundFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        33% { transform: translateY(-20px) rotate(1deg); }
        66% { transform: translateY(10px) rotate(-1deg); }
    }

    /* Glass Morphism Cards */
    .dashboard-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow-medium);
        border: 1px solid var(--glass-border);
        transition: var(--transition);
        position: relative;
        overflow: hidden;
    }

    .dashboard-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--primary-gradient);
        border-radius: var(--border-radius) var(--border-radius) 0 0;
    }

    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-strong);
    }

    /* Modern Metric Cards */
    .metric-card {
        background: var(--primary-gradient);
        color: white;
        border-radius: var(--border-radius-sm);
        padding: 2rem 1.5rem;
        text-align: center;
        margin-bottom: 1.5rem;
        position: relative;
        overflow: hidden;
        transition: var(--transition);
        box-shadow: var(--shadow-soft);
    }

    .metric-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
        pointer-events: none;
    }

    .metric-card:hover {
        transform: translateY(-3px) scale(1.02);
        box-shadow: var(--shadow-medium);
    }

    .metric-value {
        font-size: 2.8rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        animation: countUp 1s ease-out;
    }

    @keyframes countUp {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .metric-label {
        font-size: 0.95rem;
        opacity: 0.9;
        font-weight: 500;
        letter-spacing: 0.5px;
    }

    /* Enhanced Header */
    .enterprise-header {
        text-align: center;
        color: white;
        margin-bottom: 4rem;
        position: relative;
    }

    .enterprise-header h1 {
        font-size: 3.5rem;
        font-weight: 800;
        margin-bottom: 1rem;
        text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        animation: slideInDown 0.8s ease-out;
    }

    .enterprise-header p {
        font-size: 1.3rem;
        opacity: 0.95;
        font-weight: 400;
        animation: slideInUp 0.8s ease-out 0.2s both;
    }

    @keyframes slideInDown {
        from { opacity: 0; transform: translateY(-30px); }
        to { opacity: 1; transform: translateY(0); }
    }

    @keyframes slideInUp {
        from { opacity: 0; transform: translateY(30px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* Modern Filter Controls */
    .filter-controls {
        background: var(--glass-bg);
        backdrop-filter: blur(10px);
        border-radius: var(--border-radius-sm);
        padding: 1.5rem;
        margin-bottom: 3rem;
        border: 1px solid var(--glass-border);
        animation: fadeInScale 0.6s ease-out 0.4s both;
    }

    @keyframes fadeInScale {
        from { opacity: 0; transform: scale(0.95); }
        to { opacity: 1; transform: scale(1); }
    }

    /* Chart Containers */
    .chart-container {
        height: 350px;
        margin: 1.5rem 0;
        position: relative;
    }

    .heatmap-container {
        height: 400px;
        border-radius: var(--border-radius-sm);
        overflow: hidden;
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    /* Enhanced Lists */
    .org-list {
        max-height: 350px;
        overflow-y: auto;
        padding-right: 0.5rem;
    }

    .org-list::-webkit-scrollbar {
        width: 6px;
    }

    .org-list::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.05);
        border-radius: 3px;
    }

    .org-list::-webkit-scrollbar-thumb {
        background: var(--primary-gradient);
        border-radius: 3px;
    }

    .org-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.08);
        transition: var(--transition);
    }

    .org-item:hover {
        background: rgba(102, 126, 234, 0.05);
        padding-left: 0.5rem;
        border-radius: 8px;
    }

    /* Enhanced Privacy Alert */
    .privacy-alert {
        background: var(--danger-gradient);
        color: white;
        border-radius: var(--border-radius-sm);
        padding: 1.5rem;
        margin: 1.5rem 0;
        box-shadow: var(--shadow-soft);
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0%, 100% { box-shadow: var(--shadow-soft); }
        50% { box-shadow: 0 10px 25px rgba(250, 112, 154, 0.3); }
    }

    /* Enhanced Table */
    .table-responsive {
        border-radius: var(--border-radius-sm);
        overflow: hidden;
        box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
    }

    .table {
        margin-bottom: 0;
    }

    .table thead th {
        background: var(--primary-gradient);
        color: white;
        border: none;
        font-weight: 600;
        letter-spacing: 0.5px;
    }

    .table tbody tr {
        transition: var(--transition);
    }

    .table tbody tr:hover {
        background: rgba(102, 126, 234, 0.05);
        transform: scale(1.01);
    }

    /* Responsive Animations */
    .dashboard-card,
    .metric-card {
        animation: fadeInUp 0.6s ease-out both;
    }

    .dashboard-card:nth-child(1) { animation-delay: 0.1s; }
    .dashboard-card:nth-child(2) { animation-delay: 0.2s; }
    .dashboard-card:nth-child(3) { animation-delay: 0.3s; }
    .dashboard-card:nth-child(4) { animation-delay: 0.4s; }

    .metric-card:nth-child(1) { animation-delay: 0.5s; }
    .metric-card:nth-child(2) { animation-delay: 0.6s; }
    .metric-card:nth-child(3) { animation-delay: 0.7s; }
    .metric-card:nth-child(4) { animation-delay: 0.8s; }

    @keyframes fadeInUp {
        from { opacity: 0; transform: translateY(30px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* Loading States */
    .loading-skeleton {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
    }

    @keyframes loading {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
    }

    /* Mobile Optimizations */
    @media (max-width: 768px) {
        .enterprise-header h1 {
            font-size: 2.5rem;
        }

        .metric-value {
            font-size: 2.2rem;
        }

        .dashboard-card {
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .chart-container {
            height: 250px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="enterprise-dashboard">
    <div class="container">
        <!-- Enterprise Header -->
        <div class="enterprise-header">
            <h1><i class="fas fa-chart-line me-3"></i>Enterprise Analytics</h1>
            <p>Advanced QR code analytics with geo-intelligence and security insights</p>
        </div>

        <!-- Filter Controls -->
        <div class="filter-controls">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <label class="text-white me-3">Time Range:</label>
                    <select id="dateRange" class="form-select d-inline-block w-auto">
                        <option value="7" {% if days == 7 %}selected{% endif %}>Last 7 days</option>
                        <option value="30" {% if days == 30 %}selected{% endif %}>Last 30 days</option>
                        <option value="90" {% if days == 90 %}selected{% endif %}>Last 90 days</option>
                        <option value="365" {% if days == 365 %}selected{% endif %}>Last year</option>
                    </select>
                </div>
                <div class="col-md-6 text-end">
                    <button class="btn btn-light" onclick="exportData()">
                        <i class="fas fa-download me-2"></i>Export Data
                    </button>
                </div>
            </div>
        </div>

        <!-- Key Metrics -->
        <div class="row">
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value">{{ total_qr_codes }}</div>
                    <div class="metric-label">Total QR Codes</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value">{{ total_scans }}</div>
                    <div class="metric-label">Total Scans</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value">{{ unique_scanners }}</div>
                    <div class="metric-label">Unique Scanners</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value">{{ avg_scans_per_user|floatformat:1 }}</div>
                    <div class="metric-label">Avg Scans/User</div>
                </div>
            </div>
        </div>

        <!-- Charts Row 1 -->
        <div class="row">
            <div class="col-md-8">
                <div class="dashboard-card">
                    <h4><i class="fas fa-chart-area me-2"></i>Scan Trends</h4>
                    <canvas id="scanTrendsChart" class="chart-container"></canvas>
                </div>
            </div>
            <div class="col-md-4">
                <div class="dashboard-card">
                    <h4><i class="fas fa-mobile-alt me-2"></i>Scanner Types</h4>
                    <canvas id="scannerTypeChart" class="chart-container"></canvas>
                </div>
            </div>
        </div>

        <!-- Geographic Analytics -->
        <div class="row">
            <div class="col-md-6">
                <div class="dashboard-card">
                    <h4><i class="fas fa-globe me-2"></i>Geographic Distribution</h4>
                    <div id="heatmap" class="heatmap-container"></div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="dashboard-card">
                    <h4><i class="fas fa-flag me-2"></i>Top Countries</h4>
                    <div class="org-list">
                        {% for country in geo_distribution %}
                        <div class="org-item">
                            <span><i class="fas fa-map-marker-alt me-2"></i>{{ country.country }}</span>
                            <span class="badge bg-primary">{{ country.count }}</span>
                        </div>
                        {% empty %}
                        <p class="text-muted">No geographic data available</p>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Enterprise Features -->
        <div class="row">
            <div class="col-md-6">
                <div class="dashboard-card">
                    <h4><i class="fas fa-building me-2"></i>Organization Analysis</h4>
                    <div class="org-list">
                        {% for org in org_distribution %}
                        <div class="org-item">
                            <span><i class="fas fa-network-wired me-2"></i>{{ org.organization|truncatechars:30 }}</span>
                            <span class="badge bg-info">{{ org.count }}</span>
                        </div>
                        {% empty %}
                        <p class="text-muted">No organization data available</p>
                        {% endfor %}
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="dashboard-card">
                    <h4><i class="fas fa-shield-alt me-2"></i>Security Analysis</h4>
                    {% if privacy_analysis.vpn_scans > 0 or privacy_analysis.proxy_scans > 0 or privacy_analysis.tor_scans > 0 %}
                    <div class="privacy-alert">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Privacy Tools Detected</h6>
                        <p class="mb-0">Some scans came from privacy-enhanced connections</p>
                    </div>
                    {% endif %}

                    <div class="row text-center">
                        <div class="col-6">
                            <div class="metric-value text-warning">{{ privacy_analysis.vpn_scans }}</div>
                            <div class="metric-label text-muted">VPN Scans</div>
                        </div>
                        <div class="col-6">
                            <div class="metric-value text-danger">{{ privacy_analysis.proxy_scans }}</div>
                            <div class="metric-label text-muted">Proxy Scans</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Performing QR Codes -->
        <div class="row">
            <div class="col-12">
                <div class="dashboard-card">
                    <h4><i class="fas fa-trophy me-2"></i>Top Performing QR Codes</h4>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>QR Code</th>
                                    <th>Type</th>
                                    <th>Scans</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for qr in top_qr_codes %}
                                <tr>
                                    <td>
                                        <strong>{{ qr.name }}</strong>
                                        <br><small class="text-muted">{{ qr.data|truncatechars:50 }}</small>
                                    </td>
                                    <td><span class="badge bg-secondary">{{ qr.get_qr_type_display }}</span></td>
                                    <td><span class="badge bg-success">{{ qr.scan_count }}</span></td>
                                    <td>
                                        {% if qr.status == 'active' %}
                                        <span class="badge bg-success">Active</span>
                                        {% elif qr.status == 'expired' %}
                                        <span class="badge bg-warning">Expired</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ qr.get_status_display }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ qr.created_at|date:"M d, Y" }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center text-muted">No QR codes found</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
<script>
// Modern Enterprise Dashboard JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Smooth page loading
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.5s ease-in-out';

    setTimeout(() => {
        document.body.style.opacity = '1';
    }, 100);

    // Initialize all components with loading states
    initializeCharts();
    initializeHeatmap();
    initializeInteractions();

    // Animate metric values on load
    animateMetricValues();
});

// Smooth date range filter with loading state
document.getElementById('dateRange').addEventListener('change', function() {
    const days = this.value;

    // Show loading state
    showLoadingState();

    // Smooth transition to new data
    setTimeout(() => {
        window.location.href = `?days=${days}`;
    }, 300);
});

function showLoadingState() {
    const cards = document.querySelectorAll('.dashboard-card, .metric-card');
    cards.forEach(card => {
        card.style.opacity = '0.7';
        card.style.transform = 'scale(0.98)';
    });
}

function animateMetricValues() {
    const metricValues = document.querySelectorAll('.metric-value');
    metricValues.forEach((element, index) => {
        const finalValue = element.textContent;
        element.textContent = '0';

        setTimeout(() => {
            animateNumber(element, 0, parseFloat(finalValue) || 0, 1000);
        }, 500 + (index * 200));
    });
}

function animateNumber(element, start, end, duration) {
    const startTime = performance.now();
    const isFloat = end % 1 !== 0;

    function update(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // Easing function for smooth animation
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const current = start + (end - start) * easeOutQuart;

        element.textContent = isFloat ? current.toFixed(1) : Math.floor(current);

        if (progress < 1) {
            requestAnimationFrame(update);
        } else {
            element.textContent = isFloat ? end.toFixed(1) : end;
        }
    }

    requestAnimationFrame(update);
}

function initializeCharts() {
    try {
        // Enhanced Scan Trends Chart with error handling
        const scanTrendsCtx = document.getElementById('scanTrendsChart');
        if (scanTrendsCtx) {
            try {
                const dailyScans = {{ daily_scans|safe }};

                // Validate data
                if (!dailyScans || typeof dailyScans !== 'object') {
                    console.warn('Invalid daily scans data, using empty dataset');
                    const emptyData = {};
                    for (let i = 6; i >= 0; i--) {
                        const date = new Date();
                        date.setDate(date.getDate() - i);
                        emptyData[date.toISOString().split('T')[0]] = 0;
                    }
                    dailyScans = emptyData;
                }

                const dates = Object.keys(dailyScans).sort();
                const scanCounts = dates.map(date => dailyScans[date] || 0);

                new Chart(scanTrendsCtx, {
                    type: 'line',
                    data: {
                        labels: dates.map(date => {
                            try {
                                return new Date(date).toLocaleDateString();
                            } catch (e) {
                                return date;
                            }
                        }),
                        datasets: [{
                            label: 'Daily Scans',
                            data: scanCounts,
                            borderColor: '#667eea',
                            backgroundColor: 'rgba(102, 126, 234, 0.1)',
                            borderWidth: 3,
                            tension: 0.4,
                            fill: true,
                            pointBackgroundColor: '#667eea',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2,
                            pointRadius: 5,
                            pointHoverRadius: 8
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        animation: {
                            duration: 1500,
                            easing: 'easeInOutQuart'
                        },
                        plugins: {
                            legend: {
                                display: false
                            },
                            tooltip: {
                                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                titleColor: '#ffffff',
                                bodyColor: '#ffffff',
                                borderColor: '#667eea',
                                borderWidth: 1,
                                cornerRadius: 8
                            }
                        },
                        scales: {
                            x: {
                                grid: {
                                    color: 'rgba(0, 0, 0, 0.05)'
                                },
                                ticks: {
                                    color: '#718096'
                                }
                            },
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: 'rgba(0, 0, 0, 0.05)'
                                },
                                ticks: {
                                    color: '#718096'
                                }
                            }
                        }
                    }
                });
            } catch (error) {
                console.error('Error initializing scan trends chart:', error);
                scanTrendsCtx.parentElement.innerHTML = '<div class="text-center text-muted p-4"><i class="fas fa-chart-line fa-2x mb-2"></i><br>Chart data loading...</div>';
            }
        }

        // Enhanced Scanner Type Chart with error handling
        const scannerTypeCtx = document.getElementById('scannerTypeChart');
        if (scannerTypeCtx) {
            try {
                const scannerData = {{ scanner_distribution|safe }};

                // Validate scanner data
                if (!Array.isArray(scannerData) || scannerData.length === 0) {
                    console.warn('No scanner data available, showing placeholder');
                    scannerTypeCtx.parentElement.innerHTML = '<div class="text-center text-muted p-4"><i class="fas fa-mobile-alt fa-2x mb-2"></i><br>No scanner data available</div>';
                    return;
                }

                // Ensure all data items have required properties
                const validScannerData = scannerData.filter(item =>
                    item &&
                    typeof item === 'object' &&
                    item.scanner_type &&
                    typeof item.count === 'number'
                );

                if (validScannerData.length === 0) {
                    scannerTypeCtx.parentElement.innerHTML = '<div class="text-center text-muted p-4"><i class="fas fa-mobile-alt fa-2x mb-2"></i><br>No valid scanner data</div>';
                    return;
                }

                new Chart(scannerTypeCtx, {
                    type: 'doughnut',
                    data: {
                        labels: validScannerData.map(item => {
                            try {
                                return item.scanner_type.replace('_', ' ').toUpperCase();
                            } catch (e) {
                                return 'Unknown';
                            }
                        }),
                        datasets: [{
                            data: validScannerData.map(item => item.count),
                            backgroundColor: [
                                '#667eea',
                                '#764ba2',
                                '#f093fb',
                                '#f5576c',
                                '#4facfe',
                                '#43e97b',
                                '#38f9d7'
                            ],
                            borderWidth: 0,
                            hoverBorderWidth: 3,
                            hoverBorderColor: '#ffffff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        animation: {
                            duration: 1500,
                            easing: 'easeInOutQuart'
                        },
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    padding: 20,
                                    usePointStyle: true,
                                    color: '#718096'
                                }
                            },
                            tooltip: {
                                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                titleColor: '#ffffff',
                                bodyColor: '#ffffff',
                                cornerRadius: 8
                            }
                        }
                    }
                });
            } catch (error) {
                console.error('Error initializing scanner type chart:', error);
                scannerTypeCtx.parentElement.innerHTML = '<div class="text-center text-muted p-4"><i class="fas fa-mobile-alt fa-2x mb-2"></i><br>Chart error occurred</div>';
            }
        }
    } catch (error) {
        console.error('Error in chart initialization:', error);
    }
}

function initializeHeatmap() {
    const heatmapElement = document.getElementById('heatmap');
    if (heatmapElement) {
        try {
            // Initialize map with smooth animations
            const map = L.map('heatmap', {
                zoomControl: true,
                scrollWheelZoom: true,
                doubleClickZoom: true,
                fadeAnimation: true,
                zoomAnimation: true
            }).setView([20, 0], 2);

            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors',
                opacity: 0.8
            }).addTo(map);

            // Load heatmap data with smooth markers and error handling
            fetch('/enterprise/heatmap-data/?days={{ days }}')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (!data || !Array.isArray(data.heatmap_data)) {
                        console.warn('Invalid heatmap data received');
                        heatmapElement.innerHTML = '<div class="text-center text-muted p-4"><i class="fas fa-globe fa-2x mb-2"></i><br>No geographic data available</div>';
                        return;
                    }

                    if (data.heatmap_data.length === 0) {
                        heatmapElement.innerHTML = '<div class="text-center text-muted p-4"><i class="fas fa-globe fa-2x mb-2"></i><br>No scan locations to display</div>';
                        return;
                    }

                    // Add markers with validation
                    data.heatmap_data.forEach((point, index) => {
                        // Validate point data
                        if (!point || typeof point.lat !== 'number' || typeof point.lng !== 'number') {
                            console.warn('Invalid point data:', point);
                            return;
                        }

                        setTimeout(() => {
                            try {
                                const marker = L.circleMarker([point.lat, point.lng], {
                                    radius: 6,
                                    fillColor: '#667eea',
                                    color: '#ffffff',
                                    weight: 2,
                                    opacity: 1,
                                    fillOpacity: 0.8
                                }).bindPopup(`
                                    <div style="text-align: center; padding: 5px;">
                                        <strong>${point.city || 'Unknown'}, ${point.country || 'Unknown'}</strong>
                                    </div>
                                `);

                                marker.addTo(map);

                                // Animate marker appearance
                                marker.setStyle({ radius: 0 });
                                setTimeout(() => {
                                    marker.setStyle({ radius: 6 });
                                }, 50);
                            } catch (markerError) {
                                console.error('Error adding marker:', markerError);
                            }
                        }, index * 50); // Reduced delay for smoother loading
                    });
                })
                .catch(error => {
                    console.error('Error loading heatmap data:', error);
                    heatmapElement.innerHTML = '<div class="text-center text-muted p-4"><i class="fas fa-exclamation-triangle fa-2x mb-2"></i><br>Error loading map data</div>';
                });
        } catch (error) {
            console.error('Error initializing heatmap:', error);
            heatmapElement.innerHTML = '<div class="text-center text-muted p-4"><i class="fas fa-exclamation-triangle fa-2x mb-2"></i><br>Map initialization failed</div>';
        }
    }
}

function initializeInteractions() {
    // Smooth hover effects for cards
    const cards = document.querySelectorAll('.dashboard-card, .metric-card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // Smooth scrolling for org lists
    const orgLists = document.querySelectorAll('.org-list');
    orgLists.forEach(list => {
        list.style.scrollBehavior = 'smooth';
    });
}

// Enhanced export function with loading state
function exportData() {
    const button = event.target;
    const originalText = button.innerHTML;

    // Show loading state
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Exporting...';
    button.disabled = true;

    // Simulate export process
    setTimeout(() => {
        button.innerHTML = '<i class="fas fa-check me-2"></i>Export Complete!';

        setTimeout(() => {
            button.innerHTML = originalText;
            button.disabled = false;
        }, 2000);
    }, 1500);
}

// Smooth page transitions
window.addEventListener('beforeunload', function() {
    document.body.style.opacity = '0';
});

// Performance optimization
window.addEventListener('load', function() {
    // Lazy load non-critical elements
    const lazyElements = document.querySelectorAll('[data-lazy]');
    lazyElements.forEach(element => {
        setTimeout(() => {
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }, 100);
    });
});
</script>
{% endblock %}
