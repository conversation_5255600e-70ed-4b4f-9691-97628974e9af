from django.test import TestCase
from rest_framework.test import APIClient
from django.urls import reverse
from qrcode_app.models import QRCode, UserProfile, APIKey
from django.contrib.auth.models import User

# Create your tests here.
class APITestCase(TestCase):
    def setUp(self):
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        
        # Create a user profile
        self.profile = UserProfile.objects.create(
            user=self.user,
            role='admin'
        )
        
        # Create an API key
        self.api_key = APIKey.objects.create(
            user=self.user,
            name='Test API Key',
            key='test_api_key_12345'
        )
        
        # Create a test QR code
        self.qr_code = QRCode.objects.create(
            user=self.user,
            name='Test QR Code',
            data='https://example.com',
            qr_type='url'
        )
        
        # Initialize the API client
        self.client = APIClient()
    
    def test_api_key_authentication(self):
        """Test API key authentication"""
        # Test with valid API key
        self.client.credentials(HTTP_X_API_KEY=self.api_key.key)
        response = self.client.get(reverse('api:qrcode-list'))
        self.assertEqual(response.status_code, 200)
        
        # Test with invalid API key
        self.client.credentials(HTTP_X_API_KEY='invalid_key')
        response = self.client.get(reverse('api:qrcode-list'))
        self.assertEqual(response.status_code, 401)
    
    def test_qrcode_list(self):
        """Test QR code list endpoint"""
        self.client.credentials(HTTP_X_API_KEY=self.api_key.key)
        response = self.client.get(reverse('api:qrcode-list'))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['name'], 'Test QR Code')
    
    def test_qrcode_create(self):
        """Test QR code creation endpoint"""
        self.client.credentials(HTTP_X_API_KEY=self.api_key.key)
        data = {
            'name': 'New QR Code',
            'data': 'https://newexample.com',
            'qr_type': 'url'
        }
        response = self.client.post(reverse('api:qrcode-list'), data)
        self.assertEqual(response.status_code, 201)
        self.assertEqual(QRCode.objects.count(), 2)
        self.assertEqual(QRCode.objects.last().name, 'New QR Code')
