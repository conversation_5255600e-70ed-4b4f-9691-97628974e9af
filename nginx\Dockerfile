FROM nginx:1.25-alpine

# Remove default Nginx configuration
RUN rm /etc/nginx/conf.d/default.conf

# Copy custom Nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Create directories for Let's Encrypt certificates
RUN mkdir -p /etc/letsencrypt/live/example.com
RUN mkdir -p /var/www/certbot
RUN mkdir -p /var/www/html
RUN mkdir -p /var/www/static
RUN mkdir -p /var/www/media

# Expose ports
EXPOSE 80 443

# Start Nginx
CMD ["nginx", "-g", "daemon off;"]
