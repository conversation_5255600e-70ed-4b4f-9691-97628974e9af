/**
 * Select2 Campaign Dropdown CSS
 * Custom styles for the campaign dropdown using Select2
 */

/* Select2 Container */
.select2-container {
    box-sizing: border-box;
    display: inline-block;
    margin: 0;
    position: relative;
    vertical-align: middle;
    width: 100% !important;
}

/* Select2 Selection */
.select2-container--default .select2-selection--single {
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: 4px;
    height: 38px;
    padding: 0.375rem 0.75rem;
    padding-right: 2rem;
    display: flex;
    align-items: center;
}

.select2-container--default .select2-selection--single:focus {
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Select2 Arrow */
.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 36px;
    position: absolute;
    top: 1px;
    right: 1px;
    width: 20px;
}

.select2-container--default .select2-selection--single .select2-selection__arrow b {
    border-color: #6c757d transparent transparent transparent;
    border-style: solid;
    border-width: 5px 4px 0 4px;
    height: 0;
    left: 50%;
    margin-left: -4px;
    margin-top: -2px;
    position: absolute;
    top: 50%;
    width: 0;
}

/* Select2 Dropdown */
.select2-container--default .select2-dropdown {
    background-color: white;
    border: 1px solid #ced4da;
    border-radius: 4px;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Select2 Search */
.select2-container--default .select2-search--dropdown .select2-search__field {
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 0.375rem 0.75rem;
}

.select2-container--default .select2-search--dropdown .select2-search__field:focus {
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Select2 Results */
.select2-container--default .select2-results__option {
    padding: 0.5rem 0.75rem;
    user-select: none;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #3f51b5;
    color: white;
}

.select2-container--default .select2-results__option[aria-selected=true] {
    background-color: #e9ecef;
    color: #212529;
}

/* Campaign Option Styles */
.campaign-option {
    display: flex;
    align-items: center;
    padding: 0.25rem 0;
}

.campaign-option__icon {
    margin-right: 0.5rem;
    color: #6c757d;
}

.campaign-option__name {
    font-weight: 500;
}

.campaign-option__details {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.campaign-option__status {
    margin-left: auto;
    font-size: 0.75rem;
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
}

.campaign-option__status--active {
    background-color: #d4edda;
    color: #155724;
}

.campaign-option__status--pending {
    background-color: #fff3cd;
    color: #856404;
}

.campaign-option__status--draft {
    background-color: #e2e3e5;
    color: #383d41;
}

/* Create New Campaign Option */
.create-new-campaign-option {
    border-top: 1px solid #e9ecef;
    margin-top: 0.5rem;
    padding-top: 0.5rem;
}

.create-new-campaign-option__icon {
    color: #28a745;
}

/* Responsive Adjustments */
@media (max-width: 576px) {
    .select2-container--default .select2-selection--single {
        height: 36px;
        padding: 0.25rem 0.5rem;
    }
    
    .select2-container--default .select2-results__option {
        padding: 0.375rem 0.5rem;
    }
}
