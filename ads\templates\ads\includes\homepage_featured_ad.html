{% load static %}

<!-- Homepage Featured Ad Display -->
{% if ads %}
    {% with ad=ads.0 %}
    <div class="homepage-featured-ad-container">
        <div class="homepage-featured-ad-badge">
            <i class="fas fa-star"></i>
            Featured
        </div>

        <div class="homepage-featured-ad-content" data-ad-id="{{ ad.id }}" data-ad-type="{{ ad.ad_type.name }}" data-ad-location="homepage-featured">
            {% if ad.media and ad.media.name %}
            <div class="homepage-featured-ad-media">
                <img src="/media/{{ ad.media }}" alt="{{ ad.title }}" class="homepage-featured-ad-image">
                <div class="homepage-featured-ad-overlay"></div>
            </div>
            {% endif %}

            <div class="homepage-featured-ad-text">
                <h2 class="homepage-featured-ad-title">{{ ad.title }}</h2>
                <p class="homepage-featured-ad-description">{{ ad.content|truncatechars:150 }}</p>

                {% if ad.cta_link %}
                <a href="{{ ad.cta_link }}" target="_blank" class="homepage-featured-ad-cta" rel="noopener" onclick="trackAdClick('{{ ad.id }}')">
                    <span>Discover More</span>
                    <i class="fas fa-arrow-right"></i>
                </a>
                {% else %}
                <div class="homepage-featured-ad-cta homepage-featured-ad-cta-disabled">
                    <span>No Link Available</span>
                    <i class="fas fa-link-slash"></i>
                </div>
                {% endif %}
            </div>
        </div>

        <div class="homepage-featured-ad-sponsor">Sponsored Content</div>
    </div>

    <!-- Track impression -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            trackAdImpression('{{ ad.id }}');
        });
    </script>
    {% endwith %}
{% endif %}

<style>
.homepage-featured-ad-container {
    position: relative;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    overflow: hidden;
    margin: 30px 0;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    min-height: 300px;
}

.homepage-featured-ad-badge {
    position: absolute;
    top: 20px;
    left: 20px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 6px;
    backdrop-filter: blur(10px);
    z-index: 2;
}

.homepage-featured-ad-content {
    display: flex;
    align-items: center;
    min-height: 300px;
    position: relative;
}

.homepage-featured-ad-media {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
}

.homepage-featured-ad-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.homepage-featured-ad-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
}

.homepage-featured-ad-text {
    position: relative;
    z-index: 1;
    color: white;
    padding: 40px;
    max-width: 600px;
}

.homepage-featured-ad-title {
    font-size: 32px;
    font-weight: 700;
    margin: 0 0 15px 0;
    line-height: 1.2;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.homepage-featured-ad-description {
    font-size: 18px;
    margin: 0 0 25px 0;
    opacity: 0.95;
    line-height: 1.6;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.homepage-featured-ad-cta {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 15px 25px;
    border-radius: 30px;
    text-decoration: none;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.homepage-featured-ad-cta:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.homepage-featured-ad-sponsor {
    position: absolute;
    bottom: 15px;
    right: 20px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    z-index: 2;
}

/* Mobile responsive */
@media (max-width: 768px) {
    .homepage-featured-ad-container {
        margin: 20px 0;
        min-height: 250px;
    }

    .homepage-featured-ad-content {
        min-height: 250px;
    }

    .homepage-featured-ad-text {
        padding: 30px 20px;
    }

    .homepage-featured-ad-title {
        font-size: 24px;
    }

    .homepage-featured-ad-description {
        font-size: 16px;
        margin-bottom: 20px;
    }

    .homepage-featured-ad-cta {
        padding: 12px 20px;
        font-size: 14px;
    }

    .homepage-featured-ad-badge {
        top: 15px;
        left: 15px;
        padding: 6px 12px;
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .homepage-featured-ad-title {
        font-size: 20px;
    }

    .homepage-featured-ad-description {
        font-size: 14px;
    }

    .homepage-featured-ad-text {
        padding: 25px 15px;
    }
}
</style>
