{% extends 'admin/base_site.html' %}
{% load static %}

{% block title %}AI Provider Status{% endblock %}

{% block extrastyle %}
<style>
    .status-card {
        margin-bottom: 20px;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .status-card .card-header {
        padding: 15px;
        font-weight: bold;
    }
    
    .status-card .card-body {
        padding: 20px;
    }
    
    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
    }
    
    .status-indicator.online {
        background-color: #28a745;
    }
    
    .status-indicator.offline {
        background-color: #dc3545;
    }
    
    .status-indicator.unknown {
        background-color: #ffc107;
    }
    
    .status-details {
        margin-top: 15px;
    }
    
    .status-details dt {
        font-weight: 500;
    }
    
    .status-details dd {
        margin-bottom: 10px;
    }
    
    .refresh-button {
        margin-bottom: 20px;
    }
    
    .best-provider {
        font-weight: bold;
        padding: 5px 10px;
        border-radius: 4px;
        display: inline-block;
        margin-bottom: 20px;
    }
    
    .best-provider.online {
        background-color: #d4edda;
        color: #155724;
    }
    
    .best-provider.offline {
        background-color: #f8d7da;
        color: #721c24;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1>AI Provider Status</h1>
    
    <div class="refresh-button">
        <a href="?refresh=true" class="btn btn-primary">
            <i class="fas fa-sync-alt"></i> Refresh Status
        </a>
    </div>
    
    <div class="best-provider {% if best_ai_provider %}online{% else %}offline{% endif %}">
        <i class="fas {% if best_ai_provider %}fa-check-circle{% else %}fa-exclamation-triangle{% endif %} me-2"></i>
        Best Available Provider: {% if best_ai_provider %}{{ best_ai_provider|title }}{% else %}None (All Offline){% endif %}
    </div>
    
    <div class="row">
        {% for provider, status in ai_provider_status.items %}
        <div class="col-md-4">
            <div class="status-card">
                <div class="card-header {% if status.available %}bg-success text-white{% else %}bg-danger text-white{% endif %}">
                    <span class="status-indicator {% if status.available %}online{% else %}offline{% endif %}"></span>
                    {{ provider|title }} API
                </div>
                <div class="card-body">
                    <div class="status-summary">
                        <h4>
                            {% if status.available %}
                            <span class="badge bg-success">Online</span>
                            {% else %}
                            <span class="badge bg-danger">Offline</span>
                            {% endif %}
                        </h4>
                    </div>
                    
                    <div class="status-details">
                        <dl>
                            <dt>Response Time</dt>
                            <dd>{{ status.response_time|floatformat:2 }} seconds</dd>
                            
                            <dt>Last Check</dt>
                            <dd>{{ status.last_check|date:"Y-m-d H:i:s" }}</dd>
                            
                            {% if status.error %}
                            <dt>Error</dt>
                            <dd class="text-danger">{{ status.error }}</dd>
                            {% endif %}
                        </dl>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% endblock %}
