.api-key-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.api-key-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

.api-key-value {
    font-family: monospace;
    background-color: #f8f9fa;
    padding: 0.5rem;
    border-radius: 0.25rem;
    word-break: break-all;
}

.copy-btn {
    cursor: pointer;
}

.copy-btn:hover {
    color: #0d6efd;
}

.api-key-status {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 5px;
}

.status-active {
    background-color: #28a745;
}

.status-inactive {
    background-color: #dc3545;
}
