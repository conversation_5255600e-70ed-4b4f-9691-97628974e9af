/**
 * Enterprise QR Code Generator - Performance Optimizer
 * 
 * This module provides performance optimizations for high-volume QR code generation
 * in enterprise environments, including caching, batch processing, and worker threads.
 */

// Cache for generated QR codes
const QR_CACHE = new Map();

// Cache size limits
const CACHE_SIZE_LIMIT = 100; // Maximum number of cached QR codes
const CACHE_EXPIRY_TIME = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

// Queue for batch processing
let processingQueue = [];
let isProcessing = false;
let batchSize = 10; // Default batch size
let processingDelay = 50; // Default delay between batches in milliseconds

// Worker pool for parallel processing
let workerPool = [];
let maxWorkers = 4; // Default maximum number of workers

/**
 * Initialize the performance optimizer
 * @param {Object} options - Configuration options
 */
function initPerformanceOptimizer(options = {}) {
    console.log('Initializing Performance Optimizer for Enterprise QR...');
    
    // Set configuration options
    if (options.cacheSize) CACHE_SIZE_LIMIT = options.cacheSize;
    if (options.cacheExpiry) CACHE_EXPIRY_TIME = options.cacheExpiry;
    if (options.batchSize) batchSize = options.batchSize;
    if (options.processingDelay) processingDelay = options.processingDelay;
    if (options.maxWorkers) maxWorkers = options.maxWorkers;
    
    // Initialize worker pool if Web Workers are supported
    if (typeof Worker !== 'undefined' && options.useWorkers !== false) {
        initWorkerPool();
    }
    
    // Set up periodic cache cleanup
    setInterval(cleanupCache, CACHE_EXPIRY_TIME / 2);
    
    // Add performance monitoring
    if (options.enableMonitoring !== false) {
        initPerformanceMonitoring();
    }
    
    console.log('Performance Optimizer initialized with:', {
        cacheSize: CACHE_SIZE_LIMIT,
        cacheExpiry: `${CACHE_EXPIRY_TIME / (60 * 60 * 1000)} hours`,
        batchSize,
        processingDelay: `${processingDelay}ms`,
        maxWorkers,
        workersSupported: typeof Worker !== 'undefined',
        monitoringEnabled: options.enableMonitoring !== false
    });
}

/**
 * Initialize worker pool for parallel processing
 */
function initWorkerPool() {
    // Clear existing workers
    workerPool.forEach(worker => worker.terminate());
    workerPool = [];
    
    // Create new workers
    for (let i = 0; i < maxWorkers; i++) {
        try {
            const worker = new Worker('js/qr-worker.js');
            worker.id = i;
            worker.busy = false;
            
            worker.onmessage = function(e) {
                const { id, qrData, result, error } = e.data;
                
                // Mark worker as available
                worker.busy = false;
                
                // Process result
                if (error) {
                    console.error(`Worker ${worker.id} error:`, error);
                    processNextInQueue();
                    return;
                }
                
                // Cache the result
                cacheQRCode(qrData, result);
                
                // Notify listeners
                const callbacks = processingQueue.filter(item => item.id === id);
                callbacks.forEach(item => {
                    if (item.onComplete) item.onComplete(result);
                });
                
                // Remove from queue
                processingQueue = processingQueue.filter(item => item.id !== id);
                
                // Process next item
                processNextInQueue();
            };
            
            worker.onerror = function(error) {
                console.error(`Worker ${worker.id} error:`, error);
                worker.busy = false;
                processNextInQueue();
            };
            
            workerPool.push(worker);
        } catch (error) {
            console.error('Error creating worker:', error);
        }
    }
    
    console.log(`Worker pool initialized with ${workerPool.length} workers`);
}

/**
 * Generate a QR code with performance optimizations
 * @param {string} data - QR code data
 * @param {Object} options - QR code options
 * @param {Function} callback - Callback function
 * @returns {Promise} - Promise resolving to QR code data URL
 */
async function generateOptimizedQRCode(data, options = {}, callback) {
    // Create a unique key for the QR code based on data and options
    const qrKey = createCacheKey(data, options);
    
    // Check if the QR code is already cached
    if (QR_CACHE.has(qrKey)) {
        const cachedQR = QR_CACHE.get(qrKey);
        
        // Update last accessed time
        cachedQR.lastAccessed = Date.now();
        
        // Return cached result
        if (callback) callback(cachedQR.dataURL);
        return Promise.resolve(cachedQR.dataURL);
    }
    
    // If Web Workers are supported and available, use them
    if (workerPool.length > 0) {
        return new Promise((resolve, reject) => {
            // Add to processing queue
            const queueItem = {
                id: Date.now() + Math.random().toString(36).substr(2, 9),
                qrData: data,
                options,
                onComplete: (result) => {
                    if (callback) callback(result);
                    resolve(result);
                },
                onError: (error) => {
                    reject(error);
                }
            };
            
            processingQueue.push(queueItem);
            
            // Start processing if not already in progress
            if (!isProcessing) {
                processNextInQueue();
            }
        });
    }
    
    // Fallback to synchronous processing
    try {
        const qrDataURL = await generateQRCodeSync(data, options);
        
        // Cache the result
        cacheQRCode(data, qrDataURL, options);
        
        if (callback) callback(qrDataURL);
        return qrDataURL;
    } catch (error) {
        console.error('Error generating QR code:', error);
        throw error;
    }
}

/**
 * Process the next item in the queue
 */
function processNextInQueue() {
    if (processingQueue.length === 0) {
        isProcessing = false;
        return;
    }
    
    isProcessing = true;
    
    // Get available workers
    const availableWorkers = workerPool.filter(worker => !worker.busy);
    
    if (availableWorkers.length === 0) {
        // No available workers, try again later
        setTimeout(processNextInQueue, processingDelay);
        return;
    }
    
    // Process batch of items
    const itemsToProcess = processingQueue.slice(0, Math.min(batchSize, availableWorkers.length));
    
    itemsToProcess.forEach((item, index) => {
        if (index < availableWorkers.length) {
            const worker = availableWorkers[index];
            worker.busy = true;
            
            worker.postMessage({
                id: item.id,
                qrData: item.qrData,
                options: item.options
            });
        }
    });
    
    // Schedule next batch
    setTimeout(processNextInQueue, processingDelay);
}

/**
 * Generate QR code synchronously
 * @param {string} data - QR code data
 * @param {Object} options - QR code options
 * @returns {Promise} - Promise resolving to QR code data URL
 */
async function generateQRCodeSync(data, options) {
    // This is a placeholder for the actual QR code generation
    // In a real implementation, this would use the QR code library
    
    // For now, we'll use the existing generateQRCode function if available
    if (typeof generateQRCode === 'function') {
        return new Promise((resolve, reject) => {
            try {
                generateQRCode(data, options, (dataURL) => {
                    resolve(dataURL);
                });
            } catch (error) {
                reject(error);
            }
        });
    }
    
    // Fallback to a simple implementation
    return new Promise((resolve, reject) => {
        try {
            if (typeof QRCode === 'undefined') {
                throw new Error('QRCode library not found');
            }
            
            // Create a temporary canvas
            const canvas = document.createElement('canvas');
            canvas.width = options.width || 256;
            canvas.height = options.height || 256;
            
            // Generate QR code
            const qr = new QRCode(canvas, {
                text: data,
                width: canvas.width,
                height: canvas.height,
                colorDark: options.darkColor || '#000000',
                colorLight: options.lightColor || '#FFFFFF',
                correctLevel: QRCode.CorrectLevel[options.errorCorrectionLevel || 'H']
            });
            
            // Get data URL
            const dataURL = canvas.toDataURL('image/png');
            resolve(dataURL);
        } catch (error) {
            reject(error);
        }
    });
}

/**
 * Cache a generated QR code
 * @param {string} data - QR code data
 * @param {string} dataURL - QR code data URL
 * @param {Object} options - QR code options
 */
function cacheQRCode(data, dataURL, options = {}) {
    // Create a unique key for the QR code
    const qrKey = createCacheKey(data, options);
    
    // Add to cache
    QR_CACHE.set(qrKey, {
        dataURL,
        createdAt: Date.now(),
        lastAccessed: Date.now()
    });
    
    // Check if cache size exceeds limit
    if (QR_CACHE.size > CACHE_SIZE_LIMIT) {
        // Remove least recently used item
        let oldestKey = null;
        let oldestTime = Infinity;
        
        QR_CACHE.forEach((value, key) => {
            if (value.lastAccessed < oldestTime) {
                oldestTime = value.lastAccessed;
                oldestKey = key;
            }
        });
        
        if (oldestKey) {
            QR_CACHE.delete(oldestKey);
        }
    }
}

/**
 * Create a cache key for a QR code
 * @param {string} data - QR code data
 * @param {Object} options - QR code options
 * @returns {string} - Cache key
 */
function createCacheKey(data, options = {}) {
    // Create a string representation of the options
    const optionsStr = JSON.stringify(options);
    
    // Create a hash of the data and options
    return `${data}|${optionsStr}`;
}

/**
 * Clean up expired cache entries
 */
function cleanupCache() {
    const now = Date.now();
    
    QR_CACHE.forEach((value, key) => {
        if (now - value.createdAt > CACHE_EXPIRY_TIME) {
            QR_CACHE.delete(key);
        }
    });
}

/**
 * Initialize performance monitoring
 */
function initPerformanceMonitoring() {
    // Track QR code generation time
    const originalGenerateQRCode = window.generateQRCode;
    
    if (originalGenerateQRCode) {
        window.generateQRCode = function(data, options, callback) {
            const startTime = performance.now();
            
            // Call original function
            originalGenerateQRCode(data, options, (result) => {
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                // Log performance metrics
                logPerformanceMetric('qr_generation', duration);
                
                // Call original callback
                if (callback) callback(result);
            });
        };
    }
    
    // Set up periodic reporting
    setInterval(reportPerformanceMetrics, 60000); // Report every minute
}

// Performance metrics storage
const performanceMetrics = {
    qr_generation: {
        count: 0,
        totalTime: 0,
        min: Infinity,
        max: 0
    },
    cache_hits: 0,
    cache_misses: 0
};

/**
 * Log a performance metric
 * @param {string} metricName - Metric name
 * @param {number} value - Metric value
 */
function logPerformanceMetric(metricName, value) {
    if (metricName === 'qr_generation') {
        performanceMetrics.qr_generation.count++;
        performanceMetrics.qr_generation.totalTime += value;
        performanceMetrics.qr_generation.min = Math.min(performanceMetrics.qr_generation.min, value);
        performanceMetrics.qr_generation.max = Math.max(performanceMetrics.qr_generation.max, value);
    } else if (metricName === 'cache_hit') {
        performanceMetrics.cache_hits++;
    } else if (metricName === 'cache_miss') {
        performanceMetrics.cache_misses++;
    }
}

/**
 * Report performance metrics
 */
function reportPerformanceMetrics() {
    const { qr_generation, cache_hits, cache_misses } = performanceMetrics;
    
    if (qr_generation.count > 0) {
        const avgTime = qr_generation.totalTime / qr_generation.count;
        
        console.log('Performance Metrics Report:');
        console.log(`- QR Generation: ${qr_generation.count} codes generated`);
        console.log(`- Avg Generation Time: ${avgTime.toFixed(2)}ms`);
        console.log(`- Min Generation Time: ${qr_generation.min.toFixed(2)}ms`);
        console.log(`- Max Generation Time: ${qr_generation.max.toFixed(2)}ms`);
        console.log(`- Cache Hits: ${cache_hits}`);
        console.log(`- Cache Misses: ${cache_misses}`);
        console.log(`- Cache Hit Rate: ${(cache_hits / (cache_hits + cache_misses) * 100).toFixed(2)}%`);
        console.log(`- Cache Size: ${QR_CACHE.size} / ${CACHE_SIZE_LIMIT}`);
    }
}

/**
 * Get performance metrics
 * @returns {Object} - Performance metrics
 */
function getPerformanceMetrics() {
    const { qr_generation, cache_hits, cache_misses } = performanceMetrics;
    const cacheHitRate = (cache_hits + cache_misses > 0) ? 
        (cache_hits / (cache_hits + cache_misses) * 100).toFixed(2) : 0;
    
    return {
        qr_generation: {
            count: qr_generation.count,
            avgTime: qr_generation.count > 0 ? (qr_generation.totalTime / qr_generation.count).toFixed(2) : 0,
            minTime: qr_generation.min === Infinity ? 0 : qr_generation.min.toFixed(2),
            maxTime: qr_generation.max.toFixed(2)
        },
        cache: {
            hits: cache_hits,
            misses: cache_misses,
            hitRate: `${cacheHitRate}%`,
            size: QR_CACHE.size,
            limit: CACHE_SIZE_LIMIT
        }
    };
}

// Export functions for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        initPerformanceOptimizer,
        generateOptimizedQRCode,
        getPerformanceMetrics
    };
} else {
    // Add to window object
    window.initPerformanceOptimizer = initPerformanceOptimizer;
    window.generateOptimizedQRCode = generateOptimizedQRCode;
    window.getPerformanceMetrics = getPerformanceMetrics;
}
