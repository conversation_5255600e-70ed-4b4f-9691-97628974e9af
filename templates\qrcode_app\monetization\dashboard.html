{% extends 'base.html' %}
{% load static %}

{% block title %}QR Pro Dashboard{% endblock %}

{% block extra_css %}
<style>
.monetization-dashboard {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 2rem 0;
}

.dashboard-header {
    text-align: center;
    color: white;
    margin-bottom: 3rem;
}

.dashboard-header h1 {
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 1rem;
}

.dashboard-header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

.premium-badge {
    background: rgba(255,255,255,0.2);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: bold;
    margin-bottom: 1rem;
    display: inline-block;
}

.upgrade-cta {
    background: rgba(255,255,255,0.1);
    border-radius: 20px;
    padding: 3rem;
    text-align: center;
    color: white;
    margin-bottom: 3rem;
    backdrop-filter: blur(10px);
}

.upgrade-cta h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.upgrade-cta p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.btn-group {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-light {
    background: white;
    color: #667eea;
    border: none;
    padding: 1rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
}

.btn-light:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(255,255,255,0.3);
    color: #667eea;
}

.btn-outline-light {
    background: transparent;
    color: white;
    border: 2px solid white;
    padding: 1rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
}

.btn-outline-light:hover {
    background: white;
    color: #667eea;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-10px);
}

.feature-icon {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.feature-card h3 {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
    color: #333;
}

.feature-card p {
    color: #6c757d;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.btn-premium {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
}

.btn-premium:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
    color: white;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: rgba(255,255,255,0.1);
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    color: white;
    backdrop-filter: blur(10px);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.recent-activity {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    margin-top: 2rem;
}

.recent-activity h3 {
    color: #333;
    margin-bottom: 1.5rem;
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #eee;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    background: #f8f9fa;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    color: #667eea;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.25rem;
}

.activity-time {
    font-size: 0.875rem;
    color: #6c757d;
}
</style>
{% endblock %}

{% block content %}
<div class="monetization-dashboard">
    <div class="container">
        <!-- Header -->
        <div class="dashboard-header">
            {% if is_premium %}
            <div class="premium-badge">
                <i class="fas fa-crown me-2"></i>Premium User
            </div>
            <h1>🚀 QR Generator Pro Dashboard</h1>
            <p>Welcome to your premium monetization dashboard</p>
            {% else %}
            <h1>💰 Unlock QR Generator Pro</h1>
            <p>Transform your QR codes into a powerful business tool</p>
            {% endif %}
        </div>

        {% if not is_premium %}
        <!-- Upgrade CTA -->
        <div class="upgrade-cta">
            <h2><i class="fas fa-rocket me-2"></i>Upgrade to QR Pro</h2>
            <p>Get AI landing pages, dynamic redirects, branded pages, and advanced analytics</p>
            <div class="btn-group">
                <a href="{% url 'pricing' %}" class="btn-light btn-lg">
                    <i class="fas fa-crown me-2"></i>View Pricing Plans
                </a>
                <a href="{% url 'upgrade_to_premium' %}" class="btn-outline-light btn-lg">
                    <i class="fas fa-magic me-2"></i>Try QR Pro FREE
                </a>
            </div>
        </div>
        {% endif %}

        {% if is_premium %}
        <!-- Premium User Stats -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{ total_qr_codes }}</div>
                <div class="stat-label">Total QR Codes</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ branded_qr_codes }}</div>
                <div class="stat-label">Branded QR Codes</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ dynamic_redirects }}</div>
                <div class="stat-label">Dynamic Redirects</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ recent_scans }}</div>
                <div class="stat-label">Recent Scans</div>
            </div>
        </div>
        {% endif %}

        <!-- Features Grid -->
        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-magic"></i>
                </div>
                <h3>AI Landing Pages</h3>
                <p>Create professional landing pages with AI instead of simple redirects.</p>
                {% if is_premium %}
                <a href="#" class="btn-premium" onclick="showAILandingInfo()">
                    <i class="fas fa-magic me-2"></i>Create AI Page
                </a>
                {% else %}
                <span class="text-muted">Premium Feature</span>
                {% endif %}
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-exchange-alt"></i>
                </div>
                <h3>Dynamic QR Redirects</h3>
                <p>Change where your QR codes redirect without reprinting them.</p>
                {% if is_premium %}
                <a href="{% url 'dynamic_redirect_dashboard' %}" class="btn-premium">
                    <i class="fas fa-cog me-2"></i>Manage Redirects
                </a>
                {% else %}
                <span class="text-muted">Premium Feature</span>
                {% endif %}
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-palette"></i>
                </div>
                <h3>Branded Landing Pages</h3>
                <p>Create custom branded landing pages with your company colors and logo.</p>
                {% if is_premium %}
                <a href="{% url 'branding_management' %}" class="btn-premium">
                    <i class="fas fa-palette me-2"></i>Manage Branding
                </a>
                {% else %}
                <span class="text-muted">Premium Feature</span>
                {% endif %}
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h3>Advanced Analytics</h3>
                <p>Get detailed insights into your QR code performance and user behavior.</p>
                {% if is_premium %}
                <a href="{% url 'advanced_analytics' %}" class="btn-premium">
                    <i class="fas fa-chart-line me-2"></i>View Analytics
                </a>
                {% else %}
                <span class="text-muted">Premium Feature</span>
                {% endif %}
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-map-marked-alt"></i>
                </div>
                <h3>Scan Map</h3>
                <p>Visualize where your QR codes are being scanned around the world.</p>
                {% if is_premium %}
                <a href="{% url 'qr_map' %}" class="btn-premium">
                    <i class="fas fa-map-marked-alt me-2"></i>View Map
                </a>
                {% else %}
                <span class="text-muted">Premium Feature</span>
                {% endif %}
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-plug"></i>
                </div>
                <h3>Webhook Integration</h3>
                <p>Connect your QR scans to Zapier, CRM systems, and automation platforms.</p>
                {% if is_premium %}
                <a href="{% url 'webhook_dashboard' %}" class="btn-premium">
                    <i class="fas fa-plug me-2"></i>Setup Webhooks
                </a>
                {% else %}
                <span class="text-muted">Premium Feature</span>
                {% endif %}
            </div>
        </div>

        {% if is_premium and recent_scans %}
        <!-- Recent Activity -->
        <div class="recent-activity">
            <h3><i class="fas fa-clock me-2"></i>Recent Activity</h3>
            {% for scan in recent_scans %}
            <div class="activity-item">
                <div class="activity-icon">
                    <i class="fas fa-qrcode"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">QR Code Scanned: {{ scan.qr_code.name }}</div>
                    <div class="activity-time">{{ scan.scanned_at|timesince }} ago from {{ scan.country|default:"Unknown" }}</div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function showAILandingInfo() {
    alert('🚀 AI Landing Pages - NEW!\n\nCreate professional landing pages with AI instead of simple redirects.\n\n✨ Features:\n• AI-generated content\n• Professional designs\n• Mobile responsive\n• Custom branding\n• Analytics tracking\n\nUpgrade to QR Pro to access this feature!');
}
</script>
{% endblock %}
