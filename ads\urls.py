from django.urls import path
from . import views
from .views_payments import admin_transactions, admin_pending_payments, admin_approve_payment
from .views_enterprise import dashboard_enterprise, ad_create_enterprise  # Main ad creation view
from .views_analytics import analytics_enterprise
from .views_campaigns import (
    campaign_list, campaign_detail, campaign_create, campaign_edit,
    campaign_delete, campaign_activate, campaign_pause
)
from .views_api import search_campaigns, generate_ad_suggestions, submit_feedback, generate_ad_suggestions_no_auth

app_name = 'ads'

urlpatterns = [
    # Size guide
    path('size-guide/', views.ad_size_guide, name='ad_size_guide'),
    # Test views
    path('test/', views.test_view, name='test_view'),
    path('test-static/', views.test_static, name='test_static'),
    path('direct-static/', views.direct_static, name='direct_static'),
    path('dashboard-test/', views.dashboard_test, name='dashboard_test'),

    # Dashboard views
    path('', dashboard_enterprise, name='dashboard'),  # Make enterprise dashboard the default
    path('legacy/', views.dashboard, name='dashboard_legacy'),  # Keep old dashboard accessible but renamed

    # Ad creation (enterprise version only)
    path('create/', ad_create_enterprise, name='ad_create_consolidated'),  # Main route for ad creation
    path('list/', views.ad_list, name='ad_list'),
    path('detail/<slug:slug>/', views.ad_detail, name='ad_detail'),
    path('edit/<slug:slug>/', views.ad_edit, name='ad_edit'),
    path('delete/<slug:slug>/', views.ad_delete, name='ad_delete'),

    # Ad status management
    path('submit/<slug:slug>/', views.ad_submit, name='ad_submit'),
    path('submitted/<slug:slug>/', views.ad_submitted, name='ad_submitted'),
    path('pause/<slug:slug>/', views.ad_pause, name='ad_pause'),
    path('resume/<slug:slug>/', views.ad_resume, name='ad_resume'),

    # Transactions
    path('transactions/', views.transaction_list, name='transaction_list'),
    path('transaction/<int:transaction_id>/', views.transaction_detail, name='transaction_detail'),
    path('payment/<slug:slug>/', views.payment_process, name='payment_process'),

    # Analytics
    path('analytics/', views.analytics_dashboard, name='analytics_dashboard'),
    path('analytics/enterprise/', analytics_enterprise, name='analytics_enterprise'),
    path('analytics/<slug:slug>/', views.ad_analytics, name='ad_analytics'),

    # Campaign Management
    path('campaigns/', campaign_list, name='campaign_list'),
    path('campaigns/create/', campaign_create, name='campaign_create'),
    path('campaigns/<slug:slug>/', campaign_detail, name='campaign_detail'),
    path('campaigns/<slug:slug>/edit/', campaign_edit, name='campaign_edit'),
    path('campaigns/<slug:slug>/delete/', campaign_delete, name='campaign_delete'),
    path('campaigns/<slug:slug>/activate/', campaign_activate, name='campaign_activate'),
    path('campaigns/<slug:slug>/pause/', campaign_pause, name='campaign_pause'),

    # API Endpoints
    path('api/campaigns/search/', search_campaigns, name='api_search_campaigns'),
    path('api/generate-suggestions/', generate_ad_suggestions, name='generate_ad_suggestions'),  # Main AI suggestions endpoint
    path('ai/submit-feedback/', submit_feedback, name='submit_feedback'),

    # Test endpoint without authentication
    path('test/smart-ai/', views.test_smart_ai_engine, name='test_smart_ai_engine'),
    path('test/ai-suggestions-debug/', views.test_ai_suggestions_debug, name='test_ai_suggestions_debug'),
    path('test/generate-suggestions-no-auth/', generate_ad_suggestions_no_auth, name='generate_ad_suggestions_no_auth'),

    # Admin views for superusers
    path('admin/all/', views.admin_all_ads, name='admin_all_ads'),
    path('admin/pending/', views.admin_pending_ads, name='admin_pending_ads'),
    path('admin/active/', views.admin_active_ads, name='admin_active_ads'),
    path('admin/view/<slug:slug>/', views.admin_view_ad, name='admin_view_ad'),
    path('admin/approve/<slug:slug>/', views.admin_approve_ad, name='admin_approve_ad'),
    path('admin/reject/<slug:slug>/', views.admin_reject_ad, name='admin_reject_ad'),
    path('admin/update-status/<slug:slug>/', views.admin_update_ad_status, name='admin_update_ad_status'),
    path('admin/transactions/', admin_transactions, name='admin_transactions'),
    path('admin/pending-payments/', admin_pending_payments, name='admin_pending_payments'),
    path('admin/approve-payment/<int:transaction_id>/', admin_approve_payment, name='admin_approve_payment'),

    # Analytics
    path('analytics/', views.analytics_dashboard, name='analytics_dashboard'),
    path('analytics/enterprise/', analytics_enterprise, name='analytics_enterprise'),
    path('analytics/<slug:slug>/', views.ad_analytics, name='ad_analytics'),

    # API endpoints
    path('api/calculate-price/', views.calculate_price, name='calculate_price'),
    path('api/track-impression/', views.track_impression, name='track_impression'),
    path('api/track-click/', views.track_click, name='track_click'),

    # Test pages
    path('geolocation-test/', views.geolocation_test, name='geolocation_test'),
    path('ad-display-test/', views.ad_display_test, name='ad_display_test'),
]
