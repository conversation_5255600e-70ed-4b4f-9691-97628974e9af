/**
 * Smart Engine CSS
 * Styles for the AI-assisted content generation feature
 */

/* Smart Engine Toggle */
.form-switch .form-check-input:checked {
    background-color: #1a237e;
    border-color: #1a237e;
}

.form-switch .form-check-label {
    font-weight: 600;
}

/* Smart Engine Toggle Disabled State */
.smart-engine-disabled {
    opacity: 0.5;
    pointer-events: none;
    cursor: not-allowed;
}

.smart-engine-disabled .form-check-input {
    opacity: 0.5;
    cursor: not-allowed;
}

.smart-engine-disabled .form-check-label {
    opacity: 0.5;
    cursor: not-allowed;
    color: #6c757d !important;
}

.smart-engine-disabled .badge {
    opacity: 0.5;
}

.smart-engine-disabled .form-text {
    opacity: 0.5;
    color: #6c757d !important;
}

/* Smart Engine Options */
#smartEngineOptions {
    background-color: rgba(26, 35, 126, 0.03);
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
    border: 1px solid rgba(26, 35, 126, 0.1);
    transition: all 0.3s ease;
}

/* Language Selector */
#aiLanguage {
    border-color: rgba(26, 35, 126, 0.2);
}

#aiLanguage:focus {
    border-color: #1a237e;
    box-shadow: 0 0 0 0.25rem rgba(26, 35, 126, 0.25);
}

/* Generate Suggestions Button */
#generateSuggestions {
    background-color: #1a237e;
    border-color: #1a237e;
    transition: all 0.3s ease;
}

#generateSuggestions:hover {
    background-color: #0d1642;
    border-color: #0d1642;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

#generateSuggestions:disabled {
    opacity: 0.7;
    transform: none;
}

/* AI Suggestions Container */
#aiSuggestionsContainer {
    margin-top: 20px;
    transition: all 0.3s ease;
}

#aiSuggestionsContainer h5 {
    color: #1a237e;
    font-weight: 600;
    margin-bottom: 15px;
}

/* AI Suggestion Cards */
.ai-suggestion-card {
    background-color: #fff;
    transition: all 0.3s ease;
    border-color: #e9ecef !important;
    position: relative;
    overflow: hidden;
}

.ai-suggestion-card:hover {
    border-color: #1a237e !important;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    transform: translateY(-2px);
}

.ai-suggestion-card.selected {
    border-color: #1a237e !important;
    background-color: rgba(26, 35, 126, 0.05);
}

.ai-suggestion-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 0;
    background-color: #1a237e;
    transition: height 0.3s ease;
}

.ai-suggestion-card:hover::before,
.ai-suggestion-card.selected::before {
    height: 100%;
}

.ai-suggestion-title {
    font-weight: 600;
    color: #1a237e;
    margin-bottom: 5px;
}

.ai-suggestion-content {
    color: #6c757d;
    font-size: 0.875rem;
}

/* Radio Button Styling */
.ai-suggestion-card .form-check-input {
    border-color: #1a237e;
}

.ai-suggestion-card .form-check-input:checked {
    background-color: #1a237e;
    border-color: #1a237e;
}

.form-check-input:checked ~ .ai-suggestion-title {
    color: #1a237e;
    font-weight: 700;
}

/* AI Content Note */
#aiContentNote {
    color: #1a237e;
    background-color: rgba(26, 35, 126, 0.05);
    padding: 8px 12px;
    border-radius: 4px;
    margin-top: 5px;
    font-size: 0.875rem;
}

/* Animation for suggestions */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.ai-suggestion-card {
    animation: fadeIn 0.3s ease forwards;
    opacity: 0;
}

.ai-suggestion-card:nth-child(1) { animation-delay: 0.1s; }
.ai-suggestion-card:nth-child(2) { animation-delay: 0.2s; }
.ai-suggestion-card:nth-child(3) { animation-delay: 0.3s; }
