/* Luxury Corporate Body Styles */
body {
    font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: #f8f9fc;
    color: #333;
    line-height: 1.6;
    overflow-x: hidden;
}

.corporate-body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
}

/* Subtle background pattern for luxury feel */
.corporate-body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(rgba(58, 123, 213, 0.02) 1px, transparent 1px),
        linear-gradient(90deg, rgba(58, 123, 213, 0.02) 1px, transparent 1px);
    background-size: 20px 20px;
    pointer-events: none;
    z-index: -1;
}

main {
    flex: 1 0 auto;
    padding-bottom: 3rem;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

/* Hero Section Styles */
.hero-section {
    padding: 4rem 0 2rem;
    position: relative;
    overflow: hidden;
}

/* Luxury gradient background for hero */
.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(58, 123, 213, 0.03), rgba(0, 210, 255, 0.03));
    transform: skewY(-3deg);
    transform-origin: top right;
    z-index: -1;
}

/* Luxury heading styles */
.display-4 {
    font-weight: 800;
    letter-spacing: -0.02em;
    margin-bottom: 1.5rem;
    background: linear-gradient(90deg, #1a1a1a, #333);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
}

.display-4::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, #3a7bd5, #00d2ff);
    border-radius: 2px;
}

.lead {
    font-size: 1.5rem;
    font-weight: 400;
    color: #505050;
    margin-bottom: 2rem;
}

/* Luxury badge styles */
.badge {
    padding: 0.5rem 1rem;
    font-weight: 500;
    letter-spacing: 0.03em;
    border-radius: 30px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.badge::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
    pointer-events: none;
}

/* Luxury button styles */
.btn-primary {
    background: linear-gradient(135deg, #3a7bd5, #00d2ff);
    border: none;
    box-shadow: 0 5px 15px rgba(58, 123, 213, 0.3);
    padding: 0.75rem 2rem;
    font-weight: 600;
    letter-spacing: 0.03em;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-primary::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
    pointer-events: none;
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(58, 123, 213, 0.4);
    background: linear-gradient(135deg, #3a7bd5, #00d2ff);
}

.btn-outline-secondary {
    border: 2px solid rgba(58, 123, 213, 0.3);
    background: transparent;
    color: #333;
    font-weight: 600;
    letter-spacing: 0.03em;
    transition: all 0.3s ease;
}

.btn-outline-secondary:hover {
    background-color: rgba(58, 123, 213, 0.05);
    border-color: rgba(58, 123, 213, 0.5);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

/* Luxury card styles */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    transition: all 0.4s ease;
    overflow: hidden;
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #3a7bd5, #00d2ff);
}

.card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
}

.card-body {
    padding: 2rem;
}

.card-title {
    font-weight: 700;
    margin-bottom: 1.25rem;
    color: #1a1a1a;
}

/* Form control luxury styles */
.form-control, .form-select {
    padding: 0.75rem 1rem;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: rgba(58, 123, 213, 0.5);
    box-shadow: 0 0 0 3px rgba(58, 123, 213, 0.15);
}

.form-label {
    font-weight: 600;
    color: #505050;
    margin-bottom: 0.5rem;
}

/* Luxury Feature Section */
.feature-section {
    padding: 5rem 0;
    position: relative;
    overflow: hidden;
}

.feature-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(58, 123, 213, 0.02), rgba(0, 210, 255, 0.02));
    z-index: -1;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-align: center;
    background: linear-gradient(90deg, #1a1a1a, #333);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
    display: inline-block;
}

.section-subtitle {
    font-size: 1.2rem;
    color: #505050;
    margin-bottom: 3rem;
    text-align: center;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

/* Luxury Feature Cards */
.feature-card {
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    transition: all 0.4s ease;
    border: none;
    overflow: hidden;
    position: relative;
    height: 100%;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #3a7bd5, #00d2ff);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
}

/* Call to Action Section */
.cta-section {
    padding: 5rem 0;
    position: relative;
    overflow: hidden;
}

.cta-card {
    background: linear-gradient(135deg, #1a1a1a, #333);
    border-radius: 10px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
    padding: 3rem;
    position: relative;
    overflow: hidden;
}

.cta-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 20% 20%, rgba(58, 123, 213, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(0, 210, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.cta-card h2 {
    color: #fff;
    font-weight: 700;
    margin-bottom: 1.5rem;
}

.cta-card p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 2rem;
}

.cta-card .btn-primary {
    background: linear-gradient(135deg, #3a7bd5, #00d2ff);
    border: none;
    padding: 1rem 2.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    box-shadow: 0 5px 15px rgba(0, 210, 255, 0.3);
}

.cta-card .btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 210, 255, 0.4);
}

/* Responsive Styles */
@media (max-width: 991.98px) {
    .display-4 {
        font-size: 2.5rem;
    }
    
    .lead {
        font-size: 1.25rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
}

@media (max-width: 767.98px) {
    .display-4 {
        font-size: 2rem;
    }
    
    .lead {
        font-size: 1.1rem;
    }
    
    .section-title {
        font-size: 1.75rem;
    }
    
    .section-subtitle {
        font-size: 1rem;
    }
    
    .card-body {
        padding: 1.5rem;
    }
    
    .cta-card {
        padding: 2rem;
    }
}
