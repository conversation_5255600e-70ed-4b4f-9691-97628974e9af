from django import template
from django.utils import timezone
from ads.models import Ad, AdLocation

register = template.Library()

@register.inclusion_tag('ads/includes/ad_display.html')
def display_ads(location, max_ads=1, rotation='random', display_style=None, show_placeholder=False, show_carousel=False):
    """
    Template tag to display ads in a specific location.

    Usage:
    {% load ad_tags %}
    {% display_ads location='header' max_ads=1 rotation='random' display_style='carousel' %}

    Parameters:
    - location: The location identifier (e.g., 'header', 'sidebar', 'footer', 'popup', 'content')
    - max_ads: Maximum number of ads to display (default: 1)
    - rotation: Rotation strategy ('random', 'newest', 'oldest', 'highest_ctr') (default: 'random')
    - display_style: Override the default display style for the location
    - show_placeholder: Whether to show a placeholder if no ads are available (default: True)
    - show_carousel: Whether to show a carousel for multiple ads (default: False)
    """
    # Get active ads for the specified location
    # Only get ads that are active and within their display period
    now = timezone.now()

    # Try to find location objects that match the specified location
    location_objs = AdLocation.objects.filter(
        name__icontains=location
    )

    # Get ads for any of these locations
    ads = Ad.objects.filter(
        ad_location__in=location_objs,
        status='active',
        start_date__lte=now,
        end_date__gte=now
    )

    # If no ads found, try a more general search
    if not ads.exists():
        # Try to find ads with similar location names
        location_objs = AdLocation.objects.all()
        for loc_obj in location_objs:
            if location.lower() in loc_obj.name.lower() or loc_obj.name.lower() in location.lower():
                # Add ads from this location
                ads = ads | Ad.objects.filter(
                    ad_location=loc_obj,
                    status='active',
                    start_date__lte=now,
                    end_date__gte=now
                )

    # Apply rotation strategy
    if rotation == 'newest':
        ads = ads.order_by('-created_at')
    elif rotation == 'oldest':
        ads = ads.order_by('created_at')
    elif rotation == 'highest_ctr':
        # Calculate CTR for each ad and order by it
        ads = ads.order_by('-clicks', '-impressions')
    else:  # random
        ads = ads.order_by('?')

    # Limit to max_ads
    ads = ads[:max_ads]

    # Return context for the template
    return {
        'ads': ads,
        'location': location,
        'max_ads': max_ads,
        'rotation': rotation,
        'display_style': display_style,
        'show_placeholder': show_placeholder,
        'show_carousel': show_carousel,
    }
