from django.core.management.base import BaseCommand
from django.contrib.auth.models import User, Permission, Group
from django.contrib.contenttypes.models import ContentType
from qrcode_app.models import QRCode, UserProfile

class Command(BaseCommand):
    help = 'Set up permissions for administrators to access the performance dashboard'

    def handle(self, *args, **options):
        # Get the permission
        try:
            content_type = ContentType.objects.get_for_model(QRCode)
            permission = Permission.objects.get(
                codename='view_performance_dashboard',
                content_type=content_type,
            )
            self.stdout.write(self.style.SUCCESS('Found permission: view_performance_dashboard'))
        except Permission.DoesNotExist:
            self.stdout.write(self.style.ERROR('Permission view_performance_dashboard does not exist'))
            return

        # Find admin users
        admin_users = []
        
        # First, try to find users with is_staff=True
        staff_users = User.objects.filter(is_staff=True)
        admin_users.extend(staff_users)
        
        # Then, try to find users with UserProfile.role='admin' or 'superadmin'
        try:
            admin_profiles = UserProfile.objects.filter(role__in=['admin', 'superadmin'])
            for profile in admin_profiles:
                if profile.user not in admin_users:
                    admin_users.append(profile.user)
        except Exception as e:
            self.stdout.write(self.style.WARNING(f'Error finding admin profiles: {e}'))
        
        # If no admin users found, assign to superusers
        if not admin_users:
            admin_users = User.objects.filter(is_superuser=True)
            
        # Assign permission to admin users
        count = 0
        for user in admin_users:
            if not user.has_perm('qrcode_app.view_performance_dashboard'):
                user.user_permissions.add(permission)
                count += 1
                self.stdout.write(self.style.SUCCESS(f'Assigned permission to {user.username}'))
        
        if count == 0:
            self.stdout.write(self.style.WARNING('No new permissions assigned'))
        else:
            self.stdout.write(self.style.SUCCESS(f'Assigned permission to {count} users'))
            
        # Try to find or create an Administrators group
        try:
            admin_group, created = Group.objects.get_or_create(name='Administrators')
            if created:
                self.stdout.write(self.style.SUCCESS('Created Administrators group'))
            
            # Add permission to the group
            if permission not in admin_group.permissions.all():
                admin_group.permissions.add(permission)
                self.stdout.write(self.style.SUCCESS('Added permission to Administrators group'))
            else:
                self.stdout.write(self.style.WARNING('Permission already in Administrators group'))
                
            # Add admin users to the group
            for user in admin_users:
                if user not in admin_group.user_set.all():
                    admin_group.user_set.add(user)
                    self.stdout.write(self.style.SUCCESS(f'Added {user.username} to Administrators group'))
                    
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error setting up Administrators group: {e}'))
