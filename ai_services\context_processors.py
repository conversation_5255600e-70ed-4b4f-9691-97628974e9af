"""
AI Services Context Processors
Provides context processors to expose AI provider status to templates
"""
import json
import logging
from ai_services.health import get_all_provider_status, get_best_available_provider

logger = logging.getLogger(__name__)

def ai_provider_status(request):
    """
    Context processor to expose AI provider status to templates

    Args:
        request: The HTTP request

    Returns:
        Dictionary with AI provider status (full details for admins, abstracted for regular users)
    """
    try:
        # Get provider status
        provider_status = get_all_provider_status()
        best_provider = get_best_available_provider()

        # Create a simplified version for JavaScript
        js_status = {}

        # Check if user is admin/staff
        is_admin = request.user.is_authenticated and (request.user.is_staff or request.user.is_superuser)

        if is_admin:
            # Provide full details for admins
            for provider, status in provider_status.items():
                js_status[provider] = {
                    'available': status['available'],
                    'responseTime': status['response_time']
                }

            return {
                'ai_provider_status': provider_status,
                'best_ai_provider': best_provider,
                'ai_provider_status_json': json.dumps(js_status),
                'show_ai_status': True
            }
        else:
            # For regular users, always show Smart Engine as available
            # This abstracts the complexity of fallbacks from users
            abstracted_status = {
                'Smart Engine': {
                    'available': True,
                    'response_time': 0.5,  # Reasonable fake response time
                    'abstracted': True  # Flag to indicate this is abstracted
                }
            }

            js_status['Smart Engine'] = {
                'available': True,
                'responseTime': 0.5
            }

            # Always return a provider name, even if all are down
            user_facing_provider = "Smart Engine"

            return {
                'ai_provider_status': abstracted_status,
                'best_ai_provider': user_facing_provider,
                'ai_provider_status_json': json.dumps(js_status),
                'show_ai_status': False
            }
    except Exception as e:
        logger.error(f"Error in ai_provider_status context processor: {str(e)}")

        # Even on error, show Smart Engine as available to regular users
        if request.user.is_authenticated and (request.user.is_staff or request.user.is_superuser):
            return {
                'ai_provider_status': {},
                'best_ai_provider': None,
                'ai_provider_status_json': '{}',
                'show_ai_status': True
            }
        else:
            return {
                'ai_provider_status': {'Smart Engine': {'available': True, 'response_time': 0.5, 'abstracted': True}},
                'best_ai_provider': 'Smart Engine',
                'ai_provider_status_json': '{"Smart Engine": {"available": true, "responseTime": 0.5}}',
                'show_ai_status': False
            }
