"""
AI Services Settings
"""
import os
import logging
from dotenv import load_dotenv
from django.conf import settings

# Configure logging
logger = logging.getLogger(__name__)

# Load environment variables from .env file
load_dotenv()

# Environment detection
ENVIRONMENT = os.getenv('ENVIRONMENT', 'development')
IS_PRODUCTION = ENVIRONMENT.lower() == 'production'

# AI Provider Settings
# Default to fallback to try multiple providers in sequence
AI_PROVIDER = os.getenv('AI_PROVIDER', 'fallback')

# Check if local AI Engine is explicitly enabled
ENABLE_AI_ENGINE = os.getenv('ENABLE_AI_ENGINE', 'false').lower() == 'true'

# Log that the AI Engine is disabled
if not ENABLE_AI_ENGINE:
    logger.info("⚠️ AI Engine is currently disabled (ENABLE_AI_ENGINE=false)")

# Local AI Engine Settings (DISABLED)
if ENABLE_AI_ENGINE:
    if IS_PRODUCTION:
        # In production, use the Docker service name
        DEFAULT_AI_ENGINE_URL = 'http://ai_engine:8001'
    else:
        # In development, use localhost
        DEFAULT_AI_ENGINE_URL = 'http://localhost:8001'

    AI_ENGINE_URL = os.getenv('AI_ENGINE_URL', DEFAULT_AI_ENGINE_URL)
    AI_ENGINE_API_TOKEN = os.getenv('AI_ENGINE_API_TOKEN', 'ai-engine-token-1')
else:
    # Set placeholder values when disabled
    AI_ENGINE_URL = None
    AI_ENGINE_API_TOKEN = None

# Log the configuration
logger.info(f"AI Provider: {AI_PROVIDER}")
logger.info(f"AI Engine URL: {AI_ENGINE_URL}")
logger.info(f"Environment: {ENVIRONMENT}")

# Mistral AI Settings
MISTRAL_API_KEY = os.getenv('MISTRAL_API_KEY', '37OphnjoLD2IJN2kCZaFoHzgfcmJjltK')
MISTRAL_MODEL = os.getenv('MISTRAL_MODEL', 'mistral-tiny')  # Free tier model

# OpenAI Settings
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY', '')
OPENAI_MODEL = os.getenv('OPENAI_MODEL', 'gpt-4-turbo')

# Groq AI Settings (High-speed inference)
GROQ_API_KEY = os.getenv('GROQ_API_KEY', '')
GROQ_MODEL = os.getenv('GROQ_MODEL', 'llama3-8b-8192')
GROQ_TEMPERATURE = float(os.getenv('GROQ_TEMPERATURE', '0.7'))
GROQ_MAX_TOKENS = int(os.getenv('GROQ_MAX_TOKENS', '1024'))

# Make settings available to Django settings
if not hasattr(settings, 'AI_PROVIDER'):
    setattr(settings, 'AI_PROVIDER', AI_PROVIDER)
if not hasattr(settings, 'AI_ENGINE_URL'):
    setattr(settings, 'AI_ENGINE_URL', AI_ENGINE_URL)
if not hasattr(settings, 'AI_ENGINE_API_TOKEN'):
    setattr(settings, 'AI_ENGINE_API_TOKEN', AI_ENGINE_API_TOKEN)
if not hasattr(settings, 'MISTRAL_API_KEY'):
    setattr(settings, 'MISTRAL_API_KEY', MISTRAL_API_KEY)
if not hasattr(settings, 'MISTRAL_MODEL'):
    setattr(settings, 'MISTRAL_MODEL', MISTRAL_MODEL)
if not hasattr(settings, 'OPENAI_API_KEY'):
    setattr(settings, 'OPENAI_API_KEY', OPENAI_API_KEY)
if not hasattr(settings, 'OPENAI_MODEL'):
    setattr(settings, 'OPENAI_MODEL', OPENAI_MODEL)
if not hasattr(settings, 'GROQ_API_KEY'):
    setattr(settings, 'GROQ_API_KEY', GROQ_API_KEY)
if not hasattr(settings, 'GROQ_MODEL'):
    setattr(settings, 'GROQ_MODEL', GROQ_MODEL)
if not hasattr(settings, 'GROQ_TEMPERATURE'):
    setattr(settings, 'GROQ_TEMPERATURE', GROQ_TEMPERATURE)
if not hasattr(settings, 'GROQ_MAX_TOKENS'):
    setattr(settings, 'GROQ_MAX_TOKENS', GROQ_MAX_TOKENS)

# Ad Generation Settings
AD_GENERATION_TEMPERATURE = float(os.getenv('AD_GENERATION_TEMPERATURE', '0.7'))
AD_GENERATION_MAX_TOKENS = int(os.getenv('AD_GENERATION_MAX_TOKENS', '150'))
AD_GENERATION_NUM_SUGGESTIONS = int(os.getenv('AD_GENERATION_NUM_SUGGESTIONS', '3'))

# Language Settings
SUPPORTED_LANGUAGES = {
    'english': 'English',
    'swahili': 'Swahili',
    'sheng': 'Sheng'
}

# Prompts
AD_GENERATION_PROMPT_TEMPLATE = """
Generate {num_suggestions} creative and professional advertisement suggestions for a business.
Each suggestion should include a catchy title and compelling content.
The advertisements should be in {language} language.
The business type or product is: {business_type}
The target audience is: {target_audience}
The tone should be: {tone}
{title_instruction}

Format each suggestion as:
Title: {title_format}
Content: [compelling content that is concise and persuasive]

Keep titles under 60 characters and content under 150 characters.
Make the ads sound professional, engaging, and persuasive.
"""
