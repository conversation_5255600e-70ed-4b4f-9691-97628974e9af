/**
 * Notification Actions JavaScript
 * 
 * This file handles the functionality for notification action dropdowns
 * and ensures they work consistently across all pages, including admin
 * and user account pages.
 */

// Use an IIFE to avoid polluting the global namespace
(function() {
    // Store a reference to any open dropdown
    let openDropdown = null;

    // Function to initialize notification action dropdowns
    function initNotificationActions() {
        console.log('Initializing notification action dropdowns');
        
        // Get all action buttons
        const actionButtons = document.querySelectorAll('.notification-action-btn');
        
        // Add click event listeners to each button
        actionButtons.forEach(button => {
            button.addEventListener('click', handleActionButtonClick);
        });
        
        // Add global click event listener to close dropdowns when clicking outside
        document.addEventListener('click', handleDocumentClick);
        
        // Initialize confirmation dialogs
        initConfirmationDialogs();
        
        console.log(`Found ${actionButtons.length} notification action buttons`);
    }
    
    // Function to handle action button clicks
    function handleActionButtonClick(event) {
        event.preventDefault();
        event.stopPropagation();
        
        // Get the dropdown menu
        const dropdown = this.closest('.notification-dropdown');
        const menu = dropdown.querySelector('.dropdown-menu');
        
        console.log('Action button clicked', dropdown, menu);
        
        // Close any open dropdown that's not this one
        if (openDropdown && openDropdown !== menu) {
            openDropdown.classList.remove('show');
        }
        
        // Toggle this dropdown
        menu.classList.toggle('show');
        
        // Update the open dropdown reference
        openDropdown = menu.classList.contains('show') ? menu : null;
        
        console.log('Dropdown toggled:', menu.classList.contains('show'));
    }
    
    // Function to handle document clicks (close dropdowns when clicking outside)
    function handleDocumentClick(event) {
        // Don't close if clicking inside a dropdown, form, button, or link
        if (!event.target.closest('.notification-dropdown') && 
            !event.target.closest('form') &&
            !event.target.closest('button') &&
            !event.target.closest('a')) {
            
            // Close any open dropdown
            if (openDropdown) {
                openDropdown.classList.remove('show');
                openDropdown = null;
            }
        }
    }
    
    // Function to initialize confirmation dialogs
    function initConfirmationDialogs() {
        // Handle delete confirmation
        const deleteForms = document.querySelectorAll('.delete-form');
        deleteForms.forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                if (confirm('Are you sure you want to delete this notification? It will be permanently deleted after 72 hours.')) {
                    this.submit();
                }
            });
        });
        
        // Handle archive confirmation
        const archiveForms = document.querySelectorAll('.archive-form');
        archiveForms.forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                if (confirm('Are you sure you want to archive this notification? It will be automatically deleted after 30 days.')) {
                    this.submit();
                }
            });
        });
        
        // Handle mute confirmation
        const muteForms = document.querySelectorAll('.mute-form');
        muteForms.forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                // Get the category from the notification item
                const notificationItem = this.closest('.notification-item');
                const categoryElement = notificationItem.querySelector('.notification-category');
                const category = categoryElement ? categoryElement.textContent.trim() : 'this type';
                
                if (confirm(`Are you sure you want to mute notifications of category "${category}"? You will no longer receive notifications of this type.`)) {
                    this.submit();
                }
            });
        });
    }
    
    // Initialize when the DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initNotificationActions);
    } else {
        // DOM already loaded, initialize immediately
        initNotificationActions();
    }
    
    // Also initialize when the DOM is fully loaded (as a backup)
    window.addEventListener('load', initNotificationActions);
    
    // Re-initialize after AJAX content is loaded (if applicable)
    document.addEventListener('ajax:success', initNotificationActions);
    
    // Make the initialization function available globally
    window.initNotificationActions = initNotificationActions;
})();
