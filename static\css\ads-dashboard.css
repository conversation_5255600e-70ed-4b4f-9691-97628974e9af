/* Ads Dashboard Styles */

/* Card animations */
.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* Stats cards */
.card .display-4 {
    font-weight: 600;
    margin-bottom: 0;
}

/* Action cards */
.card-body h3 {
    margin-bottom: 1rem;
}

.card-body p {
    margin-bottom: 1.5rem;
}

/* Recent activity table */
.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

.badge {
    font-size: 0.85rem;
    padding: 0.4em 0.6em;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1.5rem;
    }

    .display-4 {
        font-size: 2.5rem;
    }

    /* Improve table display on mobile */
    .table-responsive {
        border: none;
        margin-bottom: 0;
    }

    .table th, .table td {
        padding: 0.75rem 0.5rem;
        font-size: 0.9rem;
    }

    /* Adjust stat cards for mobile */
    .stat-card {
        padding: 1.25rem;
        margin-bottom: 1rem;
    }

    /* Fix card headers on mobile */
    .card-header {
        padding: 1rem;
        flex-direction: column;
        align-items: flex-start;
    }

    .card-header .card-title {
        margin-bottom: 0.5rem;
    }

    .card-header .btn {
        width: 100%;
        margin-top: 0.5rem;
    }

    /* Improve quick action buttons */
    .quick-action {
        margin-bottom: 1rem;
    }

    /* Fix create ad button */
    .create-ad-button {
        width: 100%;
        text-align: center;
        padding: 1rem;
    }
}

/* Small mobile devices */
@media (max-width: 575.98px) {
    /* Further reduce font sizes */
    .display-4 {
        font-size: 2rem;
    }

    .lead {
        font-size: 1rem;
    }

    /* Adjust stat cards for very small screens */
    .stat-card {
        padding: 1rem;
    }

    .stat-value {
        font-size: 1.75rem;
    }

    /* Hide less important table columns */
    .table-responsive table th:nth-child(3),
    .table-responsive table td:nth-child(3) {
        display: none;
    }

    /* Improve badge display */
    .badge {
        display: inline-block;
        width: 100%;
        text-align: center;
        margin-top: 0.25rem;
    }
}
