"""
Management command to simulate user activity for testing notifications
"""
import random
from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from qrcode_app.models import QRCode
from qrcode_app.scan_signals import simulate_qr_code_scan
from ads.models import Ad
from ads.ad_signals import simulate_ad_analytics_update


class Command(BaseCommand):
    help = 'Simulate user activity to generate real-time notifications'

    def add_arguments(self, parser):
        parser.add_argument(
            '--qr-scans',
            type=int,
            default=5,
            help='Number of QR code scans to simulate'
        )
        parser.add_argument(
            '--ad-updates',
            type=int,
            default=3,
            help='Number of ad analytics updates to simulate'
        )
        parser.add_argument(
            '--username',
            type=str,
            help='Username to simulate activity for (default: all users)'
        )

    def handle(self, *args, **options):
        num_qr_scans = options['qr_scans']
        num_ad_updates = options['ad_updates']
        username = options['username']

        # Get users to simulate activity for
        if username:
            try:
                users = [User.objects.get(username=username)]
                self.stdout.write(self.style.SUCCESS(f"Simulating activity for user: {username}"))
            except User.DoesNotExist:
                self.stdout.write(self.style.ERROR(f"User {username} does not exist"))
                return
        else:
            users = User.objects.all()
            self.stdout.write(self.style.SUCCESS(f"Simulating activity for all users ({users.count()})"))

        # Simulate QR code scans
        self.simulate_qr_scans(users, num_qr_scans)

        # Simulate ad analytics updates
        self.simulate_ad_updates(users, num_ad_updates)

        self.stdout.write(self.style.SUCCESS('Simulation completed successfully'))

    def simulate_qr_scans(self, users, num_scans):
        """Simulate QR code scans for the given users"""
        total_scans = 0

        for user in users:
            # Get QR codes for this user
            qr_codes = QRCode.objects.filter(user=user)
            
            if not qr_codes.exists():
                self.stdout.write(self.style.WARNING(f"No QR codes found for user {user.username}"))
                continue

            # Simulate scans for random QR codes
            for _ in range(num_scans):
                # Get a random QR code
                qr_code = random.choice(qr_codes)
                
                # Simulate locations
                locations = ['Nairobi, Kenya', 'Mombasa, Kenya', 'Kisumu, Kenya', 
                            'Nakuru, Kenya', 'Eldoret, Kenya']
                location = random.choice(locations)
                
                # Simulate user agents
                user_agents = [
                    'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X)',
                    'Mozilla/5.0 (Android 10; Mobile; rv:68.0)',
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
                    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
                    'Mozilla/5.0 (Linux; Android 11; SM-G998B)'
                ]
                user_agent = random.choice(user_agents)
                
                # Simulate IP addresses
                ip_addresses = [
                    '41.204.190.' + str(random.randint(1, 254)),  # Kenya
                    '105.160.23.' + str(random.randint(1, 254)),  # Kenya
                    '197.232.61.' + str(random.randint(1, 254)),  # Kenya
                    '154.79.10.' + str(random.randint(1, 254)),   # Kenya
                    '196.201.214.' + str(random.randint(1, 254))  # Kenya
                ]
                ip_address = random.choice(ip_addresses)
                
                # Simulate the scan
                scan = simulate_qr_code_scan(
                    qr_code=qr_code,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    location=location
                )
                
                total_scans += 1
                self.stdout.write(f"Simulated scan for QR code '{qr_code.name}' by user {user.username}")

        self.stdout.write(self.style.SUCCESS(f"Simulated {total_scans} QR code scans"))

    def simulate_ad_updates(self, users, num_updates):
        """Simulate ad analytics updates for the given users"""
        total_updates = 0

        for user in users:
            # Get ads for this user
            ads = Ad.objects.filter(user=user)
            
            if not ads.exists():
                self.stdout.write(self.style.WARNING(f"No ads found for user {user.username}"))
                continue

            # Simulate updates for random ads
            for _ in range(num_updates):
                # Get a random ad
                ad = random.choice(ads)
                
                # Simulate impressions and clicks
                impressions = random.randint(30, 100)
                clicks = random.randint(1, 10)
                
                # Simulate the update
                analytics = simulate_ad_analytics_update(
                    ad=ad,
                    impressions_increase=impressions,
                    clicks_increase=clicks
                )
                
                total_updates += 1
                self.stdout.write(
                    f"Simulated analytics update for ad '{ad.title}' by user {user.username}: "
                    f"+{impressions} impressions, +{clicks} clicks"
                )

        self.stdout.write(self.style.SUCCESS(f"Simulated {total_updates} ad analytics updates"))
