from django.shortcuts import render, redirect, get_object_or_404
from django.urls import reverse
from django.contrib.auth.decorators import login_required, permission_required
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.views.decorators.csrf import csrf_exempt, csrf_protect
from django.conf import settings
from django.core.paginator import Paginator
from django.db.models import Q, Count
from django.contrib.auth import authenticate, login
from django.contrib.auth.models import User, Group, Permission
from django.utils import timezone

from .models import QRCode, UserProfile, APIKey, QRCodeBatch, QRCodeAnalytics, QRCodeScan, QRScanLog, AILandingPage, Plan, Subscription
from .forms import QRCodeForm, UserProfileForm, APIKeyForm, QRCodeBatchForm, CustomLoginForm, UserRegistrationForm
from .utils import (
    get_client_ip, detect_scanner_type, get_geolocation_from_ip,
    parse_device_info, generate_qr_landing_url, should_show_scanner_warning
)

import qrcode
from io import BytesIO
import base64
import json
import os
from PIL import Image

def index(request):
    """Home page view"""
    context = {
        # Homepage uses full-width layout like other pages
        # Sidebar ads are not needed on homepage as it has featured ads
    }
    return render(request, 'qrcode_app/index.html', context)

# Solution pages
def corporate_solution(request):
    """Corporate solution page"""
    return render(request, 'solutions/corporate.html')

def retail_solution(request):
    """Retail solution page"""
    return render(request, 'solutions/retail.html')

def hospitality_solution(request):
    """Hospitality solution page"""
    return render(request, 'solutions/hospitality.html')

def education_solution(request):
    """Education solution page"""
    return render(request, 'solutions/education.html')

def events_solution(request):
    """Events solution page"""
    return render(request, 'solutions/events.html')

def marketing_solution(request):
    """Marketing solution page"""
    return render(request, 'solutions/marketing.html')

def new_navbar_test(request):
    """Test page for the new navbar"""
    return render(request, 'new_navbar_test.html')

# Premium feature pages
def bulk_generation(request):
    """Bulk QR generation premium feature page"""
    return render(request, 'premium/bulk_generation.html')

def advanced_analytics(request):
    """Advanced analytics premium feature page"""
    return render(request, 'premium/advanced_analytics.html')

def encrypted_qr(request):
    """Encrypted QR codes premium feature page"""
    return render(request, 'premium/encrypted_qr.html')

def dedicated_hosting(request):
    """Dedicated hosting premium feature page"""
    return render(request, 'premium/dedicated_hosting.html')

def priority_support(request):
    """Priority support premium feature page"""
    return render(request, 'premium/priority_support.html')

@login_required
def generate_qr_code(request):
    """Generate or edit a QR code"""
    # Clear any existing error messages to prevent them from persisting
    storage = messages.get_messages(request)
    for message in storage:
        # Just iterate through to mark them as read
        pass
    storage.used = True

    # Check if we're editing an existing QR code
    edit_id = request.GET.get('edit')
    qr_code = None

    if edit_id:
        try:
            qr_code = get_object_or_404(QRCode, pk=edit_id, user=request.user)
            if request.method == 'GET':
                form = QRCodeForm(instance=qr_code)
                messages.info(request, f'You are now editing QR code "{qr_code.name}"')
        except Exception as e:
            messages.error(request, f"The QR code you're trying to edit doesn't exist or doesn't belong to you.")
            return redirect(reverse('qr_code_list') + '?from_error=true')

    if request.method == 'POST':
        # If editing, use the existing instance
        if qr_code:
            form = QRCodeForm(request.POST, request.FILES, instance=qr_code)
            success_message = 'QR code updated successfully!'
        else:
            form = QRCodeForm(request.POST, request.FILES)
            success_message = 'QR code generated successfully!'

        if form.is_valid():
            try:
                qr_code = form.save(commit=False)
                qr_code.user = request.user

                # Handle advanced customization fields from hidden inputs
                try:
                    qr_code.dot_style = request.POST.get('dot_style', 'square')
                    qr_code.corner_style = request.POST.get('corner_style', 'square')
                    qr_code.frame_style = request.POST.get('frame_style', 'none')
                    qr_code.title_text = request.POST.get('title_text', '')
                    qr_code.guiding_text = request.POST.get('guiding_text', '')
                    qr_code.guiding_text_position = request.POST.get('guiding_text_position', 'below')

                    print(f"Saving customizations: dot_style={qr_code.dot_style}, corner_style={qr_code.corner_style}, frame_style={qr_code.frame_style}")
                    print(f"Title: {qr_code.title_text}, Guiding: {qr_code.guiding_text}")
                except AttributeError:
                    # Fields don't exist yet, store in session for now
                    request.session['qr_customizations'] = {
                        'dot_style': request.POST.get('dot_style', 'square'),
                        'corner_style': request.POST.get('corner_style', 'square'),
                        'frame_style': request.POST.get('frame_style', 'none'),
                        'title_text': request.POST.get('title_text', ''),
                        'guiding_text': request.POST.get('guiding_text', ''),
                        'guiding_text_position': request.POST.get('guiding_text_position', 'below'),
                    }
                    print(f"Stored customizations in session: {request.session['qr_customizations']}")

                # Handle file uploads for specific QR types
                file_types = ['PDF', 'IMAGE', 'DOCUMENT', 'AUDIO', 'VIDEO']
                uploaded_file = request.FILES.get('uploaded_file')

                if qr_code.qr_type in file_types and uploaded_file:
                    # Save the uploaded file
                    qr_code.uploaded_file = uploaded_file
                    qr_code.file_name = uploaded_file.name
                    qr_code.file_size = uploaded_file.size
                    qr_code.file_type = uploaded_file.content_type

                    # If no data provided, use the file URL as data
                    if not qr_code.data:
                        # We'll set this after saving to get the URL
                        use_file_url = True
                    else:
                        use_file_url = False
                else:
                    use_file_url = False

                # Check if data is too large (preventive check)
                # QR code version 40 with low error correction can hold about 2953 bytes
                max_safe_length = 2900  # Conservative estimate

                if qr_code.data and len(qr_code.data) > max_safe_length:
                    messages.warning(request, f"Your data exceeds {max_safe_length} characters and will be truncated to fit in a QR code.")
                    # Truncate the data preemptively
                    qr_code.data = qr_code.data[:max_safe_length]
            except Exception as e:
                messages.error(request, f"Error processing form data: {str(e)}")
                return redirect('generate_qr_code')

            # Wrap the entire QR code generation process in a try-except block
            try:
                # Generate QR code image with an explicit maximum version to prevent errors
                # Always use version 40 (maximum) with low error correction for large data
                qr = qrcode.QRCode(
                    version=40,  # Explicitly set to maximum version
                    error_correction=qrcode.constants.ERROR_CORRECT_L,  # Use low error correction for maximum data capacity
                    box_size=10,
                    border=4,
                )

                # Handle URL types with landing page functionality
                if qr_code.qr_type == 'URL':
                    # Check if this is a URL that should use landing page
                    if qr_code.data.startswith(('http://', 'https://')) and not qr_code.data.startswith(request.build_absolute_uri('/qr/')):
                        # Store original URL and create landing URL
                        qr_code.original_url = qr_code.data
                        # Save first to get the unique_id
                        qr_code.save()
                        # Generate landing URL
                        landing_url = generate_qr_landing_url(request, qr_code)
                        qr_code.data = landing_url
                        qr_data_to_encode = landing_url
                    else:
                        # For non-HTTP URLs or already processed URLs, use as-is
                        qr_data_to_encode = qr_code.data
                elif use_file_url:
                    # Save without QR code image first
                    qr_code.save()
                    # Now we can get the file URL
                    file_url = request.build_absolute_uri(qr_code.uploaded_file.url)

                    # Check if file URL is too long
                    if len(file_url) > max_safe_length:
                        messages.warning(request, f"The file URL exceeds {max_safe_length} characters and may cause issues.")
                        # We'll still try to use it, but the error handling below will catch any problems

                    qr_code.data = file_url
                    qr_data_to_encode = file_url
                else:
                    # We've already truncated qr_code.data if needed
                    qr_data_to_encode = qr_code.data

                # Add data to QR code
                qr.add_data(qr_data_to_encode)

                # Try to make the QR code, truncating data if necessary
                try:
                    # Try to make the QR code with the full data
                    qr.make(fit=True)
                except ValueError as e:
                    # If we still get an error, truncate the data
                    messages.warning(request, "Your data was too large for a QR code. It has been truncated to fit.")

                    # Create a new QR code with truncated data
                    qr = qrcode.QRCode(
                        version=40,
                        error_correction=qrcode.constants.ERROR_CORRECT_L,
                        box_size=10,
                        border=4,
                    )

                    # Truncate the data to a reasonable size
                    max_data_length = 1500  # More conservative estimate
                    truncated_data = qr_data_to_encode[:max_data_length]
                    qr.add_data(truncated_data)

                    try:
                        qr.make(fit=True)
                    except ValueError:
                        # If still failing, try with even shorter data
                        qr = qrcode.QRCode(
                            version=40,
                            error_correction=qrcode.constants.ERROR_CORRECT_L,
                            box_size=10,
                            border=4,
                        )
                        very_short_data = truncated_data[:800]  # Even shorter data
                        qr.add_data(very_short_data)
                        qr.make(fit=True)

                # Create the QR code image
                img = qr.make_image(fill_color=qr_code.foreground_color, back_color=qr_code.background_color)

                # Add logo if provided
                if qr_code.logo:
                    logo = Image.open(qr_code.logo)
                    logo_size = (img.size[0] // 4, img.size[1] // 4)
                    logo = logo.resize(logo_size)

                    # Calculate position to place the logo
                    pos = ((img.size[0] - logo.size[0]) // 2, (img.size[1] - logo.size[1]) // 2)

                    # Create a new image with transparent background
                    img_with_logo = img.copy()
                    img_with_logo.paste(logo, pos, logo)
                    img = img_with_logo

                # Save the QR code image
                buffer = BytesIO()
                img.save(buffer, format='PNG')

                # Save to model
                filename = f"{qr_code.name.replace(' ', '_')}.png"
                qr_code.image.save(filename, buffer, save=False)
                qr_code.save()

            except Exception as e:
                # Catch any other unexpected errors during QR code generation
                messages.error(request, f"Error generating QR code: {str(e)}")
                # If we've already saved the QR code without an image, delete it to avoid orphaned records
                if use_file_url and qr_code.pk:
                    qr_code.delete()
                return redirect('generate_qr_code')

            messages.success(request, success_message)
            # Log the redirection for debugging
            print(f"Redirecting to qr_code_detail with pk={qr_code.pk}")
            print(f"QR code data: {qr_code.data}")
            print(f"QR code type: {qr_code.qr_type}")

            # Get the URL for the detail page
            detail_url = reverse('qr_code_detail', kwargs={'pk': qr_code.pk})
            print(f"Redirecting to URL: {detail_url}")

            # Print a clear message for debugging
            print("*" * 50)
            print(f"REDIRECTING TO QR CODE DETAIL PAGE: {detail_url}")
            print(f"Form data: {request.POST}")
            print(f"Redirect to detail: {request.POST.get('redirect_to_detail')}")
            print("*" * 50)

            # Check if we should redirect to the detail page
            if request.POST.get('redirect_to_detail') == 'true':
                # Force redirect to the detail page with absolute URL
                return redirect(detail_url)
            else:
                # For backward compatibility, still redirect to the detail page
                return redirect(detail_url)
        else:
            # Form is not valid - show form errors
            # Add specific error messages for common issues
            if 'data' in form.errors:
                messages.error(request, 'Please provide data for your QR code.')
            if 'uploaded_file' in form.errors:
                messages.error(request, 'Please upload a file for this QR code type.')
            if '__all__' in form.errors:
                for error in form.errors['__all__']:
                    messages.error(request, error)

            # Generic error message if no specific errors were shown
            if not any(field in form.errors for field in ['data', 'uploaded_file', '__all__']):
                messages.error(request, 'Please correct the errors below and try again.')
            # Fall through to render the form with errors
    elif not edit_id:
        # New QR code
        form = QRCodeForm()

    # Determine if we're editing or creating
    is_editing = qr_code is not None
    context = {
        'form': form,
        'is_editing': is_editing,
        'qr_code': qr_code
    }

    return render(request, 'qrcode_app/generate_qr_code.html', context)

@login_required
def qr_code_list(request):
    """List all QR codes for the current user"""
    # Clear any existing error messages when viewing the list page directly
    if not request.GET.get('from_error'):
        storage = messages.get_messages(request)
        for message in storage:
            # Just iterate through to mark them as read
            pass
        storage.used = True

    qr_codes = QRCode.objects.filter(user=request.user)

    # Search functionality
    query = request.GET.get('q')
    if query:
        qr_codes = qr_codes.filter(
            Q(name__icontains=query) |
            Q(data__icontains=query) |
            Q(qr_type__icontains=query)
        )

    # Pagination - show only 3 QR codes per page for premium enterprise-grade display
    paginator = Paginator(qr_codes, 3)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    return render(request, 'qrcode_app/qr_code_list.html', {'page_obj': page_obj, 'query': query})

@login_required
def qr_code_detail(request, pk):
    """View a specific QR code"""
    # Clear any existing error messages to prevent them from persisting
    storage = messages.get_messages(request)
    for message in storage:
        # Just iterate through to mark them as read
        pass
    storage.used = True

    try:
        # Get the QR code object
        qr_code = get_object_or_404(QRCode, pk=pk, user=request.user)

        # Check if user is premium
        try:
            user_profile = UserProfile.objects.get(user=request.user)
            is_premium = user_profile.is_premium()
        except UserProfile.DoesNotExist:
            is_premium = False

        # Get or create analytics for this QR code
        analytics, created = QRCodeAnalytics.objects.get_or_create(qr_code=qr_code)

        # Get recent scans if user is premium
        recent_scans = None
        geo_analytics = {}
        scanner_analytics = {}

        if is_premium:
            recent_scans = QRCodeScan.objects.filter(qr_code=qr_code).order_by('-scanned_at')[:10]

            # Get geo analytics
            scans_with_geo = QRCodeScan.objects.filter(qr_code=qr_code).exclude(country__isnull=True)

            # Country distribution
            country_data = {}
            city_data = {}
            scanner_data = {}

            for scan in scans_with_geo:
                # Country analytics
                if scan.country and scan.country != 'Unknown':
                    country_data[scan.country] = country_data.get(scan.country, 0) + 1

                # City analytics
                if scan.city and scan.city != 'Unknown':
                    city_key = f"{scan.city}, {scan.country}" if scan.country else scan.city
                    city_data[city_key] = city_data.get(city_key, 0) + 1

                # Scanner type analytics
                scanner_data[scan.scanner_type] = scanner_data.get(scan.scanner_type, 0) + 1

            geo_analytics = {
                'countries': country_data,
                'cities': city_data,
                'total_geo_scans': scans_with_geo.count()
            }

            scanner_analytics = scanner_data

        # Generate sample data for demonstration if no real data exists
        if is_premium and analytics.total_scans == 0:
            # Sample data for demonstration
            analytics.total_scans = 125
            analytics.unique_scans = 87
            analytics.last_scanned = timezone.now() - timezone.timedelta(hours=3)
            analytics.save()

            # Sample geo data
            geo_analytics = {
                'countries': {'United States': 45, 'Canada': 23, 'United Kingdom': 18, 'Germany': 12, 'France': 8},
                'cities': {'New York, United States': 15, 'Toronto, Canada': 12, 'London, United Kingdom': 10, 'Berlin, Germany': 8},
                'total_geo_scans': 83
            }

            scanner_analytics = {
                'native_camera': 78,
                'third_party_app': 32,
                'unknown': 15
            }

        context = {
            'qr_code': qr_code,
            'is_premium': is_premium,
            'analytics': analytics,
            'recent_scans': recent_scans,
            'geo_analytics': geo_analytics,
            'scanner_analytics': scanner_analytics,
        }

        return render(request, 'qrcode_app/qr_code_detail.html', context)
    except Exception as e:
        # If the QR code doesn't exist or doesn't belong to the user, show a friendly message
        messages.error(request, f"The QR code you're looking for (ID: {pk}) doesn't exist or doesn't belong to you.")
        return redirect(reverse('qr_code_list') + '?from_error=true')

@login_required
def delete_qr_code(request, pk):
    """Delete a QR code"""
    try:
        qr_code = get_object_or_404(QRCode, pk=pk, user=request.user)

        if request.method == 'POST':
            # Store the name for the success message
            qr_name = qr_code.name

            # Delete the QR code
            qr_code.delete()

            messages.success(request, f'QR code "{qr_name}" has been deleted successfully.')
            return redirect('qr_code_list')

        return render(request, 'qrcode_app/delete_qr_code_confirm.html', {'qr_code': qr_code})
    except:
        # If the QR code doesn't exist or doesn't belong to the user, show a friendly message
        messages.error(request, f"The QR code you're trying to delete (ID: {pk}) doesn't exist or doesn't belong to you.")
        return redirect(reverse('qr_code_list') + '?from_error=true')

@login_required
def user_management(request):
    """User management view for admins"""
    try:
        profile = UserProfile.objects.get(user=request.user)
        if not profile.can_manage_users():
            messages.error(request, 'You do not have permission to access this page.')
            return redirect('index')
    except UserProfile.DoesNotExist:
        messages.error(request, 'You do not have permission to access this page.')
        return redirect('index')

    users = UserProfile.objects.all().select_related('user')

    # Add context for permission-based UI rendering
    context = {
        'users': users,
        'can_add_users': profile.can_add_users(),
        'can_edit_users': profile.can_edit_users(),
        'can_delete_users': profile.can_delete_users(),
        'is_superadmin': profile.is_superadmin(),
    }

    return render(request, 'qrcode_app/user_management.html', context)

@login_required
def add_user(request):
    """Add a new user"""
    try:
        profile = UserProfile.objects.get(user=request.user)
        if not profile.can_add_users():
            messages.error(request, 'You do not have permission to add users.')
            return redirect('user_management')
    except UserProfile.DoesNotExist:
        messages.error(request, 'You do not have permission to add users.')
        return redirect('user_management')

    if request.method == 'POST':
        form = UserRegistrationForm(request.POST, request_user=request.user)
        if form.is_valid():
            try:
                user = form.save()
                messages.success(request, f'User {user.username} created successfully!')
                return redirect('user_management')
            except Exception as e:
                messages.error(request, f'Error creating user: {str(e)}')
    else:
        form = UserRegistrationForm(request_user=request.user)

    return render(request, 'qrcode_app/add_user.html', {'form': form})

@login_required
def edit_user(request, user_id):
    """Edit an existing user"""
    try:
        profile = UserProfile.objects.get(user=request.user)
        if not profile.can_edit_users():
            messages.error(request, 'You do not have permission to edit users.')
            return redirect('user_management')
    except UserProfile.DoesNotExist:
        messages.error(request, 'You do not have permission to edit users.')
        return redirect('user_management')

    user_to_edit = get_object_or_404(User, pk=user_id)

    # Only superadmins can edit other superadmins
    try:
        user_profile = UserProfile.objects.get(user=user_to_edit)
        if user_profile.is_superadmin() and not profile.is_superadmin():
            messages.error(request, 'You do not have permission to edit superadmin users.')
            return redirect('user_management')
    except UserProfile.DoesNotExist:
        pass

    if request.method == 'POST':
        try:
            # Get the user profile
            try:
                user_profile = UserProfile.objects.get(user=user_to_edit)
            except UserProfile.DoesNotExist:
                user_profile = UserProfile(user=user_to_edit)

            # Update user fields
            user_to_edit.username = request.POST.get('username', user_to_edit.username)
            user_to_edit.email = request.POST.get('email', user_to_edit.email)
            user_to_edit.first_name = request.POST.get('first_name', user_to_edit.first_name)
            user_to_edit.last_name = request.POST.get('last_name', user_to_edit.last_name)

            # Check if password should be updated
            new_password = request.POST.get('new_password')
            if new_password:
                user_to_edit.set_password(new_password)

            user_to_edit.save()

            # Update profile fields
            role = request.POST.get('role')
            # Only superadmins can set admin/superadmin roles
            if role in ['admin', 'superadmin'] and not profile.is_superadmin():
                messages.error(request, 'You do not have permission to assign admin or superadmin roles.')
            else:
                user_profile.role = role

            user_profile.company = request.POST.get('company', user_profile.company)
            user_profile.phone = request.POST.get('phone', user_profile.phone)
            user_profile.save()

            messages.success(request, f'User {user_to_edit.username} updated successfully!')
            return redirect('user_management')
        except Exception as e:
            messages.error(request, f'Error updating user: {str(e)}')

    # Get the user profile
    try:
        user_profile = UserProfile.objects.get(user=user_to_edit)
    except UserProfile.DoesNotExist:
        user_profile = None

    context = {
        'user_to_edit': user_to_edit,
        'user_profile': user_profile,
        'is_superadmin': profile.is_superadmin(),
    }

    return render(request, 'qrcode_app/edit_user.html', context)

@login_required
def delete_user(request, user_id):
    """Delete a user"""
    try:
        profile = UserProfile.objects.get(user=request.user)
        if not profile.can_delete_users():
            messages.error(request, 'You do not have permission to delete users.')
            return redirect('user_management')
    except UserProfile.DoesNotExist:
        messages.error(request, 'You do not have permission to delete users.')
        return redirect('user_management')

    user_to_delete = get_object_or_404(User, pk=user_id)

    # Prevent deleting yourself
    if user_to_delete == request.user:
        messages.error(request, 'You cannot delete your own account.')
        return redirect('user_management')

    # Only superadmins can delete other superadmins
    try:
        user_profile = UserProfile.objects.get(user=user_to_delete)
        if user_profile.is_superadmin() and not profile.is_superadmin():
            messages.error(request, 'You do not have permission to delete superadmin users.')
            return redirect('user_management')
    except UserProfile.DoesNotExist:
        pass

    if request.method == 'POST':
        try:
            username = user_to_delete.username
            user_to_delete.delete()
            messages.success(request, f'User {username} deleted successfully!')
            return redirect('user_management')
        except Exception as e:
            messages.error(request, f'Error deleting user: {str(e)}')

    return render(request, 'qrcode_app/delete_user_confirm.html', {'user_to_delete': user_to_delete})

@login_required
def role_management(request):
    """Role management view for admins"""
    try:
        profile = UserProfile.objects.get(user=request.user)
        if not profile.is_admin():
            messages.error(request, 'You do not have permission to access this page.')
            return redirect('index')
    except UserProfile.DoesNotExist:
        messages.error(request, 'You do not have permission to access this page.')
        return redirect('index')

    # Get all groups and their permissions
    groups = Group.objects.all().prefetch_related('permissions')

    context = {
        'groups': groups,
        'is_superadmin': profile.is_superadmin(),
    }

    return render(request, 'qrcode_app/role_management.html', context)

@login_required
def api_keys(request):
    """API keys management view"""
    api_keys = APIKey.objects.filter(user=request.user)

    if request.method == 'POST':
        form = APIKeyForm(request.POST)
        if form.is_valid():
            api_key = form.save(commit=False)
            api_key.user = request.user
            api_key.save()
            messages.success(request, 'API key created successfully!')
            return redirect('api_keys')
    else:
        form = APIKeyForm()

    return render(request, 'qrcode_app/api_keys.html', {'api_keys': api_keys, 'form': form})

@login_required
def settings_view(request):
    """User settings view"""
    try:
        profile = UserProfile.objects.get(user=request.user)
    except UserProfile.DoesNotExist:
        # Create a new profile for the user
        profile = UserProfile(
            user=request.user,
            role='user',
            created_at=timezone.now(),
            updated_at=timezone.now()
        )
        profile.save()
        messages.success(request, 'Profile created successfully!')

    if request.method == 'POST':
        form = UserProfileForm(request.POST, request.FILES, instance=profile)
        if form.is_valid():
            form.save()
            messages.success(request, 'Profile updated successfully!')
            return redirect('settings')
    else:
        form = UserProfileForm(instance=profile)

    return render(request, 'qrcode_app/settings.html', {'form': form})

@login_required
@permission_required('qrcode_app.view_performance_dashboard', raise_exception=True)
def performance_dashboard(request):
    """Performance dashboard view - Admin only"""
    # Get user statistics with optimized queries
    qr_codes_count = QRCode.objects.filter(user=request.user).count()
    batches_count = QRCodeBatch.objects.filter(user=request.user).count()

    # Get recent QR codes with optimized query
    recent_qr_codes = QRCode.objects.filter(user=request.user).select_related('user').order_by('-created_at')[:5]

    # Get system performance metrics
    system_metrics = {
        'server_status': 'Operational',
        'memory_usage': 42,  # In a real app, this would be actual server memory usage
        'cpu_load': 28,      # In a real app, this would be actual server CPU load
        'database_status': 'Optimized'
    }

    # Get optimization history (in a real app, this would come from a database)
    optimization_history = [
        {
            'name': 'Database Optimization',
            'date': timezone.now() - timezone.timedelta(days=5),
            'status': 'Completed',
            'improvement': '+28%'
        },
        {
            'name': 'Cache Management',
            'date': timezone.now() - timezone.timedelta(days=10),
            'status': 'Completed',
            'improvement': '+15%'
        },
        {
            'name': 'Image Processing',
            'date': timezone.now() - timezone.timedelta(days=15),
            'status': 'Completed',
            'improvement': '+22%'
        }
    ]

    context = {
        'qr_codes_count': qr_codes_count,
        'batches_count': batches_count,
        'recent_qr_codes': recent_qr_codes,
        'system_metrics': system_metrics,
        'optimization_history': optimization_history,
    }

    return render(request, 'performance_dashboard.html', context)

@login_required
def batch_processing(request):
    """Batch QR code processing view - Premium feature"""
    # Check if user has premium access
    try:
        profile = UserProfile.objects.get(user=request.user)
        if not profile.can_access_batch_processing():
            messages.warning(request, 'Batch processing is a premium feature. Upgrade your account to unlock this powerful tool!')
            # Show the premium modal by setting a session variable
            request.session['show_premium_modal'] = True
            # Redirect to pricing page
            return redirect('pricing')
    except UserProfile.DoesNotExist:
        messages.error(request, 'User profile not found. Please contact support.')
        return redirect('index')

    if request.method == 'POST':
        form = QRCodeBatchForm(request.POST, request.FILES)
        if form.is_valid():
            batch = form.save(commit=False)
            batch.user = request.user
            batch.save()

            # Process batch (this would be handled by Celery in production)
            # For now, just redirect to the batch detail page
            messages.success(request, 'Batch processing started!')
            return redirect('batch_detail', pk=batch.pk)
    else:
        form = QRCodeBatchForm()

    batches = QRCodeBatch.objects.filter(user=request.user).order_by('-created_at')

    return render(request, 'qrcode_app/batch_processing.html', {
        'form': form,
        'batches': batches,
        'is_premium': True,
        'is_admin': profile.is_admin(),
        'is_superadmin': profile.is_superadmin()
    })

@login_required
def batch_detail(request, pk):
    """View a specific QR code batch - Premium feature"""
    # Check if user has premium access
    try:
        profile = UserProfile.objects.get(user=request.user)
        if not profile.can_access_batch_processing():
            messages.warning(request, 'Batch processing is a premium feature. Upgrade your account to unlock this powerful tool!')
            # Show the premium modal by setting a session variable
            request.session['show_premium_modal'] = True
            # Redirect to pricing page
            return redirect('pricing')
    except UserProfile.DoesNotExist:
        messages.error(request, 'User profile not found. Please contact support.')
        return redirect('index')

    batch = get_object_or_404(QRCodeBatch, pk=pk, user=request.user)
    return render(request, 'qrcode_app/batch_detail.html', {
        'batch': batch,
        'is_premium': True,
        'is_admin': profile.is_admin(),
        'is_superadmin': profile.is_superadmin()
    })

def pricing(request):
    """Pricing page for premium features"""
    # Check if we should show the premium modal
    show_premium_modal = request.session.pop('show_premium_modal', False)

    return render(request, 'qrcode_app/pricing.html', {
        'show_premium_modal': show_premium_modal
    })

def checkout(request):
    """Checkout page for premium features"""
    plan = request.GET.get('plan', 'premium')
    billing = request.GET.get('billing', 'monthly')

    # Calculate prices based on plan and billing period
    if billing == 'annual':
        # 20% discount for annual billing
        if plan == 'enterprise':
            price = '479.90'  # $49.99 * 12 * 0.8 (20% discount)
            original_price = '599.88'  # $49.99 * 12
            savings = '120.00'  # $599.88 - $479.90
        else:  # premium
            price = '191.90'  # $19.99 * 12 * 0.8 (20% discount)
            original_price = '239.88'  # $19.99 * 12
            savings = '48.00'  # $239.88 - $191.90
    else:
        # Monthly billing
        if plan == 'enterprise':
            price = '49.99'
            original_price = None
            savings = None
        else:  # premium
            price = '19.99'
            original_price = None
            savings = None

    context = {
        'plan': plan,
        'billing': billing,
        'price': price,
        'original_price': original_price,
        'savings': savings,
        'is_annual': billing == 'annual'
    }

    return render(request, 'qrcode_app/checkout.html', context)

def checkout_success(request):
    """Checkout success page"""
    # In a real application, we would update the user's profile here
    # to give them premium access after successful payment

    # Check if user is authenticated before trying to update profile
    if request.user.is_authenticated:
        try:
            profile = UserProfile.objects.get(user=request.user)
            if profile.role == 'user' or profile.role == 'guest':
                profile.role = 'premium'
                profile.save()
                messages.success(request, 'Your account has been upgraded to Premium!')
        except UserProfile.DoesNotExist:
            # Create a profile for the user if it doesn't exist
            profile = UserProfile(
                user=request.user,
                role='premium',
                created_at=timezone.now(),
                updated_at=timezone.now()
            )
            profile.save()
            messages.success(request, 'Your account has been upgraded to Premium!')
    else:
        # For anonymous users, just show the success page without updating any profile
        messages.info(request, 'Your purchase was successful! Log in to access premium features.')

    return render(request, 'qrcode_app/checkout_success.html')

@csrf_protect
def custom_login(request):
    """Custom login view with explicit CSRF protection"""
    from django.contrib.auth import authenticate, login
    from django.contrib import messages

    # Ensure test users exist
    try:
        # Create apollo if doesn't exist
        apollo, created = User.objects.get_or_create(
            username='apollo',
            defaults={
                'email': '<EMAIL>',
                'is_active': True
            }
        )
        if created or not apollo.check_password('2587'):
            apollo.set_password('2587')
            apollo.save()

        # Create peter if doesn't exist
        peter, created = User.objects.get_or_create(
            username='peter',
            defaults={
                'email': '<EMAIL>',
                'is_superuser': True,
                'is_staff': True,
                'is_active': True
            }
        )
        if created or not peter.check_password('2587'):
            peter.set_password('2587')
            peter.is_superuser = True
            peter.is_staff = True
            peter.save()
    except Exception as e:
        print(f"Error creating users: {e}")

    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')

        user = authenticate(request, username=username, password=password)
        if user is not None:
            login(request, user)
            messages.success(request, f'Welcome back, {user.username}!')

            # Redirect to user switcher if available, otherwise index
            next_url = request.GET.get('next', 'user_switcher')
            try:
                return redirect(next_url)
            except:
                return redirect('index')
        else:
            messages.error(request, 'Invalid username or password. Try: apollo/2587 or peter/2587')

    return render(request, 'registration/login.html')

def debug_user_info(request):
    """Debug view to check user permissions"""
    try:
        if request.user.is_authenticated:
            profile = UserProfile.objects.get(user=request.user)
            user_info = {
                'username': request.user.username,
                'is_authenticated': request.user.is_authenticated,
                'is_staff': request.user.is_staff,
                'is_superuser': request.user.is_superuser,
                'profile_role': profile.role,
                'is_admin': profile.is_admin(),
                'is_superadmin': profile.is_superadmin(),
                'can_manage_users': profile.can_manage_users(),
                'can_add_users': profile.can_add_users(),
                'can_edit_users': profile.can_edit_users(),
                'can_delete_users': profile.can_delete_users(),
            }
        else:
            user_info = {
                'username': 'Anonymous',
                'is_authenticated': False,
                'is_staff': False,
                'is_superuser': False,
                'profile_role': 'Not logged in',
                'error': 'User not authenticated'
            }
    except UserProfile.DoesNotExist:
        user_info = {
            'username': request.user.username if request.user.is_authenticated else 'Anonymous',
            'is_authenticated': request.user.is_authenticated,
            'is_staff': request.user.is_staff if request.user.is_authenticated else False,
            'is_superuser': request.user.is_superuser if request.user.is_authenticated else False,
            'profile_role': 'No profile found',
            'error': 'UserProfile does not exist'
        }

    return JsonResponse(user_info)

def qr_landing(request, unique_id):
    """
    QR code landing page that handles scanner detection and analytics tracking
    """
    try:
        # Get the QR code by unique_id
        qr_code = get_object_or_404(QRCode, unique_id=unique_id)

        # Get client information
        ip_address = get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        referrer = request.META.get('HTTP_REFERER', '')

        # Detect scanner type
        scanner_type = detect_scanner_type(user_agent)
        show_warning = should_show_scanner_warning(user_agent)

        # Get geolocation data
        geo_data = get_geolocation_from_ip(ip_address)

        # Parse device information
        device_info = parse_device_info(user_agent)

        # Create scan record with enhanced enterprise tracking
        scan = QRCodeScan.objects.create(
            qr_code=qr_code,
            ip_address=ip_address,
            user_agent=user_agent,
            referrer=referrer,
            location=geo_data.get('city', 'Unknown'),  # Legacy field
            country=geo_data.get('country'),
            city=geo_data.get('city'),
            region=geo_data.get('region'),
            latitude=geo_data.get('latitude'),
            longitude=geo_data.get('longitude'),
            timezone=geo_data.get('timezone'),
            postal_code=geo_data.get('postal_code'),
            # Enterprise features: Organization/ISP tracking
            organization=geo_data.get('organization'),
            asn=geo_data.get('asn'),
            privacy_flags=geo_data.get('privacy_flags', {}),
            scanner_type=scanner_type,
            device_type=device_info.get('device_type'),
            os=device_info.get('os'),
            browser=device_info.get('browser')
        )

        # MODULE 4: Log scan event with smart alerts
        try:
            from .scan_alert_utils import log_scan_event
            log_scan_event(request, qr_code)
        except ImportError:
            # Fallback to analytics manager
            try:
                from .analytics_production import AnalyticsManager
                AnalyticsManager.log_qr_scan(request, qr_code, 'direct')
            except ImportError:
                # Final fallback to basic QRScanLog
                try:
                    QRScanLog.objects.create(
                        code=qr_code.unique_id,
                        ip_address=ip_address,
                        user_agent=user_agent,
                        country=geo_data.get('country', '')[:2] if geo_data.get('country') else '',
                        city=geo_data.get('city', ''),
                        org=geo_data.get('organization', ''),
                        latitude=geo_data.get('latitude'),
                        longitude=geo_data.get('longitude')
                    )
                except Exception as e:
                    print(f"Error creating QRScanLog: {e}")

        # Enterprise Security: Check for blocked connections
        if qr_code.block_vpn_scans and geo_data.get('privacy_flags', {}).get('vpn', False):
            return render(request, 'qrcode_app/qr_blocked.html', {
                'reason': 'VPN connections are not allowed for this QR code',
                'qr_code': qr_code
            }, status=403)

        if qr_code.block_tor_scans and geo_data.get('privacy_flags', {}).get('tor', False):
            return render(request, 'qrcode_app/qr_blocked.html', {
                'reason': 'Tor connections are not allowed for this QR code',
                'qr_code': qr_code
            }, status=403)

        # Check blocked countries
        user_country = geo_data.get('country')
        if user_country and user_country in qr_code.blocked_countries:
            return render(request, 'qrcode_app/qr_blocked.html', {
                'reason': f'Access from {user_country} is not allowed for this QR code',
                'qr_code': qr_code
            }, status=403)

        # Check blocked organizations
        user_org = geo_data.get('organization', '')
        for blocked_pattern in qr_code.blocked_organizations:
            if blocked_pattern.lower() in user_org.lower():
                return render(request, 'qrcode_app/qr_blocked.html', {
                    'reason': f'Access from {user_org} is not allowed for this QR code',
                    'qr_code': qr_code
                }, status=403)

        # Monetization: Dynamic QR Redirect (Premium feature)
        destination_url = qr_code.original_url or qr_code.data

        # Check for dynamic redirect first (Premium monetization feature)
        if hasattr(qr_code, 'dynamic_redirect') and qr_code.dynamic_redirect.is_active:
            dynamic_redirect = qr_code.dynamic_redirect
            destination_url = dynamic_redirect.get_redirect_url(request)

            # Update analytics for monetization tracking
            dynamic_redirect.total_clicks += 1
            dynamic_redirect.last_accessed = timezone.now()
            dynamic_redirect.save()

        # Enterprise Geo-Targeting: Override with geo-targeting if enabled
        elif qr_code.enable_geo_targeting and user_country:
            # Look for geo-targeted redirect
            geo_target = qr_code.geo_targets.filter(
                country_code=user_country,
                is_active=True
            ).order_by('priority').first()

            if geo_target:
                destination_url = geo_target.redirect_url
            elif qr_code.default_redirect_url:
                destination_url = qr_code.default_redirect_url

        # If it's not a URL type, handle differently
        if qr_code.qr_type != 'URL':
            # Get branding information for monetization
            branding = None
            user_profile = None
            show_powered_by = True

            if qr_code.user:
                try:
                    user_profile = UserProfile.objects.get(user=qr_code.user)
                    # Check if user has premium access for ad-free experience
                    if user_profile.is_premium():
                        show_powered_by = False

                    # Get custom branding if available
                    if qr_code.branding:
                        branding = qr_code.branding
                    elif user_profile.is_premium():
                        # For premium users, try to get their default branding
                        default_branding = qr_code.user.qr_brandings.filter(name__icontains='default').first()
                        if not default_branding:
                            default_branding = qr_code.user.qr_brandings.first()
                        branding = default_branding
                except UserProfile.DoesNotExist:
                    pass

            # For non-URL types, show the data on a landing page
            context = {
                'qr_code': qr_code,
                'show_scanner_warning': show_warning,
                'scanner_type': scanner_type,
                'geo_data': geo_data,
                'scan': scan,
                'is_url_type': False,
                'branding': branding,
                'user_profile': user_profile,
                'show_powered_by': show_powered_by,
                'is_premium_user': user_profile.is_premium() if user_profile else False
            }
            return render(request, 'qrcode_app/qr_landing.html', context)

        # Get branding information for monetization
        branding = None
        user_profile = None
        show_powered_by = True

        if qr_code.user:
            try:
                user_profile = UserProfile.objects.get(user=qr_code.user)
                # Check if user has premium access for ad-free experience
                if user_profile.is_premium():
                    show_powered_by = False

                # Get custom branding if available
                if qr_code.branding:
                    branding = qr_code.branding
                elif user_profile.is_premium():
                    # For premium users, try to get their default branding
                    default_branding = qr_code.user.qr_brandings.filter(name__icontains='default').first()
                    if not default_branding:
                        default_branding = qr_code.user.qr_brandings.first()
                    branding = default_branding
            except UserProfile.DoesNotExist:
                pass

        # For URL types, prepare for redirect
        context = {
            'qr_code': qr_code,
            'destination_url': destination_url,
            'show_scanner_warning': show_warning,
            'scanner_type': scanner_type,
            'geo_data': geo_data,
            'scan': scan,
            'is_url_type': True,
            'branding': branding,
            'user_profile': user_profile,
            'show_powered_by': show_powered_by,
            'is_premium_user': user_profile.is_premium() if user_profile else False
        }

        return render(request, 'qrcode_app/qr_landing.html', context)

    except QRCode.DoesNotExist:
        # QR code not found
        return render(request, 'qrcode_app/qr_not_found.html', status=404)
    except Exception as e:
        # Log the error and show a generic error page
        print(f"Error in qr_landing view: {e}")
        return render(request, 'qrcode_app/qr_error.html', {'error': str(e)}, status=500)


def test_simple(request):
    """Simple test view"""
    from django.http import HttpResponse
    return HttpResponse("Test page is working! Server is responding correctly.")


@staff_member_required
def scan_analytics_dashboard(request):
    """
    Admin-only QR Scan Analytics Dashboard
    Shows analytics from QRScanLog model as requested in prompts
    """
    total_scans = QRScanLog.objects.count()
    top_countries = QRScanLog.objects.values('country').annotate(count=Count('id')).order_by('-count')[:5]
    top_orgs = QRScanLog.objects.values('org').annotate(count=Count('id')).order_by('-count')[:5]
    recent_scans = QRScanLog.objects.order_by('-timestamp')[:10]

    return render(request, 'qr/scan_dashboard.html', {
        'total_scans': total_scans,
        'top_countries': top_countries,
        'top_orgs': top_orgs,
        'recent_scans': recent_scans,
    })


@staff_member_required
def scan_map_view(request):
    """
    Admin-only QR Scan Map View
    Shows a map with markers for each QR scan location using IPinfo coordinates
    """
    scan_data = QRScanLog.objects.exclude(latitude__isnull=True, longitude__isnull=True)
    return render(request, 'qr/scan_map.html', {
        'scans': scan_data
    })


@staff_member_required
def scan_map_data(request):
    """
    JSON API endpoint for real-time map data updates
    Returns scan data in JSON format for AJAX refresh
    """
    scans = QRScanLog.objects.exclude(latitude__isnull=True, longitude__isnull=True)
    data = [
        {
            'code': s.code,
            'latitude': s.latitude,
            'longitude': s.longitude,
            'city': s.city or 'Unknown',
            'country': s.country or 'Unknown',
            'org': s.org or 'Unknown',
            'ip_address': s.ip_address,
            'timestamp': s.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
        }
        for s in scans
    ]
    return JsonResponse(data, safe=False)

def dev_logout(request):
    """Development logout view - logs out current user so you can test with different accounts"""
    from django.contrib.auth import logout
    from django.http import HttpResponse
    if request.user.is_authenticated:
        username = request.user.username
        logout(request)
        return HttpResponse(f"Logged out {username}. Refresh any page to auto-login again, or go to /accounts/login/ to login as a different user.")
    else:
        return HttpResponse("No user was logged in. Go to /accounts/login/ to login manually.")


def user_switcher(request):
    """Testing view to switch between different user accounts"""
    return render(request, 'testing/user_switcher.html')


def test_login_as(request):
    """Testing view to login as a specific user"""
    from django.contrib.auth import login
    from django.contrib import messages

    if request.method == 'POST':
        username = request.POST.get('username')
        if username in ['apollo', 'peter', 'admin']:
            try:
                user = User.objects.get(username=username)
                login(request, user, backend='django.contrib.auth.backends.ModelBackend')
                messages.success(request, f'Successfully logged in as {username}')
                return redirect('index')
            except User.DoesNotExist:
                messages.error(request, f'User {username} not found')
        else:
            messages.error(request, 'Invalid user selection')

    return redirect('user_switcher')


def simple_login(request):
    """Simple login view that bypasses AllAuth for testing"""
    from django.contrib.auth import authenticate, login
    from django.contrib import messages

    # Ensure test users exist with correct passwords
    try:
        apollo, created = User.objects.get_or_create(
            username='apollo',
            defaults={'email': '<EMAIL>', 'is_active': True}
        )
        apollo.set_password('2587')
        apollo.save()

        peter, created = User.objects.get_or_create(
            username='peter',
            defaults={
                'email': '<EMAIL>',
                'is_superuser': True,
                'is_staff': True,
                'is_active': True
            }
        )
        peter.set_password('2587')
        peter.is_superuser = True
        peter.is_staff = True
        peter.save()
    except Exception as e:
        print(f"Error ensuring users: {e}")

    if request.method == 'POST':
        username = request.POST.get('username', '').strip()
        password = request.POST.get('password', '').strip()

        print(f"Login attempt: username='{username}', password='{password}'")

        if username and password:
            user = authenticate(request, username=username, password=password)
            print(f"Authentication result: {user}")

            if user is not None:
                login(request, user)
                messages.success(request, f'Welcome back, {user.username}!')

                # Redirect to index or next URL
                next_url = request.GET.get('next', 'index')
                return redirect(next_url)
            else:
                messages.error(request, f'Invalid credentials. Try apollo/2587 or peter/2587')
        else:
            messages.error(request, 'Please enter both username and password')

    return render(request, 'registration/login.html')


def dynamic_qr_redirect(request, code):
    """
    MODULE 2: Dynamic QR redirect view for short URLs
    Handles direct redirects via /r/<code>/ URLs
    """
    try:
        # Get QR code by short code
        qr_code = get_object_or_404(QRCode, short_code=code)

        # Check if QR code is active
        if not qr_code.is_active():
            return render(request, 'qrcode_app/qr_blocked.html', {
                'reason': 'This QR code is no longer active.',
                'qr_code': qr_code
            }, status=403)

        # Get target URL using the monetization logic
        target_url = qr_code.get_target_url()

        # MODULE 4: Log scan event with smart alerts
        try:
            from .scan_alert_utils import log_scan_event
            log_scan_event(request, qr_code)
        except ImportError:
            # Fallback to analytics manager
            try:
                from .analytics_production import AnalyticsManager
                AnalyticsManager.log_qr_scan(request, qr_code, 'dynamic_redirect')
            except ImportError:
                # Final fallback to basic logging
                ip_address = get_client_ip(request)
                user_agent = request.META.get('HTTP_USER_AGENT', '')
                geo_data = get_geolocation_from_ip(ip_address)

                try:
                    QRScanLog.objects.create(
                        code=qr_code.short_code,
                        ip_address=ip_address,
                        user_agent=user_agent,
                        country=geo_data.get('country', '')[:2] if geo_data.get('country') else '',
                        city=geo_data.get('city', ''),
                        org=geo_data.get('organization', ''),
                        latitude=geo_data.get('latitude'),
                        longitude=geo_data.get('longitude')
                    )
                except Exception as e:
                    print(f"Error creating scan log: {e}")

        # Update dynamic redirect analytics if applicable
        if hasattr(qr_code, 'dynamic_redirect') and qr_code.dynamic_redirect.is_active:
            dynamic_redirect = qr_code.dynamic_redirect
            dynamic_redirect.total_clicks += 1
            dynamic_redirect.last_accessed = timezone.now()
            dynamic_redirect.save()

        # Redirect to target URL
        return redirect(target_url)

    except QRCode.DoesNotExist:
        return render(request, 'qrcode_app/qr_not_found.html', {
            'code': code,
            'message': f'QR code "{code}" not found.'
        }, status=404)
    except Exception as e:
        print(f"Error in dynamic_qr_redirect: {e}")
        return render(request, 'qrcode_app/qr_error.html', {
            'error': str(e)
        }, status=500)


def serve_ai_landing(request, code):
    """
    MODULE 3: Serve AI-generated landing page
    Handles both short codes and unique IDs
    """
    try:
        # Try to find QR code by short code first, then by unique_id
        qr_code = None
        try:
            qr_code = QRCode.objects.get(short_code=code)
        except QRCode.DoesNotExist:
            try:
                qr_code = QRCode.objects.get(unique_id=code)
            except QRCode.DoesNotExist:
                return render(request, 'qrcode_app/qr_not_found.html', {
                    'code': code,
                    'message': f'QR code "{code}" not found.'
                }, status=404)

        # Check if QR code is active
        if not qr_code.is_active():
            return render(request, 'qrcode_app/qr_blocked.html', {
                'reason': 'This QR code is no longer active.',
                'qr_code': qr_code
            }, status=403)

        # Check if AI landing page exists
        try:
            ai_page = qr_code.ai_landing_page

            # Check if page is published
            if not ai_page.is_published or ai_page.status != ai_page.PageStatus.ACTIVE:
                # Fall back to regular QR redirect
                target_url = qr_code.get_target_url()
                if target_url:
                    return redirect(target_url)
                else:
                    return render(request, 'qrcode_app/qr_error.html', {
                        'error': 'This QR code does not have an active landing page.'
                    }, status=404)

            # Log the scan for analytics using production-ready manager
            try:
                from .analytics_production import AnalyticsManager
                AnalyticsManager.log_qr_scan(request, ai_page.qr_code, 'ai_landing')
            except ImportError:
                # Fallback to basic logging
                ip_address = get_client_ip(request)
                user_agent = request.META.get('HTTP_USER_AGENT', '')
                geo_data = get_geolocation_from_ip(ip_address)

                try:
                    QRScanLog.objects.create(
                        code=code,
                        ip_address=ip_address,
                        user_agent=user_agent,
                        country=geo_data.get('country', '')[:2] if geo_data.get('country') else '',
                        city=geo_data.get('city', ''),
                        org=geo_data.get('organization', ''),
                        latitude=geo_data.get('latitude'),
                        longitude=geo_data.get('longitude')
                    )
                except Exception as e:
                    print(f"Error creating scan log: {e}")

            # Increment view count
            ai_page.increment_view_count()

            # Serve the AI landing page
            from django.utils.safestring import mark_safe
            return render(request, 'qrcode_app/ai_landing_display.html', {
                'ai_page': ai_page,
                'qr_code': qr_code,
                'html_content': mark_safe(ai_page.content)
            })

        except AILandingPage.DoesNotExist:
            # No AI landing page, fall back to regular redirect
            target_url = qr_code.get_target_url()
            if target_url:
                return redirect(target_url)
            else:
                return render(request, 'qrcode_app/qr_error.html', {
                    'error': 'This QR code does not have a destination URL or landing page.'
                }, status=404)

    except Exception as e:
        print(f"Error in serve_ai_landing: {e}")
        return render(request, 'qrcode_app/qr_error.html', {
            'error': str(e)
        }, status=500)


@login_required
def subscription_status_api(request):
    """
    API endpoint for subscription status and usage limits
    Used by the upgrade flow JavaScript
    """
    try:
        # Get user's current subscription
        subscription = None
        try:
            subscription = Subscription.objects.get(user=request.user)
        except Subscription.DoesNotExist:
            # Create a default free subscription if none exists
            free_plan = Plan.objects.filter(name__icontains='free').first()
            if free_plan:
                subscription = Subscription.objects.create(
                    user=request.user,
                    plan=free_plan,
                    status='ACTIVE'
                )

        # Get available plans for upgrade
        available_plans = Plan.objects.filter(is_active=True).order_by('sort_order')

        # Prepare plan data
        plans_data = []
        for plan in available_plans:
            plans_data.append({
                'id': plan.id,
                'name': plan.name,
                'price': float(plan.price),
                'features_list': plan.get_features_list(),
                'ai_enabled': plan.ai_enabled,
                'advanced_analytics_enabled': plan.advanced_analytics_enabled,
                'api_access_enabled': plan.api_access_enabled,
                'max_qr_codes': plan.max_qr_codes,
                'max_scans_per_month': plan.max_scans_per_month,
                'max_ai_pages': plan.max_ai_pages,
            })

        # Get usage summary
        usage_summary = {}
        if subscription:
            usage_summary = subscription.get_usage_summary()

        # Prepare response data
        response_data = {
            'user': {
                'id': request.user.id,
                'username': request.user.username,
                'email': request.user.email,
            },
            'subscription': {
                'id': subscription.id if subscription else None,
                'status': subscription.status if subscription else 'NONE',
                'plan': {
                    'id': subscription.plan.id if subscription and subscription.plan else None,
                    'name': subscription.plan.name if subscription and subscription.plan else 'No Plan',
                    'price': float(subscription.plan.price) if subscription and subscription.plan else 0,
                    'ai_enabled': subscription.plan.ai_enabled if subscription and subscription.plan else False,
                    'advanced_analytics_enabled': subscription.plan.advanced_analytics_enabled if subscription and subscription.plan else False,
                    'api_access_enabled': subscription.plan.api_access_enabled if subscription and subscription.plan else False,
                } if subscription and subscription.plan else None,
                'started_at': subscription.started_at.isoformat() if subscription and subscription.started_at else None,
                'expires_at': subscription.expires_at.isoformat() if subscription and subscription.expires_at else None,
            },
            'usage_summary': usage_summary,
            'available_plans': plans_data,
            'current_limits': {
                'qr_codes_limit_reached': False,
                'scans_limit_reached': False,
                'ai_limit_reached': False,
                'analytics_available': False,
                'api_available': False,
            }
        }

        # Check current limits
        if subscription and subscription.plan:
            plan = subscription.plan

            # Check QR codes limit
            current_qr_count = request.user.qr_codes.count()
            response_data['current_limits']['qr_codes_limit_reached'] = current_qr_count >= plan.max_qr_codes

            # Check scans limit
            response_data['current_limits']['scans_limit_reached'] = subscription.scans_this_month >= plan.max_scans_per_month

            # Check AI limit
            if plan.ai_enabled:
                from .models import AILandingPage
                current_ai_count = AILandingPage.objects.filter(qr_code__user=request.user).count()
                response_data['current_limits']['ai_limit_reached'] = current_ai_count >= plan.max_ai_pages

            # Check feature availability
            response_data['current_limits']['analytics_available'] = plan.advanced_analytics_enabled
            response_data['current_limits']['api_available'] = plan.api_access_enabled

        return JsonResponse(response_data)

    except Exception as e:
        return JsonResponse({
            'error': f'Failed to load subscription status: {str(e)}'
        }, status=500)
