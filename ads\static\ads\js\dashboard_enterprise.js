// Enterprise Dashboard JavaScript

// Function to format numbers with commas
function formatNumber(num) {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

// Function to animate counting up
function animateCounter(element, target, duration = 1500) {
    const start = 0;
    const increment = target / (duration / 16);
    let current = start;

    const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
            clearInterval(timer);
            element.textContent = formatNumber(Math.round(target));
        } else {
            element.textContent = formatNumber(Math.round(current));
        }
    }, 16);
}

// Tab navigation functionality
function setupTabNavigation() {
    const navLinks = document.querySelectorAll('.nav-link[data-tab]');
    const tabPanes = document.querySelectorAll('.tab-pane');

    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            // Get the tab ID from data attribute
            const tabId = this.getAttribute('data-tab');

            // Remove active class from all links and add to clicked link
            navLinks.forEach(navLink => {
                navLink.parentElement.classList.remove('active');
            });
            this.parentElement.classList.add('active');

            // Hide all tab panes and show the selected one
            tabPanes.forEach(pane => {
                pane.classList.remove('active');
            });
            document.getElementById(tabId).classList.add('active');
        });
    });
}

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    // Setup tab navigation
    setupTabNavigation();

    // Initialize date range picker
    initDateRangePicker();

    // Style trend indicators
    styleTrendIndicators();

    // Animate stat values
    setTimeout(() => {
        document.querySelectorAll('.stat-value').forEach(el => {
            const text = el.textContent.trim();
            if (!text.includes('%') && !isNaN(parseInt(text))) {
                const value = parseInt(text);
                el.textContent = '0';
                animateCounter(el, value);
            }
        });
    }, 500);

    // Format numbers in metrics
    document.querySelectorAll('.metric-value').forEach(el => {
        const text = el.textContent.trim();
        if (!isNaN(parseInt(text))) {
            el.textContent = formatNumber(parseInt(text));
        }
    });

    // Add subtle hover effects to recent ad items
    const recentAdItems = document.querySelectorAll('.recent-ad-item');
    recentAdItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(5px)';
        });

        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0)';
        });
    });

    // Add subtle hover effects to stat cards
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Simulate loading sparkline charts
    // In a real implementation, this would be replaced with actual chart rendering
    simulateSparklines();
});

// Function to simulate sparkline charts
// This would be replaced with actual chart rendering in production
function simulateSparklines() {
    const sparklinePlaceholders = document.querySelectorAll('.sparkline-placeholder');

    sparklinePlaceholders.forEach(placeholder => {
        // Create a random gradient for each placeholder
        const hue = Math.floor(Math.random() * 360);
        const gradient = `linear-gradient(90deg,
            hsla(${hue}, 70%, 60%, 0.1) 0%,
            hsla(${hue}, 70%, 60%, 0.2) 20%,
            hsla(${hue}, 70%, 60%, 0.1) 40%,
            hsla(${hue}, 70%, 60%, 0.3) 60%,
            hsla(${hue}, 70%, 60%, 0.2) 80%,
            hsla(${hue}, 70%, 60%, 0.4) 100%
        )`;

        placeholder.style.background = gradient;
    });
}

// Function to style trend indicators
function styleTrendIndicators() {
    const trendIndicators = document.querySelectorAll('.metric-trend, .stat-trend');

    trendIndicators.forEach(indicator => {
        const icon = indicator.querySelector('i');
        if (!icon) return;

        if (icon.classList.contains('fa-arrow-up')) {
            indicator.classList.add('trend-up');
        } else if (icon.classList.contains('fa-arrow-down')) {
            indicator.classList.add('trend-down');
        } else {
            indicator.classList.add('trend-neutral');
        }
    });
}

// Initialize date range picker if present
function initDateRangePicker() {
    const dateRangeBtn = document.getElementById('daterange-btn');
    if (!dateRangeBtn) return;

    $(dateRangeBtn).daterangepicker({
        ranges: {
           'Today': [moment(), moment()],
           'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
           'Last 7 Days': [moment().subtract(6, 'days'), moment()],
           'Last 30 Days': [moment().subtract(29, 'days'), moment()],
           'This Month': [moment().startOf('month'), moment().endOf('month')],
           'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
        },
        startDate: moment().subtract(29, 'days'),
        endDate: moment(),
        alwaysShowCalendars: true,
        opens: 'left'
    }, function(start, end, label) {
        // Update the button text
        dateRangeBtn.querySelector('span').textContent = start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY');

        // In a real implementation, this would trigger a form submission or AJAX request
        console.log('Date range selected: ' + start.format('YYYY-MM-DD') + ' to ' + end.format('YYYY-MM-DD'));
    });
}
