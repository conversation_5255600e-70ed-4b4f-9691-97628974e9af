#!/usr/bin/env python
"""
Collect Static Files Script
This script runs Django's collectstatic command with optimized settings for production.
"""

import os
import sys
import subprocess
import argparse
import time
import shutil
from pathlib import Path

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Collect static files for production.')
    parser.add_argument('--noinput', action='store_true', help='Do not ask for user input')
    parser.add_argument('--clear', action='store_true', help='Clear the existing static files before collecting')
    parser.add_argument('--dry-run', action='store_true', help='Do everything except modify the filesystem')
    parser.add_argument('--compress', action='store_true', help='Compress static files')
    parser.add_argument('--optimize', action='store_true', help='Optimize static files (minify JS/CSS)')
    return parser.parse_args()

def set_production_environment():
    """Set environment variables for production."""
    os.environ['DJANGO_SETTINGS_MODULE'] = 'QRCodeGenerator.settings'
    os.environ['DJANGO_CONFIGURATION'] = 'Production'
    os.environ['DEBUG'] = 'False'

def run_collectstatic(args):
    """Run Django's collectstatic command."""
    print("Running collectstatic...")
    
    cmd = [sys.executable, 'manage.py', 'collectstatic']
    
    if args.noinput:
        cmd.append('--noinput')
    
    if args.clear:
        cmd.append('--clear')
    
    if args.dry_run:
        cmd.append('--dry-run')
    
    start_time = time.time()
    result = subprocess.run(cmd, capture_output=True, text=True)
    end_time = time.time()
    
    if result.returncode == 0:
        print(f"Collectstatic completed successfully in {end_time - start_time:.2f} seconds.")
        print(result.stdout)
        return True
    else:
        print("Collectstatic failed:")
        print(result.stderr)
        return False

def compress_static_files(static_root):
    """Compress static files using Django compressor."""
    if not shutil.which('django-admin'):
        print("Django admin command not found. Skipping compression.")
        return False
    
    print("Compressing static files...")
    
    cmd = [sys.executable, 'manage.py', 'compress', '--force']
    
    start_time = time.time()
    result = subprocess.run(cmd, capture_output=True, text=True)
    end_time = time.time()
    
    if result.returncode == 0:
        print(f"Compression completed successfully in {end_time - start_time:.2f} seconds.")
        print(result.stdout)
        return True
    else:
        print("Compression failed:")
        print(result.stderr)
        return False

def optimize_js_files(static_root):
    """Optimize JavaScript files using terser."""
    if not shutil.which('terser'):
        print("Terser not found. Skipping JS optimization.")
        print("Install with: npm install -g terser")
        return False
    
    print("Optimizing JavaScript files...")
    
    js_files = list(Path(static_root).glob('**/*.js'))
    js_files = [f for f in js_files if not f.name.endswith('.min.js')]
    
    if not js_files:
        print("No JavaScript files found to optimize.")
        return True
    
    print(f"Found {len(js_files)} JavaScript files to optimize.")
    
    start_time = time.time()
    success_count = 0
    
    for js_file in js_files:
        min_file = js_file.with_name(f"{js_file.stem}.min.js")
        
        cmd = ['terser', str(js_file), '--compress', '--mangle', '-o', str(min_file)]
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            success_count += 1
        else:
            print(f"Failed to optimize {js_file}:")
            print(result.stderr)
    
    end_time = time.time()
    
    print(f"Optimized {success_count}/{len(js_files)} JavaScript files in {end_time - start_time:.2f} seconds.")
    return success_count == len(js_files)

def optimize_css_files(static_root):
    """Optimize CSS files using csso."""
    if not shutil.which('csso'):
        print("CSSO not found. Skipping CSS optimization.")
        print("Install with: npm install -g csso-cli")
        return False
    
    print("Optimizing CSS files...")
    
    css_files = list(Path(static_root).glob('**/*.css'))
    css_files = [f for f in css_files if not f.name.endswith('.min.css')]
    
    if not css_files:
        print("No CSS files found to optimize.")
        return True
    
    print(f"Found {len(css_files)} CSS files to optimize.")
    
    start_time = time.time()
    success_count = 0
    
    for css_file in css_files:
        min_file = css_file.with_name(f"{css_file.stem}.min.css")
        
        cmd = ['csso', str(css_file), '-o', str(min_file)]
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            success_count += 1
        else:
            print(f"Failed to optimize {css_file}:")
            print(result.stderr)
    
    end_time = time.time()
    
    print(f"Optimized {success_count}/{len(css_files)} CSS files in {end_time - start_time:.2f} seconds.")
    return success_count == len(css_files)

def main():
    """Main function."""
    args = parse_arguments()
    
    # Set production environment
    set_production_environment()
    
    # Get static root from Django settings
    try:
        import django
        django.setup()
        from django.conf import settings
        static_root = settings.STATIC_ROOT
    except (ImportError, AttributeError):
        print("Failed to get STATIC_ROOT from Django settings.")
        print("Using default 'staticfiles' directory.")
        static_root = 'staticfiles'
    
    # Create static root directory if it doesn't exist
    os.makedirs(static_root, exist_ok=True)
    
    # Run collectstatic
    if not run_collectstatic(args):
        print("Collectstatic failed. Exiting.")
        return 1
    
    # Compress static files if requested
    if args.compress:
        if not compress_static_files(static_root):
            print("Compression failed. Continuing with optimization.")
    
    # Optimize static files if requested
    if args.optimize:
        js_success = optimize_js_files(static_root)
        css_success = optimize_css_files(static_root)
        
        if not js_success or not css_success:
            print("Optimization completed with errors.")
            return 1
    
    print("Static files collection completed successfully.")
    return 0

if __name__ == '__main__':
    sys.exit(main())
