/*
 * Notification Dropdown Styles
 * This file contains styles for the notification dropdown content
 */

/* Notification dropdown */
.notification-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    width: 320px;
    z-index: 1001;
}

.notification-dropdown-menu {
    position: relative;
    width: 100%;
    max-height: 400px;
    overflow-y: auto;
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    display: none;
    margin-top: 1px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    z-index: 1050 !important; /* Higher z-index to appear above other dropdowns */
    transition: all 0.3s ease;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    will-change: transform, opacity;
    transform-origin: top center;
    backface-visibility: hidden;
}

/* Show notification dropdown on hover for desktop */
@media (min-width: 992px) {
    .nav-item:hover .notification-dropdown .notification-dropdown-menu {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
        transform: translateY(0) !important;
        animation: smoothFadeIn 0.4s ease-out forwards;
    }
}

.notification-dropdown-menu.show {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) !important;
    animation: smoothFadeIn 0.4s ease-out forwards;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Smoother animation for notification dropdown */
.notification-dropdown-menu {
    transition: opacity 0.3s ease, transform 0.3s ease, visibility 0.3s ease;
}

.notification-dropdown-menu.show {
    animation: smoothFadeIn 0.4s ease-out forwards;
}

@keyframes smoothFadeIn {
    0% {
        opacity: 0;
        transform: translateY(-5px) scale(0.98);
    }
    70% {
        opacity: 1;
        transform: translateY(2px) scale(1.01);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Notification dropdown header */
.notification-dropdown-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    background-color: #f8f9fa;
}

.notification-dropdown-link {
    font-size: 0.8rem;
    color: #3a7bd5;
    text-decoration: none;
    transition: all 0.2s ease;
}

.notification-dropdown-link:hover {
    color: #1d4ed8;
    text-decoration: underline;
}

/* Notification dropdown content */
.notification-dropdown-content {
    max-height: 300px;
    overflow-y: auto;
    transition: opacity 0.3s ease;
}

/* Notification item */
.notification-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    position: relative;
}

.notification-item:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-item.unread {
    background-color: rgba(59, 130, 246, 0.05);
}

.notification-item.unread:hover {
    background-color: rgba(59, 130, 246, 0.08);
}

/* Notification actions */
.notification-item-actions {
    position: absolute;
    right: 0.75rem;
    top: 0.75rem;
}

.notification-action-toggle {
    background: rgba(0, 0, 0, 0.03);
    border: none;
    color: #6b7280;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    padding: 0;
    z-index: 10;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.notification-action-toggle:hover {
    background-color: rgba(0, 120, 212, 0.1);
    color: #0078d4;
    box-shadow: 0 1px 3px rgba(0, 120, 212, 0.2);
}

.notification-action-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    width: 180px;
    z-index: 1050; /* Higher z-index to ensure visibility */
    overflow: hidden;
    display: none;
    animation: fadeInDown 0.2s ease;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease, visibility 0.2s ease;
}

.notification-action-dropdown.show {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

.notification-action-btn {
    width: 100%;
    text-align: left;
    padding: 0.6rem 1rem;
    background: transparent;
    border: none;
    color: #4b5563;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.notification-action-btn:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.notification-action-btn.delete:hover {
    color: #dc2626;
    background-color: rgba(220, 38, 38, 0.05);
}

.notification-action-btn i {
    width: 16px;
    text-align: center;
}

/* Notification icon in dropdown */
.notification-item-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: rgba(59, 130, 246, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #3b82f6;
    flex-shrink: 0;
}

.notification-item-icon.success {
    background-color: rgba(16, 185, 129, 0.1);
    color: #10b981;
}

.notification-item-icon.warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
}

.notification-item-icon.danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

/* Notification content */
.notification-item-content {
    flex: 1;
}

.notification-item-title {
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
    color: #1f2937;
}

.notification-item-message {
    font-size: 0.8rem;
    color: #6b7280;
    margin-bottom: 0.25rem;
}

.notification-item-time {
    font-size: 0.7rem;
    color: #9ca3af;
}

/* Notification dropdown footer */
.notification-dropdown-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    background-color: #f8f9fa;
}

/* Loading state */
.notification-dropdown-loading {
    padding: 1.5rem;
    text-align: center;
    color: #6b7280;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Spinner animation */
.notification-dropdown-loading .spinner-border {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Empty state */
.notification-dropdown-empty {
    padding: 2rem 1rem;
    text-align: center;
    color: #6b7280;
}

.notification-dropdown-empty i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: #0078d4;
    opacity: 0.7;
}

.notification-dropdown-empty p {
    font-size: 0.9rem;
    margin-bottom: 0;
    font-weight: 500;
    color: #4b5563;
}
