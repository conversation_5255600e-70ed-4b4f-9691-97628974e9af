"""
MODULE 6: Management command to reset monthly scan counts for all users
This should be run monthly via cron job
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from qrcode_app.scan_alert_utils import reset_monthly_scans_for_all_users
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Reset monthly scan counts for all users'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be reset without actually resetting',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No changes will be made')
            )
            
            # Count subscriptions that would be reset
            from qrcode_app.models import Subscription
            count = Subscription.objects.count()
            
            self.stdout.write(
                self.style.SUCCESS(f'Would reset monthly scans for {count} subscriptions')
            )
        else:
            self.stdout.write('Resetting monthly scan counts...')
            
            # Reset scan counts
            reset_count = reset_monthly_scans_for_all_users()
            
            self.stdout.write(
                self.style.SUCCESS(f'Successfully reset monthly scans for {reset_count} subscriptions')
            )
            
            # Log the reset
            logger.info(f'Monthly scan reset completed: {reset_count} subscriptions reset at {timezone.now()}')
