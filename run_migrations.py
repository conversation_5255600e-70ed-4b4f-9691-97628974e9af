import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'qrcode_project.settings')
django.setup()

from django.db import connection
from django.db.migrations.executor import MigrationExecutor

# Create the migration executor
executor = MigrationExecutor(connection)

# Get the migration plan
plan = executor.migration_plan(executor.loader.graph.leaf_nodes())

# Execute the migrations
if plan:
    print(f"Executing {len(plan)} migrations...")
    executor.migrate(executor.loader.graph.leaf_nodes())
    print("Migrations completed successfully!")
else:
    print("No migrations to apply.")

# Check if the UserProfile table exists
with connection.cursor() as cursor:
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='qrcode_app_userprofile';")
    result = cursor.fetchone()
    if result:
        print("UserProfile table exists!")
    else:
        print("UserProfile table does not exist!")

# Create the UserProfile table manually if it doesn't exist
if not result:
    print("Creating UserProfile table manually...")
    with connection.cursor() as cursor:
        cursor.execute("""
        CREATE TABLE "qrcode_app_userprofile" (
            "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
            "role" varchar(20) NOT NULL,
            "company" varchar(100) NULL,
            "phone" varchar(20) NULL,
            "address" text NULL,
            "profile_image" varchar(100) NULL,
            "api_key" uuid NOT NULL,
            "created_at" datetime NOT NULL,
            "updated_at" datetime NOT NULL,
            "user_id" integer NOT NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED
        );
        """)
        cursor.execute("""
        CREATE UNIQUE INDEX "qrcode_app_userprofile_user_id_key" ON "qrcode_app_userprofile" ("user_id");
        """)
        cursor.execute("""
        CREATE UNIQUE INDEX "qrcode_app_userprofile_api_key_key" ON "qrcode_app_userprofile" ("api_key");
        """)
        print("UserProfile table created successfully!")
