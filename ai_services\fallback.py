"""
Fallback suggestions for when AI generation fails
"""
import logging
from typing import List, Dict
from .fallback_data import HARDCODED_IDEAS

logger = logging.getLogger(__name__)

def get_hardcoded_suggestions(
    user_input: str = "",
    language: str = "english",
    business_type: str = "",
    title: str = ""
) -> List[Dict[str, str]]:
    """
    Get hardcoded fallback suggestions
    
    Args:
        user_input: The user input to generate suggestions for
        language: The language to generate suggestions in
        business_type: The type of business
        title: The title to use for the suggestions
        
    Returns:
        A list of hardcoded suggestions
    """
    logger.info(f"Using hardcoded fallback suggestions for: {user_input[:50]}")
    
    # Get the default ideas
    ideas = HARDCODED_IDEAS.get("default", [])
    
    # Convert the simple strings to the expected suggestion format
    fallback_suggestions = []
    for i, idea in enumerate(ideas):
        suggestion_title = title if i == 0 and title else f"Idea {i+1}"
        
        fallback_suggestions.append({
            "title": suggestion_title,
            "content": idea,
            "model": "offline",
            "ai_generated": False,
            "fallback": True
        })
    
    logger.info(f"Generated {len(fallback_suggestions)} hardcoded suggestions")
    return fallback_suggestions
