{% extends "base.html" %}
{% load static %}

{% block title %}Batch Details - {{ batch.name }} - Enterprise QR{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'qrcode_app/css/batch-processing.css' %}">
{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'index' %}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'batch_processing' %}">Batch Processing</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ batch.name }}</li>
                </ol>
            </nav>
            <h1 class="h3">
                <i class="fas fa-layer-group me-2"></i>{{ batch.name }}
            </h1>
        </div>
        <div class="col-md-4 text-md-end">
            <div class="btn-group">
                {% if batch.zip_file %}
                <a href="{{ batch.zip_file.url }}" class="btn btn-primary">
                    <i class="fas fa-download me-2"></i>Download All
                </a>
                {% endif %}
                <button class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteBatchModal">
                    <i class="fas fa-trash me-2"></i>Delete
                </button>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-lg-4 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h2 class="h5 mb-0">Batch Information</h2>
                </div>
                <div class="card-body">
                    <table class="table">
                        <tbody>
                            <tr>
                                <th>Name</th>
                                <td>{{ batch.name }}</td>
                            </tr>
                            <tr>
                                <th>Description</th>
                                <td>{{ batch.description|default:"-" }}</td>
                            </tr>
                            <tr>
                                <th>Created</th>
                                <td>{{ batch.created_at }}</td>
                            </tr>
                            <tr>
                                <th>Status</th>
                                <td>
                                    {% if batch.zip_file %}
                                    <span class="badge bg-success">Completed</span>
                                    {% else %}
                                    <span class="badge bg-warning">Processing</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>QR Codes</th>
                                <td>{{ batch.count }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            {% if batch.zip_file %}
            <div class="card shadow-sm mt-4">
                <div class="card-header bg-white">
                    <h2 class="h5 mb-0">Download Options</h2>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ batch.zip_file.url }}" class="btn btn-primary">
                            <i class="fas fa-file-archive me-2"></i>Download ZIP
                        </a>
                        <button class="btn btn-outline-secondary">
                            <i class="fas fa-file-pdf me-2"></i>Download PDF
                        </button>
                        <button class="btn btn-outline-secondary">
                            <i class="fas fa-print me-2"></i>Print All
                        </button>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
        
        <div class="col-lg-8 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h2 class="h5 mb-0">QR Codes in Batch</h2>
                        <div class="input-group" style="width: 250px;">
                            <input type="text" class="form-control" placeholder="Search QR codes...">
                            <button class="btn btn-outline-secondary" type="button">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>QR Code</th>
                                    <th>Name</th>
                                    <th>Type</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for qr_code in batch.qrcodes.all %}
                                <tr>
                                    <td>
                                        <img src="{{ qr_code.image.url }}" alt="{{ qr_code.name }}" class="qr-thumbnail">
                                    </td>
                                    <td>{{ qr_code.name }}</td>
                                    <td><span class="badge bg-info">{{ qr_code.get_qr_type_display }}</span></td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{% url 'qr_code_detail' qr_code.pk %}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ qr_code.image.url }}" download class="btn btn-sm btn-outline-secondary">
                                                <i class="fas fa-download"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center py-4">
                                        <p class="text-muted mb-0">No QR codes found in this batch.</p>
                                        {% if not batch.zip_file %}
                                        <p class="text-muted mb-0">The batch is still being processed.</p>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                {% if batch.qrcodes.count > 10 %}
                <div class="card-footer bg-white">
                    <nav aria-label="QR code pagination">
                        <ul class="pagination justify-content-center mb-0">
                            <li class="page-item disabled">
                                <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Previous</a>
                            </li>
                            <li class="page-item active"><a class="page-link" href="#">1</a></li>
                            <li class="page-item"><a class="page-link" href="#">2</a></li>
                            <li class="page-item"><a class="page-link" href="#">3</a></li>
                            <li class="page-item">
                                <a class="page-link" href="#">Next</a>
                            </li>
                        </ul>
                    </nav>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Delete Batch Modal -->
<div class="modal fade" id="deleteBatchModal" tabindex="-1" aria-labelledby="deleteBatchModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteBatchModalLabel">Confirm Deletion</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this batch? This action cannot be undone.</p>
                <p><strong>Note:</strong> All QR codes in this batch will also be deleted.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="post" action="#">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="delete">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'qrcode_app/js/batch-processor.js' %}"></script>
{% endblock %}
