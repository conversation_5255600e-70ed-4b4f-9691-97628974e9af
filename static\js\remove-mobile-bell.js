/**
 * Remove Mobile Notification Bell
 * This script removes the notification bell from the mobile bottom navigation
 */

document.addEventListener('DOMContentLoaded', function() {
    // Function to remove notification bell from mobile bottom navigation
    function removeMobileNotificationBell() {
        // Find the mobile notification bell
        const mobileBell = document.querySelector('.mobile-bell');
        if (mobileBell) {
            console.log('Removing mobile notification bell');
            mobileBell.remove();
        }
        
        // Also remove any notification bell from the bottom navigation
        const bottomNavBell = document.querySelector('.enterprise-bottom-nav .bottom-nav-item.notification-bell');
        if (bottomNavBell) {
            console.log('Removing notification bell from bottom navigation');
            bottomNavBell.remove();
        }
    }
    
    // Run immediately
    removeMobileNotificationBell();
    
    // Also run after a short delay to catch any dynamically added bells
    setTimeout(removeMobileNotificationBell, 500);
    
    // Run again after mobile navigation is initialized
    setTimeout(removeMobileNotificationBell, 1000);
    
    // Also observe DOM changes to catch any dynamically added bells
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes.length) {
                removeMobileNotificationBell();
            }
        });
    });
    
    // Start observing the body for added nodes
    observer.observe(document.body, { childList: true, subtree: true });
});
