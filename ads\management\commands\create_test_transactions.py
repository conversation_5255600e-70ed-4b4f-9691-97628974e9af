from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from ads.models import Ad, Transaction
import random

class Command(BaseCommand):
    help = 'Creates test transactions for existing ads'

    def handle(self, *args, **options):
        # Get the first user
        user = User.objects.first()
        if not user:
            self.stdout.write(self.style.ERROR('No users found in the database'))
            return

        # Get all ads
        ads = Ad.objects.all()
        if not ads:
            self.stdout.write(self.style.ERROR('No ads found in the database'))
            return

        # Payment gateways
        payment_gateways = ['mpesa', 'card', 'bank', 'paypal']
        
        # Statuses
        statuses = ['pending', 'paid', 'failed', 'refunded']
        
        # Create transactions
        transactions_created = 0
        for ad in ads:
            # Create 1-3 transactions per ad
            for _ in range(random.randint(1, 3)):
                # Determine status
                status = random.choice(statuses)
                
                # Create transaction
                transaction = Transaction.objects.create(
                    user=user,
                    ad=ad,
                    amount=ad.final_pricing or random.randint(500, 5000),
                    status=status,
                    payment_gateway=random.choice(payment_gateways),
                    transaction_id=f'{random.choice(payment_gateways).upper()}{ad.id}{random.randint(100000, 999999)}',
                    timestamp=timezone.now() - timezone.timedelta(days=random.randint(0, 30))
                )
                transactions_created += 1
                self.stdout.write(self.style.SUCCESS(f'Created transaction {transaction.id} for ad: {ad.title} (Status: {status})'))
        
        self.stdout.write(self.style.SUCCESS(f'Successfully created {transactions_created} test transactions'))
