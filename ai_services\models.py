"""
AI Services Models
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
import datetime
import json

class CachedSuggestion(models.Model):
    """
    Model for caching AI-generated suggestions
    """
    prompt_hash = models.CharField(max_length=32, primary_key=True)
    content = models.TextField()
    created_at = models.DateTimeField(default=timezone.now)
    expires_at = models.DateTimeField()

    class Meta:
        db_table = 'ai_cached_suggestions'
        verbose_name = 'Cached Suggestion'
        verbose_name_plural = 'Cached Suggestions'
        indexes = [
            models.Index(fields=['expires_at']),
        ]

    def __str__(self):
        return f"Cache: {self.prompt_hash[:8]}... ({self.created_at.strftime('%Y-%m-%d %H:%M')})"

    @classmethod
    def get_suggestion(cls, prompt_hash):
        """
        Get a cached suggestion if it exists and is not expired

        Args:
            prompt_hash: The hash of the prompt

        Returns:
            The cached suggestion content or None
        """
        try:
            now = timezone.now()
            suggestion = cls.objects.get(prompt_hash=prompt_hash, expires_at__gt=now)
            return json.loads(suggestion.content)
        except cls.DoesNotExist:
            return None
        except json.JSONDecodeError:
            return None

    @classmethod
    def save_suggestion(cls, prompt_hash, content, ttl_hours=6):
        """
        Save a suggestion to the cache

        Args:
            prompt_hash: The hash of the prompt
            content: The suggestion content
            ttl_hours: Time to live in hours

        Returns:
            The created or updated CachedSuggestion instance
        """
        now = timezone.now()
        expires_at = now + datetime.timedelta(hours=ttl_hours)

        # Convert content to JSON string if it's not already a string
        if not isinstance(content, str):
            content = json.dumps(content)

        suggestion, created = cls.objects.update_or_create(
            prompt_hash=prompt_hash,
            defaults={
                'content': content,
                'created_at': now,
                'expires_at': expires_at
            }
        )

        return suggestion

    @classmethod
    def clear_expired(cls):
        """
        Clear expired suggestions

        Returns:
            Number of deleted suggestions
        """
        now = timezone.now()
        expired = cls.objects.filter(expires_at__lte=now)
        count = expired.count()
        expired.delete()
        return count
