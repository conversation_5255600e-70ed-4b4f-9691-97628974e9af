{% extends 'base.html' %}
{% load static %}

{% block title %}Branding Management{% endblock %}

{% block extra_css %}
<style>
    .branding-management {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .branding-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .branding-header {
        text-align: center;
        color: white;
        margin-bottom: 3rem;
    }

    .branding-header h1 {
        font-size: 3rem;
        font-weight: bold;
        margin-bottom: 1rem;
    }

    .brand-item {
        border: 1px solid #dee2e6;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .brand-item:hover {
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }

    .brand-preview {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .brand-logo {
        width: 50px;
        height: 50px;
        border-radius: 8px;
        object-fit: cover;
    }

    .color-swatch {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        border: 2px solid #fff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .coming-soon {
        text-align: center;
        padding: 3rem;
        color: #6c757d;
    }

    .coming-soon i {
        font-size: 4rem;
        margin-bottom: 1rem;
        color: #667eea;
    }

    /* Enhanced Branding Management Styles */
    .branding-header-enhanced {
        background: linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 249, 250, 0.9) 100%);
        border-radius: 25px;
        padding: 3rem;
        margin-bottom: 3rem;
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(20px);
        position: relative;
        overflow: hidden;
    }

    .header-content {
        display: flex;
        align-items: center;
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .header-icon {
        position: relative;
        width: 80px;
        height: 80px;
        background: linear-gradient(145deg, #667eea, #764ba2);
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2.5rem;
        color: white;
        box-shadow: 0 15px 30px rgba(102, 126, 234, 0.3);
    }

    .icon-glow {
        position: absolute;
        inset: -3px;
        background: conic-gradient(from 0deg, #667eea, #764ba2, #667eea);
        border-radius: 23px;
        z-index: -1;
        opacity: 0.7;
        animation: iconRotate 4s linear infinite;
    }

    @keyframes iconRotate {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .header-text h1 {
        font-size: 3rem;
        font-weight: 900;
        margin: 0 0 0.5rem 0;
        background: linear-gradient(135deg, #1a1a2e 0%, #667eea 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .header-text p {
        font-size: 1.2rem;
        color: #6b7280;
        margin: 0;
        font-weight: 500;
    }

    .brand-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 2rem;
    }

    .stat-item {
        text-align: center;
        padding: 1.5rem;
        background: rgba(102, 126, 234, 0.1);
        border-radius: 15px;
        border: 1px solid rgba(102, 126, 234, 0.2);
        transition: all 0.3s ease;
    }

    .stat-item:hover {
        background: rgba(102, 126, 234, 0.15);
        transform: translateY(-3px);
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 900;
        color: #667eea;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        font-size: 0.9rem;
        color: #6b7280;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .brand-themes-section {
        background: linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 249, 250, 0.9) 100%);
        border-radius: 25px;
        padding: 3rem;
        margin-bottom: 3rem;
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(20px);
    }

    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 3rem;
        flex-wrap: wrap;
        gap: 2rem;
    }

    .section-title h3 {
        font-size: 2rem;
        font-weight: 800;
        color: #1a1a2e;
        margin: 0 0 0.5rem 0;
    }

    .section-title p {
        color: #6b7280;
        margin: 0;
        font-size: 1.1rem;
    }

    .btn-create-theme {
        position: relative;
        display: inline-flex;
        align-items: center;
        gap: 0.75rem;
        padding: 1rem 2rem;
        background: linear-gradient(145deg, #667eea, #764ba2);
        color: white;
        border: none;
        border-radius: 15px;
        font-weight: 700;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        overflow: hidden;
        box-shadow: 0 15px 30px rgba(102, 126, 234, 0.3);
    }

    .btn-create-theme:hover {
        transform: translateY(-3px);
        box-shadow: 0 20px 40px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .btn-shine {
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        transition: left 0.6s ease;
    }

    .btn-create-theme:hover .btn-shine {
        left: 100%;
    }

    .brand-themes-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 2rem;
    }

    .brand-theme-card {
        background: linear-gradient(145deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 249, 250, 0.8) 100%);
        border-radius: 20px;
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        overflow: hidden;
        backdrop-filter: blur(10px);
    }

    .brand-theme-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    }

    .brand-theme-card .card-header {
        padding: 2rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        gap: 1.5rem;
    }

    .brand-logo-container {
        flex-shrink: 0;
    }

    .brand-logo {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        object-fit: cover;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    }

    .brand-logo-placeholder {
        width: 60px;
        height: 60px;
        background: linear-gradient(145deg, #f3f4f6, #e5e7eb);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #9ca3af;
        font-size: 1.5rem;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    }

    .brand-info {
        flex: 1;
    }

    .brand-name {
        font-size: 1.3rem;
        font-weight: 700;
        color: #1a1a2e;
        margin: 0 0 0.25rem 0;
    }

    .brand-company {
        color: #6b7280;
        margin: 0 0 0.25rem 0;
        font-size: 0.95rem;
    }

    .brand-tagline {
        color: #9ca3af;
        margin: 0;
        font-size: 0.85rem;
        font-style: italic;
    }

    .brand-theme-card .card-body {
        padding: 2rem;
    }

    .color-palette-section {
        margin-bottom: 1.5rem;
    }

    .color-palette-section h6 {
        font-size: 0.9rem;
        font-weight: 700;
        color: #374151;
        margin-bottom: 1rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .color-swatches {
        display: flex;
        gap: 1rem;
    }

    .color-swatches .color-swatch {
        position: relative;
        width: 50px;
        height: 50px;
        border-radius: 12px;
        border: 2px solid rgba(255, 255, 255, 0.8);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .color-swatches .color-swatch:hover {
        transform: scale(1.1);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    }

    .color-label {
        position: absolute;
        bottom: -25px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 0.7rem;
        color: #6b7280;
        font-weight: 600;
        white-space: nowrap;
    }

    .theme-style-section {
        margin-bottom: 1.5rem;
    }

    .theme-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .theme-minimal {
        background: linear-gradient(145deg, #f3f4f6, #e5e7eb);
        color: #374151;
    }

    .theme-corporate {
        background: linear-gradient(145deg, #dbeafe, #bfdbfe);
        color: #1e40af;
    }

    .theme-modern {
        background: linear-gradient(145deg, #fce7f3, #fbcfe8);
        color: #be185d;
    }

    .theme-custom {
        background: linear-gradient(145deg, #fef3c7, #fde68a);
        color: #92400e;
    }

    .brand-preview-section {
        margin-bottom: 1.5rem;
    }

    .preview-mockup {
        border-radius: 12px;
        padding: 1rem;
        min-height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .preview-content {
        border-radius: 8px;
        padding: 1rem;
        text-align: center;
        width: 100%;
    }

    .preview-header {
        font-weight: 700;
        font-size: 0.9rem;
        margin-bottom: 0.25rem;
    }

    .preview-text {
        font-size: 0.8rem;
        opacity: 0.8;
    }

    .card-actions {
        padding: 1.5rem 2rem;
        border-top: 1px solid rgba(0, 0, 0, 0.1);
        display: flex;
        gap: 0.75rem;
        flex-wrap: wrap;
    }

    .btn-action {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1rem;
        border: none;
        border-radius: 10px;
        font-size: 0.85rem;
        font-weight: 600;
        transition: all 0.3s ease;
        cursor: pointer;
        text-decoration: none;
    }

    .btn-edit {
        background: linear-gradient(145deg, #dbeafe, #bfdbfe);
        color: #1e40af;
    }

    .btn-edit:hover {
        background: linear-gradient(145deg, #bfdbfe, #93c5fd);
        transform: translateY(-2px);
        color: #1e40af;
    }

    .btn-preview {
        background: linear-gradient(145deg, #d1fae5, #a7f3d0);
        color: #065f46;
    }

    .btn-preview:hover {
        background: linear-gradient(145deg, #a7f3d0, #6ee7b7);
        transform: translateY(-2px);
        color: #065f46;
    }

    .btn-delete {
        background: linear-gradient(145deg, #fee2e2, #fecaca);
        color: #991b1b;
    }

    .btn-delete:hover {
        background: linear-gradient(145deg, #fecaca, #fca5a5);
        transform: translateY(-2px);
        color: #991b1b;
    }

    /* Empty State */
    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        background: linear-gradient(145deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
        border-radius: 20px;
        border: 2px dashed rgba(102, 126, 234, 0.3);
    }

    .empty-icon {
        position: relative;
        width: 100px;
        height: 100px;
        background: linear-gradient(145deg, #667eea, #764ba2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 3rem;
        color: white;
        margin: 0 auto 2rem;
        box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
    }

    .icon-pulse {
        position: absolute;
        inset: -10px;
        border-radius: 50%;
        background: rgba(102, 126, 234, 0.3);
        animation: pulse 2s ease-in-out infinite;
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); opacity: 0.7; }
        50% { transform: scale(1.1); opacity: 0.3; }
    }

    .empty-state h4 {
        font-size: 2rem;
        font-weight: 800;
        color: #1a1a2e;
        margin-bottom: 1rem;
    }

    .empty-state p {
        font-size: 1.1rem;
        color: #6b7280;
        margin-bottom: 2rem;
    }

    .empty-features {
        display: flex;
        justify-content: center;
        gap: 2rem;
        margin-bottom: 3rem;
        flex-wrap: wrap;
    }

    .feature-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #667eea;
        font-weight: 600;
    }

    .btn-create-first {
        position: relative;
        display: inline-flex;
        align-items: center;
        gap: 0.75rem;
        padding: 1.25rem 2.5rem;
        background: linear-gradient(145deg, #667eea, #764ba2);
        color: white;
        border: none;
        border-radius: 15px;
        font-weight: 700;
        font-size: 1.2rem;
        transition: all 0.3s ease;
        overflow: hidden;
        box-shadow: 0 15px 30px rgba(102, 126, 234, 0.3);
    }

    .btn-create-first:hover {
        transform: translateY(-3px);
        box-shadow: 0 25px 50px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .btn-glow {
        position: absolute;
        inset: -2px;
        background: linear-gradient(45deg, rgba(102, 126, 234, 0.5), rgba(118, 75, 162, 0.5));
        border-radius: 17px;
        z-index: -1;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .btn-create-first:hover .btn-glow {
        opacity: 1;
    }
</style>
{% endblock %}

{% block content %}
<div class="branding-management">
    <div class="container">
        <!-- Enhanced Header -->
        <div class="branding-header-enhanced">
            <div class="header-content">
                <div class="header-icon">
                    <i class="fas fa-palette"></i>
                    <div class="icon-glow"></div>
                </div>
                <div class="header-text">
                    <h1>Brand Management Studio</h1>
                    <p>Create stunning, professional QR code landing pages that reflect your brand identity</p>
                </div>
            </div>

            <!-- Brand Stats -->
            <div class="brand-stats">
                <div class="stat-item">
                    <div class="stat-number">{{ brandings.count }}</div>
                    <div class="stat-label">Brand Themes</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">∞</div>
                    <div class="stat-label">Customizations</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">100%</div>
                    <div class="stat-label">White Label</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Brand Themes Section -->
        <div class="brand-themes-section">
            <div class="section-header">
                <div class="section-title">
                    <h3><i class="fas fa-brush me-2"></i>Your Brand Themes</h3>
                    <p>Manage your custom brand themes and create new ones</p>
                </div>
                <button class="btn-create-theme">
                    <i class="fas fa-plus"></i>
                    <span>Create New Theme</span>
                    <div class="btn-shine"></div>
                </button>
            </div>

            <div class="brand-themes-grid">
                {% if brandings %}
                    {% for branding in brandings %}
                    <div class="brand-theme-card">
                        <div class="card-header">
                            <div class="brand-logo-container">
                                {% if branding.get_logo_url %}
                                <img src="{{ branding.get_logo_url }}" alt="{{ branding.name }}" class="brand-logo">
                                {% else %}
                                <div class="brand-logo-placeholder">
                                    <i class="fas fa-image"></i>
                                </div>
                                {% endif %}
                            </div>
                            <div class="brand-info">
                                <h4 class="brand-name">{{ branding.name }}</h4>
                                <p class="brand-company">{{ branding.company_name|default:"No company name" }}</p>
                                {% if branding.tagline %}
                                <p class="brand-tagline">{{ branding.tagline }}</p>
                                {% endif %}
                            </div>
                        </div>

                        <div class="card-body">
                            <!-- Color Palette -->
                            <div class="color-palette-section">
                                <h6>Color Palette</h6>
                                <div class="color-swatches">
                                    <div class="color-swatch" style="background-color: {{ branding.primary_color }};" title="Primary: {{ branding.primary_color }}">
                                        <span class="color-label">Primary</span>
                                    </div>
                                    <div class="color-swatch" style="background-color: {{ branding.secondary_color }};" title="Secondary: {{ branding.secondary_color }}">
                                        <span class="color-label">Secondary</span>
                                    </div>
                                    <div class="color-swatch" style="background-color: {{ branding.accent_color }};" title="Accent: {{ branding.accent_color }}">
                                        <span class="color-label">Accent</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Theme Style -->
                            <div class="theme-style-section">
                                <span class="theme-badge theme-{{ branding.theme_style }}">
                                    <i class="fas fa-paint-brush"></i>
                                    {{ branding.get_theme_style_display }}
                                </span>
                            </div>

                            <!-- Preview -->
                            <div class="brand-preview-section">
                                <div class="preview-mockup" style="background: linear-gradient(135deg, {{ branding.primary_color }}, {{ branding.accent_color }});">
                                    <div class="preview-content" style="background-color: {{ branding.secondary_color }}; color: {{ branding.primary_color }};">
                                        <div class="preview-header">{{ branding.company_name|default:"Your Company" }}</div>
                                        <div class="preview-text">Landing Page Preview</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card-actions">
                            <button class="btn-action btn-edit">
                                <i class="fas fa-edit"></i>
                                <span>Edit Theme</span>
                            </button>
                            <button class="btn-action btn-preview">
                                <i class="fas fa-eye"></i>
                                <span>Preview</span>
                            </button>
                            <button class="btn-action btn-delete">
                                <i class="fas fa-trash"></i>
                                <span>Delete</span>
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-palette"></i>
                        <div class="icon-pulse"></div>
                    </div>
                    <h4>Create Your First Brand Theme</h4>
                    <p>Start building your brand identity with custom themes for your QR code landing pages</p>
                    <div class="empty-features">
                        <div class="feature-item">
                            <i class="fas fa-paint-brush"></i>
                            <span>Custom Colors & Logos</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-mobile-alt"></i>
                            <span>Mobile-Responsive Design</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-eye"></i>
                            <span>Live Preview</span>
                        </div>
                    </div>
                    <button class="btn-create-first">
                        <i class="fas fa-plus"></i>
                        <span>Create Your First Theme</span>
                        <div class="btn-glow"></div>
                    </button>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Features Info -->
        <div class="branding-card">
            <h4><i class="fas fa-star me-2"></i>Branding Features</h4>
            <div class="row">
                <div class="col-md-6">
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Custom logos and colors</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Multiple theme styles</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Custom CSS support</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Company branding</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>White-label solutions</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Professional landing pages</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Back to Dashboard -->
        <div class="text-center">
            <a href="{% url 'enterprise_dashboard' %}" class="btn btn-outline-light btn-lg">
                <i class="fas fa-arrow-left me-2"></i>Back to Enterprise Dashboard
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Placeholder for branding management functionality
console.log('Branding management loaded');
</script>
{% endblock %}
