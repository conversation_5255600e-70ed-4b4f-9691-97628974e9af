/**
 * QR Code Generation Web Worker
 * Enterprise QR Code Generator
 * 
 * This worker handles QR code generation in a separate thread
 * to prevent blocking the main UI thread.
 */

// Import QR code library (using importScripts for Web Worker)
importScripts('https://cdn.jsdelivr.net/npm/qrcode@1.5.1/build/qrcode.min.js');

// Listen for messages from the main thread
self.addEventListener('message', function(e) {
    const { action, data, options, qrKey } = e.data;
    
    if (action === 'generate') {
        try {
            generateQRCode(data, options, qrKey);
        } catch (error) {
            self.postMessage({
                error: error.message,
                qrKey: qrKey
            });
        }
    }
});

/**
 * Generate a QR code
 * @param {string} data - QR code data
 * @param {Object} options - QR code options
 * @param {string} qrKey - Cache key for the QR code
 */
function generateQRCode(data, options, qrKey) {
    // Default options
    const qrOptions = {
        errorCorrectionLevel: options.errorCorrectionLevel || 'H',
        margin: options.margin || 4,
        scale: options.scale || 10,
        color: {
            dark: options.foregroundColor || '#000000',
            light: options.backgroundColor || '#ffffff'
        },
        width: options.width || 300
    };
    
    // Generate QR code as data URL
    QRCode.toDataURL(data, qrOptions, function(error, dataURL) {
        if (error) {
            self.postMessage({
                error: error.message,
                qrKey: qrKey
            });
            return;
        }
        
        // Send the data URL back to the main thread
        self.postMessage({
            dataURL: dataURL,
            qrKey: qrKey
        });
    });
}
