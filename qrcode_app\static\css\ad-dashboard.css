/* Ad Dashboard Styles */
:root {
    --sidebar-width: 250px;
    --header-height: 70px;
    --primary-blue: #3b82f6;
    --primary-dark-blue: #1d4ed8;
    --purple: #8b5cf6;
    --green: #10b981;
    --orange: #f59e0b;
    --red: #ef4444;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
}

.app-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.dashboard-container {
    display: flex;
    flex: 1;
    background-color: var(--gray-50);
}

/* Sidebar Styles */
.dashboard-sidebar {
    width: var(--sidebar-width);
    background-color: white;
    border-right: 1px solid var(--gray-200);
    display: flex;
    flex-direction: column;
    height: calc(100vh - var(--header-height));
    position: sticky;
    top: var(--header-height);
    overflow-y: auto;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--gray-200);
}

.sidebar-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
}

.sidebar-menu {
    list-style: none;
    padding: 1rem 0;
    margin: 0;
}

.sidebar-item {
    margin-bottom: 0.25rem;
}

.sidebar-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: var(--gray-600);
    text-decoration: none;
    transition: all 0.2s ease;
    font-size: 0.9375rem;
}

.sidebar-link i {
    margin-right: 0.75rem;
    font-size: 1.125rem;
    width: 1.25rem;
    text-align: center;
}

.sidebar-link:hover {
    background-color: var(--gray-100);
    color: var(--primary-blue);
}

.sidebar-item.active .sidebar-link {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--primary-blue);
    font-weight: 500;
    border-left: 3px solid var(--primary-blue);
}

.sidebar-footer {
    margin-top: auto;
    padding: 1.5rem;
    border-top: 1px solid var(--gray-200);
}

.help-link {
    display: flex;
    align-items: center;
    color: var(--gray-600);
    text-decoration: none;
    font-size: 0.875rem;
}

.help-link i {
    margin-right: 0.5rem;
    font-size: 1rem;
}

.help-link:hover {
    color: var(--primary-blue);
}

/* Main Content Styles */
.dashboard-content {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.dashboard-header h1 {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--gray-800);
    margin: 0;
}

.dashboard-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.date-range-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background-color: white;
    border: 1px solid var(--gray-300);
    border-radius: 0.375rem;
    font-size: 0.875rem;
    color: var(--gray-700);
    cursor: pointer;
}

.date-range-selector:hover {
    border-color: var(--primary-blue);
}

.primary-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: linear-gradient(135deg, var(--primary-blue), var(--primary-dark-blue));
    color: white;
    border: none;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.primary-btn:hover {
    box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.3);
    transform: translateY(-1px);
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background-color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
}

.stat-icon.blue {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.stat-icon.purple {
    background: linear-gradient(135deg, #8b5cf6, #6d28d9);
}

.stat-icon.green {
    background: linear-gradient(135deg, #10b981, #059669);
}

.stat-icon.orange {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.stat-content h3 {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--gray-500);
    margin: 0 0 0.5rem 0;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-800);
    margin: 0 0 0.5rem 0;
}

.stat-change {
    font-size: 0.875rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.stat-change.positive {
    color: var(--green);
}

.stat-change.negative {
    color: var(--red);
}

.stat-change span {
    color: var(--gray-500);
    font-weight: 400;
}

/* Chart Container */
.chart-container {
    background-color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.chart-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
}

.chart-controls {
    display: flex;
    gap: 0.5rem;
}

.chart-control {
    padding: 0.375rem 0.75rem;
    background-color: var(--gray-100);
    border: 1px solid var(--gray-200);
    border-radius: 0.375rem;
    font-size: 0.75rem;
    color: var(--gray-600);
    cursor: pointer;
    transition: all 0.2s ease;
}

.chart-control:hover {
    background-color: var(--gray-200);
}

.chart-control.active {
    background-color: var(--primary-blue);
    color: white;
    border-color: var(--primary-blue);
}

.chart-wrapper {
    height: 300px;
}

/* Campaigns Table */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.section-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
}

.view-all-btn {
    font-size: 0.875rem;
    color: var(--primary-blue);
    background: none;
    border: none;
    cursor: pointer;
}

.view-all-btn:hover {
    text-decoration: underline;
}

.campaigns-table {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th {
    text-align: left;
    padding: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--gray-500);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    background-color: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
}

td {
    padding: 1rem;
    font-size: 0.875rem;
    color: var(--gray-700);
    border-bottom: 1px solid var(--gray-200);
}

tr:last-child td {
    border-bottom: none;
}

.campaign-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.campaign-icon {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    background-color: var(--gray-100);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-blue);
}

.campaign-name {
    font-weight: 500;
    color: var(--gray-800);
    margin: 0 0 0.25rem 0;
}

.campaign-date {
    font-size: 0.75rem;
    color: var(--gray-500);
    margin: 0;
}

.status-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-badge.active {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--green);
}

.status-badge.paused {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--orange);
}

.budget-spent {
    font-size: 0.75rem;
    color: var(--gray-500);
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    width: 28px;
    height: 28px;
    border-radius: 6px;
    background-color: var(--gray-100);
    border: none;
    color: var(--gray-600);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background-color: var(--gray-200);
    color: var(--gray-800);
}

/* Responsive Styles */
@media (max-width: 1200px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 992px) {
    .dashboard-sidebar {
        width: 64px;
    }
    
    .sidebar-link span,
    .help-link span,
    .sidebar-header h3 {
        display: none;
    }
    
    .sidebar-link i {
        margin-right: 0;
    }
    
    .sidebar-header,
    .sidebar-link {
        display: flex;
        justify-content: center;
        padding: 1rem;
    }
    
    .sidebar-item.active .sidebar-link {
        border-left: none;
        border-left: 3px solid var(--primary-blue);
    }
}

@media (max-width: 768px) {
    .dashboard-container {
        flex-direction: column;
    }
    
    .dashboard-sidebar {
        width: 100%;
        height: auto;
        position: static;
    }
    
    .sidebar-menu {
        display: flex;
        overflow-x: auto;
        padding: 0.5rem;
    }
    
    .sidebar-item {
        margin-bottom: 0;
        margin-right: 0.5rem;
    }
    
    .sidebar-link {
        padding: 0.5rem 1rem;
        border-radius: 0.375rem;
    }
    
    .sidebar-link span {
        display: none;
    }
    
    .sidebar-header,
    .sidebar-footer {
        display: none;
    }
    
    .dashboard-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .campaigns-table {
        overflow-x: auto;
    }
    
    table {
        min-width: 800px;
    }
}
