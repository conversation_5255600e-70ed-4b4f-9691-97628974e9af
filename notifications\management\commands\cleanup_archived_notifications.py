from django.core.management.base import BaseCommand
from django.utils import timezone
from notifications.models import Notification


class Command(BaseCommand):
    help = 'Deletes archived notifications that are older than 30 days'

    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=30,
            help='Number of days to keep archived notifications (default: 30)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Perform a dry run without actually deleting notifications'
        )

    def handle(self, *args, **options):
        days = options['days']
        dry_run = options['dry_run']
        
        # Calculate the cutoff date
        cutoff_date = timezone.now() - timezone.timedelta(days=days)
        
        # Get notifications to delete
        notifications = Notification.objects.filter(
            is_archived=True,
            archived_at__lt=cutoff_date,
            is_deleted=False
        )
        
        count = notifications.count()
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING(
                    f'[DRY RUN] Would delete {count} archived notifications older than {days} days'
                )
            )
            
            if count > 0:
                # Show sample of notifications that would be deleted
                sample = notifications[:5]
                self.stdout.write(self.style.WARNING('Sample of notifications that would be deleted:'))
                for notification in sample:
                    self.stdout.write(
                        self.style.WARNING(
                            f'- ID: {notification.id}, Title: {notification.title}, '
                            f'Archived at: {notification.archived_at}'
                        )
                    )
                
                if count > 5:
                    self.stdout.write(self.style.WARNING(f'... and {count - 5} more'))
        else:
            # Actually delete the notifications
            notifications.update(is_deleted=True)
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully deleted {count} archived notifications older than {days} days'
                )
            )
