{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_list %}

{% block extrastyle %}
  {{ block.super }}
  <style>
    .card {
      margin-bottom: 20px;
      border-radius: 4px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
    }
    .card-header {
      padding: 15px;
      border-bottom: 1px solid #e9ecef;
      background-color: #f8f9fa;
      font-weight: bold;
    }
    .card-body {
      padding: 15px;
    }
    .badge {
      display: inline-block;
      padding: 0.25em 0.4em;
      font-size: 75%;
      font-weight: 700;
      line-height: 1;
      text-align: center;
      white-space: nowrap;
      vertical-align: baseline;
      border-radius: 0.25rem;
    }
    .bg-primary {
      background-color: #007bff;
      color: white;
    }
    .bg-success {
      background-color: #28a745;
      color: white;
    }
    .bg-info {
      background-color: #17a2b8;
      color: white;
    }
    .bg-dark {
      background-color: #343a40;
      color: white;
    }
    .text-white {
      color: white;
    }
    .text-muted {
      color: #6c757d;
    }
    .row {
      display: flex;
      flex-wrap: wrap;
      margin-right: -15px;
      margin-left: -15px;
    }
    .col-md-6 {
      position: relative;
      width: 100%;
      padding-right: 15px;
      padding-left: 15px;
    }
    @media (min-width: 768px) {
      .col-md-6 {
        flex: 0 0 50%;
        max-width: 50%;
      }
    }
    .mt-4 {
      margin-top: 1.5rem;
    }
    .alert {
      position: relative;
      padding: 0.75rem 1.25rem;
      margin-bottom: 1rem;
      border: 1px solid transparent;
      border-radius: 0.25rem;
    }
    .alert-info {
      color: #0c5460;
      background-color: #d1ecf1;
      border-color: #bee5eb;
    }
    pre {
      display: block;
      font-size: 87.5%;
      color: #212529;
      background-color: #f8f9fa;
      padding: 1rem;
      border-radius: 0.25rem;
      overflow: auto;
    }
  </style>
{% endblock %}

{% block content %}
<div id="content-main">
    <div class="module" id="changelist">
        <div class="results">
            <div class="card">
                <div class="card-header">
                    <h3>AI Services Settings</h3>
                    <div style="float: right;">
                        <a href="{% url 'ai_services:admin_status' %}" class="button">
                            <i class="fas fa-heartbeat"></i> Check AI Provider Status
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <h4>Current AI Provider:
                        <span class="badge
                            {% if ai_provider == 'mistral' %}bg-primary
                            {% elif ai_provider == 'openai' %}bg-success
                            {% elif ai_provider == 'local' %}bg-dark
                            {% else %}bg-info{% endif %}">
                            {{ ai_provider|title }}
                        </span>
                    </h4>

                    <div class="row mt-4">
                        <div class="col-md-3">
                            <div class="card">
                                <div class="card-header bg-dark text-white">
                                    <h5>Local AI Engine</h5>
                                </div>
                                <div class="card-body">
                                    <p><strong>URL:</strong> {{ ai_engine_url|default:"http://localhost:8001" }}</p>
                                    <p><strong>API Token:</strong> {{ ai_engine_api_token|default:"Not configured" }}</p>
                                    <p><small class="text-muted">Self-hosted models: mistral-7b-instruct</small></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h5>Mistral AI</h5>
                                </div>
                                <div class="card-body">
                                    <p><strong>API Key:</strong> {{ mistral_api_key|default:"Not configured" }}</p>
                                    <p><strong>Model:</strong> {{ mistral_model }}</p>
                                    <p><small class="text-muted">Free tier model: mistral-tiny</small></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h5>OpenAI</h5>
                                </div>
                                <div class="card-body">
                                    <p><strong>API Key:</strong> {{ openai_api_key|default:"Not configured" }}</p>
                                    <p><strong>Model:</strong> {{ openai_model }}</p>
                                    <p><small class="text-muted">Production model: gpt-4-turbo</small></p>
                                </div>
                            </div>
                        </div>

                    </div>

                    <div class="alert alert-info mt-4">
                        <h5>How to Configure</h5>
                        <p>To configure the AI services, update the <code>.env</code> file in the project root with the following settings:</p>
                        <pre>
# AI Provider Settings
AI_PROVIDER=groq  # 'mistral', 'openai', or 'groq'

# Mistral AI Settings
MISTRAL_API_KEY=your_mistral_api_key_here
MISTRAL_MODEL=mistral-tiny

# OpenAI Settings
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4-turbo

# Groq AI Settings (High-speed inference)
GROQ_API_KEY=your_groq_api_key_here
GROQ_MODEL=llama3-8b-8192
GROQ_TEMPERATURE=0.7
GROQ_MAX_TOKENS=1024
                        </pre>
                        <p>After updating the .env file, restart the server for the changes to take effect.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
