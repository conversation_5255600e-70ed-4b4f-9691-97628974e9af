# 📊 Analytics System Testing Guide

## 🎯 **Quick Testing Checklist**

### **1. Basic Analytics Functionality**
```bash
# Start the server
python manage.py runserver

# Test URLs to verify:
http://127.0.0.1:8000/qr-admin-dashboard/     # Admin analytics dashboard
http://127.0.0.1:8000/qr-admin-map/           # Interactive scan map
http://127.0.0.1:8000/qr-admin-map-data/      # JSON API endpoint
```

### **2. Create Test QR Codes**
1. Go to `http://127.0.0.1:8000/create/`
2. Create 3-5 different QR codes:
   - URL QR code
   - Text QR code  
   - Email QR code
3. Note the QR code IDs for testing

### **3. Generate Test Analytics Data**
```bash
# Method 1: Scan QR codes manually
# - Use your phone to scan the generated QR codes
# - Check if data appears in admin dashboard

# Method 2: Create test data via Django shell
python manage.py shell
```

```python
# In Django shell - create test scan data
from qrcode_app.models import QRScanLog, QRCode
from django.utils import timezone
import random

# Get your QR codes
qr_codes = QRCode.objects.all()[:3]

# Create test scan logs
test_countries = ['US', 'UK', 'CA', 'AU', 'DE']
test_cities = ['New York', 'London', 'Toronto', 'Sydney', 'Berlin']
test_orgs = ['Google LLC', 'Amazon.com', 'Microsoft Corporation', 'Apple Inc.']

for i in range(20):
    QRScanLog.objects.create(
        code=random.choice(qr_codes).unique_id,
        ip_address=f"192.168.1.{random.randint(1, 254)}",
        user_agent="Mozilla/5.0 (Test Browser)",
        country=random.choice(test_countries),
        city=random.choice(test_cities),
        org=random.choice(test_orgs),
        latitude=random.uniform(-90, 90),
        longitude=random.uniform(-180, 180)
    )

print("Created 20 test scan logs")
exit()
```

### **4. Test Analytics Dashboards**

#### **Admin Dashboard Test:**
1. Visit `http://127.0.0.1:8000/qr-admin-dashboard/`
2. **Verify you see:**
   - Total scans count
   - Top countries list
   - Top organizations list
   - Recent scans table
3. **Check for errors in console/logs**

#### **Map Dashboard Test:**
1. Visit `http://127.0.0.1:8000/qr-admin-map/`
2. **Verify you see:**
   - Interactive world map loads
   - Markers appear on map (if test data has coordinates)
   - Popup info shows when clicking markers
   - Map controls work (zoom, pan)
3. **Test AJAX updates:**
   - Open browser dev tools
   - Check Network tab for `/qr-admin-map-data/` requests

### **5. Test QR Scan Tracking**

#### **Test Direct QR Scans:**
1. Create a QR code and note its unique ID
2. Visit the QR landing page: `http://127.0.0.1:8000/qr/{unique_id}/`
3. Check if scan is logged in admin dashboard

#### **Test Dynamic Redirects (MODULE 2):**
1. Create a dynamic redirect for a QR code
2. Visit: `http://127.0.0.1:8000/r/{short_code}/`
3. Verify redirect works and scan is logged

#### **Test AI Landing Pages (MODULE 3):**
1. Create an AI landing page for a QR code
2. Visit: `http://127.0.0.1:8000/ai/{short_code}/`
3. Verify page loads and view is tracked

### **6. Test Geolocation Services**

#### **Test IPinfo.io Integration:**
```python
# In Django shell
from qrcode_app.utils import get_geolocation_from_ip

# Test with a real IP (Google's DNS)
result = get_geolocation_from_ip('*******')
print(result)

# Should return location data for Mountain View, CA
```

#### **Test Fallback Services:**
```python
# Test with invalid API token to trigger fallback
# Temporarily modify utils.py or test with rate-limited IP
```

### **7. Performance Testing**

#### **Basic Load Test:**
```python
# Create multiple scan logs quickly
from qrcode_app.models import QRScanLog
import time

start_time = time.time()

for i in range(100):
    QRScanLog.objects.create(
        code=f"test-{i}",
        ip_address="127.0.0.1",
        user_agent="Load Test",
        country="US",
        city="Test City"
    )

end_time = time.time()
print(f"Created 100 logs in {end_time - start_time:.2f} seconds")
```

#### **Dashboard Load Test:**
1. Create 1000+ scan logs using above method
2. Visit admin dashboard - check load time
3. Visit map dashboard - check if markers load properly

### **8. Error Handling Tests**

#### **Test API Failures:**
1. Temporarily disable internet connection
2. Create QR scans - verify they still work without geolocation
3. Check error logs for graceful handling

#### **Test Invalid Data:**
```python
# Test with invalid IP addresses
from qrcode_app.utils import get_geolocation_from_ip

# Should handle gracefully
result = get_geolocation_from_ip("invalid-ip")
result = get_geolocation_from_ip("127.0.0.1")  # localhost
```

### **9. Database Performance**

#### **Check Query Performance:**
```python
# In Django shell
from django.db import connection
from qrcode_app.models import QRScanLog

# Enable query logging
from django.conf import settings
settings.DEBUG = True

# Test dashboard queries
scans = QRScanLog.objects.all()[:10]
countries = QRScanLog.objects.values('country').distinct()

# Check query count
print(f"Queries executed: {len(connection.queries)}")
```

### **10. Production Readiness Checks**

#### **Environment Variables:**
```bash
# Check if these are set for production:
echo $IPINFO_API_TOKEN
echo $ANALYTICS_CACHE_TIMEOUT
echo $ANALYTICS_LOG_RETENTION_DAYS
```

#### **Cache Testing:**
```python
# Test cache functionality
from django.core.cache import cache

# Test geolocation caching
cache.set('test_key', 'test_value', 300)
result = cache.get('test_key')
print(f"Cache test: {result}")
```

## 🚨 **Common Issues & Solutions**

### **Issue: No data in dashboards**
- **Check:** Are you logged in as admin/staff?
- **Fix:** Create superuser: `python manage.py createsuperuser`

### **Issue: Map not loading**
- **Check:** Browser console for JavaScript errors
- **Fix:** Ensure internet connection for map tiles

### **Issue: Geolocation not working**
- **Check:** IPinfo.io API limits
- **Fix:** Set proper API token in environment

### **Issue: Slow dashboard loading**
- **Check:** Number of scan logs in database
- **Fix:** Add database indexes or implement pagination

## ✅ **Success Criteria**

Your analytics system is working correctly if:

- [ ] Admin dashboard loads and shows data
- [ ] Map displays scan locations with markers
- [ ] QR scans are properly logged
- [ ] Geolocation data is collected
- [ ] No errors in server logs
- [ ] Dashboard loads in <3 seconds with 1000+ records
- [ ] All QR types (direct, dynamic, AI) track properly

## 🔧 **Next Steps After Testing**

1. **Set up monitoring** (Sentry, New Relic)
2. **Configure log aggregation** (ELK stack, Splunk)
3. **Set up database backups**
4. **Implement rate limiting**
5. **Add performance monitoring**
6. **Security audit for IP data handling**

## 📝 **Test Results Template**

```
Analytics Testing Results - [Date]

✅ Basic Functionality:
- Admin dashboard: PASS/FAIL
- Map dashboard: PASS/FAIL
- QR scan tracking: PASS/FAIL

✅ Performance:
- Dashboard load time: [X] seconds
- 100 scan logs creation: [X] seconds
- Memory usage: [X] MB

✅ Error Handling:
- API failures: PASS/FAIL
- Invalid data: PASS/FAIL
- Network issues: PASS/FAIL

Issues Found:
1. [Issue description]
2. [Issue description]

Overall Status: READY/NEEDS WORK
```
