{% load static %}

<!-- Enterprise Content Inline Ads - Smart Magazine Layout -->
{% if ads %}
    <div class="enterprise-content-ads" id="enterprise-content-ads">
        <!-- Header with smart indicators -->
        <div class="content-ads-header">
            <div class="content-ads-label">
                <i class="fas fa-newspaper"></i>
                <span>Related Content</span>
                <span class="content-count">({{ ads|length }})</span>
                {% if ads|length > 6 %}
                <span class="content-overflow-badge">
                    <i class="fas fa-plus-circle"></i>
                    <span>More available</span>
                </span>
                {% endif %}
            </div>
            {% if ads|length > 3 %}
            <div class="content-ads-view-toggle">
                <button class="view-toggle-btn active" data-view="magazine" onclick="switchContentView('magazine')">
                    <i class="fas fa-th-large"></i>
                    <span>Magazine</span>
                </button>
                <button class="view-toggle-btn" data-view="list" onclick="switchContentView('list')">
                    <i class="fas fa-list"></i>
                    <span>List</span>
                </button>
                {% if ads|length > 6 %}
                <button class="view-toggle-btn" data-view="carousel" onclick="switchContentView('carousel')">
                    <i class="fas fa-images"></i>
                    <span>Carousel</span>
                </button>
                {% endif %}
            </div>
            {% endif %}
        </div>

        <!-- Magazine View (Default) -->
        <div class="content-ads-magazine" id="content-view-magazine">
            <div class="magazine-grid">
                {% for ad in ads %}
                <article class="magazine-article" data-ad-id="{{ ad.id }}" data-ad-type="{{ ad.ad_type.name }}" data-ad-location="Content Inline">
                    {% if ad.media and ad.media.name %}
                    <div class="magazine-image">
                        <img src="/media/{{ ad.media }}" alt="{{ ad.title }}" loading="lazy">
                        <div class="magazine-overlay">
                            <div class="magazine-category">
                                {% if ad.ad_type.name == 'Featured Ad' %}
                                <span class="category-featured">Featured</span>
                                {% else %}
                                <span class="category-sponsored">Sponsored</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="magazine-image magazine-image-placeholder">
                        <div class="placeholder-icon">
                            <i class="fas fa-image"></i>
                        </div>
                        <div class="magazine-overlay">
                            <div class="magazine-category">
                                <span class="category-sponsored">Sponsored</span>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <div class="magazine-content">
                        <h3 class="magazine-title">{{ ad.title|truncatechars:50 }}</h3>
                        <p class="magazine-excerpt">{{ ad.content|truncatechars:80 }}</p>

                        <div class="magazine-footer">
                            {% if ad.cta_link %}
                            <a href="{{ ad.cta_link }}" target="_blank" class="magazine-read-more" rel="noopener" onclick="trackAdClick('{{ ad.id }}')">
                                <span>Read More</span>
                                <i class="fas fa-arrow-right"></i>
                            </a>
                            {% else %}
                            <div class="magazine-read-more magazine-read-more-disabled">
                                <span>No Link</span>
                                <i class="fas fa-link-slash"></i>
                            </div>
                            {% endif %}
                            <div class="magazine-meta">
                                <i class="fas fa-clock"></i>
                                <span>Sponsored</span>
                            </div>
                        </div>
                    </div>
                </article>
                {% endfor %}
            </div>
        </div>

        <!-- List View -->
        <div class="content-ads-list" id="content-view-list" style="display: none;">
            <div class="list-container">
                {% for ad in ads %}
                <div class="list-item" data-ad-id="{{ ad.id }}" data-ad-type="{{ ad.ad_type.name }}" data-ad-location="Content Inline">
                    {% if ad.media and ad.media.name %}
                    <div class="list-thumbnail">
                        <img src="/media/{{ ad.media }}" alt="{{ ad.title }}" loading="lazy">
                    </div>
                    {% else %}
                    <div class="list-thumbnail list-thumbnail-placeholder">
                        <i class="fas fa-image"></i>
                    </div>
                    {% endif %}

                    <div class="list-content">
                        <div class="list-header">
                            <h4 class="list-title">{{ ad.title }}</h4>
                            <span class="list-badge">
                                {% if ad.ad_type.name == 'Featured Ad' %}Featured{% else %}Sponsored{% endif %}
                            </span>
                        </div>
                        <p class="list-description">{{ ad.content|truncatechars:120 }}</p>

                        {% if ad.cta_link %}
                        <a href="{{ ad.cta_link }}" target="_blank" class="list-cta" rel="noopener" onclick="trackAdClick('{{ ad.id }}')">
                            <span>Learn More</span>
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                        {% else %}
                        <div class="list-cta list-cta-disabled">
                            <span>No Link</span>
                            <i class="fas fa-link-slash"></i>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- Carousel View (for many ads) -->
        {% if ads|length > 6 %}
        <div class="content-ads-carousel" id="content-view-carousel" style="display: none;">
            <div class="carousel-container">
                <div class="carousel-track" id="content-carousel-track">
                    {% for ad in ads %}
                    <div class="carousel-slide" data-ad-id="{{ ad.id }}" data-ad-type="{{ ad.ad_type.name }}" data-ad-location="Content Inline">
                        {% if ad.media and ad.media.name %}
                        <div class="carousel-image">
                            <img src="/media/{{ ad.media }}" alt="{{ ad.title }}" loading="lazy">
                        </div>
                        {% else %}
                        <div class="carousel-image carousel-image-placeholder">
                            <i class="fas fa-image"></i>
                        </div>
                        {% endif %}

                        <div class="carousel-content">
                            <h4 class="carousel-title">{{ ad.title|truncatechars:40 }}</h4>
                            <p class="carousel-description">{{ ad.content|truncatechars:60 }}</p>

                            {% if ad.cta_link %}
                            <a href="{{ ad.cta_link }}" target="_blank" class="carousel-cta" rel="noopener" onclick="trackAdClick('{{ ad.id }}')">
                                <span>View</span>
                                <i class="fas fa-arrow-right"></i>
                            </a>
                            {% else %}
                            <div class="carousel-cta carousel-cta-disabled">
                                <span>No Link</span>
                                <i class="fas fa-link-slash"></i>
                            </div>
                            {% endif %}
                        </div>

                        <div class="carousel-badge">
                            {% if ad.ad_type.name == 'Featured Ad' %}Featured{% else %}Sponsored{% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <button class="carousel-nav carousel-prev" onclick="slideContentCarousel(-1)">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button class="carousel-nav carousel-next" onclick="slideContentCarousel(1)">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Ad tracking script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            {% for ad in ads %}
                trackAdImpression('{{ ad.id }}');
            {% endfor %}
        });

        // Function to track ad impressions
        function trackAdImpression(adId) {
            if (!adId) return;

            console.log('Content Inline: Tracking impression for ad ID:', adId);

            // Get device info
            const deviceInfo = {
                type: /Mobi|Android/i.test(navigator.userAgent) ? 'mobile' : 'desktop',
                userAgent: navigator.userAgent
            };

            // Get basic location info (more detailed would require geolocation API)
            const locationInfo = {
                city: 'Unknown', // Default value
                country: 'Unknown' // Default value
            };

            fetch('/ads/api/track-impression/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCsrfToken()
                },
                body: JSON.stringify({
                    ad_id: adId,
                    device_info: deviceInfo,
                    location_info: locationInfo
                })
            })
            .then(response => response.json())
            .then(data => {
                console.log('Content Inline: Impression tracked:', data);
            })
            .catch(error => console.error('Content Inline: Error tracking impression:', error));
        }

        // Function to track ad clicks
        function trackAdClick(adId) {
            if (!adId) return;

            console.log('Content Inline: Tracking click for ad ID:', adId);

            // Get device info
            const deviceInfo = {
                type: /Mobi|Android/i.test(navigator.userAgent) ? 'mobile' : 'desktop',
                userAgent: navigator.userAgent
            };

            // Get basic location info (more detailed would require geolocation API)
            const locationInfo = {
                city: 'Unknown', // Default value
                country: 'Unknown' // Default value
            };

            fetch('/ads/api/track-click/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCsrfToken()
                },
                body: JSON.stringify({
                    ad_id: adId,
                    device_info: deviceInfo,
                    location_info: locationInfo
                })
            })
            .then(response => response.json())
            .then(data => {
                console.log('Content Inline: Click tracked:', data);
            })
            .catch(error => console.error('Content Inline: Error tracking click:', error));

            return true;
        }

        // Helper function to get CSRF token
        function getCsrfToken() {
            const cookies = document.cookie.split(';');
            for (let cookie of cookies) {
                const [name, value] = cookie.trim().split('=');
                if (name === 'csrftoken') {
                    return value;
                }
            }
            return '';
        }
    </script>

    <style>
        /* Enterprise Content Ads - Compact Layout */
        .enterprise-content-ads {
            margin: 20px 0;
            background: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
            overflow: hidden;
            border: 1px solid #e9ecef;
        }

        /* Header Section - Compact */
        .content-ads-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 12px 20px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        .content-ads-label {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #495057;
            font-weight: 600;
            font-size: 16px;
        }

        .content-ads-label i {
            color: #007bff;
            font-size: 18px;
        }

        .content-count {
            background: #007bff;
            color: white;
            padding: 3px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
        }

        .content-overflow-badge {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #28a745;
            font-size: 12px;
            font-weight: 500;
            margin-left: 10px;
        }

        .content-overflow-badge i {
            color: #28a745;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-3px); }
            60% { transform: translateY(-2px); }
        }

        /* View Toggle Buttons */
        .content-ads-view-toggle {
            display: flex;
            background: white;
            border-radius: 8px;
            padding: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .view-toggle-btn {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 8px 12px;
            border: none;
            background: transparent;
            color: #6c757d;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 13px;
            font-weight: 500;
        }

        .view-toggle-btn.active {
            background: #007bff;
            color: white;
            box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
        }

        .view-toggle-btn:hover:not(.active) {
            background: #f8f9fa;
            color: #495057;
        }

        /* Magazine View (Default) - Compact */
        .content-ads-magazine {
            padding: 15px;
        }

        .magazine-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 12px;
        }

        .magazine-article {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
            position: relative;
        }

        .magazine-article:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .magazine-image {
            position: relative;
            height: 120px;
            overflow: hidden;
        }

        .magazine-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .magazine-article:hover .magazine-image img {
            transform: scale(1.05);
        }

        .magazine-image-placeholder {
            background: linear-gradient(135deg, #6c757d, #495057);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .placeholder-icon i {
            font-size: 40px;
            opacity: 0.7;
        }

        .magazine-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to bottom, rgba(0,0,0,0.3) 0%, transparent 50%);
            display: flex;
            align-items: flex-start;
            justify-content: flex-end;
            padding: 15px;
        }

        .magazine-category span {
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            padding: 4px 10px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .category-featured {
            background: linear-gradient(135deg, #ffd700, #ffed4e) !important;
            color: #333 !important;
        }

        .category-sponsored {
            background: rgba(255, 255, 255, 0.9) !important;
            color: #6c757d !important;
        }

        .magazine-content {
            padding: 12px;
        }

        .magazine-title {
            font-size: 14px;
            font-weight: 600;
            margin: 0 0 6px 0;
            color: #212529;
            line-height: 1.3;
        }

        .magazine-excerpt {
            font-size: 12px;
            color: #6c757d;
            margin: 0 0 10px 0;
            line-height: 1.4;
        }

        .magazine-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .magazine-read-more {
            display: flex;
            align-items: center;
            gap: 6px;
            color: #007bff;
            text-decoration: none;
            font-size: 13px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .magazine-read-more:hover {
            color: #0056b3;
            transform: translateX(3px);
        }

        .magazine-read-more-disabled {
            color: #adb5bd;
            cursor: not-allowed;
        }

        .magazine-meta {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #adb5bd;
            font-size: 11px;
        }

        /* List View - Compact */
        .content-ads-list {
            padding: 15px;
        }

        .list-container {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .list-item {
            display: flex;
            background: white;
            border-radius: 6px;
            padding: 10px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .list-item:hover {
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
            transform: translateX(3px);
        }

        .list-thumbnail {
            flex: 0 0 60px;
            height: 60px;
            border-radius: 6px;
            overflow: hidden;
            margin-right: 12px;
        }

        .list-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .list-thumbnail-placeholder {
            background: linear-gradient(135deg, #6c757d, #495057);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .list-content {
            flex: 1;
        }

        .list-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }

        .list-title {
            font-size: 15px;
            font-weight: 600;
            margin: 0;
            color: #212529;
            flex: 1;
        }

        .list-badge {
            background: #f8f9fa;
            color: #6c757d;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: 500;
            text-transform: uppercase;
            margin-left: 10px;
        }

        .list-description {
            font-size: 13px;
            color: #6c757d;
            margin: 0 0 10px 0;
            line-height: 1.4;
        }

        .list-cta {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            color: #007bff;
            text-decoration: none;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .list-cta:hover {
            color: #0056b3;
        }

        .list-cta-disabled {
            color: #adb5bd;
            cursor: not-allowed;
        }

        /* Carousel View - Compact */
        .content-ads-carousel {
            padding: 15px;
        }

        .carousel-container {
            position: relative;
            overflow: hidden;
        }

        .carousel-track {
            display: flex;
            gap: 12px;
            transition: transform 0.5s ease;
        }

        .carousel-slide {
            flex: 0 0 200px;
            background: white;
            border-radius: 6px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.06);
            border: 1px solid #e9ecef;
            position: relative;
        }

        .carousel-image {
            height: 100px;
            overflow: hidden;
        }

        .carousel-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .carousel-image-placeholder {
            background: linear-gradient(135deg, #6c757d, #495057);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .carousel-content {
            padding: 10px;
        }

        .carousel-title {
            font-size: 12px;
            font-weight: 600;
            margin: 0 0 5px 0;
            color: #212529;
        }

        .carousel-description {
            font-size: 11px;
            color: #6c757d;
            margin: 0 0 8px 0;
            line-height: 1.3;
        }

        .carousel-cta {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            color: #007bff;
            text-decoration: none;
            font-size: 11px;
            font-weight: 600;
        }

        .carousel-cta-disabled {
            color: #adb5bd;
            cursor: not-allowed;
        }

        .carousel-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255, 255, 255, 0.9);
            color: #6c757d;
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 9px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .carousel-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: white;
            border: 1px solid #dee2e6;
            color: #6c757d;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2;
        }

        .carousel-nav:hover {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .carousel-prev {
            left: -20px;
        }

        .carousel-next {
            right: -20px;
        }

        /* Responsive Design - Compact */
        @media (max-width: 768px) {
            .content-ads-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
                padding: 10px 15px;
            }

            .content-ads-view-toggle {
                width: 100%;
                justify-content: center;
            }

            .magazine-grid {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .magazine-image {
                height: 100px;
            }

            .magazine-content {
                padding: 10px;
            }

            .list-item {
                flex-direction: column;
                padding: 8px;
            }

            .list-thumbnail {
                margin-right: 0;
                margin-bottom: 8px;
                align-self: center;
                width: 50px;
                height: 50px;
            }

            .carousel-slide {
                flex: 0 0 180px;
            }

            .carousel-image {
                height: 80px;
            }

            .carousel-nav {
                display: none;
            }
        }

        @media (max-width: 480px) {
            .enterprise-content-ads {
                margin: 15px 0;
            }

            .content-ads-header {
                padding: 8px 12px;
            }

            .content-ads-magazine,
            .content-ads-list,
            .content-ads-carousel {
                padding: 12px;
            }

            .carousel-slide {
                flex: 0 0 160px;
            }

            .magazine-grid {
                gap: 8px;
            }

            .list-container {
                gap: 6px;
            }
        }
    </style>

    <script>
        let contentCurrentSlide = 0;
        let contentTotalSlides = 0;

        // Initialize content ads
        document.addEventListener('DOMContentLoaded', function() {
            initContentAds();
        });

        function initContentAds() {
            const track = document.getElementById('content-carousel-track');
            if (track) {
                contentTotalSlides = track.children.length;
            }
        }

        // Switch between view modes
        function switchContentView(viewType) {
            // Hide all views
            document.getElementById('content-view-magazine').style.display = 'none';
            document.getElementById('content-view-list').style.display = 'none';
            const carouselView = document.getElementById('content-view-carousel');
            if (carouselView) {
                carouselView.style.display = 'none';
            }

            // Show selected view
            document.getElementById('content-view-' + viewType).style.display = 'block';

            // Update toggle buttons
            document.querySelectorAll('.view-toggle-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-view="${viewType}"]`).classList.add('active');

            // Reset carousel if switching to it
            if (viewType === 'carousel') {
                contentCurrentSlide = 0;
                updateCarouselPosition();
            }
        }

        // Carousel navigation
        function slideContentCarousel(direction) {
            const track = document.getElementById('content-carousel-track');
            if (!track) return;

            contentCurrentSlide += direction;

            // Loop around
            if (contentCurrentSlide >= contentTotalSlides - 2) {
                contentCurrentSlide = 0;
            } else if (contentCurrentSlide < 0) {
                contentCurrentSlide = Math.max(0, contentTotalSlides - 3);
            }

            updateCarouselPosition();
        }

        function updateCarouselPosition() {
            const track = document.getElementById('content-carousel-track');
            if (!track) return;

            const slideWidth = 212; // 200px + 12px gap
            const offset = contentCurrentSlide * slideWidth;
            track.style.transform = `translateX(-${offset}px)`;
        }

        // Auto-advance carousel
        setInterval(() => {
            const carouselView = document.getElementById('content-view-carousel');
            if (carouselView && carouselView.style.display !== 'none' && contentTotalSlides > 3) {
                slideContentCarousel(1);
            }
        }, 5000); // Auto-advance every 5 seconds
    </script>
{% endif %}
