{% extends "base.html" %}
{% load static %}

{% block title %}User Management - Enterprise QR{% endblock %}

{% block extra_css %}
<!-- Google Fonts - Poppins and Montserrat -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

<style>
    /* Ultra-Premium Enterprise User Management Styling */
    body {
        background: linear-gradient(135deg, #000000 0%, #0a0a0a 10%, #1a1a2e 25%, #16213e 40%, #0f3460 60%, #533483 80%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        position: relative;
        overflow-x: hidden;
    }

    /* Premium animated background with enterprise patterns */
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 10% 90%, rgba(102, 126, 234, 0.7) 0%, transparent 40%),
            radial-gradient(circle at 90% 10%, rgba(255, 255, 255, 0.3) 0%, transparent 35%),
            radial-gradient(circle at 30% 70%, rgba(118, 75, 162, 0.6) 0%, transparent 45%),
            radial-gradient(circle at 70% 30%, rgba(83, 52, 131, 0.5) 0%, transparent 30%),
            radial-gradient(circle at 50% 50%, rgba(102, 126, 234, 0.4) 0%, transparent 40%);
        z-index: -1;
        animation: enterpriseFloat 45s ease-in-out infinite;
    }

    @keyframes enterpriseFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        25% { transform: translateY(-20px) rotate(1deg); }
        50% { transform: translateY(-10px) rotate(-1deg); }
        75% { transform: translateY(-30px) rotate(0.5deg); }
    }

    /* Enterprise Container */
    .enterprise-container {
        position: relative;
        z-index: 1;
        padding: 2rem 0;
        min-height: 100vh;
    }

    /* Premium Header Section */
    .enterprise-header {
        background: linear-gradient(135deg, rgba(26, 35, 126, 0.95) 0%, rgba(40, 53, 147, 0.95) 50%, rgba(63, 81, 181, 0.95) 100%);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2.5rem;
        margin-bottom: 2rem;
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        position: relative;
        overflow: hidden;
        animation: slideInDown 0.8s ease-out;
    }

    .enterprise-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
        animation: shimmer 3s ease-in-out infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    .enterprise-title {
        font-family: 'Montserrat', sans-serif !important;
        font-size: 2.5rem;
        font-weight: 700;
        color: #ffffff;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 2;
    }

    .enterprise-subtitle {
        font-size: 1.1rem;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 0;
        font-weight: 400;
        position: relative;
        z-index: 2;
    }

    .enterprise-icon {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        width: 60px;
        height: 60px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.8rem;
        color: white;
        margin-right: 1.5rem;
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        position: relative;
        z-index: 2;
    }

    /* Premium Action Button */
    .enterprise-action-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 12px;
        padding: 0.8rem 2rem;
        color: white;
        font-weight: 600;
        font-size: 1rem;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        position: relative;
        z-index: 2;
        overflow: hidden;
    }

    .enterprise-action-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .enterprise-action-btn:hover::before {
        left: 100%;
    }

    .enterprise-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    /* Premium Filter Controls */
    .enterprise-filters {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
        backdrop-filter: blur(20px);
        border-radius: 16px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow:
            0 15px 35px rgba(0, 0, 0, 0.1),
            0 0 0 1px rgba(255, 255, 255, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        animation: slideInUp 0.8s ease-out 0.2s both;
    }

    .enterprise-filters .form-select,
    .enterprise-filters .form-control {
        border: 2px solid rgba(102, 126, 234, 0.2);
        border-radius: 12px;
        padding: 0.75rem 1rem;
        font-weight: 500;
        background: rgba(255, 255, 255, 0.9);
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    }

    .enterprise-filters .form-select:focus,
    .enterprise-filters .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 8px 25px rgba(0, 0, 0, 0.1);
        outline: none;
    }

    .enterprise-filters .input-group-text {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        border-radius: 12px 0 0 12px;
        padding: 0.75rem 1rem;
    }

    /* Premium Data Table */
    .enterprise-table-container {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        overflow: hidden;
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.15),
            0 0 0 1px rgba(255, 255, 255, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        animation: slideInUp 0.8s ease-out 0.4s both;
    }

    .enterprise-table {
        margin: 0;
        background: transparent;
    }

    .enterprise-table thead th {
        background: linear-gradient(135deg, #1a237e 0%, #283593 100%);
        color: white;
        font-weight: 600;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        padding: 1.2rem 1.5rem;
        border: none;
        position: relative;
    }

    .enterprise-table thead th::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    }

    .enterprise-table tbody tr {
        transition: all 0.3s ease;
        border: none;
    }

    .enterprise-table tbody tr:hover {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .enterprise-table tbody td {
        padding: 1.2rem 1.5rem;
        border: none;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        vertical-align: middle;
        font-weight: 500;
    }

    /* Premium User Avatar */
    .enterprise-avatar {
        width: 50px;
        height: 50px;
        border-radius: 15px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        color: white;
        font-weight: 600;
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        margin-right: 1rem;
        position: relative;
        overflow: hidden;
    }

    .enterprise-avatar::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.2) 50%, transparent 70%);
        animation: avatarShimmer 3s ease-in-out infinite;
    }

    @keyframes avatarShimmer {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .enterprise-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 12px;
    }

    /* Premium User Info */
    .enterprise-user-name {
        font-weight: 700;
        font-size: 1.1rem;
        color: #1a237e;
        margin-bottom: 0.2rem;
    }

    .enterprise-user-handle {
        color: #667eea;
        font-size: 0.9rem;
        font-weight: 500;
    }

    /* Premium Role Badges */
    .enterprise-badge {
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-weight: 600;
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        position: relative;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .enterprise-badge::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        transition: left 0.5s;
    }

    .enterprise-badge:hover::before {
        left: 100%;
    }

    .enterprise-badge.badge-superadmin {
        background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
        color: white;
    }

    .enterprise-badge.badge-admin {
        background: linear-gradient(135deg, #d97706 0%, #f59e0b 100%);
        color: white;
    }

    .enterprise-badge.badge-premium {
        background: linear-gradient(135deg, #059669 0%, #10b981 100%);
        color: white;
    }

    .enterprise-badge.badge-user {
        background: linear-gradient(135deg, #4b5563 0%, #6b7280 100%);
        color: white;
    }

    .enterprise-badge.badge-guest {
        background: linear-gradient(135deg, #9ca3af 0%, #d1d5db 100%);
        color: #374151;
    }

    /* Premium Action Buttons */
    .enterprise-actions {
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }

    .enterprise-action {
        width: 40px;
        height: 40px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        font-size: 1rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .enterprise-action.action-edit {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
    }

    .enterprise-action.action-delete {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        color: white;
    }

    .enterprise-action:hover {
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        color: white;
        text-decoration: none;
    }

    .enterprise-action::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        transition: left 0.5s;
    }

    .enterprise-action:hover::before {
        left: 100%;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .enterprise-title {
            font-size: 2rem;
        }

        .enterprise-header {
            padding: 2rem 1.5rem;
        }

        .enterprise-filters {
            padding: 1rem;
        }

        .enterprise-table tbody td {
            padding: 1rem;
        }

        .enterprise-avatar {
            width: 40px;
            height: 40px;
            font-size: 1rem;
        }

        .enterprise-actions {
            flex-direction: column;
            gap: 0.3rem;
        }

        .enterprise-action {
            width: 35px;
            height: 35px;
            font-size: 0.9rem;
        }
    }

    @media (max-width: 576px) {
        .enterprise-title {
            font-size: 1.8rem;
        }

        .enterprise-header {
            padding: 1.5rem 1rem;
        }

        .enterprise-table-container {
            border-radius: 15px;
        }

        .enterprise-table thead th {
            padding: 1rem;
            font-size: 0.8rem;
        }

        .enterprise-table tbody td {
            padding: 0.8rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="enterprise-container">
    <div class="container">
        <!-- Premium Header Section -->
        <div class="enterprise-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <div class="d-flex align-items-center">
                        <div class="enterprise-icon">
                            <i class="fas fa-users-cog"></i>
                        </div>
                        <div>
                            <h1 class="enterprise-title mb-0">User Management</h1>
                            <p class="enterprise-subtitle">Manage user accounts, roles, and permissions with enterprise-grade control</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-md-end">
                    {% if can_add_users %}
                    <a href="{% url 'add_user' %}" class="enterprise-action-btn">
                        <i class="fas fa-user-plus"></i>
                        Add New User
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Premium Filter Controls -->
        <div class="enterprise-filters">
            <div class="row g-3">
                <div class="col-md-4">
                    <select class="form-select" id="roleFilter">
                        <option value="">All Roles</option>
                        <option value="guest">Guest</option>
                        <option value="user">User</option>
                        <option value="premium">Premium User</option>
                        <option value="admin">Administrator</option>
                        <option value="superadmin">Super Administrator</option>
                    </select>
                </div>
                <div class="col-md-8">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" id="userSearch" placeholder="Search users by name, email, or company...">
                    </div>
                </div>
            </div>
        </div>

        <!-- Premium Data Table -->
        <div class="enterprise-table-container">
            <div class="table-responsive">
                <table class="enterprise-table table mb-0">
                    <thead>
                        <tr>
                            <th>User</th>
                            <th>Email</th>
                            <th>Role</th>
                            <th>Company</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for profile in users %}
                        <tr data-role="{{ profile.role }}">
                            <td>
                                <div class="d-flex align-items-center">
                                    {% if profile.profile_image %}
                                    <div class="enterprise-avatar">
                                        <img src="{{ profile.profile_image.url }}" alt="{{ profile.user.username }}">
                                    </div>
                                    {% else %}
                                    <div class="enterprise-avatar">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    {% endif %}
                                    <div>
                                        <div class="enterprise-user-name">{{ profile.user.get_full_name|default:profile.user.username }}</div>
                                        <div class="enterprise-user-handle">@{{ profile.user.username }}</div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span style="color: #4b5563; font-weight: 500;">{{ profile.user.email }}</span>
                            </td>
                            <td>
                                <span class="enterprise-badge badge-{{ profile.role }}">
                                    {{ profile.get_role_display }}
                                </span>
                            </td>
                            <td>
                                <span style="color: #6b7280; font-weight: 500;">{{ profile.company|default:"-" }}</span>
                            </td>
                            <td>
                                <span style="color: #6b7280; font-weight: 500;">{{ profile.created_at|date:"M d, Y" }}</span>
                            </td>
                            <td>
                                <div class="enterprise-actions">
                                    {% if can_edit_users %}
                                    <a href="{% url 'edit_user' profile.user.id %}" class="enterprise-action action-edit" title="Edit User">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% endif %}
                                    {% if can_delete_users %}
                                    <a href="{% url 'delete_user' profile.user.id %}" class="enterprise-action action-delete" title="Delete User">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center py-5">
                                <div style="color: #9ca3af; font-size: 1.1rem;">
                                    <i class="fas fa-users fa-2x mb-3" style="opacity: 0.5;"></i>
                                    <p class="mb-0 font-weight-500">No users found matching your criteria.</p>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Role filtering
        const roleFilter = document.getElementById('roleFilter');
        if (roleFilter) {
            roleFilter.addEventListener('change', function() {
                const selectedRole = this.value;
                const rows = document.querySelectorAll('tbody tr[data-role]');

                rows.forEach(row => {
                    if (!selectedRole || row.dataset.role === selectedRole) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            });
        }

        // User search
        const userSearch = document.getElementById('userSearch');
        if (userSearch) {
            userSearch.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const rows = document.querySelectorAll('tbody tr');

                rows.forEach(row => {
                    const text = row.textContent.toLowerCase();
                    if (text.includes(searchTerm)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            });
        }
    });
</script>
{% endblock %}
