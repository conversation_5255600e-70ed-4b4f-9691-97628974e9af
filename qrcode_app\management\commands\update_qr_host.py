"""
Management command to update QR code URLs to use a different host (for testing on mobile devices)
"""
from django.core.management.base import BaseCommand
from qrcode_app.models import QRCode
import qrcode
from io import BytesIO
import socket


class Command(BaseCommand):
    help = 'Update QR code URLs to use a different host for mobile testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--host',
            type=str,
            help='New host to use (e.g., *************:8000)',
        )
        parser.add_argument(
            '--auto-detect',
            action='store_true',
            help='Auto-detect the machine IP address',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be updated without making changes',
        )

    def get_local_ip(self):
        """Get the local IP address of the machine"""
        try:
            # Connect to a remote address to get local IP
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            return ip
        except Exception:
            return "*************"  # Fallback

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        if options['auto_detect']:
            local_ip = self.get_local_ip()
            new_host = f"{local_ip}:8000"
            self.stdout.write(f'Auto-detected IP: {local_ip}')
        elif options['host']:
            new_host = options['host']
        else:
            self.stdout.write(
                self.style.ERROR('Please provide --host or use --auto-detect')
            )
            return
        
        # Find QR codes with localhost URLs
        qr_codes_to_update = QRCode.objects.filter(
            data__startswith='http://127.0.0.1:8000/qr/'
        )
        
        total_count = qr_codes_to_update.count()
        
        if total_count == 0:
            self.stdout.write(
                self.style.SUCCESS('No QR codes need updating.')
            )
            return
        
        self.stdout.write(
            f'Found {total_count} QR codes to update to use host: {new_host}'
        )
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN - No changes will be made.')
            )
            for qr_code in qr_codes_to_update:
                old_url = qr_code.data
                new_url = old_url.replace('127.0.0.1:8000', new_host)
                self.stdout.write(f'Would update: {qr_code.name}')
                self.stdout.write(f'  From: {old_url}')
                self.stdout.write(f'  To:   {new_url}')
            return
        
        updated_count = 0
        error_count = 0
        
        for qr_code in qr_codes_to_update:
            try:
                # Update the URL
                old_url = qr_code.data
                new_url = old_url.replace('127.0.0.1:8000', new_host)
                qr_code.data = new_url
                
                # Regenerate QR code image with new URL
                qr = qrcode.QRCode(
                    version=40,
                    error_correction=qrcode.constants.ERROR_CORRECT_L,
                    box_size=10,
                    border=4,
                )
                qr.add_data(new_url)
                qr.make(fit=True)
                
                # Create the QR code image
                img = qr.make_image(
                    fill_color=qr_code.foreground_color, 
                    back_color=qr_code.background_color
                )
                
                # Save the QR code image
                buffer = BytesIO()
                img.save(buffer, format='PNG')
                
                # Save to model
                filename = f"{qr_code.name.replace(' ', '_')}.png"
                qr_code.image.save(filename, buffer, save=False)
                qr_code.save()
                
                updated_count += 1
                self.stdout.write(f'Updated: {qr_code.name}')
                self.stdout.write(f'  New URL: {new_url}')
                    
            except Exception as e:
                error_count += 1
                self.stdout.write(
                    self.style.ERROR(f'Error updating {qr_code.name}: {str(e)}')
                )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully updated {updated_count} QR codes. '
                f'Errors: {error_count}'
            )
        )
