name: CD - Deploy to VPS

on:
  push:
    branches: [ main ]
  workflow_dispatch:  # Allow manual triggering

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v3
    
    - name: Set up SSH
      uses: webfactory/ssh-agent@v0.7.0
      with:
        ssh-private-key: ${{ secrets.SSH_KEY }}
    
    - name: Add host key to known_hosts
      run: |
        mkdir -p ~/.ssh
        ssh-keyscan -H ${{ secrets.SERVER_IP }} >> ~/.ssh/known_hosts
    
    - name: Deploy to VPS
      run: |
        ssh ${{ secrets.USERNAME }}@${{ secrets.SERVER_IP }} "cd /home/<USER>/QRCodeGenerator && \
        git pull && \
        docker-compose down && \
        docker-compose up --build -d"
    
    - name: Verify deployment
      run: |
        ssh ${{ secrets.USERNAME }}@${{ secrets.SERVER_IP }} "cd /home/<USER>/QRCodeGenerator && \
        docker-compose ps"
    
    - name: Notify Slack on success
      uses: rtCamp/action-slack-notify@v2
      if: success()
      env:
        SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
        SLACK_CHANNEL: deployments
        SLACK_COLOR: good
        SLACK_ICON: https://github.com/Codegx-Technology.png?size=48
        SLACK_MESSAGE: 'QR Code Generator deployed successfully :rocket:'
        SLACK_TITLE: Deployment Success
        SLACK_USERNAME: GitHub Actions
    
    - name: Notify Slack on failure
      uses: rtCamp/action-slack-notify@v2
      if: failure()
      env:
        SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
        SLACK_CHANNEL: deployments
        SLACK_COLOR: danger
        SLACK_ICON: https://github.com/Codegx-Technology.png?size=48
        SLACK_MESSAGE: 'QR Code Generator deployment failed :x:'
        SLACK_TITLE: Deployment Failure
        SLACK_USERNAME: GitHub Actions
