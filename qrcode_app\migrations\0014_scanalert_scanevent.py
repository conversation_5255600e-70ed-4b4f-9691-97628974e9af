# Generated by Django 5.1.7 on 2025-05-29 07:26

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('qrcode_app', '0013_ailandingpage'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ScanAlert',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('alert_type', models.CharField(choices=[('KEYWORD', 'Keyword Match'), ('LOCATION', 'Location Based'), ('FREQUENCY', 'Scan Frequency'), ('DEVICE', 'Device Type'), ('ORGANIZATION', 'Organization/ISP')], default='KEYWORD', max_length=20)),
                ('keyword', models.CharField(help_text='Keyword to match in location, org, user agent, etc.', max_length=255)),
                ('email', models.EmailField(help_text='Email address to send alerts to', max_length=254)),
                ('is_active', models.BooleanField(default=True, help_text='Enable/disable this alert')),
                ('send_immediately', models.BooleanField(default=True, help_text='Send alert immediately or batch daily')),
                ('max_alerts_per_day', models.PositiveIntegerField(default=10, help_text='Maximum alerts to send per day')),
                ('triggered_count', models.PositiveIntegerField(default=0, help_text='Total number of times this alert has been triggered')),
                ('last_triggered', models.DateTimeField(blank=True, null=True)),
                ('alerts_sent_today', models.PositiveIntegerField(default=0, help_text='Alerts sent today (resets daily)')),
                ('last_reset_date', models.DateField(auto_now_add=True, help_text='Last date when daily counter was reset')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('qr_code', models.ForeignKey(blank=True, help_text='Specific QR code to monitor (leave blank for all QR codes)', null=True, on_delete=django.db.models.deletion.CASCADE, to='qrcode_app.qrcode')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='scan_alerts', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Scan Alert',
                'verbose_name_plural': 'Scan Alerts',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', 'is_active'], name='qrcode_app__user_id_9b51c6_idx'), models.Index(fields=['qr_code', 'is_active'], name='qrcode_app__qr_code_f540e6_idx'), models.Index(fields=['keyword'], name='qrcode_app__keyword_934b0b_idx')],
            },
        ),
        migrations.CreateModel(
            name='ScanEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('ip', models.GenericIPAddressField()),
                ('location', models.CharField(blank=True, help_text='City, Country from IPinfo', max_length=255)),
                ('org', models.CharField(blank=True, help_text='Organization/ISP from IPinfo', max_length=255)),
                ('user_agent', models.TextField(blank=True)),
                ('device_type', models.CharField(blank=True, help_text='Mobile, Desktop, Tablet', max_length=50)),
                ('browser', models.CharField(blank=True, max_length=50)),
                ('country_code', models.CharField(blank=True, help_text='ISO country code', max_length=2)),
                ('alerts_triggered', models.PositiveIntegerField(default=0, help_text='Number of alerts triggered by this scan')),
                ('processed_for_alerts', models.BooleanField(default=False, help_text='Whether this scan has been processed for alerts')),
                ('qr_code', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='scan_events', to='qrcode_app.qrcode')),
            ],
            options={
                'verbose_name': 'Scan Event',
                'verbose_name_plural': 'Scan Events',
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['qr_code', '-timestamp'], name='qrcode_app__qr_code_758ec7_idx'), models.Index(fields=['ip', '-timestamp'], name='qrcode_app__ip_b8bb6d_idx'), models.Index(fields=['processed_for_alerts'], name='qrcode_app__process_0378c0_idx')],
            },
        ),
    ]
