/* Authentication Styles */
:root {
    --auth-card-width: 480px;
    --auth-card-radius: 12px;
    --auth-input-height: 48px;
    --auth-btn-height: 48px;
}

body {
    background-color: var(--bg-light);
}

.auth-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: calc(100vh - var(--header-height));
    padding: 2rem 1rem;
}

.auth-card {
    width: 100%;
    max-width: var(--auth-card-width);
    background-color: white;
    border-radius: var(--auth-card-radius);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

/* Auth Tabs */
.auth-tabs {
    display: flex;
    border-bottom: 1px solid var(--gray-200);
}

.auth-tab {
    flex: 1;
    padding: 1rem;
    background: none;
    border: none;
    font-size: 1rem;
    font-weight: 500;
    color: var(--gray-600);
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.auth-tab:hover {
    color: var(--primary-color);
}

.auth-tab.active {
    color: var(--primary-color);
}

.auth-tab.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--primary-color);
}

/* Auth Forms */
.auth-form {
    padding: 2rem;
    display: none;
}

.auth-form.active {
    display: block;
}

.auth-form h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
    margin: 0 0 0.5rem 0;
}

.auth-subtitle {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin: 0 0 1.5rem 0;
}

.form-group {
    margin-bottom: 1.25rem;
}

.form-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.25rem;
}

.form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
}

.input-with-icon {
    position: relative;
}

.input-with-icon i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-500);
}

.input-with-icon input {
    padding-left: 2.75rem;
}

input[type="text"],
input[type="email"],
input[type="password"] {
    width: 100%;
    height: var(--auth-input-height);
    padding: 0 1rem;
    border: 1px solid var(--gray-300);
    border-radius: 8px;
    font-size: 0.9375rem;
    color: var(--gray-800);
    transition: all 0.2s ease;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    outline: none;
}

.toggle-password {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--gray-500);
    cursor: pointer;
    padding: 0;
}

.toggle-password:hover {
    color: var(--gray-700);
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.remember-me {
    display: flex;
    align-items: center;
}

.remember-me input[type="checkbox"] {
    margin-right: 0.5rem;
}

.forgot-password {
    font-size: 0.875rem;
    color: var(--primary-color);
    text-decoration: none;
}

.forgot-password:hover {
    text-decoration: underline;
}

.auth-btn {
    width: 100%;
    height: var(--auth-btn-height);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.auth-btn:hover {
    box-shadow: 0 4px 10px rgba(59, 130, 246, 0.3);
    transform: translateY(-1px);
}

/* Auth Divider */
.auth-divider {
    display: flex;
    align-items: center;
    margin: 1.5rem 0;
    color: var(--gray-500);
    font-size: 0.875rem;
}

.auth-divider::before,
.auth-divider::after {
    content: '';
    flex: 1;
    height: 1px;
    background-color: var(--gray-200);
}

.auth-divider span {
    padding: 0 1rem;
}

/* SSO Options */
.sso-options {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.sso-btn {
    flex: 1;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    background-color: white;
    border: 1px solid var(--gray-300);
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--gray-800);
    cursor: pointer;
    transition: all 0.2s ease;
}

.sso-btn:hover {
    background-color: var(--gray-50);
    border-color: var(--gray-400);
}

.sso-btn img {
    width: 18px;
    height: 18px;
}

.sso-btn.apple i {
    font-size: 1.25rem;
}

/* Enterprise SSO */
.enterprise-sso {
    margin-top: 1.5rem;
    border-top: 1px solid var(--gray-200);
    padding-top: 1.5rem;
}

.enterprise-sso-btn {
    width: 100%;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    background-color: var(--gray-100);
    border: 1px solid var(--gray-300);
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--gray-800);
    cursor: pointer;
    transition: all 0.2s ease;
}

.enterprise-sso-btn:hover {
    background-color: var(--gray-200);
}

.enterprise-sso-form {
    margin-top: 1rem;
    display: none;
}

.enterprise-sso-form.active {
    display: block;
}

/* Password Strength */
.password-strength {
    margin-top: 0.5rem;
}

.strength-meter {
    display: flex;
    gap: 4px;
    margin-bottom: 0.25rem;
}

.strength-segment {
    height: 4px;
    flex: 1;
    background-color: var(--gray-200);
    border-radius: 2px;
}

.strength-segment.weak {
    background-color: var(--red);
}

.strength-segment.medium {
    background-color: var(--orange);
}

.strength-segment.strong {
    background-color: var(--green);
}

.strength-text {
    font-size: 0.75rem;
    color: var(--gray-500);
}

/* Checkbox Group */
.checkbox-group {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
}

.checkbox-group input[type="checkbox"] {
    margin-top: 0.25rem;
}

.checkbox-group label {
    font-size: 0.875rem;
    font-weight: 400;
    color: var(--gray-700);
    margin-bottom: 0;
}

.checkbox-group a {
    color: var(--primary-color);
    text-decoration: none;
}

.checkbox-group a:hover {
    text-decoration: underline;
}

/* Auth Footer */
.auth-footer {
    padding: 1.5rem 2rem;
    background-color: var(--gray-50);
    border-top: 1px solid var(--gray-200);
    text-align: center;
}

.auth-footer p {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin: 0 0 0.75rem 0;
}

.security-badges {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.security-badge {
    font-size: 0.75rem;
    color: var(--gray-700);
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.security-badge i {
    color: var(--primary-color);
}

/* Back Button */
.back-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--gray-700);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.back-btn:hover {
    color: var(--primary-color);
}

/* Responsive Styles */
@media (max-width: 576px) {
    .auth-card {
        border-radius: 0;
        box-shadow: none;
    }
    
    .auth-container {
        padding: 0;
    }
    
    .form-row {
        flex-direction: column;
        gap: 1.25rem;
    }
    
    .sso-options {
        flex-direction: column;
    }
}
