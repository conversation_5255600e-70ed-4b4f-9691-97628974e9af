<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test AI Suggestions</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container mt-5">
        <h2>Test AI Suggestions</h2>
        
        <!-- Test Form -->
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="adTitle" class="form-label">Ad Title</label>
                    <input type="text" class="form-control" id="adTitle" placeholder="Enter ad title">
                </div>
                
                <div class="mb-3">
                    <label for="businessType" class="form-label">Business Type</label>
                    <input type="text" class="form-control" id="businessType" placeholder="e.g., Fashion Boutique, Tech Startup">
                </div>
                
                <div class="mb-3">
                    <label for="targetAudience" class="form-label">Target Audience</label>
                    <input type="text" class="form-control" id="targetAudience" placeholder="e.g., Young Professionals">
                </div>
                
                <div class="mb-3">
                    <label for="aiStyle" class="form-label">Style</label>
                    <select class="form-select" id="aiStyle">
                        <option value="professional">Professional</option>
                        <option value="casual">Casual</option>
                        <option value="persuasive">Persuasive</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label for="aiLanguage" class="form-label">Language</label>
                    <select class="form-select" id="aiLanguage">
                        <option value="english">English</option>
                        <option value="swahili">Swahili</option>
                    </select>
                </div>
                
                <button type="button" id="generateSuggestions" class="btn btn-primary">
                    <i class="fas fa-magic me-2"></i> Generate Suggestions
                </button>
                
                <div id="formErrorContainer" class="alert alert-danger mt-3" style="display: none;"></div>
            </div>
            
            <div class="col-md-6">
                <div id="aiSuggestionsContainer" class="mt-3">
                    <h5>AI Suggestions</h5>
                    <div class="ai-suggestions">
                        <div class="text-muted">Click "Generate Suggestions" to see AI-powered content</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Simple test script
        let isGenerating = false;
        
        document.getElementById('generateSuggestions').addEventListener('click', function() {
            if (isGenerating) return;
            
            const adTitle = document.getElementById('adTitle').value.trim();
            const businessType = document.getElementById('businessType').value.trim();
            const targetAudience = document.getElementById('targetAudience').value.trim();
            const style = document.getElementById('aiStyle').value;
            const language = document.getElementById('aiLanguage').value;
            
            if (!adTitle) {
                showError('Please enter an ad title');
                return;
            }
            
            if (!businessType) {
                showError('Please enter a business type');
                return;
            }
            
            isGenerating = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Generating...';
            this.disabled = true;
            
            // Test API call
            fetch('/ads/test/smart-ai/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    language: language,
                    business_type: businessType,
                    target_audience: targetAudience,
                    tone: style,
                    title: adTitle,
                    num_suggestions: 3
                })
            })
            .then(response => response.json())
            .then(data => {
                console.log('Response:', data);
                if (data.success && data.suggestions) {
                    displaySuggestions(data.suggestions);
                } else {
                    showError('Failed to generate suggestions');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showError('Error generating suggestions: ' + error.message);
            })
            .finally(() => {
                isGenerating = false;
                this.innerHTML = '<i class="fas fa-magic me-2"></i> Generate Suggestions';
                this.disabled = false;
            });
        });
        
        function displaySuggestions(suggestions) {
            const container = document.querySelector('.ai-suggestions');
            container.innerHTML = '';
            
            suggestions.forEach((suggestion, index) => {
                const card = document.createElement('div');
                card.className = 'ai-suggestion-card mb-3 p-3 border rounded';
                card.innerHTML = `
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="radio" name="ai_suggestion" id="suggestion${index + 1}" value="${index + 1}">
                        <label class="form-check-label fw-bold" for="suggestion${index + 1}">Suggestion ${index + 1}</label>
                    </div>
                    <div class="ai-suggestion-title mb-1 fw-bold">${suggestion.title || 'No title'}</div>
                    <div class="ai-suggestion-content small text-muted">${suggestion.content || 'No content'}</div>
                `;
                container.appendChild(card);
            });
        }
        
        function showError(message) {
            const errorContainer = document.getElementById('formErrorContainer');
            errorContainer.textContent = message;
            errorContainer.style.display = 'block';
            setTimeout(() => {
                errorContainer.style.display = 'none';
            }, 5000);
        }
    </script>
</body>
</html>
