/* Footer Fix for Enterprise Dashboard */

/* Fix for extra space under footer */
.enterprise-dashboard {
    min-height: calc(100vh - 65px) !important; /* Adjust for bottom nav height */
    display: flex;
    flex-direction: column;
}

/* Ensure footer sits flush at the bottom */
.enterprise-footer {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
}

/* Fix for admin users */
body.admin-user .enterprise-footer {
    margin-top: 0 !important;
}

/* Fix for mobile bottom navigation */
@media (max-width: 991.98px) {
    .enterprise-footer {
        padding-bottom: 65px !important; /* Match bottom nav height */
        margin-bottom: 0 !important;
    }
    
    body.admin-user .enterprise-footer {
        padding-bottom: 65px !important;
    }
}

/* Landscape mode adjustments */
@media (max-width: 767.98px) and (orientation: landscape) {
    .enterprise-footer {
        padding-bottom: 55px !important; /* Match landscape bottom nav height */
    }
    
    body.admin-user .enterprise-footer {
        padding-bottom: 55px !important;
    }
}

/* Fix for enterprise dashboard pages */
.enterprise-dashboard + .enterprise-footer {
    margin-top: 0 !important;
}

/* Fix for dashboard content to take up available space */
.dashboard-content {
    flex: 1 0 auto;
}

/* Remove any extra padding that might be causing space */
.footer-main {
    padding-bottom: 0 !important;
}

/* Fix for bottom margin on last dashboard section */
.dashboard-section:last-child {
    margin-bottom: 0 !important;
}

/* Fix for tab content to take up available space */
.tab-content {
    height: 100%;
}

/* Fix for admin tab */
#admin.tab-pane {
    margin-bottom: 0 !important;
}

/* Fix for admin dashboard content */
.admin-dashboard-content {
    margin-bottom: 0 !important;
}
