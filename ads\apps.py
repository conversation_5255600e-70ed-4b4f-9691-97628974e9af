from django.apps import AppConfig


class AdsConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'ads'
    verbose_name = 'Advertisements'

    def ready(self):
        """
        Import signals when the app is ready.
        This ensures that the signal handlers are registered.
        """
        # Import signals using relative imports to avoid issues
        from . import signals
        from . import ad_signals
