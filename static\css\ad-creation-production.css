/**
 * Ad Creation Production CSS
 * Enterprise-grade ad creation styling with optimized performance
 */

/* Ad Creation Container */
.ad-creation-container {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 30px;
}

/* Ad Creation Header */
.ad-creation-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
}

.ad-creation-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 10px;
}

.ad-creation-subtitle {
    font-size: 1rem;
    color: #6c757d;
}

/* Ad Creation Form */
.ad-creation-form {
    padding: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    font-weight: 600;
    margin-bottom: 8px;
    color: #495057;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ced4da;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    outline: none;
}

.form-text {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 5px;
}

/* Ad Preview */
.ad-preview {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 20px;
    margin-bottom: 20px;
}

.ad-preview-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
}

.ad-preview-content {
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 15px;
    min-height: 150px;
}

.ad-preview-placeholder {
    color: #adb5bd;
    font-style: italic;
}

/* Smart Engine Toggle */
.smart-engine-toggle {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 5px;
    border: 1px solid #dee2e6;
}

.toggle-label {
    font-weight: 600;
    margin-right: 10px;
    color: #495057;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 34px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: #d4af37;
}

input:focus + .toggle-slider {
    box-shadow: 0 0 1px #d4af37;
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

.toggle-description {
    margin-left: 15px;
    font-size: 0.875rem;
    color: #6c757d;
}

/* AI Suggestions */
.ai-suggestions {
    margin-top: 20px;
    display: none;
}

.ai-suggestions.active {
    display: block;
}

.suggestion-card {
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 15px;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.suggestion-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.suggestion-card.selected {
    border-color: #d4af37;
    box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.3);
}

.suggestion-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.suggestion-content {
    color: #6c757d;
    font-size: 0.9rem;
}

/* Buttons */
.btn {
    display: inline-block;
    font-weight: 600;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    user-select: none;
    border: 1px solid transparent;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: 5px;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    color: #fff;
    background-color: #d4af37;
    border-color: #d4af37;
}

.btn-primary:hover {
    background-color: #c4a030;
    border-color: #b8952d;
}

.btn-secondary {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-secondary:hover {
    background-color: #5a6268;
    border-color: #545b62;
}

.btn-outline {
    color: #d4af37;
    background-color: transparent;
    border-color: #d4af37;
}

.btn-outline:hover {
    color: #fff;
    background-color: #d4af37;
}

.btn-block {
    display: block;
    width: 100%;
}

.btn-group {
    display: flex;
    gap: 10px;
}

/* Loading Spinner */
.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
    margin-right: 10px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Styles */
@media (max-width: 767px) {
    .ad-creation-container {
        border-radius: 0;
        margin-left: -15px;
        margin-right: -15px;
    }
    
    .btn-group {
        flex-direction: column;
    }
    
    .form-group {
        margin-bottom: 15px;
    }
}
