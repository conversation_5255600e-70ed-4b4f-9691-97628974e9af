import os
import django
import uuid
from django.utils import timezone

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'qrcode_project.settings')
django.setup()

from django.contrib.auth.models import User
from qrcode_app.models import UserProfile

# Get the superuser
try:
    user = User.objects.get(username='peter')
    print(f"Found user: {user.username}")
    
    # Check if the user already has a profile
    try:
        profile = UserProfile.objects.get(user=user)
        print(f"User already has a profile with role: {profile.role}")
    except UserProfile.DoesNotExist:
        # Create a profile for the user
        profile = UserProfile(
            user=user,
            role='superadmin',
            company='Codegx Technology',
            api_key=uuid.uuid4(),
            created_at=timezone.now(),
            updated_at=timezone.now()
        )
        profile.save()
        print(f"Created profile for user: {user.username} with role: {profile.role}")
except User.DoesNotExist:
    print("User 'peter' not found.")
