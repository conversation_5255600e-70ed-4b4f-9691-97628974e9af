"""
Cache utilities for AI suggestions using Redis
"""
import json
import logging
import redis

logger = logging.getLogger(__name__)

# Try to connect to Redis, fallback to file-based cache if Redis is not available
try:
    r = redis.StrictRedis(host='localhost', port=6379, db=0)
    # Test the connection
    r.ping()
    REDIS_AVAILABLE = True
    logger.info("Redis connection successful, using Redis for caching")
except Exception as e:
    REDIS_AVAILABLE = False
    logger.warning(f"Redis connection failed: {str(e)}. Using file-based cache as fallback.")

    # Import file-based cache dependencies
    import os
    import time
    from pathlib import Path

    # Cache directory for file-based cache
    CACHE_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'cache')
    os.makedirs(CACHE_DIR, exist_ok=True)

# Cache settings
CACHE_TTL = 21600  # 6 hours in seconds

def get_cached_suggestions(key, max_age=None):
    """
    Get cached suggestions from Redis or file-based cache

    Args:
        key: The cache key
        max_age: Maximum age in seconds (default: use CACHE_TTL)

    Returns:
        The cached suggestions or None if not found
    """
    if max_age is None:
        max_age = CACHE_TTL
    if REDIS_AVAILABLE:
        try:
            raw = r.get(key)
            if raw:
                try:
                    return json.loads(raw)
                except:
                    logger.error(f"Error parsing Redis cache data for key: {key}")
                    return None
            return None
        except Exception as e:
            logger.error(f"Error retrieving from Redis cache: {str(e)}")
            return None
    else:
        # Fallback to file-based cache
        try:
            cache_path = os.path.join(CACHE_DIR, f"{key}.json")

            if not os.path.exists(cache_path):
                return None

            with open(cache_path, 'r') as f:
                cache_data = json.load(f)

            # Check if cache is expired
            timestamp = cache_data.get('timestamp', 0)
            if time.time() - timestamp > max_age:
                logger.debug(f"Cache expired: {key} (age: {time.time() - timestamp:.0f}s, max_age: {max_age}s)")
                return None

            logger.info(f"Cache hit: {key}")
            return cache_data.get('suggestions')
        except Exception as e:
            logger.error(f"Error retrieving from file cache: {str(e)}")
            return None

def cache_suggestions(key, data, ttl=21600):
    """
    Cache suggestions to Redis or file-based cache

    Args:
        key: The cache key
        data: The data to cache
        ttl: Time-to-live in seconds (default: 6 hours)
    """
    if REDIS_AVAILABLE:
        try:
            r.setex(key, ttl, json.dumps(data))
            logger.info(f"Cached suggestions in Redis: {key}")
        except Exception as e:
            logger.error(f"Error caching to Redis: {str(e)}")
    else:
        # Fallback to file-based cache
        try:
            cache_path = os.path.join(CACHE_DIR, f"{key}.json")

            # Add timestamp to cached data
            cache_data = {
                'timestamp': time.time(),
                'ttl': ttl,
                'suggestions': data
            }

            with open(cache_path, 'w') as f:
                json.dump(cache_data, f)

            logger.info(f"Cached suggestions in file: {key}")
        except Exception as e:
            logger.error(f"Error caching to file: {str(e)}")
