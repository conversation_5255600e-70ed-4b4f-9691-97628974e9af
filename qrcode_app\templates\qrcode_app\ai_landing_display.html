<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="{{ ai_page.title }}">
    <title>{{ ai_page.title }}</title>
    
    <!-- SEO and Social Media Meta Tags -->
    <meta property="og:title" content="{{ ai_page.title }}">
    <meta property="og:description" content="{{ ai_page.prompt|truncatechars:160 }}">
    <meta property="og:type" content="website">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{{ ai_page.title }}">
    <meta name="twitter:description" content="{{ ai_page.prompt|truncatechars:160 }}">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Additional Styling for AI Landing Pages -->
    <style>
        /* Ensure mobile responsiveness */
        * {
            box-sizing: border-box;
        }
        
        body {
            margin: 0;
            padding: 0;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: #333;
        }
        
        /* Responsive images */
        img {
            max-width: 100%;
            height: auto;
        }
        
        /* Responsive containers */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        
        /* Mobile-first responsive design */
        @media (max-width: 768px) {
            body {
                font-size: 14px;
            }
            
            h1 {
                font-size: 1.8rem !important;
            }
            
            h2 {
                font-size: 1.5rem !important;
            }
            
            h3 {
                font-size: 1.3rem !important;
            }
            
            .container {
                padding: 0 0.5rem;
            }
        }
        
        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }
        
        /* Accessibility improvements */
        a:focus,
        button:focus {
            outline: 2px solid {{ ai_page.primary_color }};
            outline-offset: 2px;
        }
        
        /* Print styles */
        @media print {
            body {
                background: white !important;
                color: black !important;
            }
        }
        
        /* Loading animation for any dynamic content */
        .loading {
            opacity: 0;
            animation: fadeIn 0.5s ease-in-out forwards;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* Enhanced button styles */
        .btn, button, .button {
            transition: all 0.3s ease;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            cursor: pointer;
        }
        
        .btn:hover, button:hover, .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        /* Enhanced card styles */
        .card {
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        /* Analytics tracking (invisible) */
        .ai-page-analytics {
            position: absolute;
            width: 1px;
            height: 1px;
            opacity: 0;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <!-- Analytics Tracking -->
    <div class="ai-page-analytics" data-page-id="{{ ai_page.id }}" data-qr-code="{{ qr_code.id }}"></div>
    
    <!-- AI Generated Content -->
    <div class="ai-generated-content loading">
        {{ html_content }}
    </div>
    
    <!-- Powered By Footer (for non-premium users) -->
    {% if not ai_page.user.userprofile.is_premium %}
    <div style="
        position: fixed;
        bottom: 10px;
        right: 10px;
        background: rgba(0,0,0,0.8);
        color: white;
        padding: 8px 12px;
        border-radius: 20px;
        font-size: 12px;
        z-index: 1000;
        backdrop-filter: blur(10px);
    ">
        <i class="fas fa-magic" style="margin-right: 5px;"></i>
        <a href="/" style="color: white; text-decoration: none;">Powered by AI QR Generator</a>
    </div>
    {% endif %}
    
    <!-- Enhanced JavaScript for AI Landing Pages -->
    <script>
        // Page load analytics
        document.addEventListener('DOMContentLoaded', function() {
            // Track page view
            const analytics = document.querySelector('.ai-page-analytics');
            if (analytics) {
                // You can add analytics tracking here
                console.log('AI Landing Page viewed:', {
                    pageId: analytics.dataset.pageId,
                    qrCode: analytics.dataset.qrCode,
                    timestamp: new Date().toISOString()
                });
            }
            
            // Add loading animation to content
            const content = document.querySelector('.ai-generated-content');
            if (content) {
                content.classList.add('loading');
            }
            
            // Smooth scroll for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
            
            // Enhanced button interactions
            document.querySelectorAll('.btn, button, .button').forEach(button => {
                button.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });
                
                button.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
            
            // Lazy loading for images
            if ('IntersectionObserver' in window) {
                const imageObserver = new IntersectionObserver((entries, observer) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            if (img.dataset.src) {
                                img.src = img.dataset.src;
                                img.classList.remove('lazy');
                                imageObserver.unobserve(img);
                            }
                        }
                    });
                });
                
                document.querySelectorAll('img[data-src]').forEach(img => {
                    imageObserver.observe(img);
                });
            }
            
            // Form enhancements
            document.querySelectorAll('form').forEach(form => {
                form.addEventListener('submit', function(e) {
                    const submitBtn = form.querySelector('button[type="submit"], input[type="submit"]');
                    if (submitBtn) {
                        submitBtn.style.opacity = '0.7';
                        submitBtn.disabled = true;
                        
                        // Re-enable after 3 seconds to prevent permanent disable
                        setTimeout(() => {
                            submitBtn.style.opacity = '1';
                            submitBtn.disabled = false;
                        }, 3000);
                    }
                });
            });
            
            // Add copy-to-clipboard functionality for any elements with data-copy attribute
            document.querySelectorAll('[data-copy]').forEach(element => {
                element.style.cursor = 'pointer';
                element.addEventListener('click', function() {
                    const textToCopy = this.dataset.copy || this.textContent;
                    navigator.clipboard.writeText(textToCopy).then(() => {
                        // Show feedback
                        const originalText = this.textContent;
                        this.textContent = 'Copied!';
                        setTimeout(() => {
                            this.textContent = originalText;
                        }, 2000);
                    });
                });
            });
        });
        
        // Performance monitoring
        window.addEventListener('load', function() {
            // Log page load time
            const loadTime = performance.now();
            console.log('AI Landing Page loaded in:', Math.round(loadTime), 'ms');
            
            // Check for any broken images
            document.querySelectorAll('img').forEach(img => {
                img.addEventListener('error', function() {
                    this.style.display = 'none';
                    console.warn('Failed to load image:', this.src);
                });
            });
        });
        
        // Mobile-specific enhancements
        if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
            document.addEventListener('DOMContentLoaded', function() {
                // Prevent zoom on input focus for iOS
                document.querySelectorAll('input, select, textarea').forEach(element => {
                    element.addEventListener('focus', function() {
                        document.querySelector('meta[name="viewport"]').setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0');
                    });
                    
                    element.addEventListener('blur', function() {
                        document.querySelector('meta[name="viewport"]').setAttribute('content', 'width=device-width, initial-scale=1.0');
                    });
                });
            });
        }
    </script>
</body>
</html>
