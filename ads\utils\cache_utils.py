"""
Cache utilities for ad suggestions
"""
import logging
import json
import time
from typing import List, Dict, Any, Optional
import hashlib

logger = logging.getLogger(__name__)

def generate_cache_key(language: str, business_type: str, target_audience: str, title: str = "") -> str:
    """
    Generate a cache key for ad suggestions
    
    Args:
        language: The language for the suggestions
        business_type: The business type
        target_audience: The target audience
        title: Optional title
        
    Returns:
        A cache key string
    """
    # Create a string with all parameters
    key_string = f"ad_suggestions:{language}:{business_type}:{target_audience}:{title}"
    
    # Generate a hash of the string
    return hashlib.md5(key_string.encode()).hexdigest()

def get_cached_suggestions(language: str, business_type: str, target_audience: str, title: str = "", max_age: int = 21600) -> Optional[List[Dict[str, Any]]]:
    """
    Get cached suggestions if they exist and are valid
    
    Args:
        language: The language for the suggestions
        business_type: The business type
        target_audience: The target audience
        title: Optional title
        max_age: Maximum age of cache in seconds (default: 6 hours)
        
    Returns:
        List of suggestions or None if not found or invalid
    """
    # Import cache functions from the appropriate module
    try:
        # Try to use enhanced caching system first (Redis or SQLite)
        try:
            from ai_services.enhanced_cache import get_from_cache
            cache_type = "enhanced"
        except ImportError:
            # Fall back to file-based caching
            from ai_services.cache import get_from_cache
            cache_type = "file"
            
        # Generate a cache key
        from ai_services.cache import generate_cache_key
        cache_key = generate_cache_key(
            prompt="ad_suggestions",
            language=language,
            business_type=business_type,
            target_audience=target_audience,
            title=title
        )
        
        # Log the cache lookup attempt
        logger.debug(f"[CACHE_USED] Looking for suggestions in {cache_type} cache with key: {cache_key}")
        
        # Try to get from cache
        cached_data = get_from_cache(cache_key, max_age=max_age)
        
        # Check if we got valid data
        if cached_data is None:
            logger.debug(f"[CACHE_MISSED] No suggestions found in cache for key: {cache_key}")
            return None
            
        # Validate the cached data
        if not isinstance(cached_data, list):
            logger.warning(f"[CACHE_INVALID] Cached data is not a list: {type(cached_data)}")
            return None
            
        if len(cached_data) == 0:
            logger.warning(f"[CACHE_INVALID] Cached data is an empty list")
            return None
            
        # Check if each suggestion has the required fields
        for suggestion in cached_data:
            if not isinstance(suggestion, dict):
                logger.warning(f"[CACHE_INVALID] Suggestion is not a dictionary: {type(suggestion)}")
                return None
                
            if 'title' not in suggestion or 'content' not in suggestion:
                logger.warning(f"[CACHE_INVALID] Suggestion missing required fields: {suggestion.keys()}")
                return None
        
        # Log cache hit
        logger.info(f"[CACHE_USED] Retrieved {len(cached_data)} suggestions from {cache_type} cache")
        
        return cached_data
        
    except Exception as e:
        logger.error(f"[CACHE_ERROR] Error retrieving from cache: {str(e)}")
        return None
        
def get_fallback_suggestions(language: str, title: str = "") -> List[Dict[str, Any]]:
    """
    Get fallback suggestions when cache and API both fail
    
    Args:
        language: The language for the suggestions
        title: Optional title
        
    Returns:
        List of fallback suggestions
    """
    logger.info(f"[FALLBACK_TRIGGERED] Generating hardcoded fallback suggestions for {language}")
    
    # Define content templates for each language
    content_templates = {
        'swahili': [
            "Pata huduma za hali ya juu kwa bei nafuu. Wasiliana nasi leo!",
            "Tumia teknolojia ya kisasa kukuza biashara yako. Jiunge na wateja wengine wanaofanikiwa.",
            "Tunaahidi ubora na ufanisi katika kila huduma. Piga simu leo!"
        ],
        'sheng': [
            "Tukufix na tech fiti sana. Holla sisi leo!",
            "Na hizi services zetu za kubamba. Weka order leo!",
            "Hatupati kashida, tunakusolve na bei poa. Kuja sasa!"
        ],
        'english': [
            "Get high-quality services at affordable prices. Contact us today!",
            "Use modern technology to grow your business. Join our successful clients.",
            "We deliver excellence in every service. Call us now for a consultation!"
        ]
    }
    
    # Default to English if language not supported
    if language not in content_templates:
        language = 'english'
        
    # Define title templates for each language if no title is provided
    title_templates = {
        'swahili': [
            "Suluhisho Bora kwa Biashara Yako",
            "Kuza Biashara Yako Leo",
            "Huduma Bora kwa Bei Nafuu"
        ],
        'sheng': [
            "Pata Solution za Kuunda",
            "Biashara Yako Inakam Juu",
            "Pata Deals Fiti"
        ],
        'english': [
            "Premium Solutions for Your Business",
            "Grow Your Business Today",
            "Premium Solutions You Can Trust"
        ]
    }
    
    # Generate suggestions
    suggestions = []
    for i in range(3):  # Generate 3 suggestions
        if i == 0 and title:
            # Use provided title for first suggestion
            suggestion_title = title
        else:
            # Use template title
            suggestion_title = title_templates[language][i]
            
        content = content_templates[language][i]
        
        suggestions.append({
            "title": suggestion_title,
            "content": content,
            "fallback": True
        })
    
    logger.debug(f"[FALLBACK_TRIGGERED] Generated {len(suggestions)} hardcoded fallback suggestions")
    
    return suggestions
