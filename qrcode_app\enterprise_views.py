"""
Enterprise-grade views for advanced QR code analytics and management
"""
from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required, permission_required
from django.contrib import messages
from django.http import JsonResponse
from django.db.models import Count, Q, Avg
from django.utils import timezone
from datetime import timedelta, datetime
from collections import defaultdict
import json

from .models import QRCode, QRCodeScan, QRCodeBranding, QRLink, UserProfile


@login_required
def enterprise_dashboard(request):
    """
    Enterprise dashboard with advanced analytics and heatmaps
    """
    user = request.user

    # Check if user has premium access
    try:
        user_profile = UserProfile.objects.get(user=user)
        if not user_profile.is_premium():
            messages.warning(request, 'Enterprise dashboard requires premium access.')
            return redirect('qr_code_list')
    except UserProfile.DoesNotExist:
        messages.warning(request, 'Enterprise dashboard requires premium access.')
        return redirect('qr_code_list')

    # Get user's QR codes
    qr_codes = QRCode.objects.filter(user=user)

    # Get date range (default: last 30 days)
    days = int(request.GET.get('days', 30))
    start_date = timezone.now() - timedelta(days=days)

    # Get scans in date range
    scans = QRCodeScan.objects.filter(
        qr_code__user=user,
        scanned_at__gte=start_date
    )

    # Basic metrics
    total_qr_codes = qr_codes.count()
    total_scans = scans.count()
    unique_scanners = scans.values('ip_address').distinct().count()

    # Calculate average scans per user
    avg_scans_per_user = total_scans / unique_scanners if unique_scanners > 0 else 0

    # Scanner type distribution
    scanner_distribution = scans.values('scanner_type').annotate(
        count=Count('id')
    ).order_by('-count')

    # Geographic distribution
    geo_distribution = scans.exclude(
        country__in=['Unknown', 'Local', None]
    ).values('country').annotate(
        count=Count('id')
    ).order_by('-count')[:10]

    # Organization/ISP distribution (Enterprise feature)
    org_distribution = scans.exclude(
        organization__in=['Unknown', 'Local Network', None]
    ).values('organization').annotate(
        count=Count('id')
    ).order_by('-count')[:10]

    # Privacy flags analysis (Enterprise feature)
    privacy_analysis = {
        'vpn_scans': scans.filter(privacy_flags__vpn=True).count(),
        'proxy_scans': scans.filter(privacy_flags__proxy=True).count(),
        'tor_scans': scans.filter(privacy_flags__tor=True).count(),
        'hosting_scans': scans.filter(privacy_flags__hosting=True).count(),
    }

    # Daily scan trends
    daily_scans = defaultdict(int)
    for i in range(days):
        date = (timezone.now() - timedelta(days=i)).date()
        daily_scans[date.isoformat()] = 0

    scan_dates = scans.extra(
        select={'date': 'DATE(scanned_at)'}
    ).values('date').annotate(
        count=Count('id')
    )

    for item in scan_dates:
        if item['date']:
            # Convert date to string if it's a date object, otherwise use as-is
            date_str = item['date'].isoformat() if hasattr(item['date'], 'isoformat') else str(item['date'])
            daily_scans[date_str] = item['count']

    # Top performing QR codes
    top_qr_codes = qr_codes.annotate(
        scan_count=Count('scans', filter=Q(scans__scanned_at__gte=start_date))
    ).order_by('-scan_count')[:5]

    # Device type distribution
    device_distribution = scans.exclude(
        device_type__in=['Unknown', None]
    ).values('device_type').annotate(
        count=Count('id')
    ).order_by('-count')

    # QR code status distribution
    status_distribution = qr_codes.values('status').annotate(
        count=Count('id')
    ).order_by('-count')

    context = {
        'total_qr_codes': total_qr_codes,
        'total_scans': total_scans,
        'unique_scanners': unique_scanners,
        'avg_scans_per_user': avg_scans_per_user,
        'days': days,
        'scanner_distribution': list(scanner_distribution),
        'geo_distribution': list(geo_distribution),
        'org_distribution': list(org_distribution),
        'privacy_analysis': privacy_analysis,
        'daily_scans': dict(daily_scans),
        'top_qr_codes': top_qr_codes,
        'device_distribution': list(device_distribution),
        'status_distribution': list(status_distribution),
    }

    return render(request, 'qrcode_app/enterprise_dashboard.html', context)


@login_required
def scan_heatmap_data(request):
    """
    API endpoint for scan heatmap data
    """
    user = request.user

    # Check premium access
    try:
        user_profile = UserProfile.objects.get(user=user)
        if not user_profile.is_premium():
            return JsonResponse({'error': 'Premium access required'}, status=403)
    except UserProfile.DoesNotExist:
        return JsonResponse({'error': 'Premium access required'}, status=403)

    # Get date range
    days = int(request.GET.get('days', 30))
    start_date = timezone.now() - timedelta(days=days)

    # Get scans with coordinates
    scans = QRCodeScan.objects.filter(
        qr_code__user=user,
        scanned_at__gte=start_date,
        latitude__isnull=False,
        longitude__isnull=False
    ).values('latitude', 'longitude', 'city', 'country')

    # Format for heatmap
    heatmap_data = []
    for scan in scans:
        heatmap_data.append({
            'lat': float(scan['latitude']),
            'lng': float(scan['longitude']),
            'city': scan['city'],
            'country': scan['country'],
            'weight': 1
        })

    return JsonResponse({'heatmap_data': heatmap_data})


@login_required
def branding_management(request):
    """
    Manage QR code branding themes (Enterprise feature)
    """
    user = request.user

    # Check premium access
    try:
        user_profile = UserProfile.objects.get(user=user)
        if not user_profile.is_premium():
            messages.warning(request, 'Branding management requires premium access.')
            return redirect('qr_code_list')
    except UserProfile.DoesNotExist:
        messages.warning(request, 'Branding management requires premium access.')
        return redirect('qr_code_list')

    # Get user's branding themes
    brandings = QRCodeBranding.objects.filter(user=user)

    context = {
        'brandings': brandings,
    }

    return render(request, 'qrcode_app/branding_management.html', context)


@login_required
def short_links_management(request):
    """
    Manage short links (Enterprise feature)
    """
    user = request.user

    # Check premium access
    try:
        user_profile = UserProfile.objects.get(user=user)
        if not user_profile.is_premium():
            messages.warning(request, 'Short links management requires premium access.')
            return redirect('qr_code_list')
    except UserProfile.DoesNotExist:
        messages.warning(request, 'Short links management requires premium access.')
        return redirect('qr_code_list')

    # Get user's short links
    short_links = QRLink.objects.filter(user=user)

    context = {
        'short_links': short_links,
    }

    return render(request, 'qrcode_app/short_links_management.html', context)


@login_required
@permission_required('qrcode_app.view_performance_dashboard', raise_exception=True)
def admin_approval_dashboard(request):
    """
    Admin dashboard for QR code approval workflow
    """
    # Get QR codes pending approval
    pending_qr_codes = QRCode.objects.filter(
        status=QRCode.QRStatus.PENDING,
        requires_approval=True
    )

    # Get QR links pending approval
    pending_links = QRLink.objects.filter(
        status=QRLink.LinkStatus.PENDING,
        requires_approval=True
    )

    context = {
        'pending_qr_codes': pending_qr_codes,
        'pending_links': pending_links,
    }

    return render(request, 'qrcode_app/admin_approval_dashboard.html', context)


@login_required
@permission_required('qrcode_app.view_performance_dashboard', raise_exception=True)
def approve_qr_code(request, qr_id):
    """
    Approve a QR code
    """
    if request.method == 'POST':
        qr_code = get_object_or_404(QRCode, id=qr_id)
        qr_code.approve(request.user)
        messages.success(request, f'QR code "{qr_code.name}" has been approved.')

    return redirect('admin_approval_dashboard')


@login_required
@permission_required('qrcode_app.view_performance_dashboard', raise_exception=True)
def reject_qr_code(request, qr_id):
    """
    Reject a QR code
    """
    if request.method == 'POST':
        qr_code = get_object_or_404(QRCode, id=qr_id)
        reason = request.POST.get('reason', 'No reason provided')
        qr_code.reject(request.user, reason)
        messages.warning(request, f'QR code "{qr_code.name}" has been rejected.')

    return redirect('admin_approval_dashboard')


@login_required
def country_analytics_dashboard(request):
    """
    Country-based analytics dashboard (Bonus Enterprise feature)
    """
    user = request.user

    # Check premium access
    try:
        user_profile = UserProfile.objects.get(user=user)
        if not user_profile.is_premium():
            messages.warning(request, 'Country analytics requires premium access.')
            return redirect('qr_code_list')
    except UserProfile.DoesNotExist:
        messages.warning(request, 'Country analytics requires premium access.')
        return redirect('qr_code_list')

    # Get date range
    days = int(request.GET.get('days', 30))
    start_date = timezone.now() - timedelta(days=days)

    # Get scans in date range
    scans = QRCodeScan.objects.filter(
        qr_code__user=user,
        scanned_at__gte=start_date
    ).exclude(country__in=['Unknown', 'Local', None])

    # Country statistics
    country_stats = scans.values('country').annotate(
        total_scans=Count('id'),
        unique_scanners=Count('ip_address', distinct=True),
        vpn_scans=Count('id', filter=Q(privacy_flags__vpn=True)),
        proxy_scans=Count('id', filter=Q(privacy_flags__proxy=True)),
        tor_scans=Count('id', filter=Q(privacy_flags__tor=True))
    ).order_by('-total_scans')

    # Top organizations by country
    org_by_country = {}
    for country_data in country_stats[:10]:  # Top 10 countries
        country = country_data['country']
        top_orgs = scans.filter(country=country).exclude(
            organization__in=['Unknown', 'Local Network', None]
        ).values('organization').annotate(
            count=Count('id')
        ).order_by('-count')[:5]
        org_by_country[country] = list(top_orgs)

    context = {
        'country_stats': list(country_stats),
        'org_by_country': org_by_country,
        'days': days,
        'total_countries': country_stats.count(),
        'total_scans': scans.count(),
    }

    return render(request, 'qrcode_app/country_analytics.html', context)