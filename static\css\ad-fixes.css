/* Ad Fixes CSS */

/* Fix for newsletter component */
.newsletter-form {
    position: relative;
    z-index: 1;
    width: 100%;
    max-width: 100%;
}

/* Standard ad sizes for different locations */
.ad-image {
    object-fit: cover;
}

/* Header ad - 728x90 (Leaderboard) */
.header-ad-container .ad-image {
    width: 100%;
    max-width: 728px;
    height: 90px;
    margin: 0 auto;
}

/* Sidebar ad - 300x250 (Medium Rectangle) */
.sidebar-ad-container .ad-image {
    width: 100%;
    max-width: 300px;
    height: 250px;
    margin: 0 auto;
}

/* Content ad - 336x280 (Large Rectangle) */
.content-top-ad-container .ad-image,
.content-bottom-ad-container .ad-image {
    width: 100%;
    max-width: 336px;
    height: 280px;
    margin: 0 auto;
}

/* Footer ad - 728x90 (Leaderboard) */
.footer-ad-container .ad-image {
    width: 100%;
    max-width: 728px;
    height: 90px;
    margin: 0 auto;
}

/* Popup ad - 300x250 (Medium Rectangle) */
.popup-ad-container .ad-image {
    width: 100%;
    max-width: 300px;
    height: 250px;
}

/* Ad containers */
.ad-container {
    width: 100%;
    max-width: 100%;
    margin: 0 auto;
    text-align: center;
    overflow: hidden;
}

/* Ensure ads don't overflow their containers */
.ad-item {
    max-width: 100%;
    margin: 0 auto;
}

/* Fix for ad display in mobile view */
@media (max-width: 768px) {
    .header-ad-container .ad-image,
    .footer-ad-container .ad-image {
        max-width: 100%;
        height: 50px;
    }
    
    .sidebar-ad-container .ad-image,
    .content-top-ad-container .ad-image,
    .content-bottom-ad-container .ad-image {
        max-width: 100%;
        height: 150px;
    }
}

/* Fix for newsletter component in footer */
.footer-main .newsletter-form {
    position: relative;
    z-index: 1;
    width: 100%;
    max-width: 100%;
}

/* Fix for newsletter input group */
.newsletter-input-group {
    position: relative;
    z-index: 1;
    width: 100%;
    max-width: 100%;
}
