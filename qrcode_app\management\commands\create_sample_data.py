"""
Management command to create sample data for testing
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from qrcode_app.models import QRCode, UserProfile, Plan, Subscription
from ads.models import Ad
from django.utils import timezone


class Command(BaseCommand):
    help = 'Create sample data for testing'

    def handle(self, *args, **options):
        self.stdout.write("🔄 Creating sample data...")
        
        # Create test users
        self.stdout.write("👥 Creating test users...")
        
        # Create apollo user
        apollo, created = User.objects.get_or_create(
            username='apollo',
            defaults={
                'email': '<EMAIL>',
                'is_active': True
            }
        )
        if created or not apollo.check_password('2587'):
            apollo.set_password('2587')
            apollo.save()
            self.stdout.write("✅ Created/updated apollo user")
        
        # Create peter superuser
        peter, created = User.objects.get_or_create(
            username='peter',
            defaults={
                'email': '<EMAIL>',
                'is_superuser': True,
                'is_staff': True,
                'is_active': True
            }
        )
        if created or not peter.check_password('2587'):
            peter.set_password('2587')
            peter.is_superuser = True
            peter.is_staff = True
            peter.save()
            self.stdout.write("✅ Created/updated peter superuser")
        
        # Create profiles
        apollo_profile, created = UserProfile.objects.get_or_create(
            user=apollo,
            defaults={'role': 'user'}
        )
        peter_profile, created = UserProfile.objects.get_or_create(
            user=peter,
            defaults={'role': 'admin'}
        )
        
        # Create subscriptions
        try:
            free_plan = Plan.objects.get(plan_type='FREE')
            subscription, created = Subscription.objects.get_or_create(
                user=apollo,
                defaults={
                    'plan': free_plan,
                    'status': 'ACTIVE',
                    'started_at': timezone.now(),
                    'scans_this_month': 0
                }
            )
            self.stdout.write("✅ Apollo subscription created")
        except Plan.DoesNotExist:
            self.stdout.write("⚠️ FREE plan not found. Run: python manage.py create_default_plans")
        
        # Create sample QR codes for apollo
        self.stdout.write("📱 Creating sample QR codes...")
        
        sample_qrs = [
            {
                'name': 'My Business Card',
                'qr_type': 'url',
                'data': 'https://mycompany.com/contact',
                'description': 'Professional business card QR code'
            },
            {
                'name': 'Restaurant Menu',
                'qr_type': 'url', 
                'data': 'https://restaurant.com/menu',
                'description': 'Digital menu for customers'
            },
            {
                'name': 'WiFi Access',
                'qr_type': 'wifi',
                'data': 'WIFI:T:WPA;S:MyNetwork;P:password123;;',
                'description': 'Guest WiFi access'
            }
        ]
        
        for qr_data in sample_qrs:
            qr_code, created = QRCode.objects.get_or_create(
                user=apollo,
                name=qr_data['name'],
                defaults={
                    'qr_type': qr_data['qr_type'],
                    'data': qr_data['data'],
                    'description': qr_data['description'],
                    'is_active': True
                }
            )
            if created:
                self.stdout.write(f"✅ Created QR code: {qr_data['name']}")
        
        # Create sample QR codes for peter (admin)
        admin_qrs = [
            {
                'name': 'Admin Dashboard',
                'qr_type': 'url',
                'data': 'https://admin.mysite.com/dashboard',
                'description': 'Quick access to admin panel'
            },
            {
                'name': 'Support Portal',
                'qr_type': 'url',
                'data': 'https://support.mycompany.com',
                'description': 'Customer support portal'
            }
        ]
        
        for qr_data in admin_qrs:
            qr_code, created = QRCode.objects.get_or_create(
                user=peter,
                name=qr_data['name'],
                defaults={
                    'qr_type': qr_data['qr_type'],
                    'data': qr_data['data'],
                    'description': qr_data['description'],
                    'is_active': True
                }
            )
            if created:
                self.stdout.write(f"✅ Created admin QR code: {qr_data['name']}")
        
        # Create sample ads
        self.stdout.write("📢 Creating sample ads...")
        
        sample_ads = [
            {
                'title': 'Premium QR Features',
                'content': 'Upgrade to QR Pro for unlimited codes, analytics, and branding!',
                'location': 'header',
                'ad_type': 'premium',
                'is_active': True
            },
            {
                'title': 'AI Landing Pages',
                'content': 'Create professional landing pages with AI. No coding required!',
                'location': 'sidebar',
                'ad_type': 'feature',
                'is_active': True
            },
            {
                'title': 'Webhook Integration',
                'content': 'Connect your QR scans to Zapier, CRM systems, and more!',
                'location': 'footer',
                'ad_type': 'integration',
                'is_active': True
            }
        ]
        
        for ad_data in sample_ads:
            ad, created = Ad.objects.get_or_create(
                title=ad_data['title'],
                defaults={
                    'content': ad_data['content'],
                    'location': ad_data['location'],
                    'ad_type': ad_data['ad_type'],
                    'is_active': ad_data['is_active'],
                    'created_by': peter
                }
            )
            if created:
                self.stdout.write(f"✅ Created ad: {ad_data['title']}")
        
        # Final summary
        self.stdout.write("\n📊 Data Summary:")
        self.stdout.write(f"Total QR codes: {QRCode.objects.count()}")
        self.stdout.write(f"Total ads: {Ad.objects.count()}")
        self.stdout.write(f"Total users: {User.objects.count()}")
        
        self.stdout.write(
            self.style.SUCCESS("\n🎉 Sample data created successfully!")
        )
        
        self.stdout.write("\nTest Accounts:")
        self.stdout.write("Username: apollo | Password: 2587 | Role: user | Plan: FREE")
        self.stdout.write("Username: peter  | Password: 2587 | Role: admin | Superuser: Yes")
        
        self.stdout.write("\nNext Steps:")
        self.stdout.write("1. Visit: http://127.0.0.1:8000/login/")
        self.stdout.write("2. Login as apollo to see sample QR codes")
        self.stdout.write("3. Test plan limits and features")
        self.stdout.write("4. Switch to peter for admin access")
