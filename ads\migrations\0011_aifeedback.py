# Generated by Django 4.2.7 on 2025-05-18 20:17

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('ads', '0010_ad_ai_language_ad_ai_suggestion_ad_used_ai'),
    ]

    operations = [
        migrations.CreateModel(
            name='AiFeedback',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('suggestion_data', models.J<PERSON>NField(help_text='The AI suggestion that received feedback')),
                ('feedback', models.CharField(choices=[('like', 'Like'), ('dislike', 'Dislike'), ('neutral', 'Neutral')], max_length=10)),
                ('comments', models.TextField(blank=True, help_text='Optional user comments about the suggestion', null=True)),
                ('model_used', models.<PERSON>r<PERSON>ield(blank=True, help_text='The AI model that generated the content', max_length=50, null=True)),
                ('language', models.CharField(blank=True, max_length=20, null=True)),
                ('page', models.Char<PERSON>ield(blank=True, help_text='The page where feedback was given', max_length=255, null=True)),
                ('processing_time', models.FloatField(default=0, help_text='Time taken to generate the suggestion in seconds')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('ad', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='ai_feedback', to='ads.ad')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ai_feedback', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'AI Feedback',
                'verbose_name_plural': 'AI Feedback',
                'ordering': ['-created_at'],
            },
        ),
    ]
