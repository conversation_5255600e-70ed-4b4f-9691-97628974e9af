/* 
 * Admin Ad Dashboard Footer Styles
 * Specific styles for admin ad dashboard footer
 */

/* Remove top margin for admin users */
body.admin-user .enterprise-footer {
    margin-top: 0 !important;
}

/* Fix for admin ad dashboard content */
.admin-dashboard-content {
    margin-bottom: 0 !important;
}

/* Fix for admin ad sections */
.admin-section {
    margin-bottom: 20px;
}

.admin-section:last-child {
    margin-bottom: 0 !important;
}

/* Fix for admin ad activity list */
.admin-activity-list {
    margin-bottom: 0 !important;
}

/* Fix for admin ad stat cards */
.admin-stat-card {
    margin-bottom: 0 !important;
}

/* Fix for admin ad action buttons */
.admin-action-buttons {
    margin-bottom: 0 !important;
}

/* Fix for admin ad dashboard card */
.dashboard-card:last-child {
    margin-bottom: 0 !important;
}

/* Fix for admin ad tab content */
#admin.tab-pane {
    margin-bottom: 0 !important;
}

/* Fix for admin ad dashboard sections */
#admin .dashboard-section:last-child {
    margin-bottom: 0 !important;
}

/* Fix for admin ad footer on mobile */
@media (max-width: 991.98px) {
    body.admin-user .enterprise-footer {
        padding-bottom: 65px !important;
    }
}

/* Landscape mode adjustments for admin */
@media (max-width: 767.98px) and (orientation: landscape) {
    body.admin-user .enterprise-footer {
        padding-bottom: 55px !important;
    }
}

/* Fix for admin ad tables */
.admin-activity-list .table {
    margin-bottom: 0 !important;
}

/* Fix for admin ad empty states */
.empty-state {
    margin-bottom: 0 !important;
}
