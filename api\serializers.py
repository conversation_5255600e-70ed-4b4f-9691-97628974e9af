from rest_framework import serializers
from django.contrib.auth.models import User
from qrcode_app.models import QRCode, UserProfile, APIKey, QRCodeBatch

class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name']

class UserProfileSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    
    class Meta:
        model = UserProfile
        fields = ['user', 'role', 'company', 'phone', 'address', 'profile_image', 'created_at']

class QRCodeSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    
    class Meta:
        model = QRCode
        fields = ['id', 'user', 'name', 'qr_type', 'data', 'image', 'foreground_color', 
                  'background_color', 'logo', 'is_encrypted', 'created_at', 'updated_at']
        read_only_fields = ['user', 'image', 'created_at', 'updated_at']

class APIKeySerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    
    class Meta:
        model = APIKey
        fields = ['id', 'user', 'name', 'key', 'is_active', 'created_at', 'last_used']
        read_only_fields = ['user', 'key', 'created_at', 'last_used']

class QRCodeBatchSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    
    class Meta:
        model = QRCodeBatch
        fields = ['id', 'user', 'name', 'description', 'zip_file', 'count', 'created_at']
        read_only_fields = ['user', 'zip_file', 'count', 'created_at']
