"""
Management command to create test users for different privilege levels
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from qrcode_app.models import UserProfile, Plan, Subscription
from django.utils import timezone


class Command(BaseCommand):
    help = 'Create test users with different privilege levels'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Reset existing users if they exist',
        )

    def handle(self, *args, **options):
        reset = options['reset']
        
        # Test user accounts to create
        test_users = [
            {
                'username': 'apollo',
                'email': '<EMAIL>',
                'password': '2587',
                'role': 'user',
                'plan': 'FREE',
                'is_superuser': False,
                'description': 'Regular user with free plan'
            },
            {
                'username': 'peter',
                'email': '<EMAIL>',
                'password': '2587',
                'role': 'admin',
                'plan': None,
                'is_superuser': True,
                'description': 'Superuser/Admin account'
            },
            {
                'username': 'admin',
                'email': '<EMAIL>',
                'password': '2587',
                'role': 'admin',
                'plan': None,
                'is_superuser': True,
                'description': 'Alternative admin account'
            }
        ]
        
        self.stdout.write(
            self.style.SUCCESS('Creating test users for privilege testing...\n')
        )
        
        for user_data in test_users:
            username = user_data['username']
            
            # Check if user exists
            try:
                user = User.objects.get(username=username)
                if reset:
                    user.delete()
                    self.stdout.write(f'  Deleted existing user: {username}')
                else:
                    self.stdout.write(
                        self.style.WARNING(f'  User {username} already exists. Use --reset to recreate.')
                    )
                    continue
            except User.DoesNotExist:
                pass
            
            # Create user
            if user_data['is_superuser']:
                user = User.objects.create_superuser(
                    username=user_data['username'],
                    email=user_data['email'],
                    password=user_data['password']
                )
                self.stdout.write(
                    self.style.SUCCESS(f'  ✅ Created superuser: {username}')
                )
            else:
                user = User.objects.create_user(
                    username=user_data['username'],
                    email=user_data['email'],
                    password=user_data['password']
                )
                self.stdout.write(
                    self.style.SUCCESS(f'  ✅ Created user: {username}')
                )
            
            # Create or update user profile
            try:
                profile = UserProfile.objects.get(user=user)
                profile.role = user_data['role']
                profile.save()
                self.stdout.write(f'    Updated profile role: {user_data["role"]}')
            except UserProfile.DoesNotExist:
                profile = UserProfile.objects.create(
                    user=user,
                    role=user_data['role']
                )
                self.stdout.write(f'    Created profile with role: {user_data["role"]}')
            
            # Create subscription for non-admin users
            if user_data['plan'] and not user_data['is_superuser']:
                try:
                    plan = Plan.objects.get(plan_type=user_data['plan'])
                    
                    # Create or update subscription
                    subscription, created = Subscription.objects.update_or_create(
                        user=user,
                        defaults={
                            'plan': plan,
                            'status': 'ACTIVE',
                            'started_at': timezone.now(),
                            'scans_this_month': 0,
                        }
                    )
                    
                    action = 'Created' if created else 'Updated'
                    self.stdout.write(f'    {action} subscription: {plan.name}')
                    
                except Plan.DoesNotExist:
                    self.stdout.write(
                        self.style.WARNING(f'    Plan {user_data["plan"]} not found. Run create_default_plans first.')
                    )
            
            self.stdout.write(f'    Description: {user_data["description"]}\n')
        
        # Display summary
        self.stdout.write(
            self.style.SUCCESS('\n🎉 Test users created successfully!\n')
        )
        
        self.stdout.write('Test Accounts:')
        self.stdout.write('=' * 50)
        
        for user_data in test_users:
            self.stdout.write(f'Username: {user_data["username"]}')
            self.stdout.write(f'Password: {user_data["password"]}')
            self.stdout.write(f'Role: {user_data["role"]}')
            self.stdout.write(f'Type: {"Superuser" if user_data["is_superuser"] else "Regular User"}')
            if user_data['plan']:
                self.stdout.write(f'Plan: {user_data["plan"]}')
            self.stdout.write(f'Purpose: {user_data["description"]}')
            self.stdout.write('-' * 30)
        
        self.stdout.write('\nTesting Scenarios:')
        self.stdout.write('=' * 50)
        self.stdout.write('1. Login as "apollo" (password: 2587) - Test free plan limits')
        self.stdout.write('2. Login as "peter" or "admin" (password: 2587) - Test admin access')
        self.stdout.write('3. Test plan upgrade flow with apollo account')
        self.stdout.write('4. Test feature gating and middleware enforcement')
        self.stdout.write('5. Test Stripe billing integration')
        
        self.stdout.write('\nNext Steps:')
        self.stdout.write('=' * 50)
        self.stdout.write('1. Run: python manage.py create_default_plans')
        self.stdout.write('2. Test login with different accounts')
        self.stdout.write('3. Verify plan limits and feature access')
        self.stdout.write('4. Test billing and upgrade flows')
        
        self.stdout.write(
            self.style.SUCCESS('\n✅ Ready for comprehensive privilege testing!')
        )
