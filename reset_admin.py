#!/usr/bin/env python
"""
Simple script to reset admin credentials
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'qrcode_project.settings')
django.setup()

from django.contrib.auth.models import User

def reset_admin():
    try:
        # Try to get existing user
        user = User.objects.get(username='user')
        user.set_password('admin123')
        user.is_superuser = True
        user.is_staff = True
        user.is_active = True
        user.save()
        print(f"✅ Updated existing user: {user.username}")
        print(f"✅ Password: admin123")
        print(f"✅ Superuser: {user.is_superuser}")
        print(f"✅ Staff: {user.is_staff}")
        print(f"✅ Active: {user.is_active}")
    except User.DoesNotExist:
        # Create new user
        user = User.objects.create_superuser(
            username='user',
            email='<EMAIL>',
            password='admin123'
        )
        print(f"✅ Created new superuser: {user.username}")
        print(f"✅ Password: admin123")
    
    # Also create a backup admin user
    try:
        admin_user = User.objects.get(username='admin')
        admin_user.set_password('admin123')
        admin_user.is_superuser = True
        admin_user.is_staff = True
        admin_user.is_active = True
        admin_user.save()
        print(f"✅ Updated backup admin: {admin_user.username}")
    except User.DoesNotExist:
        admin_user = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='admin123'
        )
        print(f"✅ Created backup admin: {admin_user.username}")
    
    print("\n🎯 ADMIN CREDENTIALS:")
    print("Username: user")
    print("Password: admin123")
    print("\n🎯 BACKUP CREDENTIALS:")
    print("Username: admin")
    print("Password: admin123")
    print("\n🌐 ADMIN URLS:")
    print("Main Admin: http://localhost:8000/admin/")
    print("QR Admin: http://localhost:8000/admin/qr/")
    print("Ads Admin: http://localhost:8000/admin/ads/")
    print("AI Admin: http://localhost:8000/admin/ai/")
    print("Billing Admin: http://localhost:8000/admin/billing/")

if __name__ == '__main__':
    reset_admin()
