# Generated by Django 5.1.7 on 2025-05-28 21:54

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('qrcode_app', '0010_qrscanlog'),
    ]

    operations = [
        migrations.AddField(
            model_name='qrscanlog',
            name='latitude',
            field=models.FloatField(blank=True, help_text='Latitude from IPinfo geolocation', null=True),
        ),
        migrations.AddField(
            model_name='qrscanlog',
            name='longitude',
            field=models.FloatField(blank=True, help_text='Longitude from IPinfo geolocation', null=True),
        ),
    ]
