<!-- WORKING NAVBAR WITH DROPDOWNS -->
<style>
/* Basic navbar positioning */
nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: #1a365d;
    color: white;
    padding: 10px 20px;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Navbar container */
.navbar-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

/* Brand styling */
.navbar-brand {
    color: white;
    text-decoration: none;
    font-weight: bold;
    font-size: 1.2em;
}

/* Hide hamburger on desktop */
.hamburger {
    display: none;
}

/* Navigation menu */
.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 0;
}

/* Navigation items */
.nav-item {
    position: relative;
}

/* Navigation links */
.nav-link {
    display: block;
    color: white;
    text-decoration: none;
    padding: 15px 20px;
    transition: background-color 0.3s;
}

.nav-link:hover {
    background-color: rgba(255,255,255,0.1);
    color: white;
}

/* Dropdown menu */
.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    min-width: 200px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    border-radius: 4px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1001;
}

/* Show dropdown on hover */
.nav-item:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* Dropdown items */
.dropdown-item {
    display: block;
    color: #333;
    text-decoration: none;
    padding: 12px 20px;
    transition: background-color 0.3s;
    position: relative;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
    color: #333;
}

/* Dropdown headers */
.dropdown-header {
    padding: 8px 20px;
    font-size: 0.75rem;
    font-weight: 600;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

/* Dropdown dividers */
.dropdown-divider {
    height: 1px;
    background-color: #e9ecef;
    margin: 8px 0;
}

/* Badges */
.badge {
    display: inline-block;
    padding: 2px 6px;
    font-size: 0.65rem;
    font-weight: 600;
    border-radius: 10px;
    margin-left: 8px;
}

.badge.pro {
    background-color: #ffd700;
    color: #333;
}

.badge.admin {
    background-color: #dc3545;
    color: white;
}

.badge.new {
    background-color: #28a745;
    color: white;
}

.badge {
    background-color: #6f42c1;
    color: white;
}

/* Notification badge */
.notification-badge {
    display: inline-block;
    background-color: #dc3545;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 0.7rem;
    margin-left: 5px;
    min-width: 18px;
    text-align: center;
}

/* Logout item styling */
.dropdown-item.logout {
    color: #dc3545;
}

.dropdown-item.logout:hover {
    background-color: #f8d7da;
    color: #721c24;
}

/* Removed complex submenu styling - using simple dropdowns now */

/* User section */
.user-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 0;
}

/* Mobile styles */
@media (max-width: 768px) {
    .hamburger {
        display: block;
        background: none;
        border: none;
        color: white;
        font-size: 1.5em;
        cursor: pointer;
    }

    .nav-menu,
    .user-menu {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: #1a365d;
        flex-direction: column;
        padding: 20px;
        z-index: 1001;
    }

    .nav-menu.mobile-open,
    .user-menu.mobile-open {
        display: flex !important;
    }

    .nav-menu .nav-item,
    .user-menu .nav-item {
        width: 100%;
        margin-bottom: 10px;
    }

    .nav-menu .nav-link,
    .user-menu .nav-link {
        padding: 15px 10px;
        border-bottom: 1px solid rgba(255,255,255,0.1);
    }

    /* Mobile dropdown menus */
    .nav-menu .dropdown-menu {
        position: static !important;
        display: none;
        background: rgba(255,255,255,0.1);
        box-shadow: none;
        border: none;
        margin-left: 20px;
        margin-top: 10px;
    }

    .nav-menu .nav-item.mobile-dropdown-open .dropdown-menu {
        display: block !important;
    }

    /* Removed mobile submenu CSS - using simple dropdowns now */
}

/* Enhanced dropdown transitions */
.dropdown-active .dropdown-menu {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) !important;
}

/* Hamburger animation */
.hamburger {
    transition: transform 0.3s ease;
}

.hamburger.active {
    transform: rotate(90deg);
}

/* Enhanced mobile menu transitions */
.nav-menu.mobile-open,
.user-menu.mobile-open {
    transform: translateX(0) !important;
}

/* Smooth dropdown item hover */
.dropdown-item {
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    transform: translateX(5px);
    background-color: #f8f9fa !important;
}

/* Body margin for fixed navbar */
body {
    margin-top: 80px;
}
</style>

<nav>
    <div class="navbar-container">
        <!-- Brand -->
        <a href="{% url 'index' %}" class="navbar-brand">🔲 Enterprise QR</a>

        <!-- Hamburger (mobile only) -->
        <button class="hamburger">☰</button>

        <!-- Main Navigation -->
        <ul class="nav-menu">
            <!-- Home with Management Features -->
            <li class="nav-item">
                <a href="#" class="nav-link">Home ▼</a>
                <div class="dropdown-menu">
                    <div class="dropdown-header">Quick Access</div>
                    <a href="{% url 'index' %}" class="dropdown-item">🏠 Dashboard</a>
                    <a href="{% url 'generate_qr_code' %}" class="dropdown-item">🔲 Generate QR Code</a>
                    <a href="{% url 'qr_code_list' %}" class="dropdown-item">📋 My QR Codes</a>
                    <div class="dropdown-divider"></div>
                    <div class="dropdown-header">User Management</div>
                    <a href="{% url 'user_management' %}" class="dropdown-item">👥 User Management</a>
                    <a href="{% url 'role_management' %}" class="dropdown-item">🔑 Role Management</a>
                    <div class="dropdown-divider"></div>
                    <div class="dropdown-header">System Settings</div>
                    <a href="{% url 'settings' %}" class="dropdown-item">⚙️ Settings</a>
                </div>
            </li>

            <!-- Admin Panel - Analytics Focused -->
            <li class="nav-item">
                <a href="#" class="nav-link">Admin Panel ▼</a>
                <div class="dropdown-menu">
                    <div class="dropdown-header">API Management</div>
                    <a href="{% url 'api_keys' %}" class="dropdown-item">🔐 API Keys</a>
                    <div class="dropdown-divider"></div>
                    <div class="dropdown-header">Analytics & Insights</div>
                    <a href="{% url 'enterprise_dashboard' %}" class="dropdown-item">📊 Enterprise Analytics <span class="badge">Premium</span></a>
                    <a href="{% url 'country_analytics_dashboard' %}" class="dropdown-item">🌍 Country Analytics <span class="badge">Premium</span></a>
                    <a href="{% url 'performance_dashboard' %}" class="dropdown-item">📈 Performance Dashboard <span class="badge admin">Admin</span></a>
                    <a href="{% url 'admin_approval_dashboard' %}" class="dropdown-item">✅ Approval Dashboard <span class="badge admin">Admin</span></a>
                </div>
            </li>

            <!-- Solutions with Dropdown -->
            <li class="nav-item">
                <a href="#" class="nav-link">Solutions ▼</a>
                <div class="dropdown-menu">
                    <div class="dropdown-header">Industry Solutions</div>
                    <a href="{% url 'corporate_solution' %}" class="dropdown-item">🏢 Corporate</a>
                    <a href="{% url 'retail_solution' %}" class="dropdown-item">🛒 Retail</a>
                    <a href="{% url 'hospitality_solution' %}" class="dropdown-item">🏨 Hospitality</a>
                    <a href="{% url 'education_solution' %}" class="dropdown-item">🎓 Education</a>
                    <a href="{% url 'events_solution' %}" class="dropdown-item">📅 Event Management</a>
                    <a href="{% url 'marketing_solution' %}" class="dropdown-item">📢 Marketing</a>
                </div>
            </li>

            <!-- QR Pro - Core Features -->
            <li class="nav-item">
                <a href="#" class="nav-link">QR Pro ▼</a>
                <div class="dropdown-menu">
                    <div class="dropdown-header">Core Pro Features</div>
                    <a href="{% url 'monetization_dashboard' %}" class="dropdown-item">📊 Pro Dashboard <span class="badge pro">Pro</span></a>
                    <a href="{% url 'pricing' %}" class="dropdown-item">👑 Upgrade Plan <span class="badge pro">Pro</span></a>
                    <a href="{% url 'generate_qr_code' %}" class="dropdown-item">🔲 Generate QR Code</a>
                    <div class="dropdown-divider"></div>
                    <div class="dropdown-header">AI & Dynamic Features</div>
                    <a href="#" class="dropdown-item" onclick="showAILandingInfo()">🤖 AI Landing Pages <span class="badge new">New</span></a>
                    <a href="{% url 'dynamic_redirect_dashboard' %}" class="dropdown-item">🔄 Dynamic Redirects <span class="badge pro">Pro</span></a>
                    <a href="{% url 'branding_management' %}" class="dropdown-item">🎨 Branded Pages <span class="badge pro">Pro</span></a>
                </div>
            </li>

            <!-- Analytics & Tools -->
            <li class="nav-item">
                <a href="#" class="nav-link">Analytics & Tools ▼</a>
                <div class="dropdown-menu">
                    <div class="dropdown-header">Analytics & Insights</div>
                    <a href="{% url 'advanced_analytics' %}" class="dropdown-item">📈 Advanced Analytics <span class="badge pro">Pro</span></a>
                    <a href="{% url 'qr_map' %}" class="dropdown-item">🗺️ Scan Map <span class="badge pro">Pro</span></a>
                    <div class="dropdown-divider"></div>
                    <div class="dropdown-header">Integrations & API</div>
                    <a href="{% url 'webhook_dashboard' %}" class="dropdown-item">🔗 Webhooks <span class="badge new">New</span></a>
                    <a href="{% url 'api_keys' %}" class="dropdown-item">🔑 API Keys</a>
                </div>
            </li>

            <!-- Premium Features -->
            <li class="nav-item">
                <a href="#" class="nav-link">Premium ▼</a>
                <div class="dropdown-menu">
                    <div class="dropdown-header">Advanced Features</div>
                    <a href="{% url 'bulk_generation' %}" class="dropdown-item">🚀 Bulk QR Generation</a>
                    <a href="{% url 'encrypted_qr' %}" class="dropdown-item">🔒 Encrypted QR Codes</a>
                    <div class="dropdown-divider"></div>
                    <div class="dropdown-header">Enterprise Solutions</div>
                    <a href="{% url 'api_keys' %}" class="dropdown-item">🔧 API Integration</a>
                    <a href="{% url 'dedicated_hosting' %}" class="dropdown-item">🖥️ Dedicated Hosting</a>
                    <a href="{% url 'priority_support' %}" class="dropdown-item">🎧 Priority Support</a>
                </div>
            </li>

            <!-- Advertise Platform -->
            <li class="nav-item">
                <a href="#" class="nav-link">Advertise ▼</a>
                <div class="dropdown-menu">
                    <div class="dropdown-header">Ad Management</div>
                    <a href="/ads/" class="dropdown-item">📊 Ad Dashboard</a>
                    <a href="{% url 'ads:ad_create_consolidated' %}" class="dropdown-item">➕ Create Ad</a>
                    <a href="/ads/list/" class="dropdown-item">📋 Ad Listings</a>
                    <div class="dropdown-divider"></div>
                    <div class="dropdown-header">Ad Products</div>
                    <a href="/ads/analytics/" class="dropdown-item">📈 Analytics</a>
                    <a href="/ads/transactions/" class="dropdown-item">💰 Transactions</a>
                    <a href="{% url 'ads:campaign_list' %}" class="dropdown-item">🎯 Campaigns</a>
                    {% if user.is_superuser %}
                    <a href="{% url 'ads:admin_all_ads' %}" class="dropdown-item">🛡️ Admin: All Ads</a>
                    {% endif %}
                </div>
            </li>
        </ul>

        <!-- User Section -->
        <ul class="user-menu">
            {% if user.is_authenticated %}
            <!-- Notification Icon -->
            <li class="nav-item">
                <a href="{% url 'notifications:notification_list' %}" class="nav-link">
                    Notifications
                    {% if unread_notification_count > 0 %}
                    <span class="notification-badge">{{ unread_notification_count }}</span>
                    {% endif %}
                </a>
            </li>

            <!-- User Profile Dropdown -->
            <li class="nav-item">
                <a href="#" class="nav-link">{{ user.username }} ▼</a>
                <div class="dropdown-menu">
                    <div class="dropdown-header">Account</div>
                    <a href="{% url 'settings' %}" class="dropdown-item">👤 Profile</a>
                    <a href="{% url 'qr_code_list' %}" class="dropdown-item">🔲 My QR Codes</a>
                    <a href="{% url 'api_keys' %}" class="dropdown-item">🔑 API Keys</a>
                    <a href="{% url 'notifications:notification_list' %}" class="dropdown-item">🔔 Notifications</a>
                    <div class="dropdown-divider"></div>
                    <a href="{% url 'account_logout' %}" class="dropdown-item logout">🚪 Logout</a>
                </div>
            </li>
            {% else %}
            <!-- Login/Signup for non-authenticated users -->
            <li class="nav-item">
                <a href="{% url 'account_login' %}" class="nav-link">Login</a>
            </li>
            {% endif %}
        </ul>
    </div>
</nav>

<script>
function showAILandingInfo() {
    alert('🚀 AI Landing Pages - NEW!\n\nCreate professional landing pages with AI instead of simple redirects.\n\n✨ Features:\n• AI-generated content\n• Professional designs\n• Mobile responsive\n• Custom branding\n• Analytics tracking\n\nUpgrade to QR Pro to access this feature!');
}

// Simplified Navigation - No complex submenus needed
console.log('🚀 SIMPLIFIED NAVBAR LOADING');
document.addEventListener('DOMContentLoaded', function() {
    console.log('✅ Simple dropdown navigation ready');

    // HAMBURGER MENU FUNCTIONALITY FOR MOBILE - SIMPLIFIED
    console.log('🍔 Setting up hamburger menu...');
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    const userMenu = document.querySelector('.user-menu');

    if (hamburger && navMenu) {
        console.log('✅ Hamburger and nav menu found');
        hamburger.addEventListener('click', function() {
            console.log('🍔 Hamburger clicked!');

            // Simple toggle using CSS classes
            navMenu.classList.toggle('mobile-open');
            if (userMenu) {
                userMenu.classList.toggle('mobile-open');
            }

            console.log('📱 Menu toggled - navMenu has mobile-open:', navMenu.classList.contains('mobile-open'));
        });
    } else {
        console.log('❌ Hamburger or nav menu not found');
        console.log('🔍 Hamburger element:', hamburger);
        console.log('🔍 Nav menu element:', navMenu);
    }

    // MOBILE DROPDOWN FUNCTIONALITY
    console.log('📱 Setting up mobile dropdown functionality...');

    // Handle main dropdown clicks (like QR Pro)
    const dropdownLinks = document.querySelectorAll('.nav-menu .nav-link');
    dropdownLinks.forEach((link, index) => {
        if (link.textContent.includes('▼')) {
            console.log(`📱 Setting up mobile dropdown for: ${link.textContent.trim()}`);
            link.addEventListener('click', function(e) {
                e.preventDefault();
                console.log(`📱 Mobile dropdown clicked: ${link.textContent.trim()}`);

                const navItem = link.closest('.nav-item');
                const dropdownMenu = navItem.querySelector('.dropdown-menu');

                console.log('📱 Nav item:', navItem);
                console.log('📱 Dropdown menu found:', dropdownMenu);

                navItem.classList.toggle('mobile-dropdown-open');

                console.log('📱 Dropdown toggled:', navItem.classList.contains('mobile-dropdown-open'));
                console.log('📱 Dropdown menu display:', dropdownMenu ? dropdownMenu.style.display : 'no dropdown menu');
            });
        }
    });

    // No complex submenu handling needed - using simple dropdowns now
    console.log('📱 Mobile navigation setup complete');
});
</script>
