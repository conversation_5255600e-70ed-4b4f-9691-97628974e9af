{% extends 'base.html' %}
{% load static %}

{% block title %}Quick Access - Testing{% endblock %}

{% block extra_css %}
<style>
.quick-access-container {
    padding: 3rem 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.access-card {
    background: white;
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    max-width: 800px;
    margin: 0 auto;
}

.access-header {
    text-align: center;
    margin-bottom: 3rem;
}

.access-header h1 {
    color: #333;
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
}

.access-header p {
    color: #6c757d;
    font-size: 1.1rem;
}

.access-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.access-option {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.access-option:hover {
    border-color: #667eea;
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.2);
}

.access-option h3 {
    color: #333;
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
}

.access-option p {
    color: #6c757d;
    margin-bottom: 1.5rem;
    line-height: 1.5;
}

.access-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    width: 100%;
}

.access-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
    color: white;
}

.access-btn.secondary {
    background: #6c757d;
}

.access-btn.secondary:hover {
    background: #5a6268;
}

.status-section {
    background: #e8f5e8;
    border-radius: 15px;
    padding: 2rem;
    margin-top: 2rem;
    text-align: center;
}

.status-section.logged-out {
    background: #fff3cd;
}

.quick-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
}

.quick-link {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 1rem;
    text-align: center;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
}

.quick-link:hover {
    border-color: #667eea;
    color: #667eea;
    transform: translateY(-2px);
}

.icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    display: block;
}
</style>
{% endblock %}

{% block content %}
<div class="quick-access-container">
    <div class="container">
        <div class="access-card">
            <div class="access-header">
                <h1>🚀 Quick Access</h1>
                <p>Fast access to all testing features and login methods</p>
            </div>

            {% if user.is_authenticated %}
            <div class="status-section">
                <h4>✅ Currently Logged In</h4>
                <p><strong>User:</strong> {{ user.username }} | <strong>Role:</strong> {% if user.is_superuser %}Admin{% else %}User{% endif %}</p>
                <a href="{% url 'account_logout' %}" class="access-btn secondary">Logout</a>
            </div>
            {% else %}
            <div class="status-section logged-out">
                <h4>🔓 Not Logged In</h4>
                <p>Choose a login method below to access the system</p>
            </div>
            {% endif %}

            <div class="access-grid">
                <!-- Simple Login -->
                <div class="access-option">
                    <div class="icon">🔐</div>
                    <h3>Simple Login</h3>
                    <p>Direct login with username/password. Bypasses AllAuth for reliable testing.</p>
                    <a href="{% url 'simple_login' %}" class="access-btn">Simple Login</a>
                </div>

                <!-- User Switcher -->
                <div class="access-option">
                    <div class="icon">🔄</div>
                    <h3>User Switcher</h3>
                    <p>Visual interface to switch between apollo (free) and peter (admin) accounts.</p>
                    <a href="{% url 'user_switcher' %}" class="access-btn">User Switcher</a>
                </div>

                <!-- Regular Login -->
                <div class="access-option">
                    <div class="icon">🏢</div>
                    <h3>Enterprise Login</h3>
                    <p>Full AllAuth login with enterprise styling. May have authentication issues.</p>
                    <a href="{% url 'custom_login' %}" class="access-btn secondary">Enterprise Login</a>
                </div>

                <!-- AllAuth Login -->
                <div class="access-option">
                    <div class="icon">⚙️</div>
                    <h3>AllAuth Login</h3>
                    <p>Django AllAuth default login. For testing AllAuth integration.</p>
                    <a href="{% url 'account_login' %}" class="access-btn secondary">AllAuth Login</a>
                </div>
            </div>

            <div class="quick-links">
                <a href="{% url 'performance_dashboard' %}" class="quick-link">
                    <span class="icon">📊</span>
                    Performance Dashboard
                </a>
                <a href="{% url 'qr_code_list' %}" class="quick-link">
                    <span class="icon">📱</span>
                    QR Codes
                </a>
                <a href="{% url 'generate_qr_code' %}" class="quick-link">
                    <span class="icon">➕</span>
                    Generate QR
                </a>
                <a href="/admin/" class="quick-link">
                    <span class="icon">⚡</span>
                    Admin Panel
                </a>
                <a href="{% url 'enterprise_dashboard' %}" class="quick-link">
                    <span class="icon">🏢</span>
                    Enterprise
                </a>
                <a href="{% url 'index' %}" class="quick-link">
                    <span class="icon">🏠</span>
                    Homepage
                </a>
            </div>

            <div class="mt-4 text-center">
                <h5>🧪 Test Accounts</h5>
                <div class="row">
                    <div class="col-md-6">
                        <strong>Apollo (Free User)</strong><br>
                        <small>Username: apollo | Password: 2587</small>
                    </div>
                    <div class="col-md-6">
                        <strong>Peter (Admin)</strong><br>
                        <small>Username: peter | Password: 2587</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
