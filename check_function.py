# Simple script to check if the dashboard_enterprise function exists in the views module
import os
import sys
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'qrcode_project.settings')
django.setup()

try:
    # Force reload the module
    if 'ads.views' in sys.modules:
        del sys.modules['ads.views']

    # Import the module
    from ads import views

    # Check if the function exists
    if hasattr(views, 'dashboard_enterprise'):
        print("SUCCESS: dashboard_enterprise function exists in views module")
    else:
        print("ERROR: dashboard_enterprise function does not exist in views module")
        print("Available functions in views module:")
        for name in dir(views):
            if callable(getattr(views, name)) and not name.startswith('__'):
                print(f"  - {name}")
except Exception as e:
    print(f"ERROR: {e}")
