/**
 * Ad Pricing Calculator JavaScript
 * Handles real-time pricing calculations for ad creation/edit forms
 * This is a standalone version for pages that don't use the consolidated script
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Ad Pricing Calculator: Initializing...');

    const adType = document.getElementById('adType');
    const adLocation = document.getElementById('ad_location');
    const requiresAi = document.getElementById('requiresAi');
    const useSmartEngine = document.getElementById('useSmartEngine');
    const wantsSocial = document.getElementById('wantsSocial');
    const durationOption = document.getElementById('durationOption');
    const startDate = document.getElementById('startDate');
    const endDate = document.getElementById('endDate');

    // Display elements
    const basePrice = document.getElementById('basePrice');
    const locationMultiplier = document.getElementById('locationMultiplier');
    const locationPrice = document.getElementById('locationPrice');
    const aiPrice = document.getElementById('aiPrice');
    const socialPrice = document.getElementById('socialPrice');
    const totalPrice = document.getElementById('totalPrice');
    const basePricingInput = document.getElementById('basePricingInput');
    const finalPricingInput = document.getElementById('finalPricingInput');
    const reviewPrice = document.getElementById('reviewPrice');

    // Update pricing calculation
    function updatePricing() {
        let base = 0;
        let multiplier = 1;
        let ai = 0;
        let social = 0;
        let duration = 7;

        // Get base price from ad type
        if (adType?.value) {
            const selectedOption = adType.options[adType.selectedIndex];
            base = parseFloat(selectedOption.dataset.price || 0);
        }

        // Get multiplier from location
        if (adLocation?.value) {
            const selectedOption = adLocation.options[adLocation.selectedIndex];
            multiplier = parseFloat(selectedOption.dataset.multiplier || 1);
        }

        // Get duration
        if (durationOption) {
            switch(durationOption.value) {
                case '7days': duration = 7; break;
                case '2weeks': duration = 14; break;
                case 'monthly': duration = 30; break;
                case 'yearly': duration = 365; break;
                case 'custom':
                    if (endDate?.value && startDate?.value) {
                        const start = new Date(startDate.value);
                        const end = new Date(endDate.value);
                        const diffTime = Math.abs(end - start);
                        duration = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                        if (duration < 1) duration = 1;
                    }
                    break;
            }
        }

        // Calculate additional services
        if (requiresAi?.checked || useSmartEngine?.checked) {
            ai = 20; // Updated price
        }

        if (wantsSocial?.checked) {
            social = 10; // Updated price
        }

        // Calculate total
        const locationCost = base * multiplier;
        const total = (base + locationCost) * (duration / 7) + ai + social;

        // Update display
        if (basePrice) basePrice.textContent = base.toFixed(2) + ' KSH';
        if (locationMultiplier) locationMultiplier.textContent = 'x' + multiplier.toFixed(1);
        if (locationPrice) locationPrice.textContent = locationCost.toFixed(2) + ' KSH';
        if (aiPrice) aiPrice.textContent = ai.toFixed(2) + ' KSH';
        if (socialPrice) socialPrice.textContent = social.toFixed(2) + ' KSH';
        if (totalPrice) totalPrice.textContent = total.toFixed(2) + ' KSH';

        // Update hidden inputs
        if (basePricingInput) basePricingInput.value = base.toFixed(2);
        if (finalPricingInput) finalPricingInput.value = total.toFixed(2);

        // Update review section
        if (reviewPrice) reviewPrice.textContent = total.toFixed(2) + ' KSH';

        console.log('Ad Pricing Calculator: Updated pricing', {
            base, multiplier, ai, social, duration, total
        });
    }

    // Add event listeners
    [adType, adLocation, durationOption, requiresAi, useSmartEngine, wantsSocial, startDate, endDate].forEach(el => {
        if (el) {
            el.addEventListener('change', updatePricing);
        }
    });

    // Initialize pricing
    updatePricing();

    // Export function for external use
    window.updatePricing = updatePricing;

    console.log('Ad Pricing Calculator: Initialized successfully');
});
