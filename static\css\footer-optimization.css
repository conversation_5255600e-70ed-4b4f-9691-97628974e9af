/* Footer Optimization CSS - Compact Enterprise Design */

/* Ultra-Premium Footer Optimization */
.ultra-premium-footer {
    background: linear-gradient(135deg, #0f1419 0%, #1a1f36 25%, #121628 50%, #0a0e1a 100%);
    position: relative;
    overflow: hidden;
    color: #e6e6e6;
    padding: 0;
    margin-top: 2rem; /* Reduced from 4rem */
    margin-bottom: 0;
    z-index: 10;
    box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.3);
    perspective: 1000px;
    border-bottom: 0;
}

/* Ultra-Compact Main Footer Content */
.footer-main-content {
    padding: 1.5rem 0 1rem; /* Drastically reduced padding */
    position: relative;
    z-index: 2;
}

/* Ultra-Compact Brand Showcase */
.footer-brand-showcase {
    margin-bottom: 1rem; /* Reduced from 2rem */
    padding: 1rem 0; /* Drastically reduced padding */
}

.footer-brand-content {
    padding-right: 1rem; /* Reduced padding */
}

.footer-premium-logo {
    display: flex;
    align-items: center;
    margin-bottom: 0.8rem; /* Drastically reduced from 1.5rem */
}

.logo-icon-container {
    position: relative;
    width: 40px; /* Further reduced from 50px */
    height: 40px;
    margin-right: 0.8rem; /* Reduced margin */
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-icon {
    font-size: 1.4rem; /* Further reduced from 1.8rem */
    color: #667eea;
    z-index: 2;
    position: relative;
}

.logo-main {
    font-size: 1.5rem; /* Further reduced from 1.8rem */
    font-weight: 800;
    color: white;
    display: block;
    line-height: 1.1;
}

.logo-tagline {
    font-size: 0.8rem; /* Further reduced from 0.9rem */
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
}

.footer-brand-description {
    font-size: 0.85rem; /* Further reduced from 0.95rem */
    line-height: 1.5;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0.8rem; /* Drastically reduced from 1.5rem */
}

/* Compact Stats */
.footer-stats {
    display: flex;
    gap: 1.5rem; /* Reduced from 2rem */
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 1.5rem; /* Reduced from 1.8rem */
    font-weight: 800;
    color: #667eea;
    display: block;
    margin-bottom: 0.3rem;
}

.stat-label {
    font-size: 0.8rem; /* Reduced from 0.9rem */
    color: rgba(255, 255, 255, 0.6);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Ultra-Compact CTA Section */
.footer-cta-section {
    padding-left: 1rem; /* Reduced padding */
}

.cta-card {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px; /* Reduced border radius */
    padding: 1.2rem; /* Drastically reduced from 2rem */
    text-align: center;
    backdrop-filter: blur(20px);
}

.cta-icon {
    width: 35px; /* Further reduced from 50px */
    height: 35px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.6rem; /* Further reduced margin */
    font-size: 1rem; /* Further reduced from 1.3rem */
    color: white;
}

.cta-title {
    font-size: 1.1rem; /* Further reduced from 1.3rem */
    font-weight: 700;
    color: white;
    margin-bottom: 0.5rem; /* Further reduced from 0.8rem */
}

.cta-description {
    font-size: 0.8rem; /* Further reduced from 0.9rem */
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 1rem; /* Further reduced from 1.5rem */
}

/* Ultra-Compact Navigation Grid */
.footer-navigation-grid {
    margin-bottom: 1rem; /* Further reduced from 2rem */
    padding: 1rem 0; /* Drastically reduced padding */
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-nav-section {
    margin-bottom: 1rem; /* Further reduced from 1.5rem */
}

.nav-section-title {
    font-size: 1rem; /* Further reduced from 1.1rem */
    font-weight: 700;
    color: white;
    margin-bottom: 0.6rem; /* Drastically reduced from 1rem */
    display: flex;
    align-items: center;
    gap: 0.4rem; /* Reduced gap */
}

.nav-section-title i {
    font-size: 1rem; /* Reduced from 1.1rem */
    color: #667eea;
    width: 20px;
    text-align: center;
}

.nav-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-links li {
    margin-bottom: 0.3rem; /* Further reduced from 0.5rem */
}

.nav-link {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 0.8rem; /* Further reduced from 0.85rem */
    font-weight: 500;
    transition: all 0.3s ease;
    display: block;
    padding: 0.2rem 0; /* Further reduced padding */
}

.nav-link:hover {
    color: #667eea;
    transform: translateX(5px);
}

/* Ultra-Compact Contact & Newsletter */
.footer-contact-newsletter {
    margin-bottom: 1rem; /* Further reduced from 2rem */
    padding: 1rem 0; /* Drastically reduced padding */
}

.contact-info-premium {
    margin-bottom: 1rem; /* Reduced from 1.5rem */
}

.contact-title {
    font-size: 1rem; /* Further reduced from 1.1rem */
    font-weight: 700;
    color: white;
    margin-bottom: 0.6rem; /* Drastically reduced from 1rem */
    display: flex;
    align-items: center;
    gap: 0.4rem; /* Reduced gap */
}

.contact-title i {
    font-size: 0.9rem; /* Further reduced from 1rem */
    color: #667eea;
}

.contact-item-premium {
    display: flex;
    align-items: center;
    margin-bottom: 0.6rem; /* Drastically reduced from 1rem */
}

.contact-icon-wrapper {
    width: 28px; /* Further reduced from 35px */
    height: 28px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.8rem; /* Reduced margin */
    flex-shrink: 0;
}

.contact-icon-wrapper i {
    font-size: 0.8rem; /* Further reduced from 0.9rem */
    color: #667eea;
}

.contact-text {
    font-size: 0.8rem; /* Further reduced from 0.85rem */
    line-height: 1.4; /* Reduced line height */
}

.contact-text strong {
    color: white;
    font-weight: 600;
}

.contact-text a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.contact-text a:hover {
    color: #667eea;
}

/* Ultra-Compact Newsletter */
.newsletter-premium {
    margin-bottom: 1rem; /* Reduced from 1.5rem */
}

.newsletter-card {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px; /* Reduced border radius */
    padding: 1rem; /* Further reduced from 1.5rem */
    backdrop-filter: blur(20px);
}

.newsletter-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.6rem; /* Further reduced from 1rem */
}

.newsletter-icon {
    width: 28px; /* Further reduced from 35px */
    height: 28px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.8rem; /* Reduced margin */
    flex-shrink: 0;
}

.newsletter-icon i {
    font-size: 0.8rem; /* Further reduced from 0.9rem */
    color: white;
}

.newsletter-title {
    font-size: 0.9rem; /* Further reduced from 1rem */
    font-weight: 700;
    color: white;
    margin-bottom: 0.2rem; /* Reduced margin */
}

.newsletter-subtitle {
    font-size: 0.75rem; /* Further reduced from 0.8rem */
    color: rgba(255, 255, 255, 0.7);
    margin: 0;
}

/* Ultra-Compact Social & Trust */
.footer-social-trust {
    padding: 1rem 0; /* Further reduced from 1.5rem */
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 0.5rem; /* Drastically reduced from 1rem */
}

.social-title, .trust-title {
    font-size: 0.9rem; /* Further reduced from 1rem */
    font-weight: 700;
    color: white;
    margin-bottom: 0.6rem; /* Drastically reduced from 1rem */
}

.social-icons-premium {
    display: flex;
    gap: 0.8rem; /* Further reduced from 1rem */
}

.social-icon-premium {
    width: 28px; /* Further reduced from 35px */
    height: 28px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.social-icon-premium i {
    font-size: 0.8rem; /* Further reduced from 0.9rem */
    color: #667eea;
    z-index: 2;
    position: relative;
}

.social-icon-premium:hover {
    transform: translateY(-3px) scale(1.1);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.social-icon-premium:hover i {
    color: white;
}

.trust-badges {
    display: flex;
    gap: 0.6rem; /* Further reduced from 1rem */
    flex-wrap: wrap;
}

.trust-badge {
    display: flex;
    align-items: center;
    gap: 0.4rem; /* Reduced gap */
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px; /* Reduced border radius */
    padding: 0.4rem 0.6rem; /* Further reduced padding */
    font-size: 0.75rem; /* Further reduced from 0.8rem */
    color: rgba(255, 255, 255, 0.8);
    font-weight: 600;
}

.trust-badge i {
    color: #667eea;
    font-size: 0.7rem; /* Further reduced from 0.8rem */
}

/* Ultra-Compact Footer Bottom */
.footer-bottom-premium {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.3) 0%, rgba(26, 31, 54, 0.5) 100%);
    padding: 1rem 0; /* Further reduced from 1.5rem */
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.copyright-text {
    font-size: 0.75rem; /* Further reduced from 0.8rem */
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 0.2rem; /* Reduced margin */
}

.company-info {
    font-size: 0.7rem; /* Further reduced from 0.75rem */
    color: rgba(255, 255, 255, 0.5);
    margin: 0;
}

/* Developer Credit Styling */
.developer-credit {
    margin: 0;
}

.developer-link {
    color: white;
    text-decoration: none;
    font-size: 0.8rem; /* Reduced from 0.85rem */
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.4rem; /* Reduced gap */
    padding: 0.4rem 0.8rem; /* Reduced padding */
    border-radius: 6px; /* Reduced border radius */
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.developer-link:hover {
    color: white;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
    border-color: rgba(102, 126, 234, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.developer-link i {
    color: #667eea;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.developer-link:hover i {
    color: #8fa4f3;
    text-shadow: 0 0 8px rgba(102, 126, 234, 0.6);
}

.legal-links {
    display: flex;
    gap: 0.8rem; /* Further reduced from 1rem */
    justify-content: flex-end;
    flex-wrap: wrap;
}

.legal-link {
    color: rgba(255, 255, 255, 0.6);
    text-decoration: none;
    font-size: 0.75rem; /* Further reduced from 0.8rem */
    font-weight: 500;
    transition: color 0.3s ease;
}

.legal-link:hover {
    color: #667eea;
}

/* Ultra-Compact Mobile Optimizations */
@media (max-width: 991.98px) {
    .footer-main-content {
        padding: 1rem 0 0.8rem; /* Further reduced */
    }

    .footer-brand-showcase {
        margin-bottom: 0.8rem; /* Further reduced */
        padding: 0.8rem 0;
    }

    .footer-navigation-grid {
        margin-bottom: 0.8rem; /* Further reduced */
        padding: 0.8rem 0;
    }

    .footer-contact-newsletter {
        margin-bottom: 0.8rem; /* Further reduced */
        padding: 0.8rem 0;
    }

    .footer-social-trust {
        padding: 0.8rem 0; /* Further reduced */
        margin-bottom: 0.3rem;
    }
}

@media (max-width: 767.98px) {
    .ultra-premium-footer {
        margin-top: 1rem; /* Further reduced */
    }

    .footer-main-content {
        padding: 0.8rem 0 0.6rem; /* Further reduced */
    }

    .footer-brand-showcase,
    .footer-navigation-grid,
    .footer-contact-newsletter {
        margin-bottom: 0.6rem; /* Further reduced */
        padding: 0.6rem 0;
    }

    .footer-stats {
        justify-content: center;
        gap: 0.8rem; /* Reduced gap */
    }

    .social-icons-premium {
        justify-content: center;
    }

    .trust-badges {
        justify-content: center;
    }

    .legal-links {
        justify-content: center;
        margin-top: 0.6rem; /* Reduced margin */
    }

    .footer-bottom-premium {
        padding: 0.8rem 0; /* Further reduced */
    }

    .developer-link {
        font-size: 0.75rem; /* Further reduced */
        padding: 0.3rem 0.6rem; /* Further reduced */
    }
}
