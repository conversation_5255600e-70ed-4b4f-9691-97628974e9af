/**
 * Ad Create Consolidated JavaScript
 * Consolidated script that handles all ad creation functionality
 * Replaces multiple separate scripts to avoid conflicts and improve performance
 *
 * Consolidates:
 * - ad-creation-tabs.js
 * - ad-preview.js
 * - ad-pricing-calculator.js
 * - ad-duration-calculator.js
 * - create_ad_button_fix.js
 * - ad-create-alignment-fix.js (partial)
 *
 * Version: 2.0.0
 */

(function() {
    'use strict';

    // Global state
    const AdCreateManager = {
        initialized: false,
        elements: {},
        state: {
            currentStep: 1,
            isValid: false,
            pricing: {
                base: 0,
                location: 0,
                ai: 0,
                social: 0,
                total: 0
            }
        }
    };

    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        if (AdCreateManager.initialized) {
            console.warn('Ad Create Manager already initialized');
            return;
        }

        console.log('Ad Create Manager: Initializing...');

        try {
            initializeElements();
            initializeTabNavigation();
            initializeDurationCalculator();
            initializePricingCalculator();
            initializePreviewUpdates();
            initializeFormValidation();
            initializeAlignmentFixes();
            initializeProTips();

            AdCreateManager.initialized = true;
            console.log('Ad Create Manager: Initialized successfully');
        } catch (error) {
            console.error('Ad Create Manager: Initialization failed', error);
        }
    });

    /**
     * Initialize DOM elements
     */
    function initializeElements() {
        AdCreateManager.elements = {
            // Form elements
            form: document.getElementById('adCreationForm'),
            submitButton: document.getElementById('submitAd'),

            // Tab elements
            tabContainer: document.getElementById('adCreationTabs'),
            progressBar: document.querySelector('.progress-bar'),
            tabButtons: document.querySelectorAll('[data-bs-toggle="tab"]'),
            nextButtons: document.querySelectorAll('.next-step'),
            prevButtons: document.querySelectorAll('.prev-step'),

            // Form inputs
            adTitle: document.getElementById('adTitle'),
            adContent: document.getElementById('adContent'),
            adType: document.getElementById('adType'),
            adLocation: document.getElementById('ad_location'),
            ctaLink: document.getElementById('ctaLink'),
            adMedia: document.getElementById('adMedia'),

            // Duration elements
            durationOption: document.getElementById('durationOption'),
            startDate: document.getElementById('startDate'),
            startTime: document.getElementById('startTime'),
            endDate: document.getElementById('endDate'),
            endTime: document.getElementById('endTime'),
            customDurationFields: document.getElementById('customDurationFields'),
            endDateTimeDisplay: document.getElementById('endDateTimeDisplay'),

            // Pricing elements
            basePrice: document.getElementById('basePrice'),
            locationMultiplier: document.getElementById('locationMultiplier'),
            locationPrice: document.getElementById('locationPrice'),
            aiPrice: document.getElementById('aiPrice'),
            socialPrice: document.getElementById('socialPrice'),
            totalPrice: document.getElementById('totalPrice'),
            basePricingInput: document.getElementById('basePricingInput'),
            finalPricingInput: document.getElementById('finalPricingInput'),

            // AI elements
            requiresAi: document.getElementById('requiresAi'),
            useSmartEngine: document.getElementById('useSmartEngine'),
            wantsSocial: document.getElementById('wantsSocial'),

            // Preview elements
            previewTitle: document.getElementById('previewTitle'),
            previewContent: document.getElementById('previewContent'),
            previewCta: document.getElementById('previewCta'),
            previewImage: document.querySelector('#previewImage img'),
            previewWrapper: document.getElementById('previewWrapper'),
            previewLocationType: document.getElementById('previewLocationType'),
            previewLocationLabel: document.getElementById('previewLocationLabel'),
            previewSizeInfo: document.getElementById('previewSizeInfo'),

            // Review elements
            reviewTitle: document.getElementById('reviewTitle'),
            reviewContent: document.getElementById('reviewContent'),
            reviewType: document.getElementById('reviewType'),
            reviewDuration: document.getElementById('reviewDuration'),
            reviewLocation: document.getElementById('reviewLocation'),
            reviewImpressions: document.getElementById('reviewImpressions'),
            reviewPrice: document.getElementById('reviewPrice'),

            // Error container
            errorContainer: document.getElementById('formErrorContainer'),

            // Pro tips elements
            proTipsContent: document.getElementById('proTipsContent'),
            proTipsHeader: document.getElementById('proTipsHeader')
        };
    }

    /**
     * Initialize tab navigation
     */
    function initializeTabNavigation() {
        const { progressBar, tabButtons, nextButtons, prevButtons } = AdCreateManager.elements;

        // Progress bar update
        if (progressBar && tabButtons.length > 0) {
            tabButtons.forEach(button => {
                button.addEventListener('shown.bs.tab', function(e) {
                    updateProgressBar(e.target.getAttribute('id'));
                    scrollToFormTop();
                });
            });
        }

        // Next button handlers
        nextButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const nextTabId = this.getAttribute('data-next');
                if (nextTabId && validateCurrentStep()) {
                    activateTab(nextTabId);
                }
            });
        });

        // Previous button handlers
        prevButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const prevTabId = this.getAttribute('data-prev');
                if (prevTabId) {
                    activateTab(prevTabId);
                }
            });
        });
    }

    /**
     * Update progress bar
     */
    function updateProgressBar(tabId) {
        const { progressBar } = AdCreateManager.elements;
        if (!progressBar) return;

        let progress = 25;
        switch(tabId) {
            case 'step1-tab': progress = 25; AdCreateManager.state.currentStep = 1; break;
            case 'step2-tab': progress = 50; AdCreateManager.state.currentStep = 2; break;
            case 'step3-tab': progress = 75; AdCreateManager.state.currentStep = 3; break;
            case 'step4-tab': progress = 100; AdCreateManager.state.currentStep = 4; break;
        }

        progressBar.style.width = progress + '%';
        progressBar.setAttribute('aria-valuenow', progress);
    }

    /**
     * Activate a tab
     */
    function activateTab(tabId) {
        const tab = document.getElementById(tabId);
        if (tab && typeof bootstrap !== 'undefined') {
            const bsTab = new bootstrap.Tab(tab);
            bsTab.show();
        }
    }

    /**
     * Scroll to form top
     */
    function scrollToFormTop() {
        const { form } = AdCreateManager.elements;
        if (form) {
            const rect = form.getBoundingClientRect();
            const offset = window.pageYOffset + rect.top - 20;
            window.scrollTo({ top: offset, behavior: 'smooth' });
        }
    }

    /**
     * Initialize duration calculator
     */
    function initializeDurationCalculator() {
        const { durationOption, customDurationFields, startDate, startTime, endDate, endTime } = AdCreateManager.elements;

        // Set default start date
        if (startDate && !startDate.value) {
            const today = new Date();
            startDate.value = today.toISOString().split('T')[0];
        }

        // Set default start time
        if (startTime && !startTime.value) {
            const now = new Date();
            startTime.value = now.toTimeString().slice(0, 5);
        }

        // Duration option change handler
        if (durationOption) {
            durationOption.addEventListener('change', function() {
                if (customDurationFields) {
                    customDurationFields.style.display = this.value === 'custom' ? 'block' : 'none';
                }
                updateCalculatedEndTime();
                updatePricing();
            });
        }

        // Date/time change handlers
        [startDate, startTime, endDate, endTime].forEach(element => {
            if (element) {
                element.addEventListener('change', function() {
                    updateCalculatedEndTime();
                    updatePricing();
                });
            }
        });

        // Initialize
        updateCalculatedEndTime();
    }

    /**
     * Update calculated end time
     */
    function updateCalculatedEndTime() {
        const { startDate, startTime, endDate, endTime, durationOption, endDateTimeDisplay } = AdCreateManager.elements;

        if (!startDate?.value || !startTime?.value) return;

        const start = new Date(`${startDate.value}T${startTime.value}`);
        let durationDays = 7;
        let end;

        if (durationOption?.value === 'custom' && endDate?.value && endTime?.value) {
            end = new Date(`${endDate.value}T${endTime.value}`);
            const diffTime = Math.abs(end - start);
            durationDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            if (durationDays < 1) durationDays = 1;
        } else {
            switch(durationOption?.value) {
                case '7days': durationDays = 7; break;
                case '2weeks': durationDays = 14; break;
                case 'monthly': durationDays = 30; break;
                case 'yearly': durationDays = 365; break;
            }
            end = new Date(start);
            end.setDate(end.getDate() + durationDays);
        }

        // Add 2 hours bonus
        end.setHours(end.getHours() + 2);

        if (endDateTimeDisplay) {
            const options = {
                weekday: 'long', year: 'numeric', month: 'long', day: 'numeric',
                hour: '2-digit', minute: '2-digit'
            };
            endDateTimeDisplay.textContent = end.toLocaleDateString('en-US', options);
        }

        // Update duration in review
        updateDurationInReview(durationDays);
    }

    /**
     * Update duration in review
     */
    function updateDurationInReview(days) {
        const { reviewDuration, durationOption } = AdCreateManager.elements;
        if (!reviewDuration) return;

        let durationText = '';
        switch(durationOption?.value) {
            case '7days': durationText = '7 days (default)'; break;
            case '2weeks': durationText = '14 days (2 weeks)'; break;
            case 'monthly': durationText = '30 days (monthly)'; break;
            case 'yearly': durationText = '365 days (yearly)'; break;
            case 'custom': durationText = `${days} days (custom)`; break;
            default: durationText = `${days} days`;
        }
        reviewDuration.textContent = durationText;
    }

    /**
     * Initialize pricing calculator
     */
    function initializePricingCalculator() {
        const { adType, adLocation, requiresAi, useSmartEngine, wantsSocial, durationOption } = AdCreateManager.elements;

        // Add event listeners
        [adType, adLocation, durationOption, requiresAi, useSmartEngine, wantsSocial].forEach(el => {
            if (el) {
                el.addEventListener('change', updatePricing);
            }
        });

        // Initialize pricing
        updatePricing();
    }

    /**
     * Update pricing calculation
     */
    function updatePricing() {
        const { adType, adLocation, requiresAi, useSmartEngine, wantsSocial, durationOption,
                startDate, endDate, basePrice, locationMultiplier, locationPrice,
                aiPrice, socialPrice, totalPrice, basePricingInput, finalPricingInput, reviewPrice } = AdCreateManager.elements;

        let base = 0;
        let multiplier = 1;
        let ai = 0;
        let social = 0;
        let duration = 7;

        // Get base price from ad type
        if (adType?.value) {
            const selectedOption = adType.options[adType.selectedIndex];
            base = parseFloat(selectedOption.dataset.price || 0);
        }

        // Get multiplier from location
        if (adLocation?.value) {
            const selectedOption = adLocation.options[adLocation.selectedIndex];
            multiplier = parseFloat(selectedOption.dataset.multiplier || 1);
        }

        // Get duration
        if (durationOption) {
            switch(durationOption.value) {
                case '7days': duration = 7; break;
                case '2weeks': duration = 14; break;
                case 'monthly': duration = 30; break;
                case 'yearly': duration = 365; break;
                case 'custom':
                    if (endDate?.value && startDate?.value) {
                        const start = new Date(startDate.value);
                        const end = new Date(endDate.value);
                        const diffTime = Math.abs(end - start);
                        duration = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                        if (duration < 1) duration = 1;
                    }
                    break;
            }
        }

        // Calculate additional services
        if (requiresAi?.checked || useSmartEngine?.checked) {
            ai = 20; // Updated price
        }

        if (wantsSocial?.checked) {
            social = 10; // Updated price
        }

        // Calculate total
        const locationCost = base * multiplier;
        const total = (base + locationCost) * (duration / 7) + ai + social;

        // Update state
        AdCreateManager.state.pricing = { base, location: locationCost, ai, social, total };

        // Update display
        if (basePrice) basePrice.textContent = base.toFixed(2) + ' KSH';
        if (locationMultiplier) locationMultiplier.textContent = 'x' + multiplier.toFixed(1);
        if (locationPrice) locationPrice.textContent = locationCost.toFixed(2) + ' KSH';
        if (aiPrice) aiPrice.textContent = ai.toFixed(2) + ' KSH';
        if (socialPrice) socialPrice.textContent = social.toFixed(2) + ' KSH';
        if (totalPrice) totalPrice.textContent = total.toFixed(2) + ' KSH';

        // Update hidden inputs
        if (basePricingInput) basePricingInput.value = base.toFixed(2);
        if (finalPricingInput) finalPricingInput.value = total.toFixed(2);

        // Update review section
        if (reviewPrice) reviewPrice.textContent = total.toFixed(2) + ' KSH';
    }

    /**
     * Initialize preview updates
     */
    function initializePreviewUpdates() {
        const { adTitle, adContent, ctaLink, adMedia, adType, adLocation, previewLocationType } = AdCreateManager.elements;

        // Title preview
        if (adTitle) {
            adTitle.addEventListener('input', function() {
                updatePreviewTitle(this.value);
                updateReviewTitle(this.value);
            });
        }

        // Content preview
        if (adContent) {
            adContent.addEventListener('input', function() {
                updatePreviewContent(this.value);
                updateReviewContent(this.value);
            });
        }

        // CTA link preview
        if (ctaLink) {
            ctaLink.addEventListener('input', function() {
                updatePreviewCta(this.value);
            });
        }

        // Media preview
        if (adMedia) {
            adMedia.addEventListener('change', function() {
                updatePreviewImage(this.files[0]);
            });
        }

        // Ad type change
        if (adType) {
            adType.addEventListener('change', function() {
                updateReviewType(this.options[this.selectedIndex].text);
            });
        }

        // Location change
        if (adLocation) {
            adLocation.addEventListener('change', function() {
                const selectedOption = this.options[this.selectedIndex];
                updateReviewLocation(selectedOption.text);
                updateReviewImpressions(selectedOption.dataset.impressions);
                updatePreviewLocationType(selectedOption.text);
            });
        }

        // Preview location type change
        if (previewLocationType) {
            previewLocationType.addEventListener('change', updateLocationPreview);
        }

        // Initialize preview
        initializePreviewContent();
    }

    /**
     * Preview update helper functions
     */
    function updatePreviewTitle(value) {
        const { previewTitle } = AdCreateManager.elements;
        if (previewTitle) {
            previewTitle.textContent = value || 'Your Ad Title';
        }
    }

    function updatePreviewContent(value) {
        const { previewContent } = AdCreateManager.elements;
        if (previewContent) {
            previewContent.textContent = value || 'Your ad content will appear here as you type.';
        }
    }

    function updatePreviewCta(value) {
        const { previewCta } = AdCreateManager.elements;
        if (previewCta) {
            previewCta.href = value || '#';
        }
    }

    function updatePreviewImage(file) {
        const { previewImage } = AdCreateManager.elements;
        if (previewImage && file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImage.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    }

    function updatePreviewLocationType(locationText) {
        const { previewLocationType } = AdCreateManager.elements;
        if (previewLocationType) {
            const text = locationText.toLowerCase();
            if (text.includes('header')) previewLocationType.value = 'header';
            else if (text.includes('sidebar')) previewLocationType.value = 'sidebar';
            else if (text.includes('content')) previewLocationType.value = 'content';
            else if (text.includes('footer')) previewLocationType.value = 'footer';
            updateLocationPreview();
        }
    }

    function updateLocationPreview() {
        const { previewLocationType, previewWrapper, previewLocationLabel, previewSizeInfo } = AdCreateManager.elements;
        if (!previewLocationType || !previewWrapper) return;

        const locationType = previewLocationType.value;
        previewWrapper.className = 'ad-preview-wrapper';

        switch(locationType) {
            case 'header':
                previewWrapper.classList.add('ad-preview-header');
                if (previewLocationLabel) previewLocationLabel.textContent = 'Header Ad Preview';
                if (previewSizeInfo) previewSizeInfo.textContent = 'Header size: 728x90px';
                break;
            case 'sidebar':
                previewWrapper.classList.add('ad-preview-sidebar');
                if (previewLocationLabel) previewLocationLabel.textContent = 'Sidebar Ad Preview';
                if (previewSizeInfo) previewSizeInfo.textContent = 'Sidebar size: 300x250px';
                break;
            case 'content':
                previewWrapper.classList.add('ad-preview-content');
                if (previewLocationLabel) previewLocationLabel.textContent = 'In-Content Ad Preview';
                if (previewSizeInfo) previewSizeInfo.textContent = 'Content size: 468x60px';
                break;
            case 'footer':
                previewWrapper.classList.add('ad-preview-footer');
                if (previewLocationLabel) previewLocationLabel.textContent = 'Footer Ad Preview';
                if (previewSizeInfo) previewSizeInfo.textContent = 'Footer size: 970x90px';
                break;
            default:
                if (previewLocationLabel) previewLocationLabel.textContent = 'Default Ad Preview';
                if (previewSizeInfo) previewSizeInfo.textContent = 'Standard size: 300x250px';
        }
    }

    /**
     * Review update helper functions
     */
    function updateReviewTitle(value) {
        const { reviewTitle } = AdCreateManager.elements;
        if (reviewTitle) {
            reviewTitle.textContent = value || 'Not specified';
        }
    }

    function updateReviewContent(value) {
        const { reviewContent } = AdCreateManager.elements;
        if (reviewContent) {
            reviewContent.textContent = value || 'Not specified';
        }
    }

    function updateReviewType(value) {
        const { reviewType } = AdCreateManager.elements;
        if (reviewType) {
            reviewType.textContent = value || 'Not specified';
        }
    }

    function updateReviewLocation(value) {
        const { reviewLocation } = AdCreateManager.elements;
        if (reviewLocation) {
            reviewLocation.textContent = value || 'Not specified';
        }
    }

    function updateReviewImpressions(value) {
        const { reviewImpressions } = AdCreateManager.elements;
        if (reviewImpressions) {
            reviewImpressions.textContent = (value || 0) + ' views per day';
        }
    }

    function initializePreviewContent() {
        const { adTitle, adContent, previewLocationType } = AdCreateManager.elements;

        if (adTitle) updatePreviewTitle(adTitle.value);
        if (adContent) updatePreviewContent(adContent.value);
        if (previewLocationType) updateLocationPreview();
    }

    /**
     * Initialize form validation
     */
    function initializeFormValidation() {
        const { form, submitButton } = AdCreateManager.elements;

        if (submitButton) {
            // Remove existing listeners to prevent conflicts
            const newButton = submitButton.cloneNode(true);
            submitButton.parentNode.replaceChild(newButton, submitButton);
            AdCreateManager.elements.submitButton = newButton;

            newButton.addEventListener('click', function(e) {
                if (!validateAllSteps()) {
                    e.preventDefault();
                    showValidationErrors();
                    return false;
                }

                // Clear any error messages
                clearValidationErrors();
                console.log('Ad Create Manager: Form is valid, allowing submission');
            });
        }

        if (form) {
            form.addEventListener('submit', function(e) {
                console.log('Ad Create Manager: Form submission attempted');

                const isValid = validateAllSteps();
                console.log('Ad Create Manager: Validation result:', isValid);

                if (!isValid) {
                    console.log('Ad Create Manager: Validation failed, preventing submission');
                    e.preventDefault();
                    showValidationErrors();
                    return false;
                } else {
                    console.log('Ad Create Manager: Validation passed, allowing form submission');
                    clearValidationErrors();

                    // Add a small delay to ensure form data is processed
                    console.log('Ad Create Manager: Form submitting to success page');

                    // Allow the form to submit normally
                    return true;
                }
            });
        }
    }

    /**
     * Validate current step
     */
    function validateCurrentStep() {
        const { currentStep } = AdCreateManager.state;

        switch(currentStep) {
            case 1: return validateStep1();
            case 2: return validateStep2();
            case 3: return validateStep3();
            case 4: return validateStep4();
            default: return true;
        }
    }

    /**
     * Validate all steps
     */
    function validateAllSteps() {
        return validateStep1() && validateStep2() && validateStep3() && validateStep4();
    }

    /**
     * Validate step 1 (Basic Info)
     */
    function validateStep1() {
        const { adTitle, adType, adLocation } = AdCreateManager.elements;
        let isValid = true;

        // Clear previous validation
        [adTitle, adType, adLocation].forEach(el => {
            if (el) el.classList.remove('is-invalid');
        });

        // Only validate required fields that actually exist
        if (adTitle && !adTitle.value?.trim()) {
            console.log('Ad Create Manager: Ad title is missing');
            adTitle.classList.add('is-invalid');
            isValid = false;
        }

        if (adType && !adType.value) {
            console.log('Ad Create Manager: Ad type is missing');
            adType.classList.add('is-invalid');
            isValid = false;
        }

        if (adLocation && !adLocation.value) {
            console.log('Ad Create Manager: Ad location is missing');
            adLocation.classList.add('is-invalid');
            isValid = false;
        }

        console.log('Ad Create Manager: Step 1 validation result:', isValid);
        return isValid;
    }

    /**
     * Validate step 2 (Content)
     */
    function validateStep2() {
        const { adContent } = AdCreateManager.elements;
        let isValid = true;

        if (adContent) adContent.classList.remove('is-invalid');

        // Only validate if the field exists and is required
        if (adContent && !adContent.value?.trim()) {
            console.log('Ad Create Manager: Ad content is missing');
            adContent.classList.add('is-invalid');
            isValid = false;
        }

        console.log('Ad Create Manager: Step 2 validation result:', isValid);
        return isValid;
    }

    /**
     * Validate step 3 (Options)
     */
    function validateStep3() {
        // Step 3 is optional, always valid
        return true;
    }

    /**
     * Validate step 4 (Review)
     */
    function validateStep4() {
        // Step 4 is review only, always valid
        return true;
    }

    /**
     * Show validation errors
     */
    function showValidationErrors() {
        const { errorContainer } = AdCreateManager.elements;
        const errors = [];

        if (!validateStep1()) {
            errors.push('Please complete all required fields in Basic Info');
        }
        if (!validateStep2()) {
            errors.push('Please add ad content');
        }

        if (errors.length > 0 && errorContainer) {
            errorContainer.innerHTML = '<strong>Please fix the following errors:</strong><ul><li>' +
                                     errors.join('</li><li>') + '</li></ul>';
            errorContainer.style.display = 'block';
            errorContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });

            // Focus on first invalid field
            const firstInvalid = document.querySelector('.is-invalid');
            if (firstInvalid) {
                firstInvalid.focus();
                // Navigate to the tab containing the invalid field
                const tabPane = firstInvalid.closest('.tab-pane');
                if (tabPane) {
                    const tabId = tabPane.id;
                    const tabButton = document.querySelector(`[data-bs-target="#${tabId}"]`);
                    if (tabButton) {
                        tabButton.click();
                    }
                }
            }
        }
    }

    /**
     * Clear validation errors
     */
    function clearValidationErrors() {
        const { errorContainer } = AdCreateManager.elements;

        if (errorContainer) {
            errorContainer.style.display = 'none';
        }

        document.querySelectorAll('.is-invalid').forEach(el => {
            el.classList.remove('is-invalid');
        });
    }

    /**
     * Initialize alignment fixes
     */
    function initializeAlignmentFixes() {
        // Fix layout shifts
        fixLayoutShifts();

        // Fix on resize
        window.addEventListener('resize', debounce(fixLayoutShifts, 250));

        // Fix when tabs are shown
        document.addEventListener('shown.bs.tab', function() {
            setTimeout(fixLayoutShifts, 100);
        });
    }

    /**
     * Fix layout shifts
     */
    function fixLayoutShifts() {
        const { progressBar } = AdCreateManager.elements;

        // Fix progress bar
        if (progressBar && !progressBar.style.width) {
            progressBar.style.width = '25%';
        }

        // Fix form controls
        document.querySelectorAll('.form-control, .form-select').forEach(control => {
            if (!control.style.width) {
                control.style.width = '100%';
                control.style.boxSizing = 'border-box';
            }
        });

        // Fix tab alignment
        const navTabs = document.querySelector('.nav-tabs');
        if (navTabs) {
            navTabs.style.display = 'flex';
            navTabs.style.width = '100%';
        }
    }

    /**
     * Initialize pro tips functionality
     */
    function initializeProTips() {
        const { proTipsContent, proTipsHeader } = AdCreateManager.elements;

        if (!proTipsContent || !proTipsHeader) {
            console.warn('Ad Create Manager: Pro Tips elements not found');
            return;
        }

        console.log('Ad Create Manager: Initializing Pro Tips');

        // Check if Bootstrap is available
        if (typeof bootstrap === 'undefined' || typeof bootstrap.Collapse === 'undefined') {
            console.warn('Ad Create Manager: Bootstrap Collapse not available, using fallback');
            initializeProTipsFallback();
            return;
        }

        try {
            // Initialize Bootstrap collapse
            const bsCollapse = new bootstrap.Collapse(proTipsContent, {
                toggle: false
            });

            // Set initial state based on screen size
            if (window.innerWidth >= 768) {
                // Desktop: show by default
                proTipsContent.classList.add('show');
                updateChevronIcon('up');
                console.log('Ad Create Manager: Pro Tips shown on desktop');
            } else {
                // Mobile: hide by default
                proTipsContent.classList.remove('show');
                updateChevronIcon('down');
                console.log('Ad Create Manager: Pro Tips collapsed on mobile');
            }

            // Add click event to header (not the button)
            proTipsHeader.addEventListener('click', function(e) {
                // Only handle clicks on the header itself, not on the toggle button
                if (!e.target.closest('.btn-link')) {
                    console.log('Ad Create Manager: Pro Tips header clicked');

                    // Toggle the collapse
                    if (proTipsContent.classList.contains('show')) {
                        bsCollapse.hide();
                    } else {
                        bsCollapse.show();
                    }

                    // Toggle chevron icon
                    toggleChevronIcon();
                }
            });

            // Listen for collapse events
            proTipsContent.addEventListener('shown.bs.collapse', function() {
                console.log('Ad Create Manager: Pro Tips shown');
                updateChevronIcon('up');
            });

            proTipsContent.addEventListener('hidden.bs.collapse', function() {
                console.log('Ad Create Manager: Pro Tips hidden');
                updateChevronIcon('down');
            });

            // Handle window resize to adjust behavior
            window.addEventListener('resize', function() {
                if (window.innerWidth >= 768) {
                    // Desktop: show by default if not manually collapsed
                    if (!proTipsContent.classList.contains('show') && !proTipsContent.hasAttribute('data-manually-collapsed')) {
                        bsCollapse.show();
                    }
                } else {
                    // Mobile: hide by default
                    if (proTipsContent.classList.contains('show')) {
                        bsCollapse.hide();
                    }
                }
            });

        } catch (error) {
            console.error('Ad Create Manager: Error initializing Pro Tips:', error);
            initializeProTipsFallback();
        }
    }

    /**
     * Fallback pro tips functionality without Bootstrap
     */
    function initializeProTipsFallback() {
        const { proTipsContent, proTipsHeader } = AdCreateManager.elements;

        proTipsHeader.addEventListener('click', function(e) {
            if (!e.target.closest('.btn-link')) {
                proTipsContent.classList.toggle('show');
                toggleChevronIcon();
            }
        });
    }

    /**
     * Toggle chevron icon
     */
    function toggleChevronIcon() {
        const { proTipsHeader } = AdCreateManager.elements;
        const chevron = proTipsHeader.querySelector('.fa-chevron-down, .fa-chevron-up');

        if (chevron) {
            chevron.classList.toggle('fa-chevron-down');
            chevron.classList.toggle('fa-chevron-up');
        }
    }

    /**
     * Update chevron icon to specific direction
     */
    function updateChevronIcon(direction) {
        const { proTipsHeader } = AdCreateManager.elements;
        const chevron = proTipsHeader.querySelector('.fa-chevron-down, .fa-chevron-up');

        if (chevron) {
            chevron.classList.remove('fa-chevron-down', 'fa-chevron-up');
            chevron.classList.add(`fa-chevron-${direction}`);
        }
    }

    /**
     * Debounce utility
     */
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Export for external use
    window.AdCreateManager = AdCreateManager;
    window.updatePricing = function() { updatePricing(); };
    window.updateCalculatedEndTime = function() { updateCalculatedEndTime(); };

})();
