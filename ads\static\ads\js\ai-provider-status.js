/**
 * AI Provider Status
 * This script handles the AI provider status on the frontend
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize AI provider status
    initAIProviderStatus();

    // Update status every 5 minutes
    setInterval(updateAIProviderStatus, 5 * 60 * 1000);
});

/**
 * Initialize AI provider status
 */
function initAIProviderStatus() {
    // Get AI provider status from the page
    const statusElement = document.getElementById('ai-provider-status-data');
    if (!statusElement) {
        console.warn('AI provider status element not found');
        return;
    }

    try {
        // Parse the status JSON
        const status = JSON.parse(statusElement.textContent);

        // Update UI based on status
        updateUIBasedOnStatus(status);
    } catch (error) {
        console.error('Error parsing AI provider status:', error);
    }
}

/**
 * Update AI provider status
 */
function updateAIProviderStatus() {
    // Make an AJAX request to get the latest status
    fetch('/api/ai-provider-status/')
        .then(response => response.json())
        .then(status => {
            // Update UI based on status
            updateUIBasedOnStatus(status);
        })
        .catch(error => {
            console.error('Error fetching AI provider status:', error);
        });
}

/**
 * Update UI based on AI provider status
 *
 * @param {Object} status - The AI provider status
 */
function updateUIBasedOnStatus(status) {
    // Check if Smart Engine is available
    const smartEngineAvailable = status['Smart Engine'] && status['Smart Engine'].available;

    // Update Smart Engine toggle
    updateSmartEngineToggle(smartEngineAvailable);

    // Update Generate Suggestions button
    updateGenerateSuggestionsButton(smartEngineAvailable);

    // Update AI status indicators
    updateStatusIndicators(status);
}

/**
 * Update Smart Engine toggle based on Smart Engine availability
 *
 * @param {boolean} smartEngineAvailable - Whether Smart Engine is available
 */
function updateSmartEngineToggle(smartEngineAvailable) {
    const smartEngineToggle = document.getElementById('useSmartEngine');
    if (!smartEngineToggle) return;

    // Check if we should show AI status (admin only)
    const showAiStatus = document.body.hasAttribute('data-show-ai-status') &&
                         document.body.getAttribute('data-show-ai-status') === 'true';

    // For regular users, always show Smart Engine as available
    // This abstracts the complexity of fallbacks from users
    if (!showAiStatus) {
        // Don't force enable the toggle - let title validation control it
        smartEngineToggle.title = '';

        // Remove any warning message
        const warningElement = document.getElementById('smart-engine-warning');
        if (warningElement) {
            warningElement.remove();
        }

        // Check title validation to set proper state
        if (typeof checkAndDisableSmartEngine === 'function') {
            checkAndDisableSmartEngine();
        }
        return;
    }

    // Admin-only code below - only admins will see the actual status

    // If Smart Engine is not available, disable the toggle
    if (!smartEngineAvailable) {
        smartEngineToggle.disabled = true;

        // Add a tooltip explaining why it's disabled
        smartEngineToggle.title = 'Smart Engine is currently unavailable';

        // Add a warning message
        const warningElement = document.createElement('div');
        warningElement.className = 'alert alert-warning mt-2';
        warningElement.id = 'smart-engine-warning';
        warningElement.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i> Smart Engine is currently unavailable. Please try again later.';

        // Add the warning if it doesn't already exist
        if (!document.getElementById('smart-engine-warning')) {
            smartEngineToggle.parentNode.appendChild(warningElement);
        }
    } else {
        // If Smart Engine is available, check title validation before enabling
        smartEngineToggle.title = '';

        // Remove any warning message
        const warningElement = document.getElementById('smart-engine-warning');
        if (warningElement) {
            warningElement.remove();
        }

        // Check title validation to set proper state
        if (typeof checkAndDisableSmartEngine === 'function') {
            checkAndDisableSmartEngine();
        }
    }
}

/**
 * Update Generate Suggestions button based on Smart Engine availability
 *
 * @param {boolean} smartEngineAvailable - Whether Smart Engine is available
 */
function updateGenerateSuggestionsButton(smartEngineAvailable) {
    const generateSuggestionsBtn = document.getElementById('generateSuggestions');
    if (!generateSuggestionsBtn) return;

    // Check if we should show AI status (admin only)
    const showAiStatus = document.body.hasAttribute('data-show-ai-status') &&
                         document.body.getAttribute('data-show-ai-status') === 'true';

    // For regular users, always show normal button
    // This abstracts the complexity of fallbacks from users
    if (!showAiStatus) {
        // Always show normal button for regular users
        generateSuggestionsBtn.classList.add('btn-primary');
        generateSuggestionsBtn.classList.remove('btn-warning');
        generateSuggestionsBtn.innerHTML = '<i class="fas fa-magic me-2"></i> Generate Suggestions';
        generateSuggestionsBtn.title = '';
        return;
    }

    // Admin-only code below - only admins will see the actual status

    // If Smart Engine is not available, update the button
    if (!smartEngineAvailable) {
        // Add a class to indicate it's using fallback
        generateSuggestionsBtn.classList.add('btn-warning');
        generateSuggestionsBtn.classList.remove('btn-primary');

        // Update the text to indicate it's using standard mode
        generateSuggestionsBtn.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i> Generate Suggestions (Standard Mode)';

        // Add a tooltip explaining it's using standard mode
        generateSuggestionsBtn.title = 'Using standard mode for suggestions';
    } else {
        // If Smart Engine is available, restore the button
        generateSuggestionsBtn.classList.add('btn-primary');
        generateSuggestionsBtn.classList.remove('btn-warning');

        // Restore the original text
        generateSuggestionsBtn.innerHTML = '<i class="fas fa-magic me-2"></i> Generate Suggestions';

        // Remove the tooltip
        generateSuggestionsBtn.title = '';
    }
}

/**
 * Update AI status indicators
 *
 * @param {Object} status - The AI provider status
 */
function updateStatusIndicators(status) {
    // Check if we should show AI status (admin only)
    const showAiStatus = document.body.hasAttribute('data-show-ai-status') &&
                         document.body.getAttribute('data-show-ai-status') === 'true';

    // Find all AI status indicators
    const indicators = document.querySelectorAll('.ai-status-indicator');

    indicators.forEach(indicator => {
        // Get the engine from the data attribute
        const engine = indicator.dataset.engine || indicator.dataset.provider;

        // For regular users, always show as online
        if (!showAiStatus) {
            indicator.classList.add('text-success');
            indicator.classList.remove('text-danger', 'text-warning');
            indicator.innerHTML = '<i class="fas fa-check-circle me-1"></i> Online';
            return;
        }

        // Admin-only code below - only admins will see the actual status

        // Check if the engine exists in the status
        if (status[engine]) {
            // Update the indicator based on availability
            if (status[engine].available) {
                indicator.classList.add('text-success');
                indicator.classList.remove('text-danger', 'text-warning');
                indicator.innerHTML = '<i class="fas fa-check-circle me-1"></i> Online';
            } else {
                indicator.classList.add('text-danger');
                indicator.classList.remove('text-success', 'text-warning');
                indicator.innerHTML = '<i class="fas fa-times-circle me-1"></i> Offline';
            }
        }
    });

    // Update the best provider indicator
    const bestProviderIndicator = document.getElementById('best-ai-provider');
    if (bestProviderIndicator) {
        // For regular users, always show Smart Engine
        if (!showAiStatus) {
            bestProviderIndicator.textContent = 'Smart Engine';
            bestProviderIndicator.className = 'badge bg-success';
            return;
        }

        // Admin-only code below - only admins will see the actual status
        if (status.best_provider) {
            // Get the best provider from the status
            const bestProvider = status.best_provider;

            // Update the indicator
            if (bestProvider) {
                bestProviderIndicator.textContent = bestProvider;
                bestProviderIndicator.className = 'badge bg-success';
            } else {
                bestProviderIndicator.textContent = 'Offline';
                bestProviderIndicator.className = 'badge bg-danger';
            }
        }
    }
}
