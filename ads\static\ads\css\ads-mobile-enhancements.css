/* Enhanced Mobile Responsiveness for Ads Engine */

/* Base Mobile Improvements */
@media (max-width: 767.98px) {
    /* General Typography */
    body {
        font-size: 16px; /* Prevent iOS zoom on inputs */
    }
    
    h1, .h1 {
        font-size: 1.75rem;
    }
    
    h2, .h2 {
        font-size: 1.5rem;
    }
    
    h3, .h3 {
        font-size: 1.25rem;
    }
    
    /* Improved Touch Targets */
    .btn, 
    .nav-link,
    .dropdown-item,
    .form-control,
    .form-select {
        min-height: 44px; /* Apple's recommended minimum */
        padding: 0.75rem 1rem;
    }
    
    /* Fix for buttons */
    .btn-group .btn {
        padding: 0.5rem 0.75rem;
    }
    
    /* Improved Spacing */
    .container, 
    .container-fluid {
        padding-left: 1.25rem;
        padding-right: 1.25rem;
    }
    
    .card {
        margin-bottom: 1.5rem;
    }
    
    .card-body {
        padding: 1.25rem;
    }
    
    /* Fix for tables */
    .table th, 
    .table td {
        padding: 0.75rem 0.5rem;
        font-size: 0.9rem;
    }
    
    /* Fix for modals */
    .modal-body {
        padding: 1.25rem;
    }
    
    .modal-footer {
        padding: 1rem 1.25rem;
    }
    
    /* Fix for dropdowns */
    .dropdown-menu {
        width: 100%;
        max-height: 80vh;
        overflow-y: auto;
    }
}

/* Ad Creation Page Improvements */
@media (max-width: 767.98px) {
    /* Fix for ad creation form */
    .ad-creation-form .form-group {
        margin-bottom: 1.25rem;
    }
    
    .ad-creation-form .form-label {
        margin-bottom: 0.5rem;
        font-weight: 500;
    }
    
    /* Fix for ad preview */
    .ad-preview {
        margin-top: 1.5rem;
        padding: 1rem;
    }
    
    /* Fix for file uploads */
    .custom-file-upload {
        display: block;
        width: 100%;
        padding: 0.75rem 1rem;
        text-align: center;
    }
}

/* Ad Dashboard Improvements */
@media (max-width: 767.98px) {
    /* Fix for dashboard cards */
    .dashboard-card {
        padding: 1.25rem;
    }
    
    /* Fix for stat cards */
    .stat-card {
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .stat-value {
        font-size: 1.5rem;
    }
    
    /* Fix for action buttons */
    .action-button {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    /* Fix for tables */
    .table-responsive {
        margin-bottom: 0;
    }
}

/* Analytics Page Improvements */
@media (max-width: 767.98px) {
    /* Fix for chart containers */
    .chart-container {
        height: 300px;
        margin-bottom: 1.5rem;
    }
    
    /* Fix for date range picker */
    .daterangepicker {
        width: 100%;
        max-width: 300px;
    }
    
    /* Fix for analytics tabs */
    .nav-tabs .nav-link {
        padding: 0.75rem 0.5rem;
        font-size: 0.9rem;
    }
    
    /* Fix for geography view */
    #world-map {
        height: 300px !important;
    }
    
    /* Fix for sidebar */
    .dashboard-sidebar {
        margin-bottom: 1.5rem;
    }
    
    .sidebar-nav {
        display: flex;
        overflow-x: auto;
        padding-bottom: 0.5rem;
        margin-bottom: 1rem;
        border-bottom: 1px solid rgba(0,0,0,0.1);
    }
    
    .nav-item {
        margin-right: 1rem;
        white-space: nowrap;
    }
}

/* Ad List Page Improvements */
@media (max-width: 767.98px) {
    /* Fix for ad list items */
    .ad-list-item {
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    /* Fix for ad status badges */
    .ad-status {
        display: inline-block;
        width: 100%;
        text-align: center;
        margin-top: 0.5rem;
    }
    
    /* Fix for ad actions */
    .ad-actions {
        margin-top: 1rem;
        display: flex;
        justify-content: space-between;
    }
    
    .ad-actions .btn {
        flex: 1;
        margin: 0 0.25rem;
        padding: 0.5rem;
        font-size: 0.9rem;
    }
}

/* Ad Detail Page Improvements */
@media (max-width: 767.98px) {
    /* Fix for ad detail sections */
    .ad-detail-section {
        margin-bottom: 1.5rem;
    }
    
    /* Fix for ad image */
    .ad-image-container {
        height: 200px;
        margin-bottom: 1rem;
    }
    
    /* Fix for action buttons */
    .action-buttons {
        flex-direction: column;
    }
    
    .action-buttons .action-button {
        margin-right: 0;
        margin-bottom: 0.75rem;
    }
}

/* Small Mobile Devices */
@media (max-width: 575.98px) {
    /* Further reduce sizes */
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        font-size: 0.9rem;
    }
    
    /* Hide less important columns */
    .hide-xs {
        display: none;
    }
    
    /* Stack buttons */
    .btn-group {
        display: flex;
        flex-direction: column;
    }
    
    .btn-group .btn {
        border-radius: 0.25rem !important;
        margin-bottom: 0.5rem;
    }
}

/* Landscape Mode Optimizations */
@media (max-width: 767.98px) and (orientation: landscape) {
    /* Adjust heights */
    .chart-container {
        height: 250px;
    }
    
    #world-map {
        height: 250px !important;
    }
    
    /* Improve modal scrolling */
    .modal-body {
        max-height: 60vh;
        overflow-y: auto;
    }
}
