{% extends 'base.html' %}
{% load static %}

{% block title %}Enterprise QR | Bulk QR Generation{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/premium.css' %}">
<style>
    /* Ultra-Premium Bulk Generation Corporate Styling */
    body {
        background:
            linear-gradient(135deg, #000000 0%, #0a0a0a 8%, #1a1a2e 20%, #16213e 35%, #0f3460 55%, #533483 75%, #764ba2 95%, #8e44ad 100%);
        min-height: 100vh;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        position: relative;
        overflow-x: hidden;
    }

    /* Premium animated background with bulk processing patterns */
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 2% 98%, rgba(102, 126, 234, 0.8) 0%, transparent 20%),
            radial-gradient(circle at 98% 2%, rgba(255, 255, 255, 0.35) 0%, transparent 18%),
            radial-gradient(circle at 15% 80%, rgba(118, 75, 162, 0.7) 0%, transparent 25%),
            radial-gradient(circle at 85% 95%, rgba(83, 52, 131, 0.6) 0%, transparent 12%),
            radial-gradient(circle at 70% 50%, rgba(102, 126, 234, 0.5) 0%, transparent 30%),
            radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.25) 0%, transparent 20%),
            radial-gradient(circle at 60% 85%, rgba(118, 75, 162, 0.4) 0%, transparent 35%),
            radial-gradient(circle at 40% 15%, rgba(142, 68, 173, 0.3) 0%, transparent 28%);
        z-index: -1;
        animation: bulkProcessingFloat 45s ease-in-out infinite;
    }

    /* Corporate bulk processing pattern overlay */
    body::after {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image:
            repeating-linear-gradient(45deg, rgba(255, 255, 255, 0.03) 0px, rgba(255, 255, 255, 0.03) 2px, transparent 2px, transparent 12px),
            repeating-linear-gradient(-45deg, rgba(255, 255, 255, 0.02) 0px, rgba(255, 255, 255, 0.02) 1px, transparent 1px, transparent 8px),
            radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.06) 3px, transparent 3px),
            radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.04) 2px, transparent 2px);
        background-size: 120px 120px, 80px 80px, 150px 150px, 100px 100px;
        z-index: -1;
        opacity: 0.9;
        animation: bulkPatternPulse 25s ease-in-out infinite;
    }

    @keyframes bulkProcessingFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
        12% { transform: translateY(-35px) rotate(2.5deg) scale(1.06); }
        25% { transform: translateY(25px) rotate(-1.2deg) scale(0.94); }
        37% { transform: translateY(-30px) rotate(1.8deg) scale(1.05); }
        50% { transform: translateY(20px) rotate(-1.5deg) scale(0.96); }
        62% { transform: translateY(-25px) rotate(1deg) scale(1.03); }
        75% { transform: translateY(15px) rotate(-0.8deg) scale(0.98); }
        87% { transform: translateY(-20px) rotate(0.6deg) scale(1.01); }
    }

    @keyframes bulkPatternPulse {
        0%, 100% { opacity: 0.7; transform: scale(1) rotate(0deg); }
        50% { opacity: 1.2; transform: scale(1.04) rotate(3deg); }
    }

    /* Ultra-Premium Hero Section for Bulk Generation */
    .premium-hero.bulk-hero {
        padding: 7rem 0;
        position: relative;
        background: linear-gradient(135deg,
            rgba(0, 0, 0, 0.85) 0%,
            rgba(26, 26, 46, 0.75) 25%,
            rgba(22, 33, 62, 0.65) 50%,
            rgba(15, 52, 96, 0.55) 75%,
            rgba(83, 52, 131, 0.45) 100%);
        backdrop-filter: blur(40px);
        border-bottom: 4px solid rgba(255, 255, 255, 0.25);
        box-shadow:
            0 40px 80px rgba(0, 0, 0, 0.5),
            inset 0 2px 0 rgba(255, 255, 255, 0.25);
    }

    .premium-hero.bulk-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: conic-gradient(from 0deg,
            rgba(102, 126, 234, 0.4) 0deg,
            rgba(255, 255, 255, 0.25) 72deg,
            rgba(118, 75, 162, 0.4) 144deg,
            rgba(255, 255, 255, 0.15) 216deg,
            rgba(142, 68, 173, 0.35) 288deg,
            rgba(102, 126, 234, 0.4) 360deg);
        z-index: 0;
        animation: heroBulkGlow 18s linear infinite;
        opacity: 0.7;
    }

    @keyframes heroBulkGlow {
        0% { transform: rotate(0deg); opacity: 0.5; }
        50% { opacity: 0.9; }
        100% { transform: rotate(360deg); opacity: 0.5; }
    }

    /* Premium Badge for Bulk Generation */
    .premium-badge {
        display: inline-flex;
        align-items: center;
        background: linear-gradient(135deg,
            rgba(102, 126, 234, 0.95) 0%,
            rgba(118, 75, 162, 0.95) 50%,
            rgba(142, 68, 173, 0.95) 100%);
        color: white;
        padding: 1.2rem 2.5rem;
        border-radius: 60px;
        font-weight: 900;
        font-size: 0.95rem;
        letter-spacing: 2px;
        text-transform: uppercase;
        margin-bottom: 2.5rem;
        box-shadow:
            0 20px 50px rgba(102, 126, 234, 0.5),
            0 10px 30px rgba(0, 0, 0, 0.4),
            inset 0 3px 0 rgba(255, 255, 255, 0.35);
        border: 3px solid rgba(255, 255, 255, 0.35);
        position: relative;
        overflow: hidden;
        animation: badgeBulkPulse 4s ease-in-out infinite;
    }

    .premium-badge::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
            transparent 0%,
            rgba(255, 255, 255, 0.5) 50%,
            transparent 100%);
        animation: badgeBulkShimmer 5s ease-in-out infinite;
    }

    @keyframes badgeBulkPulse {
        0%, 100% {
            box-shadow:
                0 20px 50px rgba(102, 126, 234, 0.5),
                0 10px 30px rgba(0, 0, 0, 0.4),
                inset 0 3px 0 rgba(255, 255, 255, 0.35);
            transform: scale(1);
        }
        50% {
            box-shadow:
                0 25px 60px rgba(102, 126, 234, 0.7),
                0 15px 40px rgba(0, 0, 0, 0.5),
                inset 0 4px 0 rgba(255, 255, 255, 0.45);
            transform: scale(1.02);
        }
    }

    @keyframes badgeBulkShimmer {
        0%, 100% { left: -100%; }
        50% { left: 100%; }
    }

    .premium-badge i {
        margin-right: 1rem;
        font-size: 1.2rem;
        filter: drop-shadow(0 3px 10px rgba(0, 0, 0, 0.4));
        animation: crownBulkRotate 8s ease-in-out infinite;
    }

    @keyframes crownBulkRotate {
        0%, 100% { transform: rotate(0deg) scale(1); }
        25% { transform: rotate(8deg) scale(1.15); }
        75% { transform: rotate(-5deg) scale(1.08); }
    }

    /* Ultra-Premium Title for Bulk Generation */
    .premium-title {
        font-size: 4.5rem;
        font-weight: 900;
        color: white;
        margin-bottom: 2rem;
        background: linear-gradient(135deg, #ffffff 0%, #e0e0e0 25%, #ffffff 50%, #f0f0f0 75%, #ffffff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 20px 60px rgba(0, 0, 0, 0.8);
        position: relative;
        animation: titleBulkGlow 7s ease-in-out infinite;
        line-height: 1.05;
        letter-spacing: -0.03em;
    }

    .premium-title::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.6) 0%, rgba(118, 75, 162, 0.6) 50%, rgba(142, 68, 173, 0.6) 100%);
        -webkit-background-clip: text;
        background-clip: text;
        z-index: -1;
        animation: titleBulkShimmer 12s ease-in-out infinite;
    }

    @keyframes titleBulkGlow {
        0%, 100% { text-shadow: 0 20px 60px rgba(0, 0, 0, 0.8); }
        50% { text-shadow: 0 20px 60px rgba(102, 126, 234, 0.6), 0 0 120px rgba(255, 255, 255, 0.5); }
    }

    @keyframes titleBulkShimmer {
        0%, 100% { opacity: 0.6; }
        50% { opacity: 1; }
    }

    /* Premium Subtitle for Bulk Generation */
    .premium-subtitle {
        font-size: 1.5rem;
        color: rgba(255, 255, 255, 0.95);
        margin-bottom: 3.5rem;
        line-height: 1.9;
        font-weight: 600;
        text-shadow: 0 10px 35px rgba(0, 0, 0, 0.5);
        max-width: 700px;
        margin-left: auto;
        margin-right: auto;
        letter-spacing: 0.8px;
    }

    /* Premium CTA Buttons for Bulk Generation */
    .hero-cta {
        display: flex;
        gap: 2rem;
        justify-content: center;
        flex-wrap: wrap;
    }

    .btn {
        border-radius: 30px;
        padding: 1.4rem 3.5rem;
        font-weight: 900;
        font-size: 1.1rem;
        letter-spacing: 1.5px;
        transition: all 1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        text-transform: uppercase;
        border: none;
        position: relative;
        overflow: hidden;
        box-shadow: 0 15px 50px rgba(0, 0, 0, 0.4);
    }

    .btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
            transparent 0%,
            rgba(255, 255, 255, 0.5) 50%,
            transparent 100%);
        transition: left 1.2s ease;
    }

    .btn:hover::before {
        left: 100%;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #8e44ad 70%, #667eea 100%);
        color: white;
        box-shadow:
            0 20px 55px rgba(102, 126, 234, 0.6),
            0 10px 30px rgba(0, 0, 0, 0.25),
            inset 0 4px 0 rgba(255, 255, 255, 0.35);
        border: 4px solid rgba(255, 255, 255, 0.35);
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 30%, #7c3aed 70%, #5a67d8 100%);
        transform: translateY(-8px) scale(1.08);
        box-shadow:
            0 30px 70px rgba(102, 126, 234, 0.7),
            0 18px 45px rgba(0, 0, 0, 0.35),
            inset 0 5px 0 rgba(255, 255, 255, 0.45);
        color: white;
        border-color: rgba(255, 255, 255, 0.55);
    }

    .btn-outline-light {
        color: white;
        border: 4px solid rgba(255, 255, 255, 0.6);
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%);
        backdrop-filter: blur(25px);
    }

    .btn-outline-light:hover {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 249, 250, 0.85) 100%);
        color: #2c3e50;
        transform: translateY(-8px) scale(1.08);
        box-shadow:
            0 25px 60px rgba(255, 255, 255, 0.4),
            0 15px 35px rgba(0, 0, 0, 0.25);
        border-color: rgba(255, 255, 255, 0.9);
    }

    /* Premium Overview Section for Bulk Generation */
    .premium-overview {
        padding: 10rem 0;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.99) 0%,
            rgba(248, 249, 250, 0.96) 30%,
            rgba(255, 255, 255, 0.99) 70%,
            rgba(240, 242, 247, 0.96) 100%);
        position: relative;
        backdrop-filter: blur(40px);
        border-top: 4px solid rgba(102, 126, 234, 0.35);
        border-bottom: 4px solid rgba(102, 126, 234, 0.35);
    }

    .premium-overview::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg,
            rgba(102, 126, 234, 0.08) 0%,
            rgba(255, 255, 255, 0.15) 30%,
            rgba(118, 75, 162, 0.08) 70%,
            rgba(255, 255, 255, 0.12) 100%);
        z-index: 0;
    }

    .premium-overview .container {
        position: relative;
        z-index: 1;
    }

    .section-title {
        font-size: 3.5rem;
        font-weight: 900;
        color: #2c3e50;
        margin-bottom: 2.5rem;
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 30%, #2c3e50 70%, #1a252f 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 10px 35px rgba(0, 0, 0, 0.15);
        animation: sectionTitleBulkPulse 5s ease-in-out infinite;
    }

    @keyframes sectionTitleBulkPulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.03); }
    }

    .section-description {
        font-size: 1.4rem;
        color: #5a6c7d;
        line-height: 1.9;
        margin-bottom: 2.5rem;
        font-weight: 600;
    }

    /* Premium Feature List for Bulk Generation */
    .feature-list {
        list-style: none;
        padding: 0;
        margin: 3rem 0;
    }

    .feature-list li {
        display: flex;
        align-items: center;
        padding: 1.2rem 0;
        font-size: 1.2rem;
        color: #2c3e50;
        font-weight: 700;
        border-bottom: 2px solid rgba(102, 126, 234, 0.15);
        transition: all 0.4s ease;
        border-radius: 10px;
    }

    .feature-list li:hover {
        background: rgba(102, 126, 234, 0.08);
        transform: translateX(15px);
        padding-left: 1.5rem;
        border-radius: 12px;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
    }

    .feature-list li i {
        color: #10b981;
        margin-right: 1.5rem;
        font-size: 1.4rem;
        filter: drop-shadow(0 3px 10px rgba(16, 185, 129, 0.4));
    }
</style>
{% endblock %}

{% block content %}
<div class="premium-hero bulk-hero">
    <div class="premium-hero-content">
        <div class="premium-badge">
            <i class="fas fa-crown"></i>
            <span>Premium Feature</span>
        </div>
        <h1 class="premium-title">Bulk QR Generation</h1>
        <p class="premium-subtitle">Generate thousands of QR codes in minutes with our enterprise-grade bulk generation tool</p>
        <div class="hero-cta">
            <a href="{% url 'batch_processing' %}" class="btn btn-primary btn-lg">Start Generating</a>
            <a href="#features" class="btn btn-outline-light btn-lg">Learn More</a>
        </div>
    </div>
    <div class="premium-hero-image">
        <img src="{% static 'img/premium/bulk-generation-hero.svg' %}" alt="Bulk QR Generation">
    </div>
</div>

<div class="premium-overview">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h2 class="section-title">Scale Your QR Code Production</h2>
                <p class="section-description">Our bulk generation feature allows you to create large quantities of QR codes efficiently, saving you time and resources while maintaining the highest quality standards.</p>
                <p>With Enterprise QR's bulk generation, you can:</p>
                <ul class="feature-list">
                    <li><i class="fas fa-check-circle"></i> Generate thousands of unique QR codes in one batch</li>
                    <li><i class="fas fa-check-circle"></i> Import data from CSV, Excel, or database sources</li>
                    <li><i class="fas fa-check-circle"></i> Apply consistent branding across all codes</li>
                    <li><i class="fas fa-check-circle"></i> Export in multiple formats (PNG, SVG, PDF)</li>
                    <li><i class="fas fa-check-circle"></i> Track performance of all codes in one dashboard</li>
                </ul>
            </div>
            <div class="col-lg-6">
                <div class="overview-image">
                    <img src="{% static 'img/premium/bulk-generation-overview.svg' %}" alt="Bulk Generation Overview">
                </div>
            </div>
        </div>
    </div>
</div>

<div id="features" class="premium-features">
    <div class="container">
        <h2 class="section-title text-center">Key Features</h2>
        <div class="row">
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-file-import"></i>
                    </div>
                    <h3>Data Import</h3>
                    <p>Import data from various sources including CSV, Excel, Google Sheets, and database connections to generate QR codes in bulk.</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-palette"></i>
                    </div>
                    <h3>Batch Customization</h3>
                    <p>Apply consistent design elements, colors, and branding across all QR codes while maintaining individual data for each code.</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-file-export"></i>
                    </div>
                    <h3>Flexible Export</h3>
                    <p>Export your QR codes in multiple formats including individual files, contact sheets, or merged PDFs for easy printing and distribution.</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-tasks"></i>
                    </div>
                    <h3>Batch Management</h3>
                    <p>Organize, track, and manage all your QR code batches in one centralized dashboard with detailed history and version control.</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3>Batch Analytics</h3>
                    <p>Track performance metrics across entire batches to measure campaign effectiveness and user engagement patterns.</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3>Scheduled Generation</h3>
                    <p>Schedule batch generation tasks to run automatically at specified intervals, perfect for recurring campaigns or updates.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="premium-use-cases">
    <div class="container">
        <h2 class="section-title text-center">Common Use Cases</h2>
        <div class="row">
            <div class="col-lg-6">
                <div class="use-case-card">
                    <div class="use-case-icon">
                        <i class="fas fa-ticket-alt"></i>
                    </div>
                    <div class="use-case-content">
                        <h3>Event Tickets</h3>
                        <p>Generate unique QR codes for thousands of event tickets, each linked to specific attendee information for seamless check-in and access control.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="use-case-card">
                    <div class="use-case-icon">
                        <i class="fas fa-box"></i>
                    </div>
                    <div class="use-case-content">
                        <h3>Product Inventory</h3>
                        <p>Create QR codes for entire product inventories, linking each code to specific product information, tracking data, and authentication details.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="use-case-card">
                    <div class="use-case-icon">
                        <i class="fas fa-id-card"></i>
                    </div>
                    <div class="use-case-content">
                        <h3>Employee Badges</h3>
                        <p>Generate personalized QR codes for employee ID badges that provide access control, time tracking, and resource authorization.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="use-case-card">
                    <div class="use-case-icon">
                        <i class="fas fa-mail-bulk"></i>
                    </div>
                    <div class="use-case-content">
                        <h3>Direct Mail Campaigns</h3>
                        <p>Create unique QR codes for direct mail campaigns that track individual recipient engagement and provide personalized landing pages.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="premium-cta">
    <div class="container">
        <div class="cta-content">
            <h2>Ready to Scale Your QR Code Production?</h2>
            <p>Start generating QR codes in bulk today and experience the power of enterprise-grade batch processing.</p>
            <div class="cta-buttons">
                <a href="{% url 'batch_processing' %}" class="btn btn-primary btn-lg">Start Generating Now</a>
                <a href="#" class="btn btn-outline-light btn-lg">Contact Sales</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
