/* Enhanced QR Code Styles - Corporate, Modern, Sleek */

/* Main container styling */
.container-fluid {
    padding: 2rem;
    max-width: 1600px;
    margin: 0 auto;
}

/* Card styling */
.card {
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.05) !important;
    border: none !important;
}

.card:hover {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08) !important;
}

.card-header {
    padding: 1.2rem 1.5rem;
    border-bottom: none;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #4a6cf7, #6366f1);
}

.card-body {
    padding: 1.8rem;
}

/* QR Code Preview Container */
.qr-preview-container {
    width: 100%;
    min-height: 350px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 1rem 0;
    padding: 1.5rem;
    border-radius: 12px;
    background-color: white;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    border: 1px solid #f1f3f5;
    position: relative;
    overflow: hidden;
}

.qr-preview-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, rgba(74, 108, 247, 0.03) 0%, rgba(255, 255, 255, 0) 70%);
    z-index: 0;
}

#qr-preview {
    min-height: 350px;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

#qr-preview svg {
    max-width: 100%;
    height: auto;
    filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
    transition: all 0.3s ease;
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Animation for preview updates */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(74, 108, 247, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(74, 108, 247, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(74, 108, 247, 0);
    }
}

.animate-preview {
    animation: pulse 0.8s ease;
}

/* Live preview badge animation */
.preview-status .badge i {
    animation: spin 2s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Preview Card Styling */
.preview-card {
    position: sticky;
    top: 2rem;
}

.preview-status {
    position: absolute;
    top: 1rem;
    right: 1rem;
    z-index: 10;
}

.preview-status .badge {
    padding: 0.5rem 0.8rem;
    border-radius: 30px;
    font-weight: 500;
    font-size: 0.8rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.preview-info {
    width: 100%;
    padding: 1rem;
    border-radius: 8px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
}

.preview-details {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
}

.preview-detail-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0.5rem;
}

.preview-label {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0.2rem;
}

.preview-value {
    font-weight: 600;
    color: #495057;
}

.preview-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

/* Customization Panel */
.customization-panel {
    background: #fff;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-top: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    display: none;
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.customization-panel.show {
    display: block;
    animation: slideDown 0.3s ease forwards;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

/* Customization Tabs */
.customization-panel .nav-tabs {
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 1.5rem;
}

.customization-panel .nav-tabs .nav-link {
    color: #6c757d;
    font-weight: 500;
    border: none;
    border-bottom: 2px solid transparent;
    padding: 0.5rem 1rem;
    margin-right: 0.5rem;
    transition: all 0.2s ease;
}

.customization-panel .nav-tabs .nav-link:hover {
    color: #495057;
    border-color: transparent;
}

.customization-panel .nav-tabs .nav-link.active {
    color: #4a6cf7;
    background-color: transparent;
    border-color: #4a6cf7;
}

.customization-panel .tab-content {
    padding-top: 0.5rem;
}

/* Color inputs */
.customization-panel .form-control-color {
    height: 38px;
    padding: 0.25rem;
    cursor: pointer;
}

/* Logo preview */
#logo-preview {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 0.75rem;
    border: 1px solid #e9ecef;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.customization-section {
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.customization-section:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.customization-section h4 {
    margin-bottom: 1rem;
    color: #495057;
    font-weight: 600;
}

/* QR Code Frames */
.qr-frame {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.5rem;
    border-radius: 0.75rem;
    background: white;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.qr-frame:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

/* Mobile Phone Frame */
.qr-frame-phone {
    position: relative;
    background: #1a1a1a;
    border-radius: 2rem;
    padding: 3rem 1.5rem;
    border: 8px solid #333;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.qr-frame-phone::before {
    content: '';
    position: absolute;
    top: 1rem;
    left: 50%;
    transform: translateX(-50%);
    width: 4rem;
    height: 0.5rem;
    background: #333;
    border-radius: 1rem;
}

.qr-frame-phone .qr-container {
    background: white;
    padding: 1rem;
    border-radius: 0.5rem;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.qr-frame-phone .qr-title {
    color: white;
    margin-bottom: 1rem;
    font-weight: 600;
    text-align: center;
}

.qr-frame-phone .qr-guiding-text {
    color: white;
    margin-top: 1rem;
    text-align: center;
    font-size: 0.9rem;
}

/* Business Card Frame */
.qr-frame-business-card {
    position: relative;
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border-radius: 0.5rem;
    padding: 2rem;
    width: 100%;
    max-width: 400px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
}

.qr-frame-business-card .qr-container {
    flex: 0 0 40%;
}

.qr-frame-business-card .qr-info {
    flex: 0 0 55%;
    padding-left: 1rem;
}

.qr-frame-business-card .qr-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #212529;
    margin-bottom: 0.5rem;
}

.qr-frame-business-card .qr-guiding-text {
    font-size: 0.9rem;
    color: #6c757d;
}

/* Event Ticket Frame */
.qr-frame-ticket {
    position: relative;
    background: linear-gradient(135deg, #4a6cf7, #3a5bd9);
    border-radius: 0.75rem;
    padding: 2rem;
    width: 100%;
    max-width: 350px;
    color: white;
    box-shadow: 0 15px 35px rgba(74, 108, 247, 0.3);
}

.qr-frame-ticket::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
    border-radius: 0.75rem;
    opacity: 0.5;
    z-index: 0;
}

.qr-frame-ticket .qr-container {
    position: relative;
    z-index: 1;
    background: white;
    padding: 1rem;
    border-radius: 0.5rem;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.qr-frame-ticket .qr-title {
    position: relative;
    z-index: 1;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-align: center;
}

.qr-frame-ticket .qr-guiding-text {
    position: relative;
    z-index: 1;
    margin-top: 1rem;
    text-align: center;
    font-size: 0.9rem;
}

/* Corporate Frame */
.qr-frame-corporate {
    position: relative;
    background: white;
    border-radius: 0.5rem;
    padding: 2rem;
    border: 1px solid #e9ecef;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
}

.qr-frame-corporate::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, #4a6cf7, #00d2ff);
    border-radius: 0.5rem 0.5rem 0 0;
}

.qr-frame-corporate .qr-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #212529;
    margin-bottom: 1rem;
    text-align: center;
}

.qr-frame-corporate .qr-guiding-text {
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 1rem;
    text-align: center;
}

/* Type-specific forms */
.qr-type-specific {
    display: none;
    margin-top: 1.5rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 0.5rem;
    border: 1px solid #e9ecef;
}

/* File Upload Styling */
.file-upload-container {
    border: 2px dashed #4a6cf7;
    border-radius: 0.75rem;
    padding: 1.5rem;
    background-color: rgba(74, 108, 247, 0.05);
    transition: all 0.3s ease;
}

.file-upload-container:hover,
.file-upload-container.active-upload {
    background-color: rgba(74, 108, 247, 0.1);
    border-color: #3a5bd9;
}

.file-upload-container.active-upload {
    box-shadow: 0 5px 15px rgba(74, 108, 247, 0.1);
}

.file-upload-container label {
    font-weight: 600;
    color: #4a6cf7;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
}

.file-upload-container label i {
    font-size: 1.25rem;
    margin-right: 0.5rem;
}

.file-upload-wrapper {
    position: relative;
}

.file-upload-wrapper .form-control {
    border: 1px solid #ced4da;
    border-radius: 0.5rem;
    padding: 0.75rem;
    background-color: white;
}

.file-upload-wrapper .form-text {
    margin-top: 0.5rem;
    font-size: 0.9rem;
}

.file-upload-preview {
    margin-top: 1rem;
    display: none;
}

.file-upload-preview.show {
    display: block;
    animation: fadeIn 0.5s ease;
}

.file-preview-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.file-preview-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    color: white;
    font-size: 1.25rem;
}

.file-preview-info {
    flex: 1;
}

.file-preview-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
    font-size: 0.95rem;
}

.file-preview-meta {
    font-size: 0.8rem;
    color: #6c757d;
}

/* File Upload Highlight */
.highlight-field {
    animation: highlightField 2s ease;
}

@keyframes highlightField {
    0% {
        background-color: rgba(74, 108, 247, 0.1);
    }
    50% {
        background-color: rgba(74, 108, 247, 0.2);
    }
    100% {
        background-color: transparent;
    }
}

/* File Type Icons */
.file-type-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    font-size: 1.2rem;
    color: white;
}

.file-type-pdf {
    background: linear-gradient(135deg, #FF5722, #F44336);
}

.file-type-image {
    background: linear-gradient(135deg, #4CAF50, #8BC34A);
}

.file-type-audio {
    background: linear-gradient(135deg, #9C27B0, #673AB7);
}

.file-type-video {
    background: linear-gradient(135deg, #2196F3, #03A9F4);
}

.file-type-document {
    background: linear-gradient(135deg, #3F51B5, #2196F3);
}

/* File Preview */
.file-preview {
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: #f8f9fa;
}

.file-preview-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.file-preview-info {
    flex: 1;
}

.file-preview-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.file-preview-meta {
    font-size: 0.8rem;
    color: #6c757d;
}

.file-preview-actions {
    display: flex;
    gap: 0.5rem;
}

/* Responsive adjustments */
@media (max-width: 991.98px) {
    .container-fluid {
        padding: 1rem;
    }

    .preview-card {
        margin-top: 2rem;
        position: static;
    }

    .card-body {
        padding: 1.2rem;
    }

    .preview-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .preview-actions .btn {
        width: 100%;
    }

    .qr-preview-container {
        min-height: 300px;
    }
}

@media (max-width: 767.98px) {
    .qr-frame-business-card {
        flex-direction: column;
    }

    .qr-frame-business-card .qr-container,
    .qr-frame-business-card .qr-info {
        flex: 0 0 100%;
    }

    .qr-frame-business-card .qr-info {
        padding-left: 0;
        padding-top: 1rem;
        text-align: center;
    }

    .preview-details {
        flex-direction: column;
        align-items: center;
    }

    .preview-detail-item {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}
