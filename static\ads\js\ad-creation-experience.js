/**
 * Ad Creation Experience - Enterprise Grade JavaScript
 * Provides a sleek, modern, and exciting ad creation flow
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Ad Creation Experience JS loaded');
    
    // Initialize the step navigation
    initStepNavigation();
    
    // Initialize form field animations
    initFormFieldAnimations();
    
    // Initialize live preview
    initLivePreview();
    
    // Initialize form validation
    initFormValidation();
    
    // Initialize success animation
    initSuccessAnimation();
});

/**
 * Initialize step navigation
 */
function initStepNavigation() {
    const tabButtons = document.querySelectorAll('.nav-link[data-bs-toggle="tab"]');
    const nextButtons = document.querySelectorAll('.btn-next');
    const prevButtons = document.querySelectorAll('.btn-prev');
    const stepItems = document.querySelectorAll('.step-item');
    const progressBar = document.querySelector('.progress-bar');
    
    // Update step navigation based on active tab
    function updateStepNavigation() {
        const activeTabId = document.querySelector('.tab-pane.active').id;
        const activeStepIndex = Array.from(tabButtons).findIndex(button => 
            button.getAttribute('data-bs-target') === `#${activeTabId}`
        );
        
        // Update step items
        stepItems.forEach((item, index) => {
            if (index < activeStepIndex) {
                item.classList.add('completed');
                item.classList.remove('active');
            } else if (index === activeStepIndex) {
                item.classList.add('active');
                item.classList.remove('completed');
            } else {
                item.classList.remove('active', 'completed');
            }
        });
        
        // Update progress bar if it exists
        if (progressBar) {
            const progress = ((activeStepIndex + 1) / stepItems.length) * 100;
            progressBar.style.width = `${progress}%`;
            progressBar.setAttribute('aria-valuenow', progress);
        }
        
        // Animate form fields in the active tab
        animateFormFields(activeTabId);
    }
    
    // Initialize step navigation
    updateStepNavigation();
    
    // Add event listeners to tab buttons
    tabButtons.forEach(button => {
        button.addEventListener('shown.bs.tab', updateStepNavigation);
    });
    
    // Add event listeners to next buttons
    nextButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const currentTabId = document.querySelector('.tab-pane.active').id;
            
            // Validate current step before proceeding
            if (validateStep(currentTabId)) {
                const nextTabId = this.getAttribute('data-next');
                const nextTab = document.querySelector(`[data-bs-target="#${nextTabId}"]`);
                
                // Add animation class to button
                this.classList.add('btn-animated');
                
                // Animate transition to next step
                setTimeout(() => {
                    // Trigger click on the next tab button
                    nextTab.click();
                    
                    // Scroll to top of form
                    document.querySelector('.step-content').scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                    
                    // Remove animation class
                    this.classList.remove('btn-animated');
                }, 300);
            }
        });
    });
    
    // Add event listeners to prev buttons
    prevButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const prevTabId = this.getAttribute('data-prev');
            const prevTab = document.querySelector(`[data-bs-target="#${prevTabId}"]`);
            
            // Add animation class to button
            this.classList.add('btn-animated');
            
            // Animate transition to previous step
            setTimeout(() => {
                // Trigger click on the previous tab button
                prevTab.click();
                
                // Scroll to top of form
                document.querySelector('.step-content').scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
                
                // Remove animation class
                this.classList.remove('btn-animated');
            }, 300);
        });
    });
    
    // Add click event to step items
    stepItems.forEach((item, index) => {
        item.addEventListener('click', function() {
            const currentTabId = document.querySelector('.tab-pane.active').id;
            const currentStepIndex = Array.from(tabButtons).findIndex(button => 
                button.getAttribute('data-bs-target') === `#${currentTabId}`
            );
            
            // Only allow clicking on completed steps or the next step
            if (index <= currentStepIndex + 1) {
                // Validate current step before proceeding to a different step
                if (index > currentStepIndex && !validateStep(currentTabId)) {
                    return;
                }
                
                // Trigger click on the tab button
                tabButtons[index].click();
                
                // Scroll to top of form
                document.querySelector('.step-content').scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

/**
 * Animate form fields in the active tab
 */
function animateFormFields(tabId) {
    const formGroups = document.querySelectorAll(`#${tabId} .form-group`);
    
    formGroups.forEach((group, index) => {
        // Remove animation class first
        group.classList.remove('animate-in');
        
        // Add animation class with delay
        setTimeout(() => {
            group.classList.add('animate-in');
        }, index * 100);
    });
}

/**
 * Initialize form field animations
 */
function initFormFieldAnimations() {
    // Add animation class to all form groups initially
    const formGroups = document.querySelectorAll('.form-group');
    formGroups.forEach(group => group.classList.add('animate-in'));
    
    // Add focus animation to form controls
    const formControls = document.querySelectorAll('.form-control, .form-select');
    formControls.forEach(control => {
        control.addEventListener('focus', function() {
            this.closest('.form-group').classList.add('focused');
        });
        
        control.addEventListener('blur', function() {
            this.closest('.form-group').classList.remove('focused');
        });
    });
}

/**
 * Initialize live preview
 */
function initLivePreview() {
    // Get form elements
    const adTitle = document.getElementById('adTitle');
    const adContent = document.getElementById('adContent');
    const ctaLink = document.getElementById('ctaLink');
    const adMedia = document.getElementById('adMedia');
    
    // Get preview elements
    const previewTitle = document.getElementById('previewTitle');
    const previewContent = document.getElementById('previewContent');
    const previewCta = document.getElementById('previewCta');
    const previewImage = document.querySelector('.preview-image img');
    
    // Update preview when inputs change
    if (adTitle) {
        adTitle.addEventListener('input', updatePreview);
    }
    
    if (adContent) {
        adContent.addEventListener('input', updatePreview);
    }
    
    if (ctaLink) {
        ctaLink.addEventListener('input', updatePreview);
    }
    
    if (adMedia) {
        adMedia.addEventListener('change', handleImageUpload);
    }
    
    function updatePreview() {
        // Update title
        if (previewTitle && adTitle) {
            previewTitle.textContent = adTitle.value || 'Your Ad Title';
            previewTitle.classList.add('highlight');
            setTimeout(() => previewTitle.classList.remove('highlight'), 500);
        }
        
        // Update content
        if (previewContent && adContent) {
            previewContent.textContent = adContent.value || 'Your ad content will appear here as you type.';
            previewContent.classList.add('highlight');
            setTimeout(() => previewContent.classList.remove('highlight'), 500);
        }
        
        // Update CTA link
        if (previewCta && ctaLink) {
            previewCta.href = ctaLink.value || '#';
            previewCta.classList.add('highlight');
            setTimeout(() => previewCta.classList.remove('highlight'), 500);
        }
        
        // Update review tab if it exists
        updateReviewTab();
    }
    
    function handleImageUpload() {
        if (previewImage && adMedia && adMedia.files && adMedia.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImage.src = e.target.result;
                previewImage.classList.add('highlight');
                setTimeout(() => previewImage.classList.remove('highlight'), 500);
            };
            reader.readAsDataURL(adMedia.files[0]);
        }
    }
    
    // Initial preview update
    updatePreview();
}

/**
 * Update review tab with form values
 */
function updateReviewTab() {
    // Get form elements
    const adTitle = document.getElementById('adTitle');
    const adContent = document.getElementById('adContent');
    const adType = document.getElementById('adType');
    const adLocation = document.getElementById('adLocation');
    const campaign = document.querySelector('.campaign-select');
    
    // Get review elements
    const reviewTitle = document.getElementById('reviewTitle');
    const reviewContent = document.getElementById('reviewContent');
    const reviewType = document.getElementById('reviewType');
    const reviewLocation = document.getElementById('reviewLocation');
    const reviewCampaign = document.getElementById('reviewCampaign');
    
    // Update review elements if they exist
    if (reviewTitle && adTitle) {
        reviewTitle.textContent = adTitle.value || 'Not specified';
    }
    
    if (reviewContent && adContent) {
        reviewContent.textContent = adContent.value || 'Not specified';
    }
    
    if (reviewType && adType) {
        reviewType.textContent = adType.options[adType.selectedIndex]?.text || 'Not specified';
    }
    
    if (reviewLocation && adLocation) {
        reviewLocation.textContent = adLocation.options[adLocation.selectedIndex]?.text || 'Not specified';
    }
    
    if (reviewCampaign && campaign) {
        const campaignText = campaign.options[campaign.selectedIndex]?.text || 'No Campaign';
        reviewCampaign.textContent = campaignText;
    }
}

/**
 * Validate a step
 */
function validateStep(stepId) {
    const requiredFields = document.querySelectorAll(`#${stepId} [required]`);
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value) {
            isValid = false;
            field.classList.add('is-invalid');
            
            // Add shake animation
            field.classList.add('shake');
            setTimeout(() => field.classList.remove('shake'), 500);
            
            // Add error message if it doesn't exist
            if (!field.nextElementSibling || !field.nextElementSibling.classList.contains('invalid-feedback')) {
                const errorMessage = document.createElement('div');
                errorMessage.classList.add('invalid-feedback');
                errorMessage.textContent = 'This field is required';
                field.parentNode.insertBefore(errorMessage, field.nextSibling);
            }
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    return isValid;
}

/**
 * Initialize form validation
 */
function initFormValidation() {
    // Add event listeners to required fields
    const requiredFields = document.querySelectorAll('[required]');
    requiredFields.forEach(field => {
        field.addEventListener('input', function() {
            if (this.value) {
                this.classList.remove('is-invalid');
            }
        });
    });
    
    // Add form submission validation
    const adCreationForm = document.getElementById('adCreationForm');
    if (adCreationForm) {
        adCreationForm.addEventListener('submit', function(e) {
            const activeTabId = document.querySelector('.tab-pane.active').id;
            
            // Validate all steps before submission
            const allSteps = document.querySelectorAll('.tab-pane');
            let isValid = true;
            
            allSteps.forEach(step => {
                if (!validateStep(step.id)) {
                    isValid = false;
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                alert('Please fill in all required fields');
            } else {
                // Show success animation
                showSuccessAnimation();
            }
        });
    }
}

/**
 * Initialize success animation
 */
function initSuccessAnimation() {
    // Create success animation container
    const successContainer = document.createElement('div');
    successContainer.classList.add('success-animation');
    successContainer.innerHTML = `
        <div class="success-icon">
            <i class="fas fa-check"></i>
        </div>
        <h3>Ad Created Successfully!</h3>
        <p>Your ad has been created and is now pending approval.</p>
    `;
    
    // Add to document body
    document.body.appendChild(successContainer);
    
    // Hide initially
    successContainer.style.display = 'none';
}

/**
 * Show success animation
 */
function showSuccessAnimation() {
    const successContainer = document.querySelector('.success-animation');
    if (successContainer) {
        successContainer.style.display = 'block';
    }
}
