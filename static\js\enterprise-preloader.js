/**
 * Enterprise Preloader with Smooth Landing Animations
 * Provides professional loading experience with smooth transitions
 */

class EnterprisePreloader {
    constructor() {
        this.preloader = document.getElementById('preloader');
        this.progressBar = document.getElementById('progress-bar');
        this.statusText = document.getElementById('preloader-status');
        this.progress = 0;
        this.loadingSteps = [
            { progress: 20, text: 'Initializing...' },
            { progress: 40, text: 'Loading resources...' },
            { progress: 60, text: 'Preparing interface...' },
            { progress: 80, text: 'Finalizing...' },
            { progress: 100, text: 'Ready!' }
        ];
        this.currentStep = 0;
        this.isComplete = false;
        
        this.init();
    }

    init() {
        // Ensure body doesn't scroll during loading
        document.body.style.overflow = 'hidden';
        
        // Start the loading sequence
        this.startLoading();
        
        // Listen for page load events
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Start hiding preloader when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.accelerateLoading();
            });
        } else {
            this.accelerateLoading();
        }

        // Complete loading when everything is loaded
        window.addEventListener('load', () => {
            this.completeLoading();
        });

        // Fallback: Force completion after maximum time
        setTimeout(() => {
            if (!this.isComplete) {
                console.log('Preloader: Forcing completion after timeout');
                this.completeLoading();
            }
        }, 4000); // 4 second maximum
    }

    startLoading() {
        this.updateProgress();
    }

    updateProgress() {
        if (this.currentStep < this.loadingSteps.length && !this.isComplete) {
            const step = this.loadingSteps[this.currentStep];
            
            // Animate progress bar
            this.animateProgressTo(step.progress);
            
            // Update status text
            this.updateStatus(step.text);
            
            this.currentStep++;
            
            // Schedule next step
            const delay = this.currentStep === 1 ? 300 : 600; // Faster start
            setTimeout(() => this.updateProgress(), delay);
        }
    }

    accelerateLoading() {
        // Speed up loading when DOM is ready
        if (this.currentStep < this.loadingSteps.length - 1) {
            this.currentStep = this.loadingSteps.length - 2;
            this.updateProgress();
        }
    }

    completeLoading() {
        if (this.isComplete) return;
        
        this.isComplete = true;
        
        // Complete the progress bar
        this.animateProgressTo(100);
        this.updateStatus('Ready!');
        
        // Hide preloader with smooth animation
        setTimeout(() => {
            this.hidePreloader();
        }, 500);
    }

    animateProgressTo(targetProgress) {
        const startProgress = this.progress;
        const progressDiff = targetProgress - startProgress;
        const duration = 400;
        const startTime = performance.now();

        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // Smooth easing function
            const easeProgress = this.easeOutCubic(progress);
            this.progress = startProgress + (progressDiff * easeProgress);
            
            // Update progress bar
            this.progressBar.style.width = this.progress + '%';
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        requestAnimationFrame(animate);
    }

    updateStatus(text) {
        if (this.statusText) {
            // Fade out, change text, fade in
            this.statusText.style.opacity = '0';
            setTimeout(() => {
                this.statusText.textContent = text;
                this.statusText.style.opacity = '1';
            }, 150);
        }
    }

    hidePreloader() {
        if (!this.preloader) return;

        // Add fade-out class for smooth transition
        this.preloader.classList.add('fade-out');
        
        // Restore body scrolling
        document.body.style.overflow = '';
        
        // Add smooth reveal animation to main content
        this.revealMainContent();
        
        // Remove preloader from DOM after animation
        setTimeout(() => {
            if (this.preloader && this.preloader.parentNode) {
                this.preloader.parentNode.removeChild(this.preloader);
            }
        }, 600);
    }

    revealMainContent() {
        // Add smooth reveal animations to page elements
        const elementsToReveal = [
            'main',
            '.enterprise-hero-section',
            '.hero-content',
            '.navbar',
            'footer'
        ];

        elementsToReveal.forEach((selector, index) => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                if (element) {
                    element.style.opacity = '0';
                    element.style.transform = 'translateY(20px)';
                    element.style.transition = 'all 0.6s ease-out';
                    
                    // Stagger the animations
                    setTimeout(() => {
                        element.style.opacity = '1';
                        element.style.transform = 'translateY(0)';
                    }, 100 + (index * 100));
                }
            });
        });

        // Special animation for hero elements
        setTimeout(() => {
            this.animateHeroElements();
        }, 300);
    }

    animateHeroElements() {
        const heroElements = [
            '.enterprise-badge',
            '.hero-title',
            '.hero-subtitle',
            '.hero-features',
            '.hero-description',
            '.hero-actions',
            '.form-glass-card'
        ];

        heroElements.forEach((selector, index) => {
            const element = document.querySelector(selector);
            if (element) {
                element.style.opacity = '0';
                element.style.transform = 'translateY(30px)';
                element.style.transition = 'all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
                
                setTimeout(() => {
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, index * 150);
            }
        });

        // Animate floating QR elements
        setTimeout(() => {
            const floatingElements = document.querySelectorAll('.floating-qr');
            floatingElements.forEach((element, index) => {
                element.style.opacity = '0';
                element.style.transform = 'scale(0.8) rotate(45deg)';
                element.style.transition = 'all 1s cubic-bezier(0.34, 1.56, 0.64, 1)';
                
                setTimeout(() => {
                    element.style.opacity = '1';
                    element.style.transform = 'scale(1) rotate(0deg)';
                }, index * 200);
            });
        }, 600);
    }

    easeOutCubic(t) {
        return 1 - Math.pow(1 - t, 3);
    }
}

// Initialize preloader when script loads
document.addEventListener('DOMContentLoaded', () => {
    new EnterprisePreloader();
});

// Fallback initialization if DOMContentLoaded already fired
if (document.readyState !== 'loading') {
    new EnterprisePreloader();
}
