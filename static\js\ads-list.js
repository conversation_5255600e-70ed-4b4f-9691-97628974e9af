document.addEventListener('DOMContentLoaded', function() {
    console.log('Ads List JS loaded');
    
    // Search functionality
    const searchInput = document.getElementById('adSearch');
    const searchButton = document.getElementById('searchButton');
    const adsTable = document.getElementById('adsTable');
    
    if (searchInput && searchButton && adsTable) {
        searchButton.addEventListener('click', function() {
            const searchTerm = searchInput.value.toLowerCase();
            const rows = adsTable.querySelectorAll('tbody tr');
            
            rows.forEach(row => {
                const title = row.cells[0].textContent.toLowerCase();
                const type = row.cells[1].textContent.toLowerCase();
                const status = row.cells[2].textContent.toLowerCase();
                
                if (title.includes(searchTerm) || type.includes(searchTerm) || status.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
        
        // Search on Enter key press
        searchInput.addEventListener('keyup', function(event) {
            if (event.key === 'Enter') {
                searchButton.click();
            }
        });
    }
    
    // Confirm delete
    const deleteButtons = document.querySelectorAll('a[href*="ad_delete"]');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            if (!confirm('Are you sure you want to delete this advertisement? This action cannot be undone.')) {
                e.preventDefault();
            }
        });
    });
});
