"""
Simple command to test QR codes with a public URL using localhost.run or similar services
"""
from django.core.management.base import BaseCommand
from qrcode_app.models import QRCode
import qrcode
from io import BytesIO


class Command(BaseCommand):
    help = 'Update QR codes for public testing with instructions for tunnel services'

    def add_arguments(self, parser):
        parser.add_argument(
            '--public-url',
            type=str,
            help='Public URL from tunnel service (e.g., https://abc123.localhost.run)',
        )
        parser.add_argument(
            '--revert',
            action='store_true',
            help='Revert QR codes back to localhost URLs',
        )

    def update_qr_codes_with_url(self, public_url):
        """Update QR codes to use public URL"""
        # Find QR codes that contain /qr/ paths
        qr_codes_to_update = QRCode.objects.filter(
            data__contains='/qr/'
        )
        
        total_count = qr_codes_to_update.count()
        
        if total_count == 0:
            self.stdout.write('No QR codes found to update.')
            return
        
        self.stdout.write(f'Updating {total_count} QR codes to use: {public_url}')
        
        updated_count = 0
        
        for qr_code in qr_codes_to_update:
            try:
                # Extract the path from current URL
                if '/qr/' in qr_code.data:
                    # Get everything after the domain
                    parts = qr_code.data.split('/qr/')
                    if len(parts) > 1:
                        qr_path = '/qr/' + parts[1]
                        new_url = public_url.rstrip('/') + qr_path
                        
                        old_url = qr_code.data
                        qr_code.data = new_url
                        
                        # Regenerate QR code image
                        qr = qrcode.QRCode(
                            version=40,
                            error_correction=qrcode.constants.ERROR_CORRECT_L,
                            box_size=10,
                            border=4,
                        )
                        qr.add_data(new_url)
                        qr.make(fit=True)
                        
                        img = qr.make_image(
                            fill_color=qr_code.foreground_color, 
                            back_color=qr_code.background_color
                        )
                        
                        buffer = BytesIO()
                        img.save(buffer, format='PNG')
                        
                        filename = f"{qr_code.name.replace(' ', '_')}.png"
                        qr_code.image.save(filename, buffer, save=False)
                        qr_code.save()
                        
                        updated_count += 1
                        self.stdout.write(f'✓ {qr_code.name}: {old_url} → {new_url}')
                        
            except Exception as e:
                self.stdout.write(f'✗ Error updating {qr_code.name}: {str(e)}')
        
        self.stdout.write(
            self.style.SUCCESS(f'Updated {updated_count} QR codes successfully!')
        )

    def revert_to_localhost(self):
        """Revert QR codes back to localhost"""
        qr_codes_to_revert = QRCode.objects.exclude(
            data__contains='127.0.0.1:8000'
        ).filter(
            data__contains='/qr/'
        )
        
        total_count = qr_codes_to_revert.count()
        
        if total_count == 0:
            self.stdout.write('No QR codes need reverting.')
            return
        
        self.stdout.write(f'Reverting {total_count} QR codes to localhost...')
        
        reverted_count = 0
        
        for qr_code in qr_codes_to_revert:
            try:
                if '/qr/' in qr_code.data:
                    parts = qr_code.data.split('/qr/')
                    if len(parts) > 1:
                        qr_path = '/qr/' + parts[1]
                        new_url = f'http://127.0.0.1:8000{qr_path}'
                        
                        old_url = qr_code.data
                        qr_code.data = new_url
                        
                        # Regenerate QR code
                        qr = qrcode.QRCode(
                            version=40,
                            error_correction=qrcode.constants.ERROR_CORRECT_L,
                            box_size=10,
                            border=4,
                        )
                        qr.add_data(new_url)
                        qr.make(fit=True)
                        
                        img = qr.make_image(
                            fill_color=qr_code.foreground_color, 
                            back_color=qr_code.background_color
                        )
                        
                        buffer = BytesIO()
                        img.save(buffer, format='PNG')
                        
                        filename = f"{qr_code.name.replace(' ', '_')}.png"
                        qr_code.image.save(filename, buffer, save=False)
                        qr_code.save()
                        
                        reverted_count += 1
                        self.stdout.write(f'✓ {qr_code.name}: {old_url} → {new_url}')
                        
            except Exception as e:
                self.stdout.write(f'✗ Error reverting {qr_code.name}: {str(e)}')
        
        self.stdout.write(
            self.style.SUCCESS(f'Reverted {reverted_count} QR codes to localhost!')
        )

    def handle(self, *args, **options):
        public_url = options.get('public_url')
        revert = options.get('revert')
        
        if revert:
            self.revert_to_localhost()
            return
        
        if not public_url:
            self.stdout.write(
                self.style.WARNING('No public URL provided. Here are your options:')
            )
            self.stdout.write('\n' + '='*60)
            self.stdout.write('🌐 REAL-WORLD QR CODE TESTING OPTIONS')
            self.stdout.write('='*60)
            
            self.stdout.write('\n1. 🚀 Using localhost.run (FREE):')
            self.stdout.write('   ssh -R 80:localhost:8000 localhost.run')
            self.stdout.write('   Then use the provided URL with this command')
            
            self.stdout.write('\n2. 🔧 Using ngrok (FREE tier available):')
            self.stdout.write('   Download from: https://ngrok.com/')
            self.stdout.write('   Run: ngrok http 8000')
            self.stdout.write('   Then use the provided URL with this command')
            
            self.stdout.write('\n3. ☁️ Using serveo.net (FREE):')
            self.stdout.write('   ssh -R 80:localhost:8000 serveo.net')
            self.stdout.write('   Then use the provided URL with this command')
            
            self.stdout.write('\n4. 🌍 Deploy to production server')
            self.stdout.write('   Use your actual domain name')
            
            self.stdout.write('\n' + '='*60)
            self.stdout.write('📱 EXAMPLE USAGE:')
            self.stdout.write('='*60)
            self.stdout.write('python manage.py test_qr_public --public-url https://abc123.localhost.run')
            self.stdout.write('python manage.py test_qr_public --revert  # To go back to localhost')
            self.stdout.write('='*60)
            
            return
        
        # Validate URL
        if not public_url.startswith(('http://', 'https://')):
            self.stdout.write(
                self.style.ERROR('Public URL must start with http:// or https://')
            )
            return
        
        self.stdout.write(f'Setting up QR codes for public testing with: {public_url}')
        self.update_qr_codes_with_url(public_url)
        
        self.stdout.write('\n' + '='*60)
        self.stdout.write('🎉 QR CODES READY FOR REAL-WORLD TESTING!')
        self.stdout.write('='*60)
        self.stdout.write(f'Public URL: {public_url}')
        self.stdout.write('📱 Now you can scan QR codes from any device, anywhere!')
        self.stdout.write('🌍 Test from different locations, networks, and devices')
        self.stdout.write('📊 All scans will be tracked with geo-analytics')
        self.stdout.write('\nTo revert back to localhost:')
        self.stdout.write('python manage.py test_qr_public --revert')
        self.stdout.write('='*60)
