"""
Network Utilities for AI Services
Provides functions to check network connectivity and API availability
"""
import socket
import requests
import logging
import time
import os
from typing import Dict, Any, Tu<PERSON>, Optional

logger = logging.getLogger(__name__)

# Constants
DEFAULT_TIMEOUT = 5  # seconds
CONNECTIVITY_CACHE_TIME = 60  # seconds

# Cache for connectivity checks to avoid repeated checks
_connectivity_cache = {}


def check_internet_connection(timeout: int = DEFAULT_TIMEOUT) -> bool:
    """
    Check if there is an active internet connection

    Args:
        timeout: Timeout in seconds

    Returns:
        True if connected, False otherwise
    """
    # Check if we should force offline mode
    if os.environ.get('USE_LOCAL_MODE', 'false').lower() in ('true', '1', 'yes'):
        logger.info("Forcing offline mode due to USE_LOCAL_MODE=true environment variable")
        return False

    # Check cache first
    cache_key = f"internet_connection_{timeout}"
    cache_entry = _connectivity_cache.get(cache_key)
    if cache_entry:
        timestamp, result = cache_entry
        if time.time() - timestamp < CONNECTIVITY_CACHE_TIME:
            return result

    # Try to connect to a reliable host
    hosts = [
        ("*******", 53),  # Google DNS
        ("*******", 53),  # Cloudflare DNS
        ("**************", 53),  # OpenDNS
    ]

    for host, port in hosts:
        try:
            socket.setdefaulttimeout(timeout)
            socket.socket(socket.AF_INET, socket.SOCK_STREAM).connect((host, port))
            # Cache the result
            _connectivity_cache[cache_key] = (time.time(), True)
            return True
        except Exception as e:
            logger.debug(f"Failed to connect to {host}:{port} - {str(e)}")
            continue

    # Cache the negative result
    _connectivity_cache[cache_key] = (time.time(), False)
    return False


def check_api_availability(url: str, timeout: int = DEFAULT_TIMEOUT) -> Tuple[bool, Optional[str]]:
    """
    Check if an API endpoint is available

    Args:
        url: The API URL to check
        timeout: Timeout in seconds

    Returns:
        Tuple of (is_available, error_message)
    """
    # Check cache first
    cache_key = f"api_availability_{url}_{timeout}"
    cache_entry = _connectivity_cache.get(cache_key)
    if cache_entry:
        timestamp, result = cache_entry
        if time.time() - timestamp < CONNECTIVITY_CACHE_TIME:
            return result

    # First check internet connection
    if not check_internet_connection(timeout):
        result = (False, "No internet connection")
        _connectivity_cache[cache_key] = (time.time(), result)
        return result

    # Try to connect to the API
    try:
        response = requests.head(url, timeout=timeout)
        if response.status_code < 500:  # Accept any non-server error status
            result = (True, None)
            _connectivity_cache[cache_key] = (time.time(), result)
            return result
        else:
            result = (False, f"API returned status code {response.status_code}")
            _connectivity_cache[cache_key] = (time.time(), result)
            return result
    except requests.exceptions.RequestException as e:
        result = (False, f"Failed to connect to API: {str(e)}")
        _connectivity_cache[cache_key] = (time.time(), result)
        return result


def check_api_provider_availability(provider: str, timeout: int = DEFAULT_TIMEOUT) -> Tuple[bool, Optional[str]]:
    """
    Check if a specific AI provider's API is available

    Args:
        provider: The provider name ('mistral', 'openai', etc.)
        timeout: Timeout in seconds

    Returns:
        Tuple of (is_available, error_message)
    """
    # Check if we should force offline mode
    if os.environ.get('USE_LOCAL_MODE', 'false').lower() in ('true', '1', 'yes'):
        if provider.lower() == 'offline':
            return True, None  # Offline provider is always available in offline mode
        logger.info(f"Forcing offline mode due to USE_LOCAL_MODE=true environment variable, {provider} is unavailable")
        return False, "Offline mode is enabled"

    provider_urls = {
        'mistral': 'https://api.mistral.ai/v1/models',  # Updated to use models endpoint which is more reliable
        'openai': 'https://api.openai.com/v1/models',
        'groq': 'https://api.groq.com/openai/v1/models',  # Groq API models endpoint
        'local': 'http://localhost:8001/health',  # Assuming local AI engine has a health endpoint
        'offline': None  # Special case for offline provider
    }

    # Special case for offline provider
    if provider.lower() == 'offline':
        return True, None  # Offline provider is always available

    url = provider_urls.get(provider.lower())
    if not url:
        return False, f"Unknown provider: {provider}"

    return check_api_availability(url, timeout)


# Global provider status cache
_provider_status_cache = {}

def check_provider_health(provider: str) -> Dict[str, Any]:
    """
    Check the health of a specific provider and update the status cache

    Args:
        provider: The provider name

    Returns:
        Dictionary with provider status information
    """
    start_time = time.time()
    available, error_msg = check_api_provider_availability(provider)
    response_time = time.time() - start_time

    status = {
        'available': available,
        'error': error_msg,
        'last_check': time.time(),
        'response_time': response_time
    }

    # Update the cache
    _provider_status_cache[provider] = status

    return status

def get_all_provider_status() -> Dict[str, Dict[str, Any]]:
    """
    Get the status of all AI providers

    Returns:
        Dictionary with provider status information
    """
    providers = ['mistral', 'openai', 'groq', 'local', 'offline']
    status = {}

    for provider in providers:
        # Check if we have a cached status
        if provider in _provider_status_cache:
            # Use cached status if it's recent (less than 5 minutes old)
            cached_status = _provider_status_cache[provider]
            if time.time() - cached_status['last_check'] < 300:  # 5 minutes
                status[provider] = cached_status
                continue

        # Otherwise, check the provider health
        status[provider] = check_provider_health(provider)

    return status


def check_mistral_connectivity(timeout: int = DEFAULT_TIMEOUT, force_refresh: bool = False) -> Dict[str, Any]:
    """
    Check connectivity to the Mistral API and log the result

    Args:
        timeout: Timeout in seconds
        force_refresh: Whether to force a refresh of the cache

    Returns:
        Dictionary with connectivity information
    """
    # Use the models endpoint as specified in the prompt
    mistral_models_url = 'https://api.mistral.ai/v1/models'

    # Log the URL being checked
    logger.debug(f"[MISTRAL_DEBUG] Checking connectivity to: {mistral_models_url}")

    # Get API key from settings
    from ai_services.settings import MISTRAL_API_KEY
    api_key = MISTRAL_API_KEY

    # Prepare headers with API key
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }

    # Log the headers (without showing the full API key)
    if api_key:
        logger.debug(f"[MISTRAL_DEBUG] Using API key: {api_key[:4]}...{api_key[-4:]}")

    # Cache key for this specific check
    cache_key = f"mistral_connectivity_{timeout}"

    # Check if we should use cached result
    if not force_refresh and cache_key in _connectivity_cache:
        timestamp, result = _connectivity_cache.get(cache_key)
        if time.time() - timestamp < CONNECTIVITY_CACHE_TIME:
            return result

    # Start timing
    start_time = time.time()

    try:
        # Make the request
        response = requests.get(
            mistral_models_url,
            headers=headers,
            timeout=timeout
        )

        # Calculate response time
        response_time = time.time() - start_time

        # Check if the request was successful
        if response.status_code == 200:
            try:
                # Parse the response to get available models
                models_data = response.json()

                # Log the response for debugging
                logger.debug(f"[MISTRAL_DEBUG] Response: {models_data}")

                # Handle different response formats
                available_models = []

                # Format 1: { "data": [ {"id": "model1"}, {"id": "model2"} ] }
                if 'data' in models_data and isinstance(models_data['data'], list):
                    available_models = [model.get('id') for model in models_data.get('data', []) if model.get('id')]

                # Format 2: { "models": [ "model1", "model2" ] }
                elif 'models' in models_data and isinstance(models_data['models'], list):
                    available_models = models_data['models']

                # Format 3: [ {"id": "model1"}, {"id": "model2"} ]
                elif isinstance(models_data, list):
                    available_models = [model.get('id') for model in models_data if model.get('id')]

                # Create result
                result = {
                    'available': True,
                    'status_code': response.status_code,
                    'response_time': response_time,
                    'timestamp': time.time(),
                    'available_models': available_models,
                    'error': None
                }

                # Log success
                logger.info(f"Mistral API is reachable. Response time: {response_time:.2f}s")
                logger.info(f"Available models: {', '.join(available_models) if available_models else 'None found'}")
            except Exception as parse_error:
                # Handle JSON parsing errors
                logger.error(f"Error parsing Mistral API response: {str(parse_error)}")
                logger.debug(f"[MISTRAL_DEBUG] Raw response: {response.text[:500]}...")

                # Create result for parsing error
                result = {
                    'available': True,  # Still mark as available since the API responded
                    'status_code': response.status_code,
                    'response_time': response_time,
                    'timestamp': time.time(),
                    'available_models': [],
                    'error': f"Error parsing response: {str(parse_error)}"
                }
        else:
            # Create result for unsuccessful response
            result = {
                'available': False,
                'status_code': response.status_code,
                'response_time': response_time,
                'timestamp': time.time(),
                'available_models': [],
                'error': f"API returned status code {response.status_code}"
            }

            # Log failure
            logger.error(f"Mistral API check failed with status code {response.status_code}. Response time: {response_time:.2f}s")

            # Log response body for debugging
            try:
                logger.debug(f"[MISTRAL_DEBUG] Error response body: {response.text[:500]}...")
            except:
                logger.debug("[MISTRAL_DEBUG] Could not extract response body")

    except requests.exceptions.RequestException as e:
        # Calculate response time even for failures
        response_time = time.time() - start_time

        # Create result for exception
        result = {
            'available': False,
            'status_code': None,
            'response_time': response_time,
            'timestamp': time.time(),
            'available_models': [],
            'error': str(e)
        }

        # Log failure with exception details
        logger.error(f"Mistral API connectivity check failed: {str(e)}. Response time: {response_time:.2f}s")

    # Cache the result
    _connectivity_cache[cache_key] = (time.time(), result)

    return result


def get_best_available_provider(preferred_order: list = None) -> str:
    """
    Get the best available AI provider based on connectivity checks

    Args:
        preferred_order: List of providers in order of preference

    Returns:
        The name of the best available provider
    """
    # Check if we should force offline mode
    if os.environ.get('USE_LOCAL_MODE', 'false').lower() in ('true', '1', 'yes'):
        logger.info("Forcing offline mode due to USE_LOCAL_MODE=true environment variable")
        return 'offline'

    if preferred_order is None:
        preferred_order = ['local', 'groq', 'mistral', 'openai', 'offline']

    for provider in preferred_order:
        available, _ = check_api_provider_availability(provider)
        if available:
            logger.info(f"Selected {provider} as the best available provider")
            return provider

    # If no provider is available, return 'offline' as a fallback
    logger.warning("No AI providers are available, falling back to offline mode")
    return 'offline'
