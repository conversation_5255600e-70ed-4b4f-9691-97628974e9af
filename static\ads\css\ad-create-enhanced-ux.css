/**
 * Enhanced UX Styles for Ad Creation Page
 * Designed to create a highly convertible, user-friendly experience
 * Version: 1.0.0
 */

/* ==========================================================================
   ANIMATIONS & TRANSITIONS
   ========================================================================== */

.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.fade-in {
    animation: fadeIn 0.6s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* ==========================================================================
   HEADER ENHANCEMENTS
   ========================================================================== */

.welcome-stats {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 15px;
    backdrop-filter: blur(10px);
}

.stat-item {
    padding: 10px;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: #fff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-label {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.8);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ==========================================================================
   STEP WELCOME CARDS
   ========================================================================== */

.step-welcome-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    margin-bottom: 25px;
}

.step-icon {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 1.2rem;
}

.step-content h5 {
    color: white;
    margin-bottom: 5px;
}

/* ==========================================================================
   ENHANCED FORM ELEMENTS
   ========================================================================== */

.form-group-enhanced {
    position: relative;
    margin-bottom: 2rem;
}

.form-label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    font-size: 0.95rem;
}

.required-asterisk {
    color: #e74c3c;
    margin-left: 3px;
    font-weight: bold;
}

.input-wrapper {
    position: relative;
}

.enhanced-input {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.enhanced-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

.enhanced-input:valid {
    border-color: #27ae60;
}

.input-feedback {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 5px;
    font-size: 0.8rem;
}

.character-count {
    color: #6c757d;
}

.character-count.warning {
    color: #f39c12;
}

.character-count.danger {
    color: #e74c3c;
}

.input-status {
    font-weight: 500;
}

.input-status.success {
    color: #27ae60;
}

.input-status.error {
    color: #e74c3c;
}

.form-help {
    background: #f8f9fa;
    border-left: 4px solid #667eea;
    padding: 10px 15px;
    margin-top: 8px;
    border-radius: 0 6px 6px 0;
    font-size: 0.85rem;
    color: #495057;
}

/* ==========================================================================
   SMART ENGINE FEATURE CARD
   ========================================================================== */

.smart-engine-feature-card {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border-radius: 15px;
    padding: 25px;
    color: white;
    box-shadow: 0 10px 30px rgba(240, 147, 251, 0.3);
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
}

.smart-engine-feature-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.feature-header {
    margin-bottom: 15px;
}

.feature-title {
    font-size: 1.1rem;
    font-weight: 600;
}

.feature-title .badge {
    background: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
    font-size: 0.7rem;
    padding: 4px 8px;
}

.enhanced-switch {
    width: 60px !important;
    height: 30px !important;
    background-color: rgba(255, 255, 255, 0.3) !important;
    border: none !important;
}

.enhanced-switch:checked {
    background-color: rgba(255, 255, 255, 0.9) !important;
}

.feature-description p {
    margin-bottom: 15px;
    font-size: 0.95rem;
    line-height: 1.5;
}

.feature-benefits {
    margin: 15px 0;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.85rem;
    font-weight: 500;
    margin-bottom: 8px;
}

.benefit-item i {
    width: 16px;
    text-align: center;
}

.feature-pricing {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
}

.pricing-info {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.price-label {
    font-size: 0.85rem;
    opacity: 0.9;
}

.price-amount {
    font-size: 1.1rem;
    font-weight: bold;
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 12px;
    border-radius: 20px;
}

.price-note {
    font-size: 0.75rem;
    opacity: 0.8;
    font-style: italic;
}

.status-indicator .badge {
    font-size: 0.8rem;
    padding: 6px 12px;
    border-radius: 20px;
}

/* ==========================================================================
   TOOLTIPS ENHANCEMENT
   ========================================================================== */

.tippy-box {
    background: #2c3e50;
    color: white;
    border-radius: 8px;
    font-size: 0.85rem;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.tippy-arrow {
    color: #2c3e50;
}

/* ==========================================================================
   RESPONSIVE DESIGN
   ========================================================================== */

@media (max-width: 768px) {
    .welcome-stats .row {
        text-align: center;
    }

    .stat-item {
        margin-bottom: 10px;
    }

    .feature-benefits .row {
        text-align: center;
    }

    .benefit-item {
        justify-content: center;
        margin-bottom: 12px;
    }

    .pricing-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .smart-engine-feature-card {
        padding: 20px;
    }
}

/* ==========================================================================
   LOADING STATES
   ========================================================================== */

.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.btn-loading {
    position: relative;
    pointer-events: none;
}

.btn-loading .loading-spinner {
    margin-right: 8px;
}

/* ==========================================================================
   ENHANCED AI TOGGLER
   ========================================================================== */

.ai-toggle-container {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 8px;
}

.ai-toggle-input {
    display: none;
}

.ai-toggle-label {
    cursor: pointer;
    display: block;
    position: relative;
}

.ai-toggle-slider {
    width: 80px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    position: relative;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    overflow: hidden;
}

.ai-toggle-track {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 8px;
    font-size: 0.6rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.7);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.toggle-text-off {
    opacity: 1;
    transition: opacity 0.3s ease;
}

.toggle-text-on {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.ai-toggle-thumb {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    position: absolute;
    top: 2px;
    left: 2px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 2;
}

.toggle-icon {
    color: white;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

/* Checked state */
.ai-toggle-input:checked + .ai-toggle-label .ai-toggle-slider {
    background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 0 20px rgba(0, 184, 148, 0.4);
}

.ai-toggle-input:checked + .ai-toggle-label .ai-toggle-thumb {
    transform: translateX(40px);
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.ai-toggle-input:checked + .ai-toggle-label .toggle-icon {
    color: #00b894;
    transform: scale(1.1);
}

.ai-toggle-input:checked + .ai-toggle-label .toggle-text-off {
    opacity: 0;
}

.ai-toggle-input:checked + .ai-toggle-label .toggle-text-on {
    opacity: 1;
    color: rgba(255, 255, 255, 0.9);
}

/* Disabled state */
.ai-toggle-input:disabled + .ai-toggle-label {
    cursor: not-allowed;
    opacity: 0.5;
}

.ai-toggle-input:disabled + .ai-toggle-label .ai-toggle-slider {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
}

.ai-toggle-input:disabled + .ai-toggle-label .ai-toggle-thumb {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
}

.ai-toggle-input:disabled + .ai-toggle-label .toggle-icon {
    color: rgba(255, 255, 255, 0.5);
}

/* Status indicator */
.ai-toggle-status {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
    font-size: 0.75rem;
}

.status-text {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #00b894;
    animation: statusPulse 2s infinite;
}

.status-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.7rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

@keyframes statusPulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.6;
        transform: scale(1.2);
    }
}

/* Hover effects */
.ai-toggle-label:hover .ai-toggle-slider {
    transform: scale(1.02);
    box-shadow: 0 0 25px rgba(255, 255, 255, 0.2);
}

.ai-toggle-label:hover .ai-toggle-thumb {
    transform: translateX(2px) scale(1.05);
}

.ai-toggle-input:checked + .ai-toggle-label:hover .ai-toggle-thumb {
    transform: translateX(38px) scale(1.05);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .ai-toggle-container {
        align-items: center;
    }

    .ai-toggle-status {
        align-items: center;
        text-align: center;
    }

    .ai-toggle-slider {
        width: 70px;
        height: 36px;
    }

    .ai-toggle-thumb {
        width: 28px;
        height: 28px;
    }

    .ai-toggle-input:checked + .ai-toggle-label .ai-toggle-thumb {
        transform: translateX(34px);
    }
}

/* ==========================================================================
   ENHANCED AD PREVIEW SECTION
   ========================================================================== */

.preview-panel-enhanced {
    position: sticky;
    top: 20px;
    z-index: 10;
}

.preview-panel-enhanced .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: none;
}

.preview-panel-enhanced .card-title {
    color: white !important;
    margin: 0;
    font-weight: 600;
}

.preview-controls {
    display: flex;
    gap: 8px;
}

.preview-controls .btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    transition: all 0.3s ease;
}

.preview-controls .btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
    transform: scale(1.05);
}

.preview-location-select {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 10px 12px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.preview-location-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.ad-preview-container-enhanced {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    margin: 15px 0;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.preview-location-badge {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    margin-bottom: 15px;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.ad-preview-wrapper-enhanced {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 2px solid #e9ecef;
    width: 300px;
    height: 250px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    position: relative;
}

.ad-preview-wrapper-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    border-color: #667eea;
}

/* Ad Title Section */
.ad-preview-title-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 8px 12px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 40px;
    max-height: 40px;
    overflow: hidden;
    flex-shrink: 0;
}

.ad-preview-title-enhanced {
    font-size: 0.9rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
    line-height: 1.2;
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    max-width: 70%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.ad-preview-meta {
    display: flex;
    align-items: center;
}

.preview-badge {
    background: #ffc107;
    color: #212529;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 0.6rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

/* Ad Content Section */
.ad-preview-content-section {
    padding: 8px 12px;
    flex: 1;
    display: flex;
    align-items: flex-start;
    overflow: hidden;
    min-height: 60px;
    max-height: 80px;
}

.ad-preview-content-enhanced {
    font-size: 0.75rem;
    line-height: 1.3;
    color: #495057;
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    width: 100%;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
}

/* Ad Image Section */
.ad-preview-image-section {
    position: relative;
    background: #f8f9fa;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    min-height: 80px;
    max-height: 100px;
}

.ad-preview-image-enhanced {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(102, 126, 234, 0.8);
    color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
}

.ad-preview-image-enhanced:hover .image-overlay {
    opacity: 1;
}

.image-overlay i {
    font-size: 2rem;
    margin-bottom: 8px;
}

/* Call to Action Section */
.ad-preview-cta-section {
    padding: 8px 12px;
    text-align: center;
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
    flex-shrink: 0;
    min-height: 40px;
    max-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ad-preview-cta-enhanced {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 6px 16px;
    border-radius: 15px;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.7rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    white-space: nowrap;
}

.ad-preview-cta-enhanced:hover {
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* Preview Information Panel */
.preview-info-panel {
    margin-top: 15px;
    background: white;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #dee2e6;
}

.preview-stats {
    display: flex;
    justify-content: space-between;
    gap: 15px;
}

.stat-item {
    text-align: center;
    flex: 1;
}

.stat-label {
    display: block;
    font-size: 0.75rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
}

.stat-value {
    display: block;
    font-size: 0.9rem;
    font-weight: 600;
    color: #2c3e50;
}

/* Preview Tips */
.preview-tips .alert {
    border: none;
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    color: #1565c0;
    border-radius: 8px;
    font-size: 0.85rem;
}

/* Location-specific preview styles */
.ad-preview-wrapper-enhanced.header-preview {
    border-radius: 0;
    width: 300px;
    height: 90px;
}

.ad-preview-wrapper-enhanced.sidebar-preview {
    width: 300px;
    height: 250px;
    margin: 0 auto;
}

.ad-preview-wrapper-enhanced.content-preview {
    border-radius: 4px;
    width: 300px;
    height: 60px;
}

.ad-preview-wrapper-enhanced.footer-preview {
    border-radius: 0;
    width: 300px;
    height: 90px;
}

.ad-preview-wrapper-enhanced.popup-preview {
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
    border: 3px solid #667eea;
    width: 300px;
    height: 250px;
}

/* Responsive design for preview */
@media (max-width: 768px) {
    .ad-preview-container-enhanced {
        padding: 15px;
    }

    .ad-preview-title-section {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .ad-preview-title-enhanced {
        max-width: 100%;
    }

    .preview-stats {
        flex-direction: column;
        gap: 10px;
    }

    .stat-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .stat-label,
    .stat-value {
        display: inline;
    }
}

/* Content update animation */
.content-updated {
    animation: contentUpdate 0.3s ease-out;
}

@keyframes contentUpdate {
    0% {
        background-color: rgba(102, 126, 234, 0.1);
        transform: scale(1.02);
    }
    100% {
        background-color: transparent;
        transform: scale(1);
    }
}

/* ==========================================================================
   ENHANCED CONTENT SECTION (STEP 2)
   ========================================================================== */

/* Media Upload Container */
.media-upload-container {
    margin-top: 8px;
}

.upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 12px;
    padding: 40px 20px;
    text-align: center;
    background: #f8f9fa;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.upload-area:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
    transform: translateY(-2px);
}

.upload-area.dragover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.1);
    transform: scale(1.02);
}

.upload-icon {
    font-size: 3rem;
    color: #6c757d;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.upload-area:hover .upload-icon {
    color: #667eea;
    transform: scale(1.1);
}

.upload-text h6 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 5px;
}

.upload-preview {
    border-radius: 12px;
    overflow: hidden;
    position: relative;
    background: #f8f9fa;
    border: 2px solid #dee2e6;
}

.preview-img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    display: block;
}

.preview-actions {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 6px;
    padding: 5px;
}

/* ==========================================================================
   ENHANCED OPTIONS SECTION (STEP 3)
   ========================================================================== */

/* Premium Feature Card */
.premium-feature-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 25px;
    color: white;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
}

.premium-feature-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: shimmer 4s infinite;
}

.feature-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.feature-description {
    font-size: 0.9rem;
    line-height: 1.5;
    opacity: 0.9;
}

.feature-benefits {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;
}

.benefit-tag {
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 10px;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
}

.feature-pricing {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
}

.pricing-details {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.price-label {
    font-size: 0.85rem;
    opacity: 0.9;
}

.price-amount {
    font-size: 1.2rem;
    font-weight: bold;
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 12px;
    border-radius: 20px;
}

.price-note {
    font-size: 0.75rem;
    opacity: 0.8;
    font-style: italic;
}

/* Premium Toggle */
.premium-toggle-container {
    display: flex;
    align-items: center;
}

.premium-toggle-input {
    display: none;
}

.premium-toggle-label {
    cursor: pointer;
    display: block;
}

.premium-toggle-slider {
    width: 60px;
    height: 30px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    position: relative;
    transition: all 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.4);
}

.premium-toggle-thumb {
    width: 24px;
    height: 24px;
    background: white;
    border-radius: 50%;
    position: absolute;
    top: 1px;
    left: 1px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    color: #667eea;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.premium-toggle-input:checked + .premium-toggle-label .premium-toggle-slider {
    background: rgba(255, 255, 255, 0.9);
    border-color: white;
}

.premium-toggle-input:checked + .premium-toggle-label .premium-toggle-thumb {
    transform: translateX(30px);
    background: #00b894;
    color: white;
}

/* Enhanced Pricing Summary */
.pricing-summary-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    border: 2px solid #e9ecef;
}

.pricing-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #dee2e6;
}

.pricing-badge {
    background: #28a745;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
    animation: pulse 2s infinite;
}

.pricing-body {
    padding: 25px;
}

.pricing-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f1f3f4;
}

.pricing-item:last-child {
    border-bottom: none;
}

.pricing-label {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    color: #495057;
}

.pricing-value {
    font-weight: 600;
    color: #2c3e50;
}

.pricing-divider {
    height: 2px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 20px 0;
    border-radius: 2px;
}

.pricing-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
}

.total-label {
    display: flex;
    align-items: center;
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
}

.total-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #667eea;
}

.roi-indicator {
    text-align: center;
    padding: 10px;
    background: rgba(40, 167, 69, 0.1);
    border-radius: 8px;
    margin-top: 15px;
    font-size: 0.9rem;
    color: #155724;
}

/* ==========================================================================
   ENHANCED REVIEW SECTION (STEP 4)
   ========================================================================== */

/* Review Summary Card */
.review-summary-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    border: 2px solid #e9ecef;
}

.summary-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.summary-badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 6px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.summary-body {
    padding: 25px;
}

.review-item {
    display: flex;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #f1f3f4;
    transition: all 0.3s ease;
}

.review-item:hover {
    background: rgba(102, 126, 234, 0.05);
    border-radius: 8px;
    padding-left: 10px;
    padding-right: 10px;
}

.review-item:last-child {
    border-bottom: none;
}

.review-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 1.1rem;
}

.review-content {
    flex: 1;
}

.review-label {
    font-size: 0.85rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
}

.review-value {
    font-size: 1rem;
    font-weight: 500;
    color: #2c3e50;
    line-height: 1.4;
}

.content-preview {
    max-height: 60px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}

.review-status {
    margin-left: 15px;
    font-size: 1.2rem;
}

/* Performance Metrics */
.performance-metrics {
    display: flex;
    justify-content: space-around;
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
}

.metric-item {
    text-align: center;
    flex: 1;
}

.metric-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 10px;
    font-size: 1.2rem;
}

.metric-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 5px;
}

.metric-label {
    font-size: 0.8rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Investment Summary */
.investment-summary {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    margin-top: 20px;
}

.investment-header h6 {
    color: white;
    margin-bottom: 10px;
}

.investment-amount {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 10px;
}

.investment-note {
    font-size: 0.85rem;
    opacity: 0.9;
}

/* Confidence Indicators */
.confidence-indicators {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
}

.confidence-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 15px;
}

.confidence-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    color: white;
}

.confidence-icon.success {
    background: #28a745;
}

.confidence-text strong {
    display: block;
    color: #2c3e50;
    margin-bottom: 2px;
}

.confidence-text small {
    color: #6c757d;
    font-size: 0.8rem;
}

/* Mobile Responsiveness for Enhanced Sections */
@media (max-width: 768px) {
    .premium-feature-card {
        padding: 20px 15px;
    }

    .feature-benefits {
        justify-content: center;
    }

    .pricing-details {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .performance-metrics {
        flex-direction: column;
        gap: 20px;
    }

    .confidence-item {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .upload-area {
        padding: 30px 15px;
    }

    .upload-icon {
        font-size: 2rem;
    }
}

/* ==========================================================================
   ENHANCED GENERATE BUTTON
   ========================================================================== */

.generate-suggestions-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    text-align: center;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.btn-gradient-primary {
    background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
    border: none;
    border-radius: 12px;
    padding: 15px 30px;
    font-weight: 600;
    color: white;
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-gradient-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(255, 107, 107, 0.5);
    background: linear-gradient(135deg, #ff5252 0%, #ffc107 100%);
}

.btn-gradient-primary:active {
    transform: translateY(-1px);
}

.btn-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-subtitle {
    margin-top: 5px;
    opacity: 0.9;
    font-size: 0.75rem;
}

.generate-btn-enhanced {
    position: relative;
    overflow: hidden;
}

.generate-btn-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.generate-btn-enhanced:hover::before {
    left: 100%;
}

.generation-benefits {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 15px;
}

.benefit-mini {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    font-size: 0.8rem;
    font-weight: 500;
}

.benefit-mini i {
    font-size: 1.2rem;
    margin-bottom: 3px;
}

/* ==========================================================================
   ENHANCED AI SUGGESTIONS
   ========================================================================== */

.ai-suggestions-enhanced {
    margin-top: 25px;
}

.suggestions-header {
    text-align: center;
    margin-bottom: 20px;
    padding: 20px;
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    border-radius: 12px;
    color: white;
}

.suggestions-header h5 {
    margin-bottom: 10px;
    font-weight: 600;
}

.suggestions-header p {
    margin: 0;
    opacity: 0.9;
    font-size: 0.9rem;
}

.ai-suggestion-card-enhanced {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.ai-suggestion-card-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: width 0.3s ease;
}

.ai-suggestion-card-enhanced:hover {
    border-color: #667eea;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
    transform: translateY(-2px);
}

.ai-suggestion-card-enhanced:hover::before {
    width: 8px;
}

.suggestion-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 15px;
}

.suggestion-label {
    font-weight: 600;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 8px;
}

.suggestion-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
}

.apply-suggestion-btn-enhanced {
    background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 184, 148, 0.3);
}

.apply-suggestion-btn-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 184, 148, 0.4);
    background: linear-gradient(135deg, #00a085 0%, #00b7b8 100%);
}

.suggestion-title-enhanced {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 10px;
    line-height: 1.3;
}

.suggestion-content-enhanced {
    color: #6c757d;
    line-height: 1.5;
    font-size: 0.95rem;
}

.suggestion-meta {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #f1f3f4;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: #6c757d;
}

.suggestion-provider {
    display: flex;
    align-items: center;
    gap: 5px;
}

.suggestion-quality {
    display: flex;
    align-items: center;
    gap: 5px;
}

.quality-stars {
    color: #ffc107;
}

/* ==========================================================================
   SUCCESS ANIMATIONS
   ========================================================================== */

.success-animation {
    animation: successPulse 0.6s ease-out;
}

@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.checkmark-animation {
    animation: checkmarkDraw 0.8s ease-out;
}

@keyframes checkmarkDraw {
    0% { stroke-dashoffset: 100; }
    100% { stroke-dashoffset: 0; }
}

/* ==========================================================================
   MOBILE OPTIMIZATIONS
   ========================================================================== */

@media (max-width: 768px) {
    .generate-suggestions-section {
        padding: 20px 15px;
        margin: 15px 0;
    }

    .btn-gradient-primary {
        padding: 12px 20px;
        font-size: 0.9rem;
    }

    .generation-benefits .row {
        gap: 10px;
    }

    .ai-suggestion-card-enhanced {
        padding: 15px;
    }

    .suggestion-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .apply-suggestion-btn-enhanced {
        width: 100%;
        margin-top: 10px;
    }
}
