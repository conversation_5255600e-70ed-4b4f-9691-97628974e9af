from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Sum
from datetime import datetime
from django.contrib.auth.models import User

from .models import Transaction, Ad

@login_required
def admin_pending_payments(request):
    """
    View all pending payments for approval (superuser only)
    """
    # Check if user is a superuser
    if not request.user.is_superuser:
        messages.error(request, "You don't have permission to access this page.")
        return redirect('ads:dashboard')

    # Get all paid transactions for approved ads
    paid_transactions = Transaction.objects.filter(
        status='paid',
        ad__status='approved'
    ).order_by('-timestamp')

    # Paginate the results
    paginator = Paginator(paid_transactions, 10)  # Show 10 transactions per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'total_count': paid_transactions.count(),
    }

    return render(request, 'ads/admin_pending_payments.html', context)

@login_required
def admin_approve_payment(request, transaction_id):
    """
    Approve a payment and activate the ad (superuser only)
    """
    # Check if user is a superuser
    if not request.user.is_superuser:
        messages.error(request, "You don't have permission to access this page.")
        return redirect('ads:dashboard')

    # Get the transaction
    transaction = get_object_or_404(Transaction, id=transaction_id)

    # Check if the transaction is paid and the ad is approved
    if transaction.status != 'paid' or transaction.ad.status != 'approved':
        messages.error(request, "This transaction cannot be approved.")
        return redirect('ads:admin_pending_payments')

    # Check if the transaction has already been approved
    if transaction.status == 'approved':
        messages.warning(request, f"Payment for '{transaction.ad.title}' has already been approved.")
        return redirect('ads:admin_pending_payments')

    # Update the transaction status
    transaction.status = 'approved'
    transaction.save()

    # Update the ad status to active
    transaction.ad.status = 'active'
    transaction.ad.save()

    # Create a notification for the user
    from django.urls import reverse
    from notifications.services import NotificationService

    ad_detail_url = reverse('ads:ad_detail', kwargs={'slug': transaction.ad.slug})
    NotificationService.create_notification(
        user=transaction.user,
        title="Payment Approved - Ad Activated",
        message=f"Your payment for '{transaction.ad.title}' has been approved! Your ad is now active and running.",
        notification_type="success",
        category="payment",
        content_object=transaction.ad,
        action_url=ad_detail_url
    )

    messages.success(request, f"Payment for '{transaction.ad.title}' has been approved and the ad is now active. User has been notified.")
    return redirect('ads:admin_pending_payments')

@login_required
def admin_transactions(request):
    """
    View and filter all transactions (superuser only)
    """
    # Check if user is a superuser
    if not request.user.is_superuser:
        messages.error(request, "You don't have permission to access this page.")
        return redirect('ads:dashboard')

    # Get all transactions
    transactions = Transaction.objects.all().order_by('-timestamp')

    # Apply filters
    user_filter = request.GET.get('user')
    ad_filter = request.GET.get('ad')
    status_filter = request.GET.get('status')
    payment_gateway_filter = request.GET.get('payment_gateway')
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    amount_min = request.GET.get('amount_min')
    amount_max = request.GET.get('amount_max')

    # Filter by user
    if user_filter:
        transactions = transactions.filter(user__username__icontains=user_filter)

    # Filter by ad
    if ad_filter:
        transactions = transactions.filter(ad__title__icontains=ad_filter)

    # Filter by status
    if status_filter:
        transactions = transactions.filter(status=status_filter)

    # Filter by payment gateway
    if payment_gateway_filter:
        transactions = transactions.filter(payment_gateway=payment_gateway_filter)

    # Filter by date range
    if date_from:
        try:
            date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
            transactions = transactions.filter(timestamp__date__gte=date_from)
        except ValueError:
            pass

    if date_to:
        try:
            date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
            transactions = transactions.filter(timestamp__date__lte=date_to)
        except ValueError:
            pass

    # Filter by amount range
    if amount_min:
        try:
            amount_min = float(amount_min)
            transactions = transactions.filter(amount__gte=amount_min)
        except ValueError:
            pass

    if amount_max:
        try:
            amount_max = float(amount_max)
            transactions = transactions.filter(amount__lte=amount_max)
        except ValueError:
            pass

    # Calculate totals
    total_transactions = transactions.count()
    total_amount = transactions.aggregate(Sum('amount'))['amount__sum'] or 0

    # Get unique users and ads for filter dropdowns
    unique_users = User.objects.filter(ad_transactions__isnull=False).distinct()
    unique_ads = Ad.objects.filter(transactions__isnull=False).distinct()

    # Paginate the results
    paginator = Paginator(transactions, 10)  # Show 10 transactions per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'total_transactions': total_transactions,
        'total_amount': total_amount,
        'unique_users': unique_users,
        'unique_ads': unique_ads,
        'status_choices': Transaction.STATUS_CHOICES,
        'payment_gateway_choices': Transaction.PAYMENT_GATEWAY_CHOICES,
        'filters': {
            'user': user_filter,
            'ad': ad_filter,
            'status': status_filter,
            'payment_gateway': payment_gateway_filter,
            'date_from': date_from,
            'date_to': date_to,
            'amount_min': amount_min,
            'amount_max': amount_max,
        }
    }

    return render(request, 'ads/admin_transactions.html', context)
