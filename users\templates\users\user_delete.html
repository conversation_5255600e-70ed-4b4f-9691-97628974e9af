{% extends 'base.html' %}
{% load static %}

{% block title %}Delete User | User Management{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'users/css/user_management.css' %}">
{% endblock %}

{% block content %}
<div class="user-delete-container">
    <div class="user-delete-header">
        <div class="d-flex justify-content-between align-items-center">
            <h1 class="section-title">Delete User</h1>
            <div class="user-delete-actions">
                <a href="{% url 'users:user_detail' user_id=user_obj.id %}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-2"></i>Back to User Details
                </a>
            </div>
        </div>
    </div>

    <div class="user-delete-content">
        <div class="card">
            <div class="card-body">
                <div class="delete-warning">
                    <div class="warning-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h3>Are you sure you want to delete this user?</h3>
                    <p>This action cannot be undone. All data associated with this user will be permanently deleted.</p>
                </div>

                <div class="user-summary">
                    <div class="user-avatar">
                        {% if user_obj.profile.profile_image %}
                        <img src="{{ user_obj.profile.profile_image.url }}" alt="{{ user_obj.username }}">
                        {% else %}
                        <div class="default-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        {% endif %}
                    </div>
                    <div class="user-info">
                        <h4>{{ user_obj.get_full_name|default:user_obj.username }}</h4>
                        <p><strong>Username:</strong> {{ user_obj.username }}</p>
                        <p><strong>Email:</strong> {{ user_obj.email|default:"Not provided" }}</p>
                        <p><strong>Role:</strong> {{ user_obj.profile.get_role_display }}</p>
                        <p><strong>Date Joined:</strong> {{ user_obj.date_joined|date:"F d, Y" }}</p>
                    </div>
                </div>

                <form method="post" class="delete-form">
                    {% csrf_token %}
                    <div class="form-actions">
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-2"></i>Delete User
                        </button>
                        <a href="{% url 'users:user_detail' user_id=user_obj.id %}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'users/js/user_management.js' %}"></script>
{% endblock %}
