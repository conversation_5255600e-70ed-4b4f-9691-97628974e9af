/* Role Management Styles */
.list-group-item {
    transition: all 0.2s ease;
}

.list-group-item:hover {
    background-color: #f8f9fa;
}

.list-group-item.active {
    background-color: #3b82f6;
    border-color: #3b82f6;
}

.list-group-item.active h5,
.list-group-item.active p,
.list-group-item.active .badge {
    color: white;
}

.form-check-input:checked {
    background-color: #3b82f6;
    border-color: #3b82f6;
}

.form-check-input:disabled {
    opacity: 0.6;
}

/* Custom role styles */
.custom-role {
    position: relative;
}

.custom-role .role-actions {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    display: none;
}

.custom-role:hover .role-actions {
    display: block;
}

.role-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

/* Permission groups */
.permission-group {
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.permission-group:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

/* Role badges */
.role-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.role-badge-guest {
    background-color: #f3f4f6;
    color: #6b7280;
}

.role-badge-user {
    background-color: #dbeafe;
    color: #2563eb;
}

.role-badge-premium {
    background-color: #d1fae5;
    color: #059669;
}

.role-badge-admin {
    background-color: #fef3c7;
    color: #d97706;
}

.role-badge-superadmin {
    background-color: #fee2e2;
    color: #dc2626;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1.5rem;
    }
}
