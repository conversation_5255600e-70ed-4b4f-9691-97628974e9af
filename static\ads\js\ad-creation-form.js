/**
 * Ad Creation Form JavaScript
 * Handles the form functionality in the ad creation process
 */

document.addEventListener('DOMContentLoaded', function() {
    // Progress bar update
    const progressBar = document.querySelector('.progress-bar');
    const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');

    if (progressBar && tabButtons.length) {
        tabButtons.forEach(button => {
            button.addEventListener('shown.bs.tab', function(e) {
                const targetId = e.target.getAttribute('id');
                let progress = 25;

                switch(targetId) {
                    case 'step1-tab':
                        progress = 25;
                        break;
                    case 'step2-tab':
                        progress = 50;
                        break;
                    case 'step3-tab':
                        progress = 75;
                        break;
                    case 'step4-tab':
                        progress = 100;
                        break;
                }

                progressBar.style.width = progress + '%';
                progressBar.setAttribute('aria-valuenow', progress);
            });
        });
    }

    // Handle next/prev buttons
    const nextButtons = document.querySelectorAll('.next-step');
    const prevButtons = document.querySelectorAll('.prev-step');

    nextButtons.forEach(button => {
        button.addEventListener('click', function() {
            const nextTabId = this.getAttribute('data-next');
            const nextTab = document.getElementById(nextTabId);
            if (nextTab) {
                nextTab.click();
            }
        });
    });

    prevButtons.forEach(button => {
        button.addEventListener('click', function() {
            const prevTabId = this.getAttribute('data-prev');
            const prevTab = document.getElementById(prevTabId);
            if (prevTab) {
                prevTab.click();
            }
        });
    });

    // Duration handling
    const durationOption = document.getElementById('durationOption');
    const customDurationFields = document.getElementById('customDurationFields');
    const calculatedEndTime = document.getElementById('calculatedEndTime');
    const endDateTimeDisplay = document.getElementById('endDateTimeDisplay');
    const startDate = document.getElementById('startDate');
    const startTime = document.getElementById('startTime');
    const endDate = document.getElementById('endDate');
    const endTime = document.getElementById('endTime');

    if (startDate && startTime) {
        // Set default start date to today
        const today = new Date();
        const formattedDate = today.toISOString().split('T')[0];
        startDate.value = formattedDate;

        // Set default start time to current time
        const hours = String(today.getHours()).padStart(2, '0');
        const minutes = String(today.getMinutes()).padStart(2, '0');
        startTime.value = `${hours}:${minutes}`;
    }

    if (durationOption && customDurationFields) {
        durationOption.addEventListener('change', function() {
            if (this.value === 'custom') {
                // Set default end date to tomorrow
                const tomorrow = new Date(startDate.value);
                tomorrow.setDate(tomorrow.getDate() + 1);
                endDate.value = tomorrow.toISOString().split('T')[0];
                endTime.value = startTime.value;

                customDurationFields.style.display = 'flex';
                updateCalculatedEndTime(); // Still calculate end time for custom
                calculatedEndTime.style.display = 'block';
            } else {
                customDurationFields.style.display = 'none';
                updateCalculatedEndTime();
                calculatedEndTime.style.display = 'block';
            }
        });
    }

    if (startDate && startTime && endDate && endTime) {
        [startDate, startTime, endDate, endTime].forEach(el => {
            el.addEventListener('change', function() {
                updateCalculatedEndTime();
                // If changing start date/time and custom is selected, update end date/time to maintain at least 1 day
                if ((this === startDate || this === startTime) && durationOption.value === 'custom') {
                    const start = new Date(`${startDate.value}T${startTime.value}`);
                    const end = new Date(`${endDate.value}T${endTime.value}`);

                    // If end date is before or equal to start date, set it to start date + 1 day
                    if (end <= start) {
                        const newEnd = new Date(start);
                        newEnd.setDate(newEnd.getDate() + 1);
                        endDate.value = newEnd.toISOString().split('T')[0];
                        endTime.value = startTime.value;
                        updateCalculatedEndTime();
                    }
                }
            });
        });
    }

    // Calculate end time based on duration
    function updateCalculatedEndTime() {
        if (!startDate || !startDate.value || !startTime || !startTime.value) return;

        const start = new Date(`${startDate.value}T${startTime.value}`);
        let durationDays = 7; // Default
        let end;

        if (durationOption.value === 'custom' && endDate.value && endTime.value) {
            // For custom duration, use the selected end date/time
            end = new Date(`${endDate.value}T${endTime.value}`);

            // Calculate duration for pricing
            const diffTime = Math.abs(end - start);
            durationDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            // Ensure minimum 1 day
            if (durationDays < 1) {
                durationDays = 1;
                // Set end date to tomorrow
                end = new Date(start);
                end.setDate(end.getDate() + 1);
                endDate.value = end.toISOString().split('T')[0];
            }

            // Add 2 hours bonus
            end.setHours(end.getHours() + 2);
        } else {
            // For standard durations
            switch(durationOption.value) {
                case '7days':
                    durationDays = 7;
                    break;
                case '2weeks':
                    durationDays = 14;
                    break;
                case 'monthly':
                    durationDays = 30;
                    break;
                case 'yearly':
                    durationDays = 365;
                    break;
            }

            // Add duration days plus 2 hours bonus
            end = new Date(start);
            end.setDate(end.getDate() + durationDays);
            end.setHours(end.getHours() + 2);
        }

        // Format for display
        const options = {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };
        
        if (endDateTimeDisplay) {
            endDateTimeDisplay.textContent = end.toLocaleDateString('en-US', options);
        }

        // Update duration days for pricing
        const durationDaysElement = document.getElementById('durationDays');
        if (durationDaysElement) {
            durationDaysElement.textContent = durationDays + ' days';
        }
        
        // Update pricing if function exists
        if (typeof updatePricing === 'function') {
            updatePricing();
        }
    }

    // Make updateCalculatedEndTime available globally
    window.updateCalculatedEndTime = updateCalculatedEndTime;

    // Initialize end time calculation
    if (startDate && startTime) {
        updateCalculatedEndTime();
    }
});
