/**
 * Enhanced UX JavaScript for Ad Creation Page
 * Provides advanced interactions, tooltips, and animations
 * Version: 1.0.0
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Enhanced UX JavaScript loaded');

    // Initialize all enhanced features
    initTooltips();
    initCharacterCounters();
    initInputValidation();
    initAIToggleEnhancements();
    initProgressTracking();
    initAnimations();
    initFormEnhancements();
    initEnhancedPreview();
    initEnhancedUpload();
    initContentCharacterCounter();
});

/**
 * Initialize Tippy.js tooltips
 */
function initTooltips() {
    if (typeof tippy === 'undefined') {
        console.warn('Tippy.js not loaded, skipping tooltip initialization');
        return;
    }

    // Initialize tooltips for elements with data-tippy-content
    tippy('[data-tippy-content]', {
        theme: 'custom',
        animation: 'scale',
        duration: [300, 200],
        arrow: true,
        placement: 'top',
        interactive: true,
        maxWidth: 300,
    });

    // Add custom tooltips for specific elements
    const aiToggle = document.getElementById('useSmartEngine');
    if (aiToggle) {
        tippy(aiToggle.closest('.ai-toggle-container'), {
            content: 'Toggle AI Smart Engine to generate professional, high-converting ad content automatically',
            theme: 'custom',
            placement: 'bottom',
            arrow: true,
        });
    }

    console.log('Tooltips initialized');
}

/**
 * Initialize character counters for input fields
 */
function initCharacterCounters() {
    const titleInput = document.getElementById('adTitle');
    if (titleInput) {
        const counter = titleInput.parentElement.querySelector('.character-count');
        const statusSpan = titleInput.parentElement.querySelector('.input-status');

        if (counter) {
            titleInput.addEventListener('input', function() {
                const length = this.value.length;
                const maxLength = this.getAttribute('maxlength') || 60;

                counter.textContent = `${length}/${maxLength}`;

                // Update counter color based on length
                counter.classList.remove('warning', 'danger');
                if (length > maxLength * 0.8) {
                    counter.classList.add('warning');
                }
                if (length >= maxLength) {
                    counter.classList.add('danger');
                }

                // Update status with specific character thresholds
                if (statusSpan) {
                    statusSpan.classList.remove('success', 'error');
                    if (length >= 5 && length <= 10) {
                        statusSpan.classList.add('success');
                        statusSpan.textContent = '✓ Good length';
                    } else if (length > 10) {
                        statusSpan.classList.add('error');
                        statusSpan.textContent = '⚠ Too long';
                    } else {
                        statusSpan.textContent = '';
                    }
                }
            });
        }
    }

    console.log('Character counters initialized');
}

/**
 * Initialize input validation with visual feedback
 */
function initInputValidation() {
    const inputs = document.querySelectorAll('.enhanced-input');

    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateInput(this);
        });

        input.addEventListener('input', function() {
            // Clear validation state on input
            this.classList.remove('is-valid', 'is-invalid');
        });
    });

    function validateInput(input) {
        const value = input.value.trim();
        const isRequired = input.hasAttribute('required');

        if (isRequired && !value) {
            input.classList.add('is-invalid');
            input.classList.remove('is-valid');
        } else if (value) {
            input.classList.add('is-valid');
            input.classList.remove('is-invalid');
        }
    }

    console.log('Input validation initialized');
}

/**
 * Enhanced AI Toggle functionality
 */
function initAIToggleEnhancements() {
    const aiToggle = document.getElementById('useSmartEngine');
    const statusDot = document.querySelector('.status-dot');
    const statusLabel = document.querySelector('.status-label');

    if (aiToggle && statusDot && statusLabel) {
        aiToggle.addEventListener('change', function() {
            if (this.checked) {
                statusDot.style.background = '#00b894';
                statusLabel.textContent = 'Active';

                // Add success animation
                const container = this.closest('.ai-toggle-container');
                if (container) {
                    container.classList.add('success-animation');
                    setTimeout(() => {
                        container.classList.remove('success-animation');
                    }, 600);
                }
            } else {
                statusDot.style.background = '#6c757d';
                statusLabel.textContent = 'Inactive';
            }
        });

        // Handle disabled state
        const titleInput = document.getElementById('adTitle');
        if (titleInput) {
            titleInput.addEventListener('input', function() {
                const hasTitle = this.value.trim().length > 0;

                if (!hasTitle) {
                    statusDot.style.background = '#ffc107';
                    statusLabel.textContent = 'Waiting';
                } else if (aiToggle.checked) {
                    statusDot.style.background = '#00b894';
                    statusLabel.textContent = 'Active';
                } else {
                    statusDot.style.background = '#6c757d';
                    statusLabel.textContent = 'Inactive';
                }
            });
        }
    }

    console.log('AI Toggle enhancements initialized');
}

/**
 * Initialize progress tracking
 */
function initProgressTracking() {
    const progressBar = document.querySelector('.progress-bar');
    const tabs = document.querySelectorAll('.nav-link');

    if (progressBar && tabs.length > 0) {
        tabs.forEach((tab, index) => {
            tab.addEventListener('click', function() {
                const progress = ((index + 1) / tabs.length) * 100;
                progressBar.style.width = `${progress}%`;
            });
        });
    }

    console.log('Progress tracking initialized');
}

/**
 * Initialize animations
 */
function initAnimations() {
    // Animate elements on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
            }
        });
    }, observerOptions);

    // Observe form groups
    const formGroups = document.querySelectorAll('.form-group-enhanced');
    formGroups.forEach(group => {
        observer.observe(group);
    });

    console.log('Animations initialized');
}

/**
 * Initialize form enhancements
 */
function initFormEnhancements() {
    // Auto-save form data to localStorage
    const form = document.getElementById('adCreationForm');
    if (form) {
        const inputs = form.querySelectorAll('input, textarea, select');

        // Load saved data
        inputs.forEach(input => {
            const savedValue = localStorage.getItem(`ad_form_${input.name}`);
            if (savedValue && !input.value) {
                input.value = savedValue;
            }
        });

        // Save data on change
        inputs.forEach(input => {
            input.addEventListener('change', function() {
                localStorage.setItem(`ad_form_${this.name}`, this.value);
            });
        });

        // Clear saved data on successful submission
        form.addEventListener('submit', function() {
            inputs.forEach(input => {
                localStorage.removeItem(`ad_form_${input.name}`);
            });
        });
    }

    // Enhanced button interactions
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            // Add ripple effect
            const ripple = document.createElement('span');
            ripple.classList.add('ripple');
            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });

    console.log('Form enhancements initialized');
}

/**
 * Utility function to show loading state on buttons
 */
function showButtonLoading(button, text = 'Loading...') {
    if (!button) return;

    button.classList.add('btn-loading');
    button.disabled = true;

    const originalText = button.innerHTML;
    button.innerHTML = `
        <div class="loading-spinner"></div>
        ${text}
    `;

    return function hideLoading() {
        button.classList.remove('btn-loading');
        button.disabled = false;
        button.innerHTML = originalText;
    };
}

/**
 * Utility function to show success animation
 */
function showSuccessAnimation(element) {
    if (!element) return;

    element.classList.add('success-animation');
    setTimeout(() => {
        element.classList.remove('success-animation');
    }, 600);
}

/**
 * Initialize enhanced ad preview functionality
 */
function initEnhancedPreview() {
    const titleInput = document.getElementById('adTitle');
    const contentInput = document.getElementById('adContent');
    const locationSelect = document.getElementById('previewLocationType');
    const refreshBtn = document.getElementById('refreshPreview');

    const previewTitle = document.getElementById('previewTitle');
    const previewContent = document.getElementById('previewContent');
    const previewWrapper = document.getElementById('previewWrapper');
    const previewLocationLabel = document.getElementById('previewLocationLabel');
    const previewSizeInfo = document.getElementById('previewSizeInfo');
    const previewTypeInfo = document.getElementById('previewTypeInfo');
    const previewFormatInfo = document.getElementById('previewFormatInfo');

    // Real-time preview updates
    if (titleInput && previewTitle) {
        titleInput.addEventListener('input', function() {
            const title = this.value.trim();
            previewTitle.textContent = title || 'Your Ad Title Will Appear Here';

            // Add animation when content changes
            previewTitle.classList.add('content-updated');
            setTimeout(() => {
                previewTitle.classList.remove('content-updated');
            }, 300);
        });
    }

    if (contentInput && previewContent) {
        contentInput.addEventListener('input', function() {
            const content = this.value.trim();
            previewContent.textContent = content || 'Your compelling ad content will appear here as you type. This preview updates in real-time to show exactly how your ad will look to potential customers.';

            // Add animation when content changes
            previewContent.classList.add('content-updated');
            setTimeout(() => {
                previewContent.classList.remove('content-updated');
            }, 300);
        });
    }

    // Location type changes
    if (locationSelect && previewWrapper && previewLocationLabel) {
        locationSelect.addEventListener('change', function() {
            const selectedValue = this.value;
            const selectedText = this.options[this.selectedIndex].text;

            // Update location label
            previewLocationLabel.innerHTML = getLocationIcon(selectedValue) + ' ' + selectedText;

            // Remove all location classes
            previewWrapper.classList.remove('header-preview', 'sidebar-preview', 'content-preview', 'footer-preview', 'popup-preview');

            // Add appropriate class
            if (selectedValue !== 'default') {
                previewWrapper.classList.add(selectedValue + '-preview');
            }

            // Update preview info
            updatePreviewInfo(selectedValue);

            // Add transition effect
            previewWrapper.style.transform = 'scale(0.95)';
            setTimeout(() => {
                previewWrapper.style.transform = 'scale(1)';
            }, 150);
        });
    }

    // Refresh button functionality
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            // Add spinning animation
            const icon = this.querySelector('i');
            icon.classList.add('fa-spin');

            // Simulate refresh
            setTimeout(() => {
                icon.classList.remove('fa-spin');

                // Trigger content update
                if (titleInput) titleInput.dispatchEvent(new Event('input'));
                if (contentInput) contentInput.dispatchEvent(new Event('input'));

                // Show success feedback
                showSuccessAnimation(previewWrapper);
            }, 1000);
        });
    }

    function getLocationIcon(location) {
        const icons = {
            'default': '<i class="fas fa-mobile-alt"></i>',
            'header': '<i class="fas fa-arrow-up"></i>',
            'sidebar': '<i class="fas fa-columns"></i>',
            'content': '<i class="fas fa-file-alt"></i>',
            'footer': '<i class="fas fa-arrow-down"></i>',
            'popup': '<i class="fas fa-window-restore"></i>'
        };
        return icons[location] || icons['default'];
    }

    function updatePreviewInfo(location) {
        const locationData = {
            'default': { size: '300x250px', type: 'Display Ad', format: 'Responsive' },
            'header': { size: '300x90px', type: 'Banner Ad', format: 'Fixed' },
            'sidebar': { size: '300x250px', type: 'Sidebar Ad', format: 'Fixed' },
            'content': { size: '300x60px', type: 'Banner Ad', format: 'Inline' },
            'footer': { size: '300x90px', type: 'Footer Banner', format: 'Fixed' },
            'popup': { size: '300x250px', type: 'Popup Ad', format: 'Modal' }
        };

        const data = locationData[location] || locationData['default'];

        if (previewSizeInfo) previewSizeInfo.textContent = data.size;
        if (previewTypeInfo) previewTypeInfo.textContent = data.type;
        if (previewFormatInfo) previewFormatInfo.textContent = data.format;
    }

    // Initialize with default values
    if (locationSelect) {
        locationSelect.dispatchEvent(new Event('change'));
    }

    console.log('Enhanced preview initialized');
}

/**
 * Initialize enhanced upload functionality
 */
function initEnhancedUpload() {
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('adMedia');
    const uploadPreview = document.getElementById('uploadPreview');
    const previewImage = document.getElementById('previewImage');
    const removeButton = document.getElementById('removeImage');

    if (!uploadArea || !fileInput) return;

    // Click to upload
    uploadArea.addEventListener('click', function() {
        fileInput.click();
    });

    // Drag and drop functionality
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileUpload(files[0]);
        }
    });

    // File input change
    fileInput.addEventListener('change', function(e) {
        if (e.target.files.length > 0) {
            handleFileUpload(e.target.files[0]);
        }
    });

    // Remove image
    if (removeButton) {
        removeButton.addEventListener('click', function() {
            fileInput.value = '';
            uploadArea.style.display = 'block';
            uploadPreview.style.display = 'none';
        });
    }

    function handleFileUpload(file) {
        // Validate file type
        if (!file.type.startsWith('image/')) {
            alert('Please select an image file.');
            return;
        }

        // Validate file size (5MB)
        if (file.size > 5 * 1024 * 1024) {
            alert('File size must be less than 5MB.');
            return;
        }

        // Show preview
        const reader = new FileReader();
        reader.onload = function(e) {
            if (previewImage) {
                previewImage.src = e.target.result;
                uploadArea.style.display = 'none';
                uploadPreview.style.display = 'block';

                // Update preview in the ad preview section
                const adPreviewImage = document.querySelector('#previewImage img');
                if (adPreviewImage) {
                    adPreviewImage.src = e.target.result;
                }
            }
        };
        reader.readAsDataURL(file);
    }

    console.log('Enhanced upload initialized');
}

/**
 * Initialize character counter for content textarea
 */
function initContentCharacterCounter() {
    const contentInput = document.getElementById('adContent');
    if (!contentInput) return;

    const counter = contentInput.parentElement.querySelector('.character-count');
    const statusSpan = contentInput.parentElement.querySelector('.input-status');

    if (counter) {
        contentInput.addEventListener('input', function() {
            const length = this.value.length;
            const maxLength = this.getAttribute('maxlength') || 500;

            counter.textContent = `${length}/${maxLength}`;

            // Update counter color based on length
            counter.classList.remove('warning', 'danger');
            if (length > maxLength * 0.8) {
                counter.classList.add('warning');
            }
            if (length >= maxLength) {
                counter.classList.add('danger');
            }

            // Update status with specific character thresholds
            if (statusSpan) {
                statusSpan.classList.remove('success', 'error');
                if (length >= 20 && length <= 400) {
                    statusSpan.classList.add('success');
                    statusSpan.textContent = '✓ Good length';
                } else if (length > 400) {
                    statusSpan.classList.add('error');
                    statusSpan.textContent = '⚠ Getting long';
                } else if (length > 0) {
                    statusSpan.textContent = 'Keep writing...';
                } else {
                    statusSpan.textContent = '';
                }
            }
        });
    }

    console.log('Content character counter initialized');
}

// Export utilities for use by other scripts
window.EnhancedUX = {
    showButtonLoading,
    showSuccessAnimation,
    initTooltips,
    initEnhancedPreview,
    initEnhancedUpload,
    initContentCharacterCounter
};
