"""
AI Service Clients
"""
import logging
import json
import time
import os
import random
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Tuple
from pathlib import Path

try:
    from mistralai import Mistral
    from mistralai.models import UserMessage
    MISTRAL_NEW_API = True
except ImportError:
    try:
        from mistralai.client import MistralClient
        from mistralai.models.chat_completion import ChatMessage
        MISTRAL_NEW_API = False
    except ImportError:
        MistralClient = None
        ChatMessage = None
        MISTRAL_NEW_API = False
import openai
import requests
try:
    from groq import Groq
    GROQ_AVAILABLE = True
except ImportError:
    GROQ_AVAILABLE = False

from ai_services.settings import (
    MISTRAL_API_KEY, MISTRAL_MODEL,
    OPENAI_API_KEY, OPENAI_MODEL,
    GROQ_API_KEY, GROQ_MODEL, GROQ_TEMPERATURE, GROQ_MAX_TOKENS,
    AI_ENGINE_URL, AI_ENGINE_API_TOKEN,
    AD_GENERATION_TEMPERATURE, AD_GENERATION_MAX_TOKENS
)

logger = logging.getLogger(__name__)

# Check if we should force local mode
USE_LOCAL_MODE = os.environ.get('USE_LOCAL_MODE', 'false').lower() in ('true', '1', 'yes')

class AIClient(ABC):
    """Abstract base class for AI clients"""

    @abstractmethod
    def generate_ad_suggestions(
        self,
        language: str,
        business_type: str,
        target_audience: str,
        tone: str,
        num_suggestions: int,
        ad_title: str = ""
    ) -> List[Dict[str, str]]:
        """Generate ad suggestions using the AI model"""
        pass

    def generate_html_content(
        self,
        prompt: str,
        page_type: str = "CUSTOM",
        primary_color: str = "#667eea",
        secondary_color: str = "#764ba2"
    ) -> str:
        """
        Generate HTML content for landing pages
        Default implementation uses the ad generation infrastructure
        """
        try:
            # Create a specialized prompt for HTML generation
            html_prompt = f"""
Create a complete HTML landing page with the following requirements:

{prompt}

Page Type: {page_type}
Primary Color: {primary_color}
Secondary Color: {secondary_color}

Requirements:
- Generate complete HTML document with DOCTYPE, head, and body
- Include inline CSS for styling
- Use modern design principles with gradients and shadows
- Ensure mobile responsiveness with proper viewport meta tag
- Use the specified colors in the design
- Include professional typography
- Add smooth animations and hover effects
- Make it visually stunning and professional

Return ONLY the complete HTML document. No explanations or markdown formatting.
"""

            # Use the existing ad generation infrastructure but adapt for HTML
            suggestions = self.generate_ad_suggestions(
                language="english",
                business_type=f"HTML Landing Page - {page_type}",
                target_audience="Website Visitors",
                tone="professional",
                num_suggestions=1,
                ad_title="HTML Landing Page Generation"
            )

            if suggestions and len(suggestions) > 0:
                first_suggestion = suggestions[0]

                # Extract content from the suggestion
                if isinstance(first_suggestion, dict):
                    html_content = (
                        first_suggestion.get('content', '') or
                        first_suggestion.get('description', '') or
                        first_suggestion.get('text', '') or
                        str(first_suggestion)
                    )
                else:
                    html_content = str(first_suggestion)

                # If we got content but it's not HTML, wrap it in basic HTML structure
                if html_content and not html_content.strip().startswith('<'):
                    html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Generated Landing Page</title>
    <style>
        body {{
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            margin: 0;
            padding: 2rem;
            background: linear-gradient(135deg, {primary_color} 0%, {secondary_color} 100%);
            min-height: 100vh;
            color: white;
        }}
        .content {{
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }}
        h1 {{ font-size: 2.5rem; margin-bottom: 1rem; }}
        p {{ font-size: 1.2rem; line-height: 1.6; }}
        @media (max-width: 768px) {{
            body {{ padding: 1rem; }}
            h1 {{ font-size: 2rem; }}
            p {{ font-size: 1rem; }}
        }}
    </style>
</head>
<body>
    <div class="content">
        {html_content}
    </div>
</body>
</html>"""

                return html_content

            return ""

        except Exception as e:
            logger.error(f"Error generating HTML content: {str(e)}")
            return ""


class MistralAIClient(AIClient):
    """Client for Mistral AI API"""

    def __init__(self):
        """Initialize the Mistral AI client"""
        if not MISTRAL_API_KEY:
            logger.error("Mistral API key not found. Please set the MISTRAL_API_KEY environment variable.")
            raise ValueError("Mistral API key not found")

        # Print debug information
        print(f"DEBUG: Initializing MistralAIClient with API key: {MISTRAL_API_KEY[:4]}...{MISTRAL_API_KEY[-4:]}")
        print(f"DEBUG: Using model: {MISTRAL_MODEL}")

        try:
            if MISTRAL_NEW_API:
                self.client = Mistral(api_key=MISTRAL_API_KEY)
                self.use_new_api = True
                print("DEBUG: Using new Mistral API")
            else:
                self.client = MistralClient(api_key=MISTRAL_API_KEY)
                self.use_new_api = False
                print("DEBUG: Using legacy Mistral API")

            self.model = MISTRAL_MODEL
            print("DEBUG: MistralAIClient initialized successfully")
        except Exception as e:
            print(f"DEBUG: Error initializing MistralAIClient: {str(e)}")
            logger.error(f"Error initializing MistralAIClient: {str(e)}")
            raise

    def is_model_healthy(self) -> bool:
        """
        Check if the Mistral model is healthy by making a request to the models endpoint

        Returns:
            bool: True if the model is healthy, False otherwise
        """
        try:
            import requests

            # Make a request to the models endpoint
            api_url = "https://api.mistral.ai/v1/models"
            headers = {
                "Authorization": f"Bearer {MISTRAL_API_KEY}",
                "Content-Type": "application/json"
            }

            logger.debug(f"[MISTRAL_HEALTH] Checking model health at: {api_url}")

            response = requests.get(api_url, headers=headers, timeout=5)

            if response.status_code == 200:
                models_data = response.json()

                # Check if our model is in the list of available models
                available_models = []
                if 'data' in models_data and isinstance(models_data['data'], list):
                    available_models = [model.get('id') for model in models_data.get('data', []) if model.get('id')]

                is_available = self.model in available_models

                logger.info(f"[MISTRAL_HEALTH] Health check successful. Model {self.model} {'is' if is_available else 'is not'} available.")
                logger.debug(f"[MISTRAL_HEALTH] Available models: {', '.join(available_models)}")

                return is_available
            else:
                logger.warning(f"[MISTRAL_HEALTH] Health check failed with status code: {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"[MISTRAL_HEALTH] Error checking model health: {str(e)}")
            return False

    def generate_ad_suggestions(
        self,
        language: str,
        business_type: str,
        target_audience: str,
        tone: str,
        num_suggestions: int,
        ad_title: str = ""
    ) -> List[Dict[str, str]]:
        """Generate ad suggestions using Mistral AI with caching and fallbacks"""
        from ai_services.settings import AD_GENERATION_PROMPT_TEMPLATE, AD_GENERATION_TEMPERATURE, AD_GENERATION_MAX_TOKENS
        from ai_services.cache_utils import get_cached_suggestions, cache_suggestions
        from ai_services.fallback_data import HARDCODED_IDEAS
        import hashlib

        logger.info(f"Generating ad suggestions with Mistral AI in {language}")
        logger.debug(f"[MISTRAL_DEBUG] Request parameters: language={language}, business_type={business_type}, target_audience={target_audience}, tone={tone}, num_suggestions={num_suggestions}, ad_title={ad_title}")

        # Create a cache key based on the input parameters
        user_input = f"{language}:{business_type}:{target_audience}:{tone}:{ad_title}".strip().lower()
        cache_key = f"suggestions:{hashlib.md5(user_input.encode()).hexdigest()}"

        # Try to get from cache first
        cached_suggestions = get_cached_suggestions(cache_key)
        if cached_suggestions:
            logger.info(f"Using cached suggestions for: {user_input[:50]}")
            return cached_suggestions

        # Determine title instruction and format based on whether a title was provided
        title_instruction = ""
        title_format = "[catchy title]"

        if ad_title:
            title_instruction = f"The ad title is: {ad_title}\nGenerate content that aligns with this title."
            title_format = ad_title

        prompt = AD_GENERATION_PROMPT_TEMPLATE.format(
            language=language,
            business_type=business_type,
            target_audience=target_audience,
            tone=tone,
            num_suggestions=num_suggestions,
            title_instruction=title_instruction,
            title_format=title_format
        )

        if self.use_new_api:
            messages = [UserMessage(content=prompt)]
        else:
            messages = [ChatMessage(role="user", content=prompt)]

        # Check model health before making the request
        model_healthy = self.is_model_healthy()
        if model_healthy:
            logger.info(f"[MISTRAL_DEBUG] Model {self.model} is healthy, proceeding with request")
        else:
            logger.warning(f"[MISTRAL_DEBUG] Model {self.model} health check failed, but will attempt request anyway")

        # Prepare the request payload for logging and direct API call
        request_payload = {
            "model": self.model,
            "messages": [{"role": msg.role, "content": msg.content} for msg in messages],
            "temperature": AD_GENERATION_TEMPERATURE,
            "max_tokens": AD_GENERATION_MAX_TOKENS
        }

        # Log the API details
        logger.debug(f"[MISTRAL_DEBUG] API URL: https://api.mistral.ai/v1/chat/completions")
        logger.debug(f"[MISTRAL_DEBUG] Request payload: {json.dumps(request_payload, indent=2)}")
        logger.debug(f"[MISTRAL_DEBUG] Request headers: Authorization: Bearer {MISTRAL_API_KEY[:4]}...{MISTRAL_API_KEY[-4:]}, Content-Type: application/json")

        # TIER 1: Try using the mistralai client library
        try:
            logger.info(f"[MISTRAL_DEBUG] TIER 1: Attempting to use mistralai client library")

            if self.use_new_api:
                response = self.client.chat.complete(
                    model=self.model,
                    messages=messages,
                    temperature=AD_GENERATION_TEMPERATURE,
                    max_tokens=AD_GENERATION_MAX_TOKENS
                )
                content = response.choices[0].message.content
            else:
                response = self.client.chat(
                    model=self.model,
                    messages=messages,
                    temperature=AD_GENERATION_TEMPERATURE,
                    max_tokens=AD_GENERATION_MAX_TOKENS
                )
                content = response.choices[0].message.content

            logger.info(f"[MISTRAL_DEBUG] TIER 1: Successfully used mistralai client library")

            # Process the response
            suggestions = self._parse_suggestions(content)

            # If we got valid suggestions, cache them
            if suggestions and len(suggestions) > 0:
                from ai_services.cache_utils import cache_suggestions
                cache_suggestions(cache_key, suggestions)
                logger.info(f"Cached {len(suggestions)} suggestions for {business_type} in {language}")

            return suggestions

        except Exception as client_error:
            # Log the client library error
            logger.warning(f"[MISTRAL_DEBUG] TIER 1 FAILED: Client library error: {str(client_error)}")
            logger.warning(f"[MISTRAL_DEBUG] TIER 1 FAILED: Exception type: {type(client_error).__name__}")

            # TIER 2: If the client library fails, try a direct API call
            try:
                logger.info(f"[MISTRAL_DEBUG] TIER 2: Falling back to direct API call")
                import requests

                api_url = "https://api.mistral.ai/v1/chat/completions"
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {MISTRAL_API_KEY}"
                }

                logger.debug(f"[MISTRAL_DEBUG] TIER 2: Direct API call to: {api_url}")
                logger.debug(f"[MISTRAL_DEBUG] TIER 2: Direct API payload: {json.dumps(request_payload, indent=2)}")

                api_response = requests.post(api_url, headers=headers, json=request_payload, timeout=30)

                # Check response status
                if api_response.status_code != 200:
                    error_msg = f"API returned status code {api_response.status_code}"
                    try:
                        error_details = api_response.json()
                        error_msg += f": {json.dumps(error_details)}"
                    except:
                        error_msg += f": {api_response.text[:200]}"

                    logger.error(f"[MISTRAL_DEBUG] TIER 2 FAILED: {error_msg}")
                    raise Exception(error_msg)

                # Parse the response
                response_data = api_response.json()

                logger.info(f"[MISTRAL_DEBUG] TIER 2: Successfully made direct API call")
                logger.debug(f"[MISTRAL_DEBUG] TIER 2: Response: {json.dumps(response_data)[:200]}...")

                # Extract the content from the response
                try:
                    message_content = response_data.get('choices', [{}])[0].get('message', {}).get('content', '')

                    if not message_content:
                        logger.warning(f"[MISTRAL_DEBUG] TIER 2: Empty content in response")
                        raise Exception("Empty content in API response")

                    # Parse the suggestions
                    suggestions = self._parse_suggestions(message_content)

                    # If we got valid suggestions, cache them
                    if suggestions and len(suggestions) > 0:
                        from ai_services.cache_utils import cache_suggestions
                        cache_suggestions(cache_key, suggestions)
                        logger.info(f"Cached {len(suggestions)} suggestions for {business_type} in {language}")

                    return suggestions

                except Exception as parse_error:
                    logger.error(f"[MISTRAL_DEBUG] TIER 2 FAILED: Error parsing response: {str(parse_error)}")
                    raise parse_error

            except Exception as direct_api_error:
                # Log the direct API call error
                logger.error(f"[MISTRAL_DEBUG] TIER 2 FAILED: Direct API call error: {str(direct_api_error)}")
                logger.error(f"[MISTRAL_DEBUG] TIER 2 FAILED: Exception type: {type(direct_api_error).__name__}")

                # TIER 3: Try to get from cache with extended expiry as a fallback
                try:
                    logger.info(f"[MISTRAL_DEBUG] TIER 3: Attempting to retrieve from extended cache")
                    from ai_services.cache_utils import get_cached_suggestions
                    extended_cache = get_cached_suggestions(cache_key, max_age=60*60*24*30)  # 30 days
                    if extended_cache:
                        logger.info(f"[MISTRAL_DEBUG] TIER 3: Successfully retrieved from extended cache")
                        return extended_cache
                except Exception as cache_error:
                    logger.error(f"[MISTRAL_DEBUG] TIER 3 FAILED: Cache error: {str(cache_error)}")

                # TIER 4: Return hardcoded fallback suggestions
                logger.info(f"[MISTRAL_DEBUG] TIER 4: Using hardcoded fallback suggestions")
                return self._get_fallback_suggestions(language, ad_title)

    def _parse_suggestions(self, content: str) -> List[Dict[str, str]]:
        """Parse the AI response into a list of suggestions"""
        suggestions = []
        current_suggestion = {}

        # Check if we're using a T5 model by looking at the model name
        is_t5_model = 't5' in self.model.lower()
        logger.info(f"Parsing suggestions for model type: {'T5' if is_t5_model else 'Standard'}")

        # For T5 models, the output is usually a single response without the Title/Content format
        if is_t5_model:
            # Clean up the content
            content = content.strip()

            # Try to extract a title and content from the T5 output
            parts = content.split('.', 1)  # Split at the first period

            if len(parts) > 1:
                # Use the first sentence as title and the rest as content
                title = parts[0].strip()
                content_text = parts[1].strip()

                suggestions.append({
                    "title": title,
                    "content": content_text
                })

                logger.info(f"Parsed T5 suggestion with title: {title[:30]}...")
            else:
                # If we can't split, use the whole text as content with a generic title
                suggestions.append({
                    "title": "Advertisement",
                    "content": content
                })

                logger.info("Parsed T5 suggestion with generic title")

            # For T5 models, we typically only get one suggestion
            return suggestions

        # Standard parsing for non-T5 models
        for line in content.strip().split('\n'):
            line = line.strip()
            if not line:
                continue

            if line.lower().startswith('title:'):
                # If we have a previous suggestion, add it to the list
                if current_suggestion and 'title' in current_suggestion and 'content' in current_suggestion:
                    suggestions.append(current_suggestion)
                    current_suggestion = {}

                current_suggestion['title'] = line[6:].strip()

            elif line.lower().startswith('content:'):
                current_suggestion['content'] = line[8:].strip()

        # Add the last suggestion
        if current_suggestion and 'title' in current_suggestion and 'content' in current_suggestion:
            suggestions.append(current_suggestion)

        return suggestions

    def generate_curl_command(self, language: str, business_type: str, target_audience: str, tone: str, num_suggestions: int, ad_title: str = "") -> str:
        """
        Generate a curl command to manually test the Mistral API

        This is useful for debugging API issues by testing the request outside of the application
        """
        from ai_services.settings import AD_GENERATION_PROMPT_TEMPLATE, AD_GENERATION_TEMPERATURE, AD_GENERATION_MAX_TOKENS

        # Determine title instruction and format based on whether a title was provided
        title_instruction = ""
        title_format = "[catchy title]"

        if ad_title:
            title_instruction = f"The ad title is: {ad_title}\\nGenerate content that aligns with this title."
            title_format = ad_title

        prompt = AD_GENERATION_PROMPT_TEMPLATE.format(
            language=language,
            business_type=business_type,
            target_audience=target_audience,
            tone=tone,
            num_suggestions=num_suggestions,
            title_instruction=title_instruction,
            title_format=title_format
        )

        # Escape double quotes in the prompt
        prompt = prompt.replace('"', '\\"')

        # Create the JSON payload
        json_payload = f'''{{
  "model": "{self.model}",
  "messages": [
    {{
      "role": "user",
      "content": "{prompt}"
    }}
  ],
  "temperature": {AD_GENERATION_TEMPERATURE},
  "max_tokens": {AD_GENERATION_MAX_TOKENS}
}}'''

        # Create the curl command
        curl_command = f'''curl -X POST \\
  https://api.mistral.ai/v1/chat/completions \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer {MISTRAL_API_KEY}" \\
  -d '{json_payload}' \\
  | python -m json.tool'''

        return curl_command

    def _get_fallback_suggestions(self, language: str, ad_title: str = "") -> List[Dict[str, str]]:
        """Get fallback suggestions in case of API failure"""
        # Generate dynamic fallback suggestions based on language and title
        suggestions = []

        # Define content templates for each language
        content_templates = {
            'swahili': [
                "Pata huduma za hali ya juu kwa bei nafuu. Wasiliana nasi leo!",
                "Tumia teknolojia ya kisasa kukuza biashara yako. Jiunge na wateja wengine wanaofanikiwa.",
                "Tunaahidi ubora na ufanisi katika kila huduma. Piga simu leo!"
            ],
            'sheng': [
                "Tukufix na tech fiti sana. Holla sisi leo!",
                "Na hizi services zetu za kubamba. Weka order leo!",
                "Hatupati kashida, tunakusolve na bei poa. Kuja sasa!"
            ],
            'english': [
                "Get high-quality services at affordable prices. Contact us today!",
                "Use modern technology to grow your business. Join our successful clients.",
                "We deliver excellence in every service. Call us now for a consultation!"
            ]
        }

        # Default to English if language not supported
        if language not in content_templates:
            language = 'english'

        # Define title templates for each language if no title is provided
        title_templates = {
            'swahili': [
                "Suluhisho Bora kwa Biashara Yako",
                "Kuza Biashara Yako Leo",
                "Huduma Bora kwa Bei Nafuu"
            ],
            'sheng': [
                "Pata Solution za Kuunda",
                "Biashara Yako Inakam Juu",
                "Pata Deals Fiti"
            ],
            'english': [
                "Premium Solutions for Your Business",
                "Grow Your Business Today",
                "Premium Solutions You Can Trust"
            ]
        }

        # Generate suggestions
        for i in range(3):  # Generate 3 suggestions
            if i == 0 and ad_title:
                # Use provided title for first suggestion
                title = ad_title
            else:
                # Use template title
                title = title_templates[language][i]

            content = content_templates[language][i]

            suggestions.append({
                "title": title,
                "content": content
            })

        return suggestions


class OpenAIClient(AIClient):
    """Client for OpenAI API"""

    def __init__(self):
        """Initialize the OpenAI client"""
        if not OPENAI_API_KEY:
            logger.error("OpenAI API key not found. Please set the OPENAI_API_KEY environment variable.")
            raise ValueError("OpenAI API key not found")

        openai.api_key = OPENAI_API_KEY
        self.model = OPENAI_MODEL

        # Check if the model is available
        is_healthy = self.is_model_healthy()
        logger.info(f"OpenAI model health check: {'Healthy' if is_healthy else 'Unhealthy'}")

    def is_model_healthy(self) -> bool:
        """
        Check if the OpenAI model is available and responding

        Returns:
            True if the model is healthy, False otherwise
        """
        logger.info(f"[OPENAI_HEALTH] Checking health of OpenAI model {self.model}")

        try:
            # Try to list models as a health check
            response = openai.models.list()

            # Check if our model is in the list
            model_ids = [model.id for model in response.data]
            logger.info(f"[OPENAI_HEALTH] Available models: {', '.join(model_ids[:5])}{'...' if len(model_ids) > 5 else ''}")

            if self.model in model_ids:
                logger.info(f"[OPENAI_HEALTH] Model {self.model} is available")
                return True
            else:
                logger.warning(f"[OPENAI_HEALTH] Model {self.model} is not in the list of available models")
                # Still return True if we can connect to the API, even if our specific model isn't listed
                # This is because some models might not be listed but still work
                return True

        except Exception as e:
            logger.error(f"[OPENAI_HEALTH] Error checking model health: {str(e)}")
            return False

    def generate_ad_suggestions(
        self,
        language: str,
        business_type: str,
        target_audience: str,
        tone: str,
        num_suggestions: int,
        ad_title: str = ""
    ) -> List[Dict[str, str]]:
        """Generate ad suggestions using OpenAI with caching and fallbacks"""
        from ai_services.settings import AD_GENERATION_PROMPT_TEMPLATE, AD_GENERATION_TEMPERATURE, AD_GENERATION_MAX_TOKENS
        from ai_services.cache import generate_cache_key, get_from_cache, save_to_cache
        from ai_services.network import check_internet_connection, check_api_provider_availability

        logger.info(f"Generating ad suggestions with OpenAI in {language}")

        # Generate a cache key for this request
        cache_key = generate_cache_key(
            prompt="ad_suggestions",
            model=self.model,
            language=language,
            business_type=business_type,
            target_audience=target_audience,
            tone=tone,
            num_suggestions=num_suggestions,
            ad_title=ad_title
        )

        # Try to get from cache first
        cached_suggestions = get_from_cache(cache_key)
        if cached_suggestions:
            logger.info(f"Retrieved ad suggestions from cache for {business_type} in {language}")
            return cached_suggestions

        # Check internet connection and API availability
        if not check_internet_connection():
            logger.warning("No internet connection available, using offline suggestions")
            offline_suggestions = OfflineAIClient().generate_ad_suggestions(
                language=language,
                business_type=business_type,
                target_audience=target_audience,
                tone=tone,
                num_suggestions=num_suggestions,
                ad_title=ad_title
            )
            return offline_suggestions

        # Check if OpenAI API is available
        available, error_msg = check_api_provider_availability('openai')
        if not available:
            logger.warning(f"OpenAI API is not available: {error_msg}, using offline suggestions")
            offline_suggestions = OfflineAIClient().generate_ad_suggestions(
                language=language,
                business_type=business_type,
                target_audience=target_audience,
                tone=tone,
                num_suggestions=num_suggestions,
                ad_title=ad_title
            )
            return offline_suggestions

        # Determine title instruction and format based on whether a title was provided
        title_instruction = ""
        title_format = "[catchy title]"

        if ad_title:
            title_instruction = f"The ad title is: {ad_title}\nGenerate content that aligns with this title."
            title_format = ad_title

        prompt = AD_GENERATION_PROMPT_TEMPLATE.format(
            language=language,
            business_type=business_type,
            target_audience=target_audience,
            tone=tone,
            num_suggestions=num_suggestions,
            title_instruction=title_instruction,
            title_format=title_format
        )

        try:
            # Log the attempt
            logger.info(f"Making API request to OpenAI for {business_type} in {language}")

            # Make the API request
            response = openai.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "user", "content": prompt}
                ],
                temperature=AD_GENERATION_TEMPERATURE,
                max_tokens=AD_GENERATION_MAX_TOKENS
            )

            content = response.choices[0].message.content
            suggestions = self._parse_suggestions(content)

            # If we got valid suggestions, cache them
            if suggestions and len(suggestions) > 0:
                save_to_cache(cache_key, suggestions)
                logger.info(f"Cached {len(suggestions)} suggestions for {business_type} in {language}")

            return suggestions

        except Exception as e:
            logger.error(f"Error generating ad suggestions with OpenAI: {str(e)}")

            # Try to get from cache with extended expiry as a fallback
            extended_cache = get_from_cache(cache_key, max_age=60*60*24*30)  # 30 days
            if extended_cache:
                logger.info(f"Retrieved ad suggestions from extended cache for {business_type} in {language}")
                return extended_cache

            # Return fallback suggestions
            logger.info(f"Using fallback suggestions for {business_type} in {language}")
            return self._get_fallback_suggestions(language, ad_title)

    def _parse_suggestions(self, content: str) -> List[Dict[str, str]]:
        """Parse the AI response into a list of suggestions"""
        suggestions = []
        current_suggestion = {}

        for line in content.strip().split('\n'):
            line = line.strip()
            if not line:
                continue

            if line.lower().startswith('title:'):
                # If we have a previous suggestion, add it to the list
                if current_suggestion and 'title' in current_suggestion and 'content' in current_suggestion:
                    suggestions.append(current_suggestion)
                    current_suggestion = {}

                current_suggestion['title'] = line[6:].strip()

            elif line.lower().startswith('content:'):
                current_suggestion['content'] = line[8:].strip()

        # Add the last suggestion
        if current_suggestion and 'title' in current_suggestion and 'content' in current_suggestion:
            suggestions.append(current_suggestion)

        return suggestions

    def _get_fallback_suggestions(self, language: str, ad_title: str = "") -> List[Dict[str, str]]:
        """Get fallback suggestions in case of API failure"""
        # Use the same fallback suggestions as MistralAIClient
        mistral_client = MistralAIClient()
        return mistral_client._get_fallback_suggestions(language, ad_title)


class GroqAIClient(AIClient):
    """Client for Groq AI API (High-speed inference with multiple model support)"""

    def __init__(self, model=None):
        """Initialize the Groq AI client"""
        if not GROQ_AVAILABLE:
            logger.error("Groq library not installed. Install with: pip install groq")
            raise ValueError("Groq library not installed")

        if not GROQ_API_KEY:
            logger.error("Groq API key not found. Please set the GROQ_API_KEY environment variable.")
            raise ValueError("Groq API key not found")

        self.client = Groq(api_key=GROQ_API_KEY)
        self.model = model or GROQ_MODEL
        self.temperature = GROQ_TEMPERATURE
        self.max_tokens = GROQ_MAX_TOKENS

        # Define Groq model priority order (best to fastest)
        self.model_priority = [
            "llama3-70b-8192",      # Best quality, slower
            "llama3-8b-8192",       # Balanced quality/speed
            "gemma2-9b-it"          # Alternative fast model
        ]

        logger.info(f"Initialized GroqAIClient with model: {self.model}")

    def is_model_healthy(self) -> bool:
        """
        Check if the Groq model is available and responding

        Returns:
            True if the model is healthy, False otherwise
        """
        logger.info(f"[GROQ_HEALTH] Checking health of Groq model {self.model}")

        try:
            # Make a simple test request to check if the API is responding
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=10,
                temperature=0.1
            )

            if response and response.choices:
                logger.info(f"[GROQ_HEALTH] Model {self.model} is healthy")
                return True
            else:
                logger.warning(f"[GROQ_HEALTH] Model {self.model} returned empty response")
                return False

        except Exception as e:
            logger.error(f"[GROQ_HEALTH] Error checking model health: {str(e)}")
            return False

    def generate_ad_suggestions(
        self,
        language: str,
        business_type: str,
        target_audience: str,
        tone: str,
        num_suggestions: int,
        ad_title: str = ""
    ) -> List[Dict[str, str]]:
        """Generate ad suggestions using Groq AI with caching and fallbacks"""
        from ai_services.settings import AD_GENERATION_PROMPT_TEMPLATE
        from ai_services.cache import generate_cache_key, get_from_cache, save_to_cache
        from ai_services.network import check_internet_connection, check_api_provider_availability

        logger.info(f"Generating ad suggestions with Groq AI in {language}")

        # Generate a cache key for this request
        cache_key = generate_cache_key(
            prompt="ad_suggestions",
            model=self.model,
            language=language,
            business_type=business_type,
            target_audience=target_audience,
            tone=tone,
            num_suggestions=num_suggestions,
            ad_title=ad_title
        )

        # Try to get from cache first
        cached_suggestions = get_from_cache(cache_key)
        if cached_suggestions:
            logger.info(f"Retrieved ad suggestions from cache for {business_type} in {language}")
            return cached_suggestions

        # Check internet connection
        if not check_internet_connection():
            logger.warning("No internet connection available, using offline suggestions")
            offline_suggestions = OfflineAIClient().generate_ad_suggestions(
                language=language,
                business_type=business_type,
                target_audience=target_audience,
                tone=tone,
                num_suggestions=num_suggestions,
                ad_title=ad_title
            )
            return offline_suggestions

        # Determine title instruction and format based on whether a title was provided
        title_instruction = ""
        title_format = "[catchy title]"

        if ad_title:
            title_instruction = f"The ad title is: {ad_title}\nGenerate content that aligns with this title."
            title_format = ad_title

        prompt = AD_GENERATION_PROMPT_TEMPLATE.format(
            language=language,
            business_type=business_type,
            target_audience=target_audience,
            tone=tone,
            num_suggestions=num_suggestions,
            title_instruction=title_instruction,
            title_format=title_format
        )

        try:
            # Log the attempt
            logger.info(f"Making API request to Groq for {business_type} in {language}")

            # Make the API request using the official Groq client
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=self.temperature,
                max_tokens=self.max_tokens
            )

            # Extract content from response
            content = response.choices[0].message.content

            if not content:
                logger.warning("Empty content in Groq API response")
                raise Exception("Empty content in API response")

            suggestions = self._parse_suggestions(content)

            # If we got valid suggestions, cache them
            if suggestions and len(suggestions) > 0:
                save_to_cache(cache_key, suggestions)
                logger.info(f"Cached {len(suggestions)} suggestions for {business_type} in {language}")

            return suggestions

        except Exception as e:
            logger.error(f"Error generating ad suggestions with Groq: {str(e)}")

            # Try to get from cache with extended expiry as a fallback
            extended_cache = get_from_cache(cache_key, max_age=60*60*24*30)  # 30 days
            if extended_cache:
                logger.info(f"Retrieved ad suggestions from extended cache for {business_type} in {language}")
                return extended_cache

            # Return fallback suggestions
            logger.info(f"Using fallback suggestions for {business_type} in {language}")
            return self._get_fallback_suggestions(language, ad_title)

    def generate_with_model_priority(
        self,
        language: str,
        business_type: str,
        target_audience: str,
        tone: str,
        num_suggestions: int,
        ad_title: str = ""
    ) -> List[Dict[str, str]]:
        """Generate ad suggestions trying Groq models in priority order"""
        from ai_services.settings import AD_GENERATION_PROMPT_TEMPLATE

        # Determine title instruction and format based on whether a title was provided
        title_instruction = ""
        title_format = "[catchy title]"

        if ad_title:
            title_instruction = f"The ad title is: {ad_title}\nGenerate content that aligns with this title."
            title_format = ad_title

        prompt = AD_GENERATION_PROMPT_TEMPLATE.format(
            language=language,
            business_type=business_type,
            target_audience=target_audience,
            tone=tone,
            num_suggestions=num_suggestions,
            title_instruction=title_instruction,
            title_format=title_format
        )

        # Try each model in priority order
        for model in self.model_priority:
            try:
                logger.info(f"Trying Groq model: {model} for {business_type} in {language}")

                response = self.client.chat.completions.create(
                    model=model,
                    messages=[{"role": "user", "content": prompt}],
                    temperature=self.temperature,
                    max_tokens=self.max_tokens
                )

                content = response.choices[0].message.content
                if content:
                    suggestions = self._parse_suggestions(content)
                    if suggestions and len(suggestions) > 0:
                        # Add model info to suggestions
                        for suggestion in suggestions:
                            suggestion['model'] = model
                            suggestion['provider'] = 'groq'
                            suggestion['ai_generated'] = True

                        logger.info(f"Successfully generated {len(suggestions)} suggestions with {model}")
                        return suggestions

            except Exception as e:
                logger.warning(f"Groq model {model} failed: {str(e)}")
                continue

        # If all models failed, return fallback
        logger.error("All Groq models failed, using fallback suggestions")
        return self._get_fallback_suggestions(language, ad_title)

    def _parse_suggestions(self, content: str) -> List[Dict[str, str]]:
        """Parse the AI response into a list of suggestions"""
        suggestions = []
        current_suggestion = {}

        for line in content.strip().split('\n'):
            line = line.strip()
            if not line:
                continue

            if line.lower().startswith('title:'):
                # If we have a previous suggestion, add it to the list
                if current_suggestion and 'title' in current_suggestion and 'content' in current_suggestion:
                    suggestions.append(current_suggestion)
                    current_suggestion = {}

                current_suggestion['title'] = line[6:].strip()

            elif line.lower().startswith('content:'):
                current_suggestion['content'] = line[8:].strip()

        # Add the last suggestion
        if current_suggestion and 'title' in current_suggestion and 'content' in current_suggestion:
            suggestions.append(current_suggestion)

        return suggestions

    def _get_fallback_suggestions(self, language: str, ad_title: str = "") -> List[Dict[str, str]]:
        """Get fallback suggestions in case of API failure"""
        # Use the same fallback suggestions as MistralAIClient
        mistral_client = MistralAIClient()
        return mistral_client._get_fallback_suggestions(language, ad_title)


class OfflineAIClient(AIClient):
    """Client for offline mode with hardcoded responses"""

    def __init__(self):
        """Initialize the offline client"""
        logger.warning("Using OfflineAIClient - all responses will be from hardcoded templates")
        self.model = "offline"
        self.api_key = None
        self.api_url = None
        self.offline_suggestions = self._load_offline_suggestions()

    def _load_offline_suggestions(self) -> Dict:
        """
        Load offline suggestions from JSON file

        Returns:
            Dict: Offline suggestions by language and business type
        """
        try:
            # Get the path to the offline suggestions file
            file_path = Path(__file__).parent / 'offline_suggestions.json'

            # Check if the file exists
            if file_path.exists():
                # Load the suggestions from the file
                with open(file_path, 'r', encoding='utf-8') as f:
                    suggestions = json.load(f)

                logger.info(f"Loaded offline suggestions from {file_path}")
                return suggestions
            else:
                logger.warning(f"Offline suggestions file not found at {file_path}, using hardcoded fallbacks")
                return None
        except Exception as e:
            logger.error(f"Error loading offline suggestions: {str(e)}")
            return None

    def generate_ad_suggestions(
        self,
        language: str,
        business_type: str,
        target_audience: str,
        tone: str,
        num_suggestions: int,
        ad_title: str = ""
    ) -> List[Dict[str, str]]:
        """Generate ad suggestions using offline suggestions from JSON file or hardcoded templates

        Args:
            language: The language for the suggestions
            business_type: The type of business
            target_audience: The target audience
            tone: The tone of the ad
            num_suggestions: Number of suggestions to generate
            ad_title: Optional title to use

        Returns:
            List of suggestion dictionaries
        """
        logger.info(f"Generating offline ad suggestions in {language} for {business_type}")

        # Check if we have offline suggestions loaded from JSON
        if self.offline_suggestions:
            # Normalize language
            language = language.lower()
            if language not in self.offline_suggestions:
                language = "english"  # Default to English if language not found

            # Normalize business type
            business_type_key = business_type.lower() if business_type else "general"

            # Find the best matching business type
            if business_type_key not in self.offline_suggestions[language]:
                # Try to find a similar business type
                for key in self.offline_suggestions[language].keys():
                    if business_type_key in key or key in business_type_key:
                        business_type_key = key
                        break
                else:
                    business_type_key = "general"  # Default to general if no match

            # Get suggestions for the language and business type
            available_suggestions = self.offline_suggestions[language][business_type_key]

            # If ad_title is provided, try to find suggestions that match
            if ad_title:
                matching_suggestions = []
                for suggestion in available_suggestions:
                    if ad_title.lower() in suggestion["title"].lower():
                        matching_suggestions.append(suggestion)

                if matching_suggestions:
                    available_suggestions = matching_suggestions

            # Select random suggestions up to num_suggestions
            selected_suggestions = []
            if available_suggestions:
                # Shuffle the suggestions to get random ones
                random.shuffle(available_suggestions)
                selected_suggestions = available_suggestions[:num_suggestions]

                # If we don't have enough suggestions, duplicate some
                while len(selected_suggestions) < num_suggestions:
                    # Get a random suggestion from the available ones
                    suggestion = random.choice(available_suggestions)
                    # Make a copy with slight variations
                    new_suggestion = suggestion.copy()
                    new_suggestion["title"] = f"New! {suggestion['title']}"
                    selected_suggestions.append(new_suggestion)

                # Add metadata to each suggestion
                for suggestion in selected_suggestions:
                    suggestion["model"] = "offline"
                    suggestion["ai_generated"] = False
                    suggestion["offline"] = True
                    suggestion["processing_time"] = 0

                logger.info(f"Generated {len(selected_suggestions)} offline suggestions from JSON for {business_type} in {language}")
                return selected_suggestions

        # If we don't have offline suggestions from JSON, use templates
        logger.info("No matching offline suggestions found in JSON, using templates")

        # Use the provided title if available, otherwise use a generic title
        title = ad_title if ad_title else f"{business_type.title()} for {target_audience.title()}"

        # Get templates based on language
        templates = self._get_templates(language, tone)

        # Generate suggestions
        suggestions = []
        for i in range(min(num_suggestions, len(templates))):
            template = templates[i]

            # Replace placeholders in the template
            content = template.replace("{business_type}", business_type)
            content = content.replace("{target_audience}", target_audience)

            suggestions.append({
                'title': f"{title} - Option {i+1}",
                'content': content,
                'model': "offline-template",
                'ai_generated': False,
                'offline': True
            })

        return suggestions

    def _get_templates(self, language: str, tone: str) -> List[str]:
        """Get templates for the specified language and tone

        Args:
            language: The language for the templates
            tone: The tone for the templates

        Returns:
            List of template strings
        """
        # English templates
        english_templates = {
            'professional': [
                "Introducing our premium {business_type} services designed specifically for {target_audience}. Our experienced team delivers exceptional quality and value. Contact us today to learn more about our offerings.",
                "Elevate your experience with our industry-leading {business_type} solutions. Trusted by {target_audience} for our commitment to excellence and attention to detail. Schedule a consultation today.",
                "Discover why {target_audience} choose our {business_type} services. With our proven track record and dedicated support, we ensure your complete satisfaction. Reach out now to get started."
            ],
            'casual': [
                "Hey {target_audience}! Looking for amazing {business_type}? We've got you covered with the best options around. Check us out and see the difference quality makes!",
                "Ready to try something awesome? Our {business_type} is perfect for {target_audience} like you! Stop by today and discover why everyone's talking about us.",
                "Don't settle for less when it comes to {business_type}! We're the go-to choice for {target_audience} who want quality without the hassle. Come see what you've been missing!"
            ],
            'persuasive': [
                "Why wait? The {business_type} you deserve is just a click away. Join thousands of satisfied {target_audience} who have already made the smart choice. Limited time offer - act now!",
                "Don't miss this opportunity to experience the best {business_type} available to {target_audience}. With our satisfaction guarantee, there's absolutely no risk. Secure your spot today!",
                "Imagine having access to premium {business_type} designed exclusively for {target_audience}. Stop imagining and make it a reality! Special pricing available for new customers."
            ]
        }

        # Swahili templates
        swahili_templates = {
            'professional': [
                "Tunatangaza huduma zetu bora za {business_type} zilizoundwa maalum kwa {target_audience}. Timu yetu yenye uzoefu hutoa ubora wa kipekee na thamani. Wasiliana nasi leo kujifunza zaidi kuhusu huduma zetu.",
                "Inua uzoefu wako na suluhisho zetu zinazotambulika za {business_type}. Tumeaminika na {target_audience} kwa kujitolea kwetu kwa ubora na umakini wa maelezo. Panga ushauri leo.",
                "Gundua kwa nini {target_audience} huchagua huduma zetu za {business_type}. Kwa rekodi yetu iliyothibitishwa na msaada wetu uliotolewa, tunahakikisha kuridhika kwako kikamilifu. Wasiliana sasa kuanza."
            ],
            'casual': [
                "Hujambo {target_audience}! Unatafuta {business_type} ya ajabu? Tunakusaidia na chaguo bora karibu. Tutembelee na uone tofauti inayofanya ubora!",
                "Uko tayari kujaribu kitu kizuri? {business_type} yetu ni kamili kwa {target_audience} kama wewe! Simama leo na ugundua kwa nini kila mtu anaongea kutuhusu.",
                "Usiridhike na kidogo inapokuja kwa {business_type}! Sisi ni chaguo la kwenda kwa {target_audience} ambao wanataka ubora bila usumbufu. Njoo uone kile ambacho umekuwa ukikosa!"
            ]
        }

        # Sheng templates
        sheng_templates = {
            'professional': [
                "Tunakam na {business_type} fiti sana kwa {target_audience}. Team yetu iko na skills za kudeliver quality na value. Tuma message leo ujue more kuhusu vitu zetu.",
                "Upgrade lifestyle yako na {business_type} solutions zetu za pro. {target_audience} wametutrustia kwa excellence na attention to detail. Book consultation leo.",
                "Discover mbona {target_audience} wanachagua {business_type} services zetu. Tuko na track record na dedicated support, tunasort satisfaction yako. Hit us up sasa."
            ],
            'casual': [
                "Niaje {target_audience}! Unatafuta {business_type} fiti? Tumekusort na best options karibu. Check us out uone difference ya quality!",
                "Uko ready kujaribu kitu fiti? {business_type} yetu ni perfect kwa {target_audience} kama wewe! Slide leo uone mbona watu wote wanaongea kutuhusu.",
                "Usikubali less ukiwa unatafuta {business_type}! Sisi ndio choice ya {target_audience} wanaotaka quality bila stress. Kuja uone vitu ulikuwa unamiss!"
            ]
        }

        # Select templates based on language
        if language.lower() == 'swahili':
            templates = swahili_templates
        elif language.lower() == 'sheng':
            templates = sheng_templates
        else:
            templates = english_templates

        # Select templates based on tone, defaulting to professional
        return templates.get(tone.lower(), templates['professional'])


class LocalAIEngineClient(AIClient):
    """Client for the local AI Engine microservice"""

    def __init__(self):
        """Initialize the local AI Engine client"""
        self.base_url = AI_ENGINE_URL
        self.api_token = AI_ENGINE_API_TOKEN
        self.model = "mistral-7b-instruct"  # Default model
        self.is_healthy = False

        logger.info(f"LocalAIEngineClient: Initialized with base URL {self.base_url}")

        # Check if the AI Engine is healthy
        self.is_healthy = self._check_health()

    def _check_health(self):
        """
        Check if the AI Engine is healthy

        Returns:
            bool: True if the AI Engine is healthy, False otherwise
        """
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            if response.status_code == 200:
                logger.info("LocalAIEngineClient: AI Engine is healthy")
                return True
            else:
                logger.warning(f"LocalAIEngineClient: AI Engine health check failed with status code {response.status_code}")
                return False
        except Exception as e:
            logger.warning(f"LocalAIEngineClient: AI Engine health check failed: {str(e)}")
            return False

    def generate_ad_suggestions(
        self,
        language: str,
        business_type: str,
        target_audience: str,
        tone: str,
        num_suggestions: int,
        ad_title: str = ""
    ) -> List[Dict[str, str]]:
        """Generate ad suggestions using the local AI Engine microservice with caching and fallbacks"""
        from ai_services.settings import AD_GENERATION_PROMPT_TEMPLATE
        from ai_services.cache import generate_cache_key, get_from_cache, save_to_cache
        from ai_services.network import check_api_provider_availability

        logger.info(f"LocalAIEngineClient: Generating ad suggestions in {language}")

        # Generate a cache key for this request
        cache_key = generate_cache_key(
            prompt="ad_suggestions_local",
            model="local_engine",
            language=language,
            business_type=business_type,
            target_audience=target_audience,
            tone=tone,
            num_suggestions=num_suggestions,
            ad_title=ad_title
        )

        # Try to get from cache first
        cached_suggestions = get_from_cache(cache_key)
        if cached_suggestions:
            logger.info(f"Retrieved local ad suggestions from cache for {business_type} in {language}")
            return cached_suggestions

        # Check if the AI Engine is healthy
        if not self.is_healthy:
            # Try to check health again
            self.is_healthy = self._check_health()

            # If still not healthy, check if we have an extended cache
            if not self.is_healthy:
                logger.warning("LocalAIEngineClient: AI Engine is not healthy")

                # Try to get from cache with extended expiry as a fallback
                extended_cache = get_from_cache(cache_key, max_age=60*60*24*30)  # 30 days
                if extended_cache:
                    logger.info(f"Retrieved local ad suggestions from extended cache for {business_type} in {language}")
                    return extended_cache

                # Check if any other provider is available
                available, _ = check_api_provider_availability('mistral')
                if available:
                    logger.info("Falling back to Mistral AI provider")
                    from ai_services.clients import MistralAIClient
                    return MistralAIClient().generate_ad_suggestions(
                        language=language,
                        business_type=business_type,
                        target_audience=target_audience,
                        tone=tone,
                        num_suggestions=num_suggestions,
                        ad_title=ad_title
                    )

                # If no other provider is available, use offline suggestions
                logger.warning("LocalAIEngineClient: Using offline suggestions")
                return OfflineAIClient().generate_ad_suggestions(
                    language=language,
                    business_type=business_type,
                    target_audience=target_audience,
                    tone=tone,
                    num_suggestions=num_suggestions,
                    ad_title=ad_title
                )

        # Determine title instruction and format based on whether a title was provided
        title_instruction = ""
        title_format = "[catchy title]"

        if ad_title:
            title_instruction = f"The ad title is: {ad_title}\nGenerate content that aligns with this title."
            title_format = ad_title

        prompt = AD_GENERATION_PROMPT_TEMPLATE.format(
            language=language,
            business_type=business_type,
            target_audience=target_audience,
            tone=tone,
            num_suggestions=num_suggestions,
            title_instruction=title_instruction,
            title_format=title_format
        )

        # Determine which model to use based on language
        models_to_try = []

        # For all languages, use Mistral as the primary model
        models_to_try = ["mistral"]

        # Try each model in sequence until one works
        all_errors = []

        for model_name in models_to_try:
            try:
                logger.info(f"LocalAIEngineClient: Trying model {model_name}")

                # Prepare headers with authentication token
                headers = {}
                if self.api_token:
                    headers["Authorization"] = f"Bearer {self.api_token}"

                # Prepare the request payload
                payload = {
                    "prompt": prompt,
                    "model": model_name,
                    "language": language,
                    "business_type": business_type,
                    "target_audience": target_audience,
                    "tone": tone,
                    "max_new_tokens": AD_GENERATION_MAX_TOKENS,
                    "temperature": AD_GENERATION_TEMPERATURE
                }

                # Make the request to the AI Engine
                start_time = time.time()
                response = requests.post(
                    f"{self.base_url}/generate_ad",
                    json=payload,
                    headers=headers,
                    timeout=30  # Longer timeout for model inference
                )
                processing_time = time.time() - start_time

                # Check for errors
                response.raise_for_status()

                # Parse the response
                result = response.json()

                # Extract the generated text
                generated_text = result.get("result", "")

                # Parse the generated text to extract suggestions
                suggestions = self._parse_suggestions(generated_text)

                # If no suggestions were parsed, use fallback
                if not suggestions:
                    logger.warning(f"LocalAIEngineClient: No suggestions parsed from model {model_name}, trying next model")
                    continue

                # Add metadata to each suggestion
                for suggestion in suggestions:
                    suggestion["model"] = model_name
                    suggestion["ai_generated"] = True
                    suggestion["processing_time"] = processing_time

                logger.info(f"LocalAIEngineClient: Generated {len(suggestions)} suggestions with model {model_name}")

                # Cache the successful response
                from ai_services.cache import save_to_cache
                save_to_cache(cache_key, suggestions)
                logger.info(f"Cached {len(suggestions)} local suggestions for {business_type} in {language}")

                return suggestions

            except Exception as e:
                error_msg = f"Error with model {model_name}: {str(e)}"
                logger.error(error_msg)
                all_errors.append(error_msg)
                # Continue to the next model

        # If we get here, all models failed
        error_details = "\n".join(all_errors)
        logger.error(f"LocalAIEngineClient: All models failed: {error_details}")

        # Try to get from cache with extended expiry as a last resort
        from ai_services.cache import get_from_cache
        extended_cache = get_from_cache(cache_key, max_age=60*60*24*30)  # 30 days
        if extended_cache:
            logger.info(f"Retrieved local ad suggestions from extended cache as last resort for {business_type} in {language}")
            return extended_cache

        # Check if any other provider is available
        from ai_services.network import check_api_provider_availability
        available, _ = check_api_provider_availability('mistral')
        if available:
            logger.info("Falling back to Mistral AI provider after local engine failure")
            from ai_services.clients import MistralAIClient
            return MistralAIClient().generate_ad_suggestions(
                language=language,
                business_type=business_type,
                target_audience=target_audience,
                tone=tone,
                num_suggestions=num_suggestions,
                ad_title=ad_title
            )

        # If all else fails, use fallback suggestions
        logger.info("Using hardcoded fallback suggestions as last resort")
        return self._get_fallback_suggestions(language, ad_title)

    def _parse_suggestions(self, text: str) -> List[Dict[str, str]]:
        """Parse the generated text to extract suggestions"""
        suggestions = []

        try:
            # Split the text by "Title:" to get individual suggestions
            parts = text.split("Title:")

            # Skip the first part (it's empty or contains the prompt)
            for part in parts[1:]:
                # Split by "Content:" to separate title and content
                if "Content:" in part:
                    title_content = part.split("Content:")
                    title = title_content[0].strip()
                    content = title_content[1].strip()

                    # Add to suggestions
                    suggestions.append({
                        "title": title,
                        "content": content
                    })

            return suggestions

        except Exception as e:
            logger.error(f"LocalAIEngineClient: Error parsing suggestions: {str(e)}")
            return []

    def _get_fallback_suggestions(self, language: str, ad_title: str = "") -> List[Dict[str, str]]:
        """Get fallback suggestions when AI generation fails"""
        # Use the provided title if available, otherwise use a generic title
        ad_title = ad_title if ad_title else "Special Offer"

        suggestions = []

        # Create language-specific fallback suggestions
        if language == "english":
            suggestions = [
                {
                    "title": ad_title,
                    "content": "Limited time offer! Don't miss out on our amazing products and services. Visit us today and discover the difference quality makes.",
                    "ai_generated": False,
                    "fallback": True
                },
                {
                    "title": "Exclusive Deal",
                    "content": "Experience excellence with our premium services. Designed for those who demand the best. Contact us today!",
                    "ai_generated": False,
                    "fallback": True
                }
            ]
        elif language == "swahili":
            suggestions = [
                {
                    "title": ad_title,
                    "content": "Ofa ya muda mfupi! Usikose bidhaa na huduma zetu za ajabu. Tutembelee leo na ugundue tofauti inayofanya ubora.",
                    "ai_generated": False,
                    "fallback": True
                }
            ]
        else:  # sheng or other
            suggestions = [
                {
                    "title": ad_title,
                    "content": "Hii ni deal ya kipekee! Usikose hizi products zetu fiti. Kuja sasa ujionee mwenyewe vile quality inakaa.",
                    "ai_generated": False,
                    "fallback": True
                }
            ]

        return suggestions





class MistralDirectClient(AIClient):
    """Client for direct Mistral API calls using requests instead of the official client library"""

    def __init__(self):
        """Initialize the Mistral Direct client"""
        if not MISTRAL_API_KEY:
            logger.error("Mistral API key not found. Please set the MISTRAL_API_KEY environment variable.")
            raise ValueError("Mistral API key not found")

        self.api_key = MISTRAL_API_KEY
        self.model = MISTRAL_MODEL
        self.api_url = "https://api.mistral.ai/v1/chat/completions"

        logger.info(f"MistralDirectClient: Initialized with model {self.model}")

    def generate_ad_suggestions(
        self,
        language: str,
        business_type: str,
        target_audience: str,
        tone: str,
        num_suggestions: int,
        ad_title: str = ""
    ) -> List[Dict[str, str]]:
        """Generate ad suggestions using direct Mistral API calls"""
        from ai_services.settings import AD_GENERATION_PROMPT_TEMPLATE, AD_GENERATION_TEMPERATURE, AD_GENERATION_MAX_TOKENS
        from ai_services.cache import generate_cache_key, get_from_cache, save_to_cache

        logger.info(f"[MISTRAL_DIRECT] Generating ad suggestions in {language}")
        logger.debug(f"[MISTRAL_DIRECT] Request parameters: language={language}, business_type={business_type}, target_audience={target_audience}, tone={tone}, num_suggestions={num_suggestions}, ad_title={ad_title}")

        # Generate a cache key for this request
        cache_key = generate_cache_key(
            prompt="ad_suggestions_direct",
            model=self.model,
            language=language,
            business_type=business_type,
            target_audience=target_audience,
            tone=tone,
            num_suggestions=num_suggestions,
            ad_title=ad_title
        )

        # Try to get from cache first
        cached_suggestions = get_from_cache(cache_key)
        if cached_suggestions:
            logger.info(f"[MISTRAL_DIRECT] Retrieved ad suggestions from cache for {business_type} in {language}")
            return cached_suggestions

        # Determine title instruction and format based on whether a title was provided
        title_instruction = ""
        title_format = "[catchy title]"

        if ad_title:
            title_instruction = f"The ad title is: {ad_title}\nGenerate content that aligns with this title."
            title_format = ad_title

        prompt = AD_GENERATION_PROMPT_TEMPLATE.format(
            language=language,
            business_type=business_type,
            target_audience=target_audience,
            tone=tone,
            num_suggestions=num_suggestions,
            title_instruction=title_instruction,
            title_format=title_format
        )

        # Prepare the request payload
        payload = {
            "model": self.model,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": AD_GENERATION_TEMPERATURE,
            "max_tokens": AD_GENERATION_MAX_TOKENS
        }

        # Prepare headers
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }

        logger.debug(f"[MISTRAL_DIRECT] API URL: {self.api_url}")
        logger.debug(f"[MISTRAL_DIRECT] Request payload: {json.dumps(payload, indent=2)}")
        logger.debug(f"[MISTRAL_DIRECT] Request headers: Authorization: Bearer {self.api_key[:4]}...{self.api_key[-4:]}, Content-Type: application/json")

        try:
            # Make the API request
            response = requests.post(self.api_url, headers=headers, json=payload, timeout=30)

            # Check response status
            if response.status_code != 200:
                error_msg = f"API returned status code {response.status_code}"
                try:
                    error_details = response.json()
                    error_msg += f": {json.dumps(error_details)}"
                except:
                    error_msg += f": {response.text[:200]}"

                logger.error(f"[MISTRAL_DIRECT] Error: {error_msg}")
                raise Exception(error_msg)

            # Parse the response
            response_data = response.json()

            logger.info(f"[MISTRAL_DIRECT] Successfully made API call")
            logger.debug(f"[MISTRAL_DIRECT] Response: {json.dumps(response_data)[:200]}...")

            # Extract the content from the response
            message_content = response_data.get('choices', [{}])[0].get('message', {}).get('content', '')

            if not message_content:
                logger.warning(f"[MISTRAL_DIRECT] Empty content in response")
                raise Exception("Empty content in API response")

            # Parse the suggestions
            suggestions = self._parse_suggestions(message_content)

            # If we got valid suggestions, cache them
            if suggestions and len(suggestions) > 0:
                save_to_cache(cache_key, suggestions)
                logger.info(f"[MISTRAL_DIRECT] Cached {len(suggestions)} suggestions for {business_type} in {language}")

                # Mark these suggestions as coming from MistralDirect
                for suggestion in suggestions:
                    suggestion['provider'] = 'mistral_direct'

                return suggestions
            else:
                logger.warning(f"[MISTRAL_DIRECT] No valid suggestions parsed from response")
                raise Exception("No valid suggestions parsed from response")

        except Exception as e:
            logger.error(f"[MISTRAL_DIRECT] Error generating ad suggestions: {str(e)}")
            raise

    def _parse_suggestions(self, content: str) -> List[Dict[str, str]]:
        """Parse the AI response into a list of suggestions"""
        suggestions = []
        current_suggestion = {}

        # Standard parsing for non-T5 models
        for line in content.strip().split('\n'):
            line = line.strip()
            if not line:
                continue

            if line.lower().startswith('title:'):
                # If we have a previous suggestion, add it to the list
                if current_suggestion and 'title' in current_suggestion and 'content' in current_suggestion:
                    suggestions.append(current_suggestion)
                    current_suggestion = {}

                current_suggestion['title'] = line[6:].strip()

            elif line.lower().startswith('content:'):
                current_suggestion['content'] = line[8:].strip()

        # Add the last suggestion
        if current_suggestion and 'title' in current_suggestion and 'content' in current_suggestion:
            suggestions.append(current_suggestion)

        # If we couldn't parse any suggestions in the standard format, try a simpler approach
        if not suggestions:
            logger.info("[MISTRAL_DIRECT] No suggestions parsed in standard format, trying simple parsing")

            # Split the content by double newlines to separate suggestions
            parts = content.strip().split('\n\n')

            for part in parts:
                if not part.strip():
                    continue

                # Try to extract a title and content
                lines = part.strip().split('\n')

                if len(lines) >= 2:
                    # Use the first line as title and the rest as content
                    title = lines[0].strip()
                    content_text = ' '.join(lines[1:]).strip()

                    suggestions.append({
                        "title": title,
                        "content": content_text
                    })
                elif len(lines) == 1:
                    # If there's only one line, use it as content with a generic title
                    suggestions.append({
                        "title": "Advertisement",
                        "content": lines[0].strip()
                    })

        # If we still have no suggestions, create one from the entire content
        if not suggestions:
            logger.info("[MISTRAL_DIRECT] Creating a single suggestion from the entire content")
            suggestions.append({
                "title": "Advertisement",
                "content": content.strip()
            })

        return suggestions


class FallbackAIClient(AIClient):
    """Client that tries multiple AI providers in sequence"""

    def __init__(self):
        """Initialize the fallback client with a list of providers to try"""
        logger.info("Initializing FallbackAIClient with multiple providers")

        # Initialize the list of clients to try in order
        self.clients = []

        # Try to initialize LocalAIEngineClient first (if enabled)
        from ai_services.settings import ENABLE_AI_ENGINE
        if ENABLE_AI_ENGINE:
            try:
                logger.debug("FallbackAIClient: Trying to initialize LocalAIEngineClient")
                local_client = LocalAIEngineClient()

                # Check if the local AI engine is healthy
                if local_client.is_healthy:
                    logger.info("FallbackAIClient: LocalAIEngineClient is healthy")
                    self.clients.append(("LocalAIEngineClient", local_client))
                else:
                    logger.warning("FallbackAIClient: LocalAIEngineClient is not healthy, skipping")

                logger.info("FallbackAIClient: Successfully initialized LocalAIEngineClient")
            except Exception as e:
                logger.warning(f"FallbackAIClient: Failed to initialize LocalAIEngineClient: {str(e)}")
        else:
            logger.info("FallbackAIClient: LocalAIEngineClient is disabled (ENABLE_AI_ENGINE=false), skipping")

        # Try to initialize each client and add it to the list if successful
        try:
            logger.debug("FallbackAIClient: Trying to initialize MistralAIClient")
            mistral_client = MistralAIClient()

            # Check if the Mistral model is healthy
            if hasattr(mistral_client, 'is_model_healthy') and callable(mistral_client.is_model_healthy):
                is_healthy = mistral_client.is_model_healthy()
                if is_healthy:
                    logger.info("FallbackAIClient: MistralAIClient is healthy")
                    self.clients.append(("MistralAIClient", mistral_client))
                else:
                    logger.warning("FallbackAIClient: MistralAIClient is not healthy, skipping")
            else:
                # If there's no health check method, add it anyway
                logger.info("FallbackAIClient: MistralAIClient health check not available, adding anyway")
                self.clients.append(("MistralAIClient", mistral_client))

            logger.info("FallbackAIClient: Successfully initialized MistralAIClient")
        except Exception as e:
            logger.warning(f"FallbackAIClient: Failed to initialize MistralAIClient: {str(e)}")

        try:
            logger.debug("FallbackAIClient: Trying to initialize MistralDirectClient")
            self.clients.append(("MistralDirectClient", MistralDirectClient()))
            logger.info("FallbackAIClient: Successfully initialized MistralDirectClient")
        except Exception as e:
            logger.warning(f"FallbackAIClient: Failed to initialize MistralDirectClient: {str(e)}")

        try:
            logger.debug("FallbackAIClient: Trying to initialize OpenAIClient")
            openai_client = OpenAIClient()

            # Check if the OpenAI model is healthy
            if hasattr(openai_client, 'is_model_healthy') and callable(openai_client.is_model_healthy):
                is_healthy = openai_client.is_model_healthy()
                if is_healthy:
                    logger.info("FallbackAIClient: OpenAIClient is healthy")
                    self.clients.append(("OpenAIClient", openai_client))
                else:
                    logger.warning("FallbackAIClient: OpenAIClient is not healthy, skipping")
            else:
                # If there's no health check method, add it anyway
                logger.info("FallbackAIClient: OpenAIClient health check not available, adding anyway")
                self.clients.append(("OpenAIClient", openai_client))

            logger.info("FallbackAIClient: Successfully initialized OpenAIClient")
        except Exception as e:
            logger.warning(f"FallbackAIClient: Failed to initialize OpenAIClient: {str(e)}")

        try:
            logger.debug("FallbackAIClient: Trying to initialize GroqAIClient")
            groq_client = GroqAIClient()

            # Check if the Groq model is healthy
            if hasattr(groq_client, 'is_model_healthy') and callable(groq_client.is_model_healthy):
                is_healthy = groq_client.is_model_healthy()
                if is_healthy:
                    logger.info("FallbackAIClient: GroqAIClient is healthy")
                    self.clients.append(("GroqAIClient", groq_client))
                else:
                    logger.warning("FallbackAIClient: GroqAIClient is not healthy, skipping")
            else:
                # If there's no health check method, add it anyway
                logger.info("FallbackAIClient: GroqAIClient health check not available, adding anyway")
                self.clients.append(("GroqAIClient", groq_client))

            logger.info("FallbackAIClient: Successfully initialized GroqAIClient")
        except Exception as e:
            logger.warning(f"FallbackAIClient: Failed to initialize GroqAIClient: {str(e)}")

        # Always add the offline client as a last resort
        self.offline_client = OfflineAIClient()

        if not self.clients:
            logger.warning("FallbackAIClient: No AI clients could be initialized, will use offline client only")

        # Log the order of providers
        provider_order = [client_name for client_name, _ in self.clients]
        logger.info(f"FallbackAIClient: Provider order: {' -> '.join(provider_order)} -> Offline Fallback")

    def generate_ad_suggestions(
        self,
        language: str,
        business_type: str,
        target_audience: str,
        tone: str,
        num_suggestions: int,
        ad_title: str = ""
    ) -> List[Dict[str, str]]:
        """Generate ad suggestions by trying multiple providers in sequence"""
        logger.info(f"FallbackAIClient: Generating ad suggestions in {language}")
        logger.debug(f"FallbackAIClient: Request parameters: language={language}, business_type={business_type}, target_audience={target_audience}, tone={tone}, num_suggestions={num_suggestions}, ad_title={ad_title}")

        # Try each client in order
        for client_name, client in self.clients:
            try:
                logger.info(f"FallbackAIClient: Trying {client_name}")
                suggestions = client.generate_ad_suggestions(
                    language=language,
                    business_type=business_type,
                    target_audience=target_audience,
                    tone=tone,
                    num_suggestions=num_suggestions,
                    ad_title=ad_title
                )

                # If we got valid suggestions, mark them with the provider and return
                if suggestions and len(suggestions) > 0:
                    logger.info(f"FallbackAIClient: Successfully generated {len(suggestions)} suggestions with {client_name}")

                    # Mark the suggestions with the provider
                    for suggestion in suggestions:
                        suggestion['provider'] = client_name.lower().replace('client', '')

                    return suggestions
                else:
                    logger.warning(f"FallbackAIClient: {client_name} returned no suggestions, trying next provider")

            except Exception as e:
                logger.error(f"FallbackAIClient: Error with {client_name}: {str(e)}")
                logger.debug(f"FallbackAIClient: Exception type: {type(e).__name__}")
                logger.info(f"FallbackAIClient: Trying next provider")

        # If all clients failed, use the offline client
        logger.warning("FallbackAIClient: All providers failed, using offline fallback suggestions")
        suggestions = self.offline_client.generate_ad_suggestions(
            language=language,
            business_type=business_type,
            target_audience=target_audience,
            tone=tone,
            num_suggestions=num_suggestions,
            ad_title=ad_title
        )

        # Mark the suggestions as fallback
        for suggestion in suggestions:
            suggestion['provider'] = 'offline'
            suggestion['fallback'] = True

        return suggestions


class SmartAIEngine(AIClient):
    """
    Smart AI Engine that implements intelligent suggestion selection:
    1. ✅ CACHE FIRST: Tries cached suggestions first (6-hour TTL)
    2. ⚡ GROQ PRIORITY: Uses Groq models in priority order (llama3-70b → llama3-8b → mixtral)
    3. 🔄 PROVIDER FALLBACK: Falls back to Mistral, then OpenAI
    4. 🧠 BEST SELECTION: Chooses the best suggestions from multiple sources
    5. 💾 EXTENDED CACHE: Falls back to 30-day cache if all providers fail
    6. 📱 OFFLINE FALLBACK: Uses offline suggestions as final fallback

    Cache Strategy:
    - Primary Cache: 6 hours (21600 seconds) for fresh suggestions
    - Extended Cache: 30 days for reliability when providers are down
    - Cache Key: Based on language, business_type, target_audience, tone, ad_title, num_suggestions
    """

    def __init__(self):
        """Initialize the Smart AI Engine"""
        self.groq_client = None
        self.mistral_client = None
        self.openai_client = None

        # Initialize available clients
        try:
            self.groq_client = GroqAIClient()
            logger.info("SmartAIEngine: Groq client initialized")
        except Exception as e:
            logger.warning(f"SmartAIEngine: Failed to initialize Groq client: {str(e)}")

        try:
            self.mistral_client = MistralAIClient()
            logger.info("SmartAIEngine: Mistral client initialized")
        except Exception as e:
            logger.warning(f"SmartAIEngine: Failed to initialize Mistral client: {str(e)}")

        try:
            self.openai_client = OpenAIClient()
            logger.info("SmartAIEngine: OpenAI client initialized")
        except Exception as e:
            logger.warning(f"SmartAIEngine: Failed to initialize OpenAI client: {str(e)}")

    def generate_ad_suggestions(
        self,
        language: str,
        business_type: str,
        target_audience: str,
        tone: str,
        num_suggestions: int,
        ad_title: str = ""
    ) -> List[Dict[str, str]]:
        """
        Generate the best ad suggestions using intelligent multi-source selection

        Process:
        1. Check cache first (6-hour TTL)
        2. Try Groq models in priority order
        3. Fall back to Mistral, then OpenAI
        4. Select best suggestions from all sources
        """
        from ai_services.cache_utils import get_cached_suggestions, cache_suggestions
        import hashlib

        logger.info(f"SmartAIEngine: Generating intelligent ad suggestions in {language}")

        # Step 1: Check cache first (6-hour TTL)
        user_input = f"{language}:{business_type}:{target_audience}:{tone}:{ad_title}:{num_suggestions}".strip().lower()
        cache_key = f"smart_suggestions:{hashlib.md5(user_input.encode()).hexdigest()}"

        # Try to get from cache first (6-hour TTL)
        cached_suggestions = get_cached_suggestions(cache_key)
        if cached_suggestions:
            logger.info(f"SmartAIEngine: Retrieved {len(cached_suggestions)} suggestions from cache (6h TTL)")
            # Add cache metadata to suggestions
            for suggestion in cached_suggestions:
                suggestion['cached'] = True
                suggestion['cache_source'] = 'smart_engine_6h'
            return cached_suggestions

        # Step 2: Collect suggestions from multiple sources
        all_suggestions = []

        # Try Groq models in priority order
        if self.groq_client:
            groq_suggestions = self.groq_client.generate_with_model_priority(
                language=language,
                business_type=business_type,
                target_audience=target_audience,
                tone=tone,
                num_suggestions=num_suggestions * 2,  # Get more for selection
                ad_title=ad_title
            )
            if groq_suggestions:
                all_suggestions.extend(groq_suggestions)
                logger.info(f"SmartAIEngine: Got {len(groq_suggestions)} suggestions from Groq")

        # Try Mistral if we need more suggestions
        if self.mistral_client and len(all_suggestions) < num_suggestions * 2:
            try:
                mistral_suggestions = self.mistral_client.generate_ad_suggestions(
                    language=language,
                    business_type=business_type,
                    target_audience=target_audience,
                    tone=tone,
                    num_suggestions=num_suggestions,
                    ad_title=ad_title
                )
                if mistral_suggestions:
                    # Add provider metadata to Mistral suggestions
                    for suggestion in mistral_suggestions:
                        suggestion['provider'] = 'mistral'
                        suggestion['model'] = suggestion.get('model', 'mistral-tiny')
                        suggestion['ai_generated'] = True
                    all_suggestions.extend(mistral_suggestions)
                    logger.info(f"SmartAIEngine: Got {len(mistral_suggestions)} suggestions from Mistral")
            except Exception as e:
                logger.warning(f"SmartAIEngine: Mistral failed: {str(e)}")

        # Try OpenAI if we still need more suggestions
        if self.openai_client and len(all_suggestions) < num_suggestions * 2:
            try:
                openai_suggestions = self.openai_client.generate_ad_suggestions(
                    language=language,
                    business_type=business_type,
                    target_audience=target_audience,
                    tone=tone,
                    num_suggestions=num_suggestions,
                    ad_title=ad_title
                )
                if openai_suggestions:
                    # Add provider metadata to OpenAI suggestions
                    for suggestion in openai_suggestions:
                        suggestion['provider'] = 'openai'
                        suggestion['model'] = suggestion.get('model', 'gpt-4-turbo')
                        suggestion['ai_generated'] = True
                    all_suggestions.extend(openai_suggestions)
                    logger.info(f"SmartAIEngine: Got {len(openai_suggestions)} suggestions from OpenAI")
            except Exception as e:
                logger.warning(f"SmartAIEngine: OpenAI failed: {str(e)}")

        # Step 3: Select the best suggestions
        if all_suggestions:
            best_suggestions = self._select_best_suggestions(
                all_suggestions,
                num_suggestions,
                ad_title,
                business_type,
                target_audience
            )

            # Cache the best suggestions (6-hour TTL)
            if best_suggestions:
                cache_suggestions(cache_key, best_suggestions, ttl=21600)  # 6 hours
                logger.info(f"SmartAIEngine: Selected and cached {len(best_suggestions)} best suggestions (6h TTL)")

            return best_suggestions

        # Step 4: Try extended cache as fallback (30 days)
        logger.warning("SmartAIEngine: All AI providers failed, trying extended cache")
        extended_cache = get_cached_suggestions(cache_key, max_age=60*60*24*30)  # 30 days
        if extended_cache:
            logger.info(f"SmartAIEngine: Retrieved {len(extended_cache)} suggestions from extended cache (30d)")
            # Add extended cache metadata
            for suggestion in extended_cache:
                suggestion['cached'] = True
                suggestion['cache_source'] = 'smart_engine_30d'
                suggestion['extended_cache'] = True
            return extended_cache

        # Step 5: Final fallback to offline suggestions
        logger.warning("SmartAIEngine: No cache available, using offline fallback")
        offline_client = OfflineAIClient()
        return offline_client.generate_ad_suggestions(
            language=language,
            business_type=business_type,
            target_audience=target_audience,
            tone=tone,
            num_suggestions=num_suggestions,
            ad_title=ad_title
        )

    def _select_best_suggestions(
        self,
        all_suggestions: List[Dict[str, str]],
        num_suggestions: int,
        ad_title: str,
        business_type: str,
        target_audience: str
    ) -> List[Dict[str, str]]:
        """
        Select the best suggestions from all sources using intelligent scoring
        """
        logger.info(f"SmartAIEngine: Selecting best {num_suggestions} from {len(all_suggestions)} suggestions")

        # Score each suggestion
        scored_suggestions = []
        for suggestion in all_suggestions:
            score = self._score_suggestion(suggestion, ad_title, business_type, target_audience)
            scored_suggestions.append((score, suggestion))

        # Sort by score (highest first) and select the best
        scored_suggestions.sort(key=lambda x: x[0], reverse=True)
        best_suggestions = [suggestion for _, suggestion in scored_suggestions[:num_suggestions]]

        # Add smart engine metadata
        for i, suggestion in enumerate(best_suggestions):
            suggestion['smart_engine'] = True
            suggestion['rank'] = i + 1
            suggestion['selection_score'] = scored_suggestions[i][0]

        return best_suggestions

    def _score_suggestion(
        self,
        suggestion: Dict[str, str],
        ad_title: str,
        business_type: str,
        target_audience: str
    ) -> float:
        """
        Score a suggestion based on multiple criteria
        """
        score = 0.0
        title = suggestion.get('title', '').lower()
        content = suggestion.get('content', '').lower()
        provider = suggestion.get('provider', '')
        model = suggestion.get('model', '')

        # Provider quality scoring (Groq models get highest scores)
        provider_scores = {
            'groq': 10.0,
            'mistral': 7.0,
            'openai': 8.0,
            'offline': 3.0
        }
        score += provider_scores.get(provider, 5.0)

        # Model quality scoring for Groq
        if provider == 'groq':
            model_scores = {
                'llama3-70b-8192': 10.0,      # Best quality
                'llama3-8b-8192': 8.0,        # Good balance
                'gemma2-9b-it': 7.0           # Fast alternative
            }
            score += model_scores.get(model, 5.0)

        # Title relevance scoring
        if ad_title:
            ad_title_lower = ad_title.lower()
            # Check if title contains key words from ad_title
            title_words = ad_title_lower.split()
            title_matches = sum(1 for word in title_words if word in title)
            score += (title_matches / len(title_words)) * 5.0 if title_words else 0

        # Business type relevance
        business_words = business_type.lower().split()
        business_matches = sum(1 for word in business_words if word in content or word in title)
        score += (business_matches / len(business_words)) * 3.0 if business_words else 0

        # Target audience relevance
        audience_words = target_audience.lower().split()
        audience_matches = sum(1 for word in audience_words if word in content or word in title)
        score += (audience_matches / len(audience_words)) * 3.0 if audience_words else 0

        # Content quality indicators
        if len(content) > 50:  # Prefer substantial content
            score += 2.0
        if len(content) < 200:  # But not too long
            score += 1.0

        # Avoid duplicates (penalize very similar content)
        # This would need to be implemented with comparison to other suggestions

        return score


def get_ai_client():
    """Get the appropriate AI client based on settings"""
    from ai_services.settings import AI_PROVIDER

    logger.info(f"🔧 get_ai_client() called with AI_PROVIDER: '{AI_PROVIDER}' (type: {type(AI_PROVIDER)})")

    # Force SmartAIEngine for groq provider (intelligent multi-model selection)
    if AI_PROVIDER and AI_PROVIDER.lower() == 'groq':
        logger.info("✅ Using SmartAIEngine for intelligent Groq model selection")
        return SmartAIEngine()

    # Use SmartAIEngine for smart provider
    elif AI_PROVIDER and AI_PROVIDER.lower() == 'smart':
        logger.info("✅ Using SmartAIEngine for intelligent multi-provider selection")
        return SmartAIEngine()

    # Use FallbackAIClient for fallback provider
    elif AI_PROVIDER and AI_PROVIDER.lower() == 'fallback':
        logger.info("⚠️ Using FallbackAIClient for provider fallback")
        return FallbackAIClient()

    # Use specific providers
    elif AI_PROVIDER and AI_PROVIDER.lower() == 'mistral':
        logger.info("⚠️ Using MistralAIClient")
        return MistralAIClient()

    elif AI_PROVIDER and AI_PROVIDER.lower() == 'openai':
        logger.info("⚠️ Using OpenAIClient")
        return OpenAIClient()

    # Default to SmartAIEngine for best experience
    else:
        logger.info(f"❓ AI_PROVIDER '{AI_PROVIDER}' not recognized, defaulting to SmartAIEngine")
        return SmartAIEngine()
