"""
MODULE 6: Management command to create default subscription plans
"""

from django.core.management.base import BaseCommand
from qrcode_app.models import Plan


class Command(BaseCommand):
    help = 'Create default subscription plans'

    def handle(self, *args, **options):
        self.stdout.write('Creating default subscription plans...')
        
        # Free Plan
        free_plan, created = Plan.objects.get_or_create(
            name='Free',
            defaults={
                'plan_type': 'FREE',
                'description': 'Perfect for personal use and small projects',
                'max_qr_codes': 5,
                'max_scans_per_month': 100,
                'max_dynamic_redirects': 0,
                'max_ai_pages': 0,
                'max_webhooks': 0,
                'max_scan_alerts': 0,
                'ai_enabled': False,
                'alerts_enabled': False,
                'webhooks_enabled': False,
                'analytics_enabled': True,
                'advanced_analytics_enabled': False,
                'branding_enabled': False,
                'batch_processing_enabled': False,
                'api_access_enabled': False,
                'priority_support_enabled': False,
                'price': 0.00,
                'yearly_price': 0.00,
                'is_active': True,
                'is_default': True,
                'sort_order': 1
            }
        )
        
        if created:
            self.stdout.write(self.style.SUCCESS('✅ Created Free plan'))
        else:
            self.stdout.write('Free plan already exists')
        
        # Pro Plan
        pro_plan, created = Plan.objects.get_or_create(
            name='Pro',
            defaults={
                'plan_type': 'PRO',
                'description': 'Ideal for businesses and marketing professionals',
                'max_qr_codes': 100,
                'max_scans_per_month': 10000,
                'max_dynamic_redirects': 50,
                'max_ai_pages': 25,
                'max_webhooks': 10,
                'max_scan_alerts': 20,
                'ai_enabled': True,
                'alerts_enabled': True,
                'webhooks_enabled': True,
                'analytics_enabled': True,
                'advanced_analytics_enabled': True,
                'branding_enabled': True,
                'batch_processing_enabled': True,
                'api_access_enabled': False,
                'priority_support_enabled': True,
                'price': 29.99,
                'yearly_price': 299.99,
                'is_active': True,
                'is_default': False,
                'sort_order': 2
            }
        )
        
        if created:
            self.stdout.write(self.style.SUCCESS('✅ Created Pro plan'))
        else:
            self.stdout.write('Pro plan already exists')
        
        # Enterprise Plan
        enterprise_plan, created = Plan.objects.get_or_create(
            name='Enterprise',
            defaults={
                'plan_type': 'ENTERPRISE',
                'description': 'Complete solution for large organizations',
                'max_qr_codes': 10000,  # Effectively unlimited
                'max_scans_per_month': 1000000,  # Effectively unlimited
                'max_dynamic_redirects': 1000,
                'max_ai_pages': 500,
                'max_webhooks': 100,
                'max_scan_alerts': 200,
                'ai_enabled': True,
                'alerts_enabled': True,
                'webhooks_enabled': True,
                'analytics_enabled': True,
                'advanced_analytics_enabled': True,
                'branding_enabled': True,
                'batch_processing_enabled': True,
                'api_access_enabled': True,
                'priority_support_enabled': True,
                'price': 99.99,
                'yearly_price': 999.99,
                'is_active': True,
                'is_default': False,
                'sort_order': 3
            }
        )
        
        if created:
            self.stdout.write(self.style.SUCCESS('✅ Created Enterprise plan'))
        else:
            self.stdout.write('Enterprise plan already exists')
        
        self.stdout.write(
            self.style.SUCCESS('Default plans setup complete!')
        )
