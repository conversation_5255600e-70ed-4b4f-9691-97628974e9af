/* Enterprise-Grade Mobile View Enhancements */

/* Mobile Navigation Improvements */
@media (max-width: 991.98px) {
    /* Fix for navbar collapse background */
    .enhanced-navbar .navbar-collapse {
        background: linear-gradient(135deg, #1a1f36, #121628);
        border-bottom: 1px solid rgba(58, 123, 213, 0.3);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        padding: 0;
        overflow-y: auto;
        max-height: 80vh;
    }

    /* Fix for nav links color on mobile */
    .enhanced-navbar .nav-link {
        color: rgba(255, 255, 255, 0.85) !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        padding: 1rem 1.5rem;
        font-weight: 500;
    }

    /* Improve dropdown toggle visibility */
    .enhanced-navbar .nav-link.dropdown-toggle::after {
        border-top-color: rgba(255, 255, 255, 0.5);
    }

    /* Fix dropdown menu styling */
    .enhanced-dropdown-menu {
        background: rgba(255, 255, 255, 0.05);
        border-left: 3px solid #3a7bd5;
        margin: 0;
        padding: 0;
    }

    /* Fix dropdown items */
    .enhanced-dropdown-item {
        color: rgba(255, 255, 255, 0.8) !important;
        padding: 0.75rem 1.5rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    }

    .enhanced-dropdown-item:hover {
        background-color: rgba(58, 123, 213, 0.1);
        color: #fff !important;
    }

    .enhanced-dropdown-item i {
        color: rgba(255, 255, 255, 0.6);
    }

    /* Fix dropdown headers */
    .enhanced-dropdown-header {
        background-color: rgba(0, 0, 0, 0.2);
        color: rgba(255, 255, 255, 0.6);
        padding: 0.5rem 1.5rem;
    }

    /* Improve navbar toggler */
    .navbar-toggler {
        border: none;
        padding: 0.5rem;
        color: #fff;
    }

    .navbar-toggler-icon {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.8%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") !important;
    }

    /* Login button improvements */
    .enhanced-login-btn {
        margin: 1rem 1.5rem;
        width: calc(100% - 3rem);
        display: flex;
        justify-content: center;
        background: linear-gradient(135deg, #3a7bd5, #00d2ff);
    }
}

/* Hero Section Mobile Improvements */
@media (max-width: 767.98px) {
    .hero-section {
        padding: 2rem 0 1rem;
    }

    .display-4 {
        font-size: 2rem;
        text-align: center;
    }

    .display-4::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .lead {
        font-size: 1.1rem;
        text-align: center;
    }

    /* Center badges on mobile */
    .hero-section .d-flex.flex-wrap {
        justify-content: center;
    }

    /* Center paragraph text */
    .hero-section p.mb-4 {
        text-align: center;
    }

    /* Center buttons */
    .hero-section .d-flex.flex-wrap.gap-3 {
        justify-content: center;
    }

    /* Add spacing between hero sections */
    .hero-section .col-lg-6:first-child {
        margin-bottom: 2rem;
    }

    /* Improve card appearance */
    .card {
        margin-bottom: 1.5rem;
    }
}

/* Feature Section Mobile Improvements */
@media (max-width: 767.98px) {
    .feature-section {
        padding: 3rem 0;
    }

    .section-title {
        font-size: 1.75rem;
        text-align: center;
    }

    .section-subtitle {
        font-size: 1rem;
        text-align: center;
        margin-bottom: 2rem;
    }

    /* Improve feature cards on mobile */
    .feature-card {
        margin-bottom: 1.5rem;
    }

    .feature-card .card-body {
        padding: 1.5rem;
    }

    /* Improve feature icons */
    .feature-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
}

/* CTA Section Mobile Improvements */
@media (max-width: 767.98px) {
    .cta-section {
        padding: 3rem 0;
    }

    .cta-card {
        padding: 2rem 1.5rem;
    }

    .cta-card h2 {
        font-size: 1.75rem;
    }

    .cta-card p {
        font-size: 1rem;
    }

    .cta-card .btn-primary {
        width: 100%;
        padding: 0.75rem 1.5rem;
    }
}

/* Footer Mobile Improvements */
@media (max-width: 767.98px) {
    .enterprise-footer {
        margin-top: 3rem;
    }

    .footer-main {
        padding: 3rem 0 2rem;
    }

    .footer-logo {
        justify-content: center;
        margin-bottom: 1rem;
    }

    .footer-tagline {
        text-align: center;
        margin-left: auto;
        margin-right: auto;
    }

    .social-icons {
        justify-content: center;
    }

    .footer-heading {
        margin-top: 2rem;
        text-align: center;
    }

    .footer-heading::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .footer-links {
        text-align: center;
    }

    .contact-info {
        max-width: 300px;
        margin-left: auto;
        margin-right: auto;
    }

    .newsletter-form {
        max-width: 300px;
        margin-left: auto;
        margin-right: auto;
    }
}

/* Preloader Mobile Improvements */
@media (max-width: 767.98px) {
    .preloader-content {
        max-width: 280px;
        padding: 1.5rem;
    }

    .preloader-logo {
        font-size: 3rem;
    }

    .preloader-text {
        font-size: 1.75rem;
    }

    .preloader-spinner {
        width: 50px;
        height: 50px;
    }

    .preloader-progress {
        height: 4px;
    }

    .preloader-status {
        font-size: 0.85rem;
        padding: 0.4rem 0.75rem;
    }
}

/* VPN Modal Mobile Improvements */
@media (max-width: 767.98px) {
    #vpn-modal .modal-content {
        margin: 0.5rem;
    }

    .vpn-status {
        padding: 1.25rem;
        margin-bottom: 1rem;
    }

    .vpn-icon {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }

    .vpn-info h4 {
        font-size: 1.2rem;
    }

    .vpn-info p {
        font-size: 0.9rem;
    }

    .vpn-features {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .vpn-feature {
        padding: 1.25rem;
        flex-direction: row;
        align-items: center;
    }

    .feature-icon-wrapper {
        margin-bottom: 0;
        margin-right: 1rem;
        width: 40px;
        height: 40px;
        flex-shrink: 0;
    }

    .vpn-feature i {
        font-size: 1.25rem;
    }

    .feature-content h5 {
        font-size: 1rem;
        margin-bottom: 0.5rem;
    }

    .feature-content p {
        font-size: 0.85rem;
    }

    .vpn-features-title {
        font-size: 1.1rem;
        margin-bottom: 0.75rem;
    }

    .vpn-modal-footer {
        padding: 1.25rem;
    }

    .vpn-activate-btn {
        padding: 0.75rem 1.25rem;
    }

    .thank-you-message {
        font-size: 0.9rem;
        padding: 0.75rem;
    }
}

/* Ensure all content is visible on small screens */
@media (max-width: 575.98px) {
    /* Adjust container padding */
    .container {
        padding-left: 1.25rem;
        padding-right: 1.25rem;
    }

    /* Ensure buttons are properly sized */
    .btn {
        padding: 0.75rem 1.25rem;
        font-size: 1rem;
    }

    /* Ensure form elements are properly sized */
    .form-control, .form-select {
        font-size: 16px; /* Prevent iOS zoom */
        padding: 0.75rem 1rem;
    }

    /* Ensure text is readable */
    body {
        font-size: 16px;
    }

    /* Improve spacing in cards */
    .card-body {
        padding: 1.25rem;
    }

    /* Ensure badges are properly sized */
    .badge {
        font-size: 0.75rem;
        padding: 0.4rem 0.75rem;
    }

    /* Ensure icons are visible */
    .feature-icon {
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }

    /* Improve spacing in feature cards */
    .feature-card .card-body {
        padding: 1.25rem;
    }

    /* Ensure CTA is properly sized */
    .cta-card {
        padding: 1.75rem 1.25rem;
    }

    /* Ensure footer is properly spaced */
    .footer-main {
        padding: 2.5rem 0 2rem;
    }
}

/* Landscape mode optimizations */
@media (max-width: 767.98px) and (orientation: landscape) {
    .navbar-collapse {
        max-height: 70vh;
    }

    .hero-section {
        padding: 1.5rem 0;
    }

    .feature-section {
        padding: 2rem 0;
    }

    .cta-section {
        padding: 2rem 0;
    }

    .footer-main {
        padding: 2rem 0 1.5rem;
    }
}

/* Fix for iOS input zoom */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
    select,
    textarea,
    input[type="text"],
    input[type="password"],
    input[type="datetime"],
    input[type="datetime-local"],
    input[type="date"],
    input[type="month"],
    input[type="time"],
    input[type="week"],
    input[type="number"],
    input[type="email"],
    input[type="url"],
    input[type="search"],
    input[type="tel"],
    input[type="color"] {
        font-size: 16px;
    }
}

/* Ensure touch targets are large enough */
@media (max-width: 767.98px) {
    .nav-link,
    .enhanced-dropdown-item,
    .btn,
    .footer-link,
    .social-icon,
    .footer-bottom-link {
        min-height: 44px; /* Apple's recommended minimum touch target size */
        display: flex;
        align-items: center;
    }
}
