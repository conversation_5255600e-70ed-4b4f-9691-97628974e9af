# Generated by Django 5.1.7 on 2025-05-14 12:37

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('qrcode_app', '0003_qrcode_file_name_qrcode_file_size_qrcode_file_type_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='QRCodeAnalytics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_scans', models.PositiveIntegerField(default=0)),
                ('unique_scans', models.PositiveIntegerField(default=0)),
                ('last_scanned', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('qr_code', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='analytics', to='qrcode_app.qrcode')),
            ],
            options={
                'verbose_name': 'QR Code Analytics',
                'verbose_name_plural': 'QR Code Analytics',
            },
        ),
        migrations.CreateModel(
            name='QRCodeScan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True, null=True)),
                ('referrer', models.URLField(blank=True, null=True)),
                ('location', models.CharField(blank=True, max_length=255, null=True)),
                ('device_type', models.CharField(blank=True, max_length=50, null=True)),
                ('os', models.CharField(blank=True, max_length=50, null=True)),
                ('browser', models.CharField(blank=True, max_length=50, null=True)),
                ('scanned_at', models.DateTimeField(auto_now_add=True)),
                ('qr_code', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='scans', to='qrcode_app.qrcode')),
            ],
            options={
                'ordering': ['-scanned_at'],
            },
        ),
    ]
