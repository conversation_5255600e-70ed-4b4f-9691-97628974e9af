# Generated by Django 4.2.7 on 2025-05-13 11:13

from django.db import migrations, models
import qrcode_app.models


class Migration(migrations.Migration):

    dependencies = [
        ('qrcode_app', '0002_alter_qrcode_options'),
    ]

    operations = [
        migrations.AddField(
            model_name='qrcode',
            name='file_name',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='qrcode',
            name='file_size',
            field=models.PositiveIntegerField(blank=True, help_text='File size in bytes', null=True),
        ),
        migrations.AddField(
            model_name='qrcode',
            name='file_type',
            field=models.CharField(blank=True, help_text='MIME type of the file', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='qrcode',
            name='uploaded_file',
            field=models.FileField(blank=True, help_text='Upload a file for PDF, Image, Document, Audio, or Video QR codes', null=True, upload_to=qrcode_app.models.file_upload_path),
        ),
        migrations.AlterField(
            model_name='qrcode',
            name='qr_type',
            field=models.CharField(choices=[('URL', 'URL'), ('TEXT', 'Text'), ('VCARD', 'vCard'), ('WIFI', 'WiFi'), ('EMAIL', 'Email'), ('PHONE', 'Phone'), ('LOCATION', 'Location'), ('PDF', 'PDF Document'), ('IMAGE', 'Image'), ('DOCUMENT', 'Document'), ('AUDIO', 'Audio File'), ('VIDEO', 'Video File'), ('CALENDAR', 'Calendar Event'), ('PRODUCT', 'Product Information'), ('APP', 'App Download')], default='URL', max_length=20),
        ),
    ]
