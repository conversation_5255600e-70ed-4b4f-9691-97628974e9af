{% extends 'base.html' %}
{% load static %}

{% block title %}{{ ad.title }} - Ad Details{% endblock %}

{% block extra_css %}
<!-- Google Fonts - Poppins and Montserrat -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

<!-- Include common ads CSS -->
{% include 'ads/includes/ads_common_css.html' %}

<style>
    /* Ultra-Premium Enterprise Ad Detail Styling */
    body {
        background: linear-gradient(135deg, #000000 0%, #0a0a0a 10%, #1a1a2e 25%, #16213e 40%, #0f3460 60%, #533483 80%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        position: relative;
        overflow-x: hidden;
    }

    /* Premium animated background with enterprise patterns */
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 45% 55%, rgba(102, 126, 234, 0.7) 0%, transparent 40%),
            radial-gradient(circle at 55% 45%, rgba(255, 255, 255, 0.3) 0%, transparent 35%),
            radial-gradient(circle at 35% 65%, rgba(118, 75, 162, 0.6) 0%, transparent 45%),
            radial-gradient(circle at 65% 35%, rgba(83, 52, 131, 0.5) 0%, transparent 30%),
            radial-gradient(circle at 25% 75%, rgba(102, 126, 234, 0.4) 0%, transparent 40%);
        z-index: -1;
        animation: enterpriseDetailFloat 80s ease-in-out infinite;
    }

    @keyframes enterpriseDetailFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        25% { transform: translateY(-55px) rotate(4.5deg); }
        50% { transform: translateY(-45px) rotate(-4.5deg); }
        75% { transform: translateY(-65px) rotate(2.2deg); }
    }

    /* Enterprise Container */
    .enterprise-container {
        position: relative;
        z-index: 1;
        padding: 2rem 0;
        min-height: 100vh;
    }

    /* Premium Header Section */
    .enterprise-header {
        background: linear-gradient(135deg, rgba(26, 35, 126, 0.95) 0%, rgba(40, 53, 147, 0.95) 50%, rgba(63, 81, 181, 0.95) 100%);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2.5rem;
        margin-bottom: 2rem;
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        position: relative;
        overflow: hidden;
        animation: slideInDown 0.8s ease-out;
    }

    .enterprise-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
        animation: shimmer 3s ease-in-out infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    .enterprise-title {
        font-family: 'Montserrat', sans-serif !important;
        font-size: 2.5rem;
        font-weight: 700;
        color: #ffffff;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 2;
    }

    .enterprise-subtitle {
        font-size: 1.1rem;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 1.5rem;
        font-weight: 400;
        position: relative;
        z-index: 2;
    }

    .enterprise-breadcrumb {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        margin-bottom: 1.5rem;
        position: relative;
        z-index: 2;
    }

    .enterprise-breadcrumb .breadcrumb {
        margin: 0;
        background: transparent;
        padding: 0;
    }

    .enterprise-breadcrumb .breadcrumb-item a {
        color: rgba(255, 255, 255, 0.8);
        text-decoration: none;
        font-weight: 500;
    }

    .enterprise-breadcrumb .breadcrumb-item a:hover {
        color: white;
    }

    .enterprise-breadcrumb .breadcrumb-item.active {
        color: white;
        font-weight: 600;
    }

    /* Premium Status Badges */
    .enterprise-status {
        display: inline-flex;
        align-items: center;
        padding: 0.6rem 1.2rem;
        border-radius: 12px;
        font-weight: 600;
        letter-spacing: 0.5px;
        text-transform: uppercase;
        font-size: 0.75rem;
        position: relative;
        z-index: 2;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .status-draft {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        color: white;
    }

    .status-pending {
        background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
        color: #212529;
    }

    .status-approved {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        color: white;
    }

    .status-active {
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
        color: white;
    }

    .status-paused {
        background: linear-gradient(135deg, #6f42c1 0%, #59359a 100%);
        color: white;
    }

    .status-rejected {
        background: linear-gradient(135deg, #dc3545 0%, #bd2130 100%);
        color: white;
    }

    .status-expired {
        background: linear-gradient(135deg, #343a40 0%, #23272b 100%);
        color: white;
    }

    /* Premium Enterprise Cards */
    .enterprise-card {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        overflow: hidden;
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.15),
            0 0 0 1px rgba(255, 255, 255, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        animation: slideInUp 0.8s ease-out 0.2s both;
        margin-bottom: 2rem;
        transition: all 0.3s ease;
    }

    .enterprise-card:hover {
        transform: translateY(-5px);
        box-shadow:
            0 35px 70px rgba(0, 0, 0, 0.2),
            0 0 0 1px rgba(255, 255, 255, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.4);
    }

    .enterprise-card-header {
        background: linear-gradient(135deg, #1a237e 0%, #283593 100%);
        color: white;
        padding: 1.5rem 2rem;
        font-weight: 600;
        font-size: 1.2rem;
        position: relative;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .enterprise-card-header::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    }

    .enterprise-card-title {
        font-family: 'Montserrat', sans-serif !important;
        font-weight: 700;
        margin: 0;
        position: relative;
        z-index: 2;
    }

    .enterprise-card-body {
        padding: 2rem;
    }

    /* Premium Statistics */
    .enterprise-stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .enterprise-stat-card {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
        border-radius: 16px;
        padding: 1.5rem;
        transition: all 0.3s ease;
        border: 2px solid transparent;
        position: relative;
        overflow: hidden;
        text-align: center;
    }

    .enterprise-stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
        transition: left 0.5s;
    }

    .enterprise-stat-card:hover::before {
        left: 100%;
    }

    .enterprise-stat-card:hover {
        transform: translateY(-5px);
        border-color: rgba(102, 126, 234, 0.3);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    }

    .enterprise-stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        margin: 0 auto 1rem;
        position: relative;
        z-index: 2;
    }

    .enterprise-stat-icon.impressions {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
    }

    .enterprise-stat-icon.clicks {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);
    }

    .enterprise-stat-icon.ctr {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        box-shadow: 0 8px 20px rgba(245, 158, 11, 0.3);
    }

    .enterprise-stat-value {
        font-size: 2rem;
        font-weight: 700;
        color: #1a237e;
        margin-bottom: 0.5rem;
    }

    .enterprise-stat-label {
        font-weight: 600;
        color: #667eea;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    /* Premium Action Buttons */
    .enterprise-actions {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-top: 2rem;
    }

    .enterprise-action-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 12px;
        padding: 0.8rem 2rem;
        color: white;
        font-weight: 600;
        font-size: 1rem;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        position: relative;
        overflow: hidden;
        cursor: pointer;
    }

    .enterprise-action-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .enterprise-action-btn:hover::before {
        left: 100%;
    }

    .enterprise-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    .enterprise-action-btn.secondary {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        box-shadow: 0 8px 25px rgba(108, 117, 125, 0.3);
    }

    .enterprise-action-btn.secondary:hover {
        box-shadow: 0 12px 35px rgba(108, 117, 125, 0.4);
    }

    .enterprise-action-btn.danger {
        background: linear-gradient(135deg, #dc3545 0%, #bd2130 100%);
        box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
    }

    .enterprise-action-btn.danger:hover {
        box-shadow: 0 12px 35px rgba(220, 53, 69, 0.4);
    }

    .enterprise-action-btn.success {
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
        box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
    }

    .enterprise-action-btn.success:hover {
        box-shadow: 0 12px 35px rgba(40, 167, 69, 0.4);
    }

    .enterprise-action-btn.warning {
        background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
        box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3);
        color: #212529;
    }

    .enterprise-action-btn.warning:hover {
        box-shadow: 0 12px 35px rgba(255, 193, 7, 0.4);
        color: #212529;
    }

    .ad-content {
        margin-bottom: 20px;
        line-height: 1.6;
    }

    .ad-media {
        margin-bottom: 20px;
        border-radius: 8px;
        overflow: hidden;
    }

    .ad-media img {
        width: 100%;
        height: auto;
        object-fit: cover;
    }

    .ad-details-list {
        list-style: none;
        padding: 0;
        margin: 0 0 20px;
    }

    .ad-details-item {
        display: flex;
        margin-bottom: 15px;
        border-bottom: 1px solid #f0f0f0;
        padding-bottom: 15px;
    }

    .ad-details-item:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }

    .ad-details-label {
        width: 150px;
        font-weight: 600;
        color: #495057;
    }

    .ad-details-value {
        flex: 1;
        color: #212529;
    }

    .ad-stats {
        display: flex;
        flex-wrap: wrap;
        margin: 0 -10px 20px;
    }

    .stat-card {
        flex: 1;
        min-width: 120px;
        margin: 10px;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 8px;
        text-align: center;
    }

    .stat-value {
        font-size: 24px;
        font-weight: 700;
        color: #212529;
        margin-bottom: 5px;
    }

    .stat-label {
        font-size: 12px;
        color: #6c757d;
        text-transform: uppercase;
    }

    .action-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 30px;
    }

    .action-button {
        padding: 10px 20px;
        border-radius: 6px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    .action-button i {
        margin-right: 8px;
    }

    .primary-button {
        background-color: #007bff;
        color: white;
        border: none;
    }

    .primary-button:hover {
        background-color: #0069d9;
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0, 105, 217, 0.3);
    }

    .secondary-button {
        background-color: #f8f9fa;
        color: #212529;
        border: 1px solid #dee2e6;
    }

    .secondary-button:hover {
        background-color: #e9ecef;
        color: #212529;
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .danger-button {
        background-color: #dc3545;
        color: white;
        border: none;
    }

    .danger-button:hover {
        background-color: #c82333;
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
    }

    .success-button {
        background-color: #28a745;
        color: white;
        border: none;
    }

    .success-button:hover {
        background-color: #218838;
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
    }

    .warning-button {
        background-color: #ffc107;
        color: #212529;
        border: none;
    }

    .warning-button:hover {
        background-color: #e0a800;
        color: #212529;
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(255, 193, 7, 0.3);
    }

    /* Mobile responsiveness */
    @media (max-width: 768px) {
        .ad-detail-container {
            padding: 20px 0;
        }

        .ad-header {
            margin-bottom: 20px;
        }

        .ad-title {
            font-size: 1.75rem;
            margin-bottom: 10px;
        }

        .ad-meta {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;
        }

        .ad-details-item {
            flex-direction: column;
        }

        .ad-details-label {
            width: 100%;
            margin-bottom: 5px;
        }

        .action-buttons {
            flex-direction: column;
            gap: 12px;
        }

        .action-button {
            width: 100%;
            padding: 12px 20px;
            font-size: 1rem;
        }

        .ad-card {
            padding: 20px;
            margin-bottom: 20px;
        }

        .card-header {
            padding-bottom: 12px;
            margin-bottom: 15px;
        }

        .ad-stats {
            margin: 0 -5px 15px;
        }

        .stat-card {
            margin: 5px;
            padding: 12px;
        }

        .stat-value {
            font-size: 20px;
        }

        .breadcrumb {
            font-size: 0.85rem;
            margin-bottom: 15px;
            white-space: nowrap;
            overflow-x: auto;
            padding-bottom: 5px;
        }

        .alert {
            padding: 12px;
            font-size: 0.9rem;
        }
    }

    /* Enhanced stat cards */
    .stat-card {
        flex: 1;
        min-width: 120px;
        margin: 10px;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 12px;
        text-align: center;
        box-shadow: 0 4px 15px rgba(0,0,0,0.03);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.08);
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, rgba(0, 123, 255, 0.1), rgba(0, 123, 255, 0.2));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 15px;
        color: #007bff;
        font-size: 1.25rem;
    }

    .stat-card:nth-child(2) .stat-icon {
        background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(40, 167, 69, 0.2));
        color: #28a745;
    }

    .stat-card:nth-child(3) .stat-icon {
        background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.2));
        color: #ffc107;
    }

    .stat-value {
        font-size: 28px;
        font-weight: 700;
        color: #212529;
        margin-bottom: 5px;
    }

    .stat-label {
        font-size: 14px;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 10px;
    }

    .stat-progress {
        margin-top: 10px;
    }

    .progress {
        height: 6px;
        border-radius: 3px;
        background-color: rgba(0,0,0,0.05);
        overflow: hidden;
    }

    /* Ad Timeline Styles */
    .ad-timeline {
        position: relative;
        padding: 20px 0;
        margin: 0 20px;
    }

    .ad-timeline:before {
        content: '';
        position: absolute;
        top: 0;
        left: 16px;
        height: 100%;
        width: 4px;
        background: #e9ecef;
        border-radius: 2px;
    }

    .timeline-item {
        position: relative;
        padding-left: 45px;
        margin-bottom: 30px;
    }

    .timeline-item:last-child {
        margin-bottom: 0;
    }

    .timeline-icon {
        position: absolute;
        left: 0;
        top: 0;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background-color: #e9ecef;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #adb5bd;
        z-index: 1;
        transition: all 0.3s ease;
    }

    .timeline-content {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.03);
        transition: all 0.3s ease;
    }

    .timeline-title {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 5px;
        color: #495057;
    }

    .timeline-date {
        font-size: 12px;
        color: #6c757d;
        margin-bottom: 5px;
    }

    .timeline-text {
        font-size: 14px;
        color: #6c757d;
        margin-bottom: 0;
    }

    /* Active timeline item */
    .timeline-item.active .timeline-icon {
        background-color: #007bff;
        color: white;
        box-shadow: 0 0 0 4px rgba(0, 123, 255, 0.2);
    }

    .timeline-item.active .timeline-content {
        background-color: rgba(0, 123, 255, 0.05);
        border-left: 3px solid #007bff;
    }

    .timeline-item.active .timeline-title {
        color: #007bff;
    }

    /* Rejected timeline item */
    .timeline-item.rejected .timeline-icon {
        background-color: #dc3545;
        color: white;
        box-shadow: 0 0 0 4px rgba(220, 53, 69, 0.2);
    }

    .timeline-item.rejected .timeline-content {
        background-color: rgba(220, 53, 69, 0.05);
        border-left: 3px solid #dc3545;
    }

    .timeline-item.rejected .timeline-title {
        color: #dc3545;
    }

    /* Countdown Timer Styles */
    .countdown-container {
        padding: 20px;
        text-align: center;
    }

    .countdown-message {
        font-size: 16px;
        color: #495057;
        margin-bottom: 15px;
    }

    .countdown-timer {
        display: flex;
        justify-content: center;
        gap: 15px;
        margin-bottom: 20px;
    }

    .countdown-item {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 10px;
        padding: 15px 10px;
        min-width: 70px;
        box-shadow: 0 4px 10px rgba(0,0,0,0.05);
        position: relative;
        overflow: hidden;
    }

    .countdown-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 50%;
        background: linear-gradient(to bottom, rgba(255,255,255,0.5), rgba(255,255,255,0));
        pointer-events: none;
    }

    .countdown-value {
        font-size: 24px;
        font-weight: 700;
        color: #007bff;
        margin-bottom: 5px;
    }

    .countdown-label {
        font-size: 12px;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .countdown-progress {
        margin-top: 10px;
    }

    .countdown-progress .progress {
        height: 8px;
        border-radius: 4px;
        background-color: rgba(0,0,0,0.05);
        overflow: hidden;
    }

    /* Share Button Styles */
    .share-button {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        padding: 6px 12px;
        font-size: 14px;
        color: #495057;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 5px;
    }

    .share-button:hover {
        background-color: #e9ecef;
        color: #007bff;
    }

    .share-dropdown-menu {
        border: none;
        border-radius: 10px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        padding: 10px 0;
        min-width: 200px;
    }

    .share-item {
        padding: 10px 15px;
        font-size: 14px;
        color: #495057;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .share-item i {
        font-size: 16px;
        width: 20px;
        text-align: center;
    }

    .share-item:hover {
        background-color: #f8f9fa;
        color: #007bff;
    }

    .share-item.copy-link {
        color: #007bff;
    }

    /* Countdown Animation */
    @keyframes pulse {
        0% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.05);
        }
        100% {
            transform: scale(1);
        }
    }

    .countdown-item.pulse {
        animation: pulse 0.5s ease;
    }

    /* Small mobile devices */
    @media (max-width: 575.98px) {
        .ad-title {
            font-size: 1.5rem;
        }

        .card-title {
            font-size: 1.25rem;
        }

        .ad-card {
            padding: 15px;
        }

        .ad-stats {
            flex-direction: column;
        }

        .stat-card {
            min-width: 100%;
            margin: 5px 0;
        }

        .timeline-item {
            padding-left: 35px;
        }

        .timeline-icon {
            width: 28px;
            height: 28px;
            font-size: 12px;
        }

        .timeline-title {
            font-size: 14px;
        }

        .timeline-date, .timeline-text {
            font-size: 12px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="enterprise-container">
    <div class="container">
        <!-- Premium Header Section -->
        <div class="enterprise-header">
            <nav aria-label="breadcrumb" class="enterprise-breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'index' %}">Home</a></li>
                    <li class="breadcrumb-item"><a href="/ads/">Ads Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="/ads/list/">My Ads</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ ad.title }}</li>
                </ol>
            </nav>

            <div class="text-center">
                <h1 class="enterprise-title">{{ ad.title }}</h1>
                <p class="enterprise-subtitle">Comprehensive ad details and performance analytics</p>

                <div class="d-flex justify-content-center align-items-center gap-3 mt-3">
                    <span class="text-white-50">
                        <i class="fas fa-calendar-alt me-1"></i>
                        Created: {{ ad.created_at|date:"M d, Y" }}
                    </span>
                    <span class="text-white-50">
                        <i class="fas fa-tag me-1"></i>
                        Type: {{ ad.ad_type.name }}
                    </span>
                    <span class="text-white-50">
                        <i class="fas fa-user me-1"></i>
                        By: {{ ad.user.username }}
                    </span>
                    <span class="enterprise-status status-{{ ad.status }}">
                        {{ ad.get_status_display }}
                    </span>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <!-- Premium Ad Content Card -->
                <div class="enterprise-card">
                    <div class="enterprise-card-header">
                        <h3 class="enterprise-card-title">
                            <i class="fas fa-file-alt me-2"></i>Ad Content
                        </h3>
                    </div>
                    <div class="enterprise-card-body">
                        {% if ad.media %}
                        <div class="ad-media mb-3">
                            <img src="{{ ad.media.url }}" alt="{{ ad.title }}" class="img-fluid rounded">
                        </div>
                        {% endif %}

                        <div class="ad-content mb-3">
                            {{ ad.content|linebreaks }}
                        </div>

                        {% if ad.cta_link %}
                        <div class="text-center">
                            <a href="{{ ad.cta_link }}" target="_blank" class="enterprise-action-btn">
                                <i class="fas fa-external-link-alt"></i> Visit Website
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Premium Performance Metrics Card -->
                <div class="enterprise-card">
                    <div class="enterprise-card-header">
                        <h3 class="enterprise-card-title">
                            <i class="fas fa-chart-line me-2"></i>Performance Metrics
                        </h3>
                        <span class="badge bg-light text-dark">Last 7 Days</span>
                    </div>
                    <div class="enterprise-card-body">
                        <div class="enterprise-stats-grid">
                            <div class="enterprise-stat-card">
                                <div class="enterprise-stat-icon impressions">
                                    <i class="fas fa-eye"></i>
                                </div>
                                <div class="enterprise-stat-value">{{ ad.impressions|default:"0" }}</div>
                                <div class="enterprise-stat-label">Impressions</div>
                            </div>
                            <div class="enterprise-stat-card">
                                <div class="enterprise-stat-icon clicks">
                                    <i class="fas fa-mouse-pointer"></i>
                                </div>
                                <div class="enterprise-stat-value">{{ ad.clicks|default:"0" }}</div>
                                <div class="enterprise-stat-label">Clicks</div>
                            </div>
                            <div class="enterprise-stat-card">
                                <div class="enterprise-stat-icon ctr">
                                    <i class="fas fa-percentage"></i>
                                </div>
                                <div class="enterprise-stat-value">
                                    {% if ad.impressions > 0 %}
                                        {{ ad.clicks|floatformat:2 }}%
                                    {% else %}
                                        0.00%
                                    {% endif %}
                                </div>
                                <div class="enterprise-stat-label">CTR</div>
                            </div>
                        </div>

                        <div class="text-center mt-4">
                            <a href="/ads/analytics/{{ ad.slug }}/" class="enterprise-action-btn">
                                <i class="fas fa-chart-bar"></i> View Detailed Analytics
                            </a>
                        </div>
                    </div>
                </div>

            <!-- Ad Status Timeline -->
            {% if is_owner %}
            <div class="ads-card animate__animated animate__fadeIn" style="animation-delay: 0.2s;">
                <div class="ads-card-header">
                    <h3 class="ads-card-title">Ad Journey</h3>
                </div>

                <div class="ad-timeline">
                    <div class="timeline-item {% if ad.status == 'draft' or ad.status == 'pending' or ad.status == 'approved' or ad.status == 'active' or ad.status == 'paused' or ad.status == 'expired' %}active{% endif %}">
                        <div class="timeline-icon">
                            <i class="fas fa-pencil-alt"></i>
                        </div>
                        <div class="timeline-content">
                            <h4 class="timeline-title">Draft</h4>
                            <p class="timeline-date">{{ ad.created_at|date:"M d, Y" }}</p>
                            <p class="timeline-text">Ad created and saved as draft</p>
                        </div>
                    </div>

                    <div class="timeline-item {% if ad.status == 'pending' or ad.status == 'approved' or ad.status == 'active' or ad.status == 'paused' or ad.status == 'expired' %}active{% elif ad.status == 'rejected' %}rejected{% endif %}">
                        <div class="timeline-icon">
                            <i class="fas fa-paper-plane"></i>
                        </div>
                        <div class="timeline-content">
                            <h4 class="timeline-title">Submitted</h4>
                            <p class="timeline-date">{% if ad.status != 'draft' %}{{ ad.updated_at|date:"M d, Y" }}{% else %}Pending{% endif %}</p>
                            <p class="timeline-text">Ad submitted for approval</p>
                        </div>
                    </div>

                    <div class="timeline-item {% if ad.status == 'approved' or ad.status == 'active' or ad.status == 'paused' or ad.status == 'expired' %}active{% elif ad.status == 'rejected' %}rejected{% endif %}">
                        <div class="timeline-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="timeline-content">
                            <h4 class="timeline-title">Approved</h4>
                            <p class="timeline-date">{% if ad.status == 'approved' or ad.status == 'active' or ad.status == 'paused' or ad.status == 'expired' %}{{ ad.updated_at|date:"M d, Y" }}{% else %}Pending{% endif %}</p>
                            <p class="timeline-text">Ad approved by admin</p>
                        </div>
                    </div>

                    <div class="timeline-item {% if ad.status == 'active' or ad.status == 'paused' or ad.status == 'expired' %}active{% endif %}">
                        <div class="timeline-icon">
                            <i class="fas fa-play-circle"></i>
                        </div>
                        <div class="timeline-content">
                            <h4 class="timeline-title">Active</h4>
                            <p class="timeline-date">{% if ad.status == 'active' or ad.status == 'paused' or ad.status == 'expired' %}{{ ad.start_date|date:"M d, Y" }}{% else %}Pending{% endif %}</p>
                            <p class="timeline-text">Ad is live and running</p>
                        </div>
                    </div>

                    <div class="timeline-item {% if ad.status == 'expired' %}active{% endif %}">
                        <div class="timeline-icon">
                            <i class="fas fa-flag-checkered"></i>
                        </div>
                        <div class="timeline-content">
                            <h4 class="timeline-title">Completed</h4>
                            <p class="timeline-date">{% if ad.status == 'expired' %}{{ ad.end_date|date:"M d, Y" }}{% else %}{{ ad.end_date|date:"M d, Y" }} (Scheduled){% endif %}</p>
                            <p class="timeline-text">Ad campaign completed</p>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        <div class="col-lg-4">
            {% if ad.status == 'active' %}
            <div class="ads-card animate__animated animate__fadeIn">
                <div class="ads-card-header">
                    <h3 class="ads-card-title">Campaign Timer</h3>
                </div>
                <div class="countdown-container">
                    <div class="countdown-message">Your ad will be active for:</div>
                    <div class="countdown-timer" id="adCountdown">
                        <div class="countdown-item">
                            <div class="countdown-value" id="countdown-days">--</div>
                            <div class="countdown-label">Days</div>
                        </div>
                        <div class="countdown-item">
                            <div class="countdown-value" id="countdown-hours">--</div>
                            <div class="countdown-label">Hours</div>
                        </div>
                        <div class="countdown-item">
                            <div class="countdown-value" id="countdown-minutes">--</div>
                            <div class="countdown-label">Minutes</div>
                        </div>
                        <div class="countdown-item">
                            <div class="countdown-value" id="countdown-seconds">--</div>
                            <div class="countdown-label">Seconds</div>
                        </div>
                    </div>
                    <div class="countdown-progress">
                        <div class="progress">
                            <div class="progress-bar bg-success" role="progressbar" style="width: 0%" id="countdown-progress"></div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <div class="ads-card animate__animated animate__fadeIn">
                <div class="ads-card-header d-flex justify-content-between align-items-center">
                    <h3 class="ads-card-title">Ad Details</h3>
                    <div class="share-dropdown dropdown">
                        <button class="share-button" id="shareDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-share-alt"></i> Share
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end share-dropdown-menu" aria-labelledby="shareDropdown">
                            <li><a class="dropdown-item share-item" href="https://wa.me/?text={{ request.build_absolute_uri }}" target="_blank"><i class="fab fa-whatsapp"></i> WhatsApp</a></li>
                            <li><a class="dropdown-item share-item" href="https://www.facebook.com/sharer/sharer.php?u={{ request.build_absolute_uri }}" target="_blank"><i class="fab fa-facebook"></i> Facebook</a></li>
                            <li><a class="dropdown-item share-item" href="https://twitter.com/intent/tweet?url={{ request.build_absolute_uri }}&text=Check out this ad: {{ ad.title }}" target="_blank"><i class="fab fa-twitter"></i> Twitter</a></li>
                            <li><a class="dropdown-item share-item" href="https://www.linkedin.com/shareArticle?mini=true&url={{ request.build_absolute_uri }}&title={{ ad.title }}" target="_blank"><i class="fab fa-linkedin"></i> LinkedIn</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item share-item copy-link" href="#" data-url="{{ request.build_absolute_uri }}"><i class="fas fa-copy"></i> Copy Link</a></li>
                        </ul>
                    </div>
                </div>

                <ul class="ad-details-list">
                    <li class="ad-details-item">
                        <div class="ad-details-label">Status</div>
                        <div class="ad-details-value">
                            <span class="ad-status status-{{ ad.status }}">
                                {{ ad.get_status_display }}
                            </span>
                        </div>
                    </li>
                    <li class="ad-details-item">
                        <div class="ad-details-label">Start Date</div>
                        <div class="ad-details-value">{{ ad.start_date|date:"M d, Y H:i" }}</div>
                    </li>
                    <li class="ad-details-item">
                        <div class="ad-details-label">End Date</div>
                        <div class="ad-details-value">{{ ad.end_date|date:"M d, Y H:i" }}</div>
                    </li>
                    <li class="ad-details-item">
                        <div class="ad-details-label">Duration</div>
                        <div class="ad-details-value">
                            {% with duration=ad.end_date|timeuntil:ad.start_date %}
                                {{ duration }}
                            {% endwith %}
                        </div>
                    </li>
                    <li class="ad-details-item">
                        <div class="ad-details-label">Campaign</div>
                        <div class="ad-details-value">
                            {% if ad.campaign %}
                                <a href="{% url 'ads:campaign_detail' ad.campaign.slug %}" class="text-primary">
                                    {{ ad.campaign.name }}
                                </a>
                            {% else %}
                                <span class="text-muted">No Campaign (Individual Ad)</span>
                            {% endif %}
                        </div>
                    </li>
                    <li class="ad-details-item">
                        <div class="ad-details-label">Ad Placement</div>
                        <div class="ad-details-value">{{ ad.ad_location.name|default:"Standard placement" }}</div>
                    </li>
                    <li class="ad-details-item">
                        <div class="ad-details-label">Geographic Target</div>
                        <div class="ad-details-value">{{ ad.target_location|default:"Not specified" }}</div>
                    </li>
                    <li class="ad-details-item">
                        <div class="ad-details-label">Target Audience</div>
                        <div class="ad-details-value">{{ ad.target_audience|default:"Not specified" }}</div>
                    </li>
                    <li class="ad-details-item">
                        <div class="ad-details-label">Est. Daily Views</div>
                        <div class="ad-details-value">{{ ad.ad_location.daily_impressions|default:"0" }} views</div>
                    </li>
                    <li class="ad-details-item">
                        <div class="ad-details-label">AI Content</div>
                        <div class="ad-details-value">{{ ad.requires_ai|yesno:"Yes,No" }}</div>
                    </li>
                    <li class="ad-details-item">
                        <div class="ad-details-label">Social Media</div>
                        <div class="ad-details-value">{{ ad.wants_social|yesno:"Yes,No" }}</div>
                    </li>
                    <li class="ad-details-item">
                        <div class="ad-details-label">Base Price</div>
                        <div class="ad-details-value">{{ ad.base_pricing }} KSH</div>
                    </li>
                    <li class="ad-details-item">
                        <div class="ad-details-label">Location Multiplier</div>
                        <div class="ad-details-value">x{{ ad.ad_location.price_multiplier|default:"1.0" }}</div>
                    </li>
                    <li class="ad-details-item">
                        <div class="ad-details-label">Final Price</div>
                        <div class="ad-details-value">{{ ad.final_pricing }} KSH</div>
                    </li>
                </ul>
            </div>

            <div class="action-buttons">
                {% if is_owner %}
                    {% if ad.status == 'draft' %}
                        <a href="/ads/edit/{{ ad.slug }}/" class="action-button primary-button">
                            <i class="fas fa-edit"></i> Edit Ad
                        </a>
                        <a href="/ads/submit/{{ ad.slug }}/" class="action-button success-button">
                            <i class="fas fa-paper-plane"></i> Submit for Approval
                        </a>
                    {% elif ad.status == 'pending' %}
                        <div class="alert alert-warning">
                            <i class="fas fa-clock"></i> Your ad is pending approval.
                            <a href="{% url 'ads:ad_submitted' ad.slug %}" class="alert-link">View submission details</a>
                        </div>
                    {% elif ad.status == 'approved' %}
                        {% if has_paid_transaction %}
                            <div class="alert alert-info mb-3">
                                <i class="fas fa-info-circle"></i> Your payment has been received and is awaiting approval.
                                {% if latest_transaction %}
                                    <p class="mt-2">Payment method: {{ latest_transaction.get_payment_gateway_display }}</p>
                                    <p>Transaction ID: {{ latest_transaction.transaction_id|default:"Processing" }}</p>
                                    <p>Amount: {{ latest_transaction.amount }} KSH</p>
                                    <p>Date: {{ latest_transaction.timestamp|date:"M d, Y H:i" }}</p>
                                {% endif %}
                            </div>
                        {% else %}
                            <a href="/ads/payment/{{ ad.slug }}/" class="action-button success-button">
                                <i class="fas fa-credit-card"></i> Pay Now
                            </a>
                        {% endif %}
                    {% elif ad.status == 'active' %}
                        <a href="/ads/pause/{{ ad.slug }}/" class="action-button warning-button">
                            <i class="fas fa-pause"></i> Pause Ad
                        </a>
                    {% elif ad.status == 'paused' %}
                        <a href="/ads/resume/{{ ad.slug }}/" class="action-button success-button">
                            <i class="fas fa-play"></i> Resume Ad
                        </a>
                    {% elif ad.status == 'rejected' %}
                        <div class="alert alert-danger mb-3">
                            <i class="fas fa-exclamation-triangle"></i> Your ad was rejected.
                            {% if ad.rejection_reason %}
                                <p class="mt-2">Reason: {{ ad.rejection_reason }}</p>
                            {% endif %}
                        </div>
                        <a href="/ads/edit/{{ ad.slug }}/" class="action-button primary-button">
                            <i class="fas fa-edit"></i> Edit and Resubmit
                        </a>
                    {% endif %}

                    <a href="/ads/list/" class="action-button secondary-button">
                        <i class="fas fa-arrow-left"></i> Back to My Ads
                    </a>

                    {% if ad.status != 'active' and ad.status != 'pending' and ad.status != 'approved' %}
                        <a href="/ads/delete/{{ ad.slug }}/" class="action-button danger-button">
                            <i class="fas fa-trash"></i> Delete Ad
                        </a>
                    {% endif %}
                {% else %}
                    <!-- Viewer is not the owner -->
                    <div class="alert alert-info mb-3">
                        <i class="fas fa-info-circle"></i> You are viewing an advertisement by {{ ad.user.username }}.
                    </div>

                    <a href="javascript:history.back()" class="action-button secondary-button">
                        <i class="fas fa-arrow-left"></i> Go Back
                    </a>

                    {% if ad.cta_link %}
                    <a href="{{ ad.cta_link }}" target="_blank" class="action-button primary-button">
                        <i class="fas fa-external-link-alt"></i> Visit Website
                    </a>
                    {% endif %}
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add hover effects for desktop only
        if (window.innerWidth > 768) {
            const adCards = document.querySelectorAll('.ad-card');
            adCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                    this.style.boxShadow = '0 8px 30px rgba(0,0,0,0.1)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '0 4px 20px rgba(0,0,0,0.05)';
                });
            });
        }

        // Mobile-specific enhancements
        if (window.innerWidth <= 768) {
            // Add touch feedback for cards
            const adCards = document.querySelectorAll('.ad-card');
            adCards.forEach(card => {
                card.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.98)';
                    this.style.transition = 'transform 0.2s ease';
                });

                card.addEventListener('touchend', function() {
                    this.style.transform = 'scale(1)';
                });
            });

            // Make action buttons more touch-friendly
            const actionButtons = document.querySelectorAll('.action-button');
            actionButtons.forEach(button => {
                button.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.98)';
                });

                button.addEventListener('touchend', function() {
                    this.style.transform = 'scale(1)';
                });
            });

            // Add scroll to top button for long pages
            const body = document.querySelector('body');
            const scrollTopButton = document.createElement('button');
            scrollTopButton.className = 'btn btn-primary rounded-circle position-fixed';
            scrollTopButton.style.bottom = '80px';
            scrollTopButton.style.right = '20px';
            scrollTopButton.style.width = '45px';
            scrollTopButton.style.height = '45px';
            scrollTopButton.style.display = 'flex';
            scrollTopButton.style.alignItems = 'center';
            scrollTopButton.style.justifyContent = 'center';
            scrollTopButton.style.zIndex = '1000';
            scrollTopButton.style.boxShadow = '0 4px 10px rgba(0,0,0,0.2)';
            scrollTopButton.style.opacity = '0';
            scrollTopButton.style.transition = 'opacity 0.3s ease';
            scrollTopButton.innerHTML = '<i class="fas fa-arrow-up"></i>';

            body.appendChild(scrollTopButton);

            scrollTopButton.addEventListener('click', function() {
                window.scrollTo({top: 0, behavior: 'smooth'});
            });

            window.addEventListener('scroll', function() {
                if (window.scrollY > 300) {
                    scrollTopButton.style.opacity = '1';
                } else {
                    scrollTopButton.style.opacity = '0';
                }
            });
        }

        // Countdown Timer for Active Ads
        {% if ad.status == 'active' %}
        function updateCountdown() {
            const now = new Date();
            const endDate = new Date('{{ ad.end_date|date:"Y-m-d H:i:s" }}');
            const startDate = new Date('{{ ad.start_date|date:"Y-m-d H:i:s" }}');
            const totalDuration = endDate - startDate;
            const timeLeft = endDate - now;

            if (timeLeft <= 0) {
                // Ad has expired
                document.getElementById('countdown-days').textContent = '0';
                document.getElementById('countdown-hours').textContent = '0';
                document.getElementById('countdown-minutes').textContent = '0';
                document.getElementById('countdown-seconds').textContent = '0';
                document.getElementById('countdown-progress').style.width = '100%';
                return;
            }

            // Calculate time units
            const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
            const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

            // Update the countdown display
            document.getElementById('countdown-days').textContent = days;
            document.getElementById('countdown-hours').textContent = hours;
            document.getElementById('countdown-minutes').textContent = minutes;
            document.getElementById('countdown-seconds').textContent = seconds;

            // Calculate and update progress bar
            const elapsed = now - startDate;
            const progressPercentage = Math.min(100, Math.max(0, (elapsed / totalDuration) * 100));
            document.getElementById('countdown-progress').style.width = progressPercentage.toFixed(2) + '%';

            // Add animation to the countdown
            const countdownItems = document.querySelectorAll('.countdown-item');
            countdownItems.forEach(item => {
                item.classList.add('pulse');
                setTimeout(() => {
                    item.classList.remove('pulse');
                }, 1000);
            });
        }

        // Initialize countdown
        updateCountdown();

        // Update countdown every second
        setInterval(updateCountdown, 1000);
        {% endif %}

        // Share functionality
        const copyLinkButton = document.querySelector('.copy-link');
        if (copyLinkButton) {
            copyLinkButton.addEventListener('click', function(e) {
                e.preventDefault();
                const url = this.getAttribute('data-url');

                // Create a temporary input element
                const tempInput = document.createElement('input');
                tempInput.value = url;
                document.body.appendChild(tempInput);

                // Select and copy the URL
                tempInput.select();
                document.execCommand('copy');

                // Remove the temporary input
                document.body.removeChild(tempInput);

                // Show success message
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="fas fa-check"></i> Link Copied!';
                this.style.color = '#28a745';

                // Reset after 2 seconds
                setTimeout(() => {
                    this.innerHTML = originalText;
                    this.style.color = '';
                }, 2000);
            });
        }

        // Add animation to the timeline
        const timelineItems = document.querySelectorAll('.timeline-item');
        timelineItems.forEach((item, index) => {
            item.style.animationDelay = (index * 0.2) + 's';
            item.classList.add('animate__animated', 'animate__fadeInLeft');
        });
    });
</script>
{% endblock %}
