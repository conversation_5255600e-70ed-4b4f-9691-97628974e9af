/* Solutions Pages Styling */

:root {
    --primary-color: #0078d4;
    --secondary-color: #00a2ed;
    --accent-color: #ffb900;
    --dark-color: #1a1a1a;
    --light-color: #f8f9fa;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;
    --border-radius: 8px;
    --border-radius-sm: 4px;
    --border-radius-lg: 12px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --box-shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* Hero Section */
.solution-hero {
    background: linear-gradient(135deg, var(--primary-color), #005a9e);
    color: white;
    padding: 6rem 0;
    position: relative;
    overflow: hidden;
}

.solution-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('../img/patterns/dots-pattern.svg');
    background-size: 30px;
    opacity: 0.1;
    z-index: 1;
}

.solution-hero-content {
    position: relative;
    z-index: 2;
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
    padding: 0 1rem;
}

.solution-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    background: linear-gradient(to right, #ffffff, #e0e0e0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.solution-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-cta {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
}

.hero-cta .btn {
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.hero-cta .btn-primary {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
    color: var(--dark-color);
}

.hero-cta .btn-primary:hover {
    background-color: #e6a800;
    border-color: #e6a800;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(255, 185, 0, 0.3);
}

.hero-cta .btn-outline-light:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(255, 255, 255, 0.1);
}

.solution-hero-image {
    position: relative;
    z-index: 2;
    max-width: 500px;
    margin: 3rem auto 0;
    text-align: center;
}

.solution-hero-image img {
    max-width: 100%;
    height: auto;
    filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.2));
    animation: float 6s ease-in-out infinite;
}

/* Overview Section */
.solution-overview {
    padding: 5rem 0;
    background-color: white;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: var(--dark-color);
}

.section-description {
    font-size: 1.1rem;
    color: var(--gray-700);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.feature-list {
    list-style: none;
    padding: 0;
    margin: 2rem 0;
}

.feature-list li {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.feature-list li i {
    color: var(--primary-color);
    margin-right: 1rem;
    font-size: 1.25rem;
}

.overview-image {
    text-align: center;
}

.overview-image img {
    max-width: 100%;
    height: auto;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-lg);
}

/* Features Section */
.solution-features {
    padding: 5rem 0;
    background-color: var(--gray-100);
}

.feature-card {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--box-shadow);
    height: 100%;
    transition: var(--transition);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    color: white;
    font-size: 1.75rem;
    box-shadow: 0 5px 15px rgba(0, 120, 212, 0.3);
}

.feature-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.feature-card p {
    color: var(--gray-700);
    margin-bottom: 0;
    line-height: 1.6;
}

/* Use Cases Section */
.solution-use-cases {
    padding: 5rem 0;
    background-color: white;
}

.use-case-card {
    display: flex;
    align-items: flex-start;
    margin-bottom: 2.5rem;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    background-color: var(--gray-100);
    transition: var(--transition);
}

.use-case-card:hover {
    background-color: var(--gray-200);
    transform: translateX(5px);
}

.use-case-icon {
    flex-shrink: 0;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin-right: 1.5rem;
    box-shadow: 0 5px 15px rgba(0, 120, 212, 0.2);
}

.use-case-content h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: var(--dark-color);
}

.use-case-content p {
    color: var(--gray-700);
    margin-bottom: 0;
    line-height: 1.6;
}

/* CTA Section */
.solution-cta {
    padding: 5rem 0;
    background: linear-gradient(135deg, var(--primary-color), #005a9e);
    color: white;
    position: relative;
    overflow: hidden;
}

.solution-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('../img/patterns/dots-pattern.svg');
    background-size: 30px;
    opacity: 0.1;
}

.cta-content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
    padding: 0 1rem;
}

.cta-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.cta-content p {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

/* Industry-specific styling */
.corporate-hero {
    background: linear-gradient(135deg, #0078d4, #005a9e);
}

.retail-hero {
    background: linear-gradient(135deg, #7b68ee, #5546d6);
}

.hospitality-hero {
    background: linear-gradient(135deg, #00b4d8, #0077b6);
}

.education-hero {
    background: linear-gradient(135deg, #2ec4b6, #20a39e);
}

.events-hero {
    background: linear-gradient(135deg, #ff6b6b, #ee5253);
}

.marketing-hero {
    background: linear-gradient(135deg, #ff9f1c, #f77f00);
}

/* Animations */
@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0px);
    }
}

/* Responsive Styles */
@media (max-width: 991.98px) {
    .solution-title {
        font-size: 2.5rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .solution-hero {
        padding: 4rem 0;
    }
    
    .solution-overview,
    .solution-features,
    .solution-use-cases,
    .solution-cta {
        padding: 4rem 0;
    }
}

@media (max-width: 767.98px) {
    .solution-title {
        font-size: 2rem;
    }
    
    .solution-subtitle {
        font-size: 1.1rem;
    }
    
    .section-title {
        font-size: 1.75rem;
    }
    
    .hero-cta {
        flex-direction: column;
    }
    
    .hero-cta .btn {
        width: 100%;
    }
    
    .cta-buttons {
        flex-direction: column;
    }
    
    .cta-buttons .btn {
        width: 100%;
    }
}
