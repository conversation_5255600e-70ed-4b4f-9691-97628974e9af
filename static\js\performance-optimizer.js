/**
 * Performance Optimizer
 * Enterprise QR Code Generator
 * 
 * This module provides performance optimizations for QR code generation
 * and handling, including caching, web workers, and image optimization.
 */

// Cache for generated QR codes
const QR_CACHE = new Map();
const CACHE_SIZE_LIMIT = 100; // Maximum number of QR codes to cache

// Web Worker for QR code generation
let qr<PERSON><PERSON>ker;

// Initialize the optimizer
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Web Worker if supported
    if (window.Worker) {
        try {
            qrWorker = new Worker('/static/js/qr-worker.js');
            console.log('QR Code Web Worker initialized');
        } catch (e) {
            console.warn('Failed to initialize QR Code Web Worker:', e);
        }
    }
    
    // Initialize performance monitoring
    initPerformanceMonitoring();
});

/**
 * Generate a QR code with performance optimizations
 * @param {string} data - QR code data
 * @param {Object} options - QR code options
 * @param {Function} callback - Callback function
 * @returns {Promise} - Promise resolving to QR code data URL
 */
async function generateOptimizedQRCode(data, options = {}, callback) {
    // Start performance measurement
    const startTime = performance.now();
    
    // Create a unique key for the QR code based on data and options
    const qrKey = createCacheKey(data, options);
    
    // Check if the QR code is already cached
    if (QR_CACHE.has(qrKey)) {
        const cachedQR = QR_CACHE.get(qrKey);
        
        // Update last accessed time
        cachedQR.lastAccessed = Date.now();
        
        // Log cache hit
        const endTime = performance.now();
        logPerformance('qr_generation', endTime - startTime, true);
        
        // Return cached result
        if (callback) callback(cachedQR.dataURL);
        return Promise.resolve(cachedQR.dataURL);
    }
    
    // Generate QR code using Web Worker if available
    if (qrWorker) {
        return new Promise((resolve, reject) => {
            // Set up message handler
            const messageHandler = function(e) {
                if (e.data.error) {
                    // Worker encountered an error
                    qrWorker.removeEventListener('message', messageHandler);
                    reject(new Error(e.data.error));
                    return;
                }
                
                if (e.data.qrKey === qrKey) {
                    // Remove event listener
                    qrWorker.removeEventListener('message', messageHandler);
                    
                    // Cache the result
                    cacheQRCode(data, e.data.dataURL, options);
                    
                    // Log performance
                    const endTime = performance.now();
                    logPerformance('qr_generation', endTime - startTime, false);
                    
                    // Return result
                    if (callback) callback(e.data.dataURL);
                    resolve(e.data.dataURL);
                }
            };
            
            // Add event listener
            qrWorker.addEventListener('message', messageHandler);
            
            // Send data to worker
            qrWorker.postMessage({
                action: 'generate',
                data: data,
                options: options,
                qrKey: qrKey
            });
        });
    } else {
        // Fallback to synchronous generation
        try {
            // Generate QR code (implementation depends on your QR library)
            const dataURL = await generateQRCode(data, options);
            
            // Cache the result
            cacheQRCode(data, dataURL, options);
            
            // Log performance
            const endTime = performance.now();
            logPerformance('qr_generation', endTime - startTime, false);
            
            // Return result
            if (callback) callback(dataURL);
            return dataURL;
        } catch (error) {
            console.error('Error generating QR code:', error);
            throw error;
        }
    }
}

/**
 * Create a cache key for a QR code
 * @param {string} data - QR code data
 * @param {Object} options - QR code options
 * @returns {string} - Cache key
 */
function createCacheKey(data, options) {
    // Create a string representation of options
    const optionsStr = JSON.stringify(options);
    
    // Combine data and options for the key
    return `${data}|${optionsStr}`;
}

/**
 * Cache a generated QR code
 * @param {string} data - QR code data
 * @param {string} dataURL - QR code data URL
 * @param {Object} options - QR code options
 */
function cacheQRCode(data, dataURL, options = {}) {
    // Create a unique key for the QR code
    const qrKey = createCacheKey(data, options);
    
    // Add to cache
    QR_CACHE.set(qrKey, {
        dataURL,
        createdAt: Date.now(),
        lastAccessed: Date.now()
    });
    
    // Check if cache size exceeds limit
    if (QR_CACHE.size > CACHE_SIZE_LIMIT) {
        // Remove least recently used item
        let oldestKey = null;
        let oldestTime = Infinity;
        
        QR_CACHE.forEach((value, key) => {
            if (value.lastAccessed < oldestTime) {
                oldestTime = value.lastAccessed;
                oldestKey = key;
            }
        });
        
        if (oldestKey) {
            QR_CACHE.delete(oldestKey);
        }
    }
}

/**
 * Clear the QR code cache
 */
function clearQRCache() {
    QR_CACHE.clear();
    console.log('QR code cache cleared');
}

/**
 * Initialize performance monitoring
 */
function initPerformanceMonitoring() {
    // Create performance data store if it doesn't exist
    if (!window.qrPerformanceData) {
        window.qrPerformanceData = {
            qr_generation: [],
            api_requests: [],
            image_processing: []
        };
    }
    
    // Set up periodic reporting
    setInterval(reportPerformanceData, 60000); // Report every minute
}

/**
 * Log performance data
 * @param {string} category - Performance category
 * @param {number} duration - Duration in milliseconds
 * @param {boolean} cached - Whether the result was cached
 */
function logPerformance(category, duration, cached = false) {
    if (!window.qrPerformanceData) return;
    
    // Add data point
    window.qrPerformanceData[category].push({
        timestamp: Date.now(),
        duration: duration,
        cached: cached
    });
    
    // Limit array size
    const maxDataPoints = 1000;
    if (window.qrPerformanceData[category].length > maxDataPoints) {
        window.qrPerformanceData[category] = window.qrPerformanceData[category].slice(-maxDataPoints);
    }
}

/**
 * Report performance data to the server
 */
function reportPerformanceData() {
    if (!window.qrPerformanceData) return;
    
    // Clone the data
    const performanceData = JSON.parse(JSON.stringify(window.qrPerformanceData));
    
    // Clear the data
    for (const category in window.qrPerformanceData) {
        window.qrPerformanceData[category] = [];
    }
    
    // Send to server (implementation depends on your backend)
    // This is just a placeholder
    console.log('Performance data reported:', performanceData);
    
    // In a real implementation, you would send this data to your server
    // fetch('/api/performance-data', {
    //     method: 'POST',
    //     headers: {
    //         'Content-Type': 'application/json'
    //     },
    //     body: JSON.stringify(performanceData)
    // });
}

/**
 * Optimize an image for QR code embedding
 * @param {File|Blob} imageFile - Image file or blob
 * @param {Object} options - Optimization options
 * @returns {Promise<Blob>} - Optimized image blob
 */
async function optimizeImage(imageFile, options = {}) {
    return new Promise((resolve, reject) => {
        // Start performance measurement
        const startTime = performance.now();
        
        // Create image element
        const img = new Image();
        img.onload = function() {
            // Create canvas
            const canvas = document.createElement('canvas');
            
            // Calculate dimensions
            let width = img.width;
            let height = img.height;
            
            // Resize if needed
            const maxSize = options.maxSize || 200; // Default max size
            if (width > maxSize || height > maxSize) {
                if (width > height) {
                    height = Math.round(height * (maxSize / width));
                    width = maxSize;
                } else {
                    width = Math.round(width * (maxSize / height));
                    height = maxSize;
                }
            }
            
            // Set canvas dimensions
            canvas.width = width;
            canvas.height = height;
            
            // Draw image on canvas
            const ctx = canvas.getContext('2d');
            ctx.drawImage(img, 0, 0, width, height);
            
            // Convert to blob
            canvas.toBlob(function(blob) {
                // Log performance
                const endTime = performance.now();
                logPerformance('image_processing', endTime - startTime);
                
                resolve(blob);
            }, options.format || 'image/png', options.quality || 0.8);
        };
        
        img.onerror = function() {
            reject(new Error('Failed to load image'));
        };
        
        // Load image
        img.src = URL.createObjectURL(imageFile);
    });
}

// Export functions
window.qrOptimizer = {
    generateOptimizedQRCode,
    clearQRCache,
    optimizeImage
};
