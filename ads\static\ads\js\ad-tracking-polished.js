/**
 * Ad Tracking System - Production Version
 * Tracks ad impressions and clicks with geolocation data
 */
(function() {
    'use strict';
    
    if (window.adTrackingLoaded) return;
    window.adTrackingLoaded = true;

    // Global location data storage
    window.userLocationData = window.userLocationData || {
        city: 'unknown',
        region: 'unknown',
        country: 'unknown',
        loc: 'unknown'
    };

    // Initialize tracking system when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        getUserLocation();
        setupAdTracking();
    });

    function getUserLocation() {
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                function(position) {
                    const lat = position.coords.latitude;
                    const lon = position.coords.longitude;
                    reverseGeocode(lat, lon);
                },
                function(error) {
                    getIPBasedLocation();
                },
                {
                    timeout: 5000,
                    maximumAge: 600000
                }
            );
        } else {
            getIPBasedLocation();
        }
    }

    function reverseGeocode(lat, lon) {
        fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lon}&zoom=10`)
            .then(response => response.json())
            .then(data => {
                if (data && data.address) {
                    window.userLocationData = {
                        city: data.address.city || data.address.town || data.address.village || 'unknown',
                        region: data.address.state || data.address.county || 'unknown',
                        country: data.address.country || 'unknown',
                        loc: `${lat},${lon}`
                    };
                }
            })
            .catch(error => {
                getIPBasedLocation();
            });
    }

    function getIPBasedLocation() {
        fetch('https://ipinfo.io/json?token=082682c8d0e7a8')
            .then(response => {
                if (!response.ok) throw new Error('Network response was not ok');
                return response.json();
            })
            .then(data => {
                window.userLocationData = {
                    city: data.city || 'unknown',
                    region: data.region || 'unknown',
                    country: data.country || 'unknown',
                    loc: data.loc || 'unknown',
                    timezone: data.timezone || 'unknown',
                    postal: data.postal || 'unknown'
                };
                
                if (!data.city) {
                    fallbackGeolocation();
                }
            })
            .catch(error => {
                fallbackGeolocation();
            });
    }

    function fallbackGeolocation() {
        fetch('https://ipapi.co/json/')
            .then(response => {
                if (!response.ok) throw new Error('Network response was not ok');
                return response.json();
            })
            .then(data => {
                window.userLocationData = {
                    city: data.city || window.userLocationData.city || 'unknown',
                    region: data.region || window.userLocationData.region || 'unknown',
                    country: data.country_name || window.userLocationData.country || 'unknown',
                    loc: `${data.latitude},${data.longitude}` || window.userLocationData.loc || 'unknown',
                    timezone: data.timezone || window.userLocationData.timezone || 'unknown',
                    postal: data.postal || window.userLocationData.postal || 'unknown'
                };
            })
            .catch(error => {
                // Keep existing values or defaults
            });
    }

    function setupAdTracking() {
        const adElements = document.querySelectorAll('[data-ad-id]');
        
        adElements.forEach(adElement => {
            const adId = adElement.getAttribute('data-ad-id');
            
            // Track impression if visible
            if (isElementVisible(adElement)) {
                trackImpression(adId);
            }
            
            // Set up intersection observer for lazy loading
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting && !adElement.getAttribute('data-impression-tracked')) {
                        trackImpression(adId);
                        adElement.setAttribute('data-impression-tracked', 'true');
                    }
                });
            }, { threshold: 0.5 });
            
            observer.observe(adElement);
            
            // Track clicks
            adElement.addEventListener('click', function() {
                trackClick(adId);
            });
        });
    }

    function isElementVisible(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }

    function getDeviceInfo() {
        let deviceType = 'desktop';
        
        if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
            deviceType = /iPad|tablet|Tablet/i.test(navigator.userAgent) ? 'tablet' : 'mobile';
        }
        
        return {
            type: deviceType,
            browser: navigator.userAgent,
            screenWidth: window.innerWidth,
            screenHeight: window.innerHeight
        };
    }

    function trackImpression(adId) {
        const deviceInfo = getDeviceInfo();
        
        fetch('/ads/api/track-impression/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            },
            body: JSON.stringify({
                ad_id: adId,
                device_info: deviceInfo,
                location_info: window.userLocationData
            })
        })
        .then(response => {
            if (!response.ok) throw new Error('Network response was not ok');
            return response.json();
        })
        .then(data => {
            // Impression tracked successfully
        })
        .catch(error => {
            console.error('Error tracking impression:', error);
        });
    }

    function trackClick(adId) {
        const deviceInfo = getDeviceInfo();
        
        fetch('/ads/api/track-click/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            },
            body: JSON.stringify({
                ad_id: adId,
                device_info: deviceInfo,
                location_info: window.userLocationData
            })
        })
        .then(response => {
            if (!response.ok) throw new Error('Network response was not ok');
            return response.json();
        })
        .then(data => {
            // Click tracked successfully
        })
        .catch(error => {
            console.error('Error tracking click:', error);
        });
    }

    function getCsrfToken() {
        const name = 'csrftoken';
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const trimmed = cookie.trim();
            if (trimmed.startsWith(name + '=')) {
                return decodeURIComponent(trimmed.substring(name.length + 1));
            }
        }
        return null;
    }

    // Export functions for external use
    window.AdTracking = {
        trackImpression,
        trackClick,
        getUserLocation,
        getDeviceInfo
    };

})();
