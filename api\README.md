# API App

This Django app provides a RESTful API for the QR Code Generator application.

## Features

- RESTful API endpoints for QR code generation and management
- API key authentication
- Token authentication
- Role-based access control
- Comprehensive API documentation

## API Endpoints

### Authentication

- `POST /api/auth/token/` - Obtain an authentication token
- `GET /api/user-info/` - Get current user information

### QR Codes

- `GET /api/qrcodes/` - List all QR codes
- `POST /api/qrcodes/` - Create a new QR code
- `GET /api/qrcodes/{id}/` - Retrieve a specific QR code
- `PUT /api/qrcodes/{id}/` - Update a QR code
- `DELETE /api/qrcodes/{id}/` - Delete a QR code
- `POST /api/generate/` - Generate a QR code without saving

### User Profiles

- `GET /api/profiles/` - List all user profiles (admin only)
- `GET /api/profiles/{id}/` - Retrieve a specific user profile
- `PUT /api/profiles/{id}/` - Update a user profile

### API Keys

- `GET /api/apikeys/` - List all API keys
- `POST /api/apikeys/` - Create a new API key
- `GET /api/apikeys/{id}/` - Retrieve a specific API key
- `PUT /api/apikeys/{id}/` - Update an API key
- `DELETE /api/apikeys/{id}/` - Delete an API key

### Batch Processing

- `GET /api/batches/` - List all QR code batches
- `POST /api/batches/` - Create a new QR code batch
- `GET /api/batches/{id}/` - Retrieve a specific QR code batch
- `DELETE /api/batches/{id}/` - Delete a QR code batch

## Authentication

The API supports two authentication methods:

1. **API Key Authentication**: Include your API key in the `X-API-Key` header
2. **Token Authentication**: Include your token in the `Authorization` header as `Bearer <token>`

## Permissions

The API implements role-based access control:

- Regular users can only access their own resources
- Admin users can access all resources
- Some endpoints are restricted to admin users only

## Usage Example

```python
import requests

# API key authentication
headers = {
    'X-API-Key': 'your_api_key_here'
}

# Get all QR codes
response = requests.get('https://api.example.com/api/qrcodes/', headers=headers)
qr_codes = response.json()

# Create a new QR code
data = {
    'name': 'My QR Code',
    'data': 'https://example.com',
    'qr_type': 'url',
    'foreground_color': '#000000',
    'background_color': '#FFFFFF'
}
response = requests.post('https://api.example.com/api/qrcodes/', json=data, headers=headers)
new_qr_code = response.json()
```
