/**
 * nav-controller.js
 *
 * This script centralizes all navigation-related event listeners to prevent
 * conflicts and duplicate event bindings. All nav events should be bound here
 * and nowhere else.
 */

// Wait for DOM to be ready
window.DOMReadyManager.register(function() {
    console.log('Nav Controller: Initializing');

    // Initialize navigation with event delegation
    initNavigation();
}, 'nav-controller');

/**
 * Initialize all navigation-related functionality
 * All nav events are bound in nav-controller.js. Do not duplicate.
 */
function initNavigation() {
    // Initialize desktop navigation
    initDesktopNav();

    // Initialize mobile navigation
    initMobileNav();

    // Initialize dropdown menus - DISABLED: handled by new_nav.js
    // initDropdowns();

    // Initialize notification system
    initNotifications();

    // Handle window resize events
    handleWindowResize();

    // Handle scroll events
    handleScrollEvents();

    console.log('Nav Controller: All navigation components initialized');
}

/**
 * Initialize desktop navigation
 */
function initDesktopNav() {
    // Use event delegation for better performance
    const navbar = document.querySelector('.new-navbar');
    if (!navbar) return;

    // Handle all navigation clicks through event delegation
    navbar.addEventListener('click', function(event) {
        // Find the closest clickable element
        const link = event.target.closest('a');
        const button = event.target.closest('button');

        if (link && link.classList.contains('new-navbar-link')) {
            // Handle navbar link clicks
            handleNavLinkClick(event, link);
        } else if (button && button.classList.contains('new-navbar-toggle')) {
            // Handle mobile toggle button
            handleMobileToggleClick(event, button);
        }
    });

    console.log('Nav Controller: Desktop navigation initialized');
}

/**
 * Initialize mobile navigation
 */
function initMobileNav() {
    // Mobile menu button
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    if (mobileMenuBtn) {
        // Remove existing listeners by cloning
        const newMobileMenuBtn = mobileMenuBtn.cloneNode(true);
        if (mobileMenuBtn.parentNode) {
            mobileMenuBtn.parentNode.replaceChild(newMobileMenuBtn, mobileMenuBtn);
        }

        // Add new listener
        newMobileMenuBtn.addEventListener('click', handleMobileMenuToggle);
    }

    // Mobile menu overlay
    const mobileMenuOverlay = document.querySelector('.mobile-menu-overlay');
    if (mobileMenuOverlay) {
        // Remove existing listeners by cloning
        const newMobileMenuOverlay = mobileMenuOverlay.cloneNode(true);
        if (mobileMenuOverlay.parentNode) {
            mobileMenuOverlay.parentNode.replaceChild(newMobileMenuOverlay, mobileMenuOverlay);
        }

        // Add new listener
        newMobileMenuOverlay.addEventListener('click', handleMobileMenuClose);
    }

    console.log('Nav Controller: Mobile navigation initialized');
}

/**
 * Initialize dropdown menus
 */
function initDropdowns() {
    // Desktop dropdowns (hover-based)
    const dropdownItems = document.querySelectorAll('.new-navbar-item.has-dropdown');

    dropdownItems.forEach(item => {
        const dropdownMenu = item.querySelector('.new-navbar-dropdown');
        const dropdownToggle = item.querySelector('.new-navbar-link');

        if (!dropdownMenu || !dropdownToggle) return;

        // Handle hover events for desktop
        if (window.innerWidth >= 992) {
            // Mouse enter on item
            item.addEventListener('mouseenter', function() {
                showDropdown(dropdownMenu, dropdownToggle);
            });

            // Mouse leave on item
            item.addEventListener('mouseleave', function() {
                hideDropdown(dropdownMenu, dropdownToggle);
            });
        }

        // Handle click events for mobile
        dropdownToggle.addEventListener('click', function(e) {
            if (window.innerWidth < 992) {
                e.preventDefault();
                toggleDropdown(dropdownMenu, dropdownToggle);
            }
        });
    });

    console.log('Nav Controller: Dropdown menus initialized');
}

/**
 * Initialize notification system
 */
function initNotifications() {
    // Notification toggle
    const notificationToggle = document.querySelector('.notification-toggle');
    if (notificationToggle) {
        notificationToggle.addEventListener('click', function(e) {
            e.preventDefault();
            toggleNotifications();
        });
    }

    console.log('Nav Controller: Notification system initialized');
}

/**
 * Handle window resize events
 */
function handleWindowResize() {
    window.addEventListener('resize', function() {
        // Reinitialize navigation based on screen size
        if (window.innerWidth >= 992) {
            // Desktop mode
            initDesktopNav();
        } else {
            // Mobile mode
            initMobileNav();
        }
    });

    console.log('Nav Controller: Window resize handler initialized');
}

/**
 * Handle scroll events
 */
function handleScrollEvents() {
    window.addEventListener('scroll', function() {
        const navbar = document.querySelector('.new-navbar');
        if (navbar) {
            if (window.scrollY > 10) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        }
    });

    console.log('Nav Controller: Scroll event handler initialized');
}

// Helper functions
function handleNavLinkClick(event, link) {
    // DISABLED: dropdown handling moved to new_nav.js
    // If it's a dropdown toggle, let new_nav.js handle it
    if (link.parentElement.classList.contains('has-dropdown')) {
        // Don't interfere with dropdown functionality
        return;
    }
}

function handleMobileToggleClick(event, button) {
    event.preventDefault();
    const navMenu = document.querySelector('.new-navbar-nav');
    if (navMenu) {
        navMenu.classList.toggle('open');
        button.setAttribute('aria-expanded', navMenu.classList.contains('open'));
    }
}

function handleMobileMenuToggle(event) {
    event.preventDefault();
    this.classList.toggle('active');

    const mobileMenu = document.querySelector('.mobile-menu');
    const mobileMenuOverlay = document.querySelector('.mobile-menu-overlay');

    if (mobileMenu) mobileMenu.classList.toggle('active');
    if (mobileMenuOverlay) mobileMenuOverlay.classList.toggle('active');

    document.body.classList.toggle('menu-open');
}

function handleMobileMenuClose() {
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const mobileMenu = document.querySelector('.mobile-menu');

    if (mobileMenuBtn) mobileMenuBtn.classList.remove('active');
    if (mobileMenu) mobileMenu.classList.remove('active');
    this.classList.remove('active');

    document.body.classList.remove('menu-open');
}

function showDropdown(dropdownMenu, dropdownToggle) {
    dropdownMenu.classList.add('show');
    dropdownToggle.setAttribute('aria-expanded', 'true');
}

function hideDropdown(dropdownMenu, dropdownToggle) {
    dropdownMenu.classList.remove('show');
    dropdownToggle.setAttribute('aria-expanded', 'false');
}

function toggleDropdown(dropdownMenu, dropdownToggle) {
    const isExpanded = dropdownToggle.getAttribute('aria-expanded') === 'true';
    dropdownMenu.classList.toggle('show');
    dropdownToggle.setAttribute('aria-expanded', !isExpanded);
}

function toggleNotifications() {
    const notificationDropdown = document.querySelector('.notification-dropdown');
    if (notificationDropdown) {
        notificationDropdown.classList.toggle('show');
    }
}
