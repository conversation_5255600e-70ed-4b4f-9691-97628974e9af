{% extends "base.html" %}
{% load i18n %}
{% load account socialaccount %}
{% load static %}

{% block title %}{% trans "Enterprise Sign Up" %} - Enterprise QR{% endblock %}

{% block extra_css %}
<style>
    /* Inherit all auth styles from login page */
    .auth-container {
        min-height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        position: relative;
        overflow: hidden;
        display: flex;
        align-items: center;
        padding: 40px 0;
    }

    .auth-background {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 1;
    }

    .auth-grid-pattern {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: 
            linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
            linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
        background-size: 40px 40px;
        animation: authGridMove 25s linear infinite;
    }

    @keyframes authGridMove {
        0% { transform: translate(0, 0); }
        100% { transform: translate(40px, 40px); }
    }

    .floating-qr {
        position: absolute;
        width: 60px;
        height: 60px;
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        justify-content: center;
        color: rgba(255, 255, 255, 0.7);
        font-size: 24px;
    }

    .floating-qr-1 {
        top: 15%;
        left: 10%;
        animation: floatQR1 8s ease-in-out infinite;
    }

    .floating-qr-2 {
        top: 70%;
        right: 15%;
        animation: floatQR2 10s ease-in-out infinite;
    }

    .floating-qr-3 {
        bottom: 20%;
        left: 20%;
        animation: floatQR3 12s ease-in-out infinite;
    }

    @keyframes floatQR1 {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(5deg); }
    }

    @keyframes floatQR2 {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-30px) rotate(-5deg); }
    }

    @keyframes floatQR3 {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-25px) rotate(3deg); }
    }

    .auth-card {
        position: relative;
        z-index: 2;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 20px;
        box-shadow: 
            0 20px 60px rgba(0, 0, 0, 0.1),
            inset 0 2px 0 rgba(255, 255, 255, 0.5),
            inset 0 -2px 0 rgba(0, 0, 0, 0.05);
        overflow: hidden;
        max-width: 500px;
        width: 100%;
        margin: 0 auto;
    }

    .auth-header {
        text-align: center;
        padding: 40px 40px 20px;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        border-bottom: 1px solid rgba(102, 126, 234, 0.1);
    }

    .auth-logo {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        transform: rotate(45deg);
    }

    .auth-logo i {
        font-size: 32px;
        color: white;
        transform: rotate(-45deg);
    }

    .auth-title {
        font-size: 2rem;
        font-weight: 800;
        color: #2c3e50;
        margin-bottom: 10px;
    }

    .auth-subtitle {
        color: #6c757d;
        font-size: 1rem;
        margin-bottom: 0;
    }

    .auth-body {
        padding: 40px;
    }

    .enterprise-badge {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(25, 135, 84, 0.1) 100%);
        border: 2px solid rgba(40, 167, 69, 0.3);
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 700;
        color: #28a745;
        margin-bottom: 30px;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.2);
    }

    .form-group {
        margin-bottom: 25px;
    }

    .form-label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 8px;
        display: block;
    }

    .form-control {
        border: 2px solid rgba(102, 126, 234, 0.1);
        border-radius: 12px;
        padding: 15px 20px;
        font-size: 16px;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
        width: 100%;
    }

    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        background: rgba(255, 255, 255, 0.95);
        outline: none;
    }

    .password-input-container {
        position: relative;
    }

    .password-toggle {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #6c757d;
        cursor: pointer;
        padding: 5px;
        transition: color 0.3s ease;
    }

    .password-toggle:hover {
        color: #667eea;
    }

    .btn-enterprise {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        color: white;
        padding: 15px 30px;
        border-radius: 12px;
        font-weight: 700;
        font-size: 16px;
        width: 100%;
        transition: all 0.3s ease;
        box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
        position: relative;
        overflow: hidden;
    }

    .btn-enterprise:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 35px rgba(40, 167, 69, 0.4);
        color: white;
    }

    .btn-enterprise::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
    }

    .btn-enterprise:hover::before {
        left: 100%;
    }

    .auth-links {
        text-align: center;
        margin-top: 25px;
    }

    .auth-links a {
        color: #667eea;
        text-decoration: none;
        font-weight: 600;
        transition: color 0.3s ease;
    }

    .auth-links a:hover {
        color: #764ba2;
    }

    .divider {
        display: flex;
        align-items: center;
        margin: 30px 0;
        color: #6c757d;
        font-size: 14px;
    }

    .divider::before,
    .divider::after {
        content: '';
        flex: 1;
        height: 1px;
        background: rgba(102, 126, 234, 0.2);
    }

    .divider span {
        padding: 0 20px;
        background: rgba(255, 255, 255, 0.95);
    }

    .alert-enterprise {
        background: rgba(220, 53, 69, 0.1);
        border: 2px solid rgba(220, 53, 69, 0.2);
        border-radius: 12px;
        padding: 15px;
        margin-bottom: 25px;
        color: #dc3545;
        font-weight: 500;
    }

    .invalid-feedback {
        color: #dc3545;
        font-size: 14px;
        margin-top: 5px;
    }

    .terms-checkbox {
        display: flex;
        align-items: flex-start;
        gap: 10px;
        margin-bottom: 30px;
        font-size: 14px;
        line-height: 1.5;
    }

    .terms-checkbox input[type="checkbox"] {
        width: 18px;
        height: 18px;
        accent-color: #28a745;
        margin-top: 2px;
    }

    .terms-checkbox a {
        color: #667eea;
        text-decoration: none;
    }

    .terms-checkbox a:hover {
        text-decoration: underline;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .auth-container {
            padding: 20px;
        }

        .auth-card {
            margin: 20px;
        }

        .auth-header,
        .auth-body {
            padding: 30px 25px;
        }

        .auth-title {
            font-size: 1.5rem;
        }

        .floating-qr {
            display: none;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="auth-container">
    <!-- Animated Background -->
    <div class="auth-background">
        <div class="auth-grid-pattern"></div>
        <div class="auth-floating-elements">
            <div class="floating-qr floating-qr-1">
                <i class="fas fa-qrcode"></i>
            </div>
            <div class="floating-qr floating-qr-2">
                <i class="fas fa-qrcode"></i>
            </div>
            <div class="floating-qr floating-qr-3">
                <i class="fas fa-qrcode"></i>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="auth-card">
            <!-- Header -->
            <div class="auth-header">
                <div class="auth-logo">
                    <i class="fas fa-qrcode"></i>
                </div>
                <h1 class="auth-title">{% trans "Join Enterprise QR" %}</h1>
                <p class="auth-subtitle">{% trans "Create your professional QR code account" %}</p>
            </div>

            <!-- Body -->
            <div class="auth-body">
                <!-- Enterprise Badge -->
                <div class="enterprise-badge">
                    <i class="fas fa-user-plus"></i>
                    <span>{% trans "Free Enterprise Trial" %}</span>
                </div>

                <!-- Error Messages -->
                {% if form.non_field_errors %}
                <div class="alert-enterprise">
                    {% for error in form.non_field_errors %}
                        <i class="fas fa-exclamation-triangle"></i> {{ error }}
                    {% endfor %}
                </div>
                {% endif %}

                <!-- Signup Form -->
                <form class="signup" method="POST" action="{% url 'account_signup' %}">
                    {% csrf_token %}
                    
                    <!-- Email Field -->
                    <div class="form-group">
                        <label for="id_email" class="form-label">
                            <i class="fas fa-envelope"></i> {% trans "Email Address" %}
                        </label>
                        <input type="email" 
                               name="email" 
                               id="id_email" 
                               class="form-control" 
                               placeholder="{% trans 'Enter your business email' %}"
                               required
                               autocomplete="email">
                        {% if form.email.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.email.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Username Field -->
                    {% if form.username %}
                    <div class="form-group">
                        <label for="id_username" class="form-label">
                            <i class="fas fa-user"></i> {% trans "Username" %}
                        </label>
                        <input type="text" 
                               name="username" 
                               id="id_username" 
                               class="form-control" 
                               placeholder="{% trans 'Choose a username' %}"
                               required
                               autocomplete="username">
                        {% if form.username.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.username.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    {% endif %}

                    <!-- Password Field -->
                    <div class="form-group">
                        <label for="id_password1" class="form-label">
                            <i class="fas fa-lock"></i> {% trans "Password" %}
                        </label>
                        <div class="password-input-container">
                            <input type="password" 
                                   name="password1" 
                                   id="id_password1" 
                                   class="form-control" 
                                   placeholder="{% trans 'Create a strong password' %}"
                                   required
                                   autocomplete="new-password">
                            <button type="button" class="password-toggle" onclick="togglePassword('id_password1')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        {% if form.password1.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.password1.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Confirm Password Field -->
                    <div class="form-group">
                        <label for="id_password2" class="form-label">
                            <i class="fas fa-lock"></i> {% trans "Confirm Password" %}
                        </label>
                        <div class="password-input-container">
                            <input type="password" 
                                   name="password2" 
                                   id="id_password2" 
                                   class="form-control" 
                                   placeholder="{% trans 'Confirm your password' %}"
                                   required
                                   autocomplete="new-password">
                            <button type="button" class="password-toggle" onclick="togglePassword('id_password2')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        {% if form.password2.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.password2.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Terms and Conditions -->
                    <div class="terms-checkbox">
                        <input type="checkbox" id="terms" required>
                        <label for="terms">
                            {% trans "I agree to the" %} 
                            <a href="#" target="_blank">{% trans "Terms of Service" %}</a> 
                            {% trans "and" %} 
                            <a href="#" target="_blank">{% trans "Privacy Policy" %}</a>
                        </label>
                    </div>

                    <!-- Hidden Next Field -->
                    {% if redirect_field_value %}
                    <input type="hidden" name="{{ redirect_field_name }}" value="{{ redirect_field_value }}" />
                    {% endif %}

                    <!-- Submit Button -->
                    <button type="submit" class="btn-enterprise">
                        <i class="fas fa-rocket"></i>
                        {% trans "Start Free Enterprise Trial" %}
                    </button>
                </form>

                <!-- Auth Links -->
                <div class="auth-links">
                    <a href="{% url 'account_login' %}">
                        <i class="fas fa-sign-in-alt"></i> {% trans "Already have an account? Sign in" %}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const toggle = field.nextElementSibling;
    const icon = toggle.querySelector('i');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// Add loading state to form submission
document.querySelector('.signup').addEventListener('submit', function(e) {
    const submitBtn = this.querySelector('.btn-enterprise');
    const originalText = submitBtn.innerHTML;
    
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> {% trans "Creating account..." %}';
    submitBtn.disabled = true;
    
    // Re-enable button after 10 seconds as fallback
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 10000);
});
</script>
{% endblock %}
