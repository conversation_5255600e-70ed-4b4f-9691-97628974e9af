#!/bin/bash
# Script to set up the deployment environment on a VPS

# Check if running as root
if [ "$EUID" -ne 0 ]; then
  echo "Please run as root"
  exit 1
fi

# Update system
echo "Updating system..."
apt-get update
apt-get upgrade -y

# Install Docker and Docker Compose
echo "Installing Docker and Docker Compose..."
apt-get install -y apt-transport-https ca-certificates curl software-properties-common
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | apt-key add -
add-apt-repository "deb [arch=amd64] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable"
apt-get update
apt-get install -y docker-ce docker-ce-cli containerd.io
curl -L "https://github.com/docker/compose/releases/download/v2.20.3/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Install Git
echo "Installing Git..."
apt-get install -y git

# Create deployment user
echo "Creating deployment user..."
read -p "Enter username for deployment: " USERNAME
adduser --gecos "" $USERNAME
usermod -aG docker $USERNAME

# Set up project directory
echo "Setting up project directory..."
mkdir -p /home/<USER>/QRCodeGenerator
chown -R $USERNAME:$USERNAME /home/<USER>/QRCodeGenerator

# Set up SSH for GitHub Actions
echo "Setting up SSH for GitHub Actions..."
mkdir -p /home/<USER>/.ssh
touch /home/<USER>/.ssh/authorized_keys
chown -R $USERNAME:$USERNAME /home/<USER>/.ssh
chmod 700 /home/<USER>/.ssh
chmod 600 /home/<USER>/.ssh/authorized_keys

echo "Please add the following public key to /home/<USER>/.ssh/authorized_keys:"
echo "Generate a new SSH key pair for GitHub Actions and add the public key here."
echo "Keep the private key secure and add it as a secret in your GitHub repository."

# Clone the repository
echo "Cloning the repository..."
su - $USERNAME -c "git clone https://github.com/Codegx-Technology/QRCodeGenerator.git /home/<USER>/QRCodeGenerator"

# Create .env file
echo "Creating .env file..."
cp /home/<USER>/QRCodeGenerator/.env.example /home/<USER>/QRCodeGenerator/.env
chown $USERNAME:$USERNAME /home/<USER>/QRCodeGenerator/.env

echo "Please edit the .env file with your production settings:"
echo "nano /home/<USER>/QRCodeGenerator/.env"

# Set up Nginx for HTTPS (if needed)
echo "Do you want to set up Nginx for HTTPS? (y/n)"
read SETUP_NGINX
if [ "$SETUP_NGINX" = "y" ]; then
  echo "Installing Nginx and Certbot..."
  apt-get install -y nginx certbot python3-certbot-nginx
  
  echo "Please enter your domain name:"
  read DOMAIN_NAME
  
  # Create Nginx configuration
  cat > /etc/nginx/sites-available/$DOMAIN_NAME <<EOF
server {
    listen 80;
    server_name $DOMAIN_NAME;
    
    location /.well-known/acme-challenge/ {
        root /var/www/html;
    }
    
    location / {
        return 301 https://\$host\$request_uri;
    }
}

server {
    listen 443 ssl http2;
    server_name $DOMAIN_NAME;
    
    ssl_certificate /etc/letsencrypt/live/$DOMAIN_NAME/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$DOMAIN_NAME/privkey.pem;
    
    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    location /ai/ {
        proxy_pass http://localhost:8001/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }
    
    location /static/ {
        alias /home/<USER>/QRCodeGenerator/static/;
        expires 30d;
    }
    
    location /media/ {
        alias /home/<USER>/QRCodeGenerator/media/;
        expires 30d;
    }
}
EOF
  
  # Enable the site
  ln -s /etc/nginx/sites-available/$DOMAIN_NAME /etc/nginx/sites-enabled/
  rm -f /etc/nginx/sites-enabled/default
  
  # Test Nginx configuration
  nginx -t
  
  # Restart Nginx
  systemctl restart nginx
  
  # Get SSL certificate
  certbot --nginx -d $DOMAIN_NAME
  
  echo "Nginx has been set up with HTTPS for $DOMAIN_NAME"
fi

echo "Deployment environment setup complete!"
echo "You can now deploy the application using GitHub Actions."
