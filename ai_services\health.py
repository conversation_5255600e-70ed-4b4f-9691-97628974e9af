"""
AI Services Health Check Module
Provides functions to check the health of AI providers and maintain their status
"""
import time
import threading
import requests
import logging
from typing import Dict, Any, List, Optional
from django.conf import settings

logger = logging.getLogger(__name__)

# Global registry of provider status
PROVIDER_STATUS = {
    'mistral': {
        'available': False,
        'last_check': 0,
        'response_time': 0,
        'error': None
    },
    'openai': {
        'available': False,
        'last_check': 0,
        'response_time': 0,
        'error': None
    },
    'local': {
        'available': False,
        'last_check': 0,
        'response_time': 0,
        'error': None
    }
}

# Constants
HEALTH_CHECK_TIMEOUT = 3  # seconds
HEALTH_CHECK_INTERVAL = 60 * 5  # 5 minutes in seconds
HEALTH_CHECK_URLS = {
    'mistral': 'https://api.mistral.ai/v1/health',
    'openai': 'https://api.openai.com/v1/models',
    'local': 'http://localhost:8001/health'  # Local AI engine health endpoint
}

def check_provider_health(provider: str, timeout: int = HEALTH_CHECK_TIMEOUT) -> Dict[str, Any]:
    """
    Check the health of an AI provider
    
    Args:
        provider: The provider name ('mistral', 'openai', 'local')
        timeout: Timeout in seconds
        
    Returns:
        Dictionary with health check results
    """
    start_time = time.time()
    url = HEALTH_CHECK_URLS.get(provider)
    
    if not url:
        logger.warning(f"Unknown provider: {provider}")
        return {
            'available': False,
            'response_time': 0,
            'error': f"Unknown provider: {provider}"
        }
    
    # Get API key if needed
    headers = {}
    if provider == 'mistral':
        api_key = getattr(settings, 'MISTRAL_API_KEY', None)
        if api_key:
            headers['Authorization'] = f'Bearer {api_key}'
    elif provider == 'openai':
        api_key = getattr(settings, 'OPENAI_API_KEY', None)
        if api_key:
            headers['Authorization'] = f'Bearer {api_key}'
    
    try:
        response = requests.head(url, timeout=timeout, headers=headers)
        response_time = time.time() - start_time
        
        # Check if response is successful (status code < 400)
        if response.status_code < 400:
            result = {
                'available': True,
                'response_time': response_time,
                'error': None
            }
        else:
            result = {
                'available': False,
                'response_time': response_time,
                'error': f"API returned status code {response.status_code}"
            }
    except requests.exceptions.RequestException as e:
        response_time = time.time() - start_time
        result = {
            'available': False,
            'response_time': response_time,
            'error': str(e)
        }
    
    # Update global status
    PROVIDER_STATUS[provider].update({
        'available': result['available'],
        'last_check': time.time(),
        'response_time': result['response_time'],
        'error': result['error']
    })
    
    # Log the result
    if result['available']:
        logger.info(f"Health check for {provider}: Available (response time: {result['response_time']:.2f}s)")
    else:
        logger.warning(f"Health check for {provider}: Unavailable - {result['error']}")
    
    return result

def check_all_providers(timeout: int = HEALTH_CHECK_TIMEOUT) -> Dict[str, Dict[str, Any]]:
    """
    Check the health of all AI providers
    
    Args:
        timeout: Timeout in seconds
        
    Returns:
        Dictionary with health check results for all providers
    """
    results = {}
    
    for provider in HEALTH_CHECK_URLS.keys():
        results[provider] = check_provider_health(provider, timeout)
    
    return results

def get_provider_status(provider: str) -> Dict[str, Any]:
    """
    Get the current status of an AI provider
    
    Args:
        provider: The provider name ('mistral', 'openai', 'local')
        
    Returns:
        Dictionary with provider status
    """
    if provider not in PROVIDER_STATUS:
        return {
            'available': False,
            'last_check': 0,
            'response_time': 0,
            'error': f"Unknown provider: {provider}"
        }
    
    # If status is stale, perform a new health check
    if time.time() - PROVIDER_STATUS[provider]['last_check'] > HEALTH_CHECK_INTERVAL:
        check_provider_health(provider)
    
    return PROVIDER_STATUS[provider]

def get_all_provider_status() -> Dict[str, Dict[str, Any]]:
    """
    Get the current status of all AI providers
    
    Returns:
        Dictionary with status for all providers
    """
    return PROVIDER_STATUS

def get_best_available_provider() -> str:
    """
    Get the best available AI provider based on health checks
    
    Returns:
        The name of the best available provider, or None if none are available
    """
    # Check providers in order of preference
    preferred_order = ['mistral', 'openai', 'local']
    
    for provider in preferred_order:
        status = get_provider_status(provider)
        if status['available']:
            return provider
    
    # If no provider is available, return the first one as a fallback
    return preferred_order[0]

def start_background_health_checks():
    """
    Start background thread for periodic health checks
    """
    def run_periodic_checks():
        while True:
            try:
                check_all_providers()
            except Exception as e:
                logger.error(f"Error in background health checks: {str(e)}")
            
            # Sleep for the check interval
            time.sleep(HEALTH_CHECK_INTERVAL)
    
    # Start the background thread
    thread = threading.Thread(target=run_periodic_checks, daemon=True)
    thread.start()
    logger.info("Started background health checks for AI providers")

# Run initial health checks
def run_initial_health_checks():
    """
    Run initial health checks for all providers
    """
    logger.info("Running initial health checks for AI providers")
    check_all_providers()
