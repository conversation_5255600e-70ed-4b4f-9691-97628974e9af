/* Enterprise-Grade Notification System */

/* Notification Bell */
.notification-bell {
    position: fixed;
    bottom: 80px;
    right: 20px;
    z-index: 999;
    cursor: pointer;
}

/* Desktop hover functionality */
@media (min-width: 992px) {
    .notification-bell:hover .bell-icon {
        transform: scale(1.05);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    }

    /* Show notification panel on hover for desktop */
    .notification-bell:hover .desktop-notification-panel,
    .desktop-notification-panel:hover,
    .notification-bell.active .desktop-notification-panel {
        transform: translateY(0);
        opacity: 1;
        pointer-events: auto;
    }

    /* Show mobile notification panel on hover for desktop */
    .notification-bell:hover + .notification-panel,
    .notification-panel:hover {
        transform: translateX(0);
        opacity: 1;
        pointer-events: auto;
    }

    /* Position the panel differently for desktop hover */
    .notification-bell.desktop-nav {
        position: relative;
        bottom: auto;
        right: auto;
        margin-left: 15px;
    }

    .notification-bell.desktop-nav .bell-icon {
        width: 40px;
        height: 40px;
    }

    .notification-bell.desktop-nav .notification-badge {
        min-width: 18px;
        height: 18px;
        font-size: 0.65rem;
        top: -3px;
        right: -3px;
        padding: 0 4px;
    }



    /* Desktop notification panel positioning */
    .desktop-notification-panel {
        position: absolute;
        top: 100%;
        right: 0;
        width: 350px;
        max-height: 450px;
        margin-top: 10px;
        transform: translateY(-10px);
        opacity: 0;
        pointer-events: none;
        transition: transform 0.3s ease, opacity 0.3s ease;
        border-radius: 12px;
        overflow: hidden;
        background: linear-gradient(135deg, #1a1f36, #121628);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        z-index: 1000;
    }

    /* Add arrow for header notification panel */
    .notification-bell.header-bell .desktop-notification-panel::after {
        content: '';
        position: absolute;
        top: -8px;
        right: 15px;
        width: 0;
        height: 0;
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        border-bottom: 8px solid #3a7bd5; /* Match header gradient start color */
    }

    .notification-bell.desktop-nav:hover .desktop-notification-panel,
    .desktop-notification-panel:hover,
    .notification-bell.desktop-nav.active .desktop-notification-panel {
        transform: translateY(0);
        opacity: 1;
        pointer-events: auto;
    }
}

.bell-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #3a7bd5, #00d2ff);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    position: relative;
    transition: all 0.3s ease;
    overflow: hidden;
}

.bell-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.1) 50%,
        rgba(255, 255, 255, 0) 100%);
    animation: shine 6s infinite linear;
    pointer-events: none;
}

.bell-icon i {
    color: #fff;
    font-size: 1.25rem;
    position: relative;
    z-index: 2;
}

.notification-bell:active .bell-icon {
    transform: scale(0.9);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.notification-bell:active .bell-icon i {
    animation: bell-ring 0.5s ease;
}

@keyframes bell-ring {
    0% { transform: rotate(0); }
    20% { transform: rotate(15deg); }
    40% { transform: rotate(-15deg); }
    60% { transform: rotate(7deg); }
    80% { transform: rotate(-7deg); }
    100% { transform: rotate(0); }
}

@keyframes shine {
    0% {
        transform: translateX(-100%) rotate(45deg);
    }
    100% {
        transform: translateX(100%) rotate(45deg);
    }
}

.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ff3e3e;
    color: #fff;
    font-size: 0.7rem;
    font-weight: 600;
    min-width: 22px;
    height: 22px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    padding: 0 5px;
    line-height: 1;
    border: 2px solid #fff;
}

/* Add pulsating effect only when there are multiple notifications */
.notification-badge.has-notifications {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(255, 62, 62, 0.7);
    }
    70% {
        transform: scale(1.1);
        box-shadow: 0 0 0 10px rgba(255, 62, 62, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(255, 62, 62, 0);
    }
}

.notification-bell.mobile-bell:hover .bell-icon {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.bell-icon:hover {
    transform: scale(1.05);
}

.bell-icon:active {
    transform: scale(0.95);
}

/* Notification Panel */
.notification-panel {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    width: 90%;
    max-width: 360px;
    background: linear-gradient(135deg, #1a1f36, #121628);
    box-shadow: -5px 0 25px rgba(0, 0, 0, 0.4);
    border-left: 1px solid rgba(58, 123, 213, 0.25);
    z-index: 2001;
    overflow-y: auto;
    transform: translateX(100%);
    transition: transform 0.7s cubic-bezier(0.19, 1, 0.22, 1), opacity 0.5s ease;
    border-top-left-radius: 16px;
    border-bottom-left-radius: 16px;
    opacity: 0;
}

.notification-panel.active {
    transform: translateX(0);
    opacity: 1;
}

.notification-panel .notification-item {
    transform: translateX(50px);
    opacity: 0;
    transition: transform 0.5s cubic-bezier(0.19, 1, 0.22, 1), opacity 0.5s ease, background 0.3s ease, height 0.3s ease, padding 0.3s ease, margin 0.3s ease;
}

.notification-panel.active .notification-item {
    transform: translateX(0);
    opacity: 1;
}

.notification-panel.active .notification-item:nth-child(1) {
    transition-delay: 0.1s;
}

.notification-panel.active .notification-item:nth-child(2) {
    transition-delay: 0.2s;
}

.notification-panel.active .notification-item:nth-child(3) {
    transition-delay: 0.3s;
}

.notification-panel.active .notification-item:nth-child(4) {
    transition-delay: 0.4s;
}

.notification-header {
    background: linear-gradient(135deg, #3a7bd5, #00d2ff);
    padding: 1.25rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 10;
}

.notification-header h3 {
    color: #fff;
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.notification-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: #fff;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.notification-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: rotate(90deg);
}

.notification-list {
    padding: 1rem 0;
}

.notification-item {
    display: flex;
    padding: 1rem 1.25rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    position: relative;
    transition: all 0.3s ease;
}

.notification-item:hover {
    background: rgba(58, 123, 213, 0.1);
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.notification-item.success .notification-icon {
    background: rgba(40, 199, 111, 0.2);
    color: #28c76f;
}

.notification-item.info .notification-icon {
    background: rgba(0, 207, 232, 0.2);
    color: #00cfe8;
}

.notification-item.warning .notification-icon {
    background: rgba(255, 159, 67, 0.2);
    color: #ff9f43;
}

.notification-item.danger .notification-icon {
    background: rgba(234, 84, 85, 0.2);
    color: #ea5455;
}

.notification-content {
    flex: 1;
}

.notification-title {
    color: #fff;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.notification-message {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.85rem;
    margin-bottom: 0.25rem;
}

.notification-time {
    color: rgba(255, 255, 255, 0.5);
    font-size: 0.75rem;
}

.notification-actions {
    margin-left: 0.5rem;
}

.notification-action {
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.5);
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.notification-action:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
}

.notification-footer {
    padding: 1rem 1.25rem;
    display: flex;
    justify-content: space-between;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    position: sticky;
    bottom: 0;
    background: linear-gradient(135deg, rgba(26, 31, 54, 0.95), rgba(18, 22, 40, 0.95));
    backdrop-filter: blur(10px);
}

.notification-footer button {
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.85rem;
    padding: 0.5rem 0;
    transition: all 0.3s ease;
}

.notification-footer button:hover {
    color: #00d2ff;
}

.mark-all-read {
    color: rgba(255, 255, 255, 0.7);
}

.view-all {
    color: #00d2ff;
    font-weight: 600;
}

/* Read notification state */
.notification-item.read {
    opacity: 0.7;
    background: rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

/* Loading indicator */
.notification-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
    text-align: center;
    color: rgba(255, 255, 255, 0.7);
    animation: fadeIn 0.3s ease;
}

.notification-loading i {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #00d2ff;
}

/* Empty notification state */
.notification-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
    text-align: center;
    color: rgba(255, 255, 255, 0.7);
    animation: fadeIn 0.3s ease;
}

.notification-empty i {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: rgba(255, 255, 255, 0.3);
    background: linear-gradient(135deg, rgba(58, 123, 213, 0.2), rgba(0, 210, 255, 0.2));
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-empty p {
    margin: 0.5rem 0;
}

.notification-empty-message {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.5);
    max-width: 250px;
}

/* Confirmation message */
.notification-confirmation {
    background: rgba(40, 199, 111, 0.2);
    color: #28c76f;
    padding: 0.75rem 1.25rem;
    text-align: center;
    margin: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 500;
    animation: fadeIn 0.3s ease;
}

.mute-confirmation {
    background: rgba(0, 207, 232, 0.2);
    color: #00cfe8;
    padding: 0.5rem;
    text-align: center;
    margin-top: 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    animation: fadeIn 0.3s ease;
}

/* Error state */
.notification-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
    text-align: center;
    color: rgba(255, 255, 255, 0.7);
    animation: fadeIn 0.3s ease;
}

.notification-error i {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #ea5455;
}

.notification-error-message {
    background: rgba(234, 84, 85, 0.2);
    color: #ea5455;
    padding: 0.75rem 1.25rem;
    text-align: center;
    margin: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 500;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Action dropdown */
.action-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: linear-gradient(135deg, #1a1f36, #121628);
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    z-index: 10;
    overflow: hidden;
    width: 180px;
    animation: dropdownFadeIn 0.2s ease;
}

@keyframes dropdownFadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.action-dropdown ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.action-dropdown li {
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
}

.action-dropdown li i {
    margin-right: 0.75rem;
    width: 16px;
    text-align: center;
}

.action-dropdown li:hover {
    background: rgba(58, 123, 213, 0.2);
    color: #fff;
}

.action-dropdown li.mark-read:hover {
    color: #28c76f;
}

.action-dropdown li.delete:hover {
    color: #ea5455;
}

.action-dropdown li.mute:hover {
    color: #ff9f43;
}
