/**
 * Campaign Select JavaScript
 * Handles the Select2 campaign dropdown functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize Select2 for campaign dropdown
    const campaignSelect = $('.campaign-select');
    
    if (campaignSelect.length) {
        campaignSelect.select2({
            placeholder: 'Select a campaign or create a new one',
            allowClear: true,
            ajax: {
                url: '/ads/api/campaigns/',
                dataType: 'json',
                delay: 250,
                data: function(params) {
                    return {
                        q: params.term || '',
                        page: params.page || 1
                    };
                },
                processResults: function(data, params) {
                    params.page = params.page || 1;
                    
                    // Add "No Campaign" option
                    if (params.page === 1) {
                        data.results.unshift({
                            id: '',
                            text: 'No Campaign (Individual Ad)',
                            description: 'Create this ad without associating it with a campaign'
                        });
                    }
                    
                    // Add "Create New Campaign" option
                    if (params.page === 1 && params.term) {
                        data.results.push({
                            id: 'new:' + params.term,
                            text: 'Create New Campaign: "' + params.term + '"',
                            description: 'Create a new campaign with this name',
                            isNew: true
                        });
                    }
                    
                    return {
                        results: data.results,
                        pagination: {
                            more: data.pagination.more
                        }
                    };
                },
                cache: true
            },
            escapeMarkup: function(markup) {
                return markup;
            },
            minimumInputLength: 0,
            templateResult: formatCampaignResult,
            templateSelection: formatCampaignSelection
        });
        
        // Handle selection of "Create New Campaign" option
        campaignSelect.on('select2:select', function(e) {
            const data = e.params.data;
            
            if (data && data.id && data.id.startsWith('new:')) {
                const campaignName = data.id.substring(4);
                
                // Create new campaign via API
                $.ajax({
                    url: '/ads/api/campaigns/create/',
                    type: 'POST',
                    dataType: 'json',
                    data: {
                        name: campaignName,
                        csrfmiddlewaretoken: getCSRFToken()
                    },
                    success: function(response) {
                        if (response.success) {
                            // Add the new campaign to the select options
                            const newOption = new Option(
                                response.campaign.name,
                                response.campaign.id,
                                true,
                                true
                            );
                            
                            campaignSelect.append(newOption).trigger('change');
                            
                            // Show success message
                            showMessage('success', 'Campaign "' + response.campaign.name + '" created successfully!');
                        } else {
                            // Show error message
                            showMessage('danger', 'Failed to create campaign: ' + (response.error || 'Unknown error'));
                            
                            // Reset selection
                            campaignSelect.val('').trigger('change');
                        }
                    },
                    error: function() {
                        // Show error message
                        showMessage('danger', 'Failed to create campaign. Please try again.');
                        
                        // Reset selection
                        campaignSelect.val('').trigger('change');
                    }
                });
            }
        });
    }
    
    // Function to format campaign result
    function formatCampaignResult(campaign) {
        if (!campaign.id) {
            return campaign.text;
        }
        
        if (campaign.isNew) {
            return $(`
                <div class="create-new-campaign-option">
                    <div class="campaign-option">
                        <span class="campaign-option__icon create-new-campaign-option__icon">
                            <i class="fas fa-plus-circle"></i>
                        </span>
                        <span class="campaign-option__name">${campaign.text}</span>
                    </div>
                    <div class="campaign-option__details">
                        ${campaign.description || ''}
                    </div>
                </div>
            `);
        }
        
        let statusClass = '';
        let statusText = '';
        
        if (campaign.status) {
            switch (campaign.status.toLowerCase()) {
                case 'active':
                    statusClass = 'campaign-option__status--active';
                    statusText = 'Active';
                    break;
                case 'pending':
                    statusClass = 'campaign-option__status--pending';
                    statusText = 'Pending';
                    break;
                case 'draft':
                    statusClass = 'campaign-option__status--draft';
                    statusText = 'Draft';
                    break;
            }
        }
        
        return $(`
            <div class="campaign-option">
                <span class="campaign-option__icon">
                    <i class="fas fa-bullhorn"></i>
                </span>
                <div>
                    <span class="campaign-option__name">${campaign.text}</span>
                    <div class="campaign-option__details">
                        ${campaign.description || ''}
                        ${campaign.ad_count ? '<div>Ads: ' + campaign.ad_count + '</div>' : ''}
                    </div>
                </div>
                ${statusText ? '<span class="campaign-option__status ' + statusClass + '">' + statusText + '</span>' : ''}
            </div>
        `);
    }
    
    // Function to format campaign selection
    function formatCampaignSelection(campaign) {
        if (!campaign.id) {
            return 'No Campaign (Individual Ad)';
        }
        
        if (campaign.id.startsWith('new:')) {
            return 'New Campaign: "' + campaign.id.substring(4) + '"';
        }
        
        return campaign.text;
    }
    
    // Function to show message
    function showMessage(type, message) {
        const alertDiv = $('<div class="alert alert-' + type + ' alert-dismissible fade show" role="alert">' +
            message +
            '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
            '</div>');
        
        // Add to the page
        const form = $('#adCreationForm');
        if (form.length) {
            form.prepend(alertDiv);
            
            // Remove after 5 seconds
            setTimeout(function() {
                alertDiv.alert('close');
            }, 5000);
        }
    }
    
    // Function to get CSRF token
    function getCSRFToken() {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.startsWith('csrftoken=')) {
                return cookie.substring('csrftoken='.length, cookie.length);
            }
        }
        return '';
    }
});
