# Generated manually for customization fields

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('qrcode_app', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='qrcode',
            name='dot_style',
            field=models.CharField(blank=True, default='square', help_text='QR code dot style (square, dots, rounded, classy, etc.)', max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='qrcode',
            name='corner_style',
            field=models.CharField(blank=True, default='square', help_text='QR code corner style (square, dot, rounded, extra-rounded, diamond)', max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='qrcode',
            name='frame_style',
            field=models.CharField(blank=True, default='none', help_text='QR code frame style (none, corporate, phone, business-card, ticket, elegant, modern)', max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='qrcode',
            name='title_text',
            field=models.CharField(blank=True, help_text='Title text to display above QR code', max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='qrcode',
            name='guiding_text',
            field=models.CharField(blank=True, help_text='Guiding text to display below QR code', max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='qrcode',
            name='guiding_text_position',
            field=models.CharField(blank=True, default='below', help_text='Position of guiding text (below, above, left, right)', max_length=20, null=True),
        ),
    ]
