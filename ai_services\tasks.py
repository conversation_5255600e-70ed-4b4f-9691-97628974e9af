"""
Celery tasks for AI services
"""
import logging
from celery import shared_task
from ai_services.models import CachedSuggestion

logger = logging.getLogger(__name__)

@shared_task
def clear_expired_cache():
    """
    Clear expired cache entries
    """
    try:
        # Clear expired entries from the database
        count = CachedSuggestion.clear_expired()
        logger.info(f'Successfully cleared {count} expired cache entries from database')
        
        # Try to clear expired entries from the enhanced cache
        try:
            from ai_services.enhanced_cache import clear_expired_cache as clear_enhanced_cache
            clear_enhanced_cache()
            logger.info('Successfully cleared expired entries from enhanced cache')
        except ImportError:
            logger.warning('Enhanced cache module not available')
            
        return f'Cleared {count} expired cache entries'
    except Exception as e:
        logger.error(f'Error clearing expired cache entries: {str(e)}', exc_info=True)
        return f'Error: {str(e)}'
