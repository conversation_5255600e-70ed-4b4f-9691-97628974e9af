/**
 * global-error-handler.js
 * 
 * This script provides global error handling and utility functions
 * that are used across the application.
 */

// Global error handler - redundant but kept for compatibility
window.onerror = function(message, source, lineno, colno, error) {
    console.error('JavaScript Error:', message, 'at', source, 'line:', lineno, 'column:', colno);
    // Don't alert as it might freeze the page
    return true; // Prevent default error handling
};

// Global CSRF token function - redundant but kept for compatibility
function getCsrfToken() {
    if (window.getCsrfToken) {
        return window.getCsrfToken();
    }

    const name = 'csrftoken';
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// Global function to get CSRF token - redundant but kept for compatibility
function getCSRFToken() {
    return getCsrfToken();
}
