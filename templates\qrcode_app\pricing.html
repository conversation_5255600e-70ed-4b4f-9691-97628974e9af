{% extends "base.html" %}
{% load static %}

{% block title %}Pricing Plans - Enterprise QR{% endblock %}

{% block extra_css %}
<!-- Google Fonts - Poppins and Montserrat -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

<style>
/* QR Pro Features Styling */
.new-badge {
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    color: white;
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 600;
    margin-left: 8px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.feature-icon.premium i.fa-magic {
    color: #9b59b6;
}

.feature-icon.premium i.fa-exchange-alt {
    color: #27ae60;
}

.feature-icon.enterprise i.fa-magic {
    color: #8e44ad;
}

.feature-icon.enterprise i.fa-exchange-alt {
    color: #2ecc71;
}
</style>

<style>
    /* Ultra-Modern Startup-Focused Pricing Styling */
    body {
        background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
        min-height: 100vh;
        font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        position: relative;
        overflow-x: hidden;
    }

    /* Dynamic animated background with modern startup patterns */
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.15) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(236, 72, 153, 0.12) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 60% 60%, rgba(16, 185, 129, 0.08) 0%, transparent 50%),
            linear-gradient(135deg, rgba(139, 92, 246, 0.05) 0%, rgba(59, 130, 246, 0.05) 100%);
        z-index: -1;
        animation: modernStartupFloat 100s ease-in-out infinite;
    }

    body::after {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            repeating-linear-gradient(
                90deg,
                transparent,
                transparent 98px,
                rgba(255, 255, 255, 0.02) 100px
            ),
            repeating-linear-gradient(
                0deg,
                transparent,
                transparent 98px,
                rgba(255, 255, 255, 0.02) 100px
            );
        z-index: -1;
        opacity: 0.3;
    }

    @keyframes modernStartupFloat {
        0%, 100% {
            transform: translateY(0px) rotate(0deg) scale(1);
            opacity: 1;
        }
        25% {
            transform: translateY(-40px) rotate(2deg) scale(1.02);
            opacity: 0.8;
        }
        50% {
            transform: translateY(-25px) rotate(-1deg) scale(0.98);
            opacity: 0.9;
        }
        75% {
            transform: translateY(-45px) rotate(1.5deg) scale(1.01);
            opacity: 0.85;
        }
    }

    /* Elegant Professional Pricing Header */
    .pricing-header {
        text-align: center;
        padding: 6rem 3rem;
        background: linear-gradient(135deg,
            rgba(99, 102, 241, 0.08) 0%,
            rgba(139, 92, 246, 0.06) 25%,
            rgba(59, 130, 246, 0.05) 50%,
            rgba(236, 72, 153, 0.04) 75%,
            rgba(16, 185, 129, 0.03) 100%);
        backdrop-filter: blur(60px) saturate(200%);
        border-radius: 40px;
        margin: 3rem auto 5rem;
        max-width: 1400px;
        position: relative;
        overflow: hidden;
        box-shadow:
            0 40px 80px rgba(0, 0, 0, 0.12),
            0 0 0 1px rgba(255, 255, 255, 0.08),
            inset 0 2px 0 rgba(255, 255, 255, 0.15),
            0 16px 48px rgba(99, 102, 241, 0.08);
        border: 1px solid rgba(255, 255, 255, 0.06);
        animation: slideInDown 1s cubic-bezier(0.4, 0, 0.2, 1);
    }

    @keyframes headerGlow {
        0% {
            box-shadow:
                0 32px 64px rgba(0, 0, 0, 0.25),
                0 0 0 1px rgba(255, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2),
                0 8px 32px rgba(99, 102, 241, 0.15);
        }
        100% {
            box-shadow:
                0 32px 64px rgba(0, 0, 0, 0.25),
                0 0 0 1px rgba(255, 255, 255, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.3),
                0 8px 32px rgba(99, 102, 241, 0.25),
                0 0 80px rgba(139, 92, 246, 0.1);
        }
    }

    /* Enhanced Mobile Responsiveness */
    @media (max-width: 768px) {
        .pricing-header {
            padding: 2.5rem 1.5rem;
            margin: 1rem auto 2rem;
            border-radius: 16px;
        }

        .pricing-title {
            font-size: 2.2rem;
        }

        .pricing-subtitle {
            font-size: 1.1rem;
        }

        .enterprise-container {
            padding: 1rem 0;
        }
    }

    @media (max-width: 576px) {
        .pricing-header {
            padding: 2rem 1rem;
            margin: 0.5rem auto 1.5rem;
        }

        .pricing-title {
            font-size: 1.8rem;
        }

        .pricing-subtitle {
            font-size: 1rem;
        }
    }

    .pricing-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
        animation: shimmer 3s ease-in-out infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    .pricing-title {
        font-family: 'Inter', 'Segoe UI', sans-serif !important;
        font-size: 3.2rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        color: #ffffff;
        position: relative;
        z-index: 2;
        letter-spacing: -0.03em;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        line-height: 1.1;
    }

    .pricing-subtitle {
        font-size: 1.3rem;
        color: rgba(255, 255, 255, 0.85);
        max-width: 750px;
        margin: 0 auto 3rem;
        position: relative;
        z-index: 2;
        font-weight: 300;
        line-height: 1.7;
        letter-spacing: 0.01em;
    }

    /* Billing Toggle */
    .billing-toggle-container {
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 2rem auto 0;
        position: relative;
        z-index: 1;
    }

    .billing-option {
        font-weight: 600;
        color: rgba(255, 255, 255, 0.7);
        padding: 0 1rem;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        cursor: pointer;
    }

    .billing-option.active {
        color: #ffffff;
    }

    .billing-option:hover {
        color: rgba(255, 255, 255, 0.9);
    }

    .billing-toggle {
        position: relative;
        display: inline-block;
        width: 60px;
        height: 30px;
        margin: 0 0.5rem;
    }

    .billing-toggle input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .toggle-slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #e2e8f0;
        transition: .4s;
        border-radius: 34px;
    }

    .toggle-slider:before {
        position: absolute;
        content: "";
        height: 22px;
        width: 22px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    input:checked + .toggle-slider {
        background: linear-gradient(135deg, #2563eb, #1d4ed8);
    }

    input:checked + .toggle-slider:before {
        transform: translateX(30px);
    }

    .discount-badge {
        background: linear-gradient(135deg, #FF9500, #FF2D55);
        color: white;
        font-size: 0.75rem;
        font-weight: 700;
        padding: 0.25rem 0.5rem;
        border-radius: 1rem;
        margin-left: 0.5rem;
        box-shadow: 0 2px 4px rgba(255, 45, 85, 0.2);
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(255, 45, 85, 0.4);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(255, 45, 85, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(255, 45, 85, 0);
        }
    }

    .pricing-card {
        border: none;
        border-radius: 24px;
        overflow: hidden;
        transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        height: 100%;
        display: flex;
        flex-direction: column;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.99) 0%,
            rgba(255, 255, 255, 0.97) 100%);
        backdrop-filter: blur(30px) saturate(200%);
        box-shadow:
            0 32px 64px rgba(0, 0, 0, 0.06),
            0 0 0 1px rgba(255, 255, 255, 0.4),
            inset 0 2px 0 rgba(255, 255, 255, 0.8);
        border: 1px solid rgba(255, 255, 255, 0.3);
        animation: slideInUp 1s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
    }

    .pricing-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
            rgba(99, 102, 241, 0.02) 0%,
            rgba(139, 92, 246, 0.01) 50%,
            rgba(59, 130, 246, 0.02) 100%);
        opacity: 0;
        transition: opacity 0.4s ease;
        pointer-events: none;
        border-radius: 20px;
    }

    .pricing-card:hover {
        transform: translateY(-16px) scale(1.01);
        box-shadow:
            0 48px 96px rgba(0, 0, 0, 0.08),
            0 0 0 1px rgba(255, 255, 255, 0.5),
            inset 0 2px 0 rgba(255, 255, 255, 0.9),
            0 20px 60px rgba(99, 102, 241, 0.06);
    }

    .pricing-card:hover::before {
        opacity: 1;
    }

    .pricing-card.popular {
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.98) 0%,
            rgba(255, 255, 255, 0.95) 100%);
        border: 2px solid transparent;
        background-clip: padding-box;
        position: relative;
        transform: scale(1.05);
        z-index: 1;
        box-shadow:
            0 30px 60px rgba(0, 0, 0, 0.1),
            0 0 0 1px rgba(255, 255, 255, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.6);
    }

    .pricing-card.popular::after {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(135deg, #6366f1, #8b5cf6, #ec4899);
        border-radius: 22px;
        z-index: -1;
    }

    .pricing-card.popular:hover {
        transform: scale(1.05) translateY(-8px);
        box-shadow:
            0 50px 100px rgba(0, 0, 0, 0.15),
            0 0 0 1px rgba(255, 255, 255, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.7),
            0 20px 60px rgba(99, 102, 241, 0.15);
    }

    .pricing-card.enterprise-elite {
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.98) 0%,
            rgba(255, 255, 255, 0.95) 100%);
        border: 2px solid rgba(59, 130, 246, 0.2);
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.08),
            0 0 0 1px rgba(59, 130, 246, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.6),
            0 8px 32px rgba(59, 130, 246, 0.1);
    }

    .pricing-card.enterprise-elite:hover {
        box-shadow:
            0 40px 80px rgba(0, 0, 0, 0.12),
            0 0 0 1px rgba(59, 130, 246, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.7),
            0 16px 48px rgba(59, 130, 246, 0.15);
    }

    .pricing-card-header {
        padding: 2.5rem 2rem;
        text-align: center;
        border-bottom: 1px solid rgba(226, 232, 240, 0.2);
        position: relative;
        overflow: hidden;
        flex-shrink: 0;
        min-height: 240px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        background: linear-gradient(135deg,
            rgba(248, 250, 252, 0.6) 0%,
            rgba(255, 255, 255, 0.8) 100%);
    }

    .pricing-plan-badge {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        padding: 0.75rem;
        font-size: 0.75rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        background: linear-gradient(135deg, #f8fafc, #e2e8f0);
        color: #64748b;
        border-bottom: 1px solid rgba(226, 232, 240, 0.3);
    }

    .pricing-plan-badge.premium {
        background: linear-gradient(135deg, #fef3c7, #fde68a);
        color: #d97706;
        border-bottom: 1px solid rgba(217, 119, 6, 0.2);
    }

    .pricing-plan-badge.enterprise {
        background: linear-gradient(135deg, #dbeafe, #bfdbfe);
        color: #2563eb;
        border-bottom: 1px solid rgba(37, 99, 235, 0.2);
    }

    .pricing-plan {
        font-size: 1.9rem;
        font-weight: 600;
        margin: 1.5rem 0 1.2rem;
        color: #0f172a;
        letter-spacing: -0.01em;
    }

    .pricing-amount {
        font-size: 3.8rem;
        font-weight: 700;
        margin-bottom: 0.75rem;
        color: #0f172a;
        line-height: 0.9;
        transition: all 0.4s ease;
        letter-spacing: -0.02em;
    }

    .pricing-amount.annually {
        display: none;
    }

    .pricing-period {
        font-size: 1.125rem;
        color: #64748b;
        margin-bottom: 0.5rem;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .pricing-period.annually {
        display: none;
    }

    .pricing-save {
        font-size: 0.875rem;
        font-weight: 700;
        color: white;
        background: linear-gradient(135deg, #f59e0b, #d97706);
        padding: 0.25rem 0.5rem;
        border-radius: 1rem;
        margin-left: 0.5rem;
        box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
    }

    /* Annual pricing display */
    body.annual-pricing .pricing-amount.monthly,
    body.annual-pricing .pricing-period.monthly {
        display: none;
    }

    body.annual-pricing .pricing-amount.annually,
    body.annual-pricing .pricing-period.annually {
        display: flex;
    }

    .pricing-card-body {
        padding: 2rem;
        display: flex;
        flex-direction: column;
        flex: 1;
        position: relative;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.9) 0%,
            rgba(248, 250, 252, 0.8) 100%);
    }

    .pricing-features-container {
        flex: 1;
        margin-bottom: 80px; /* Space for the button */
    }

    .pricing-btn-container {
        position: absolute;
        bottom: 2rem;
        left: 2rem;
        right: 2rem;
    }

    .feature-category {
        font-size: 0.875rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        color: #475569;
        margin: 1.5rem 0 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid rgba(226, 232, 240, 0.5);
    }

    .pricing-features {
        list-style: none;
        padding: 0;
        margin: 0 0 1.5rem;
    }

    .pricing-feature {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
        color: #334155;
        position: relative;
    }

    .feature-icon {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background: linear-gradient(135deg, #10b981, #059669);
        color: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        flex-shrink: 0;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(16, 185, 129, 0.2);
    }

    .feature-icon.premium {
        background: linear-gradient(135deg, #f59e0b, #d97706);
        box-shadow: 0 2px 8px rgba(245, 158, 11, 0.2);
    }

    .feature-icon.enterprise {
        background: linear-gradient(135deg, #3b82f6, #2563eb);
        box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
    }

    .feature-icon.unavailable {
        background: linear-gradient(135deg, #ef4444, #dc2626);
        box-shadow: 0 2px 8px rgba(239, 68, 68, 0.2);
    }

    .response-time {
        font-size: 0.75rem;
        color: #64748b;
        background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
        padding: 0.25rem 0.5rem;
        border-radius: 1rem;
        margin-left: 0.5rem;
        border: 1px solid rgba(100, 116, 139, 0.1);
    }

    .pricing-btn {
        display: block;
        width: 100%;
        padding: 1.2rem 1.5rem;
        text-align: center;
        border-radius: 16px;
        font-weight: 600;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        text-decoration: none;
        border: none;
        cursor: pointer;
        font-size: 1.05rem;
        letter-spacing: 0.01em;
    }

    .pricing-btn-primary {
        background: linear-gradient(135deg, #3b82f6, #2563eb);
        color: white;
        box-shadow: 0 4px 14px rgba(59, 130, 246, 0.3);
    }

    .pricing-btn-primary:hover {
        background: linear-gradient(135deg, #2563eb, #1d4ed8);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        color: white;
        text-decoration: none;
    }

    .pricing-btn-premium {
        background: linear-gradient(135deg, #f59e0b, #d97706);
        color: white;
        box-shadow: 0 4px 14px rgba(245, 158, 11, 0.3);
    }

    .pricing-btn-premium:hover {
        background: linear-gradient(135deg, #d97706, #b45309);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
        color: white;
        text-decoration: none;
    }

    .pricing-btn-outline {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
        color: #3b82f6;
        border: 2px solid #3b82f6;
        box-shadow: 0 4px 14px rgba(59, 130, 246, 0.1);
    }

    .pricing-btn-outline:hover {
        background: linear-gradient(135deg, #3b82f6, #2563eb);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
        text-decoration: none;
    }

    .popular-badge {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: linear-gradient(135deg, #f59e0b, #d97706);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 2rem;
        font-weight: 700;
        font-size: 0.875rem;
        box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
        animation: pulse 2s infinite;
        z-index: 2;
    }

    .elite-badge {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: linear-gradient(135deg, #3b82f6, #2563eb);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 2rem;
        font-weight: 700;
        font-size: 0.875rem;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        z-index: 2;
    }

    .payment-methods {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        gap: 1.5rem;
        margin-top: 3rem;
    }

    .payment-method {
        width: 80px;
        height: 50px;
        background-color: white;
        border-radius: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        padding: 0.5rem;
    }

    .payment-method:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    }

    .payment-method img {
        max-width: 90%;
        max-height: 90%;
        object-fit: contain;
    }

    .payment-method.mpesa img {
        max-width: 85%;
        max-height: 85%;
    }

    .btn-back-home {
        display: inline-flex;
        align-items: center;
        padding: 0.75rem 1.5rem;
        background: linear-gradient(to right, #f8f9fa, #e9ecef);
        color: #1e293b;
        font-weight: 600;
        border-radius: 0.75rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        text-decoration: none;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .btn-back-home:hover {
        background: linear-gradient(to right, #e9ecef, #dee2e6);
        transform: translateY(-2px);
        box-shadow: 0 6px 10px rgba(0, 0, 0, 0.08);
        color: #0f172a;
    }

    /* Enhanced Conversion-Optimized Styling */
    .trust-indicators {
        display: flex;
        justify-content: center;
        gap: 2rem;
        margin: 2rem 0;
        flex-wrap: wrap;
    }

    .trust-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 1rem 2rem;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
        backdrop-filter: blur(25px);
        border-radius: 16px;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.04);
        border: 1px solid rgba(255, 255, 255, 0.4);
        font-weight: 500;
        color: #1e293b;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        letter-spacing: 0.01em;
    }

    .trust-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    }

    .trust-item i {
        font-size: 1.1rem;
    }

    .value-proposition {
        font-size: 0.9rem;
        color: #6b7280;
        font-weight: 500;
        margin-top: 0.5rem;
        font-style: italic;
    }

    .roi-indicator {
        font-size: 0.85rem;
        color: #10b981;
        font-weight: 700;
        margin-top: 0.25rem;
        padding: 0.25rem 0.75rem;
        background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%);
        border-radius: 12px;
        border: 1px solid rgba(16, 185, 129, 0.2);
        display: inline-block;
    }

    .cents {
        font-size: 0.6em;
        vertical-align: top;
        opacity: 0.8;
    }

    .popular-badge {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
        color: white !important;
        font-weight: 700;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.85rem;
        box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
        animation: pulse 2s infinite;
    }

    .elite-badge {
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        color: white;
        font-weight: 700;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.85rem;
        box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
        position: absolute;
        top: -12px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 10;
        animation: glow 3s ease-in-out infinite alternate;
    }

    .enterprise-elite {
        border: 2px solid #8b5cf6 !important;
        position: relative;
    }

    .guarantee-text {
        font-size: 0.8rem;
        color: #10b981;
        font-weight: 600;
        margin-top: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.25rem;
    }

    .guarantee-text::before {
        content: "✓";
        color: #10b981;
        font-weight: bold;
    }

    .urgency-text {
        font-size: 0.75rem;
        color: #6b7280;
        font-weight: 500;
        margin-top: 0.25rem;
        font-style: italic;
    }

    .pricing-btn-premium {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
        border: none !important;
        font-weight: 700 !important;
        font-size: 1rem !important;
        padding: 1rem 2rem !important;
        border-radius: 12px !important;
        box-shadow: 0 8px 20px rgba(245, 158, 11, 0.3) !important;
        transition: all 0.3s ease !important;
        text-transform: none !important;
    }

    .pricing-btn-premium:hover {
        transform: translateY(-3px) !important;
        box-shadow: 0 12px 30px rgba(245, 158, 11, 0.4) !important;
        background: linear-gradient(135deg, #d97706 0%, #b45309 100%) !important;
    }

    .pricing-btn-primary {
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%) !important;
        border: none !important;
        font-weight: 700 !important;
        font-size: 1rem !important;
        padding: 1rem 2rem !important;
        border-radius: 12px !important;
        box-shadow: 0 8px 20px rgba(139, 92, 246, 0.3) !important;
        transition: all 0.3s ease !important;
        text-transform: none !important;
    }

    .pricing-btn-primary:hover {
        transform: translateY(-3px) !important;
        box-shadow: 0 12px 30px rgba(139, 92, 246, 0.4) !important;
        background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%) !important;
    }

    .pricing-btn-outline {
        background: transparent !important;
        border: 2px solid #6b7280 !important;
        color: #1e293b !important;
        font-weight: 600 !important;
        font-size: 1rem !important;
        padding: 1rem 2rem !important;
        border-radius: 12px !important;
        transition: all 0.3s ease !important;
        text-transform: none !important;
    }

    .pricing-btn-outline:hover {
        background: linear-gradient(135deg, #1e293b 0%, #334155 100%) !important;
        color: white !important;
        border-color: #1e293b !important;
        transform: translateY(-3px) !important;
        box-shadow: 0 8px 20px rgba(30, 41, 59, 0.3) !important;
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    @keyframes glow {
        from { box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3); }
        to { box-shadow: 0 8px 25px rgba(139, 92, 246, 0.6); }
    }

    /* Enhanced Mobile Responsiveness for Trust Indicators */
    @media (max-width: 768px) {
        .trust-indicators {
            gap: 1rem;
        }

        .trust-item {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }

        .trust-item span {
            display: none;
        }

        .trust-item i {
            font-size: 1.2rem;
        }
    }

    @media (max-width: 576px) {
        .trust-indicators {
            justify-content: space-around;
            gap: 0.5rem;
        }

        .trust-item {
            padding: 0.5rem;
            min-width: auto;
        }
    }

    /* Social Proof Section */
    .social-proof-section {
        background: linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(241, 245, 249, 0.95) 100%);
        backdrop-filter: blur(25px);
        border-radius: 24px;
        margin: 3rem 2rem;
        padding: 4rem 2rem;
        box-shadow:
            0 15px 35px rgba(0, 0, 0, 0.08),
            0 0 0 1px rgba(255, 255, 255, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.4);
        border: 1px solid rgba(255, 255, 255, 0.2);
        position: relative;
        overflow: hidden;
    }

    .social-proof-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
        animation: shimmer 4s ease-in-out infinite;
        pointer-events: none;
    }

    .social-proof-section h3 {
        font-family: 'Montserrat', sans-serif;
        font-size: 2rem;
        font-weight: 700;
        color: #1e293b;
        position: relative;
        z-index: 2;
    }

    .testimonial-card {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
        backdrop-filter: blur(15px);
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow:
            0 10px 25px rgba(0, 0, 0, 0.05),
            0 0 0 1px rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
        position: relative;
        z-index: 2;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .testimonial-card:hover {
        transform: translateY(-8px);
        box-shadow:
            0 20px 40px rgba(0, 0, 0, 0.1),
            0 0 0 1px rgba(255, 255, 255, 0.3);
    }

    .testimonial-content {
        margin-bottom: 1.5rem;
    }

    .testimonial-content p {
        font-size: 1rem;
        color: #475569;
        line-height: 1.6;
        font-style: italic;
        margin: 0;
    }

    .testimonial-author {
        margin-bottom: 1rem;
    }

    .testimonial-author strong {
        display: block;
        font-size: 1.1rem;
        color: #1e293b;
        font-weight: 600;
        margin-bottom: 0.25rem;
    }

    .testimonial-author span {
        font-size: 0.9rem;
        color: #6b7280;
        font-weight: 500;
    }

    .testimonial-rating {
        font-size: 1.2rem;
        text-align: center;
    }

    /* Comprehensive Mobile Responsiveness */
    @media (max-width: 1200px) {
        .pricing-header {
            padding: 5rem 2rem;
            margin: 2rem auto 4rem;
        }

        .pricing-title {
            font-size: 2.8rem;
        }

        .pricing-subtitle {
            font-size: 1.2rem;
        }
    }

    @media (max-width: 992px) {
        .pricing-header {
            padding: 4rem 2rem;
        }

        .pricing-title {
            font-size: 2.5rem;
        }

        .pricing-subtitle {
            font-size: 1.1rem;
        }

        .pricing-card.popular {
            transform: none;
            margin-bottom: 2rem;
        }

        .pricing-card.popular:hover {
            transform: translateY(-8px);
        }
    }

    @media (max-width: 768px) {
        body {
            padding: 0;
        }

        .pricing-header {
            padding: 3rem 1.5rem;
            margin: 1rem auto 3rem;
            border-radius: 24px;
        }

        .pricing-title {
            font-size: 2.2rem;
            margin-bottom: 1rem;
        }

        .pricing-subtitle {
            font-size: 1rem;
            margin-bottom: 2rem;
        }

        .billing-toggle-container {
            margin: 2rem 0;
        }

        .billing-option {
            font-size: 0.9rem;
            padding: 0 0.75rem;
        }

        .trust-indicators {
            flex-direction: column;
            gap: 1rem;
            margin: 2rem 0;
        }

        .trust-item {
            padding: 0.75rem 1.5rem;
            font-size: 0.9rem;
        }

        .pricing-card {
            margin-bottom: 2rem;
            border-radius: 20px;
        }

        .pricing-card-header {
            padding: 2rem 1.5rem;
            min-height: 200px;
        }

        .pricing-plan {
            font-size: 1.6rem;
        }

        .pricing-amount {
            font-size: 3.2rem;
        }

        .pricing-card-body {
            padding: 1.5rem;
        }

        .pricing-btn-container {
            bottom: 1.5rem;
            left: 1.5rem;
            right: 1.5rem;
        }

        .pricing-btn {
            padding: 1rem 1.25rem;
            font-size: 1rem;
        }

        .payment-methods {
            gap: 1rem;
            margin-top: 2rem;
        }

        .payment-method {
            width: 70px;
            height: 45px;
        }
    }

    @media (max-width: 576px) {
        .pricing-header {
            padding: 2.5rem 1rem;
            margin: 0.5rem auto 2rem;
            border-radius: 20px;
        }

        .pricing-title {
            font-size: 1.9rem;
            line-height: 1.2;
        }

        .pricing-subtitle {
            font-size: 0.95rem;
            line-height: 1.6;
        }

        .billing-option {
            font-size: 0.85rem;
            padding: 0 0.5rem;
        }

        .billing-toggle {
            width: 50px;
            height: 25px;
            margin: 0 0.25rem;
        }

        .toggle-slider:before {
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
        }

        input:checked + .toggle-slider:before {
            transform: translateX(25px);
        }

        .trust-indicators {
            gap: 0.75rem;
            margin: 1.5rem 0;
        }

        .trust-item {
            padding: 0.5rem 1rem;
            font-size: 0.8rem;
            flex-direction: column;
            text-align: center;
            gap: 0.5rem;
        }

        .pricing-card {
            border-radius: 16px;
        }

        .pricing-card-header {
            padding: 1.5rem 1rem;
            min-height: 180px;
        }

        .pricing-plan {
            font-size: 1.4rem;
            margin: 1rem 0;
        }

        .pricing-amount {
            font-size: 2.8rem;
        }

        .pricing-period {
            font-size: 1rem;
        }

        .pricing-card-body {
            padding: 1rem;
        }

        .pricing-features-container {
            margin-bottom: 70px;
        }

        .pricing-btn-container {
            bottom: 1rem;
            left: 1rem;
            right: 1rem;
        }

        .pricing-btn {
            padding: 0.9rem 1rem;
            font-size: 0.95rem;
            border-radius: 12px;
        }

        .feature-category {
            font-size: 0.8rem;
            margin: 1rem 0 0.75rem;
        }

        .pricing-feature {
            margin-bottom: 0.75rem;
            font-size: 0.9rem;
        }

        .feature-icon {
            width: 20px;
            height: 20px;
            margin-right: 0.75rem;
        }

        .payment-methods {
            gap: 0.75rem;
            margin-top: 1.5rem;
        }

        .payment-method {
            width: 60px;
            height: 40px;
        }

        .popular-badge,
        .elite-badge {
            top: 0.75rem;
            right: 0.75rem;
            padding: 0.4rem 0.8rem;
            font-size: 0.75rem;
        }

        .pricing-plan-badge {
            padding: 0.5rem;
            font-size: 0.7rem;
        }

        .pricing-plan {
            font-size: 1.4rem;
            margin: 1rem 0;
        }

        .pricing-amount {
            font-size: 2.8rem;
            line-height: 1;
        }

        .pricing-period {
            font-size: 1rem;
        }

        .value-proposition {
            font-size: 0.8rem;
            margin-bottom: 0.5rem;
        }
    }

    @media (max-width: 480px) {
        .pricing-header {
            padding: 2rem 0.75rem;
        }

        .pricing-title {
            font-size: 1.7rem;
        }

        .pricing-subtitle {
            font-size: 0.9rem;
        }

        .pricing-card-header {
            padding: 1.25rem 0.75rem;
            min-height: 160px;
        }

        .pricing-plan {
            font-size: 1.3rem;
        }

        .pricing-amount {
            font-size: 2.5rem;
        }

        .pricing-card-body {
            padding: 0.75rem;
        }

        .pricing-btn {
            padding: 0.8rem;
            font-size: 0.9rem;
        }
    }

    /* Enhanced Trust Indicators */
    .trust-indicators {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        gap: 2rem;
        margin: 3rem 0;
        position: relative;
        z-index: 2;
    }

    .trust-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 1rem 1.5rem;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.1) 0%,
            rgba(255, 255, 255, 0.05) 100%);
        backdrop-filter: blur(20px);
        border-radius: 16px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        color: #ffffff;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .trust-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.15) 0%,
            rgba(255, 255, 255, 0.08) 100%);
    }

    .trust-item i {
        font-size: 1.25rem;
    }







    /* Enhanced Payment Methods */
    .payment-methods {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
        gap: 1.5rem;
        margin: 2rem 0;
    }

    .payment-method {
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.1) 0%,
            rgba(255, 255, 255, 0.05) 100%);
        backdrop-filter: blur(20px);
        border-radius: 12px;
        padding: 1rem;
        border: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .payment-method:hover {
        transform: translateY(-3px);
        box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
    }

    .payment-method img {
        height: 32px;
        width: auto;
        filter: brightness(1.2);
    }

    /* Enhanced Mobile Responsiveness */
    @media (max-width: 768px) {
        .trust-indicators {
            gap: 1rem;
        }

        .trust-item {
            padding: 0.75rem 1rem;
            font-size: 0.9rem;
        }

        .payment-methods {
            gap: 1rem;
        }

        .payment-method {
            padding: 0.75rem;
        }

        .payment-method img {
            height: 24px;
        }
    }

    /* Enhanced Container */
    .enterprise-container {
        padding: 2rem 0;
        position: relative;
    }

    /* Enhanced Payment Section */
    .text-center.mb-5 {
        color: #ffffff;
        margin-top: 3rem;
    }

    .text-center.mb-5 h4 {
        color: #ffffff;
        font-weight: 700;
        margin-bottom: 2rem;
    }

    /* Enhanced Back to Home Button */
    .btn-back-home {
        display: inline-flex;
        align-items: center;
        padding: 1rem 2rem;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.1) 0%,
            rgba(255, 255, 255, 0.05) 100%);
        backdrop-filter: blur(20px);
        border-radius: 12px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        color: #ffffff;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .btn-back-home:hover {
        transform: translateY(-3px);
        box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
        color: #ffffff;
        text-decoration: none;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.15) 0%,
            rgba(255, 255, 255, 0.08) 100%);
    }
</style>
{% endblock %}

{% block content %}
<div class="enterprise-container">
    <div class="container">
        <div class="pricing-header">
            <h1 class="pricing-title">
                <i class="fas fa-rocket me-3" style="background: linear-gradient(135deg, #6366f1, #8b5cf6); -webkit-background-clip: text; -webkit-text-fill-color: transparent;"></i>
                Scale Your Startup to New Heights
            </h1>
            <p class="pricing-subtitle">Join 10K+ startups and growing businesses using our QR solutions to boost engagement and drive growth. Start your journey today.</p>

            <!-- Trust Indicators -->
            <div class="trust-indicators">

                <div class="trust-item">
                    <i class="fas fa-clock" style="color: #3b82f6;"></i>
                    <span>99.5% Uptime</span>
                </div>
                <div class="trust-item">
                    <i class="fas fa-users" style="color: #8b5cf6;"></i>
                    <span>Startup-Friendly Support</span>
                </div>
                <div class="trust-item">
                    <i class="fas fa-chart-line" style="color: #f59e0b;"></i>
                    <span>3x Better ROI</span>
                </div>
            </div>

            <div class="billing-toggle-container">
                <span class="billing-option monthly active">Monthly</span>
                <label class="billing-toggle">
                    <input type="checkbox" id="billingToggle">
                    <span class="toggle-slider"></span>
                </label>
                <span class="billing-option annually">
                    Annually
                    <span class="discount-badge">Save 20%</span>
                </span>
            </div>
        </div>

        <div class="row row-cols-1 row-cols-md-3 g-4 mb-5">
    <!-- Starter Plan -->
    <div class="col">
        <div class="pricing-card h-100">
            <div class="pricing-card-header">
                <div class="pricing-plan-badge">Starter</div>
                <h3 class="pricing-plan">Starter</h3>
                <div class="pricing-amount">Free</div>
                <div class="pricing-period">Perfect for Testing</div>

            </div>
            <div class="pricing-card-body">
                <div class="pricing-features-container">
                    <div class="feature-category">Core Features</div>
                    <ul class="pricing-features">
                        <li class="pricing-feature">
                            <div class="feature-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <span>Standard QR Code Generation</span>
                        </li>
                        <li class="pricing-feature">
                            <div class="feature-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <span>Basic Customization Options</span>
                        </li>
                        <li class="pricing-feature">
                            <div class="feature-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <span>Up to 50 QR Codes per Month</span>
                        </li>
                    </ul>

                    <div class="feature-category">Advanced Features</div>
                    <ul class="pricing-features">
                        <li class="pricing-feature">
                            <div class="feature-icon unavailable">
                                <i class="fas fa-times"></i>
                            </div>
                            <span class="text-muted">Batch Processing</span>
                        </li>
                        <li class="pricing-feature">
                            <div class="feature-icon unavailable">
                                <i class="fas fa-times"></i>
                            </div>
                            <span class="text-muted">Advanced Analytics</span>
                        </li>
                        <li class="pricing-feature">
                            <div class="feature-icon unavailable">
                                <i class="fas fa-times"></i>
                            </div>
                            <span class="text-muted">API Access</span>
                        </li>
                    </ul>

                    <div class="feature-category">Support</div>
                    <ul class="pricing-features">
                        <li class="pricing-feature">
                            <div class="feature-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <span>Community Support</span>
                        </li>
                    </ul>
                </div>
                <div class="pricing-btn-container">
                    <a href="{% url 'generate_qr_code' %}" class="pricing-btn pricing-btn-outline">Start Free Trial</a>
                    <div class="guarantee-text">No credit card required</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Professional Plan -->
    <div class="col">
        <div class="pricing-card popular h-100">
            <div class="popular-badge">🔥 Most Popular</div>
            <div class="pricing-card-header">
                <div class="pricing-plan-badge premium">Professional</div>
                <h3 class="pricing-plan">Professional</h3>
                <div class="pricing-amount monthly">$19<span class="cents">.99</span></div>
                <div class="pricing-amount annually">$191<span class="cents">.90</span></div>
                <div class="pricing-period monthly">per month</div>
                <div class="pricing-period annually">per year <span class="pricing-save">Save $48</span></div>
                <div class="value-proposition">Perfect for growing startups</div>
            </div>
            <div class="pricing-card-body">
                <div class="pricing-features-container">
                    <div class="feature-category">Core Features</div>
                    <ul class="pricing-features">
                        <li class="pricing-feature">
                            <div class="feature-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <span>Everything in Basic</span>
                        </li>
                        <li class="pricing-feature">
                            <div class="feature-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <span>Advanced Customization</span>
                        </li>
                        <li class="pricing-feature">
                            <div class="feature-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <span>Unlimited QR Codes</span>
                        </li>
                    </ul>

                    <div class="feature-category">QR Pro Features</div>
                    <ul class="pricing-features">
                        <li class="pricing-feature">
                            <div class="feature-icon premium">
                                <i class="fas fa-magic"></i>
                            </div>
                            <span><strong>AI Landing Pages</strong> <span class="new-badge">New!</span></span>
                        </li>
                        <li class="pricing-feature">
                            <div class="feature-icon premium">
                                <i class="fas fa-exchange-alt"></i>
                            </div>
                            <span><strong>Dynamic QR Redirects</strong> <span class="new-badge">New!</span></span>
                        </li>
                        <li class="pricing-feature">
                            <div class="feature-icon premium">
                                <i class="fas fa-palette"></i>
                            </div>
                            <span>Branded Landing Pages</span>
                        </li>
                        <li class="pricing-feature">
                            <div class="feature-icon premium">
                                <i class="fas fa-link"></i>
                            </div>
                            <span>Short URL System</span>
                        </li>
                    </ul>

                    <div class="feature-category">Advanced Features</div>
                    <ul class="pricing-features">
                        <li class="pricing-feature">
                            <div class="feature-icon premium">
                                <i class="fas fa-check"></i>
                            </div>
                            <span><strong>Batch Processing</strong> (up to 1,000 codes)</span>
                        </li>
                        <li class="pricing-feature">
                            <div class="feature-icon premium">
                                <i class="fas fa-check"></i>
                            </div>
                            <span>Basic Analytics Dashboard</span>
                        </li>
                        <li class="pricing-feature">
                            <div class="feature-icon premium">
                                <i class="fas fa-check"></i>
                            </div>
                            <span>Custom Logo Integration</span>
                        </li>
                        <li class="pricing-feature">
                            <div class="feature-icon unavailable">
                                <i class="fas fa-times"></i>
                            </div>
                            <span class="text-muted">API Access</span>
                        </li>
                    </ul>

                    <div class="feature-category">Support</div>
                    <ul class="pricing-features">
                        <li class="pricing-feature">
                            <div class="feature-icon premium">
                                <i class="fas fa-check"></i>
                            </div>
                            <span>Email Support</span>
                            <span class="response-time">24-hour response</span>
                        </li>
                    </ul>
                </div>
                <div class="pricing-btn-container">
                    <a href="{% url 'checkout' %}?plan=premium" class="pricing-btn pricing-btn-premium">🚀 Scale Your Startup</a>
                    <div class="urgency-text">Join 8K+ growing startups</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enterprise Plan -->
    <div class="col">
        <div class="pricing-card enterprise-elite h-100">
            <div class="elite-badge">⭐ Enterprise Elite</div>
            <div class="pricing-card-header">
                <div class="pricing-plan-badge enterprise">Enterprise</div>
                <h3 class="pricing-plan">Enterprise</h3>
                <div class="pricing-amount monthly">$49<span class="cents">.99</span></div>
                <div class="pricing-amount annually">$479<span class="cents">.90</span></div>
                <div class="pricing-period monthly">per month</div>
                <div class="pricing-period annually">per year <span class="pricing-save">Save $120</span></div>
                <div class="value-proposition">Scale-ready for ambitious startups</div>
            </div>
            <div class="pricing-card-body">
                <div class="pricing-features-container">
                    <div class="feature-category">Core Features</div>
                    <ul class="pricing-features">
                        <li class="pricing-feature">
                            <div class="feature-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <span>Everything in Premium</span>
                        </li>
                        <li class="pricing-feature">
                            <div class="feature-icon enterprise">
                                <i class="fas fa-check"></i>
                            </div>
                            <span>White-label QR Codes</span>
                        </li>
                        <li class="pricing-feature">
                            <div class="feature-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <span>Unlimited QR Codes</span>
                        </li>
                    </ul>

                    <div class="feature-category">QR Pro Features</div>
                    <ul class="pricing-features">
                        <li class="pricing-feature">
                            <div class="feature-icon enterprise">
                                <i class="fas fa-magic"></i>
                            </div>
                            <span><strong>AI Landing Pages</strong> (Unlimited) <span class="new-badge">New!</span></span>
                        </li>
                        <li class="pricing-feature">
                            <div class="feature-icon enterprise">
                                <i class="fas fa-exchange-alt"></i>
                            </div>
                            <span><strong>Dynamic QR Redirects</strong> (Unlimited) <span class="new-badge">New!</span></span>
                        </li>
                        <li class="pricing-feature">
                            <div class="feature-icon enterprise">
                                <i class="fas fa-palette"></i>
                            </div>
                            <span>White-label Branded Pages</span>
                        </li>
                        <li class="pricing-feature">
                            <div class="feature-icon enterprise">
                                <i class="fas fa-link"></i>
                            </div>
                            <span>Custom Domain Short URLs</span>
                        </li>
                    </ul>

                    <div class="feature-category">Advanced Features</div>
                    <ul class="pricing-features">
                        <li class="pricing-feature">
                            <div class="feature-icon enterprise">
                                <i class="fas fa-check"></i>
                            </div>
                            <span><strong>Batch Processing</strong> (unlimited)</span>
                        </li>
                        <li class="pricing-feature">
                            <div class="feature-icon enterprise">
                                <i class="fas fa-check"></i>
                            </div>
                            <span>Advanced Analytics & Reporting</span>
                        </li>
                        <li class="pricing-feature">
                            <div class="feature-icon enterprise">
                                <i class="fas fa-check"></i>
                            </div>
                            <span>Full API Access</span>
                        </li>
                        <li class="pricing-feature">
                            <div class="feature-icon enterprise">
                                <i class="fas fa-check"></i>
                            </div>
                            <span>Custom Integrations</span>
                        </li>
                        <li class="pricing-feature">
                            <div class="feature-icon enterprise">
                                <i class="fas fa-check"></i>
                            </div>
                            <span>Dedicated Server Resources</span>
                        </li>
                    </ul>

                    <div class="feature-category">Support</div>
                    <ul class="pricing-features">
                        <li class="pricing-feature">
                            <div class="feature-icon enterprise">
                                <i class="fas fa-check"></i>
                            </div>
                            <span>Priority Support</span>
                            <span class="response-time">4-hour response</span>
                        </li>
                        <li class="pricing-feature">
                            <div class="feature-icon enterprise">
                                <i class="fas fa-check"></i>
                            </div>
                            <span>Dedicated Account Manager</span>
                        </li>
                    </ul>
                </div>
                <div class="pricing-btn-container">
                    <a href="{% url 'checkout' %}?plan=enterprise" class="pricing-btn pricing-btn-primary">⭐ Unlock Full Potential</a>
                    <div class="urgency-text">Trusted by 2K+ scaling startups</div>
                </div>
            </div>
        </div>
    </div>


</div>

<div class="text-center mb-5">
    <h4 class="mb-3">Secure Payment Methods</h4>
    <div class="payment-methods">
        <div class="payment-method">
            <img src="https://cdn.visa.com/v2/assets/images/logos/visa/blue/logo.png" alt="Visa" title="Visa">
        </div>
        <div class="payment-method">
            <img src="https://www.mastercard.com/content/dam/public/brandcenter/assets/images/logos/mastercard/logo.svg" alt="Mastercard" title="Mastercard">
        </div>
        <div class="payment-method">
            <img src="https://www.americanexpress.com/content/dam/amex/us/merchant/supplies-uplift/logos/amex-logo-color-100x100.png" alt="American Express" title="American Express">
        </div>
        <div class="payment-method">
            <img src="https://www.paypalobjects.com/webstatic/mktg/logo/pp_cc_mark_111x69.jpg" alt="PayPal" title="PayPal">
        </div>
        <div class="payment-method">
            <img src="https://developer.apple.com/apple-pay/marketing/images/apple-pay-mark.svg" alt="Apple Pay" title="Apple Pay">
        </div>
        <div class="payment-method mpesa">
            <img src="https://www.safaricom.co.ke/images/M-PESA_LOGO.png" alt="M-Pesa" title="M-Pesa">
        </div>
    </div>

        <div class="mt-5 text-center">
            <a href="{% url 'index' %}" class="btn-back-home">
                <i class="fas fa-arrow-left me-2"></i>Back to Home
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const billingToggle = document.getElementById('billingToggle');
        const monthlyOption = document.querySelector('.billing-option.monthly');
        const annuallyOption = document.querySelector('.billing-option.annually');

        // Toggle between monthly and annual billing
        billingToggle.addEventListener('change', function() {
            if (this.checked) {
                // Annual billing
                document.body.classList.add('annual-pricing');
                monthlyOption.classList.remove('active');
                annuallyOption.classList.add('active');

                // Update checkout URLs to include billing period
                updateCheckoutUrls('annual');
            } else {
                // Monthly billing
                document.body.classList.remove('annual-pricing');
                annuallyOption.classList.remove('active');
                monthlyOption.classList.add('active');

                // Update checkout URLs to include billing period
                updateCheckoutUrls('monthly');
            }
        });

        // Click handlers for the billing option text
        monthlyOption.addEventListener('click', function() {
            billingToggle.checked = false;
            billingToggle.dispatchEvent(new Event('change'));
        });

        annuallyOption.addEventListener('click', function() {
            billingToggle.checked = true;
            billingToggle.dispatchEvent(new Event('change'));
        });

        // Function to update checkout URLs
        function updateCheckoutUrls(billingPeriod) {
            const checkoutButtons = document.querySelectorAll('.pricing-btn-premium, .pricing-btn-primary');

            checkoutButtons.forEach(button => {
                const currentUrl = button.getAttribute('href');
                const baseUrl = currentUrl.split('?')[0];
                const planParam = currentUrl.includes('premium') ? 'premium' : 'enterprise';

                button.setAttribute('href', `${baseUrl}?plan=${planParam}&billing=${billingPeriod}`);
            });
        }

        // Add hover effects to pricing cards
        const pricingCards = document.querySelectorAll('.pricing-card');

        pricingCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.classList.add('hover-effect');
            });

            card.addEventListener('mouseleave', function() {
                this.classList.remove('hover-effect');
            });
        });
    });
</script>
{% endblock %}