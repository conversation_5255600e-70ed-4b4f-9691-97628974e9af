document.addEventListener('DOMContentLoaded', function() {
    console.log('Ads Dashboard JS loaded');
    
    // Add animations to cards
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 10px 20px rgba(0, 0, 0, 0.1)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'none';
        });
    });
    
    // Example: Fetch recent activity data
    function fetchRecentActivity() {
        // In a real implementation, this would make an AJAX call to get the latest data
        console.log('Fetching recent activity data...');
    }
    
    // Call the function when the page loads
    fetchRecentActivity();
});
