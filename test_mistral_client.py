import os
import sys
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'QRCodeGenerator.settings')
django.setup()

from ai_services.clients import MistralAIClient
from ai_services.settings import MISTRAL_API_KEY, MISTRAL_MODEL

def test_mistral_client():
    """Test the Mistral AI client directly"""
    print(f"Testing Mistral AI client with API key: {MISTRAL_API_KEY[:4]}...{MISTRAL_API_KEY[-4:]}")
    print(f"Using model: {MISTRAL_MODEL}")

    try:
        # Initialize the client
        client = MistralAIClient()
        print("Client initialized successfully")

        # Generate ad suggestions
        suggestions = client.generate_ad_suggestions(
            language="english",
            business_type="coffee shop",
            target_audience="young professionals",
            tone="professional",
            num_suggestions=1,
            ad_title="Best Coffee in Town"
        )

        print("Suggestions generated successfully:")
        for i, suggestion in enumerate(suggestions):
            print(f"\nSuggestion {i+1}:")
            print(f"Title: {suggestion.get('title', 'No title')}")
            print(f"Content: {suggestion.get('content', 'No content')}")

        return True
    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_mistral_client()
    sys.exit(0 if success else 1)
