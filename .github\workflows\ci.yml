name: <PERSON><PERSON> - <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and Dockerize

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build-and-test:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v3
    
    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        cache: 'pip'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install black flake8 pytest
    
    - name: Check formatting with Black
      run: |
        black --check --line-length 100 .
    
    - name: Lint with Flake8
      run: |
        # stop the build if there are Python syntax errors or undefined names
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        # exit-zero treats all errors as warnings
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=100 --statistics
    
    - name: Run Django tests
      run: |
        python manage.py test
    
  build-docker-images:
    runs-on: ubuntu-latest
    needs: build-and-test
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v3
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2
    
    - name: Cache Docker layers
      uses: actions/cache@v3
      with:
        path: /tmp/.buildx-cache
        key: ${{ runner.os }}-buildx-${{ github.sha }}
        restore-keys: |
          ${{ runner.os }}-buildx-
    
    - name: Build Django web image
      uses: docker/build-push-action@v4
      with:
        context: .
        file: ./Dockerfile
        push: false
        tags: qrcode-generator-web:latest
        cache-from: type=local,src=/tmp/.buildx-cache
        cache-to: type=local,dest=/tmp/.buildx-cache-new,mode=max
    
    - name: Build AI Engine image
      uses: docker/build-push-action@v4
      with:
        context: ./ai_engine
        file: ./ai_engine/Dockerfile
        push: false
        tags: qrcode-generator-ai-engine:latest
        cache-from: type=local,src=/tmp/.buildx-cache
        cache-to: type=local,dest=/tmp/.buildx-cache-new,mode=max
    
    # Temp fix for https://github.com/docker/build-push-action/issues/252
    - name: Move cache
      run: |
        rm -rf /tmp/.buildx-cache
        mv /tmp/.buildx-cache-new /tmp/.buildx-cache
    
    - name: Test Docker Compose
      run: |
        docker-compose config
        docker-compose build
    
    - name: Run security scan on images
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'qrcode-generator-web:latest'
        format: 'table'
        exit-code: '1'
        ignore-unfixed: true
        vuln-type: 'os,library'
        severity: 'CRITICAL,HIGH'
    
    - name: Run security scan on AI Engine image
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'qrcode-generator-ai-engine:latest'
        format: 'table'
        exit-code: '1'
        ignore-unfixed: true
        vuln-type: 'os,library'
        severity: 'CRITICAL,HIGH'
