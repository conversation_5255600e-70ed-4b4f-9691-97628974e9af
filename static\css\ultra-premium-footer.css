/* Ultra-Premium Enterprise Footer Styling */

/* Main Footer Container */
.ultra-premium-footer {
    position: relative;
    background: linear-gradient(135deg,
        #000000 0%,
        #0a0a0a 8%,
        #1a1a2e 20%,
        #16213e 35%,
        #0f3460 55%,
        #533483 75%,
        #764ba2 95%,
        #8e44ad 100%);
    overflow: hidden;
    margin-top: 5rem;
}

/* Animated Background Elements */
.footer-animated-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    overflow: hidden;
}

.footer-gradient-orb {
    position: absolute;
    border-radius: 50%;
    filter: blur(60px);
    animation: footerOrbFloat 25s ease-in-out infinite;
}

.footer-orb-1 {
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.4) 0%, transparent 70%);
    top: -150px;
    left: -150px;
    animation-delay: 0s;
}

.footer-orb-2 {
    width: 400px;
    height: 400px;
    background: radial-gradient(circle, rgba(118, 75, 162, 0.3) 0%, transparent 70%);
    top: 20%;
    right: -200px;
    animation-delay: -8s;
}

.footer-orb-3 {
    width: 250px;
    height: 250px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    bottom: -125px;
    left: 30%;
    animation-delay: -15s;
}

.footer-orb-4 {
    width: 350px;
    height: 350px;
    background: radial-gradient(circle, rgba(142, 68, 173, 0.25) 0%, transparent 70%);
    top: 60%;
    left: 10%;
    animation-delay: -20s;
}

@keyframes footerOrbFloat {
    0%, 100% { transform: translateY(0px) translateX(0px) scale(1); }
    25% { transform: translateY(-30px) translateX(20px) scale(1.1); }
    50% { transform: translateY(20px) translateX(-15px) scale(0.9); }
    75% { transform: translateY(-15px) translateX(25px) scale(1.05); }
}

/* Corporate Pattern Overlay */
.footer-corporate-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.05) 2px, transparent 2px),
        radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.03) 1px, transparent 1px),
        linear-gradient(45deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px),
        linear-gradient(-45deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
    background-size: 100px 100px, 60px 60px, 40px 40px, 40px 40px;
    z-index: 1;
    opacity: 0.6;
    animation: patternPulse 20s ease-in-out infinite;
}

@keyframes patternPulse {
    0%, 100% { opacity: 0.4; transform: scale(1); }
    50% { opacity: 0.8; transform: scale(1.02); }
}

/* Premium Top Accent */
.footer-premium-accent {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 6px;
    z-index: 2;
    display: flex;
    gap: 2px;
}

.accent-line {
    flex: 1;
    height: 100%;
    animation: accentGlow 4s ease-in-out infinite;
}

.accent-line-1 {
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    animation-delay: 0s;
}

.accent-line-2 {
    background: linear-gradient(90deg, #764ba2 0%, #8e44ad 100%);
    animation-delay: 1.3s;
}

.accent-line-3 {
    background: linear-gradient(90deg, #8e44ad 0%, #667eea 100%);
    animation-delay: 2.6s;
}

@keyframes accentGlow {
    0%, 100% { opacity: 0.6; transform: scaleY(1); }
    50% { opacity: 1; transform: scaleY(1.5); }
}

/* Main Footer Content */
.footer-main-content {
    position: relative;
    z-index: 3;
    padding: 6rem 0 4rem;
}

/* Premium Brand Section */
.footer-brand-showcase {
    margin-bottom: 5rem;
    padding: 3rem 0;
    border-bottom: 2px solid rgba(255, 255, 255, 0.1);
}

.footer-premium-logo {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
}

.logo-icon-container {
    position: relative;
    width: 80px;
    height: 80px;
    margin-right: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-icon-bg {
    position: absolute;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    animation: logoIconPulse 3s ease-in-out infinite;
}

.logo-icon {
    position: relative;
    z-index: 2;
    font-size: 2.5rem;
    color: white;
    filter: drop-shadow(0 4px 15px rgba(0, 0, 0, 0.3));
}

.logo-icon-glow {
    position: absolute;
    width: 120%;
    height: 120%;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    top: -10%;
    left: -10%;
    animation: logoGlow 4s ease-in-out infinite;
}

@keyframes logoIconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes logoGlow {
    0%, 100% { opacity: 0.5; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.1); }
}

.logo-text {
    display: flex;
    flex-direction: column;
}

.logo-main {
    font-size: 2.2rem;
    font-weight: 900;
    color: white;
    background: linear-gradient(135deg, #ffffff 0%, #e0e0e0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    line-height: 1.1;
}

.logo-tagline {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-top: 0.3rem;
}

.footer-brand-description {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.7;
    margin-bottom: 2rem;
    font-weight: 500;
}

/* Footer Stats */
.footer-stats {
    display: flex;
    gap: 3rem;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 900;
    color: white;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    line-height: 1;
    animation: statPulse 3s ease-in-out infinite;
}

.stat-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-top: 0.5rem;
}

@keyframes statPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* CTA Section */
.footer-cta-section {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

.cta-card {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 100%);
    backdrop-filter: blur(30px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 32px;
    padding: 3rem 2.5rem;
    text-align: center;
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.6s ease;
    position: relative;
    overflow: hidden;
}

.cta-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: conic-gradient(from 0deg,
        rgba(102, 126, 234, 0.2) 0deg,
        rgba(255, 255, 255, 0.1) 90deg,
        rgba(118, 75, 162, 0.2) 180deg,
        rgba(255, 255, 255, 0.05) 270deg,
        rgba(102, 126, 234, 0.2) 360deg);
    z-index: 0;
    animation: ctaCardGlow 8s linear infinite;
    opacity: 0;
    transition: opacity 0.6s ease;
}

.cta-card:hover::before {
    opacity: 1;
}

.cta-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow:
        0 40px 80px rgba(0, 0, 0, 0.4),
        0 0 60px rgba(102, 126, 234, 0.3),
        inset 0 2px 0 rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.4);
}

@keyframes ctaCardGlow {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.cta-card > * {
    position: relative;
    z-index: 1;
}

.cta-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
    box-shadow:
        0 15px 30px rgba(102, 126, 234, 0.4),
        inset 0 2px 0 rgba(255, 255, 255, 0.3);
    animation: ctaIconFloat 4s ease-in-out infinite;
}

@keyframes ctaIconFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(5deg); }
}

.cta-title {
    font-size: 1.8rem;
    font-weight: 800;
    color: white;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #ffffff 0%, #e0e0e0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.cta-description {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 2rem;
    font-weight: 500;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-cta {
    padding: 1rem 2rem;
    border-radius: 20px;
    font-weight: 700;
    font-size: 0.9rem;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.5s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
}

.btn-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.3) 50%,
        transparent 100%);
    transition: left 0.8s ease;
}

.btn-cta:hover::before {
    left: 100%;
}

.btn-cta-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    box-shadow:
        0 8px 25px rgba(102, 126, 234, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.btn-cta-primary:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    transform: translateY(-3px) scale(1.05);
    box-shadow:
        0 15px 40px rgba(102, 126, 234, 0.5),
        inset 0 2px 0 rgba(255, 255, 255, 0.3);
    color: white;
}

.btn-cta-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(10px);
}

.btn-cta-secondary:hover {
    background: rgba(255, 255, 255, 0.9);
    color: #2c3e50;
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 12px 30px rgba(255, 255, 255, 0.3);
}

/* Navigation Grid */
.footer-navigation-grid {
    margin: 4rem 0;
    padding: 3rem 0;
    border-top: 2px solid rgba(255, 255, 255, 0.1);
    border-bottom: 2px solid rgba(255, 255, 255, 0.1);
}

.footer-nav-section {
    margin-bottom: 2rem;
}

.nav-section-title {
    font-size: 1.2rem;
    font-weight: 800;
    color: white;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    background: linear-gradient(135deg, #ffffff 0%, #e0e0e0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-section-title i {
    color: #667eea;
    font-size: 1.1rem;
    filter: drop-shadow(0 2px 8px rgba(102, 126, 234, 0.4));
}

.nav-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-links li {
    margin-bottom: 0.8rem;
}

.nav-link {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    display: inline-block;
    position: relative;
    padding: 0.3rem 0;
}

.nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    transition: width 0.3s ease;
}

.nav-link:hover {
    color: white;
    transform: translateX(8px);
    text-shadow: 0 2px 10px rgba(255, 255, 255, 0.3);
}

.nav-link:hover::before {
    width: 100%;
}

/* Contact & Newsletter Section */
.footer-contact-newsletter {
    margin: 4rem 0;
    padding: 3rem 0;
}

.contact-info-premium {
    margin-bottom: 2rem;
}

.contact-title {
    font-size: 1.4rem;
    font-weight: 800;
    color: white;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    background: linear-gradient(135deg, #ffffff 0%, #e0e0e0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.contact-title i {
    color: #667eea;
    filter: drop-shadow(0 2px 8px rgba(102, 126, 234, 0.4));
}

.contact-details {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.contact-item-premium {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.contact-item-premium:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.contact-icon-wrapper {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    box-shadow:
        0 8px 20px rgba(102, 126, 234, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.contact-icon-wrapper i {
    color: white;
    font-size: 1.2rem;
}

.contact-text {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    font-weight: 500;
}

.contact-text strong {
    color: white;
    font-weight: 700;
    display: block;
    margin-bottom: 0.3rem;
}

.contact-text a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
}

.contact-text a:hover {
    color: #667eea;
    text-shadow: 0 0 10px rgba(102, 126, 234, 0.5);
}

/* Newsletter Premium */
.newsletter-premium {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

.newsletter-card {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 100%);
    backdrop-filter: blur(30px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 32px;
    padding: 2.5rem;
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.6s ease;
    position: relative;
    overflow: hidden;
}

.newsletter-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: conic-gradient(from 0deg,
        rgba(102, 126, 234, 0.15) 0deg,
        rgba(255, 255, 255, 0.08) 90deg,
        rgba(118, 75, 162, 0.15) 180deg,
        rgba(255, 255, 255, 0.05) 270deg,
        rgba(102, 126, 234, 0.15) 360deg);
    z-index: 0;
    animation: newsletterCardGlow 10s linear infinite;
    opacity: 0;
    transition: opacity 0.6s ease;
}

.newsletter-card:hover::before {
    opacity: 1;
}

.newsletter-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
        0 40px 80px rgba(0, 0, 0, 0.4),
        0 0 60px rgba(102, 126, 234, 0.3),
        inset 0 2px 0 rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.4);
}

@keyframes newsletterCardGlow {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.newsletter-card > * {
    position: relative;
    z-index: 1;
}

.newsletter-header {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.newsletter-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    box-shadow:
        0 12px 25px rgba(102, 126, 234, 0.4),
        inset 0 2px 0 rgba(255, 255, 255, 0.3);
    animation: newsletterIconFloat 3s ease-in-out infinite;
}

@keyframes newsletterIconFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-5px) rotate(3deg); }
}

.newsletter-title {
    font-size: 1.4rem;
    font-weight: 800;
    color: white;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #e0e0e0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.newsletter-subtitle {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    margin: 0;
    font-weight: 500;
}

.newsletter-form-premium {
    margin-bottom: 1.5rem;
}

.input-group-premium {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.newsletter-input-premium {
    flex: 1;
    padding: 1rem 1.5rem;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-weight: 500;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.newsletter-input-premium::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.newsletter-input-premium:focus {
    outline: none;
    border-color: #667eea;
    background: rgba(255, 255, 255, 0.15);
    box-shadow:
        0 0 0 3px rgba(102, 126, 234, 0.2),
        0 8px 25px rgba(0, 0, 0, 0.2);
    transform: translateY(-2px);
}

.newsletter-btn-premium {
    padding: 1rem 2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 16px;
    font-weight: 700;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.5s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow:
        0 8px 25px rgba(102, 126, 234, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.newsletter-btn-premium::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.3) 50%,
        transparent 100%);
    transition: left 0.8s ease;
}

.newsletter-btn-premium:hover::before {
    left: 100%;
}

.newsletter-btn-premium:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    transform: translateY(-3px) scale(1.05);
    box-shadow:
        0 15px 40px rgba(102, 126, 234, 0.5),
        inset 0 2px 0 rgba(255, 255, 255, 0.3);
}

.newsletter-benefits {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.85rem;
    font-weight: 500;
}

.benefit-item i {
    color: #10b981;
    font-size: 0.9rem;
    filter: drop-shadow(0 2px 4px rgba(16, 185, 129, 0.3));
}

/* Social Media & Trust Indicators */
.footer-social-trust {
    padding: 3rem 0;
    border-top: 2px solid rgba(255, 255, 255, 0.1);
    margin-top: 2rem;
}

.social-media-section {
    margin-bottom: 2rem;
}

.social-title {
    font-size: 1.2rem;
    font-weight: 800;
    color: white;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #e0e0e0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.social-icons-premium {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.social-icon-premium {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    transition: all 0.5s ease;
    padding: 1rem;
    border-radius: 16px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    min-width: 80px;
}

.social-icon-premium:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.social-icon-premium .icon-bg {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    box-shadow:
        0 8px 20px rgba(102, 126, 234, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.social-icon-premium:hover .icon-bg {
    transform: scale(1.1) rotate(5deg);
    box-shadow:
        0 12px 30px rgba(102, 126, 234, 0.4),
        inset 0 2px 0 rgba(255, 255, 255, 0.3);
}

.social-icon-premium .icon-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.3) 50%,
        transparent 100%);
    transition: left 0.6s ease;
}

.social-icon-premium:hover .icon-bg::before {
    left: 100%;
}

.social-icon-premium i {
    color: white;
    font-size: 1.3rem;
    position: relative;
    z-index: 1;
}

.social-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.social-icon-premium:hover .social-label {
    color: white;
    transform: translateY(-2px);
}

/* Trust Indicators */
.trust-indicators {
    margin-bottom: 2rem;
}

.trust-title {
    font-size: 1.2rem;
    font-weight: 800;
    color: white;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #e0e0e0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.trust-badges {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.trust-badge {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 1rem 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.trust-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(102, 126, 234, 0.1) 50%,
        transparent 100%);
    transition: left 0.6s ease;
}

.trust-badge:hover::before {
    left: 100%;
}

.trust-badge:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.2);
}

.trust-badge i {
    color: #10b981;
    font-size: 1.1rem;
    filter: drop-shadow(0 2px 8px rgba(16, 185, 129, 0.3));
    position: relative;
    z-index: 1;
}

.trust-badge span {
    color: rgba(255, 255, 255, 0.8);
    font-weight: 600;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    z-index: 1;
}

/* Premium Footer Bottom */
.footer-bottom-premium {
    background: linear-gradient(135deg,
        rgba(0, 0, 0, 0.8) 0%,
        rgba(26, 26, 46, 0.7) 50%,
        rgba(0, 0, 0, 0.8) 100%);
    border-top: 2px solid rgba(255, 255, 255, 0.1);
    padding: 2rem 0;
    position: relative;
    backdrop-filter: blur(20px);
}

.footer-bottom-premium::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        rgba(102, 126, 234, 0.05) 0%,
        rgba(255, 255, 255, 0.02) 50%,
        rgba(118, 75, 162, 0.05) 100%);
    z-index: 0;
}

.footer-bottom-content-premium {
    position: relative;
    z-index: 1;
}

.copyright-section {
    margin-bottom: 1rem;
}

.copyright-text {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 600;
    font-size: 0.95rem;
    margin-bottom: 0.5rem;
}

.company-info {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.85rem;
    font-weight: 500;
    margin: 0;
    font-style: italic;
}

.legal-links {
    display: flex;
    gap: 2rem;
    justify-content: flex-end;
    flex-wrap: wrap;
    align-items: center;
}

.legal-link {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    position: relative;
    padding: 0.5rem 0;
}

.legal-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    transition: width 0.3s ease;
}

.legal-link:hover {
    color: white;
    text-shadow: 0 2px 10px rgba(255, 255, 255, 0.3);
}

.legal-link:hover::before {
    width: 100%;
}

/* Responsive Design */
@media (max-width: 991px) {
    .footer-main-content {
        padding: 4rem 0 3rem;
    }

    .footer-brand-showcase {
        margin-bottom: 3rem;
        text-align: center;
    }

    .footer-premium-logo {
        justify-content: center;
        margin-bottom: 1.5rem;
    }

    .footer-stats {
        justify-content: center;
        gap: 2rem;
    }

    .cta-card {
        padding: 2rem 1.5rem;
    }

    .footer-navigation-grid {
        margin: 3rem 0;
        padding: 2rem 0;
    }

    .footer-contact-newsletter {
        margin: 3rem 0;
        padding: 2rem 0;
    }

    .contact-info-premium,
    .newsletter-premium {
        margin-bottom: 2rem;
    }

    .newsletter-card {
        padding: 2rem;
    }

    .input-group-premium {
        flex-direction: column;
        gap: 1rem;
    }

    .social-icons-premium {
        justify-content: center;
    }

    .trust-badges {
        justify-content: center;
    }

    .legal-links {
        justify-content: center;
        gap: 1.5rem;
    }
}

@media (max-width: 767px) {
    .footer-main-content {
        padding: 3rem 0 2rem;
    }

    .logo-main {
        font-size: 1.8rem;
    }

    .footer-stats {
        gap: 1.5rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .cta-buttons {
        flex-direction: column;
        gap: 1rem;
    }

    .btn-cta {
        width: 100%;
        justify-content: center;
    }

    .newsletter-header {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .social-icons-premium {
        gap: 1rem;
    }

    .trust-badges {
        flex-direction: column;
        gap: 0.8rem;
    }

    .legal-links {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
}
