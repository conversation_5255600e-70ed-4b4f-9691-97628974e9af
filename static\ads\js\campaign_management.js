/**
 * Campaign Management JavaScript
 * Handles campaign metrics and charts with real data
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize campaign performance chart
    initializeCampaignPerformanceChart();
    
    // Set up tab navigation
    setupTabNavigation();
});

/**
 * Initialize the campaign performance chart with real data
 */
function initializeCampaignPerformanceChart() {
    const chartElement = document.getElementById('campaignPerformanceChart');
    if (!chartElement) return;
    
    // Get campaign data from the page
    const campaignId = document.querySelector('[data-campaign-id]')?.dataset.campaignId;
    if (!campaignId) {
        console.warn('Campaign ID not found, using default data');
        initializeWithDefaultData(chartElement);
        return;
    }
    
    // Fetch real campaign analytics data
    fetchCampaignAnalytics(campaignId)
        .then(data => {
            if (!data || Object.keys(data).length === 0) {
                console.warn('No campaign analytics data available, using default data');
                initializeWithDefaultData(chartElement);
                return;
            }
            
            createCampaignChart(chartElement, data);
        })
        .catch(error => {
            console.error('Error fetching campaign analytics:', error);
            initializeWithDefaultData(chartElement);
        });
}

/**
 * Fetch campaign analytics data from the server
 * @param {string} campaignId - The ID of the campaign
 * @returns {Promise} - Promise resolving to campaign analytics data
 */
function fetchCampaignAnalytics(campaignId) {
    // This would be replaced with an actual API call in production
    // For now, we'll simulate a fetch with a promise that resolves with data
    return new Promise((resolve) => {
        // Check if we have data in the page
        const dataElement = document.getElementById('campaign-analytics-data');
        if (dataElement) {
            try {
                const data = JSON.parse(dataElement.textContent);
                resolve(data);
            } catch (e) {
                console.error('Error parsing campaign analytics data:', e);
                resolve({});
            }
        } else {
            // No data element found
            resolve({});
        }
    });
}

/**
 * Initialize chart with default data when real data is not available
 * @param {HTMLElement} chartElement - The canvas element for the chart
 */
function initializeWithDefaultData(chartElement) {
    // Get the last 7 days for labels
    const labels = [];
    for (let i = 6; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        labels.push(date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }));
    }
    
    // Create default datasets with zeros
    const impressions = new Array(7).fill(0);
    const clicks = new Array(7).fill(0);
    
    createCampaignChart(chartElement, {
        labels: labels,
        impressions: impressions,
        clicks: clicks
    });
}

/**
 * Create the campaign performance chart
 * @param {HTMLElement} chartElement - The canvas element for the chart
 * @param {Object} data - The chart data
 */
function createCampaignChart(chartElement, data) {
    const ctx = chartElement.getContext('2d');
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: data.labels,
            datasets: [
                {
                    label: 'Impressions',
                    data: data.impressions,
                    borderColor: '#2196f3',
                    backgroundColor: 'rgba(33, 150, 243, 0.1)',
                    borderWidth: 2,
                    tension: 0.4,
                    fill: true
                },
                {
                    label: 'Clicks',
                    data: data.clicks,
                    borderColor: '#9c27b0',
                    backgroundColor: 'rgba(156, 39, 176, 0.1)',
                    borderWidth: 2,
                    tension: 0.4,
                    fill: true
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        // Format tooltip values
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            label += context.parsed.y;
                            return label;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0 // Only show whole numbers
                    }
                }
            }
        }
    });
}

/**
 * Set up tab navigation
 */
function setupTabNavigation() {
    const tabLinks = document.querySelectorAll('.sidebar-nav .nav-link');
    const tabContents = document.querySelectorAll('.tab-pane');
    
    tabLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active class from all links and contents
            tabLinks.forEach(l => l.classList.remove('active'));
            tabContents.forEach(c => {
                c.classList.remove('show', 'active');
            });
            
            // Add active class to clicked link
            this.classList.add('active');
            
            // Get the target tab
            const target = this.getAttribute('href');
            const targetContent = document.querySelector(target);
            
            // Show the target content
            if (targetContent) {
                targetContent.classList.add('show', 'active');
            }
        });
    });
}

/**
 * Calculate CTR (Click-Through Rate)
 * @param {number} clicks - Number of clicks
 * @param {number} impressions - Number of impressions
 * @returns {number} - CTR percentage
 */
function calculateCTR(clicks, impressions) {
    if (!impressions || impressions === 0) return 0;
    return (clicks / impressions) * 100;
}

/**
 * Format number with commas for thousands
 * @param {number} number - Number to format
 * @returns {string} - Formatted number
 */
function formatNumber(number) {
    return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}
