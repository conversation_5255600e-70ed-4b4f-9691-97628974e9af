# Django Settings
DEBUG=false
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=localhost,127.0.0.1,yourdomain.com
DATABASE_URL=sqlite:///db.sqlite3

# AI Provider Settings
# Choose one of: 'mistral', 'openai', or 'groq'
# Note: 'local' option is disabled
AI_PROVIDER=mistral

# Local AI Engine Settings (DISABLED)
# Uncomment these settings only if you re-enable the local AI Engine
# ENABLE_AI_ENGINE=false
# AI_ENGINE_URL=http://ai_engine:8001
# AI_ENGINE_API_TOKEN=ai-engine-token-1
# AI_ENGINE_ENABLE_AUTH=true
# AI_ENGINE_API_TOKENS=ai-engine-token-1,ai-engine-token-2,custom-token-3

# AI Models (DISABLED)
# These settings are only used if you re-enable the local AI Engine
# AI_MODEL_ONE=mistralai/Mistral-7B-Instruct-v0.2
# AI_MODEL_TWO=mistralai/Mistral-7B-Instruct-v0.2

# Mistral AI Settings
MISTRAL_API_KEY=your_mistral_api_key_here
MISTRAL_MODEL=mistral-tiny

# OpenAI Settings
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4-turbo

# Groq AI Settings (High-speed inference)
GROQ_API_KEY=your_groq_api_key_here
GROQ_MODEL=llama3-8b-8192
GROQ_TEMPERATURE=0.7
GROQ_MAX_TOKENS=1024

# Ad Generation Settings
AD_GENERATION_TEMPERATURE=0.7
AD_GENERATION_MAX_TOKENS=150
AD_GENERATION_NUM_SUGGESTIONS=3

# Logging
LOG_LEVEL=INFO
LOG_DIR=/app/logs

# Production Settings
DOMAIN_NAME=yourdomain.com
HTTPS_ENABLED=true
