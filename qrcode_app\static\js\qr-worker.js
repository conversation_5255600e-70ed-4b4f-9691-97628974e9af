/**
 * QR Code Generator Web Worker
 * 
 * This worker handles QR code generation in a separate thread to prevent
 * blocking the main UI thread during intensive operations.
 */

// Import QR code library
importScripts('https://cdn.jsdelivr.net/npm/qrcode-generator@1.4.4/qrcode.min.js');

// Handle messages from the main thread
self.onmessage = function(e) {
    const { id, qrData, options } = e.data;
    
    try {
        // Generate QR code
        const dataURL = generateQRCode(qrData, options);
        
        // Send result back to main thread
        self.postMessage({
            id,
            qrData,
            result: dataURL
        });
    } catch (error) {
        // Send error back to main thread
        self.postMessage({
            id,
            qrData,
            error: error.message || 'Error generating QR code'
        });
    }
};

/**
 * Generate a QR code
 * @param {string} data - QR code data
 * @param {Object} options - QR code options
 * @returns {string} - QR code data URL
 */
function generateQRCode(data, options = {}) {
    // Set default options
    const width = options.width || 256;
    const height = options.height || 256;
    const typeNumber = options.typeNumber || 4;
    const errorCorrectionLevel = options.errorCorrectionLevel || 'H';
    const darkColor = options.darkColor || '#000000';
    const lightColor = options.lightColor || '#FFFFFF';
    const margin = options.margin || 4;
    
    // Create QR code
    const qr = qrcode(typeNumber, errorCorrectionLevel);
    qr.addData(data);
    qr.make();
    
    // Create canvas
    const canvas = new OffscreenCanvas(width, height);
    const ctx = canvas.getContext('2d');
    
    // Calculate cell size
    const moduleCount = qr.getModuleCount();
    const cellSize = Math.floor((width - margin * 2) / moduleCount);
    const actualMargin = Math.floor((width - cellSize * moduleCount) / 2);
    
    // Fill background
    ctx.fillStyle = lightColor;
    ctx.fillRect(0, 0, width, height);
    
    // Draw QR code
    ctx.fillStyle = darkColor;
    
    for (let row = 0; row < moduleCount; row++) {
        for (let col = 0; col < moduleCount; col++) {
            if (qr.isDark(row, col)) {
                ctx.fillRect(
                    actualMargin + col * cellSize,
                    actualMargin + row * cellSize,
                    cellSize,
                    cellSize
                );
            }
        }
    }
    
    // Apply advanced styling if specified
    if (options.qrShape && options.qrShape !== 'standard') {
        applyQRShape(ctx, moduleCount, cellSize, actualMargin, options.qrShape);
    }
    
    if (options.eyeStyle && options.eyeStyle !== 'standard') {
        applyEyeStyle(ctx, moduleCount, cellSize, actualMargin, options.eyeStyle);
    }
    
    // Convert to data URL
    return canvas.convertToBlob()
        .then(blob => {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => resolve(reader.result);
                reader.onerror = reject;
                reader.readAsDataURL(blob);
            });
        });
}

/**
 * Apply QR code shape styling
 * @param {CanvasRenderingContext2D} ctx - Canvas context
 * @param {number} moduleCount - Number of modules
 * @param {number} cellSize - Cell size
 * @param {number} margin - Margin
 * @param {string} shape - Shape type
 */
function applyQRShape(ctx, moduleCount, cellSize, margin, shape) {
    // Save original image data
    const imageData = ctx.getImageData(0, 0, ctx.canvas.width, ctx.canvas.height);
    
    // Clear canvas
    ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
    
    // Draw background
    ctx.putImageData(imageData, 0, 0);
    
    // Apply shape to each cell
    for (let row = 0; row < moduleCount; row++) {
        for (let col = 0; col < moduleCount; col++) {
            const x = margin + col * cellSize;
            const y = margin + row * cellSize;
            
            // Skip finder patterns (eyes)
            if ((row < 7 && col < 7) || // Top-left
                (row < 7 && col >= moduleCount - 7) || // Top-right
                (row >= moduleCount - 7 && col < 7)) { // Bottom-left
                continue;
            }
            
            // Get pixel color
            const pixelData = ctx.getImageData(x + cellSize / 2, y + cellSize / 2, 1, 1).data;
            const isDark = pixelData[0] < 128; // Check if dark based on red channel
            
            if (isDark) {
                // Clear original square
                ctx.clearRect(x, y, cellSize, cellSize);
                
                // Draw shaped cell
                ctx.fillStyle = '#000000';
                
                switch (shape) {
                    case 'rounded':
                        // Rounded rectangle
                        ctx.beginPath();
                        ctx.roundRect(x, y, cellSize, cellSize, cellSize / 3);
                        ctx.fill();
                        break;
                        
                    case 'circular':
                        // Circle
                        ctx.beginPath();
                        ctx.arc(x + cellSize / 2, y + cellSize / 2, cellSize / 2, 0, Math.PI * 2);
                        ctx.fill();
                        break;
                        
                    case 'dotted':
                        // Smaller circle
                        ctx.beginPath();
                        ctx.arc(x + cellSize / 2, y + cellSize / 2, cellSize / 3, 0, Math.PI * 2);
                        ctx.fill();
                        break;
                        
                    case 'diamond':
                        // Diamond shape
                        ctx.beginPath();
                        ctx.moveTo(x + cellSize / 2, y);
                        ctx.lineTo(x + cellSize, y + cellSize / 2);
                        ctx.lineTo(x + cellSize / 2, y + cellSize);
                        ctx.lineTo(x, y + cellSize / 2);
                        ctx.closePath();
                        ctx.fill();
                        break;
                        
                    case 'hexagonal':
                        // Hexagon shape
                        ctx.beginPath();
                        ctx.moveTo(x + cellSize / 4, y);
                        ctx.lineTo(x + cellSize * 3 / 4, y);
                        ctx.lineTo(x + cellSize, y + cellSize / 2);
                        ctx.lineTo(x + cellSize * 3 / 4, y + cellSize);
                        ctx.lineTo(x + cellSize / 4, y + cellSize);
                        ctx.lineTo(x, y + cellSize / 2);
                        ctx.closePath();
                        ctx.fill();
                        break;
                }
            }
        }
    }
}

/**
 * Apply QR code eye styling
 * @param {CanvasRenderingContext2D} ctx - Canvas context
 * @param {number} moduleCount - Number of modules
 * @param {number} cellSize - Cell size
 * @param {number} margin - Margin
 * @param {string} style - Eye style
 */
function applyEyeStyle(ctx, moduleCount, cellSize, margin, style) {
    // Define eye positions
    const eyePositions = [
        { row: 0, col: 0 }, // Top-left
        { row: 0, col: moduleCount - 7 }, // Top-right
        { row: moduleCount - 7, col: 0 } // Bottom-left
    ];
    
    // Apply style to each eye
    eyePositions.forEach(pos => {
        const x = margin + pos.col * cellSize;
        const y = margin + pos.row * cellSize;
        const size = cellSize * 7;
        
        // Clear original eye
        ctx.clearRect(x, y, size, size);
        
        // Draw styled eye
        ctx.fillStyle = '#000000';
        
        switch (style) {
            case 'rounded':
                // Outer square with rounded corners
                ctx.beginPath();
                ctx.roundRect(x, y, size, size, size / 4);
                ctx.fill();
                
                // Inner white square with rounded corners
                ctx.fillStyle = '#FFFFFF';
                ctx.beginPath();
                ctx.roundRect(x + cellSize, y + cellSize, size - 2 * cellSize, size - 2 * cellSize, (size - 2 * cellSize) / 4);
                ctx.fill();
                
                // Center black square with rounded corners
                ctx.fillStyle = '#000000';
                ctx.beginPath();
                ctx.roundRect(x + 2 * cellSize, y + 2 * cellSize, size - 4 * cellSize, size - 4 * cellSize, (size - 4 * cellSize) / 4);
                ctx.fill();
                break;
                
            case 'circular':
                // Outer circle
                ctx.beginPath();
                ctx.arc(x + size / 2, y + size / 2, size / 2, 0, Math.PI * 2);
                ctx.fill();
                
                // Inner white circle
                ctx.fillStyle = '#FFFFFF';
                ctx.beginPath();
                ctx.arc(x + size / 2, y + size / 2, size / 2 - cellSize, 0, Math.PI * 2);
                ctx.fill();
                
                // Center black circle
                ctx.fillStyle = '#000000';
                ctx.beginPath();
                ctx.arc(x + size / 2, y + size / 2, size / 2 - 2 * cellSize, 0, Math.PI * 2);
                ctx.fill();
                break;
                
            case 'leafy':
                // Leaf-shaped eye
                ctx.beginPath();
                ctx.moveTo(x, y + size / 2);
                ctx.quadraticCurveTo(x, y, x + size / 2, y);
                ctx.quadraticCurveTo(x + size, y, x + size, y + size / 2);
                ctx.quadraticCurveTo(x + size, y + size, x + size / 2, y + size);
                ctx.quadraticCurveTo(x, y + size, x, y + size / 2);
                ctx.fill();
                
                // Inner white leaf
                ctx.fillStyle = '#FFFFFF';
                ctx.beginPath();
                ctx.moveTo(x + cellSize, y + size / 2);
                ctx.quadraticCurveTo(x + cellSize, y + cellSize, x + size / 2, y + cellSize);
                ctx.quadraticCurveTo(x + size - cellSize, y + cellSize, x + size - cellSize, y + size / 2);
                ctx.quadraticCurveTo(x + size - cellSize, y + size - cellSize, x + size / 2, y + size - cellSize);
                ctx.quadraticCurveTo(x + cellSize, y + size - cellSize, x + cellSize, y + size / 2);
                ctx.fill();
                
                // Center black dot
                ctx.fillStyle = '#000000';
                ctx.beginPath();
                ctx.arc(x + size / 2, y + size / 2, size / 5, 0, Math.PI * 2);
                ctx.fill();
                break;
                
            case 'diamond':
                // Outer diamond
                ctx.beginPath();
                ctx.moveTo(x + size / 2, y);
                ctx.lineTo(x + size, y + size / 2);
                ctx.lineTo(x + size / 2, y + size);
                ctx.lineTo(x, y + size / 2);
                ctx.closePath();
                ctx.fill();
                
                // Inner white diamond
                ctx.fillStyle = '#FFFFFF';
                ctx.beginPath();
                ctx.moveTo(x + size / 2, y + cellSize);
                ctx.lineTo(x + size - cellSize, y + size / 2);
                ctx.lineTo(x + size / 2, y + size - cellSize);
                ctx.lineTo(x + cellSize, y + size / 2);
                ctx.closePath();
                ctx.fill();
                
                // Center black diamond
                ctx.fillStyle = '#000000';
                ctx.beginPath();
                ctx.moveTo(x + size / 2, y + 2 * cellSize);
                ctx.lineTo(x + size - 2 * cellSize, y + size / 2);
                ctx.lineTo(x + size / 2, y + size - 2 * cellSize);
                ctx.lineTo(x + 2 * cellSize, y + size / 2);
                ctx.closePath();
                ctx.fill();
                break;
        }
    });
}
