#!/usr/bin/env python
"""
Script to restore sample data for testing
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'qrcode_project.settings')
django.setup()

from django.contrib.auth.models import User
from qrcode_app.models import QRCode, UserProfile, Plan, Subscription
from ads.models import Ad
from django.utils import timezone
import qrcode
from io import BytesIO
import base64

def create_sample_data():
    print("🔄 Restoring sample data...")
    
    # Check current data
    print(f"Current QR codes: {QRCode.objects.count()}")
    print(f"Current ads: {Ad.objects.count()}")
    print(f"Current users: {User.objects.count()}")
    
    # Ensure test users exist
    print("\n👥 Creating test users...")
    
    # Create apollo user
    apollo, created = User.objects.get_or_create(
        username='apollo',
        defaults={
            'email': '<EMAIL>',
            'is_active': True
        }
    )
    if created:
        apollo.set_password('2587')
        apollo.save()
        print("✅ Created apollo user")
    else:
        print("✅ Apollo user exists")
    
    # Create peter superuser
    peter, created = User.objects.get_or_create(
        username='peter',
        defaults={
            'email': '<EMAIL>',
            'is_superuser': True,
            'is_staff': True,
            'is_active': True
        }
    )
    if created:
        peter.set_password('2587')
        peter.save()
        print("✅ Created peter superuser")
    else:
        peter.set_password('2587')
        peter.is_superuser = True
        peter.is_staff = True
        peter.save()
        print("✅ Updated peter as superuser")
    
    # Create profiles
    apollo_profile, created = UserProfile.objects.get_or_create(
        user=apollo,
        defaults={'role': 'user'}
    )
    peter_profile, created = UserProfile.objects.get_or_create(
        user=peter,
        defaults={'role': 'admin'}
    )
    
    # Create subscriptions
    try:
        free_plan = Plan.objects.get(plan_type='FREE')
        subscription, created = Subscription.objects.get_or_create(
            user=apollo,
            defaults={
                'plan': free_plan,
                'status': 'ACTIVE',
                'started_at': timezone.now(),
                'scans_this_month': 0
            }
        )
        print("✅ Apollo subscription created")
    except Plan.DoesNotExist:
        print("⚠️ FREE plan not found. Run: python manage.py create_default_plans")
    
    # Create sample QR codes for apollo
    print("\n📱 Creating sample QR codes...")
    
    sample_qrs = [
        {
            'name': 'My Business Card',
            'qr_type': 'url',
            'data': 'https://mycompany.com/contact',
            'description': 'Professional business card QR code'
        },
        {
            'name': 'Restaurant Menu',
            'qr_type': 'url', 
            'data': 'https://restaurant.com/menu',
            'description': 'Digital menu for customers'
        },
        {
            'name': 'WiFi Access',
            'qr_type': 'wifi',
            'data': 'WIFI:T:WPA;S:MyNetwork;P:password123;;',
            'description': 'Guest WiFi access'
        }
    ]
    
    for qr_data in sample_qrs:
        qr_code, created = QRCode.objects.get_or_create(
            user=apollo,
            name=qr_data['name'],
            defaults={
                'qr_type': qr_data['qr_type'],
                'data': qr_data['data'],
                'description': qr_data['description'],
                'is_active': True
            }
        )
        if created:
            print(f"✅ Created QR code: {qr_data['name']}")
        else:
            print(f"✅ QR code exists: {qr_data['name']}")
    
    # Create sample QR codes for peter (admin)
    admin_qrs = [
        {
            'name': 'Admin Dashboard',
            'qr_type': 'url',
            'data': 'https://admin.mysite.com/dashboard',
            'description': 'Quick access to admin panel'
        },
        {
            'name': 'Support Portal',
            'qr_type': 'url',
            'data': 'https://support.mycompany.com',
            'description': 'Customer support portal'
        }
    ]
    
    for qr_data in admin_qrs:
        qr_code, created = QRCode.objects.get_or_create(
            user=peter,
            name=qr_data['name'],
            defaults={
                'qr_type': qr_data['qr_type'],
                'data': qr_data['data'],
                'description': qr_data['description'],
                'is_active': True
            }
        )
        if created:
            print(f"✅ Created admin QR code: {qr_data['name']}")
        else:
            print(f"✅ Admin QR code exists: {qr_data['name']}")
    
    # Create sample ads
    print("\n📢 Creating sample ads...")
    
    sample_ads = [
        {
            'title': 'Premium QR Features',
            'content': 'Upgrade to QR Pro for unlimited codes, analytics, and branding!',
            'location': 'header',
            'ad_type': 'premium',
            'is_active': True
        },
        {
            'title': 'AI Landing Pages',
            'content': 'Create professional landing pages with AI. No coding required!',
            'location': 'sidebar',
            'ad_type': 'feature',
            'is_active': True
        },
        {
            'title': 'Webhook Integration',
            'content': 'Connect your QR scans to Zapier, CRM systems, and more!',
            'location': 'footer',
            'ad_type': 'integration',
            'is_active': True
        }
    ]
    
    for ad_data in sample_ads:
        ad, created = Ad.objects.get_or_create(
            title=ad_data['title'],
            defaults={
                'content': ad_data['content'],
                'location': ad_data['location'],
                'ad_type': ad_data['ad_type'],
                'is_active': ad_data['is_active'],
                'created_by': peter
            }
        )
        if created:
            print(f"✅ Created ad: {ad_data['title']}")
        else:
            print(f"✅ Ad exists: {ad_data['title']}")
    
    # Final summary
    print("\n📊 Data Summary:")
    print(f"Total QR codes: {QRCode.objects.count()}")
    print(f"Total ads: {Ad.objects.count()}")
    print(f"Total users: {User.objects.count()}")
    
    print("\n🎉 Sample data restored successfully!")
    print("\nTest Accounts:")
    print("Username: apollo | Password: 2587 | Role: user | Plan: FREE")
    print("Username: peter  | Password: 2587 | Role: admin | Superuser: Yes")
    
    print("\nNext Steps:")
    print("1. Visit: http://127.0.0.1:8000/user-switcher/")
    print("2. Login as apollo to see sample QR codes")
    print("3. Test plan limits and features")
    print("4. Switch to peter for admin access")

if __name__ == '__main__':
    create_sample_data()
