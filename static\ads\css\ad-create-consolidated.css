/**
 * Ad Create Consolidated CSS
 * Consolidated styles for the ad creation page
 * Replaces multiple separate CSS files to avoid conflicts
 *
 * Consolidates:
 * - ad-create-form-fixes.css
 * - ad-create-enterprise-styles.css
 * - ad-create-alignment-fixes.css
 *
 * Version: 2.0.0
 */

/* ==========================================================================
   DASHBOARD LAYOUT
   ========================================================================== */

.enterprise-dashboard {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.dashboard-content {
    flex: 1;
    padding: 20px 0;
}

.dashboard-sidebar {
    position: sticky;
    top: 80px;
    height: fit-content;
    max-height: calc(100vh - 100px);
    overflow-y: auto;
}

/* Right column sticky positioning for preview and pro tips */
@media (min-width: 992px) {
    .col-lg-4 {
        position: sticky;
        top: 20px;
        height: fit-content;
        max-height: calc(100vh - 40px);
        overflow-y: auto;
        align-self: flex-start;
    }

    /* Ensure pro tips appear immediately under preview */
    .col-lg-4 .dashboard-card + .dashboard-card {
        margin-top: 1rem;
    }
}

.dashboard-main {
    min-height: 600px;
}

/* ==========================================================================
   DASHBOARD CARDS
   ========================================================================== */

.dashboard-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    overflow: visible;
    position: relative;
}

.dashboard-card .card-header {
    background-color: #f8f9fa !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 15px 20px;
    background-image: none !important;
    color: #333 !important;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.dashboard-card .card-header .card-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333 !important;
    background-image: none !important;
    background: none !important;
    -webkit-background-clip: initial !important;
    -webkit-text-fill-color: #333 !important;
    text-fill-color: #333 !important;
    flex: 1;
}

.dashboard-card .card-body {
    padding: 20px;
    position: relative;
}

/* ==========================================================================
   PROGRESS BAR
   ========================================================================== */

.card-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.progress {
    min-width: 200px;
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background-color: #007bff;
    transition: width 0.3s ease;
    width: 25%;
}

/* ==========================================================================
   FORM ELEMENTS
   ========================================================================== */

.form-label {
    font-weight: 500;
    margin-bottom: 8px;
    color: #333;
    display: block;
    width: 100%;
}

.form-control, .form-select {
    border-radius: 4px;
    border: 1px solid #ced4da;
    padding: 10px 12px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    width: 100%;
    box-sizing: border-box;
    min-height: 38px;
}

.form-control:focus, .form-select:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    outline: none;
}

.form-control.is-invalid, .form-select.is-invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    padding-right: calc(1.5em + 0.75rem);
}

.form-control.is-valid, .form-select.is-valid {
    border-color: #28a745;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    padding-right: calc(1.5em + 0.75rem);
}

/* Form groups */
.mb-3, .mb-4 {
    margin-bottom: 1rem !important;
    width: 100%;
}

/* Row and column alignment */
.row {
    margin-left: 0;
    margin-right: 0;
}

.row > * {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
}

/* ==========================================================================
   BUTTONS
   ========================================================================== */

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
    color: #fff;
    font-weight: 500;
    padding: 8px 16px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.btn-primary:hover {
    background-color: #0069d9;
    border-color: #0062cc;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
    color: #fff;
    font-weight: 500;
    padding: 8px 16px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.btn-secondary:hover {
    background-color: #5a6268;
    border-color: #545b62;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn:focus {
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.d-flex.justify-content-between .btn,
.d-flex.justify-content-end .btn {
    min-width: 120px;
    margin: 0 5px;
}

/* ==========================================================================
   TAB NAVIGATION
   ========================================================================== */

.nav-tabs {
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
    width: 100%;
}

.nav-tabs .nav-item {
    flex: 1;
    min-width: 0;
}

.nav-tabs .nav-link {
    border: 1px solid #dee2e6;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    color: #495057 !important;
    background-color: #f8f9fa !important;
    font-weight: 500;
    padding: 10px 15px;
    transition: all 0.2s ease;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    display: block;
}

.nav-tabs .nav-link:hover {
    border-color: #e9ecef #e9ecef #dee2e6;
    color: #007bff !important;
    background-color: #fff !important;
    transform: none;
}

.nav-tabs .nav-link.active {
    color: #007bff !important;
    background-color: #fff !important;
    border-color: #dee2e6 #dee2e6 #fff;
    position: relative;
    z-index: 1;
}

.nav-link:focus {
    box-shadow: none;
}

/* Tab content */
.tab-content {
    overflow: visible;
    position: relative;
}

.tab-pane {
    position: relative;
}

/* ==========================================================================
   AI SUGGESTIONS
   ========================================================================== */

#aiSuggestionsContainer {
    margin-top: 15px;
    padding: 15px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background-color: #f8f9fa;
    display: none;
}

.ai-suggestion-card {
    position: relative;
    margin-bottom: 15px;
    padding: 15px;
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.ai-suggestion-card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-color: #007bff;
    transform: translateY(-2px);
}

.ai-suggestion-title {
    font-weight: 600;
    color: #333;
}

.ai-suggestion-content {
    color: #6c757d;
}

.apply-suggestion-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 4px 8px;
    font-size: 12px;
    z-index: 10;
}

.ai-suggestion-card .form-check {
    padding-right: 80px;
}

/* ==========================================================================
   CREATIVITY OPTIONS
   ========================================================================== */

.creativity-options {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    justify-content: space-between;
}

.creativity-options .form-check {
    flex: 1;
    min-width: 120px;
    margin: 0;
}

.creativity-options .form-check-label {
    display: block;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid #dee2e6;
    border-radius: 6px;
    padding: 8px 12px;
    margin: 0;
}

.creativity-options .form-check-input:checked + .form-check-label {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

/* ==========================================================================
   PREVIEW PANEL
   ========================================================================== */

.ad-preview-container {
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 15px;
    background-color: #f8f9fa;
}

.ad-preview-location-label {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #6c757d;
}

.ad-preview-wrapper {
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    min-height: 200px;
    display: flex;
    flex-direction: column;
}

.ad-preview-title {
    font-weight: 600;
    margin-bottom: 5px;
    color: #1a237e;
    padding: 12px 15px 5px;
    font-size: 16px;
    border-bottom: 1px solid #f5f5f5;
    background-color: #f8f9fa;
}

.ad-preview-content {
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
    padding: 15px;
    line-height: 1.5;
    flex: 1;
}

.ad-preview-image {
    position: relative;
    overflow: hidden;
}

.ad-preview-image img {
    width: 100%;
    height: auto;
    display: block;
    transition: transform 0.3s ease;
}

.ad-preview-wrapper:hover .ad-preview-image img {
    transform: scale(1.03);
}

.ad-preview-cta {
    padding: 15px;
    text-align: center;
    border-top: 1px solid #f0f0f0;
    background-color: #f8f9fa;
}

.ad-preview-cta .btn {
    padding: 8px 20px;
    font-weight: 500;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.ad-preview-cta .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.ad-preview-size-info {
    margin-top: 10px;
}

/* Location-specific styles */
.ad-preview-header, .ad-preview-footer {
    width: 100%;
    height: 90px;
}

.ad-preview-sidebar {
    width: 300px;
    height: 250px;
}

.ad-preview-content {
    width: 468px;
    height: 60px;
}

/* ==========================================================================
   PRICING SUMMARY
   ========================================================================== */

.pricing-summary {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 15px;
}

.pricing-summary .row {
    margin-bottom: 10px;
}

.pricing-summary .total-price {
    font-weight: 700;
    color: #007bff;
    font-size: 18px;
}

.pricing-summary .d-flex {
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
    padding: 5px 0;
}

.pricing-summary hr {
    margin: 15px 0;
    border-color: #dee2e6;
}

/* ==========================================================================
   FORM VALIDATION
   ========================================================================== */

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: #dc3545;
}

.is-invalid ~ .invalid-feedback {
    display: block;
}

/* ==========================================================================
   ALERTS
   ========================================================================== */

.alert {
    position: relative;
    margin-bottom: 1rem;
    border-radius: 6px;
}

.alert-dismissible .btn-close {
    position: absolute;
    top: 50%;
    right: 1rem;
    transform: translateY(-50%);
    padding: 0;
    background: none;
    border: none;
    font-size: 1.25rem;
    opacity: 0.5;
}

/* ==========================================================================
   Z-INDEX FIXES
   ========================================================================== */

.select2-container {
    z-index: 1050;
}

.select2-dropdown {
    z-index: 1051;
}

.select2-container--open .select2-dropdown {
    z-index: 1056 !important;
}

/* ==========================================================================
   FORM SWITCHES
   ========================================================================== */

.form-switch {
    padding-left: 2.5em;
}

.form-switch .form-check-input {
    width: 2em;
    margin-left: -2.5em;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%28255,255,255,1%29'/%3e%3c/svg%3e");
    background-position: left center;
    border-radius: 2em;
    transition: background-position .15s ease-in-out;
    margin-top: 0.15rem;
}

.form-switch .form-check-input:checked {
    background-position: right center;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%28255,255,255,1%29'/%3e%3c/svg%3e");
}

/* Form check alignment */
.form-check-input {
    margin-top: 0.25rem;
}

.form-check-input[type="radio"] {
    margin-top: 0.3rem;
}

/* Form text spacing */
.form-text {
    display: block;
    margin-top: 0.25rem;
}

/* File input */
.form-control[type="file"] {
    overflow: hidden;
}

/* Date and time inputs */
input[type="date"],
input[type="time"] {
    min-height: 38px;
}

/* ==========================================================================
   FLOATING ELEMENTS
   ========================================================================== */

.float-end {
    float: right !important;
}

.clearfix::after {
    content: "";
    display: table;
    clear: both;
}

/* ==========================================================================
   PRO TIPS SECTION
   ========================================================================== */

#proTipsHeader {
    background-color: #fff3cd !important;
    color: #856404 !important;
    border-bottom: 1px solid #ffeaa7;
}

/* Ensure pro tips panel is positioned immediately under preview */
.col-lg-4 .dashboard-card:nth-child(2) {
    margin-top: 1rem !important;
}

#proTipsHeader .card-title {
    color: #856404 !important;
    font-weight: 600;
}

#proTipsHeader .btn-link {
    color: #856404 !important;
    text-decoration: none;
    padding: 0;
    border: none;
    background: none;
}

#proTipsHeader .btn-link:hover {
    color: #6c5ce7 !important;
}

#proTipsHeader .btn-link:focus {
    box-shadow: none;
}

#proTipsContent {
    border-top: none;
}

#proTipsContent .card-body {
    padding: 15px 20px;
}

#proTipsContent .list-group-item {
    padding: 8px 0;
    border: none;
    background: none;
    font-size: 14px;
    line-height: 1.5;
}

#proTipsContent .list-group-item:first-child {
    padding-top: 0;
}

#proTipsContent .list-group-item:last-child {
    padding-bottom: 0;
}

#proTipsContent .list-group-item i {
    width: 16px;
    text-align: center;
}

/* Pro tips collapse animation */
#proTipsContent.collapsing {
    transition: height 0.35s ease;
}

/* Pro tips responsive behavior */
@media (max-width: 991.98px) {
    #proTipsHeader {
        cursor: pointer;
    }

    #proTipsContent.show {
        display: block;
    }

    #proTipsContent:not(.show) {
        display: none;
    }
}

@media (max-width: 767.98px) {
    #proTipsContent {
        display: none !important;
    }

    #proTipsContent.show {
        display: block !important;
    }

    #proTipsHeader {
        background-color: #f8f9fa !important;
        color: #6c757d !important;
        cursor: pointer;
    }

    #proTipsHeader .card-title {
        color: #6c757d !important;
    }

    #proTipsHeader .btn-link {
        color: #6c757d !important;
    }

    /* Change chevron to down arrow by default on mobile */
    #proTipsHeader .fa-chevron-up {
        transform: rotate(180deg);
    }
}

/* ==========================================================================
   REVIEW SECTION
   ========================================================================== */

#reviewTitle,
#reviewType,
#reviewDuration,
#reviewLocation,
#reviewContent,
#reviewImpressions,
#reviewPrice {
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* ==========================================================================
   MOBILE RESPONSIVENESS
   ========================================================================== */

@media (max-width: 991.98px) {
    .dashboard-sidebar {
        position: static;
        margin-bottom: 20px;
    }

    .dashboard-main {
        margin-top: 0;
    }

    .card-actions {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .progress {
        width: 100%;
        min-width: auto;
    }
}

@media (max-width: 767.98px) {
    .creativity-options {
        flex-direction: column;
    }

    .creativity-options .form-check {
        flex: none;
        min-width: auto;
        margin-bottom: 10px;
    }

    .d-flex.justify-content-between {
        flex-direction: column;
        gap: 10px;
    }

    .d-flex.justify-content-between .btn,
    .d-flex.justify-content-end .btn {
        width: 100%;
        margin: 5px 0;
    }

    .ad-preview-container {
        margin-top: 20px;
    }
}

@media (max-width: 576px) {
    .nav-tabs {
        flex-wrap: nowrap;
        overflow-x: auto;
        overflow-y: hidden;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .nav-tabs::-webkit-scrollbar {
        display: none;
    }

    .nav-tabs .nav-item {
        flex: none;
        min-width: 120px;
    }

    .nav-tabs .nav-link {
        white-space: nowrap;
        padding: 8px 12px;
        font-size: 14px;
    }

    .dashboard-card .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .dashboard-card .card-body {
        padding: 15px;
    }

    .form-control, .form-select {
        padding: 8px 10px;
        font-size: 16px; /* Prevent zoom on iOS */
    }

    .btn {
        width: 100%;
        margin-bottom: 10px;
    }

    .btn + .btn {
        margin-left: 0;
    }
}

/* ==========================================================================
   CUSTOM DURATION FIELDS
   ========================================================================== */

#customDurationFields {
    display: none;
}

#smartEngineOptions {
    display: none;
}

#aiSuggestionsContainer {
    display: none;
}

#aiContentNote {
    display: none;
}
