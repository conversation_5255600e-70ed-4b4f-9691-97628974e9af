{% extends "base.html" %}
{% load static %}

{% block title %}Role Management - Enterprise QR{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'qrcode_app/css/role-management.css' %}">
{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="h3">
                <i class="fas fa-user-tag me-2"></i>Role Management
            </h1>
        </div>
        <div class="col-md-4 text-md-end">
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addRoleModal">
                <i class="fas fa-plus me-2"></i>Add Custom Role
            </button>
        </div>
    </div>
    
    <div class="row">
        <div class="col-lg-4">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white">
                    <h2 class="h5 mb-0">System Roles</h2>
                </div>
                <div class="card-body">
                    <div class="list-group">
                        <a href="#" class="list-group-item list-group-item-action active" data-role="guest">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h5 class="mb-1">Guest</h5>
                                    <p class="mb-1 small">Limited access to basic features</p>
                                </div>
                                <span class="badge bg-secondary rounded-pill">5 users</span>
                            </div>
                        </a>
                        <a href="#" class="list-group-item list-group-item-action" data-role="user">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h5 class="mb-1">User</h5>
                                    <p class="mb-1 small">Standard access to all basic features</p>
                                </div>
                                <span class="badge bg-secondary rounded-pill">12 users</span>
                            </div>
                        </a>
                        <a href="#" class="list-group-item list-group-item-action" data-role="premium">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h5 class="mb-1">Premium User</h5>
                                    <p class="mb-1 small">Access to premium features</p>
                                </div>
                                <span class="badge bg-secondary rounded-pill">3 users</span>
                            </div>
                        </a>
                        <a href="#" class="list-group-item list-group-item-action" data-role="admin">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h5 class="mb-1">Administrator</h5>
                                    <p class="mb-1 small">Administrative access to most features</p>
                                </div>
                                <span class="badge bg-secondary rounded-pill">2 users</span>
                            </div>
                        </a>
                        <a href="#" class="list-group-item list-group-item-action" data-role="superadmin">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h5 class="mb-1">Super Administrator</h5>
                                    <p class="mb-1 small">Full access to all features</p>
                                </div>
                                <span class="badge bg-secondary rounded-pill">1 user</span>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h2 class="h5 mb-0">Custom Roles</h2>
                </div>
                <div class="card-body">
                    <div class="list-group">
                        <a href="#" class="list-group-item list-group-item-action" data-role="manager">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h5 class="mb-1">Manager</h5>
                                    <p class="mb-1 small">Team management capabilities</p>
                                </div>
                                <span class="badge bg-secondary rounded-pill">2 users</span>
                            </div>
                        </a>
                        <a href="#" class="list-group-item list-group-item-action" data-role="analyst">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h5 class="mb-1">Analyst</h5>
                                    <p class="mb-1 small">Access to analytics features</p>
                                </div>
                                <span class="badge bg-secondary rounded-pill">1 user</span>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h2 class="h5 mb-0">Role Permissions: <span id="selectedRoleName">Guest</span></h2>
                </div>
                <div class="card-body">
                    <form id="rolePermissionsForm">
                        <h3 class="h6 mb-3">QR Code Permissions</h3>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="qrCreate" checked disabled>
                                    <label class="form-check-label" for="qrCreate">Create</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="qrRead" checked disabled>
                                    <label class="form-check-label" for="qrRead">Read</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="qrUpdate" checked disabled>
                                    <label class="form-check-label" for="qrUpdate">Update</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="qrDelete" checked disabled>
                                    <label class="form-check-label" for="qrDelete">Delete</label>
                                </div>
                            </div>
                        </div>
                        
                        <h3 class="h6 mb-3">User Management</h3>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="userCreate" disabled>
                                    <label class="form-check-label" for="userCreate">Create</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="userRead" disabled>
                                    <label class="form-check-label" for="userRead">Read</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="userUpdate" disabled>
                                    <label class="form-check-label" for="userUpdate">Update</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="userDelete" disabled>
                                    <label class="form-check-label" for="userDelete">Delete</label>
                                </div>
                            </div>
                        </div>
                        
                        <h3 class="h6 mb-3">API Access</h3>
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="apiKeys" checked disabled>
                                    <label class="form-check-label" for="apiKeys">Manage API Keys</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="apiAccess" checked disabled>
                                    <label class="form-check-label" for="apiAccess">API Access</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="apiWebhooks" disabled>
                                    <label class="form-check-label" for="apiWebhooks">Webhooks</label>
                                </div>
                            </div>
                        </div>
                        
                        <h3 class="h6 mb-3">Advanced Features</h3>
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="batchProcessing" disabled>
                                    <label class="form-check-label" for="batchProcessing">Batch Processing</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="analytics" disabled>
                                    <label class="form-check-label" for="analytics">Analytics</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="customization" disabled>
                                    <label class="form-check-label" for="customization">Advanced Customization</label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-outline-danger" id="deleteRoleBtn" disabled>Delete Role</button>
                            <button type="submit" class="btn btn-primary" id="savePermissionsBtn" disabled>Save Changes</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Role Modal -->
<div class="modal fade" id="addRoleModal" tabindex="-1" aria-labelledby="addRoleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addRoleModalLabel">Add Custom Role</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addRoleForm">
                    <div class="mb-3">
                        <label for="roleName" class="form-label">Role Name</label>
                        <input type="text" class="form-control" id="roleName" required>
                    </div>
                    <div class="mb-3">
                        <label for="roleDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="roleDescription" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Base Permissions On</label>
                        <select class="form-select" id="baseRole">
                            <option value="guest">Guest</option>
                            <option value="user" selected>User</option>
                            <option value="premium">Premium User</option>
                            <option value="admin">Administrator</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="createRoleBtn">Create Role</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'qrcode_app/js/role-management.js' %}"></script>
{% endblock %}
