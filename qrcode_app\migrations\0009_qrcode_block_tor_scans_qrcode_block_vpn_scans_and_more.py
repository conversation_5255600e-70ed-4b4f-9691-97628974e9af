# Generated by Django 5.1.7 on 2025-05-28 16:45

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('qrcode_app', '0008_qrcode_approved_at_qrcode_approved_by_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='qrcode',
            name='block_tor_scans',
            field=models.BooleanField(default=False, help_text='Block scans from Tor network'),
        ),
        migrations.AddField(
            model_name='qrcode',
            name='block_vpn_scans',
            field=models.BooleanField(default=False, help_text='Block scans from VPN/proxy connections'),
        ),
        migrations.AddField(
            model_name='qrcode',
            name='blocked_countries',
            field=models.JSONField(blank=True, default=list, help_text='List of blocked country codes'),
        ),
        migrations.AddField(
            model_name='qrcode',
            name='blocked_organizations',
            field=models.JSONField(blank=True, default=list, help_text='List of blocked organization patterns'),
        ),
        migrations.AddField(
            model_name='qrcode',
            name='default_redirect_url',
            field=models.URLField(blank=True, help_text='Default URL when no geo-target matches', null=True),
        ),
        migrations.AddField(
            model_name='qrcode',
            name='enable_geo_targeting',
            field=models.BooleanField(default=False, help_text='Enable different URLs for different countries'),
        ),
        migrations.CreateModel(
            name='QRGeoTarget',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('country_code', models.CharField(help_text="ISO 2-letter country code (e.g., 'US', 'GB')", max_length=2)),
                ('redirect_url', models.URLField(help_text='URL to redirect users from this country')),
                ('priority', models.PositiveIntegerField(default=1, help_text='Priority order (lower number = higher priority)')),
                ('is_active', models.BooleanField(default=True)),
                ('region', models.CharField(blank=True, help_text='Specific region/state within country', max_length=100, null=True)),
                ('city', models.CharField(blank=True, help_text='Specific city within country', max_length=100, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('qr_code', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='geo_targets', to='qrcode_app.qrcode')),
            ],
            options={
                'verbose_name': 'QR Geo Target',
                'verbose_name_plural': 'QR Geo Targets',
                'ordering': ['priority', 'country_code'],
                'unique_together': {('qr_code', 'country_code', 'region', 'city')},
            },
        ),
    ]
